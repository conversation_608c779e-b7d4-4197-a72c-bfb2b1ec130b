"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9227],{33480:function(e,t,n){n.d(t,{Z:function(){return l}});var r=n(2265),i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=(e,t)=>{let n=(0,r.forwardRef)((n,l)=>{let{color:u="currentColor",size:a=24,strokeWidth:s=2,absoluteStrokeWidth:c,className:f="",children:d,...p}=n;return(0,r.createElement)("svg",{ref:l,...i,width:a,height:a,stroke:u,strokeWidth:c?24*Number(s)/Number(a):s,className:["lucide","lucide-".concat(o(e)),f].join(" "),...p},[...t.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(d)?d:[d]])});return n.displayName="".concat(e),n}},78149:function(e,t,n){n.d(t,{M:function(){return r}});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},1584:function(e,t,n){n.d(t,{F:function(){return i},e:function(){return o}});var r=n(2265);function i(...e){return t=>e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}function o(...e){return r.useCallback(i(...e),e)}},98324:function(e,t,n){n.d(t,{b:function(){return l},k:function(){return o}});var r=n(2265),i=n(57437);function o(e,t){let n=r.createContext(t);function o(e){let{children:t,...o}=e,l=r.useMemo(()=>o,Object.values(o));return(0,i.jsx)(n.Provider,{value:l,children:t})}return o.displayName=e+"Provider",[o,function(i){let o=r.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return o.scopeName=e,[function(t,o){let l=r.createContext(o),u=n.length;function a(t){let{scope:n,children:o,...a}=t,s=n?.[e][u]||l,c=r.useMemo(()=>a,Object.values(a));return(0,i.jsx)(s.Provider,{value:c,children:o})}return n=[...n,o],a.displayName=t+"Provider",[a,function(n,i){let a=i?.[e][u]||l,s=r.useContext(a);if(s)return s;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(o,...t)]}},53938:function(e,t,n){n.d(t,{I0:function(){return g},XB:function(){return d},fC:function(){return v}});var r,i=n(2265),o=n(78149),l=n(18676),u=n(1584),a=n(75137),s=n(57437),c="dismissableLayer.update",f=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=i.forwardRef((e,t)=>{var n,d;let{disableOutsidePointerEvents:p=!1,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:b,...x}=e,E=i.useContext(f),[R,A]=i.useState(null),C=null!==(d=null==R?void 0:R.ownerDocument)&&void 0!==d?d:null===(n=globalThis)||void 0===n?void 0:n.document,[,O]=i.useState({}),L=(0,u.e)(t,e=>A(e)),P=Array.from(E.layers),[N]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),T=P.indexOf(N),S=R?P.indexOf(R):-1,W=E.layersWithOutsidePointerEventsDisabled.size>0,D=S>=T,k=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,a.W)(e),o=i.useRef(!1),l=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));!D||n||(null==g||g(e),null==w||w(e),e.defaultPrevented||null==b||b())},C),j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,a.W)(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(null==y||y(e),null==w||w(e),e.defaultPrevented||null==b||b())},C);return!function(e,t=globalThis?.document){let n=(0,a.W)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{S!==E.layers.size-1||(null==v||v(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},C),i.useEffect(()=>{if(R)return p&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(R)),E.layers.add(R),m(),()=>{p&&1===E.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=r)}},[R,C,p,E]),i.useEffect(()=>()=>{R&&(E.layers.delete(R),E.layersWithOutsidePointerEventsDisabled.delete(R),m())},[R,E]),i.useEffect(()=>{let e=()=>O({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,s.jsx)(l.WV.div,{...x,ref:L,style:{pointerEvents:W?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.M)(e.onFocusCapture,j.onFocusCapture),onBlurCapture:(0,o.M)(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:(0,o.M)(e.onPointerDownCapture,k.onPointerDownCapture)})});d.displayName="DismissableLayer";var p=i.forwardRef((e,t)=>{let n=i.useContext(f),r=i.useRef(null),o=(0,u.e)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(l.WV.div,{...e,ref:o})});function m(){let e=new CustomEvent(c);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:i}=r,o=n.originalEvent.target,u=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),i?(0,l.jH)(o,u):o.dispatchEvent(u)}p.displayName="DismissableLayerBranch";var v=d,g=p},53201:function(e,t,n){n.d(t,{M:function(){return a}});var r,i=n(2265),o=n(1336),l=(r||(r=n.t(i,2)))["useId".toString()]||(()=>void 0),u=0;function a(e){let[t,n]=i.useState(l());return(0,o.b)(()=>{e||n(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},25510:function(e,t,n){n.d(t,{ee:function(){return eX},Eh:function(){return eq},VY:function(){return eZ},fC:function(){return eY},D7:function(){return eT}});var r=n(2265);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,u=Math.round,a=Math.floor,s=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>f[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function E(e,t,n){let r,{reference:i,floating:o}=e,l=g(t),u=h(g(t)),a=v(u),s=p(t),c="y"===l,f=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,y=i[a]/2-o[a]/2;switch(s){case"top":r={x:f,y:i.y-o.height};break;case"bottom":r={x:f,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(m(t)){case"start":r[u]-=y*(n&&c?-1:1);break;case"end":r[u]+=y*(n&&c?-1:1)}return r}let R=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,u=o.filter(Boolean),a=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:f}=E(s,r,a),d=r,p={},m=0;for(let n=0;n<u.length;n++){let{name:o,fn:h}=u[n],{x:v,y:g,data:y,reset:w}=await h({x:c,y:f,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=v?v:c,f=null!=g?g:f,p={...p,[o]:{...p[o],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(s=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):w.rects),{x:c,y:f}=E(s,d,a)),n=-1)}return{x:c,y:f,placement:d,strategy:i,middlewareData:p}};async function A(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:u,strategy:a}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:p=!1,padding:m=0}=d(t,e),h=b(m),v=u[p?"floating"===f?"reference":"floating":f],g=x(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(v)))||n?v:v.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(u.floating)),boundary:s,rootBoundary:c,strategy:a})),y="floating"===f?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==o.getOffsetParent?void 0:o.getOffsetParent(u.floating)),E=await (null==o.isElement?void 0:o.isElement(w))&&await (null==o.getScale?void 0:o.getScale(w))||{x:1,y:1},R=x(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:u,rect:y,offsetParent:w,strategy:a}):y);return{top:(g.top-R.top+h.top)/E.y,bottom:(R.bottom-g.bottom+h.bottom)/E.y,left:(g.left-R.left+h.left)/E.x,right:(R.right-g.right+h.right)/E.x}}function C(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function O(e){return i.some(t=>e[t]>=0)}async function L(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),u=m(n),a="y"===g(n),s=["left","top"].includes(l)?-1:1,c=o&&a?-1:1,f=d(t,e),{mainAxis:h,crossAxis:v,alignmentAxis:y}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...f};return u&&"number"==typeof y&&(v="end"===u?-1*y:y),a?{x:v*c,y:h*s}:{x:h*s,y:v*c}}function P(){return"undefined"!=typeof window}function N(e){return W(e)?(e.nodeName||"").toLowerCase():"#document"}function T(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function S(e){var t;return null==(t=(W(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function W(e){return!!P()&&(e instanceof Node||e instanceof T(e).Node)}function D(e){return!!P()&&(e instanceof Element||e instanceof T(e).Element)}function k(e){return!!P()&&(e instanceof HTMLElement||e instanceof T(e).HTMLElement)}function j(e){return!!P()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof T(e).ShadowRoot)}function M(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=B(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function H(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function F(e){let t=V(),n=D(e)?B(e):e;return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function V(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function z(e){return["html","body","#document"].includes(N(e))}function B(e){return T(e).getComputedStyle(e)}function _(e){return D(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function I(e){if("html"===N(e))return e;let t=e.assignedSlot||e.parentNode||j(e)&&e.host||S(e);return j(t)?t.host:t}function $(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=I(t);return z(n)?t.ownerDocument?t.ownerDocument.body:t.body:k(n)&&M(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=T(i);if(o){let e=l.parent&&Object.getPrototypeOf(l.parent)?l.frameElement:null;return t.concat(l,l.visualViewport||[],M(i)?i:[],e&&n?$(e):[])}return t.concat(i,$(i,[],n))}function U(e){let t=B(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=k(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,a=u(n)!==o||u(r)!==l;return a&&(n=o,r=l),{width:n,height:r,$:a}}function Y(e){return D(e)?e:e.contextElement}function X(e){let t=Y(e);if(!k(t))return s(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=U(t),l=(o?u(n.width):n.width)/r,a=(o?u(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let Z=s(0);function q(e){let t=T(e);return V()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Z}function G(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=Y(e),u=s(1);t&&(r?D(r)&&(u=X(r)):u=X(e));let a=(void 0===(i=n)&&(i=!1),r&&(!i||r===T(l))&&i)?q(l):s(0),c=(o.left+a.x)/u.x,f=(o.top+a.y)/u.y,d=o.width/u.x,p=o.height/u.y;if(l){let e=T(l),t=r&&D(r)?T(r):r,n=e,i=n.frameElement;for(;i&&r&&t!==n;){let e=X(i),t=i.getBoundingClientRect(),r=B(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,f*=e.y,d*=e.x,p*=e.y,c+=o,f+=l,i=(n=T(i)).frameElement}}return x({width:d,height:p,x:c,y:f})}function J(e){return G(S(e)).left+_(e).scrollLeft}function K(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=T(e),r=S(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,u=0,a=0;if(i){o=i.width,l=i.height;let e=V();(!e||e&&"fixed"===t)&&(u=i.offsetLeft,a=i.offsetTop)}return{width:o,height:l,x:u,y:a}}(e,n);else if("document"===t)r=function(e){let t=S(e),n=_(e),r=e.ownerDocument.body,i=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),u=-n.scrollLeft+J(e),a=-n.scrollTop;return"rtl"===B(r).direction&&(u+=l(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:u,y:a}}(S(e));else if(D(t))r=function(e,t){let n=G(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=k(e)?X(e):s(1),l=e.clientWidth*o.x;return{width:l,height:e.clientHeight*o.y,x:i*o.x,y:r*o.y}}(t,n);else{let n=q(e);r={...t,x:t.x-n.x,y:t.y-n.y}}return x(r)}function Q(e){return"static"===B(e).position}function ee(e,t){return k(e)&&"fixed"!==B(e).position?t?t(e):e.offsetParent:null}function et(e,t){let n=T(e);if(H(e))return n;if(!k(e)){let t=I(e);for(;t&&!z(t);){if(D(t)&&!Q(t))return t;t=I(t)}return n}let r=ee(e,t);for(;r&&["table","td","th"].includes(N(r))&&Q(r);)r=ee(r,t);return r&&z(r)&&Q(r)&&!F(r)?n:r||function(e){let t=I(e);for(;k(t)&&!z(t);){if(F(t))return t;if(H(t))break;t=I(t)}return null}(e)||n}let en=async function(e){let t=this.getOffsetParent||et,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=k(t),i=S(t),o="fixed"===n,l=G(e,!0,o,t),u={scrollLeft:0,scrollTop:0},a=s(0);if(r||!r&&!o){if(("body"!==N(t)||M(i))&&(u=_(t)),r){let e=G(t,!0,o,t);a.x=e.x+t.clientLeft,a.y=e.y+t.clientTop}else i&&(a.x=J(i))}return{x:l.left+u.scrollLeft-a.x,y:l.top+u.scrollTop-a.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},er={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=S(r),u=!!t&&H(t.floating);if(r===l||u&&o)return n;let a={scrollLeft:0,scrollTop:0},c=s(1),f=s(0),d=k(r);if((d||!d&&!o)&&(("body"!==N(r)||M(l))&&(a=_(r)),k(r))){let e=G(r);c=X(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-a.scrollLeft*c.x+f.x,y:n.y*c.y-a.scrollTop*c.y+f.y}},getDocumentElement:S,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,u=[..."clippingAncestors"===n?H(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=$(e,[],!1).filter(e=>D(e)&&"body"!==N(e)),i=null,o="fixed"===B(e).position,l=o?I(e):e;for(;D(l)&&!z(l);){let t=B(l),n=F(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||M(l)&&!n&&function e(t,n){let r=I(t);return!(r===n||!D(r)||z(r))&&("fixed"===B(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=I(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=u[0],s=u.reduce((e,n)=>{let r=K(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},K(t,a,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:et,getElementRects:en,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=U(e);return{width:t,height:n}},getScale:X,isElement:D,isRTL:function(e){return"rtl"===B(e).direction}},ei=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:u,platform:a,elements:s,middlewareData:c}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let y=b(p),w={x:n,y:r},x=h(g(i)),E=v(x),R=await a.getDimensions(f),A="y"===x,C=A?"clientHeight":"clientWidth",O=u.reference[E]+u.reference[x]-w[x]-u.floating[E],L=w[x]-u.reference[x],P=await (null==a.getOffsetParent?void 0:a.getOffsetParent(f)),N=P?P[C]:0;N&&await (null==a.isElement?void 0:a.isElement(P))||(N=s.floating[C]||u.floating[E]);let T=N/2-R[E]/2-1,S=o(y[A?"top":"left"],T),W=o(y[A?"bottom":"right"],T),D=N-R[E]-W,k=N/2-R[E]/2+(O/2-L/2),j=l(S,o(k,D)),M=!c.arrow&&null!=m(i)&&k!==j&&u.reference[E]/2-(k<S?S:W)-R[E]/2<0,H=M?k<S?k-S:k-D:0;return{[x]:w[x]+H,data:{[x]:j,centerOffset:k-j-H,...M&&{alignmentOffset:H}},reset:M}}}),eo=(e,t,n)=>{let r=new Map,i={platform:er,...n},o={...i.platform,_c:r};return R(e,t,{...i,platform:o})};var el=n(54887),eu="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ea(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ea(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!ea(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function es(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ec(e,t){let n=es(e);return Math.round(t*n)/n}function ef(e){let t=r.useRef(e);return eu(()=>{t.current=e}),t}let ed=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ei({element:n.current,padding:r}).fn(t):{}:n?ei({element:n,padding:r}).fn(t):{}}}),ep=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:i,y:o,placement:l,middlewareData:u}=e,a=await L(e,n);return l===(null==(t=u.offset)?void 0:t.placement)&&null!=(r=u.arrow)&&r.alignmentOffset?{}:{x:i+a.x,y:o+a.y,data:{...a,placement:l}}}}),options:[e,t]}},em=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:i}=e,{mainAxis:u=!0,crossAxis:a=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=d(n,e),f={x:t,y:r},m=await A(e,c),v=g(p(i)),y=h(v),w=f[y],b=f[v];if(u){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+m[e],r=w-m[t];w=l(n,o(w,r))}if(a){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+m[e],r=b-m[t];b=l(n,o(b,r))}let x=s.fn({...e,[y]:w,[v]:b});return{...x,data:{x:x.x-t,y:x.y-r}}}}),options:[e,t]}},eh=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:i,rects:o,middlewareData:l}=e,{offset:u=0,mainAxis:a=!0,crossAxis:s=!0}=d(n,e),c={x:t,y:r},f=g(i),m=h(f),v=c[m],y=c[f],w=d(u,e),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(a){let e="y"===m?"height":"width",t=o.reference[m]-o.floating[e]+b.mainAxis,n=o.reference[m]+o.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(s){var x,E;let e="y"===m?"width":"height",t=["top","left"].includes(p(i)),n=o.reference[f]-o.floating[e]+(t&&(null==(x=l.offset)?void 0:x[f])||0)+(t?0:b.crossAxis),r=o.reference[f]+o.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[f])||0)-(t?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[m]:v,[f]:y}}}),options:[e,t]}},ev=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,i,o,l;let{placement:u,middlewareData:a,rects:s,initialPlacement:c,platform:f,elements:b}=e,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:R,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:O="none",flipAlignment:L=!0,...P}=d(n,e);if(null!=(t=a.arrow)&&t.alignmentOffset)return{};let N=p(u),T=g(c),S=p(c)===c,W=await (null==f.isRTL?void 0:f.isRTL(b.floating)),D=R||(S||!L?[w(c)]:function(e){let t=w(e);return[y(e),t,y(t)]}(c)),k="none"!==O;!R&&k&&D.push(...function(e,t,n,r){let i=m(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(y)))),o}(c,L,O,W));let j=[c,...D],M=await A(e,P),H=[],F=(null==(r=a.flip)?void 0:r.overflows)||[];if(x&&H.push(M[N]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=m(e),i=h(g(e)),o=v(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=w(l)),[l,w(l)]}(u,s,W);H.push(M[e[0]],M[e[1]])}if(F=[...F,{placement:u,overflows:H}],!H.every(e=>e<=0)){let e=((null==(i=a.flip)?void 0:i.index)||0)+1,t=j[e];if(t)return{data:{index:e,overflows:F},reset:{placement:t}};let n=null==(o=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(C){case"bestFit":{let e=null==(l=F.filter(e=>{if(k){let t=g(e.placement);return t===T||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(u!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},eg=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){let t,r;let{placement:i,rects:u,platform:a,elements:s}=e,{apply:c=()=>{},...f}=d(n,e),h=await A(e,f),v=p(i),y=m(i),w="y"===g(i),{width:b,height:x}=u.floating;"top"===v||"bottom"===v?(t=v,r=y===(await (null==a.isRTL?void 0:a.isRTL(s.floating))?"start":"end")?"left":"right"):(r=v,t="end"===y?"top":"bottom");let E=x-h.top-h.bottom,R=b-h.left-h.right,C=o(x-h[t],E),O=o(b-h[r],R),L=!e.middlewareData.shift,P=C,N=O;if(w?N=y||L?o(O,R):R:P=y||L?o(C,E):E,L&&!y){let e=l(h.left,0),t=l(h.right,0),n=l(h.top,0),r=l(h.bottom,0);w?N=b-2*(0!==e||0!==t?e+t:l(h.left,h.right)):P=x-2*(0!==n||0!==r?n+r:l(h.top,h.bottom))}await c({...e,availableWidth:N,availableHeight:P});let T=await a.getDimensions(s.floating);return b!==T.width||x!==T.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},ey=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...i}=d(n,e);switch(r){case"referenceHidden":{let n=C(await A(e,{...i,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:O(n)}}}case"escaped":{let n=C(await A(e,{...i,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:O(n)}}}default:return{}}}}),options:[e,t]}},ew=(e,t)=>({...ed(e),options:[e,t]});var eb=n(18676),ex=n(57437),eE=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,ex.jsx)(eb.WV.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,ex.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eE.displayName="Arrow";var eR=n(1584),eA=n(98324),eC=n(75137),eO=n(1336),eL=n(75238),eP="Popper",[eN,eT]=(0,eA.b)(eP),[eS,eW]=eN(eP),eD=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,ex.jsx)(eS,{scope:t,anchor:i,onAnchorChange:o,children:n})};eD.displayName=eP;var ek="PopperAnchor",ej=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,l=eW(ek,n),u=r.useRef(null),a=(0,eR.e)(t,u);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||u.current)}),i?null:(0,ex.jsx)(eb.WV.div,{...o,ref:a})});ej.displayName=ek;var eM="PopperContent",[eH,eF]=eN(eM),eV=r.forwardRef((e,t)=>{var n,i,u,s,c,f,d,p;let{__scopePopper:m,side:h="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:b=!0,collisionBoundary:x=[],collisionPadding:E=0,sticky:R="partial",hideWhenDetached:A=!1,updatePositionStrategy:C="optimized",onPlaced:O,...L}=e,P=eW(eM,m),[N,T]=r.useState(null),W=(0,eR.e)(t,e=>T(e)),[D,k]=r.useState(null),j=(0,eL.t)(D),M=null!==(d=null==j?void 0:j.width)&&void 0!==d?d:0,H=null!==(p=null==j?void 0:j.height)&&void 0!==p?p:0,F="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},V=Array.isArray(x)?x:[x],z=V.length>0,B={padding:F,boundary:V.filter(eI),altBoundary:z},{refs:_,floatingStyles:I,placement:U,isPositioned:X,middlewareData:Z}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:u}={},transform:a=!0,whileElementsMounted:s,open:c}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=r.useState(i);ea(p,i)||m(i);let[h,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),b=r.useCallback(e=>{e!==A.current&&(A.current=e,y(e))},[]),x=l||h,E=u||g,R=r.useRef(null),A=r.useRef(null),C=r.useRef(f),O=null!=s,L=ef(s),P=ef(o),N=ef(c),T=r.useCallback(()=>{if(!R.current||!A.current)return;let e={placement:t,strategy:n,middleware:p};P.current&&(e.platform=P.current),eo(R.current,A.current,e).then(e=>{let t={...e,isPositioned:!1!==N.current};S.current&&!ea(C.current,t)&&(C.current=t,el.flushSync(()=>{d(t)}))})},[p,t,n,P,N]);eu(()=>{!1===c&&C.current.isPositioned&&(C.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let S=r.useRef(!1);eu(()=>(S.current=!0,()=>{S.current=!1}),[]),eu(()=>{if(x&&(R.current=x),E&&(A.current=E),x&&E){if(L.current)return L.current(x,E,T);T()}},[x,E,T,L,O]);let W=r.useMemo(()=>({reference:R,floating:A,setReference:w,setFloating:b}),[w,b]),D=r.useMemo(()=>({reference:x,floating:E}),[x,E]),k=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=ec(D.floating,f.x),r=ec(D.floating,f.y);return a?{...e,transform:"translate("+t+"px, "+r+"px)",...es(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,a,D.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:T,refs:W,elements:D,floatingStyles:k}),[f,T,W,D,k])}({strategy:"fixed",placement:h+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:u=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=Y(e),m=u||s?[...p?$(p):[],...$(t)]:[];m.forEach(e=>{u&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let h=p&&f?function(e,t){let n,r=null,i=S(e);function u(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(c,f){void 0===c&&(c=!1),void 0===f&&(f=1),u();let{left:d,top:p,width:m,height:h}=e.getBoundingClientRect();if(c||t(),!m||!h)return;let v=a(p),g=a(i.clientWidth-(d+m)),y={rootMargin:-v+"px "+-g+"px "+-a(i.clientHeight-(p+h))+"px "+-a(d)+"px",threshold:l(0,o(1,f))||1},w=!0;function b(e){let t=e[0].intersectionRatio;if(t!==f){if(!w)return s();t?s(!1,t):n=setTimeout(()=>{s(!1,1e-7)},1e3)}w=!1}try{r=new IntersectionObserver(b,{...y,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,y)}r.observe(e)}(!0),u}(p,n):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!d&&g.observe(p),g.observe(t));let y=d?G(e):null;return d&&function t(){let r=G(e);y&&(r.x!==y.x||r.y!==y.y||r.width!==y.width||r.height!==y.height)&&n(),y=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{u&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===C})},elements:{reference:P.anchor},middleware:[ep({mainAxis:v+H,alignmentAxis:y}),b&&em({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?eh():void 0,...B}),b&&ev({...B}),eg({...B,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:l}=n.reference,u=t.floating.style;u.setProperty("--radix-popper-available-width","".concat(r,"px")),u.setProperty("--radix-popper-available-height","".concat(i,"px")),u.setProperty("--radix-popper-anchor-width","".concat(o,"px")),u.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),D&&ew({element:D,padding:w}),e$({arrowWidth:M,arrowHeight:H}),A&&ey({strategy:"referenceHidden",...B})]}),[q,J]=eU(U),K=(0,eC.W)(O);(0,eO.b)(()=>{X&&(null==K||K())},[X,K]);let Q=null===(n=Z.arrow)||void 0===n?void 0:n.x,ee=null===(i=Z.arrow)||void 0===i?void 0:i.y,et=(null===(u=Z.arrow)||void 0===u?void 0:u.centerOffset)!==0,[en,er]=r.useState();return(0,eO.b)(()=>{N&&er(window.getComputedStyle(N).zIndex)},[N]),(0,ex.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...I,transform:X?I.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null===(s=Z.transformOrigin)||void 0===s?void 0:s.x,null===(c=Z.transformOrigin)||void 0===c?void 0:c.y].join(" "),...(null===(f=Z.hide)||void 0===f?void 0:f.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,ex.jsx)(eH,{scope:m,placedSide:q,onArrowChange:k,arrowX:Q,arrowY:ee,shouldHideArrow:et,children:(0,ex.jsx)(eb.WV.div,{"data-side":q,"data-align":J,...L,ref:W,style:{...L.style,animation:X?void 0:"none"}})})})});eV.displayName=eM;var ez="PopperArrow",eB={top:"bottom",right:"left",bottom:"top",left:"right"},e_=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eF(ez,n),o=eB[i.placedSide];return(0,ex.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,ex.jsx)(eE,{...r,ref:t,style:{...r.style,display:"block"}})})});function eI(e){return null!==e}e_.displayName=ez;var e$=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,l;let{placement:u,rects:a,middlewareData:s}=t,c=(null===(n=s.arrow)||void 0===n?void 0:n.centerOffset)!==0,f=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[p,m]=eU(u),h={start:"0%",center:"50%",end:"100%"}[m],v=(null!==(o=null===(r=s.arrow)||void 0===r?void 0:r.x)&&void 0!==o?o:0)+f/2,g=(null!==(l=null===(i=s.arrow)||void 0===i?void 0:i.y)&&void 0!==l?l:0)+d/2,y="",w="";return"bottom"===p?(y=c?h:"".concat(v,"px"),w="".concat(-d,"px")):"top"===p?(y=c?h:"".concat(v,"px"),w="".concat(a.floating.height+d,"px")):"right"===p?(y="".concat(-d,"px"),w=c?h:"".concat(g,"px")):"left"===p&&(y="".concat(a.floating.width+d,"px"),w=c?h:"".concat(g,"px")),{data:{x:y,y:w}}}});function eU(e){let[t,n="center"]=e.split("-");return[t,n]}var eY=eD,eX=ej,eZ=eV,eq=e_},56935:function(e,t,n){n.d(t,{h:function(){return a}});var r=n(2265),i=n(54887),o=n(18676),l=n(1336),u=n(57437),a=r.forwardRef((e,t)=>{var n,a;let{container:s,...c}=e,[f,d]=r.useState(!1);(0,l.b)(()=>d(!0),[]);let p=s||f&&(null===(a=globalThis)||void 0===a?void 0:null===(n=a.document)||void 0===n?void 0:n.body);return p?i.createPortal((0,u.jsx)(o.WV.div,{...c,ref:t}),p):null});a.displayName="Portal"},31383:function(e,t,n){n.d(t,{z:function(){return u}});var r=n(2265),i=n(54887),o=n(1584),l=n(1336),u=e=>{var t,n;let u,s;let{present:c,children:f}=e,d=function(e){var t,n;let[o,u]=r.useState(),s=r.useRef({}),c=r.useRef(e),f=r.useRef("none"),[d,p]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(s.current);f.current="mounted"===d?e:"none"},[d]),(0,l.b)(()=>{let t=s.current,n=c.current;if(n!==e){let r=f.current,i=a(t);e?p("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):n&&r!==i?p("ANIMATION_OUT"):p("UNMOUNT"),c.current=e}},[e,p]),(0,l.b)(()=>{if(o){let e=e=>{let t=a(s.current).includes(e.animationName);e.target===o&&t&&i.flushSync(()=>p("ANIMATION_END"))},t=e=>{e.target===o&&(f.current=a(s.current))};return o.addEventListener("animationstart",t),o.addEventListener("animationcancel",e),o.addEventListener("animationend",e),()=>{o.removeEventListener("animationstart",t),o.removeEventListener("animationcancel",e),o.removeEventListener("animationend",e)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(s.current=getComputedStyle(e)),u(e)},[])}}(c),p="function"==typeof f?f({present:d.isPresent}):r.Children.only(f),m=(0,o.e)(d.ref,(u=null===(t=Object.getOwnPropertyDescriptor(p.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in u&&u.isReactWarning?p.ref:(u=null===(n=Object.getOwnPropertyDescriptor(p,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in u&&u.isReactWarning?p.props.ref:p.props.ref||p.ref);return"function"==typeof f||d.isPresent?r.cloneElement(p,{ref:m}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}u.displayName="Presence"},18676:function(e,t,n){n.d(t,{WV:function(){return f},jH:function(){return d}});var r=n(2265),i=n(54887),o=n(1584),l=n(57437),u=r.forwardRef((e,t)=>{let{children:n,...i}=e,o=r.Children.toArray(n),u=o.find(c);if(u){let e=u.props.children,n=o.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,l.jsx)(a,{...i,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,l.jsx)(a,{...i,ref:t,children:n})});u.displayName="Slot";var a=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){let e,l;let u=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref;return r.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{o(...e),i(...e)}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props),ref:t?(0,o.F)(t,u):u})}return r.Children.count(n)>1?r.Children.only(null):null});a.displayName="SlotClone";var s=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});function c(e){return r.isValidElement(e)&&e.type===s}var f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...i}=e,o=r?u:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function d(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},75137:function(e,t,n){n.d(t,{W:function(){return i}});var r=n(2265);function i(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},91715:function(e,t,n){n.d(t,{T:function(){return o}});var r=n(2265),i=n(75137);function o({prop:e,defaultProp:t,onChange:n=()=>{}}){let[o,l]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[o]=n,l=r.useRef(o),u=(0,i.W)(t);return r.useEffect(()=>{l.current!==o&&(u(o),l.current=o)},[o,l,u]),n}({defaultProp:t,onChange:n}),u=void 0!==e,a=u?e:o,s=(0,i.W)(n);return[a,r.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&s(n)}else l(t)},[u,e,l,s])]}},1336:function(e,t,n){n.d(t,{b:function(){return i}});var r=n(2265),i=globalThis?.document?r.useLayoutEffect:()=>{}},75238:function(e,t,n){n.d(t,{t:function(){return o}});var r=n(2265),i=n(1336);function o(e){let[t,n]=r.useState(void 0);return(0,i.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}}]);