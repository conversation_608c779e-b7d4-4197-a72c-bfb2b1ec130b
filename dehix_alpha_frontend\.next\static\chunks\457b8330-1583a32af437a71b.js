"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7481],{1238:function(e,t,n){let i,s,r,o,l,a,h,u,c,d,_;n.d(t,{N8:function(){return nG}});var p,f,m=n(99279),g=n(42680),y=n(71028),v=n(19053),C=n(20357);let w="@firebase/database",T="1.0.6",I="";/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class k{constructor(e){this.domStorage_=e,this.prefix_="firebase:"}set(e,t){null==t?this.domStorage_.removeItem(this.prefixedName_(e)):this.domStorage_.setItem(this.prefixedName_(e),(0,y.Wl)(t))}get(e){let t=this.domStorage_.getItem(this.prefixedName_(e));return null==t?null:(0,y.cI)(t)}remove(e){this.domStorage_.removeItem(this.prefixedName_(e))}prefixedName_(e){return this.prefix_+e}toString(){return this.domStorage_.toString()}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class b{constructor(){this.cache_={},this.isInMemoryStorage=!0}set(e,t){null==t?delete this.cache_[e]:this.cache_[e]=t}get(e){return(0,y.r3)(this.cache_,e)?this.cache_[e]:null}remove(e){delete this.cache_[e]}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let S=function(e){try{if("undefined"!=typeof window&&void 0!==window[e]){let t=window[e];return t.setItem("firebase:sentinel","cache"),t.removeItem("firebase:sentinel"),new k(t)}}catch(e){}return new b},E=S("localStorage"),N=S("sessionStorage"),R=new v.Yd("@firebase/database"),P=(_=1,function(){return _++}),x=function(e){let t=(0,y.dS)(e),n=new y.gQ;n.update(t);let i=n.digest();return y.US.encodeByteArray(i)},D=function(...e){let t="";for(let n=0;n<e.length;n++){let i=e[n];Array.isArray(i)||i&&"object"==typeof i&&"number"==typeof i.length?t+=D.apply(null,i):"object"==typeof i?t+=(0,y.Wl)(i):t+=i,t+=" "}return t},M=null,A=!0,F=function(e,t){(0,y.hu)(!t||!0===e||!1===e,"Can't turn on custom loggers persistently."),!0===e?(R.logLevel=v.in.VERBOSE,M=R.log.bind(R),t&&N.set("logging_enabled",!0)):"function"==typeof e?M=e:(M=null,N.remove("logging_enabled"))},O=function(...e){if(!0===A&&(A=!1,null===M&&!0===N.get("logging_enabled")&&F(!0)),M){let t=D.apply(null,e);M(t)}},L=function(e){return function(...t){O(e,...t)}},q=function(...e){let t="FIREBASE INTERNAL ERROR: "+D(...e);R.error(t)},W=function(...e){let t=`FIREBASE FATAL ERROR: ${D(...e)}`;throw R.error(t),Error(t)},U=function(...e){let t="FIREBASE WARNING: "+D(...e);R.warn(t)},H=function(){"undefined"!=typeof window&&window.location&&window.location.protocol&&-1!==window.location.protocol.indexOf("https:")&&U("Insecure Firebase access from a secure page. Please use https in calls to new Firebase().")},j=function(e){return"number"==typeof e&&(e!=e||e===Number.POSITIVE_INFINITY||e===Number.NEGATIVE_INFINITY)},z=function(e){if((0,y.Yr)()||"complete"===document.readyState)e();else{let t=!1,n=function(){if(!document.body){setTimeout(n,Math.floor(10));return}t||(t=!0,e())};document.addEventListener?(document.addEventListener("DOMContentLoaded",n,!1),window.addEventListener("load",n,!1)):document.attachEvent&&(document.attachEvent("onreadystatechange",()=>{"complete"===document.readyState&&n()}),window.attachEvent("onload",n))}},Y="[MIN_NAME]",V="[MAX_NAME]",B=function(e,t){if(e===t)return 0;if(e===Y||t===V)return -1;if(t===Y||e===V)return 1;{let n=ee(e),i=ee(t);return null!==n?null!==i?n-i==0?e.length-t.length:n-i:-1:null!==i?1:e<t?-1:1}},K=function(e,t){return e===t?0:e<t?-1:1},G=function(e,t){if(t&&e in t)return t[e];throw Error("Missing required key ("+e+") in object: "+(0,y.Wl)(t))},$=function(e){if("object"!=typeof e||null===e)return(0,y.Wl)(e);let t=[];for(let n in e)t.push(n);t.sort();let n="{";for(let i=0;i<t.length;i++)0!==i&&(n+=","),n+=(0,y.Wl)(t[i])+":"+$(e[t[i]]);return n+"}"},Q=function(e,t){let n=e.length;if(n<=t)return[e];let i=[];for(let s=0;s<n;s+=t)s+t>n?i.push(e.substring(s,n)):i.push(e.substring(s,s+t));return i};function X(e,t){for(let n in e)e.hasOwnProperty(n)&&t(n,e[n])}let J=function(e){let t,n,i,s,r;(0,y.hu)(!j(e),"Invalid JSON number"),0===e?(n=0,i=0,t=1/e==-1/0?1:0):(t=e<0,(e=Math.abs(e))>=22250738585072014e-324?(n=(s=Math.min(Math.floor(Math.log(e)/Math.LN2),1023))+1023,i=Math.round(e*Math.pow(2,52-s)-4503599627370496)):(n=0,i=Math.round(e/5e-324)));let o=[];for(r=52;r;r-=1)o.push(i%2?1:0),i=Math.floor(i/2);for(r=11;r;r-=1)o.push(n%2?1:0),n=Math.floor(n/2);o.push(t?1:0),o.reverse();let l=o.join(""),a="";for(r=0;r<64;r+=8){let e=parseInt(l.substr(r,8),2).toString(16);1===e.length&&(e="0"+e),a+=e}return a.toLowerCase()},Z=RegExp("^-?(0*)\\d{1,10}$"),ee=function(e){if(Z.test(e)){let t=Number(e);if(t>=-**********&&t<=**********)return t}return null},et=function(e){try{e()}catch(e){setTimeout(()=>{throw U("Exception was thrown by user callback.",e.stack||""),e},Math.floor(0))}},en=function(e,t){let n=setTimeout(e,t);return"number"==typeof n&&"undefined"!=typeof Deno&&Deno.unrefTimer?Deno.unrefTimer(n):"object"==typeof n&&n.unref&&n.unref(),n};/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ei{constructor(e,t){this.appName_=e,this.appCheckProvider=t,this.appCheck=null==t?void 0:t.getImmediate({optional:!0}),this.appCheck||null==t||t.get().then(e=>this.appCheck=e)}getToken(e){return this.appCheck?this.appCheck.getToken(e):new Promise((t,n)=>{setTimeout(()=>{this.appCheck?this.getToken(e).then(t,n):t(null)},0)})}addTokenChangeListener(e){var t;null===(t=this.appCheckProvider)||void 0===t||t.get().then(t=>t.addTokenListener(e))}notifyForInvalidToken(){U(`Provided AppCheck credentials for the app named "${this.appName_}" are invalid. This usually indicates your app was not initialized correctly.`)}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class es{constructor(e,t,n){this.appName_=e,this.firebaseOptions_=t,this.authProvider_=n,this.auth_=null,this.auth_=n.getImmediate({optional:!0}),this.auth_||n.onInit(e=>this.auth_=e)}getToken(e){return this.auth_?this.auth_.getToken(e).catch(e=>e&&"auth/token-not-initialized"===e.code?(O("Got auth/token-not-initialized error.  Treating as null token."),null):Promise.reject(e)):new Promise((t,n)=>{setTimeout(()=>{this.auth_?this.getToken(e).then(t,n):t(null)},0)})}addTokenChangeListener(e){this.auth_?this.auth_.addAuthTokenListener(e):this.authProvider_.get().then(t=>t.addAuthTokenListener(e))}removeTokenChangeListener(e){this.authProvider_.get().then(t=>t.removeAuthTokenListener(e))}notifyForInvalidToken(){let e='Provided authentication credentials for the app named "'+this.appName_+'" are invalid. This usually indicates your app was not initialized correctly. ';"credential"in this.firebaseOptions_?e+='Make sure the "credential" property provided to initializeApp() is authorized to access the specified "databaseURL" and is from the correct project.':"serviceAccount"in this.firebaseOptions_?e+='Make sure the "serviceAccount" property provided to initializeApp() is authorized to access the specified "databaseURL" and is from the correct project.':e+='Make sure the "apiKey" and "databaseURL" properties provided to initializeApp() match the values provided for your app at https://console.firebase.google.com/.',U(e)}}class er{constructor(e){this.accessToken=e}getToken(e){return Promise.resolve({accessToken:this.accessToken})}addTokenChangeListener(e){e(this.accessToken)}removeTokenChangeListener(e){}notifyForInvalidToken(){}}er.OWNER="owner";let eo=/(console\.firebase|firebase-console-\w+\.corp|firebase\.corp)\.google\.com/,el="websocket",ea="long_polling";/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class eh{constructor(e,t,n,i,s=!1,r="",o=!1,l=!1){this.secure=t,this.namespace=n,this.webSocketOnly=i,this.nodeAdmin=s,this.persistenceKey=r,this.includeNamespaceInQueryParams=o,this.isUsingEmulator=l,this._host=e.toLowerCase(),this._domain=this._host.substr(this._host.indexOf(".")+1),this.internalHost=E.get("host:"+e)||this._host}isCacheableHost(){return"s-"===this.internalHost.substr(0,2)}isCustomHost(){return"firebaseio.com"!==this._domain&&"firebaseio-demo.com"!==this._domain}get host(){return this._host}set host(e){e!==this.internalHost&&(this.internalHost=e,this.isCacheableHost()&&E.set("host:"+this._host,this.internalHost))}toString(){let e=this.toURLString();return this.persistenceKey&&(e+="<"+this.persistenceKey+">"),e}toURLString(){let e=this.secure?"https://":"http://",t=this.includeNamespaceInQueryParams?`?ns=${this.namespace}`:"";return`${e}${this.host}/${t}`}}function eu(e,t,n){let i;if((0,y.hu)("string"==typeof t,"typeof type must == string"),(0,y.hu)("object"==typeof n,"typeof params must == object"),t===el)i=(e.secure?"wss://":"ws://")+e.internalHost+"/.ws?";else if(t===ea)i=(e.secure?"https://":"http://")+e.internalHost+"/.lp?";else throw Error("Unknown connection type: "+t);(e.host!==e.internalHost||e.isCustomHost()||e.includeNamespaceInQueryParams)&&(n.ns=e.namespace);let s=[];return X(n,(e,t)=>{s.push(e+"="+t)}),i+s.join("&")}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ec{constructor(){this.counters_={}}incrementCounter(e,t=1){(0,y.r3)(this.counters_,e)||(this.counters_[e]=0),this.counters_[e]+=t}get(){return(0,y.p$)(this.counters_)}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let ed={},e_={};function ep(e){let t=e.toString();return ed[t]||(ed[t]=new ec),ed[t]}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ef{constructor(e){this.onMessage_=e,this.pendingResponses=[],this.currentResponseNum=0,this.closeAfterResponse=-1,this.onClose=null}closeAfter(e,t){this.closeAfterResponse=e,this.onClose=t,this.closeAfterResponse<this.currentResponseNum&&(this.onClose(),this.onClose=null)}handleResponse(e,t){for(this.pendingResponses[e]=t;this.pendingResponses[this.currentResponseNum];){let e=this.pendingResponses[this.currentResponseNum];delete this.pendingResponses[this.currentResponseNum];for(let t=0;t<e.length;++t)e[t]&&et(()=>{this.onMessage_(e[t])});if(this.currentResponseNum===this.closeAfterResponse){this.onClose&&(this.onClose(),this.onClose=null);break}this.currentResponseNum++}}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let em="start";class eg{constructor(e,t,n,i,s,r,o){this.connId=e,this.repoInfo=t,this.applicationId=n,this.appCheckToken=i,this.authToken=s,this.transportSessionId=r,this.lastSessionId=o,this.bytesSent=0,this.bytesReceived=0,this.everConnected_=!1,this.log_=L(e),this.stats_=ep(t),this.urlFn=e=>(this.appCheckToken&&(e.ac=this.appCheckToken),eu(t,ea,e))}open(e,t){this.curSegmentNum=0,this.onDisconnect_=t,this.myPacketOrderer=new ef(e),this.isClosed_=!1,this.connectTimeoutTimer_=setTimeout(()=>{this.log_("Timed out trying to connect."),this.onClosed_(),this.connectTimeoutTimer_=null},Math.floor(3e4)),z(()=>{if(this.isClosed_)return;this.scriptTagHolder=new ey((...e)=>{let[t,n,i,s,r]=e;if(this.incrementIncomingBytes_(e),this.scriptTagHolder){if(this.connectTimeoutTimer_&&(clearTimeout(this.connectTimeoutTimer_),this.connectTimeoutTimer_=null),this.everConnected_=!0,t===em)this.id=n,this.password=i;else if("close"===t)n?(this.scriptTagHolder.sendNewPolls=!1,this.myPacketOrderer.closeAfter(n,()=>{this.onClosed_()})):this.onClosed_();else throw Error("Unrecognized command received: "+t)}},(...e)=>{let[t,n]=e;this.incrementIncomingBytes_(e),this.myPacketOrderer.handleResponse(t,n)},()=>{this.onClosed_()},this.urlFn);let e={};e[em]="t",e.ser=Math.floor(1e8*Math.random()),this.scriptTagHolder.uniqueCallbackIdentifier&&(e.cb=this.scriptTagHolder.uniqueCallbackIdentifier),e.v="5",this.transportSessionId&&(e.s=this.transportSessionId),this.lastSessionId&&(e.ls=this.lastSessionId),this.applicationId&&(e.p=this.applicationId),this.appCheckToken&&(e.ac=this.appCheckToken),"undefined"!=typeof location&&location.hostname&&eo.test(location.hostname)&&(e.r="f");let t=this.urlFn(e);this.log_("Connecting via long-poll to "+t),this.scriptTagHolder.addTag(t,()=>{})})}start(){this.scriptTagHolder.startLongPoll(this.id,this.password),this.addDisconnectPingFrame(this.id,this.password)}static forceAllow(){eg.forceAllow_=!0}static forceDisallow(){eg.forceDisallow_=!0}static isAvailable(){return!(0,y.Yr)()&&(!!eg.forceAllow_||!eg.forceDisallow_&&"undefined"!=typeof document&&null!=document.createElement&&!("object"==typeof window&&window.chrome&&window.chrome.extension&&!/^chrome/.test(window.location.href))&&!("object"==typeof Windows&&"object"==typeof Windows.UI))}markConnectionHealthy(){}shutdown_(){this.isClosed_=!0,this.scriptTagHolder&&(this.scriptTagHolder.close(),this.scriptTagHolder=null),this.myDisconnFrame&&(document.body.removeChild(this.myDisconnFrame),this.myDisconnFrame=null),this.connectTimeoutTimer_&&(clearTimeout(this.connectTimeoutTimer_),this.connectTimeoutTimer_=null)}onClosed_(){!this.isClosed_&&(this.log_("Longpoll is closing itself"),this.shutdown_(),this.onDisconnect_&&(this.onDisconnect_(this.everConnected_),this.onDisconnect_=null))}close(){this.isClosed_||(this.log_("Longpoll is being closed."),this.shutdown_())}send(e){let t=(0,y.Wl)(e);this.bytesSent+=t.length,this.stats_.incrementCounter("bytes_sent",t.length);let n=Q((0,y.h$)(t),1840);for(let e=0;e<n.length;e++)this.scriptTagHolder.enqueueSegment(this.curSegmentNum,n.length,n[e]),this.curSegmentNum++}addDisconnectPingFrame(e,t){if((0,y.Yr)())return;this.myDisconnFrame=document.createElement("iframe");let n={};n.dframe="t",n.id=e,n.pw=t,this.myDisconnFrame.src=this.urlFn(n),this.myDisconnFrame.style.display="none",document.body.appendChild(this.myDisconnFrame)}incrementIncomingBytes_(e){let t=(0,y.Wl)(e).length;this.bytesReceived+=t,this.stats_.incrementCounter("bytes_received",t)}}class ey{constructor(e,t,n,i){if(this.onDisconnect=n,this.urlFn=i,this.outstandingRequests=new Set,this.pendingSegs=[],this.currentSerial=Math.floor(1e8*Math.random()),this.sendNewPolls=!0,(0,y.Yr)())this.commandCB=e,this.onMessageCB=t;else{this.uniqueCallbackIdentifier=P(),window["pLPCommand"+this.uniqueCallbackIdentifier]=e,window["pRTLPCB"+this.uniqueCallbackIdentifier]=t,this.myIFrame=ey.createIFrame_();let n="";this.myIFrame.src&&"javascript:"===this.myIFrame.src.substr(0,11)&&(n='<script>document.domain="'+document.domain+'";</script>');let i="<html><body>"+n+"</body></html>";try{this.myIFrame.doc.open(),this.myIFrame.doc.write(i),this.myIFrame.doc.close()}catch(e){O("frame writing exception"),e.stack&&O(e.stack),O(e)}}}static createIFrame_(){let e=document.createElement("iframe");if(e.style.display="none",document.body){document.body.appendChild(e);try{e.contentWindow.document||O("No IE domain setting required")}catch(n){let t=document.domain;e.src="javascript:void((function(){document.open();document.domain='"+t+"';document.close();})())"}}else throw"Document body has not initialized. Wait to initialize Firebase until after the document is ready.";return e.contentDocument?e.doc=e.contentDocument:e.contentWindow?e.doc=e.contentWindow.document:e.document&&(e.doc=e.document),e}close(){this.alive=!1,this.myIFrame&&(this.myIFrame.doc.body.textContent="",setTimeout(()=>{null!==this.myIFrame&&(document.body.removeChild(this.myIFrame),this.myIFrame=null)},Math.floor(0)));let e=this.onDisconnect;e&&(this.onDisconnect=null,e())}startLongPoll(e,t){for(this.myID=e,this.myPW=t,this.alive=!0;this.newRequest_(););}newRequest_(){if(!this.alive||!this.sendNewPolls||!(this.outstandingRequests.size<(this.pendingSegs.length>0?2:1)))return!1;{this.currentSerial++;let e={};e.id=this.myID,e.pw=this.myPW,e.ser=this.currentSerial;let t=this.urlFn(e),n="",i=0;for(;this.pendingSegs.length>0;)if(this.pendingSegs[0].d.length+30+n.length<=1870){let e=this.pendingSegs.shift();n=n+"&seg"+i+"="+e.seg+"&ts"+i+"="+e.ts+"&d"+i+"="+e.d,i++}else break;return t+=n,this.addLongPollTag_(t,this.currentSerial),!0}}enqueueSegment(e,t,n){this.pendingSegs.push({seg:e,ts:t,d:n}),this.alive&&this.newRequest_()}addLongPollTag_(e,t){this.outstandingRequests.add(t);let n=()=>{this.outstandingRequests.delete(t),this.newRequest_()},i=setTimeout(n,Math.floor(25e3));this.addTag(e,()=>{clearTimeout(i),n()})}addTag(e,t){(0,y.Yr)()?this.doNodeLongPoll(e,t):setTimeout(()=>{try{if(!this.sendNewPolls)return;let n=this.myIFrame.doc.createElement("script");n.type="text/javascript",n.async=!0,n.src=e,n.onload=n.onreadystatechange=function(){let e=n.readyState;e&&"loaded"!==e&&"complete"!==e||(n.onload=n.onreadystatechange=null,n.parentNode&&n.parentNode.removeChild(n),t())},n.onerror=()=>{O("Long-poll script failed to load: "+e),this.sendNewPolls=!1,this.close()},this.myIFrame.doc.body.appendChild(n)}catch(e){}},Math.floor(1))}}let ev=null;"undefined"!=typeof MozWebSocket?ev=MozWebSocket:"undefined"!=typeof WebSocket&&(ev=WebSocket);class eC{constructor(e,t,n,i,s,r,o){this.connId=e,this.applicationId=n,this.appCheckToken=i,this.authToken=s,this.keepaliveTimer=null,this.frames=null,this.totalFrames=0,this.bytesSent=0,this.bytesReceived=0,this.log_=L(this.connId),this.stats_=ep(t),this.connURL=eC.connectionURL_(t,r,o,i,n),this.nodeAdmin=t.nodeAdmin}static connectionURL_(e,t,n,i,s){let r={};return r.v="5",!(0,y.Yr)()&&"undefined"!=typeof location&&location.hostname&&eo.test(location.hostname)&&(r.r="f"),t&&(r.s=t),n&&(r.ls=n),i&&(r.ac=i),s&&(r.p=s),eu(e,el,r)}open(e,t){this.onDisconnect=t,this.onMessage=e,this.log_("Websocket connecting to "+this.connURL),this.everConnected_=!1,E.set("previous_websocket_failure",!0);try{let e;if((0,y.Yr)()){let t=this.nodeAdmin?"AdminNode":"Node";e={headers:{"User-Agent":`Firebase/5/${I}/${C.platform}/${t}`,"X-Firebase-GMPID":this.applicationId||""}},this.authToken&&(e.headers.Authorization=`Bearer ${this.authToken}`),this.appCheckToken&&(e.headers["X-Firebase-AppCheck"]=this.appCheckToken);let n=C.env,i=0===this.connURL.indexOf("wss://")?n.HTTPS_PROXY||n.https_proxy:n.HTTP_PROXY||n.http_proxy;i&&(e.proxy={origin:i})}this.mySock=new ev(this.connURL,[],e)}catch(t){this.log_("Error instantiating WebSocket.");let e=t.message||t.data;e&&this.log_(e),this.onClosed_();return}this.mySock.onopen=()=>{this.log_("Websocket connected."),this.everConnected_=!0},this.mySock.onclose=()=>{this.log_("Websocket connection was disconnected."),this.mySock=null,this.onClosed_()},this.mySock.onmessage=e=>{this.handleIncomingFrame(e)},this.mySock.onerror=e=>{this.log_("WebSocket error.  Closing connection.");let t=e.message||e.data;t&&this.log_(t),this.onClosed_()}}start(){}static forceDisallow(){eC.forceDisallow_=!0}static isAvailable(){let e=!1;if("undefined"!=typeof navigator&&navigator.userAgent){let t=navigator.userAgent.match(/Android ([0-9]{0,}\.[0-9]{0,})/);t&&t.length>1&&4.4>parseFloat(t[1])&&(e=!0)}return!e&&null!==ev&&!eC.forceDisallow_}static previouslyFailed(){return E.isInMemoryStorage||!0===E.get("previous_websocket_failure")}markConnectionHealthy(){E.remove("previous_websocket_failure")}appendFrame_(e){if(this.frames.push(e),this.frames.length===this.totalFrames){let e=this.frames.join("");this.frames=null;let t=(0,y.cI)(e);this.onMessage(t)}}handleNewFrameCount_(e){this.totalFrames=e,this.frames=[]}extractFrameCount_(e){if((0,y.hu)(null===this.frames,"We already have a frame buffer"),e.length<=6){let t=Number(e);if(!isNaN(t))return this.handleNewFrameCount_(t),null}return this.handleNewFrameCount_(1),e}handleIncomingFrame(e){if(null===this.mySock)return;let t=e.data;if(this.bytesReceived+=t.length,this.stats_.incrementCounter("bytes_received",t.length),this.resetKeepAlive(),null!==this.frames)this.appendFrame_(t);else{let e=this.extractFrameCount_(t);null!==e&&this.appendFrame_(e)}}send(e){this.resetKeepAlive();let t=(0,y.Wl)(e);this.bytesSent+=t.length,this.stats_.incrementCounter("bytes_sent",t.length);let n=Q(t,16384);n.length>1&&this.sendString_(String(n.length));for(let e=0;e<n.length;e++)this.sendString_(n[e])}shutdown_(){this.isClosed_=!0,this.keepaliveTimer&&(clearInterval(this.keepaliveTimer),this.keepaliveTimer=null),this.mySock&&(this.mySock.close(),this.mySock=null)}onClosed_(){!this.isClosed_&&(this.log_("WebSocket is closing itself"),this.shutdown_(),this.onDisconnect&&(this.onDisconnect(this.everConnected_),this.onDisconnect=null))}close(){this.isClosed_||(this.log_("WebSocket is being closed"),this.shutdown_())}resetKeepAlive(){clearInterval(this.keepaliveTimer),this.keepaliveTimer=setInterval(()=>{this.mySock&&this.sendString_("0"),this.resetKeepAlive()},Math.floor(45e3))}sendString_(e){try{this.mySock.send(e)}catch(e){this.log_("Exception thrown from WebSocket.send():",e.message||e.data,"Closing connection."),setTimeout(this.onClosed_.bind(this),0)}}}eC.responsesRequiredToBeHealthy=2,eC.healthyTimeout=3e4;/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ew{constructor(e){this.initTransports_(e)}static get ALL_TRANSPORTS(){return[eg,eC]}static get IS_TRANSPORT_INITIALIZED(){return this.globalTransportInitialized_}initTransports_(e){let t=eC&&eC.isAvailable(),n=t&&!eC.previouslyFailed();if(e.webSocketOnly&&(t||U("wss:// URL used, but browser isn't known to support websockets.  Trying anyway."),n=!0),n)this.transports_=[eC];else{let e=this.transports_=[];for(let t of ew.ALL_TRANSPORTS)t&&t.isAvailable()&&e.push(t);ew.globalTransportInitialized_=!0}}initialTransport(){if(this.transports_.length>0)return this.transports_[0];throw Error("No transports available")}upgradeTransport(){return this.transports_.length>1?this.transports_[1]:null}}ew.globalTransportInitialized_=!1;class eT{constructor(e,t,n,i,s,r,o,l,a,h){this.id=e,this.repoInfo_=t,this.applicationId_=n,this.appCheckToken_=i,this.authToken_=s,this.onMessage_=r,this.onReady_=o,this.onDisconnect_=l,this.onKill_=a,this.lastSessionId=h,this.connectionCount=0,this.pendingDataMessages=[],this.state_=0,this.log_=L("c:"+this.id+":"),this.transportManager_=new ew(t),this.log_("Connection created"),this.start_()}start_(){let e=this.transportManager_.initialTransport();this.conn_=new e(this.nextTransportId_(),this.repoInfo_,this.applicationId_,this.appCheckToken_,this.authToken_,null,this.lastSessionId),this.primaryResponsesRequired_=e.responsesRequiredToBeHealthy||0;let t=this.connReceiver_(this.conn_),n=this.disconnReceiver_(this.conn_);this.tx_=this.conn_,this.rx_=this.conn_,this.secondaryConn_=null,this.isHealthy_=!1,setTimeout(()=>{this.conn_&&this.conn_.open(t,n)},Math.floor(0));let i=e.healthyTimeout||0;i>0&&(this.healthyTimeout_=en(()=>{this.healthyTimeout_=null,this.isHealthy_||(this.conn_&&this.conn_.bytesReceived>102400?(this.log_("Connection exceeded healthy timeout but has received "+this.conn_.bytesReceived+" bytes.  Marking connection healthy."),this.isHealthy_=!0,this.conn_.markConnectionHealthy()):this.conn_&&this.conn_.bytesSent>10240?this.log_("Connection exceeded healthy timeout but has sent "+this.conn_.bytesSent+" bytes.  Leaving connection alive."):(this.log_("Closing unhealthy connection after timeout."),this.close()))},Math.floor(i)))}nextTransportId_(){return"c:"+this.id+":"+this.connectionCount++}disconnReceiver_(e){return t=>{e===this.conn_?this.onConnectionLost_(t):e===this.secondaryConn_?(this.log_("Secondary connection lost."),this.onSecondaryConnectionLost_()):this.log_("closing an old connection")}}connReceiver_(e){return t=>{2!==this.state_&&(e===this.rx_?this.onPrimaryMessageReceived_(t):e===this.secondaryConn_?this.onSecondaryMessageReceived_(t):this.log_("message on old connection"))}}sendRequest(e){this.sendData_({t:"d",d:e})}tryCleanupConnection(){this.tx_===this.secondaryConn_&&this.rx_===this.secondaryConn_&&(this.log_("cleaning up and promoting a connection: "+this.secondaryConn_.connId),this.conn_=this.secondaryConn_,this.secondaryConn_=null)}onSecondaryControl_(e){if("t"in e){let t=e.t;"a"===t?this.upgradeIfSecondaryHealthy_():"r"===t?(this.log_("Got a reset on secondary, closing it"),this.secondaryConn_.close(),(this.tx_===this.secondaryConn_||this.rx_===this.secondaryConn_)&&this.close()):"o"===t&&(this.log_("got pong on secondary."),this.secondaryResponsesRequired_--,this.upgradeIfSecondaryHealthy_())}}onSecondaryMessageReceived_(e){let t=G("t",e),n=G("d",e);if("c"===t)this.onSecondaryControl_(n);else if("d"===t)this.pendingDataMessages.push(n);else throw Error("Unknown protocol layer: "+t)}upgradeIfSecondaryHealthy_(){this.secondaryResponsesRequired_<=0?(this.log_("Secondary connection is healthy."),this.isHealthy_=!0,this.secondaryConn_.markConnectionHealthy(),this.proceedWithUpgrade_()):(this.log_("sending ping on secondary."),this.secondaryConn_.send({t:"c",d:{t:"p",d:{}}}))}proceedWithUpgrade_(){this.secondaryConn_.start(),this.log_("sending client ack on secondary"),this.secondaryConn_.send({t:"c",d:{t:"a",d:{}}}),this.log_("Ending transmission on primary"),this.conn_.send({t:"c",d:{t:"n",d:{}}}),this.tx_=this.secondaryConn_,this.tryCleanupConnection()}onPrimaryMessageReceived_(e){let t=G("t",e),n=G("d",e);"c"===t?this.onControl_(n):"d"===t&&this.onDataMessage_(n)}onDataMessage_(e){this.onPrimaryResponse_(),this.onMessage_(e)}onPrimaryResponse_(){!this.isHealthy_&&(this.primaryResponsesRequired_--,this.primaryResponsesRequired_<=0&&(this.log_("Primary connection is healthy."),this.isHealthy_=!0,this.conn_.markConnectionHealthy()))}onControl_(e){let t=G("t",e);if("d"in e){let n=e.d;if("h"===t){let e=Object.assign({},n);this.repoInfo_.isUsingEmulator&&(e.h=this.repoInfo_.host),this.onHandshake_(e)}else if("n"===t){this.log_("recvd end transmission on primary"),this.rx_=this.secondaryConn_;for(let e=0;e<this.pendingDataMessages.length;++e)this.onDataMessage_(this.pendingDataMessages[e]);this.pendingDataMessages=[],this.tryCleanupConnection()}else"s"===t?this.onConnectionShutdown_(n):"r"===t?this.onReset_(n):"e"===t?q("Server Error: "+n):"o"===t?(this.log_("got pong on primary."),this.onPrimaryResponse_(),this.sendPingOnPrimaryIfNecessary_()):q("Unknown control packet command: "+t)}}onHandshake_(e){let t=e.ts,n=e.v,i=e.h;this.sessionId=e.s,this.repoInfo_.host=i,0===this.state_&&(this.conn_.start(),this.onConnectionEstablished_(this.conn_,t),"5"!==n&&U("Protocol version mismatch detected"),this.tryStartUpgrade_())}tryStartUpgrade_(){let e=this.transportManager_.upgradeTransport();e&&this.startUpgrade_(e)}startUpgrade_(e){this.secondaryConn_=new e(this.nextTransportId_(),this.repoInfo_,this.applicationId_,this.appCheckToken_,this.authToken_,this.sessionId),this.secondaryResponsesRequired_=e.responsesRequiredToBeHealthy||0;let t=this.connReceiver_(this.secondaryConn_),n=this.disconnReceiver_(this.secondaryConn_);this.secondaryConn_.open(t,n),en(()=>{this.secondaryConn_&&(this.log_("Timed out trying to upgrade."),this.secondaryConn_.close())},Math.floor(6e4))}onReset_(e){this.log_("Reset packet received.  New host: "+e),this.repoInfo_.host=e,1===this.state_?this.close():(this.closeConnections_(),this.start_())}onConnectionEstablished_(e,t){this.log_("Realtime connection established."),this.conn_=e,this.state_=1,this.onReady_&&(this.onReady_(t,this.sessionId),this.onReady_=null),0===this.primaryResponsesRequired_?(this.log_("Primary connection is healthy."),this.isHealthy_=!0):en(()=>{this.sendPingOnPrimaryIfNecessary_()},Math.floor(5e3))}sendPingOnPrimaryIfNecessary_(){this.isHealthy_||1!==this.state_||(this.log_("sending ping on primary."),this.sendData_({t:"c",d:{t:"p",d:{}}}))}onSecondaryConnectionLost_(){let e=this.secondaryConn_;this.secondaryConn_=null,(this.tx_===e||this.rx_===e)&&this.close()}onConnectionLost_(e){this.conn_=null,e||0!==this.state_?1===this.state_&&this.log_("Realtime connection lost."):(this.log_("Realtime connection failed."),this.repoInfo_.isCacheableHost()&&(E.remove("host:"+this.repoInfo_.host),this.repoInfo_.internalHost=this.repoInfo_.host)),this.close()}onConnectionShutdown_(e){this.log_("Connection shutdown command received. Shutting down..."),this.onKill_&&(this.onKill_(e),this.onKill_=null),this.onDisconnect_=null,this.close()}sendData_(e){if(1!==this.state_)throw"Connection is not connected";this.tx_.send(e)}close(){2!==this.state_&&(this.log_("Closing realtime connection."),this.state_=2,this.closeConnections_(),this.onDisconnect_&&(this.onDisconnect_(),this.onDisconnect_=null))}closeConnections_(){this.log_("Shutting down all connections"),this.conn_&&(this.conn_.close(),this.conn_=null),this.secondaryConn_&&(this.secondaryConn_.close(),this.secondaryConn_=null),this.healthyTimeout_&&(clearTimeout(this.healthyTimeout_),this.healthyTimeout_=null)}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class eI{put(e,t,n,i){}merge(e,t,n,i){}refreshAuthToken(e){}refreshAppCheckToken(e){}onDisconnectPut(e,t,n){}onDisconnectMerge(e,t,n){}onDisconnectCancel(e,t){}reportStats(e){}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ek{constructor(e){this.allowedEvents_=e,this.listeners_={},(0,y.hu)(Array.isArray(e)&&e.length>0,"Requires a non-empty array")}trigger(e,...t){if(Array.isArray(this.listeners_[e])){let n=[...this.listeners_[e]];for(let e=0;e<n.length;e++)n[e].callback.apply(n[e].context,t)}}on(e,t,n){this.validateEventType_(e),this.listeners_[e]=this.listeners_[e]||[],this.listeners_[e].push({callback:t,context:n});let i=this.getInitialEvent(e);i&&t.apply(n,i)}off(e,t,n){this.validateEventType_(e);let i=this.listeners_[e]||[];for(let e=0;e<i.length;e++)if(i[e].callback===t&&(!n||n===i[e].context)){i.splice(e,1);return}}validateEventType_(e){(0,y.hu)(this.allowedEvents_.find(t=>t===e),"Unknown event: "+e)}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class eb extends ek{constructor(){super(["online"]),this.online_=!0,"undefined"==typeof window||void 0===window.addEventListener||(0,y.uI)()||(window.addEventListener("online",()=>{this.online_||(this.online_=!0,this.trigger("online",!0))},!1),window.addEventListener("offline",()=>{this.online_&&(this.online_=!1,this.trigger("online",!1))},!1))}static getInstance(){return new eb}getInitialEvent(e){return(0,y.hu)("online"===e,"Unknown event type: "+e),[this.online_]}currentlyOnline(){return this.online_}}class eS{constructor(e,t){if(void 0===t){this.pieces_=e.split("/");let t=0;for(let e=0;e<this.pieces_.length;e++)this.pieces_[e].length>0&&(this.pieces_[t]=this.pieces_[e],t++);this.pieces_.length=t,this.pieceNum_=0}else this.pieces_=e,this.pieceNum_=t}toString(){let e="";for(let t=this.pieceNum_;t<this.pieces_.length;t++)""!==this.pieces_[t]&&(e+="/"+this.pieces_[t]);return e||"/"}}function eE(){return new eS("")}function eN(e){return e.pieceNum_>=e.pieces_.length?null:e.pieces_[e.pieceNum_]}function eR(e){return e.pieces_.length-e.pieceNum_}function eP(e){let t=e.pieceNum_;return t<e.pieces_.length&&t++,new eS(e.pieces_,t)}function ex(e){return e.pieceNum_<e.pieces_.length?e.pieces_[e.pieces_.length-1]:null}function eD(e,t=0){return e.pieces_.slice(e.pieceNum_+t)}function eM(e){if(e.pieceNum_>=e.pieces_.length)return null;let t=[];for(let n=e.pieceNum_;n<e.pieces_.length-1;n++)t.push(e.pieces_[n]);return new eS(t,0)}function eA(e,t){let n=[];for(let t=e.pieceNum_;t<e.pieces_.length;t++)n.push(e.pieces_[t]);if(t instanceof eS)for(let e=t.pieceNum_;e<t.pieces_.length;e++)n.push(t.pieces_[e]);else{let e=t.split("/");for(let t=0;t<e.length;t++)e[t].length>0&&n.push(e[t])}return new eS(n,0)}function eF(e){return e.pieceNum_>=e.pieces_.length}function eO(e,t){let n=eN(e),i=eN(t);if(null===n)return t;if(n===i)return eO(eP(e),eP(t));throw Error("INTERNAL ERROR: innerPath ("+t+") is not within outerPath ("+e+")")}function eL(e,t){if(eR(e)!==eR(t))return!1;for(let n=e.pieceNum_,i=t.pieceNum_;n<=e.pieces_.length;n++,i++)if(e.pieces_[n]!==t.pieces_[i])return!1;return!0}function eq(e,t){let n=e.pieceNum_,i=t.pieceNum_;if(eR(e)>eR(t))return!1;for(;n<e.pieces_.length;){if(e.pieces_[n]!==t.pieces_[i])return!1;++n,++i}return!0}class eW{constructor(e,t){this.errorPrefix_=t,this.parts_=eD(e,0),this.byteLength_=Math.max(1,this.parts_.length);for(let e=0;e<this.parts_.length;e++)this.byteLength_+=(0,y.ug)(this.parts_[e]);eU(this)}}function eU(e){if(e.byteLength_>768)throw Error(e.errorPrefix_+"has a key path longer than 768 bytes ("+e.byteLength_+").");if(e.parts_.length>32)throw Error(e.errorPrefix_+"path specified exceeds the maximum depth that can be written (32) or object contains a cycle "+eH(e))}function eH(e){return 0===e.parts_.length?"":"in property '"+e.parts_.join(".")+"'"}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ej extends ek{constructor(){let e,t;super(["visible"]),"undefined"!=typeof document&&void 0!==document.addEventListener&&(void 0!==document.hidden?(t="visibilitychange",e="hidden"):void 0!==document.mozHidden?(t="mozvisibilitychange",e="mozHidden"):void 0!==document.msHidden?(t="msvisibilitychange",e="msHidden"):void 0!==document.webkitHidden&&(t="webkitvisibilitychange",e="webkitHidden")),this.visible_=!0,t&&document.addEventListener(t,()=>{let t=!document[e];t!==this.visible_&&(this.visible_=t,this.trigger("visible",t))},!1)}static getInstance(){return new ej}getInitialEvent(e){return(0,y.hu)("visible"===e,"Unknown event type: "+e),[this.visible_]}}class ez extends eI{constructor(e,t,n,i,s,r,o,l){if(super(),this.repoInfo_=e,this.applicationId_=t,this.onDataUpdate_=n,this.onConnectStatus_=i,this.onServerInfoUpdate_=s,this.authTokenProvider_=r,this.appCheckTokenProvider_=o,this.authOverride_=l,this.id=ez.nextPersistentConnectionId_++,this.log_=L("p:"+this.id+":"),this.interruptReasons_={},this.listens=new Map,this.outstandingPuts_=[],this.outstandingGets_=[],this.outstandingPutCount_=0,this.outstandingGetCount_=0,this.onDisconnectRequestQueue_=[],this.connected_=!1,this.reconnectDelay_=1e3,this.maxReconnectDelay_=3e5,this.securityDebugCallback_=null,this.lastSessionId=null,this.establishConnectionTimer_=null,this.visible_=!1,this.requestCBHash_={},this.requestNumber_=0,this.realtime_=null,this.authToken_=null,this.appCheckToken_=null,this.forceTokenRefresh_=!1,this.invalidAuthTokenCount_=0,this.invalidAppCheckTokenCount_=0,this.firstConnection_=!0,this.lastConnectionAttemptTime_=null,this.lastConnectionEstablishedTime_=null,l&&!(0,y.Yr)())throw Error("Auth override specified in options, but not supported on non Node.js platforms");ej.getInstance().on("visible",this.onVisible_,this),-1===e.host.indexOf("fblocal")&&eb.getInstance().on("online",this.onOnline_,this)}sendRequest(e,t,n){let i=++this.requestNumber_,s={r:i,a:e,b:t};this.log_((0,y.Wl)(s)),(0,y.hu)(this.connected_,"sendRequest call when we're not connected not allowed."),this.realtime_.sendRequest(s),n&&(this.requestCBHash_[i]=n)}get(e){this.initConnection_();let t=new y.BH,n={p:e._path.toString(),q:e._queryObject};this.outstandingGets_.push({action:"g",request:n,onComplete:e=>{let n=e.d;"ok"===e.s?t.resolve(n):t.reject(n)}}),this.outstandingGetCount_++;let i=this.outstandingGets_.length-1;return this.connected_&&this.sendGet_(i),t.promise}listen(e,t,n,i){this.initConnection_();let s=e._queryIdentifier,r=e._path.toString();this.log_("Listen called for "+r+" "+s),this.listens.has(r)||this.listens.set(r,new Map),(0,y.hu)(e._queryParams.isDefault()||!e._queryParams.loadsAllData(),"listen() called for non-default but complete query"),(0,y.hu)(!this.listens.get(r).has(s),"listen() called twice for same path/queryId.");let o={onComplete:i,hashFn:t,query:e,tag:n};this.listens.get(r).set(s,o),this.connected_&&this.sendListen_(o)}sendGet_(e){let t=this.outstandingGets_[e];this.sendRequest("g",t.request,n=>{delete this.outstandingGets_[e],this.outstandingGetCount_--,0===this.outstandingGetCount_&&(this.outstandingGets_=[]),t.onComplete&&t.onComplete(n)})}sendListen_(e){let t=e.query,n=t._path.toString(),i=t._queryIdentifier;this.log_("Listen on "+n+" for "+i);let s={p:n};e.tag&&(s.q=t._queryObject,s.t=e.tag),s.h=e.hashFn(),this.sendRequest("q",s,s=>{let r=s.d,o=s.s;ez.warnOnListenWarnings_(r,t),(this.listens.get(n)&&this.listens.get(n).get(i))===e&&(this.log_("listen response",s),"ok"!==o&&this.removeListen_(n,i),e.onComplete&&e.onComplete(o,r))})}static warnOnListenWarnings_(e,t){if(e&&"object"==typeof e&&(0,y.r3)(e,"w")){let n=(0,y.DV)(e,"w");if(Array.isArray(n)&&~n.indexOf("no_index")){let e='".indexOn": "'+t._queryParams.getIndex().toString()+'"',n=t._path.toString();U(`Using an unspecified index. Your data will be downloaded and filtered on the client. Consider adding ${e} at ${n} to your security rules for better performance.`)}}}refreshAuthToken(e){this.authToken_=e,this.log_("Auth token refreshed"),this.authToken_?this.tryAuth():this.connected_&&this.sendRequest("unauth",{},()=>{}),this.reduceReconnectDelayIfAdminCredential_(e)}reduceReconnectDelayIfAdminCredential_(e){(e&&40===e.length||(0,y.GJ)(e))&&(this.log_("Admin auth credential detected.  Reducing max reconnect time."),this.maxReconnectDelay_=3e4)}refreshAppCheckToken(e){this.appCheckToken_=e,this.log_("App check token refreshed"),this.appCheckToken_?this.tryAppCheck():this.connected_&&this.sendRequest("unappeck",{},()=>{})}tryAuth(){if(this.connected_&&this.authToken_){let e=this.authToken_,t=(0,y.w9)(e)?"auth":"gauth",n={cred:e};null===this.authOverride_?n.noauth=!0:"object"==typeof this.authOverride_&&(n.authvar=this.authOverride_),this.sendRequest(t,n,t=>{let n=t.s,i=t.d||"error";this.authToken_===e&&("ok"===n?this.invalidAuthTokenCount_=0:this.onAuthRevoked_(n,i))})}}tryAppCheck(){this.connected_&&this.appCheckToken_&&this.sendRequest("appcheck",{token:this.appCheckToken_},e=>{let t=e.s,n=e.d||"error";"ok"===t?this.invalidAppCheckTokenCount_=0:this.onAppCheckRevoked_(t,n)})}unlisten(e,t){let n=e._path.toString(),i=e._queryIdentifier;this.log_("Unlisten called for "+n+" "+i),(0,y.hu)(e._queryParams.isDefault()||!e._queryParams.loadsAllData(),"unlisten() called for non-default but complete query"),this.removeListen_(n,i)&&this.connected_&&this.sendUnlisten_(n,i,e._queryObject,t)}sendUnlisten_(e,t,n,i){this.log_("Unlisten on "+e+" for "+t);let s={p:e};i&&(s.q=n,s.t=i),this.sendRequest("n",s)}onDisconnectPut(e,t,n){this.initConnection_(),this.connected_?this.sendOnDisconnect_("o",e,t,n):this.onDisconnectRequestQueue_.push({pathString:e,action:"o",data:t,onComplete:n})}onDisconnectMerge(e,t,n){this.initConnection_(),this.connected_?this.sendOnDisconnect_("om",e,t,n):this.onDisconnectRequestQueue_.push({pathString:e,action:"om",data:t,onComplete:n})}onDisconnectCancel(e,t){this.initConnection_(),this.connected_?this.sendOnDisconnect_("oc",e,null,t):this.onDisconnectRequestQueue_.push({pathString:e,action:"oc",data:null,onComplete:t})}sendOnDisconnect_(e,t,n,i){let s={p:t,d:n};this.log_("onDisconnect "+e,s),this.sendRequest(e,s,e=>{i&&setTimeout(()=>{i(e.s,e.d)},Math.floor(0))})}put(e,t,n,i){this.putInternal("p",e,t,n,i)}merge(e,t,n,i){this.putInternal("m",e,t,n,i)}putInternal(e,t,n,i,s){this.initConnection_();let r={p:t,d:n};void 0!==s&&(r.h=s),this.outstandingPuts_.push({action:e,request:r,onComplete:i}),this.outstandingPutCount_++;let o=this.outstandingPuts_.length-1;this.connected_?this.sendPut_(o):this.log_("Buffering put: "+t)}sendPut_(e){let t=this.outstandingPuts_[e].action,n=this.outstandingPuts_[e].request,i=this.outstandingPuts_[e].onComplete;this.outstandingPuts_[e].queued=this.connected_,this.sendRequest(t,n,n=>{this.log_(t+" response",n),delete this.outstandingPuts_[e],this.outstandingPutCount_--,0===this.outstandingPutCount_&&(this.outstandingPuts_=[]),i&&i(n.s,n.d)})}reportStats(e){if(this.connected_){let t={c:e};this.log_("reportStats",t),this.sendRequest("s",t,e=>{if("ok"!==e.s){let t=e.d;this.log_("reportStats","Error sending stats: "+t)}})}}onDataMessage_(e){if("r"in e){this.log_("from server: "+(0,y.Wl)(e));let t=e.r,n=this.requestCBHash_[t];n&&(delete this.requestCBHash_[t],n(e.b))}else if("error"in e)throw"A server-side error has occurred: "+e.error;else"a"in e&&this.onDataPush_(e.a,e.b)}onDataPush_(e,t){this.log_("handleServerMessage",e,t),"d"===e?this.onDataUpdate_(t.p,t.d,!1,t.t):"m"===e?this.onDataUpdate_(t.p,t.d,!0,t.t):"c"===e?this.onListenRevoked_(t.p,t.q):"ac"===e?this.onAuthRevoked_(t.s,t.d):"apc"===e?this.onAppCheckRevoked_(t.s,t.d):"sd"===e?this.onSecurityDebugPacket_(t):q("Unrecognized action received from server: "+(0,y.Wl)(e)+"\nAre you using the latest client?")}onReady_(e,t){this.log_("connection ready"),this.connected_=!0,this.lastConnectionEstablishedTime_=new Date().getTime(),this.handleTimestamp_(e),this.lastSessionId=t,this.firstConnection_&&this.sendConnectStats_(),this.restoreState_(),this.firstConnection_=!1,this.onConnectStatus_(!0)}scheduleConnect_(e){(0,y.hu)(!this.realtime_,"Scheduling a connect when we're already connected/ing?"),this.establishConnectionTimer_&&clearTimeout(this.establishConnectionTimer_),this.establishConnectionTimer_=setTimeout(()=>{this.establishConnectionTimer_=null,this.establishConnection_()},Math.floor(e))}initConnection_(){!this.realtime_&&this.firstConnection_&&this.scheduleConnect_(0)}onVisible_(e){!e||this.visible_||this.reconnectDelay_!==this.maxReconnectDelay_||(this.log_("Window became visible.  Reducing delay."),this.reconnectDelay_=1e3,this.realtime_||this.scheduleConnect_(0)),this.visible_=e}onOnline_(e){e?(this.log_("Browser went online."),this.reconnectDelay_=1e3,this.realtime_||this.scheduleConnect_(0)):(this.log_("Browser went offline.  Killing connection."),this.realtime_&&this.realtime_.close())}onRealtimeDisconnect_(){if(this.log_("data client disconnected"),this.connected_=!1,this.realtime_=null,this.cancelSentTransactions_(),this.requestCBHash_={},this.shouldReconnect_()){this.visible_?this.lastConnectionEstablishedTime_&&(new Date().getTime()-this.lastConnectionEstablishedTime_>3e4&&(this.reconnectDelay_=1e3),this.lastConnectionEstablishedTime_=null):(this.log_("Window isn't visible.  Delaying reconnect."),this.reconnectDelay_=this.maxReconnectDelay_,this.lastConnectionAttemptTime_=new Date().getTime());let e=new Date().getTime()-this.lastConnectionAttemptTime_,t=Math.max(0,this.reconnectDelay_-e);t=Math.random()*t,this.log_("Trying to reconnect in "+t+"ms"),this.scheduleConnect_(t),this.reconnectDelay_=Math.min(this.maxReconnectDelay_,1.3*this.reconnectDelay_)}this.onConnectStatus_(!1)}async establishConnection_(){if(this.shouldReconnect_()){this.log_("Making a connection attempt"),this.lastConnectionAttemptTime_=new Date().getTime(),this.lastConnectionEstablishedTime_=null;let e=this.onDataMessage_.bind(this),t=this.onReady_.bind(this),n=this.onRealtimeDisconnect_.bind(this),i=this.id+":"+ez.nextConnectionId_++,s=this.lastSessionId,r=!1,o=null,l=function(){o?o.close():(r=!0,n())};this.realtime_={close:l,sendRequest:function(e){(0,y.hu)(o,"sendRequest call when we're not connected not allowed."),o.sendRequest(e)}};let a=this.forceTokenRefresh_;this.forceTokenRefresh_=!1;try{let[l,h]=await Promise.all([this.authTokenProvider_.getToken(a),this.appCheckTokenProvider_.getToken(a)]);r?O("getToken() completed but was canceled"):(O("getToken() completed. Creating connection."),this.authToken_=l&&l.accessToken,this.appCheckToken_=h&&h.token,o=new eT(i,this.repoInfo_,this.applicationId_,this.appCheckToken_,this.authToken_,e,t,n,e=>{U(e+" ("+this.repoInfo_.toString()+")"),this.interrupt("server_kill")},s))}catch(e){this.log_("Failed to get token: "+e),r||(this.repoInfo_.nodeAdmin&&U(e),l())}}}interrupt(e){O("Interrupting connection for reason: "+e),this.interruptReasons_[e]=!0,this.realtime_?this.realtime_.close():(this.establishConnectionTimer_&&(clearTimeout(this.establishConnectionTimer_),this.establishConnectionTimer_=null),this.connected_&&this.onRealtimeDisconnect_())}resume(e){O("Resuming connection for reason: "+e),delete this.interruptReasons_[e],(0,y.xb)(this.interruptReasons_)&&(this.reconnectDelay_=1e3,this.realtime_||this.scheduleConnect_(0))}handleTimestamp_(e){let t=e-new Date().getTime();this.onServerInfoUpdate_({serverTimeOffset:t})}cancelSentTransactions_(){for(let e=0;e<this.outstandingPuts_.length;e++){let t=this.outstandingPuts_[e];t&&"h"in t.request&&t.queued&&(t.onComplete&&t.onComplete("disconnect"),delete this.outstandingPuts_[e],this.outstandingPutCount_--)}0===this.outstandingPutCount_&&(this.outstandingPuts_=[])}onListenRevoked_(e,t){let n;n=t?t.map(e=>$(e)).join("$"):"default";let i=this.removeListen_(e,n);i&&i.onComplete&&i.onComplete("permission_denied")}removeListen_(e,t){let n;let i=new eS(e).toString();if(this.listens.has(i)){let e=this.listens.get(i);n=e.get(t),e.delete(t),0===e.size&&this.listens.delete(i)}else n=void 0;return n}onAuthRevoked_(e,t){O("Auth token revoked: "+e+"/"+t),this.authToken_=null,this.forceTokenRefresh_=!0,this.realtime_.close(),("invalid_token"===e||"permission_denied"===e)&&(this.invalidAuthTokenCount_++,this.invalidAuthTokenCount_>=3&&(this.reconnectDelay_=3e4,this.authTokenProvider_.notifyForInvalidToken()))}onAppCheckRevoked_(e,t){O("App check token revoked: "+e+"/"+t),this.appCheckToken_=null,this.forceTokenRefresh_=!0,("invalid_token"===e||"permission_denied"===e)&&(this.invalidAppCheckTokenCount_++,this.invalidAppCheckTokenCount_>=3&&this.appCheckTokenProvider_.notifyForInvalidToken())}onSecurityDebugPacket_(e){this.securityDebugCallback_?this.securityDebugCallback_(e):"msg"in e&&console.log("FIREBASE: "+e.msg.replace("\n","\nFIREBASE: "))}restoreState_(){for(let e of(this.tryAuth(),this.tryAppCheck(),this.listens.values()))for(let t of e.values())this.sendListen_(t);for(let e=0;e<this.outstandingPuts_.length;e++)this.outstandingPuts_[e]&&this.sendPut_(e);for(;this.onDisconnectRequestQueue_.length;){let e=this.onDisconnectRequestQueue_.shift();this.sendOnDisconnect_(e.action,e.pathString,e.data,e.onComplete)}for(let e=0;e<this.outstandingGets_.length;e++)this.outstandingGets_[e]&&this.sendGet_(e)}sendConnectStats_(){let e={},t="js";(0,y.Yr)()&&(t=this.repoInfo_.nodeAdmin?"admin_node":"node"),e["sdk."+t+"."+I.replace(/\./g,"-")]=1,(0,y.uI)()?e["framework.cordova"]=1:(0,y.b$)()&&(e["framework.reactnative"]=1),this.reportStats(e)}shouldReconnect_(){let e=eb.getInstance().currentlyOnline();return(0,y.xb)(this.interruptReasons_)&&e}}ez.nextPersistentConnectionId_=0,ez.nextConnectionId_=0;/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class eY{constructor(e,t){this.name=e,this.node=t}static Wrap(e,t){return new eY(e,t)}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class eV{getCompare(){return this.compare.bind(this)}indexedValueChanged(e,t){let n=new eY(Y,e),i=new eY(Y,t);return 0!==this.compare(n,i)}minPost(){return eY.MIN}}class eB extends eV{static get __EMPTY_NODE(){return i}static set __EMPTY_NODE(e){i=e}compare(e,t){return B(e.name,t.name)}isDefinedOn(e){throw(0,y.g5)("KeyIndex.isDefinedOn not expected to be called.")}indexedValueChanged(e,t){return!1}minPost(){return eY.MIN}maxPost(){return new eY(V,i)}makePost(e,t){return(0,y.hu)("string"==typeof e,"KeyIndex indexValue must always be a string."),new eY(e,i)}toString(){return".key"}}let eK=new eB;/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class eG{constructor(e,t,n,i,s=null){this.isReverse_=i,this.resultGenerator_=s,this.nodeStack_=[];let r=1;for(;!e.isEmpty();)if(r=t?n(e.key,t):1,i&&(r*=-1),r<0)e=this.isReverse_?e.left:e.right;else if(0===r){this.nodeStack_.push(e);break}else this.nodeStack_.push(e),e=this.isReverse_?e.right:e.left}getNext(){let e;if(0===this.nodeStack_.length)return null;let t=this.nodeStack_.pop();if(e=this.resultGenerator_?this.resultGenerator_(t.key,t.value):{key:t.key,value:t.value},this.isReverse_)for(t=t.left;!t.isEmpty();)this.nodeStack_.push(t),t=t.right;else for(t=t.right;!t.isEmpty();)this.nodeStack_.push(t),t=t.left;return e}hasNext(){return this.nodeStack_.length>0}peek(){if(0===this.nodeStack_.length)return null;let e=this.nodeStack_[this.nodeStack_.length-1];return this.resultGenerator_?this.resultGenerator_(e.key,e.value):{key:e.key,value:e.value}}}class e${constructor(e,t,n,i,s){this.key=e,this.value=t,this.color=null!=n?n:e$.RED,this.left=null!=i?i:eX.EMPTY_NODE,this.right=null!=s?s:eX.EMPTY_NODE}copy(e,t,n,i,s){return new e$(null!=e?e:this.key,null!=t?t:this.value,null!=n?n:this.color,null!=i?i:this.left,null!=s?s:this.right)}count(){return this.left.count()+1+this.right.count()}isEmpty(){return!1}inorderTraversal(e){return this.left.inorderTraversal(e)||!!e(this.key,this.value)||this.right.inorderTraversal(e)}reverseTraversal(e){return this.right.reverseTraversal(e)||e(this.key,this.value)||this.left.reverseTraversal(e)}min_(){return this.left.isEmpty()?this:this.left.min_()}minKey(){return this.min_().key}maxKey(){return this.right.isEmpty()?this.key:this.right.maxKey()}insert(e,t,n){let i=this,s=n(e,i.key);return(i=s<0?i.copy(null,null,null,i.left.insert(e,t,n),null):0===s?i.copy(null,t,null,null,null):i.copy(null,null,null,null,i.right.insert(e,t,n))).fixUp_()}removeMin_(){if(this.left.isEmpty())return eX.EMPTY_NODE;let e=this;return e.left.isRed_()||e.left.left.isRed_()||(e=e.moveRedLeft_()),(e=e.copy(null,null,null,e.left.removeMin_(),null)).fixUp_()}remove(e,t){let n,i;if(n=this,0>t(e,n.key))n.left.isEmpty()||n.left.isRed_()||n.left.left.isRed_()||(n=n.moveRedLeft_()),n=n.copy(null,null,null,n.left.remove(e,t),null);else{if(n.left.isRed_()&&(n=n.rotateRight_()),n.right.isEmpty()||n.right.isRed_()||n.right.left.isRed_()||(n=n.moveRedRight_()),0===t(e,n.key)){if(n.right.isEmpty())return eX.EMPTY_NODE;i=n.right.min_(),n=n.copy(i.key,i.value,null,null,n.right.removeMin_())}n=n.copy(null,null,null,null,n.right.remove(e,t))}return n.fixUp_()}isRed_(){return this.color}fixUp_(){let e=this;return e.right.isRed_()&&!e.left.isRed_()&&(e=e.rotateLeft_()),e.left.isRed_()&&e.left.left.isRed_()&&(e=e.rotateRight_()),e.left.isRed_()&&e.right.isRed_()&&(e=e.colorFlip_()),e}moveRedLeft_(){let e=this.colorFlip_();return e.right.left.isRed_()&&(e=(e=(e=e.copy(null,null,null,null,e.right.rotateRight_())).rotateLeft_()).colorFlip_()),e}moveRedRight_(){let e=this.colorFlip_();return e.left.left.isRed_()&&(e=(e=e.rotateRight_()).colorFlip_()),e}rotateLeft_(){let e=this.copy(null,null,e$.RED,null,this.right.left);return this.right.copy(null,null,this.color,e,null)}rotateRight_(){let e=this.copy(null,null,e$.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,e)}colorFlip_(){let e=this.left.copy(null,null,!this.left.color,null,null),t=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,e,t)}checkMaxDepth_(){return Math.pow(2,this.check_())<=this.count()+1}check_(){if(this.isRed_()&&this.left.isRed_())throw Error("Red node has red child("+this.key+","+this.value+")");if(this.right.isRed_())throw Error("Right child of ("+this.key+","+this.value+") is red");let e=this.left.check_();if(e===this.right.check_())return e+(this.isRed_()?0:1);throw Error("Black depths differ")}}e$.RED=!0,e$.BLACK=!1;class eQ{copy(e,t,n,i,s){return this}insert(e,t,n){return new e$(e,t,null)}remove(e,t){return this}count(){return 0}isEmpty(){return!0}inorderTraversal(e){return!1}reverseTraversal(e){return!1}minKey(){return null}maxKey(){return null}check_(){return 0}isRed_(){return!1}}class eX{constructor(e,t=eX.EMPTY_NODE){this.comparator_=e,this.root_=t}insert(e,t){return new eX(this.comparator_,this.root_.insert(e,t,this.comparator_).copy(null,null,e$.BLACK,null,null))}remove(e){return new eX(this.comparator_,this.root_.remove(e,this.comparator_).copy(null,null,e$.BLACK,null,null))}get(e){let t;let n=this.root_;for(;!n.isEmpty();){if(0===(t=this.comparator_(e,n.key)))return n.value;t<0?n=n.left:t>0&&(n=n.right)}return null}getPredecessorKey(e){let t,n=this.root_,i=null;for(;!n.isEmpty();){if(0===(t=this.comparator_(e,n.key))){if(n.left.isEmpty()){if(i)return i.key;return null}for(n=n.left;!n.right.isEmpty();)n=n.right;return n.key}t<0?n=n.left:t>0&&(i=n,n=n.right)}throw Error("Attempted to find predecessor key for a nonexistent key.  What gives?")}isEmpty(){return this.root_.isEmpty()}count(){return this.root_.count()}minKey(){return this.root_.minKey()}maxKey(){return this.root_.maxKey()}inorderTraversal(e){return this.root_.inorderTraversal(e)}reverseTraversal(e){return this.root_.reverseTraversal(e)}getIterator(e){return new eG(this.root_,null,this.comparator_,!1,e)}getIteratorFrom(e,t){return new eG(this.root_,e,this.comparator_,!1,t)}getReverseIteratorFrom(e,t){return new eG(this.root_,e,this.comparator_,!0,t)}getReverseIterator(e){return new eG(this.root_,null,this.comparator_,!0,e)}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function eJ(e,t){return B(e.name,t.name)}function eZ(e,t){return B(e,t)}eX.EMPTY_NODE=new eQ;let e0=function(e){return"number"==typeof e?"number:"+J(e):"string:"+e},e1=function(e){if(e.isLeafNode()){let t=e.val();(0,y.hu)("string"==typeof t||"number"==typeof t||"object"==typeof t&&(0,y.r3)(t,".sv"),"Priority must be a string or number.")}else(0,y.hu)(e===s||e.isEmpty(),"priority of unexpected type.");(0,y.hu)(e===s||e.getPriority().isEmpty(),"Priority nodes can't have a priority of their own.")};class e3{constructor(e,t=e3.__childrenNodeConstructor.EMPTY_NODE){this.value_=e,this.priorityNode_=t,this.lazyHash_=null,(0,y.hu)(void 0!==this.value_&&null!==this.value_,"LeafNode shouldn't be created with null/undefined value."),e1(this.priorityNode_)}static set __childrenNodeConstructor(e){r=e}static get __childrenNodeConstructor(){return r}isLeafNode(){return!0}getPriority(){return this.priorityNode_}updatePriority(e){return new e3(this.value_,e)}getImmediateChild(e){return".priority"===e?this.priorityNode_:e3.__childrenNodeConstructor.EMPTY_NODE}getChild(e){return eF(e)?this:".priority"===eN(e)?this.priorityNode_:e3.__childrenNodeConstructor.EMPTY_NODE}hasChild(){return!1}getPredecessorChildName(e,t){return null}updateImmediateChild(e,t){return".priority"===e?this.updatePriority(t):t.isEmpty()&&".priority"!==e?this:e3.__childrenNodeConstructor.EMPTY_NODE.updateImmediateChild(e,t).updatePriority(this.priorityNode_)}updateChild(e,t){let n=eN(e);return null===n?t:t.isEmpty()&&".priority"!==n?this:((0,y.hu)(".priority"!==n||1===eR(e),".priority must be the last token in a path"),this.updateImmediateChild(n,e3.__childrenNodeConstructor.EMPTY_NODE.updateChild(eP(e),t)))}isEmpty(){return!1}numChildren(){return 0}forEachChild(e,t){return!1}val(e){return e&&!this.getPriority().isEmpty()?{".value":this.getValue(),".priority":this.getPriority().val()}:this.getValue()}hash(){if(null===this.lazyHash_){let e="";this.priorityNode_.isEmpty()||(e+="priority:"+e0(this.priorityNode_.val())+":");let t=typeof this.value_;e+=t+":","number"===t?e+=J(this.value_):e+=this.value_,this.lazyHash_=x(e)}return this.lazyHash_}getValue(){return this.value_}compareTo(e){return e===e3.__childrenNodeConstructor.EMPTY_NODE?1:e instanceof e3.__childrenNodeConstructor?-1:((0,y.hu)(e.isLeafNode(),"Unknown node type"),this.compareToLeafNode_(e))}compareToLeafNode_(e){let t=typeof e.value_,n=typeof this.value_,i=e3.VALUE_TYPE_ORDER.indexOf(t),s=e3.VALUE_TYPE_ORDER.indexOf(n);return((0,y.hu)(i>=0,"Unknown leaf type: "+t),(0,y.hu)(s>=0,"Unknown leaf type: "+n),i!==s)?s-i:"object"===n?0:this.value_<e.value_?-1:this.value_===e.value_?0:1}withIndex(){return this}isIndexed(){return!0}equals(e){return e===this||!!e.isLeafNode()&&this.value_===e.value_&&this.priorityNode_.equals(e.priorityNode_)}}e3.VALUE_TYPE_ORDER=["object","boolean","number","string"];class e2 extends eV{compare(e,t){let n=e.node.getPriority(),i=t.node.getPriority(),s=n.compareTo(i);return 0===s?B(e.name,t.name):s}isDefinedOn(e){return!e.getPriority().isEmpty()}indexedValueChanged(e,t){return!e.getPriority().equals(t.getPriority())}minPost(){return eY.MIN}maxPost(){return new eY(V,new e3("[PRIORITY-POST]",l))}makePost(e,t){return new eY(t,new e3("[PRIORITY-POST]",o(e)))}toString(){return".priority"}}let e4=new e2,e5=Math.log(2);class e8{constructor(e){this.count=parseInt(Math.log(e+1)/e5,10),this.current_=this.count-1;let t=parseInt(Array(this.count+1).join("1"),2);this.bits_=e+1&t}nextBitIsOne(){let e=!(this.bits_&1<<this.current_);return this.current_--,e}}let e7=function(e,t,n,i){e.sort(t);let s=function(t,i){let r;let o=i-t;if(0===o)return null;if(1===o)return r=e[t],new e$(n?n(r):r,r.node,e$.BLACK,null,null);{let l=parseInt(o/2,10)+t,a=s(t,l),h=s(l+1,i);return r=e[l],new e$(n?n(r):r,r.node,e$.BLACK,a,h)}};return new eX(i||t,function(t){let i=null,r=null,o=e.length,l=function(t,i){let r=o-t,l=o;o-=t;let h=s(r+1,l),u=e[r];a(new e$(n?n(u):u,u.node,i,null,h))},a=function(e){i?i.left=e:r=e,i=e};for(let e=0;e<t.count;++e){let n=t.nextBitIsOne(),i=Math.pow(2,t.count-(e+1));n?l(i,e$.BLACK):(l(i,e$.BLACK),l(i,e$.RED))}return r}(new e8(e.length)))},e6={};class e9{constructor(e,t){this.indexes_=e,this.indexSet_=t}static get Default(){return(0,y.hu)(e6&&e4,"ChildrenNode.ts has not been loaded"),a=a||new e9({".priority":e6},{".priority":e4})}get(e){let t=(0,y.DV)(this.indexes_,e);if(!t)throw Error("No index defined for "+e);return t instanceof eX?t:null}hasIndex(e){return(0,y.r3)(this.indexSet_,e.toString())}addIndex(e,t){let n;(0,y.hu)(e!==eK,"KeyIndex always exists and isn't meant to be added to the IndexMap.");let i=[],s=!1,r=t.getIterator(eY.Wrap),o=r.getNext();for(;o;)s=s||e.isDefinedOn(o.node),i.push(o),o=r.getNext();n=s?e7(i,e.getCompare()):e6;let l=e.toString(),a=Object.assign({},this.indexSet_);a[l]=e;let h=Object.assign({},this.indexes_);return h[l]=n,new e9(h,a)}addToIndexes(e,t){return new e9((0,y.UI)(this.indexes_,(n,i)=>{let s=(0,y.DV)(this.indexSet_,i);if((0,y.hu)(s,"Missing index implementation for "+i),n===e6){if(!s.isDefinedOn(e.node))return e6;{let n=[],i=t.getIterator(eY.Wrap),r=i.getNext();for(;r;)r.name!==e.name&&n.push(r),r=i.getNext();return n.push(e),e7(n,s.getCompare())}}{let i=t.get(e.name),s=n;return i&&(s=s.remove(new eY(e.name,i))),s.insert(e,e.node)}}),this.indexSet_)}removeFromIndexes(e,t){return new e9((0,y.UI)(this.indexes_,n=>{if(n===e6)return n;{let i=t.get(e.name);return i?n.remove(new eY(e.name,i)):n}}),this.indexSet_)}}class te{constructor(e,t,n){this.children_=e,this.priorityNode_=t,this.indexMap_=n,this.lazyHash_=null,this.priorityNode_&&e1(this.priorityNode_),this.children_.isEmpty()&&(0,y.hu)(!this.priorityNode_||this.priorityNode_.isEmpty(),"An empty node cannot have a priority")}static get EMPTY_NODE(){return h||(h=new te(new eX(eZ),null,e9.Default))}isLeafNode(){return!1}getPriority(){return this.priorityNode_||h}updatePriority(e){return this.children_.isEmpty()?this:new te(this.children_,e,this.indexMap_)}getImmediateChild(e){if(".priority"===e)return this.getPriority();{let t=this.children_.get(e);return null===t?h:t}}getChild(e){let t=eN(e);return null===t?this:this.getImmediateChild(t).getChild(eP(e))}hasChild(e){return null!==this.children_.get(e)}updateImmediateChild(e,t){if((0,y.hu)(t,"We should always be passing snapshot nodes"),".priority"===e)return this.updatePriority(t);{let n,i;let s=new eY(e,t);t.isEmpty()?(n=this.children_.remove(e),i=this.indexMap_.removeFromIndexes(s,this.children_)):(n=this.children_.insert(e,t),i=this.indexMap_.addToIndexes(s,this.children_));let r=n.isEmpty()?h:this.priorityNode_;return new te(n,r,i)}}updateChild(e,t){let n=eN(e);if(null===n)return t;{(0,y.hu)(".priority"!==eN(e)||1===eR(e),".priority must be the last token in a path");let i=this.getImmediateChild(n).updateChild(eP(e),t);return this.updateImmediateChild(n,i)}}isEmpty(){return this.children_.isEmpty()}numChildren(){return this.children_.count()}val(e){if(this.isEmpty())return null;let t={},n=0,i=0,s=!0;if(this.forEachChild(e4,(r,o)=>{t[r]=o.val(e),n++,s&&te.INTEGER_REGEXP_.test(r)?i=Math.max(i,Number(r)):s=!1}),e||!s||!(i<2*n))return e&&!this.getPriority().isEmpty()&&(t[".priority"]=this.getPriority().val()),t;{let e=[];for(let n in t)e[n]=t[n];return e}}hash(){if(null===this.lazyHash_){let e="";this.getPriority().isEmpty()||(e+="priority:"+e0(this.getPriority().val())+":"),this.forEachChild(e4,(t,n)=>{let i=n.hash();""!==i&&(e+=":"+t+":"+i)}),this.lazyHash_=""===e?"":x(e)}return this.lazyHash_}getPredecessorChildName(e,t,n){let i=this.resolveIndex_(n);if(!i)return this.children_.getPredecessorKey(e);{let n=i.getPredecessorKey(new eY(e,t));return n?n.name:null}}getFirstChildName(e){let t=this.resolveIndex_(e);if(!t)return this.children_.minKey();{let e=t.minKey();return e&&e.name}}getFirstChild(e){let t=this.getFirstChildName(e);return t?new eY(t,this.children_.get(t)):null}getLastChildName(e){let t=this.resolveIndex_(e);if(!t)return this.children_.maxKey();{let e=t.maxKey();return e&&e.name}}getLastChild(e){let t=this.getLastChildName(e);return t?new eY(t,this.children_.get(t)):null}forEachChild(e,t){let n=this.resolveIndex_(e);return n?n.inorderTraversal(e=>t(e.name,e.node)):this.children_.inorderTraversal(t)}getIterator(e){return this.getIteratorFrom(e.minPost(),e)}getIteratorFrom(e,t){let n=this.resolveIndex_(t);if(n)return n.getIteratorFrom(e,e=>e);{let n=this.children_.getIteratorFrom(e.name,eY.Wrap),i=n.peek();for(;null!=i&&0>t.compare(i,e);)n.getNext(),i=n.peek();return n}}getReverseIterator(e){return this.getReverseIteratorFrom(e.maxPost(),e)}getReverseIteratorFrom(e,t){let n=this.resolveIndex_(t);if(n)return n.getReverseIteratorFrom(e,e=>e);{let n=this.children_.getReverseIteratorFrom(e.name,eY.Wrap),i=n.peek();for(;null!=i&&t.compare(i,e)>0;)n.getNext(),i=n.peek();return n}}compareTo(e){return this.isEmpty()?e.isEmpty()?0:-1:e.isLeafNode()||e.isEmpty()?1:e===tn?-1:0}withIndex(e){if(e===eK||this.indexMap_.hasIndex(e))return this;{let t=this.indexMap_.addIndex(e,this.children_);return new te(this.children_,this.priorityNode_,t)}}isIndexed(e){return e===eK||this.indexMap_.hasIndex(e)}equals(e){if(e===this)return!0;if(e.isLeafNode()||!this.getPriority().equals(e.getPriority())||this.children_.count()!==e.children_.count())return!1;{let t=this.getIterator(e4),n=e.getIterator(e4),i=t.getNext(),s=n.getNext();for(;i&&s;){if(i.name!==s.name||!i.node.equals(s.node))return!1;i=t.getNext(),s=n.getNext()}return null===i&&null===s}}resolveIndex_(e){return e===eK?null:this.indexMap_.get(e.toString())}}te.INTEGER_REGEXP_=/^(0|[1-9]\d*)$/;class tt extends te{constructor(){super(new eX(eZ),te.EMPTY_NODE,e9.Default)}compareTo(e){return e===this?0:1}equals(e){return e===this}getPriority(){return this}getImmediateChild(e){return te.EMPTY_NODE}isEmpty(){return!1}}let tn=new tt;function ti(e,t=null){if(null===e)return te.EMPTY_NODE;if("object"==typeof e&&".priority"in e&&(t=e[".priority"]),(0,y.hu)(null===t||"string"==typeof t||"number"==typeof t||"object"==typeof t&&".sv"in t,"Invalid priority type found: "+typeof t),"object"==typeof e&&".value"in e&&null!==e[".value"]&&(e=e[".value"]),"object"!=typeof e||".sv"in e)return new e3(e,ti(t));if(e instanceof Array){let n=te.EMPTY_NODE;return X(e,(t,i)=>{if((0,y.r3)(e,t)&&"."!==t.substring(0,1)){let e=ti(i);(e.isLeafNode()||!e.isEmpty())&&(n=n.updateImmediateChild(t,e))}}),n.updatePriority(ti(t))}{let n=[],i=!1;if(X(e,(e,t)=>{if("."!==e.substring(0,1)){let s=ti(t);s.isEmpty()||(i=i||!s.getPriority().isEmpty(),n.push(new eY(e,s)))}}),0===n.length)return te.EMPTY_NODE;let s=e7(n,eJ,e=>e.name,eZ);if(!i)return new te(s,ti(t),e9.Default);{let e=e7(n,e4.getCompare());return new te(s,ti(t),new e9({".priority":e},{".priority":e4}))}}}Object.defineProperties(eY,{MIN:{value:new eY(Y,te.EMPTY_NODE)},MAX:{value:new eY(V,tn)}}),eB.__EMPTY_NODE=te.EMPTY_NODE,e3.__childrenNodeConstructor=te,s=tn,l=tn,o=ti;/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ts extends eV{constructor(e){super(),this.indexPath_=e,(0,y.hu)(!eF(e)&&".priority"!==eN(e),"Can't create PathIndex with empty path or .priority key")}extractChild(e){return e.getChild(this.indexPath_)}isDefinedOn(e){return!e.getChild(this.indexPath_).isEmpty()}compare(e,t){let n=this.extractChild(e.node),i=this.extractChild(t.node),s=n.compareTo(i);return 0===s?B(e.name,t.name):s}makePost(e,t){let n=ti(e);return new eY(t,te.EMPTY_NODE.updateChild(this.indexPath_,n))}maxPost(){return new eY(V,te.EMPTY_NODE.updateChild(this.indexPath_,tn))}toString(){return eD(this.indexPath_,0).join("/")}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class tr extends eV{compare(e,t){let n=e.node.compareTo(t.node);return 0===n?B(e.name,t.name):n}isDefinedOn(e){return!0}indexedValueChanged(e,t){return!e.equals(t)}minPost(){return eY.MIN}maxPost(){return eY.MAX}makePost(e,t){return new eY(t,ti(e))}toString(){return".value"}}let to=new tr;function tl(e,t,n){return{type:"child_changed",snapshotNode:t,childName:e,oldSnap:n}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ta{constructor(){this.limitSet_=!1,this.startSet_=!1,this.startNameSet_=!1,this.startAfterSet_=!1,this.endSet_=!1,this.endNameSet_=!1,this.endBeforeSet_=!1,this.limit_=0,this.viewFrom_="",this.indexStartValue_=null,this.indexStartName_="",this.indexEndValue_=null,this.indexEndName_="",this.index_=e4}hasStart(){return this.startSet_}isViewFromLeft(){return""===this.viewFrom_?this.startSet_:"l"===this.viewFrom_}getIndexStartValue(){return(0,y.hu)(this.startSet_,"Only valid if start has been set"),this.indexStartValue_}getIndexStartName(){return((0,y.hu)(this.startSet_,"Only valid if start has been set"),this.startNameSet_)?this.indexStartName_:Y}hasEnd(){return this.endSet_}getIndexEndValue(){return(0,y.hu)(this.endSet_,"Only valid if end has been set"),this.indexEndValue_}getIndexEndName(){return((0,y.hu)(this.endSet_,"Only valid if end has been set"),this.endNameSet_)?this.indexEndName_:V}hasLimit(){return this.limitSet_}hasAnchoredLimit(){return this.limitSet_&&""!==this.viewFrom_}getLimit(){return(0,y.hu)(this.limitSet_,"Only valid if limit has been set"),this.limit_}getIndex(){return this.index_}loadsAllData(){return!(this.startSet_||this.endSet_||this.limitSet_)}isDefault(){return this.loadsAllData()&&this.index_===e4}copy(){let e=new ta;return e.limitSet_=this.limitSet_,e.limit_=this.limit_,e.startSet_=this.startSet_,e.startAfterSet_=this.startAfterSet_,e.indexStartValue_=this.indexStartValue_,e.startNameSet_=this.startNameSet_,e.indexStartName_=this.indexStartName_,e.endSet_=this.endSet_,e.endBeforeSet_=this.endBeforeSet_,e.indexEndValue_=this.indexEndValue_,e.endNameSet_=this.endNameSet_,e.indexEndName_=this.indexEndName_,e.index_=this.index_,e.viewFrom_=this.viewFrom_,e}}function th(e){let t;let n={};if(e.isDefault())return n;if(e.index_===e4?t="$priority":e.index_===to?t="$value":e.index_===eK?t="$key":((0,y.hu)(e.index_ instanceof ts,"Unrecognized index type!"),t=e.index_.toString()),n.orderBy=(0,y.Wl)(t),e.startSet_){let t=e.startAfterSet_?"startAfter":"startAt";n[t]=(0,y.Wl)(e.indexStartValue_),e.startNameSet_&&(n[t]+=","+(0,y.Wl)(e.indexStartName_))}if(e.endSet_){let t=e.endBeforeSet_?"endBefore":"endAt";n[t]=(0,y.Wl)(e.indexEndValue_),e.endNameSet_&&(n[t]+=","+(0,y.Wl)(e.indexEndName_))}return e.limitSet_&&(e.isViewFromLeft()?n.limitToFirst=e.limit_:n.limitToLast=e.limit_),n}function tu(e){let t={};if(e.startSet_&&(t.sp=e.indexStartValue_,e.startNameSet_&&(t.sn=e.indexStartName_),t.sin=!e.startAfterSet_),e.endSet_&&(t.ep=e.indexEndValue_,e.endNameSet_&&(t.en=e.indexEndName_),t.ein=!e.endBeforeSet_),e.limitSet_){t.l=e.limit_;let n=e.viewFrom_;""===n&&(n=e.isViewFromLeft()?"l":"r"),t.vf=n}return e.index_!==e4&&(t.i=e.index_.toString()),t}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class tc extends eI{constructor(e,t,n,i){super(),this.repoInfo_=e,this.onDataUpdate_=t,this.authTokenProvider_=n,this.appCheckTokenProvider_=i,this.log_=L("p:rest:"),this.listens_={}}reportStats(e){throw Error("Method not implemented.")}static getListenId_(e,t){return void 0!==t?"tag$"+t:((0,y.hu)(e._queryParams.isDefault(),"should have a tag if it's not a default query."),e._path.toString())}listen(e,t,n,i){let s=e._path.toString();this.log_("Listen called for "+s+" "+e._queryIdentifier);let r=tc.getListenId_(e,n),o={};this.listens_[r]=o;let l=th(e._queryParams);this.restRequest_(s+".json",l,(e,t)=>{let l=t;404===e&&(l=null,e=null),null===e&&this.onDataUpdate_(s,l,!1,n),(0,y.DV)(this.listens_,r)===o&&i(e?401===e?"permission_denied":"rest_error:"+e:"ok",null)})}unlisten(e,t){let n=tc.getListenId_(e,t);delete this.listens_[n]}get(e){let t=th(e._queryParams),n=e._path.toString(),i=new y.BH;return this.restRequest_(n+".json",t,(e,t)=>{let s=t;404===e&&(s=null,e=null),null===e?(this.onDataUpdate_(n,s,!1,null),i.resolve(s)):i.reject(Error(s))}),i.promise}refreshAuthToken(e){}restRequest_(e,t={},n){return t.format="export",Promise.all([this.authTokenProvider_.getToken(!1),this.appCheckTokenProvider_.getToken(!1)]).then(([i,s])=>{i&&i.accessToken&&(t.auth=i.accessToken),s&&s.token&&(t.ac=s.token);let r=(this.repoInfo_.secure?"https://":"http://")+this.repoInfo_.host+e+"?ns="+this.repoInfo_.namespace+(0,y.xO)(t);this.log_("Sending REST request for "+r);let o=new XMLHttpRequest;o.onreadystatechange=()=>{if(n&&4===o.readyState){this.log_("REST Response for "+r+" received. status:",o.status,"response:",o.responseText);let e=null;if(o.status>=200&&o.status<300){try{e=(0,y.cI)(o.responseText)}catch(e){U("Failed to parse JSON response for "+r+": "+o.responseText)}n(null,e)}else 401!==o.status&&404!==o.status&&U("Got unsuccessful REST response for "+r+" Status: "+o.status),n(o.status);n=null}},o.open("GET",r,!0),o.send()})}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class td{constructor(){this.rootNode_=te.EMPTY_NODE}getNode(e){return this.rootNode_.getChild(e)}updateSnapshot(e,t){this.rootNode_=this.rootNode_.updateChild(e,t)}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function t_(){return{value:null,children:new Map}}function tp(e,t,n){null!==e.value?n(t,e.value):function(e,t){e.children.forEach((e,n)=>{t(n,e)})}(e,(e,i)=>{tp(i,new eS(t.toString()+"/"+e),n)})}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class tf{constructor(e){this.collection_=e,this.last_=null}get(){let e=this.collection_.get(),t=Object.assign({},e);return this.last_&&X(this.last_,(e,n)=>{t[e]=t[e]-n}),this.last_=e,t}}class tm{constructor(e,t){this.server_=t,this.statsToReport_={},this.statsListener_=new tf(e),en(this.reportStats_.bind(this),Math.floor(1e4+2e4*Math.random()))}reportStats_(){let e=this.statsListener_.get(),t={},n=!1;X(e,(e,i)=>{i>0&&(0,y.r3)(this.statsToReport_,e)&&(t[e]=i,n=!0)}),n&&this.server_.reportStats(t),en(this.reportStats_.bind(this),Math.floor(2*Math.random()*3e5))}}function tg(){return{fromUser:!0,fromServer:!1,queryId:null,tagged:!1}}function ty(){return{fromUser:!1,fromServer:!0,queryId:null,tagged:!1}}function tv(e){return{fromUser:!1,fromServer:!0,queryId:e,tagged:!0}}(p=f||(f={}))[p.OVERWRITE=0]="OVERWRITE",p[p.MERGE=1]="MERGE",p[p.ACK_USER_WRITE=2]="ACK_USER_WRITE",p[p.LISTEN_COMPLETE=3]="LISTEN_COMPLETE";/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class tC{constructor(e,t,n){this.path=e,this.affectedTree=t,this.revert=n,this.type=f.ACK_USER_WRITE,this.source=tg()}operationForChild(e){if(!eF(this.path))return(0,y.hu)(eN(this.path)===e,"operationForChild called for unrelated child."),new tC(eP(this.path),this.affectedTree,this.revert);if(null!=this.affectedTree.value)return(0,y.hu)(this.affectedTree.children.isEmpty(),"affectedTree should not have overlapping affected paths."),this;{let t=this.affectedTree.subtree(new eS(e));return new tC(eE(),t,this.revert)}}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class tw{constructor(e,t,n){this.source=e,this.path=t,this.snap=n,this.type=f.OVERWRITE}operationForChild(e){return eF(this.path)?new tw(this.source,eE(),this.snap.getImmediateChild(e)):new tw(this.source,eP(this.path),this.snap)}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class tT{constructor(e,t,n){this.source=e,this.path=t,this.children=n,this.type=f.MERGE}operationForChild(e){if(!eF(this.path))return(0,y.hu)(eN(this.path)===e,"Can't get a merge for a child not on the path of the operation"),new tT(this.source,eP(this.path),this.children);{let t=this.children.subtree(new eS(e));return t.isEmpty()?null:t.value?new tw(this.source,eE(),t.value):new tT(this.source,eE(),t)}}toString(){return"Operation("+this.path+": "+this.source.toString()+" merge: "+this.children.toString()+")"}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class tI{constructor(e,t,n){this.node_=e,this.fullyInitialized_=t,this.filtered_=n}isFullyInitialized(){return this.fullyInitialized_}isFiltered(){return this.filtered_}isCompleteForPath(e){if(eF(e))return this.isFullyInitialized()&&!this.filtered_;let t=eN(e);return this.isCompleteForChild(t)}isCompleteForChild(e){return this.isFullyInitialized()&&!this.filtered_||this.node_.hasChild(e)}getNode(){return this.node_}}function tk(e,t,n,i,s,r){let o=i.filter(e=>e.type===n);o.sort((t,n)=>(function(e,t,n){if(null==t.childName||null==n.childName)throw(0,y.g5)("Should only compare child_ events.");let i=new eY(t.childName,t.snapshotNode),s=new eY(n.childName,n.snapshotNode);return e.index_.compare(i,s)})(e,t,n)),o.forEach(n=>{let i=("value"===n.type||"child_removed"===n.type||(n.prevName=r.getPredecessorChildName(n.childName,n.snapshotNode,e.index_)),n);s.forEach(s=>{s.respondsTo(n.type)&&t.push(s.createEvent(i,e.query_))})})}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function tb(e,t){return{eventCache:e,serverCache:t}}function tS(e,t,n,i){return tb(new tI(t,n,i),e.serverCache)}function tE(e,t,n,i){return tb(e.eventCache,new tI(t,n,i))}function tN(e){return e.eventCache.isFullyInitialized()?e.eventCache.getNode():null}function tR(e){return e.serverCache.isFullyInitialized()?e.serverCache.getNode():null}let tP=()=>(u||(u=new eX(K)),u);class tx{constructor(e,t=tP()){this.value=e,this.children=t}static fromObject(e){let t=new tx(null);return X(e,(e,n)=>{t=t.set(new eS(e),n)}),t}isEmpty(){return null===this.value&&this.children.isEmpty()}findRootMostMatchingPathAndValue(e,t){if(null!=this.value&&t(this.value))return{path:eE(),value:this.value};if(eF(e))return null;{let n=eN(e),i=this.children.get(n);if(null===i)return null;{let s=i.findRootMostMatchingPathAndValue(eP(e),t);return null!=s?{path:eA(new eS(n),s.path),value:s.value}:null}}}findRootMostValueAndPath(e){return this.findRootMostMatchingPathAndValue(e,()=>!0)}subtree(e){if(eF(e))return this;{let t=eN(e),n=this.children.get(t);return null!==n?n.subtree(eP(e)):new tx(null)}}set(e,t){if(eF(e))return new tx(t,this.children);{let n=eN(e),i=(this.children.get(n)||new tx(null)).set(eP(e),t),s=this.children.insert(n,i);return new tx(this.value,s)}}remove(e){if(eF(e))return this.children.isEmpty()?new tx(null):new tx(null,this.children);{let t=eN(e),n=this.children.get(t);if(!n)return this;{let i;let s=n.remove(eP(e));return(i=s.isEmpty()?this.children.remove(t):this.children.insert(t,s),null===this.value&&i.isEmpty())?new tx(null):new tx(this.value,i)}}}get(e){if(eF(e))return this.value;{let t=eN(e),n=this.children.get(t);return n?n.get(eP(e)):null}}setTree(e,t){if(eF(e))return t;{let n;let i=eN(e),s=(this.children.get(i)||new tx(null)).setTree(eP(e),t);return n=s.isEmpty()?this.children.remove(i):this.children.insert(i,s),new tx(this.value,n)}}fold(e){return this.fold_(eE(),e)}fold_(e,t){let n={};return this.children.inorderTraversal((i,s)=>{n[i]=s.fold_(eA(e,i),t)}),t(e,this.value,n)}findOnPath(e,t){return this.findOnPath_(e,eE(),t)}findOnPath_(e,t,n){let i=!!this.value&&n(t,this.value);if(i)return i;if(eF(e))return null;{let i=eN(e),s=this.children.get(i);return s?s.findOnPath_(eP(e),eA(t,i),n):null}}foreachOnPath(e,t){return this.foreachOnPath_(e,eE(),t)}foreachOnPath_(e,t,n){if(eF(e))return this;{this.value&&n(t,this.value);let i=eN(e),s=this.children.get(i);return s?s.foreachOnPath_(eP(e),eA(t,i),n):new tx(null)}}foreach(e){this.foreach_(eE(),e)}foreach_(e,t){this.children.inorderTraversal((n,i)=>{i.foreach_(eA(e,n),t)}),this.value&&t(e,this.value)}foreachChild(e){this.children.inorderTraversal((t,n)=>{n.value&&e(t,n.value)})}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class tD{constructor(e){this.writeTree_=e}static empty(){return new tD(new tx(null))}}function tM(e,t,n){if(eF(t))return new tD(new tx(n));{let i=e.writeTree_.findRootMostValueAndPath(t);if(null!=i){let s=i.path,r=i.value,o=eO(s,t);return r=r.updateChild(o,n),new tD(e.writeTree_.set(s,r))}{let i=new tx(n);return new tD(e.writeTree_.setTree(t,i))}}}function tA(e,t,n){let i=e;return X(n,(e,n)=>{i=tM(i,eA(t,e),n)}),i}function tF(e,t){return eF(t)?tD.empty():new tD(e.writeTree_.setTree(t,new tx(null)))}function tO(e,t){return null!=tL(e,t)}function tL(e,t){let n=e.writeTree_.findRootMostValueAndPath(t);return null!=n?e.writeTree_.get(n.path).getChild(eO(n.path,t)):null}function tq(e){let t=[],n=e.writeTree_.value;return null!=n?n.isLeafNode()||n.forEachChild(e4,(e,n)=>{t.push(new eY(e,n))}):e.writeTree_.children.inorderTraversal((e,n)=>{null!=n.value&&t.push(new eY(e,n.value))}),t}function tW(e,t){if(eF(t))return e;{let n=tL(e,t);return new tD(null!=n?new tx(n):e.writeTree_.subtree(t))}}function tU(e){return e.writeTree_.isEmpty()}function tH(e,t){return function e(t,n,i){if(null!=n.value)return i.updateChild(t,n.value);{let s=null;return n.children.inorderTraversal((n,r)=>{".priority"===n?((0,y.hu)(null!==r.value,"Priority writes must always be leaf nodes"),s=r.value):i=e(eA(t,n),r,i)}),i.getChild(t).isEmpty()||null===s||(i=i.updateChild(eA(t,".priority"),s)),i}}(eE(),e.writeTree_,t)}function tj(e){return e.visible}function tz(e,t,n){let i=tD.empty();for(let s=0;s<e.length;++s){let r=e[s];if(t(r)){let e;let t=r.path;if(r.snap)eq(n,t)?i=tM(i,e=eO(n,t),r.snap):eq(t,n)&&(e=eO(t,n),i=tM(i,eE(),r.snap.getChild(e)));else if(r.children){if(eq(n,t))i=tA(i,e=eO(n,t),r.children);else if(eq(t,n)){if(eF(e=eO(t,n)))i=tA(i,eE(),r.children);else{let t=(0,y.DV)(r.children,eN(e));if(t){let n=t.getChild(eP(e));i=tM(i,eE(),n)}}}}else throw(0,y.g5)("WriteRecord should have .snap or .children")}}return i}function tY(e,t,n,i,s){if(i||s){let r=tW(e.visibleWrites,t);return!s&&tU(r)?n:s||null!=n||tO(r,eE())?tH(tz(e.allWrites,function(e){return(e.visible||s)&&(!i||!~i.indexOf(e.writeId))&&(eq(e.path,t)||eq(t,e.path))},t),n||te.EMPTY_NODE):null}{let i=tL(e.visibleWrites,t);if(null!=i)return i;{let i=tW(e.visibleWrites,t);return tU(i)?n:null!=n||tO(i,eE())?tH(i,n||te.EMPTY_NODE):null}}}function tV(e,t,n,i){return tY(e.writeTree,e.treePath,t,n,i)}function tB(e,t){return function(e,t,n){let i=te.EMPTY_NODE,s=tL(e.visibleWrites,t);if(s)return s.isLeafNode()||s.forEachChild(e4,(e,t)=>{i=i.updateImmediateChild(e,t)}),i;if(!n)return tq(tW(e.visibleWrites,t)).forEach(e=>{i=i.updateImmediateChild(e.name,e.node)}),i;{let s=tW(e.visibleWrites,t);return n.forEachChild(e4,(e,t)=>{let n=tH(tW(s,new eS(e)),t);i=i.updateImmediateChild(e,n)}),tq(s).forEach(e=>{i=i.updateImmediateChild(e.name,e.node)}),i}}(e.writeTree,e.treePath,t)}function tK(e,t,n,i){return function(e,t,n,i,s){(0,y.hu)(i||s,"Either existingEventSnap or existingServerSnap must exist");let r=eA(t,n);if(tO(e.visibleWrites,r))return null;{let t=tW(e.visibleWrites,r);return tU(t)?s.getChild(n):tH(t,s.getChild(n))}}(e.writeTree,e.treePath,t,n,i)}function tG(e,t){var n,i;return n=e.writeTree,i=eA(e.treePath,t),tL(n.visibleWrites,i)}function t$(e,t,n){return function(e,t,n,i){let s=eA(t,n),r=tL(e.visibleWrites,s);return null!=r?r:i.isCompleteForChild(n)?tH(tW(e.visibleWrites,s),i.getNode().getImmediateChild(n)):null}(e.writeTree,e.treePath,t,n)}function tQ(e,t){return tX(eA(e.treePath,t),e.writeTree)}function tX(e,t){return{treePath:e,writeTree:t}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class tJ{constructor(){this.changeMap=new Map}trackChildChange(e){let t=e.type,n=e.childName;(0,y.hu)("child_added"===t||"child_changed"===t||"child_removed"===t,"Only child changes supported for tracking"),(0,y.hu)(".priority"!==n,"Only non-priority child changes can be tracked.");let i=this.changeMap.get(n);if(i){let s=i.type;if("child_added"===t&&"child_removed"===s)this.changeMap.set(n,tl(n,e.snapshotNode,i.snapshotNode));else if("child_removed"===t&&"child_added"===s)this.changeMap.delete(n);else if("child_removed"===t&&"child_changed"===s)this.changeMap.set(n,{type:"child_removed",snapshotNode:i.oldSnap,childName:n});else if("child_changed"===t&&"child_added"===s)this.changeMap.set(n,{type:"child_added",snapshotNode:e.snapshotNode,childName:n});else if("child_changed"===t&&"child_changed"===s)this.changeMap.set(n,tl(n,e.snapshotNode,i.oldSnap));else throw(0,y.g5)("Illegal combination of changes: "+e+" occurred after "+i)}else this.changeMap.set(n,e)}getChanges(){return Array.from(this.changeMap.values())}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class tZ{getCompleteChild(e){return null}getChildAfterChild(e,t,n){return null}}let t0=new tZ;class t1{constructor(e,t,n=null){this.writes_=e,this.viewCache_=t,this.optCompleteServerCache_=n}getCompleteChild(e){let t=this.viewCache_.eventCache;if(t.isCompleteForChild(e))return t.getNode().getImmediateChild(e);{let t=null!=this.optCompleteServerCache_?new tI(this.optCompleteServerCache_,!0,!1):this.viewCache_.serverCache;return t$(this.writes_,e,t)}}getChildAfterChild(e,t,n){var i;let s=null!=this.optCompleteServerCache_?this.optCompleteServerCache_:tR(this.viewCache_),r=function(e,t,n,i,s,r,o){let l;let a=tW(e.visibleWrites,t),h=tL(a,eE());if(null!=h)l=h;else{if(null==n)return[];l=tH(a,n)}if((l=l.withIndex(o)).isEmpty()||l.isLeafNode())return[];{let e=[],t=o.getCompare(),n=r?l.getReverseIteratorFrom(i,o):l.getIteratorFrom(i,o),s=n.getNext();for(;s&&e.length<1;)0!==t(s,i)&&e.push(s),s=n.getNext();return e}}((i=this.writes_).writeTree,i.treePath,s,t,0,n,e);return 0===r.length?null:r[0]}}function t3(e,t,n,i,s,r){let o=t.eventCache;if(null!=tG(i,n))return t;{let l,a;if(eF(n)){if((0,y.hu)(t.serverCache.isFullyInitialized(),"If change path is empty, we must have complete server data"),t.serverCache.isFiltered()){let n=tR(t),s=tB(i,n instanceof te?n:te.EMPTY_NODE);l=e.filter.updateFullNode(t.eventCache.getNode(),s,r)}else{let n=tV(i,tR(t));l=e.filter.updateFullNode(t.eventCache.getNode(),n,r)}}else{let h=eN(n);if(".priority"===h){(0,y.hu)(1===eR(n),"Can't have a priority with additional path components");let s=o.getNode(),r=tK(i,n,s,a=t.serverCache.getNode());l=null!=r?e.filter.updatePriority(s,r):o.getNode()}else{let u;let c=eP(n);if(o.isCompleteForChild(h)){a=t.serverCache.getNode();let e=tK(i,n,o.getNode(),a);u=null!=e?o.getNode().getImmediateChild(h).updateChild(c,e):o.getNode().getImmediateChild(h)}else u=t$(i,h,t.serverCache);l=null!=u?e.filter.updateChild(o.getNode(),h,u,c,s,r):o.getNode()}}return tS(t,l,o.isFullyInitialized()||eF(n),e.filter.filtersNodes())}}function t2(e,t,n,i,s,r,o,l){let a;let h=t.serverCache,u=o?e.filter:e.filter.getIndexedFilter();if(eF(n))a=u.updateFullNode(h.getNode(),i,null);else if(u.filtersNodes()&&!h.isFiltered()){let e=h.getNode().updateChild(n,i);a=u.updateFullNode(h.getNode(),e,null)}else{let e=eN(n);if(!h.isCompleteForPath(n)&&eR(n)>1)return t;let s=eP(n),r=h.getNode().getImmediateChild(e).updateChild(s,i);a=".priority"===e?u.updatePriority(h.getNode(),r):u.updateChild(h.getNode(),e,r,s,t0,null)}let c=tE(t,a,h.isFullyInitialized()||eF(n),u.filtersNodes()),d=new t1(s,c,r);return t3(e,c,n,s,d,l)}function t4(e,t,n,i,s,r,o){let l,a;let h=t.eventCache,u=new t1(s,t,r);if(eF(n))a=e.filter.updateFullNode(t.eventCache.getNode(),i,o),l=tS(t,a,!0,e.filter.filtersNodes());else{let s=eN(n);if(".priority"===s)a=e.filter.updatePriority(t.eventCache.getNode(),i),l=tS(t,a,h.isFullyInitialized(),h.isFiltered());else{let r;let a=eP(n),c=h.getNode().getImmediateChild(s);if(eF(a))r=i;else{let e=u.getCompleteChild(s);r=null!=e?".priority"===ex(a)&&e.getChild(eM(a)).isEmpty()?e:e.updateChild(a,i):te.EMPTY_NODE}l=c.equals(r)?t:tS(t,e.filter.updateChild(h.getNode(),s,r,a,u,o),h.isFullyInitialized(),e.filter.filtersNodes())}}return l}function t5(e,t){return e.eventCache.isCompleteForChild(t)}function t8(e,t,n){return n.foreach((e,n)=>{t=t.updateChild(e,n)}),t}function t7(e,t,n,i,s,r,o,l){let a;if(t.serverCache.getNode().isEmpty()&&!t.serverCache.isFullyInitialized())return t;let h=t;a=eF(n)?i:new tx(null).setTree(n,i);let u=t.serverCache.getNode();return a.children.inorderTraversal((n,i)=>{if(u.hasChild(n)){let a=t8(e,t.serverCache.getNode().getImmediateChild(n),i);h=t2(e,h,new eS(n),a,s,r,o,l)}}),a.children.inorderTraversal((n,i)=>{let a=!t.serverCache.isCompleteForChild(n)&&null===i.value;if(!u.hasChild(n)&&!a){let a=t8(e,t.serverCache.getNode().getImmediateChild(n),i);h=t2(e,h,new eS(n),a,s,r,o,l)}}),h}function t6(e,t,n,i){var s,r;t.type===f.MERGE&&null!==t.source.queryId&&((0,y.hu)(tR(e.viewCache_),"We should always have a full cache before handling merges"),(0,y.hu)(tN(e.viewCache_),"Missing event cache, even though we have a server cache"));let o=e.viewCache_,l=function(e,t,n,i,s){let r,o;let l=new tJ;if(n.type===f.OVERWRITE)n.source.fromUser?r=t4(e,t,n.path,n.snap,i,s,l):((0,y.hu)(n.source.fromServer,"Unknown source."),o=n.source.tagged||t.serverCache.isFiltered()&&!eF(n.path),r=t2(e,t,n.path,n.snap,i,s,o,l));else if(n.type===f.MERGE){var a,h;let u;n.source.fromUser?(a=n.path,h=n.children,u=t,h.foreach((n,r)=>{let o=eA(a,n);t5(t,eN(o))&&(u=t4(e,u,o,r,i,s,l))}),h.foreach((n,r)=>{let o=eA(a,n);t5(t,eN(o))||(u=t4(e,u,o,r,i,s,l))}),r=u):((0,y.hu)(n.source.fromServer,"Unknown source."),o=n.source.tagged||t.serverCache.isFiltered(),r=t7(e,t,n.path,n.children,i,s,o,l))}else if(n.type===f.ACK_USER_WRITE)r=n.revert?function(e,t,n,i,s,r){let o;if(null!=tG(i,n))return t;{let l;let a=new t1(i,t,s),h=t.eventCache.getNode();if(eF(n)||".priority"===eN(n)){let n;if(t.serverCache.isFullyInitialized())n=tV(i,tR(t));else{let e=t.serverCache.getNode();(0,y.hu)(e instanceof te,"serverChildren would be complete if leaf node"),n=tB(i,e)}l=e.filter.updateFullNode(h,n,r)}else{let s=eN(n),u=t$(i,s,t.serverCache);null==u&&t.serverCache.isCompleteForChild(s)&&(u=h.getImmediateChild(s)),(l=null!=u?e.filter.updateChild(h,s,u,eP(n),a,r):t.eventCache.getNode().hasChild(s)?e.filter.updateChild(h,s,te.EMPTY_NODE,eP(n),a,r):h).isEmpty()&&t.serverCache.isFullyInitialized()&&(o=tV(i,tR(t))).isLeafNode()&&(l=e.filter.updateFullNode(l,o,r))}return o=t.serverCache.isFullyInitialized()||null!=tG(i,eE()),tS(t,l,o,e.filter.filtersNodes())}}(e,t,n.path,i,s,l):function(e,t,n,i,s,r,o){if(null!=tG(s,n))return t;let l=t.serverCache.isFiltered(),a=t.serverCache;if(null!=i.value){if(eF(n)&&a.isFullyInitialized()||a.isCompleteForPath(n))return t2(e,t,n,a.getNode().getChild(n),s,r,l,o);if(!eF(n))return t;{let i=new tx(null);return a.getNode().forEachChild(eK,(e,t)=>{i=i.set(new eS(e),t)}),t7(e,t,n,i,s,r,l,o)}}{let h=new tx(null);return i.foreach((e,t)=>{let i=eA(n,e);a.isCompleteForPath(i)&&(h=h.set(e,a.getNode().getChild(i)))}),t7(e,t,n,h,s,r,l,o)}}(e,t,n.path,n.affectedTree,i,s,l);else if(n.type===f.LISTEN_COMPLETE)r=function(e,t,n,i,s){let r=t.serverCache;return t3(e,tE(t,r.getNode(),r.isFullyInitialized()||eF(n),r.isFiltered()),n,i,t0,s)}(e,t,n.path,i,l);else throw(0,y.g5)("Unknown operation type: "+n.type);let u=l.getChanges();return function(e,t,n){let i=t.eventCache;if(i.isFullyInitialized()){let s=i.getNode().isLeafNode()||i.getNode().isEmpty(),r=tN(e);!(n.length>0)&&e.eventCache.isFullyInitialized()&&(!s||i.getNode().equals(r))&&i.getNode().getPriority().equals(r.getPriority())||n.push({type:"value",snapshotNode:tN(t)})}}(t,r,u),{viewCache:r,changes:u}}(e.processor_,o,t,n,i);return s=e.processor_,r=l.viewCache,(0,y.hu)(r.eventCache.getNode().isIndexed(s.filter.getIndex()),"Event snap not indexed"),(0,y.hu)(r.serverCache.getNode().isIndexed(s.filter.getIndex()),"Server snap not indexed"),(0,y.hu)(l.viewCache.serverCache.isFullyInitialized()||!o.serverCache.isFullyInitialized(),"Once a server snap is complete, it should never go back"),e.viewCache_=l.viewCache,function(e,t,n,i){let s=e.eventRegistrations_;return function(e,t,n,i){let s=[],r=[];return t.forEach(t=>{if("child_changed"===t.type&&e.index_.indexedValueChanged(t.oldSnap,t.snapshotNode)){var n;r.push((n=t.childName,{type:"child_moved",snapshotNode:t.snapshotNode,childName:n}))}}),tk(e,s,"child_removed",t,i,n),tk(e,s,"child_added",t,i,n),tk(e,s,"child_moved",r,i,n),tk(e,s,"child_changed",t,i,n),tk(e,s,"value",t,i,n),s}(e.eventGenerator_,t,n,s)}(e,l.changes,l.viewCache.eventCache.getNode(),0)}function t9(e,t,n,i){let s=t.source.queryId;if(null!==s){let r=e.views.get(s);return(0,y.hu)(null!=r,"SyncTree gave us an op for an invalid query."),t6(r,t,n,i)}{let s=[];for(let r of e.views.values())s=s.concat(t6(r,t,n,i));return s}}function ne(e,t){let n=null;for(let i of e.views.values())n=n||function(e,t){let n=tR(e.viewCache_);return n&&(e.query._queryParams.loadsAllData()||!eF(t)&&!n.getImmediateChild(eN(t)).isEmpty())?n.getChild(t):null}(i,t);return n}class nt{constructor(e){this.listenProvider_=e,this.syncPointTree_=new tx(null),this.pendingWriteTree_={visibleWrites:tD.empty(),allWrites:[],lastWriteId:-1},this.tagToQueryMap=new Map,this.queryToTagMap=new Map}}function nn(e,t,n=!1){let i=function(e,t){for(let n=0;n<e.allWrites.length;n++){let i=e.allWrites[n];if(i.writeId===t)return i}return null}(e.pendingWriteTree_,t);if(!function(e,t){let n=e.allWrites.findIndex(e=>e.writeId===t);(0,y.hu)(n>=0,"removeWrite called with nonexistent writeId.");let i=e.allWrites[n];e.allWrites.splice(n,1);let s=i.visible,r=!1,o=e.allWrites.length-1;for(;s&&o>=0;){let t=e.allWrites[o];t.visible&&(o>=n&&function(e,t){if(e.snap)return eq(e.path,t);for(let n in e.children)if(e.children.hasOwnProperty(n)&&eq(eA(e.path,n),t))return!0;return!1}(t,i.path)?s=!1:eq(i.path,t.path)&&(r=!0)),o--}return!!s&&(r?(e.visibleWrites=tz(e.allWrites,tj,eE()),e.allWrites.length>0?e.lastWriteId=e.allWrites[e.allWrites.length-1].writeId:e.lastWriteId=-1):i.snap?e.visibleWrites=tF(e.visibleWrites,i.path):X(i.children,t=>{e.visibleWrites=tF(e.visibleWrites,eA(i.path,t))}),!0)}(e.pendingWriteTree_,t))return[];{let t=new tx(null);return null!=i.snap?t=t.set(eE(),!0):X(i.children,e=>{t=t.set(new eS(e),!0)}),nr(e,new tC(i.path,t,n))}}function ni(e,t,n){return nr(e,new tw(ty(),t,n))}function ns(e,t,n){let i=e.pendingWriteTree_,s=e.syncPointTree_.findOnPath(t,(e,n)=>{let i=ne(n,eO(e,t));if(i)return i});return tY(i,t,s,n,!0)}function nr(e,t){var n;return function e(t,n,i,s){if(eF(t.path))return function e(t,n,i,s){let r=n.get(eE());null==i&&null!=r&&(i=ne(r,eE()));let o=[];return n.children.inorderTraversal((n,r)=>{let l=i?i.getImmediateChild(n):null,a=tQ(s,n),h=t.operationForChild(n);h&&(o=o.concat(e(h,r,l,a)))}),r&&(o=o.concat(t9(r,t,s,i))),o}(t,n,i,s);{let r=n.get(eE());null==i&&null!=r&&(i=ne(r,eE()));let o=[],l=eN(t.path),a=t.operationForChild(l),h=n.children.get(l);if(h&&a){let t=i?i.getImmediateChild(l):null,n=tQ(s,l);o=o.concat(e(a,h,t,n))}return r&&(o=o.concat(t9(r,t,s,i))),o}}(t,e.syncPointTree_,null,(n=e.pendingWriteTree_,tX(eE(),n)))}function no(e,t){return e.tagToQueryMap.get(t)}function nl(e){let t=e.indexOf("$");return(0,y.hu)(-1!==t&&t<e.length-1,"Bad queryKey."),{queryId:e.substr(t+1),path:new eS(e.substr(0,t))}}function na(e,t,n){let i=e.syncPointTree_.get(t);return(0,y.hu)(i,"Missing sync point for query tag that we're tracking"),t9(i,n,tX(t,e.pendingWriteTree_),null)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class nh{constructor(e){this.node_=e}getImmediateChild(e){return new nh(this.node_.getImmediateChild(e))}node(){return this.node_}}class nu{constructor(e,t){this.syncTree_=e,this.path_=t}getImmediateChild(e){let t=eA(this.path_,e);return new nu(this.syncTree_,t)}node(){return ns(this.syncTree_,this.path_)}}let nc=function(e,t,n){return e&&"object"==typeof e?((0,y.hu)(".sv"in e,"Unexpected leaf node or priority contents"),"string"==typeof e[".sv"])?nd(e[".sv"],t,n):"object"==typeof e[".sv"]?n_(e[".sv"],t):void(0,y.hu)(!1,"Unexpected server value: "+JSON.stringify(e,null,2)):e},nd=function(e,t,n){if("timestamp"===e)return n.timestamp;(0,y.hu)(!1,"Unexpected server value: "+e)},n_=function(e,t,n){e.hasOwnProperty("increment")||(0,y.hu)(!1,"Unexpected server value: "+JSON.stringify(e,null,2));let i=e.increment;"number"!=typeof i&&(0,y.hu)(!1,"Unexpected increment value: "+i);let s=t.node();if((0,y.hu)(null!=s,"Expected ChildrenNode.EMPTY_NODE for nulls"),!s.isLeafNode())return i;let r=s.getValue();return"number"!=typeof r?i:r+i};function np(e,t,n){let i;let s=nc(e.getPriority().val(),t.getImmediateChild(".priority"),n);if(!e.isLeafNode())return i=e,s!==e.getPriority().val()&&(i=i.updatePriority(new e3(s))),e.forEachChild(e4,(e,s)=>{let r=np(s,t.getImmediateChild(e),n);r!==s&&(i=i.updateImmediateChild(e,r))}),i;{let i=nc(e.getValue(),t,n);return i!==e.getValue()||s!==e.getPriority().val()?new e3(i,ti(s)):e}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class nf{constructor(e="",t=null,n={children:{},childCount:0}){this.name=e,this.parent=t,this.node=n}}function nm(e,t){let n=t instanceof eS?t:new eS(t),i=e,s=eN(n);for(;null!==s;){let e=(0,y.DV)(i.node.children,s)||{children:{},childCount:0};i=new nf(s,i,e),s=eN(n=eP(n))}return i}function ng(e){return e.node.value}function ny(e,t){e.node.value=t,function e(t){null!==t.parent&&function(t,n,i){let s=void 0===ng(i)&&!nv(i),r=(0,y.r3)(t.node.children,n);s&&r?(delete t.node.children[n],t.node.childCount--,e(t)):s||r||(t.node.children[n]=i.node,t.node.childCount++,e(t))}(t.parent,t.name,t)}(e)}function nv(e){return e.node.childCount>0}function nC(e,t){X(e.node.children,(n,i)=>{t(new nf(n,e,i))})}function nw(e){return new eS(null===e.parent?e.name:nw(e.parent)+"/"+e.name)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let nT=/[\[\].#$\/\u0000-\u001F\u007F]/,nI=/[\[\].#$\u0000-\u001F\u007F]/,nk=function(e){return"string"==typeof e&&0!==e.length&&!nT.test(e)},nb=function(e){var t;return e&&(e=e.replace(/^\/*\.info(\/|$)/,"/")),"string"==typeof(t=e)&&0!==t.length&&!nI.test(t)},nS=function(e,t,n){let i=n instanceof eS?new eW(n,e):n;if(void 0===t)throw Error(e+"contains undefined "+eH(i));if("function"==typeof t)throw Error(e+"contains a function "+eH(i)+" with contents = "+t.toString());if(j(t))throw Error(e+"contains "+t.toString()+" "+eH(i));if("string"==typeof t&&t.length>3495253.**********&&(0,y.ug)(t)>10485760)throw Error(e+"contains a string greater than 10485760 utf8 bytes "+eH(i)+" ('"+t.substring(0,50)+"...')");if(t&&"object"==typeof t){let n=!1,s=!1;if(X(t,(t,r)=>{if(".value"===t)n=!0;else if(".priority"!==t&&".sv"!==t&&(s=!0,!nk(t)))throw Error(e+" contains an invalid key ("+t+") "+eH(i)+'.  Keys must be non-empty strings and can\'t contain ".", "#", "$", "/", "[", or "]"');i.parts_.length>0&&(i.byteLength_+=1),i.parts_.push(t),i.byteLength_+=(0,y.ug)(t),eU(i),nS(e,r,i),function(e){let t=e.parts_.pop();e.byteLength_-=(0,y.ug)(t),e.parts_.length>0&&(e.byteLength_-=1)}(i)}),n&&s)throw Error(e+' contains ".value" child '+eH(i)+" in addition to actual children.")}},nE=function(e,t){let n=t.path.toString();if("string"!=typeof t.repoInfo.host||0===t.repoInfo.host.length||!nk(t.repoInfo.namespace)&&"localhost"!==t.repoInfo.host.split(":")[0]||0!==n.length&&!nb(n))throw Error((0,y.gK)(e,"url")+'must be a valid firebase URL and the path can\'t contain ".", "#", "$", "[", or "]".')};/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class nN{constructor(){this.eventLists_=[],this.recursionDepth_=0}}function nR(e,t,n){!function(e,t){let n=null;for(let i=0;i<t.length;i++){let s=t[i],r=s.getPath();null===n||eL(r,n.path)||(e.eventLists_.push(n),n=null),null===n&&(n={events:[],path:r}),n.events.push(s)}n&&e.eventLists_.push(n)}(e,n),function(e,t){e.recursionDepth_++;let n=!0;for(let i=0;i<e.eventLists_.length;i++){let s=e.eventLists_[i];s&&(t(s.path)?(function(e){for(let t=0;t<e.events.length;t++){let n=e.events[t];if(null!==n){e.events[t]=null;let i=n.getEventRunner();M&&O("event: "+n.toString()),et(i)}}}(e.eventLists_[i]),e.eventLists_[i]=null):n=!1)}n&&(e.eventLists_=[]),e.recursionDepth_--}(e,e=>eq(e,t)||eq(t,e))}class nP{constructor(e,t,n,i){this.repoInfo_=e,this.forceRestClient_=t,this.authTokenProvider_=n,this.appCheckProvider_=i,this.dataUpdateCount=0,this.statsListener_=null,this.eventQueue_=new nN,this.nextWriteId_=1,this.interceptServerDataCallback_=null,this.onDisconnect_=t_(),this.transactionQueueTree_=new nf,this.persistentConnection_=null,this.key=this.repoInfo_.toURLString()}toString(){return(this.repoInfo_.secure?"https://":"http://")+this.repoInfo_.host}}function nx(e){var t;return(t=t={timestamp:function(e){let t=e.infoData_.getNode(new eS(".info/serverTimeOffset")).val()||0;return new Date().getTime()+t}(e)}).timestamp=t.timestamp||new Date().getTime(),t}function nD(e,t,n,i,s){e.dataUpdateCount++;let r=new eS(t);n=e.interceptServerDataCallback_?e.interceptServerDataCallback_(t,n):n;let o=[];if(s){if(i){let t=(0,y.UI)(n,e=>ti(e));o=function(e,t,n,i){let s=no(e,i);if(!s)return[];{let i=nl(s),r=i.path,o=i.queryId,l=eO(r,t),a=tx.fromObject(n);return na(e,r,new tT(tv(o),l,a))}}(e.serverSyncTree_,r,t,s)}else{let t=ti(n);o=function(e,t,n,i){let s=no(e,i);if(null==s)return[];{let i=nl(s),r=i.path,o=i.queryId,l=eO(r,t);return na(e,r,new tw(tv(o),l,n))}}(e.serverSyncTree_,r,t,s)}}else if(i){let t=(0,y.UI)(n,e=>ti(e));o=function(e,t,n){let i=tx.fromObject(n);return nr(e,new tT(ty(),t,i))}(e.serverSyncTree_,r,t)}else{let t=ti(n);o=ni(e.serverSyncTree_,r,t)}let l=r;o.length>0&&(l=nL(e,r)),nR(e.eventQueue_,l,o)}function nM(e,t){nA(e,"connected",t),!1===t&&function(e){nF(e,"onDisconnectEvents");let t=nx(e),n=t_();tp(e.onDisconnect_,eE(),(i,s)=>{let r=np(s,new nu(e.serverSyncTree_,i),t);!function e(t,n,i){if(eF(n))t.value=i,t.children.clear();else if(null!==t.value)t.value=t.value.updateChild(n,i);else{let s=eN(n);t.children.has(s)||t.children.set(s,t_()),e(t.children.get(s),n=eP(n),i)}}(n,i,r)});let i=[];tp(n,eE(),(t,n)=>{i=i.concat(ni(e.serverSyncTree_,t,n));let s=function(e,t){let n=nw(nq(e,t)),i=nm(e.transactionQueueTree_,t);return function(e,t,n){let i=e.parent;for(;null!==i;){if(t(i))return!0;i=i.parent}}(i,t=>{nH(e,t)}),nH(e,i),function e(t,n,i,s){i&&!s&&n(t),nC(t,t=>{e(t,n,!0,s)}),i&&s&&n(t)}(i,t=>{nH(e,t)}),n}(e,t);nL(e,s)}),e.onDisconnect_=t_(),nR(e.eventQueue_,eE(),i)}(e)}function nA(e,t,n){let i=new eS("/.info/"+t),s=ti(n);e.infoData_.updateSnapshot(i,s);let r=ni(e.infoSyncTree_,i,s);nR(e.eventQueue_,i,r)}function nF(e,...t){let n="";e.persistentConnection_&&(n=e.persistentConnection_.id+":"),O(n,...t)}function nO(e,t,n){return ns(e.serverSyncTree_,t,n)||te.EMPTY_NODE}function nL(e,t){let n=nq(e,t),i=nw(n),s=nW(e,n);return function(e,t,n){if(0===t.length)return;let i=[],s=[],r=t.filter(e=>0===e.status).map(e=>e.currentWriteId);for(let o=0;o<t.length;o++){let l=t[o],a=eO(n,l.path),h=!1,u;if((0,y.hu)(null!==a,"rerunTransactionsUnderNode_: relativePath should not be null."),4===l.status)h=!0,u=l.abortReason,s=s.concat(nn(e.serverSyncTree_,l.currentWriteId,!0));else if(0===l.status){if(l.retryCount>=25)h=!0,u="maxretry",s=s.concat(nn(e.serverSyncTree_,l.currentWriteId,!0));else{let n=nO(e,l.path,r);l.currentInputSnapshot=n;let i=t[o].update(n.val());if(void 0!==i){nS("transaction failed: Data returned ",i,l.path);let t=ti(i);"object"==typeof i&&null!=i&&(0,y.r3)(i,".priority")||(t=t.updatePriority(n.getPriority()));let o=l.currentWriteId,a=nx(e),h=np(t,new nh(n),a);l.currentOutputSnapshotRaw=t,l.currentOutputSnapshotResolved=h,l.currentWriteId=e.nextWriteId_++,r.splice(r.indexOf(o),1),s=(s=s.concat(function(e,t,n,i,s){var r,o;return(r=e.pendingWriteTree_,o=s,(0,y.hu)(i>r.lastWriteId,"Stacking an older write on top of newer ones"),void 0===o&&(o=!0),r.allWrites.push({path:t,snap:n,writeId:i,visible:o}),o&&(r.visibleWrites=tM(r.visibleWrites,t,n)),r.lastWriteId=i,s)?nr(e,new tw(tg(),t,n)):[]}(e.serverSyncTree_,l.path,h,l.currentWriteId,l.applyLocally))).concat(nn(e.serverSyncTree_,o,!0))}else h=!0,u="nodata",s=s.concat(nn(e.serverSyncTree_,l.currentWriteId,!0))}}nR(e.eventQueue_,n,s),s=[],h&&(t[o].status=2,setTimeout(t[o].unwatcher,Math.floor(0)),t[o].onComplete&&("nodata"===u?i.push(()=>t[o].onComplete(null,!1,t[o].currentInputSnapshot)):i.push(()=>t[o].onComplete(Error(u),!1,null))))}nU(e,e.transactionQueueTree_);for(let e=0;e<i.length;e++)et(i[e]);(function e(t,n=t.transactionQueueTree_){if(n||nU(t,n),ng(n)){let i=nW(t,n);(0,y.hu)(i.length>0,"Sending zero length transaction queue"),i.every(e=>0===e.status)&&function(t,n,i){let s=nO(t,n,i.map(e=>e.currentWriteId)),r=s,o=s.hash();for(let e=0;e<i.length;e++){let t=i[e];(0,y.hu)(0===t.status,"tryToSendTransactionQueue_: items in queue should all be run."),t.status=1,t.retryCount++;let s=eO(n,t.path);r=r.updateChild(s,t.currentOutputSnapshotRaw)}let l=r.val(!0);t.server_.put(n.toString(),l,s=>{nF(t,"transaction put response",{path:n.toString(),status:s});let r=[];if("ok"===s){let s=[];for(let e=0;e<i.length;e++)i[e].status=2,r=r.concat(nn(t.serverSyncTree_,i[e].currentWriteId)),i[e].onComplete&&s.push(()=>i[e].onComplete(null,!0,i[e].currentOutputSnapshotResolved)),i[e].unwatcher();nU(t,nm(t.transactionQueueTree_,n)),e(t,t.transactionQueueTree_),nR(t.eventQueue_,n,r);for(let e=0;e<s.length;e++)et(s[e])}else{if("datastale"===s)for(let e=0;e<i.length;e++)3===i[e].status?i[e].status=4:i[e].status=0;else{U("transaction at "+n.toString()+" failed: "+s);for(let e=0;e<i.length;e++)i[e].status=4,i[e].abortReason=s}nL(t,n)}},o)}(t,nw(n),i)}else nv(n)&&nC(n,n=>{e(t,n)})})(e,e.transactionQueueTree_)}(e,s,i),i}function nq(e,t){let n;let i=e.transactionQueueTree_;for(n=eN(t);null!==n&&void 0===ng(i);)i=nm(i,n),n=eN(t=eP(t));return i}function nW(e,t){let n=[];return function e(t,n,i){let s=ng(n);if(s)for(let e=0;e<s.length;e++)i.push(s[e]);nC(n,n=>{e(t,n,i)})}(e,t,n),n.sort((e,t)=>e.order-t.order),n}function nU(e,t){let n=ng(t);if(n){let e=0;for(let t=0;t<n.length;t++)2!==n[t].status&&(n[e]=n[t],e++);n.length=e,ny(t,n.length>0?n:void 0)}nC(t,t=>{nU(e,t)})}function nH(e,t){let n=ng(t);if(n){let i=[],s=[],r=-1;for(let t=0;t<n.length;t++)3===n[t].status||(1===n[t].status?((0,y.hu)(r===t-1,"All SENT items should be at beginning of queue."),r=t,n[t].status=3,n[t].abortReason="set"):((0,y.hu)(0===n[t].status,"Unexpected transaction status in abort"),n[t].unwatcher(),s=s.concat(nn(e.serverSyncTree_,n[t].currentWriteId,!0)),n[t].onComplete&&i.push(n[t].onComplete.bind(null,Error("set"),!1,null))));-1===r?ny(t,void 0):n.length=r+1,nR(e.eventQueue_,nw(t),s);for(let e=0;e<i.length;e++)et(i[e])}}let nj=function(e,t){let n=nz(e),i=n.namespace;"firebase.com"===n.domain&&W(n.host+" is no longer supported. Please use <YOUR FIREBASE>.firebaseio.com instead"),i&&"undefined"!==i||"localhost"===n.domain||W("Cannot parse Firebase url. Please use https://<YOUR FIREBASE>.firebaseio.com"),n.secure||H();let s="ws"===n.scheme||"wss"===n.scheme;return{repoInfo:new eh(n.host,n.secure,i,s,t,"",i!==n.subdomain),path:new eS(n.pathString)}},nz=function(e){let t="",n="",i="",s="",r="",o=!0,l="https",a=443;if("string"==typeof e){let h=e.indexOf("//");h>=0&&(l=e.substring(0,h-1),e=e.substring(h+2));let u=e.indexOf("/");-1===u&&(u=e.length);let c=e.indexOf("?");-1===c&&(c=e.length),t=e.substring(0,Math.min(u,c)),u<c&&(s=/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function(e){let t="",n=e.split("/");for(let e=0;e<n.length;e++)if(n[e].length>0){let i=n[e];try{i=decodeURIComponent(i.replace(/\+/g," "))}catch(e){}t+="/"+i}return t}(e.substring(u,c)));let d=function(e){let t={};for(let n of("?"===e.charAt(0)&&(e=e.substring(1)),e.split("&"))){if(0===n.length)continue;let i=n.split("=");2===i.length?t[decodeURIComponent(i[0])]=decodeURIComponent(i[1]):U(`Invalid query segment '${n}' in query '${e}'`)}return t}(e.substring(Math.min(e.length,c)));(h=t.indexOf(":"))>=0?(o="https"===l||"wss"===l,a=parseInt(t.substring(h+1),10)):h=t.length;let _=t.slice(0,h);if("localhost"===_.toLowerCase())n="localhost";else if(_.split(".").length<=2)n=_;else{let e=t.indexOf(".");i=t.substring(0,e).toLowerCase(),n=t.substring(e+1),r=i}"ns"in d&&(r=d.ns)}return{host:t,port:a,domain:n,subdomain:i,secure:o,scheme:l,pathString:s,namespace:r}};/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class nY{constructor(e,t,n,i){this._repo=e,this._path=t,this._queryParams=n,this._orderByCalled=i}get key(){return eF(this._path)?null:ex(this._path)}get ref(){return new nV(this._repo,this._path)}get _queryIdentifier(){let e=$(tu(this._queryParams));return"{}"===e?"default":e}get _queryObject(){return tu(this._queryParams)}isEqual(e){if(!((e=(0,y.m9)(e))instanceof nY))return!1;let t=this._repo===e._repo,n=eL(this._path,e._path),i=this._queryIdentifier===e._queryIdentifier;return t&&n&&i}toJSON(){return this.toString()}toString(){return this._repo.toString()+function(e){let t="";for(let n=e.pieceNum_;n<e.pieces_.length;n++)""!==e.pieces_[n]&&(t+="/"+encodeURIComponent(String(e.pieces_[n])));return t||"/"}(this._path)}}class nV extends nY{constructor(e,t){super(e,t,new ta,!1)}get parent(){let e=eM(this._path);return null===e?null:new nV(this._repo,e)}get root(){let e=this;for(;null!==e.parent;)e=e.parent;return e}}(0,y.hu)(!c,"__referenceConstructor has already been defined"),c=nV,(0,y.hu)(!d,"__referenceConstructor has already been defined"),d=nV;let nB={};class nK{constructor(e,t){this._repoInternal=e,this.app=t,this.type="database",this._instanceStarted=!1}get _repo(){return this._instanceStarted||(function(e,t,n){if(e.stats_=ep(e.repoInfo_),e.forceRestClient_||("object"==typeof window&&window.navigator&&window.navigator.userAgent||"").search(/googlebot|google webmaster tools|bingbot|yahoo! slurp|baiduspider|yandexbot|duckduckbot/i)>=0)e.server_=new tc(e.repoInfo_,(t,n,i,s)=>{nD(e,t,n,i,s)},e.authTokenProvider_,e.appCheckProvider_),setTimeout(()=>nM(e,!0),0);else{if(null!=n){if("object"!=typeof n)throw Error("Only objects are supported for option databaseAuthVariableOverride");try{(0,y.Wl)(n)}catch(e){throw Error("Invalid authOverride provided: "+e)}}e.persistentConnection_=new ez(e.repoInfo_,t,(t,n,i,s)=>{nD(e,t,n,i,s)},t=>{nM(e,t)},t=>{X(t,(t,n)=>{nA(e,t,n)})},e.authTokenProvider_,e.appCheckProvider_,n),e.server_=e.persistentConnection_}e.authTokenProvider_.addTokenChangeListener(t=>{e.server_.refreshAuthToken(t)}),e.appCheckProvider_.addTokenChangeListener(t=>{e.server_.refreshAppCheckToken(t.token)}),e.statsReporter_=function(e,t){let n=e.toString();return e_[n]||(e_[n]=t()),e_[n]}(e.repoInfo_,()=>new tm(e.stats_,e.server_)),e.infoData_=new td,e.infoSyncTree_=new nt({startListening:(t,n,i,s)=>{let r=[],o=e.infoData_.getNode(t._path);return o.isEmpty()||(r=ni(e.infoSyncTree_,t._path,o),setTimeout(()=>{s("ok")},0)),r},stopListening:()=>{}}),nA(e,"connected",!1),e.serverSyncTree_=new nt({startListening:(t,n,i,s)=>(e.server_.listen(t,i,n,(n,i)=>{let r=s(n,i);nR(e.eventQueue_,t._path,r)}),[]),stopListening:(t,n)=>{e.server_.unlisten(t,n)}})}(this._repoInternal,this.app.options.appId,this.app.options.databaseAuthVariableOverride),this._instanceStarted=!0),this._repoInternal}get _root(){return this._rootInternal||(this._rootInternal=new nV(this._repo,eE())),this._rootInternal}_delete(){return null!==this._rootInternal&&(function(e,t){let n=nB[t];n&&n[e.key]===e||W(`Database ${t}(${e.repoInfo_}) has already been deleted.`),e.persistentConnection_&&e.persistentConnection_.interrupt("repo_interrupt"),delete n[e.key]}(this._repo,this.app.name),this._repoInternal=null,this._rootInternal=null),Promise.resolve()}_checkNotDeleted(e){null===this._rootInternal&&W("Cannot call "+e+" on a deleted database.")}}function nG(e=(0,m.Mq)(),t){let n=(0,m.qX)(e,"database").getImmediate({identifier:t});if(!n._instanceStarted){let e=(0,y.P0)("database");e&&function(e,t,n,i={}){var s;let r;(e=(0,y.m9)(e))._checkNotDeleted("useEmulator"),e._instanceStarted&&W("Cannot call useEmulator() after instance has already been initialized.");let o=e._repoInternal;o.repoInfo_.nodeAdmin?(i.mockUserToken&&W('mockUserToken is not supported by the Admin SDK. For client access with mock users, please use the "firebase" package instead of "firebase-admin".'),r=new er(er.OWNER)):i.mockUserToken&&(r=new er("string"==typeof i.mockUserToken?i.mockUserToken:(0,y.Sg)(i.mockUserToken,e.app.options.projectId))),s=r,o.repoInfo_=new eh(`${t}:${n}`,!1,o.repoInfo_.namespace,o.repoInfo_.webSocketOnly,o.repoInfo_.nodeAdmin,o.repoInfo_.persistenceKey,o.repoInfo_.includeNamespaceInQueryParams,!0),s&&(o.authTokenProvider_=s)}(n,...e)}return n}ez.prototype.simpleListen=function(e,t){this.sendRequest("q",{p:e},t)},ez.prototype.echo=function(e,t){this.sendRequest("echo",{d:e},t)},I=m.Jn,(0,m.Xd)(new g.wA("database",(e,{instanceIdentifier:t})=>(function(e,t,n,i,s){var r,o;let l,a,h,u,c=i||e.options.databaseURL;void 0===c&&(e.options.projectId||W("Can't determine Firebase Database URL. Be sure to include  a Project ID when calling firebase.initializeApp()."),O("Using default host for project ",e.options.projectId),c=`${e.options.projectId}-default-rtdb.firebaseio.com`);let d=nj(c,void 0),_=d.repoInfo;void 0!==C&&C.env&&(h=C.env.FIREBASE_DATABASE_EMULATOR_HOST),h?(u=!0,_=(d=nj(c=`http://${h}?ns=${_.namespace}`,void 0)).repoInfo):u=!d.repoInfo.secure;let p=new es(e.name,e.options,t);return nE("Invalid Firebase Database URL",d),eF(d.path)||W("Database URL must point to the root of a Firebase Database (not including a child path)."),new nK((r=_,o=new ei(e.name,n),(l=nB[e.name])||(l={},nB[e.name]=l),(a=l[r.toURLString()])&&W("Database initialized multiple times. Please make sure the format of the database URL matches with each database() call."),a=new nP(r,!1,p,o),l[r.toURLString()]=a,a),e)})(e.getProvider("app").getImmediate(),e.getProvider("auth-internal"),e.getProvider("app-check-internal"),t),"PUBLIC").setMultipleInstances(!0)),(0,m.KN)(w,T,void 0),(0,m.KN)(w,T,"esm2017")}}]);