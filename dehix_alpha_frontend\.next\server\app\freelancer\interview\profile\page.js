(()=>{var e={};e.id=379,e.ids=[379],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},46568:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>o,routeModule:()=>h,tree:()=>d}),s(47874),s(54302),s(12523);var r=s(23191),a=s(88716),n=s(37922),i=s.n(n),l=s(95231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let d=["",{children:["freelancer",{children:["interview",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,47874)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\interview\\profile\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\interview\\profile\\page.tsx"],u="/freelancer/interview/profile/page",m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/freelancer/interview/profile/page",pathname:"/freelancer/interview/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},60517:(e,t,s)=>{Promise.resolve().then(s.bind(s,31784))},88295:function(e){var t;t=function(){"use strict";var e="millisecond",t="second",s="minute",r="hour",a="week",n="month",i="quarter",l="year",c="date",d="Invalid Date",o=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,u=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,m=function(e,t,s){var r=String(e);return!r||r.length>=t?e:""+Array(t+1-r.length).join(s)+e},h="en",p={};p[h]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],s=e%100;return"["+e+(t[(s-20)%10]||t[s]||"th")+"]"}};var x="$isDayjsObject",f=function(e){return e instanceof y||!(!e||!e[x])},v=function e(t,s,r){var a;if(!t)return h;if("string"==typeof t){var n=t.toLowerCase();p[n]&&(a=n),s&&(p[n]=s,a=n);var i=t.split("-");if(!a&&i.length>1)return e(i[0])}else{var l=t.name;p[l]=t,a=l}return!r&&a&&(h=a),a||!r&&h},j=function(e,t){if(f(e))return e.clone();var s="object"==typeof t?t:{};return s.date=e,s.args=arguments,new y(s)},g={s:m,z:function(e){var t=-e.utcOffset(),s=Math.abs(t);return(t<=0?"+":"-")+m(Math.floor(s/60),2,"0")+":"+m(s%60,2,"0")},m:function e(t,s){if(t.date()<s.date())return-e(s,t);var r=12*(s.year()-t.year())+(s.month()-t.month()),a=t.clone().add(r,n),i=s-a<0,l=t.clone().add(r+(i?-1:1),n);return+(-(r+(s-a)/(i?a-l:l-a))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(d){return({M:n,y:l,w:a,d:"day",D:c,h:r,m:s,s:t,ms:e,Q:i})[d]||String(d||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};g.l=v,g.i=f,g.w=function(e,t){return j(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var y=function(){function m(e){this.$L=v(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[x]=!0}var h=m.prototype;return h.parse=function(e){this.$d=function(e){var t=e.date,s=e.utc;if(null===t)return new Date(NaN);if(g.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var r=t.match(o);if(r){var a=r[2]-1||0,n=(r[7]||"0").substring(0,3);return s?new Date(Date.UTC(r[1],a,r[3]||1,r[4]||0,r[5]||0,r[6]||0,n)):new Date(r[1],a,r[3]||1,r[4]||0,r[5]||0,r[6]||0,n)}}return new Date(t)}(e),this.init()},h.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},h.$utils=function(){return g},h.isValid=function(){return this.$d.toString()!==d},h.isSame=function(e,t){var s=j(e);return this.startOf(t)<=s&&s<=this.endOf(t)},h.isAfter=function(e,t){return j(e)<this.startOf(t)},h.isBefore=function(e,t){return this.endOf(t)<j(e)},h.$g=function(e,t,s){return g.u(e)?this[t]:this.set(s,e)},h.unix=function(){return Math.floor(this.valueOf()/1e3)},h.valueOf=function(){return this.$d.getTime()},h.startOf=function(e,i){var d=this,o=!!g.u(i)||i,u=g.p(e),m=function(e,t){var s=g.w(d.$u?Date.UTC(d.$y,t,e):new Date(d.$y,t,e),d);return o?s:s.endOf("day")},h=function(e,t){return g.w(d.toDate()[e].apply(d.toDate("s"),(o?[0,0,0,0]:[23,59,59,999]).slice(t)),d)},p=this.$W,x=this.$M,f=this.$D,v="set"+(this.$u?"UTC":"");switch(u){case l:return o?m(1,0):m(31,11);case n:return o?m(1,x):m(0,x+1);case a:var j=this.$locale().weekStart||0,y=(p<j?p+7:p)-j;return m(o?f-y:f+(6-y),x);case"day":case c:return h(v+"Hours",0);case r:return h(v+"Minutes",1);case s:return h(v+"Seconds",2);case t:return h(v+"Milliseconds",3);default:return this.clone()}},h.endOf=function(e){return this.startOf(e,!1)},h.$set=function(a,i){var d,o=g.p(a),u="set"+(this.$u?"UTC":""),m=((d={}).day=u+"Date",d[c]=u+"Date",d[n]=u+"Month",d[l]=u+"FullYear",d[r]=u+"Hours",d[s]=u+"Minutes",d[t]=u+"Seconds",d[e]=u+"Milliseconds",d)[o],h="day"===o?this.$D+(i-this.$W):i;if(o===n||o===l){var p=this.clone().set(c,1);p.$d[m](h),p.init(),this.$d=p.set(c,Math.min(this.$D,p.daysInMonth())).$d}else m&&this.$d[m](h);return this.init(),this},h.set=function(e,t){return this.clone().$set(e,t)},h.get=function(e){return this[g.p(e)]()},h.add=function(e,i){var c,d=this;e=Number(e);var o=g.p(i),u=function(t){var s=j(d);return g.w(s.date(s.date()+Math.round(t*e)),d)};if(o===n)return this.set(n,this.$M+e);if(o===l)return this.set(l,this.$y+e);if("day"===o)return u(1);if(o===a)return u(7);var m=((c={})[s]=6e4,c[r]=36e5,c[t]=1e3,c)[o]||1,h=this.$d.getTime()+e*m;return g.w(h,this)},h.subtract=function(e,t){return this.add(-1*e,t)},h.format=function(e){var t=this,s=this.$locale();if(!this.isValid())return s.invalidDate||d;var r=e||"YYYY-MM-DDTHH:mm:ssZ",a=g.z(this),n=this.$H,i=this.$m,l=this.$M,c=s.weekdays,o=s.months,m=s.meridiem,h=function(e,s,a,n){return e&&(e[s]||e(t,r))||a[s].slice(0,n)},p=function(e){return g.s(n%12||12,e,"0")},x=m||function(e,t,s){var r=e<12?"AM":"PM";return s?r.toLowerCase():r};return r.replace(u,function(e,r){return r||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return g.s(t.$y,4,"0");case"M":return l+1;case"MM":return g.s(l+1,2,"0");case"MMM":return h(s.monthsShort,l,o,3);case"MMMM":return h(o,l);case"D":return t.$D;case"DD":return g.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return h(s.weekdaysMin,t.$W,c,2);case"ddd":return h(s.weekdaysShort,t.$W,c,3);case"dddd":return c[t.$W];case"H":return String(n);case"HH":return g.s(n,2,"0");case"h":return p(1);case"hh":return p(2);case"a":return x(n,i,!0);case"A":return x(n,i,!1);case"m":return String(i);case"mm":return g.s(i,2,"0");case"s":return String(t.$s);case"ss":return g.s(t.$s,2,"0");case"SSS":return g.s(t.$ms,3,"0");case"Z":return a}return null}(e)||a.replace(":","")})},h.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},h.diff=function(e,c,d){var o,u=this,m=g.p(c),h=j(e),p=(h.utcOffset()-this.utcOffset())*6e4,x=this-h,f=function(){return g.m(u,h)};switch(m){case l:o=f()/12;break;case n:o=f();break;case i:o=f()/3;break;case a:o=(x-p)/6048e5;break;case"day":o=(x-p)/864e5;break;case r:o=x/36e5;break;case s:o=x/6e4;break;case t:o=x/1e3;break;default:o=x}return d?o:g.a(o)},h.daysInMonth=function(){return this.endOf(n).$D},h.$locale=function(){return p[this.$L]},h.locale=function(e,t){if(!e)return this.$L;var s=this.clone(),r=v(e,t,!0);return r&&(s.$L=r),s},h.clone=function(){return g.w(this.$d,this)},h.toDate=function(){return new Date(this.valueOf())},h.toJSON=function(){return this.isValid()?this.toISOString():null},h.toISOString=function(){return this.$d.toISOString()},h.toString=function(){return this.$d.toUTCString()},m}(),w=y.prototype;return j.prototype=w,[["$ms",e],["$s",t],["$m",s],["$H",r],["$W","day"],["$M",n],["$y",l],["$D",c]].forEach(function(e){w[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),j.extend=function(e,t){return e.$i||(e(t,y,j),e.$i=!0),j},j.locale=v,j.isDayjs=f,j.unix=function(e){return j(1e3*e)},j.en=p[h],j.Ls=p,j.p={},j},e.exports=t()},47546:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},31784:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>B});var r=s(10326),a=s(17577),n=s.n(a),i=s(25842),l=s(40588),c=s(92166),d=s(30325),o=s(74723),u=s(74064),m=s(27256),h=s(97685),p=s(83855),x=s(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let f=(0,x.Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),v=(0,x.Z)("Pen",[["path",{d:"M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z",key:"5qss01"}]]);var j=s(56627),g=s(91664),y=s(41156),w=s(6260),N=s(38227),b=s(38443);function S({onClick:e,variant:t="ghost",icon:s,...a}){return r.jsx(g.z,{variant:t,size:"icon",className:"h-4 py-4",onClick:e,...a,children:s})}var $=s(24118),D=s(29280),k=s(41190);let M=m.z.object({name:m.z.string().min(1,"Domain is required"),experience:m.z.preprocess(e=>parseFloat(e),m.z.number().min(0,"Experience must be a non-negative number").max(50,"Experience can't exceed 50")),level:m.z.string().min(1,"Level is required")}),_=({open:e,onClose:t,onSubmit:s,domainOptions:a,levels:n,defaultValues:i,loading:l})=>{let{handleSubmit:c,control:d,reset:m,formState:{errors:h}}=(0,o.cI)({resolver:(0,u.F)(M),defaultValues:i});return r.jsx($.Vq,{open:e,onOpenChange:t,children:(0,r.jsxs)($.cZ,{children:[(0,r.jsxs)($.fK,{children:[r.jsx($.$N,{children:i?"Edit Domain":"Add Domain"}),r.jsx($.Be,{children:"Select a domain and provide your experience and level."})]}),(0,r.jsxs)("form",{onSubmit:c(e=>{s(e),m(),t()}),children:[(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(o.Qr,{control:d,name:"name",render:({field:e})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(D.Ph,{onValueChange:e.onChange,value:e.value,children:[r.jsx(D.i4,{className:"w-full",children:r.jsx(D.ki,{placeholder:"Select a domain"})}),r.jsx(D.Bw,{children:a.length>0?a.map((e,t)=>r.jsx(D.Ql,{value:e.talentName,children:e.talentName},t)):(0,r.jsxs)("p",{className:"p-2",children:["No verified Domain."," ",r.jsx("span",{className:"text-blue-500",children:"Get verified !"})]})})]}),h.name&&r.jsx("p",{className:"text-red-500 text-sm",children:h.name.message})]})}),r.jsx(o.Qr,{control:d,name:"experience",render:({field:e})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"col-span-3 relative",children:[r.jsx(k.I,{...e,placeholder:"Years of experience",type:"number",min:"0",step:"0.1",className:"w-full pl-2 pr-1"}),r.jsx("span",{className:"absolute right-8 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"YEARS"})]}),h.experience&&r.jsx("p",{className:"text-red-500 text-sm",children:h.experience.message})]})}),r.jsx(o.Qr,{control:d,name:"level",render:({field:e})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(D.Ph,{onValueChange:e.onChange,value:e.value,children:[r.jsx(D.i4,{children:r.jsx(D.ki,{placeholder:"Select level"})}),r.jsx(D.Bw,{children:n.map((e,t)=>r.jsx(D.Ql,{value:e,children:e},t))})]}),h.level&&r.jsx("p",{className:"text-red-500 text-sm",children:h.level.message})]})})]}),r.jsx($.cN,{children:(0,r.jsxs)(g.z,{className:"mt-3",type:"submit",disabled:l,children:[i?"Update":"Add"," Domain"]})})]})]})})};var q=s(54423);let C=m.z.object({name:m.z.string().min(1,"Skill is required"),experience:m.z.preprocess(e=>parseFloat(e),m.z.number().min(0,"Experience must be a non-negative number").max(50,"Experience can't exceed 50")),level:m.z.string().min(1,"Level is required")}),Y=({open:e,onClose:t,onSubmit:s,skillOptions:a,levels:n,defaultValues:i,loading:l})=>{let{handleSubmit:c,control:d,reset:m,formState:{errors:h}}=(0,o.cI)({resolver:(0,u.F)(C),defaultValues:i});return r.jsx($.Vq,{open:e,onOpenChange:t,children:(0,r.jsxs)($.cZ,{children:[(0,r.jsxs)($.fK,{children:[r.jsx($.$N,{children:i?"Edit Skill":"Add Skill"}),r.jsx($.Be,{children:"Select a skill and provide your experience and level."})]}),(0,r.jsxs)("form",{onSubmit:c(e=>{s(e),m(),t()}),children:[(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(o.Qr,{control:d,name:"name",render:({field:e})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(D.Ph,{onValueChange:e.onChange,value:e.value,children:[r.jsx(D.i4,{className:"w-full",children:r.jsx(D.ki,{placeholder:"Select a skill"})}),r.jsx(D.Bw,{children:a.length>0?a.map((e,t)=>r.jsx(D.Ql,{value:e.talentName,children:e.talentName},t)):(0,r.jsxs)("p",{className:"p-2",children:["No verified skills."," ",r.jsx("span",{className:"text-blue-500",children:"Get verified !"})]})})]}),h.name&&r.jsx("p",{className:"text-red-500 text-sm",children:h.name.message})]})}),r.jsx(o.Qr,{control:d,name:"experience",render:({field:e})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"col-span-3 relative",children:[r.jsx(k.I,{...e,placeholder:"Years of experience",type:"number",min:"0",step:"0.1",className:"w-full pl-2 pr-1"}),r.jsx("span",{className:"absolute right-8 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"YEARS"})]}),h.experience&&r.jsx("p",{className:"text-red-500 text-sm",children:h.experience.message})]})}),r.jsx(o.Qr,{control:d,name:"level",render:({field:e})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(D.Ph,{onValueChange:e.onChange,value:e.value,children:[r.jsx(D.i4,{children:r.jsx(D.ki,{placeholder:"Select level"})}),r.jsx(D.Bw,{children:n.map((e,t)=>r.jsx(D.Ql,{value:e,children:e},t))})]}),h.level&&r.jsx("p",{className:"text-red-500 text-sm",children:h.level.message})]})})]}),r.jsx($.cN,{children:(0,r.jsxs)(g.z,{className:"mt-3",type:"submit",disabled:l,children:[i?"Update":"Add"," Skill"]})})]})]})})};var O=s(88295),A=s.n(O),I=s(35047),z=s(44794),F=s(84097);let P=function({isOpen:e,onClose:t,doc_id:s,doc_type:n}){(0,i.v9)(e=>e.user);let l=(0,I.useRouter)(),c=(0,I.useSearchParams)(),[d,o]=(0,a.useState)(""),[u,m]=(0,a.useState)(""),[h,p]=(0,a.useState)(A()().add(1,"day").format("YYYY-MM-DD")),[x,f]=(0,a.useState)(A()().add(1,"day").add(1,"hour").format("YYYY-MM-DD")),v=(0,a.useState)([""]),[j,y]=(0,a.useState)([]),N=async e=>{let t=Object.fromEntries(c.entries());t.code?await b(e,t.code):S()},b=async(e,t)=>{await w.b.post("/meeting",e,{params:{code:t}})},S=async()=>{try{let e=window.location.origin+window.location.pathname,t=(await w.b.get("/meeting/auth-url",{params:{redirectUri:e}})).data.url;t&&l.push(t)}catch(e){console.error("Error fetching Google Auth URL:",e),(0,F.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}},D=(e,t,s)=>{let r=A()(t).set("hour",parseInt(s.split(":")[0])).set("minute",parseInt(s.split(":")[1])).format("YYYY-MM-DDTHH:mm");"start"===e?p(r):f(r)},M=e=>{A()(e).isBefore(A()(h))?f(A()(h).add(1,"hour").format("YYYY-MM-DDTHH:mm")):f(e)};return r.jsx($.Vq,{open:e,onOpenChange:t,children:(0,r.jsxs)($.cZ,{className:"sm:max-w-[425px]",children:[(0,r.jsxs)($.fK,{children:[r.jsx($.$N,{children:"Create a Meeting"}),r.jsx($.Be,{children:"Fill in the details below to schedule a new meeting."})]}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),N({summary:d,description:u,start:{dateTime:A()(h).toISOString(),timeZone:"Asia/Kolkata"},end:{dateTime:A()(x).toISOString(),timeZone:"Asia/Kolkata"},attendees:v})},className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[r.jsx(z.Label,{htmlFor:"summary",className:"text-right",children:"Summary"}),r.jsx(k.I,{id:"summary",value:d,onChange:e=>o(e.target.value),className:"col-span-3",placeholder:"Meeting Summary",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[r.jsx(z.Label,{htmlFor:"description",className:"text-right",children:"Description"}),r.jsx(k.I,{id:"description",value:u,onChange:e=>m(e.target.value),className:"col-span-3",placeholder:"Meeting Description",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[r.jsx(z.Label,{htmlFor:"start-date",className:"text-right",children:"Start Date"}),r.jsx(k.I,{type:"date",value:A()(h).format("YYYY-MM-DD"),onChange:e=>p(e.target.value+"T"+A()(h).format("HH:mm")),className:"col-span-3",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[r.jsx(z.Label,{htmlFor:"end-date",className:"text-right",children:"End Date"}),r.jsx(k.I,{type:"date",value:A()(x).format("YYYY-MM-DD"),onChange:e=>M(e.target.value+"T"+A()(x).format("HH:mm")),className:"col-span-3",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[r.jsx(z.Label,{htmlFor:"start-time",className:"text-right",children:"Start Time"}),r.jsx(k.I,{type:"time",className:"col-span-3",value:A()(h).format("HH:mm"),onChange:e=>D("start",A()(h).format("YYYY-MM-DD"),e.target.value),required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[r.jsx(z.Label,{htmlFor:"end-time",className:"text-right",children:"End Time"}),r.jsx(k.I,{type:"time",className:"col-span-3",value:A()(x).format("HH:mm"),onChange:e=>D("end",A()(x).format("YYYY-MM-DD"),e.target.value),required:!0})]}),r.jsx($.cN,{className:"flex justify-center",children:r.jsx(g.z,{type:"submit",children:"Create Meeting"})})]})]})})};var E=s(10143),H=s(51027);let L=["Mastery","Proficient","Beginner"],Z="Pending",T=m.z.object({skill:m.z.string().min(1,"Skill is required"),experience:m.z.preprocess(e=>parseFloat(e),m.z.number().min(0,"Experience must be a non-negative number").max(50,"Experience can't exceed 50")),level:m.z.string().min(1,"Level is required")}),V=m.z.object({domain:m.z.string().min(1,"Domain is required"),experience:m.z.preprocess(e=>parseFloat(e),m.z.number().min(0,"Experience must be a non-negative number").max(50,"Experience can't exceed 50")),level:m.z.string().min(1,"Level is required")}),U=({freelancerId:e})=>{let t=(0,i.v9)(e=>e.user),[s,l]=(0,a.useState)([]),[c,d]=(0,a.useState)([]),[m,x]=(0,a.useState)([]),[$,D]=(0,a.useState)([]),[k,M]=(0,a.useState)(!1),[C,O]=(0,a.useState)(!1),[A,I]=(0,a.useState)(!1),[z,F]=(0,a.useState)(null),[U,B]=(0,a.useState)(null),[Q,W]=(0,a.useState)(!1),[G,R]=(0,a.useState)(),[J,K]=(0,a.useState)(),[X,ee]=n().useState("All");(0,a.useEffect)(()=>{(async function(){M(!0);try{let s=await w.b.get(`/freelancer/${t.uid}/skill`),r=await w.b.get(`/freelancer/${e}/domain`),a=s.data.data[0].skills,n=r.data.data[0].domain;x(a),D(n);let i=await w.b.get(`/freelancer/${t.uid}/dehix-talent`),c=i.data.data.skills.filter(e=>!a.some(t=>t.name===e.talentName));l(c);let o=i.data.data.domains.filter(e=>!n.some(t=>t.name===e.talentName));d(o)}catch(e){console.error("Error fetching data:",e),(0,j.Am)({variant:"destructive",title:"Error",description:"Failed to fetch data. Please try again later."})}finally{M(!1)}})()},[e,t?.uid]);let{reset:et}=(0,o.cI)({resolver:(0,u.F)(T)}),{reset:es}=(0,o.cI)({resolver:(0,u.F)(V)}),er=async e=>{M(!0);try{let t={skills:[{name:e.name,level:e.level,experience:e.experience,interviewPermission:"NOT_VERIFIED"}]};if(z){let s={...z,...e,interviewStatus:Z},r=await w.b.put("/freelancer/skill",t);if(200===r.status){let t=m.map(e=>e._id===z._id?{...e,...s}:e);x(t),(0,j.Am)({title:"Skill Updated",description:`${e.name} skill updated successfully.`})}else throw Error("Failed to update skill")}else{let s=await w.b.put("/freelancer/skill",t);if(200===s.status)x([...m,{name:e.name,experience:e.experience,level:e.level,interviewStatus:Z}]),(0,j.Am)({title:"Skill Added",description:`${e.name} skill added successfully.`});else throw Error("Failed to add skill");x([...m,{name:e.name,experience:e.experience,level:e.level,interviewStatus:Z}]),(0,j.Am)({title:"Skill Added",description:`${e.name} skill added successfully.`})}et(),O(!1)}finally{M(!1)}},ea=async e=>{M(!0);try{if(U){let t={...U,...e},s=await w.b.put("/freelancer/domain",t);if(200===s.status){let s=$.map(e=>e._id===U._id?{...e,...t}:e);D(s),(0,j.Am)({title:"Domain Updated",description:`${e.name} domain updated successfully.`})}else throw Error("Failed to update domain")}else D([...$,{name:e.name,experience:e.experience,level:e.level,interviewStatus:Z}]),(0,j.Am)({title:"Domain Added",description:`${e.name} domain added successfully.`});es(),I(!1)}finally{M(!1)}},en=(e,t)=>{console.log("Opening dialog for",t,"with ID:",e?._id),W(!0),R(e?._id),K(t)};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-8 ml-5",children:[r.jsx("h1",{className:"text-3xl font-bold",children:"Interview Profile"}),r.jsx("p",{className:"text-gray-400 mt-2",children:"Manage and track your skills and domains. Add new skills or domains and provide your experience levels."})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-4 p-2 sm:px-6 sm:py-0 md:gap-8  pt-2 pl-4 sm:pt-4 sm:pl-6 md:pt-6 md:pl-8 min-h-screen relative",children:[r.jsx("div",{className:"w-1/5",children:(0,r.jsxs)(E.h_,{children:[r.jsx(E.$F,{asChild:!0,children:(0,r.jsxs)(g.z,{variant:"outline",size:"sm",className:"h-7 gap-1 w-auto text-sm",children:[r.jsx(h.Z,{className:"h-3.5 w-3.5"}),r.jsx("span",{className:"sr-only sm:not-sr-only",children:"Filter"})]})}),(0,r.jsxs)(E.AW,{align:"end",children:[r.jsx(E.Ju,{children:"Filter by"}),r.jsx(E.VD,{}),r.jsx(E.bO,{checked:"All"===X,onSelect:()=>ee("All"),children:"All"}),r.jsx(E.bO,{checked:"Skills"===X,onSelect:()=>ee("Skills"),children:"Skills"}),r.jsx(E.bO,{checked:"Domain"===X,onSelect:()=>ee("Domain"),children:"Domain"})]})]})}),(0,r.jsxs)("div",{className:"w-full relative border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h2",{className:"text-xs md:text-xl font-semibold w-1/2",children:"Skills & Domains"}),(0,r.jsxs)("div",{className:"flex justify-end items-center  w-1/2",children:[(0,r.jsxs)(g.z,{onClick:()=>O(!0),className:"mr-2 md:px-4 md:py-2 py-1 px-1.5 text-xs md:text-sm",children:[r.jsx(p.Z,{className:" mr-1 md:mr-2 h-4 w-4"})," ",r.jsx("span",{className:"hidden  md:block mr-1",children:"Add"})," Skill"]}),(0,r.jsxs)(g.z,{onClick:()=>I(!0),className:"mr-2 md:px-4 md:py-2 py-1 px-1.5 text-xs md:text-sm",children:[r.jsx(p.Z,{className:" mr-1 md:mr-2 h-4 w-4"})," ",r.jsx("span",{className:"hidden md:block mr-1",children:"Add"})," Domain"]})]}),r.jsx(Y,{open:C,onClose:()=>O(!1),onSubmit:er,skillOptions:s,levels:L,defaultValues:z||void 0,loading:k}),r.jsx(_,{open:A,onClose:()=>I(!1),onSubmit:ea,domainOptions:c,levels:L,defaultValues:U||void 0,loading:k})]}),(0,r.jsxs)(y.iA,{children:[r.jsx(y.xD,{children:(0,r.jsxs)(y.SC,{className:"hover:bg-[#09090B]",children:[r.jsx(y.ss,{className:"",children:"Item"}),r.jsx(y.ss,{className:"",children:"Level"}),r.jsx(y.ss,{className:"",children:"Experience"}),r.jsx(y.ss,{className:"",children:"Status"}),r.jsx(y.ss,{className:"",children:(0,r.jsxs)("div",{className:"flex gap-2 items-center",children:["Actions",(0,r.jsxs)(H.J2,{children:[r.jsx(H.xo,{asChild:!0,children:r.jsx(f,{className:"w-4 h-4 text-muted-foreground  cursor-pointer"})}),r.jsx(H.yk,{className:"text-sm w-fit  border rounded p-2 shadow",children:"This will be available in the next phase."})]})]})})]})}),r.jsx(y.RM,{children:k?[void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsxs)(y.SC,{children:[r.jsx(y.pj,{className:"",children:r.jsx(N.O,{className:"h-6 w-24"})}),r.jsx(y.pj,{className:"",children:r.jsx(N.O,{className:"h-6 w-24"})}),r.jsx(y.pj,{className:"",children:r.jsx(N.O,{className:"h-6 w-24"})}),r.jsx(y.pj,{className:"",children:r.jsx(N.O,{className:"h-6 w-20 rounded-full"})}),r.jsx(y.pj,{className:"",children:r.jsx(N.O,{className:"w-8 h-8 p-2 rounded-md"})})]},t)):("All"===X?[...m,...$]:"Skills"===X?m:"Domain"===X?$:void 0).map(e=>(0,r.jsxs)(y.SC,{children:[r.jsx(y.pj,{className:"",children:e.name}),r.jsx(y.pj,{className:"",children:e.level}),r.jsx(y.pj,{className:"",children:"number"==typeof e.experience&&e.experience>0?e.experience+" years":""}),r.jsx(y.pj,{className:"",children:r.jsx(b.C,{className:(0,q.S)(e.interviewStatus),children:e?.interviewStatus?.toUpperCase()})}),r.jsx(y.pj,{className:"flex items-center gap-2",children:r.jsx(S,{icon:r.jsx(v,{className:"w-4 h-4 text-gray-400 cursor-not-allowed"}),disabled:!0,onClick:()=>en(e,e.experience?"SKILL":"DOMAIN")})})]},e._id))})]})]})]}),r.jsx(P,{isOpen:Q,onClose:()=>W(!1),doc_id:G||"",doc_type:J||""})]})};function B(){let e=(0,i.v9)(e=>e.user);return(0,r.jsxs)("div",{className:"flex min-h-screen w-full",children:[r.jsx(c.Z,{menuItemsTop:d.y,menuItemsBottom:d.$,active:"Profile"}),(0,r.jsxs)("div",{className:"flex flex-col sm:py-2 sm:pl-14 mb-8 w-full",children:[r.jsx(l.Z,{menuItemsTop:d.y,menuItemsBottom:d.$,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Interview",link:"#"}]}),r.jsx(U,{freelancerId:e?.uid})]})]})}},44794:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Label:()=>d});var r=s(10326),a=s(17577),n=s(34478),i=s(28671),l=s(51223);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...t},s)=>r.jsx(n.f,{ref:s,className:(0,l.cn)(c(),e),...t}));d.displayName=n.f.displayName},38227:(e,t,s)=>{"use strict";s.d(t,{O:()=>n});var r=s(10326),a=s(51223);function n({className:e,...t}){return r.jsx("div",{className:(0,a.cn)("animate-pulse rounded-md bg-primary/10",e),...t})}},30325:(e,t,s)=>{"use strict";s.d(t,{$:()=>h,y:()=>m});var r=s(10326),a=s(95920),n=s(94909),i=s(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,i.Z)("ListVideo",[["path",{d:"M12 12H3",key:"18klou"}],["path",{d:"M16 6H3",key:"1wxfjs"}],["path",{d:"M12 18H3",key:"11ftsu"}],["path",{d:"m16 12 5 3-5 3v-6Z",key:"zpskkp"}]]);var c=s(47546);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,i.Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var o=s(88378),u=s(46226);let m=[{href:"#",icon:r.jsx(u.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:r.jsx(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/interview/profile",icon:r.jsx(n.Z,{className:"h-5 w-5"}),label:"Profile"},{href:"/freelancer/interview/current",icon:r.jsx(l,{className:"h-5 w-5"}),label:"Current"},{href:"/freelancer/interview/bids",icon:r.jsx(c.Z,{className:"h-5 w-5"}),label:"Bids"},{href:"/freelancer/interview/history",icon:r.jsx(d,{className:"h-5 w-5"}),label:"History"}],h=[{href:"/freelancer/settings/personal-info",icon:r.jsx(o.Z,{className:"h-5 w-5"}),label:"Settings"}]},47874:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>l});var r=s(68570);let a=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\interview\profile\page.tsx`),{__esModule:n,$$typeof:i}=a;a.default;let l=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\interview\profile\page.tsx#default`)},34478:(e,t,s)=>{"use strict";s.d(t,{f:()=>l});var r=s(17577),a=s(77335),n=s(10326),i=r.forwardRef((e,t)=>(0,n.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,6686,4736,6499,8066,588],()=>s(46568));module.exports=r})();