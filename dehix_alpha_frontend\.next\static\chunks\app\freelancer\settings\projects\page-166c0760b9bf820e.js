(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5154],{92196:function(e,t,r){Promise.resolve().then(r.bind(r,62490))},6540:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},25912:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},24241:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},87055:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},40036:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("ImagePlus",[["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7",key:"31hg93"}],["line",{x1:"16",x2:"22",y1:"5",y2:"5",key:"ez7e4s"}],["line",{x1:"19",x2:"19",y1:"2",y2:"8",key:"1gkr8c"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},47390:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},67524:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},62490:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return R}});var s=r(57437),n=r(11444),a=r(2265),l=r(64797),i=r(15922),c=r(87055),o=r(47390),d=r(68515),u=r(79055),m=r(48185),h=e=>{let{projectName:t,description:r,verified:n,githubLink:a,start:l,end:i,refer:h,techUsed:x,role:f,projectType:j,comments:p}=e;return(0,s.jsxs)(m.Zb,{className:"w-full h-full mx-auto md:max-w-2xl ",children:[(0,s.jsxs)(m.Ol,{children:[(0,s.jsxs)(m.ll,{className:"flex",children:[t,a&&(0,s.jsx)("div",{className:"ml-auto",children:(0,s.jsx)("a",{href:a,className:"text-sm text-white underline",target:"_blank",rel:"noopener noreferrer",children:(0,s.jsx)(c.Z,{})})})]}),(0,s.jsx)(m.SZ,{className:"block mt-1 uppercase tracking-wide leading-tight font-medium text-white",children:j})]}),(0,s.jsxs)(m.aY,{children:[n?(0,s.jsx)(u.C,{className:"bg-success hover:bg-success",children:"VERIFIED"}):(0,s.jsx)(u.C,{className:"bg-warning hover:bg-warning",children:"PENDING"}),(0,s.jsx)("p",{className:"text-gray-300 pt-4",children:r}),(0,s.jsxs)("p",{className:"mt-2 flex text-gray-500 border p-3 rounded",children:[(0,s.jsx)(o.Z,{className:"pr-1"}),p]}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Reference: ",h]})}),(0,s.jsx)("div",{className:"my-4",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Role: ",f]})}),x.map((e,t)=>(0,s.jsx)(u.C,{className:"uppercase mx-1 text-xs font-normal bg-gray-300",children:e},t))]}),(0,s.jsx)(m.eW,{className:"flex",children:(0,s.jsx)(d.Z,{startDate:l,endDate:i})})]})},x=r(31014),f=r(39343),j=r(59772),p=r(92513),g=r(74697),v=r(95137),y=r(71976),b=r(16463),N=r(55384),S=r(54662),D=r(89733),w=r(93363),I=r(77209),k=r(78068),E=r(2128),A=r(9437);let Z=j.z.object({projectName:j.z.string().min(1,{message:"Project name is required."}),description:j.z.string().min(1,{message:"Description is required."}),githubLink:j.z.string().url({message:"GitHub Repositry link must be a valid URL."}).optional().refine(e=>!e||e.startsWith("https://github.com/"),{message:"GitHub repository URL must start with https://github.com/"}),start:j.z.string().min(1,{message:"Start date is required."}),end:j.z.string().min(1,{message:"End date is required."}),refer:j.z.string().min(1,{message:"Reference is required."}),techUsed:j.z.array(j.z.string()).min(1,{message:"At least one technology is required."}),role:j.z.string().min(1,{message:"Role is required."}),projectType:j.z.string().optional(),verificationStatus:j.z.string().optional(),comments:j.z.string().optional()}).refine(e=>!e.start||!e.end||new Date(e.start)<new Date(e.end),{message:"Start Date must be before End Date",path:["end"]}),z=e=>{let{onFormSubmit:t}=e,[r,n]=(0,a.useState)(1),[l,c]=(0,a.useState)([]),[o,d]=(0,a.useState)([]),[m,h]=(0,a.useState)(""),[j,z]=(0,a.useState)(!1),[C,O]=(0,a.useState)(!1),R=new Date().toISOString().split("T")[0],F=(0,a.useRef)(null),{freelancer_id:P}=(0,b.useParams)(),T=(0,f.cI)({resolver:(0,x.F)(Z),defaultValues:{projectName:"",description:"",githubLink:"",start:"",end:"",refer:"",techUsed:[],role:"",projectType:"",verificationStatus:"ADDED",comments:""},mode:"all"}),J=()=>{let{projectName:e,description:t,start:r,end:s}=T.getValues(),n=new Date(r),a=new Date(s);return e&&t&&r&&s?n>=a?(T.setError("end",{type:"manual",message:"Start Date must be before End Date"}),!1):0!==o.length||((0,k.Am)({variant:"destructive",title:"Skills required",description:"Please add at least one skill."}),!1):((0,k.Am)({variant:"destructive",title:"Missing fields",description:"Please fill all required fields in Step 1."}),!1)},X=e=>{m.trim()&&!o.includes(m)&&(d([...o,m]),e.onChange([...o,m]),h(""))},G=e=>{d(o.filter(t=>t!==e))};(0,a.useEffect)(()=>{(async()=>{try{var e,t;let r=await i.b.get("/skills"),s=(null==r?void 0:null===(t=r.data)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.map(e=>({value:e.label,label:e.label})))||[];c(s)}catch(e){console.error("API Error:",e),(0,k.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}})()},[]),(0,a.useEffect)(()=>{C&&n(1)},[C,T]);let{handleSaveAndClose:H,showDraftDialog:M,setShowDraftDialog:_,confirmExitDialog:L,setConfirmExitDialog:V,handleDiscardAndClose:W,handleDialogClose:q,discardDraft:U,loadDraft:Y}=(0,A.Z)({form:T,formSection:"projects",isDialogOpen:C,setIsDialogOpen:O,onSave:e=>{F.current={...e,techUsed:o}},onDiscard:()=>{F.current=null},setCurrSkills:d});async function B(e){z(!0);try{await i.b.post("/freelancer/".concat(P,"/project"),{...e,techUsed:o,verified:!1,oracleAssigned:"",start:e.start?new Date(e.start).toISOString():null,end:e.end?new Date(e.end).toISOString():null,verificationUpdateTime:new Date().toISOString()}),t(),O(!1),(0,k.Am)({title:"Project Added",description:"The project has been successfully added."})}catch(e){console.error("API Error:",e),(0,k.Am)({variant:"destructive",title:"Error",description:"Failed to add project. Please try again later."})}finally{z(!1)}}return(0,s.jsxs)(S.Vq,{open:C,onOpenChange:e=>{O(e),e||q()},children:[(0,s.jsx)(S.hg,{asChild:!0,children:(0,s.jsx)(D.z,{variant:"outline",size:"icon",className:"my-auto",children:(0,s.jsx)(p.Z,{className:"h-4 w-4"})})}),(0,s.jsxs)(S.cZ,{className:"lg:max-w-screen-lg overflow-y-scroll max-h-screen no-scrollbar",children:[(0,s.jsxs)(S.fK,{children:[(0,s.jsxs)(S.$N,{children:["Add Project - Step ",r," of 2"]}),(0,s.jsx)(S.Be,{children:1===r?"Fill in the basic details of your project.":"Fill in the additional project details."})]}),(0,s.jsx)(w.l0,{...T,children:(0,s.jsxs)("form",{onSubmit:T.handleSubmit(B),className:"space-y-4",children:[1===r&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(w.Wi,{control:T.control,name:"projectName",render:e=>{let{field:t}=e;return(0,s.jsxs)(w.xJ,{children:[(0,s.jsx)(w.lX,{children:"Project Name"}),(0,s.jsx)(w.NI,{children:(0,s.jsx)(I.I,{placeholder:"Enter project name",...t})}),(0,s.jsx)(w.pf,{children:"Enter the project name"}),(0,s.jsx)(w.zG,{})]})}}),(0,s.jsx)(w.Wi,{control:T.control,name:"description",render:e=>{let{field:t}=e;return(0,s.jsxs)(w.xJ,{children:[(0,s.jsx)(w.lX,{children:"Description"}),(0,s.jsx)(w.NI,{children:(0,s.jsx)(I.I,{placeholder:"Enter project description",...t})}),(0,s.jsx)(w.pf,{children:"Enter the project description"}),(0,s.jsx)(w.zG,{})]})}}),(0,s.jsx)(w.Wi,{control:T.control,name:"start",render:e=>{let{field:t}=e;return(0,s.jsxs)(w.xJ,{children:[(0,s.jsx)(w.lX,{children:"Start Date"}),(0,s.jsx)(w.NI,{children:(0,s.jsx)(I.I,{type:"date",max:R,...t})}),(0,s.jsx)(w.pf,{children:"Select the start date"}),(0,s.jsx)(w.zG,{})]})}}),(0,s.jsx)(w.Wi,{control:T.control,name:"end",render:e=>{let{field:t}=e;return(0,s.jsxs)(w.xJ,{children:[(0,s.jsx)(w.lX,{children:"End Date"}),(0,s.jsx)(w.NI,{children:(0,s.jsx)(I.I,{type:"date",...t})}),(0,s.jsx)(w.pf,{children:"Select the end date"}),(0,s.jsx)(w.zG,{})]})}}),(0,s.jsx)(w.Wi,{control:T.control,name:"techUsed",render:e=>{let{field:t}=e;return(0,s.jsxs)(w.xJ,{className:"mb-4",children:[(0,s.jsx)(w.lX,{children:"Skills"}),(0,s.jsx)(w.NI,{children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center mt-2",children:[(0,s.jsxs)(E.Ph,{onValueChange:e=>{h(e)},value:m,children:[(0,s.jsx)(E.i4,{children:(0,s.jsx)(E.ki,{placeholder:"Select skill"})}),(0,s.jsx)(E.Bw,{children:l.map((e,t)=>(0,s.jsx)(E.Ql,{value:e.label,children:e.label},t))})]}),(0,s.jsx)(D.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",onClick:()=>X(t),children:(0,s.jsx)(p.Z,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"flex flex-wrap mt-5",children:o.map((e,t)=>(0,s.jsxs)(u.C,{className:"uppercase mx-1 text-xs font-normal bg-gray-400 flex items-center my-2",children:[e,(0,s.jsx)("button",{type:"button",onClick:()=>G(e),className:"ml-2 text-red-500 hover:text-red-700",children:(0,s.jsx)(g.Z,{className:"h-4 w-4"})})]},t))})]})}),(0,s.jsx)(w.zG,{})]})}})]}),2===r&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(w.Wi,{control:T.control,name:"githubLink",render:e=>{let{field:t}=e;return(0,s.jsxs)(w.xJ,{children:[(0,s.jsx)(w.lX,{children:"GitHub Repo Link"}),(0,s.jsx)(w.NI,{children:(0,s.jsx)(I.I,{placeholder:"Enter GitHub repository link",...t})}),(0,s.jsx)(w.pf,{children:"Enter the GitHub repository link (optional)"}),(0,s.jsx)(w.zG,{})]})}}),(0,s.jsx)(w.Wi,{control:T.control,name:"refer",render:e=>{let{field:t}=e;return(0,s.jsxs)(w.xJ,{children:[(0,s.jsx)(w.lX,{children:"Reference"}),(0,s.jsx)(w.NI,{children:(0,s.jsx)(I.I,{placeholder:"Enter project reference",...t})}),(0,s.jsx)(w.pf,{children:"Enter the project reference"}),(0,s.jsx)(w.zG,{})]})}}),(0,s.jsx)(w.Wi,{control:T.control,name:"role",render:e=>{let{field:t}=e;return(0,s.jsxs)(w.xJ,{children:[(0,s.jsx)(w.lX,{children:"Role"}),(0,s.jsx)(w.NI,{children:(0,s.jsx)(I.I,{placeholder:"Enter role",...t})}),(0,s.jsx)(w.pf,{children:"Enter the role"}),(0,s.jsx)(w.zG,{})]})}}),(0,s.jsx)(w.Wi,{control:T.control,name:"projectType",render:e=>{let{field:t}=e;return(0,s.jsxs)(w.xJ,{children:[(0,s.jsx)(w.lX,{children:"Project Type"}),(0,s.jsx)(w.NI,{children:(0,s.jsx)(I.I,{placeholder:"Enter project type",...t})}),(0,s.jsx)(w.pf,{children:"Enter the project type (optional)"}),(0,s.jsx)(w.zG,{})]})}}),(0,s.jsx)(w.Wi,{control:T.control,name:"comments",render:e=>{let{field:t}=e;return(0,s.jsxs)(w.xJ,{children:[(0,s.jsx)(w.lX,{children:"Comments"}),(0,s.jsx)(w.NI,{children:(0,s.jsx)(I.I,{placeholder:"Enter any comments",...t})}),(0,s.jsx)(w.pf,{children:"Enter any comments (optional)"}),(0,s.jsx)(w.zG,{})]})}})]}),(0,s.jsx)(S.cN,{className:"flex justify-between",children:2===r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(D.z,{type:"button",variant:"outline",onClick:()=>{2===r&&n(1)},children:[(0,s.jsx)(v.Z,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,s.jsx)(D.z,{type:"submit",disabled:j,children:j?"Loading...":"Add Project"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{})," ",(0,s.jsxs)(D.z,{type:"button",onClick:()=>{1===r&&J()&&n(2)},children:["Next",(0,s.jsx)(y.Z,{className:"h-4 w-4 ml-2"})]})]})})]})})]}),L&&(0,s.jsx)(N.Z,{dialogChange:L,setDialogChange:V,heading:"Save Draft?",desc:"Do you want to save your draft before leaving?",handleClose:W,handleSave:H,btn1Txt:"Don't save",btn2Txt:"Yes save"}),M&&(0,s.jsx)(N.Z,{dialogChange:M,setDialogChange:_,heading:"Load Draft?",desc:"You have unsaved data. Would you like to restore it?",handleClose:U,handleSave:Y,btn1Txt:" No, start fresh",btn2Txt:"Yes, load draft"})]})};var C=r(27756),O=r(62688);function R(){let e=(0,n.v9)(e=>e.user),[t,r]=(0,a.useState)(!1),[c,o]=(0,a.useState)([]);return(0,a.useEffect)(()=>{(async()=>{try{var t,r,s;let n=await i.b.get("/freelancer/".concat(e.uid)),a=null===(t=n.data)||void 0===t?void 0:t.data;if(!a||"object"!=typeof a){console.warn("No projects data found, setting empty array."),o([]);return}o(Object.values(null==n?void 0:null===(s=n.data)||void 0===s?void 0:null===(r=s.data)||void 0===r?void 0:r.projects))}catch(e){(0,k.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."}),console.error("API Error:",e),o([])}})()},[e.uid,t]),(0,s.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,s.jsx)(l.Z,{menuItemsTop:C.y,menuItemsBottom:C.$,active:"Projects",isKycCheck:!0}),(0,s.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,s.jsx)(O.Z,{menuItemsTop:C.y,menuItemsBottom:C.$,activeMenu:"Projects",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Settings",link:"#"},{label:"Projects",link:"#"}]}),(0,s.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8    grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3",children:[c.map((e,t)=>(0,s.jsx)(h,{...e},t)),(0,s.jsx)(z,{onFormSubmit:()=>{r(e=>!e)}})]})]})]})}},68515:function(e,t,r){"use strict";var s=r(57437);r(2265);var n=r(24241);t.Z=e=>{let{startDate:t,endDate:r}=e,a=t?new Date(t).toLocaleDateString():"Start Date N/A",l="current"!==r&&r?new Date(r).toLocaleDateString():"Still Going On!";return(0,s.jsxs)("div",{className:"flex relative whitespace-nowrap items-start sm:items-center gap-1 rounded-md ",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2 ",children:[(0,s.jsx)(n.Z,{className:"w-4 h-4 sm:w-5 sm:h-5 "}),(0,s.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:"Start  ".concat(a)})]}),(0,s.jsx)("p",{children:"-"}),(0,s.jsx)("div",{className:"flex items-center ",children:(0,s.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:" ".concat(l)})})]})}},55384:function(e,t,r){"use strict";var s=r(57437);r(2265);var n=r(54662),a=r(89733);t.Z=e=>{let{dialogChange:t,setDialogChange:r,heading:l,desc:i,handleClose:c,handleSave:o,btn1Txt:d,btn2Txt:u}=e;return(0,s.jsx)(n.Vq,{open:t,onOpenChange:r,children:(0,s.jsxs)(n.cZ,{children:[(0,s.jsxs)(n.fK,{children:[(0,s.jsx)(n.$N,{children:l}),(0,s.jsx)(n.Be,{children:i})]}),(0,s.jsxs)(n.cN,{children:[(0,s.jsx)(a.z,{variant:"outline",onClick:c,children:d}),(0,s.jsx)(a.z,{onClick:o,children:u})]})]})})}},93363:function(e,t,r){"use strict";r.d(t,{NI:function(){return j},Wi:function(){return u},l0:function(){return o},lX:function(){return f},pf:function(){return p},xJ:function(){return x},zG:function(){return g}});var s=r(57437),n=r(2265),a=r(63355),l=r(39343),i=r(49354),c=r(70402);let o=l.RV,d=n.createContext({}),u=e=>{let{...t}=e;return(0,s.jsx)(d.Provider,{value:{name:t.name},children:(0,s.jsx)(l.Qr,{...t})})},m=()=>{let e=n.useContext(d),t=n.useContext(h),{getFieldState:r,formState:s}=(0,l.Gc)(),a=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...a}},h=n.createContext({}),x=n.forwardRef((e,t)=>{let{className:r,...a}=e,l=n.useId();return(0,s.jsx)(h.Provider,{value:{id:l},children:(0,s.jsx)("div",{ref:t,className:(0,i.cn)("space-y-2",r),...a})})});x.displayName="FormItem";let f=n.forwardRef((e,t)=>{let{className:r,...n}=e,{error:a,formItemId:l}=m();return(0,s.jsx)(c.Label,{ref:t,className:(0,i.cn)(a&&"text-destructive",r),htmlFor:l,...n})});f.displayName="FormLabel";let j=n.forwardRef((e,t)=>{let{...r}=e,{error:n,formItemId:l,formDescriptionId:i,formMessageId:c}=m();return(0,s.jsx)(a.g7,{ref:t,id:l,"aria-describedby":n?"".concat(i," ").concat(c):"".concat(i),"aria-invalid":!!n,...r})});j.displayName="FormControl";let p=n.forwardRef((e,t)=>{let{className:r,...n}=e,{formDescriptionId:a}=m();return(0,s.jsx)("p",{ref:t,id:a,className:(0,i.cn)("text-sm text-muted-foreground",r),...n})});p.displayName="FormDescription";let g=n.forwardRef((e,t)=>{let{className:r,children:n,...a}=e,{error:l,formMessageId:c}=m(),o=l?String(null==l?void 0:l.message):n;return o?(0,s.jsx)("p",{ref:t,id:c,className:(0,i.cn)("text-sm font-medium text-destructive",r),...a,children:o}):null});g.displayName="FormMessage"},70402:function(e,t,r){"use strict";r.r(t),r.d(t,{Label:function(){return o}});var s=r(57437),n=r(2265),a=r(38364),l=r(12218),i=r(49354);let c=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.f,{ref:t,className:(0,i.cn)(c(),r),...n})});o.displayName=a.f.displayName},27756:function(e,t,r){"use strict";r.d(t,{$:function(){return m},y:function(){return u}});var s=r(57437),n=r(11005),a=r(52022),l=r(25912),i=r(67524),c=r(6540),o=r(40036),d=r(66648);let u=[{href:"#",icon:(0,s.jsx)(d.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,s.jsx)(n.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/settings/personal-info",icon:(0,s.jsx)(a.Z,{className:"h-5 w-5"}),label:"Personal Info"},{href:"/freelancer/settings/professional-info",icon:(0,s.jsx)(l.Z,{className:"h-5 w-5"}),label:"Professional Info"},{href:"/freelancer/settings/projects",icon:(0,s.jsx)(i.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"/freelancer/settings/education-info",icon:(0,s.jsx)(c.Z,{className:"h-5 w-5"}),label:"Education"},{href:"/freelancer/settings/resume",icon:(0,s.jsx)(o.Z,{className:"h-5 w-5"}),label:"Portfolio"}],m=[]},9437:function(e,t,r){"use strict";var s=r(2265),n=r(86763);t.Z=e=>{let{form:t,formSection:r="",isDialogOpen:a,setIsDialogOpen:l,onSave:i,onDiscard:c,setCurrSkills:o}=e,[d,u]=(0,s.useState)(!1),[m,h]=(0,s.useState)(!1),x=(0,s.useRef)(!1),f=(0,s.useRef)(null),j=e=>{if(!r)return;let t=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");t[r]&&(f.current=t[r]),Object.values(e).some(e=>void 0!==e&&""!==e)&&(t[r]=e,localStorage.setItem("DEHIX_DRAFT",JSON.stringify(t)),(0,n.Am)({title:"Draft Saved",description:"Your ".concat(r," draft has been saved."),duration:1500}))};(0,s.useEffect)(()=>{if(a&&!x.current&&r){let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");e&&e[r]&&u(!0),x.current=!0}},[a,r]);let p=e=>e&&"object"==typeof e?Object.fromEntries(Object.entries(e).map(e=>{let[t,r]=e;return[t,"string"==typeof r?r.trim():r]})):{};return{showDraftDialog:d,setShowDraftDialog:u,confirmExitDialog:m,setConfirmExitDialog:h,loadDraft:()=>{if(!r)return;let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");e&&e[r]&&(Object.keys(e[r]).forEach(t=>{void 0===e[r][t]&&delete e[r][t]}),"projects"===r&&(delete e[r].verificationStatus,Array.isArray(e[r].techUsed)&&o(e[r].techUsed)),Object.entries(e[r]).some(e=>{let[,t]=e;return""!==t&&void 0!==t&&!(Array.isArray(t)&&0===t.length)})&&t&&(t.reset(e[r]),f.current=e[r],(0,n.Am)({title:"Draft Loaded",description:"Your ".concat(r," draft has been restored."),duration:1500})),u(!1))},discardDraft:()=>{if(!r)return;let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");e&&(delete e[r],0===Object.keys(e).length?localStorage.removeItem("DEHIX_DRAFT"):localStorage.setItem("DEHIX_DRAFT",JSON.stringify(e))),null==t||t.reset(),(0,n.Am)({title:"Draft Discarded",description:"Your ".concat(r," draft has been discarded."),duration:1500}),u(!1),c&&c()},handleSaveAndClose:()=>{if(!r)return;let e=null==t?void 0:t.getValues();j(e),(0,n.Am)({title:"Draft Saved",description:"Your draft has been saved.",duration:1500}),f.current=e,h(!1),l&&l(!1),i&&i(e)},handleDiscardAndClose:()=>{if(!r)return;let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");delete e[r],0===Object.keys(e).length?localStorage.removeItem("DEHIX_DRAFT"):localStorage.setItem("DEHIX_DRAFT",JSON.stringify(e)),(0,n.Am)({title:"Draft Discarded",description:"Your ".concat(r," draft has been discarded."),duration:1500}),h(!1),l&&l(!1),c&&c()},handleDialogClose:()=>{if(!a||!r)return;let e=(null==t?void 0:t.getValues())||{},s=f.current||{},n=p(e),i=p(s),c=Object.entries(i).some(e=>{let[t,r]=e,s=n[t];return Array.isArray(r)&&Array.isArray(s)?JSON.stringify(r)!==JSON.stringify(s):r!==s}),o=Object.entries(n).some(e=>{let[t,r]=e;return"verificationStatus"!==t&&void 0!==r&&""!==r&&void 0===i[t]});if(!c&&!o&&l){l(!1);return}Object.values(n).some(e=>null==e?void 0:e.toString().trim())?h(!0):l&&l(!1)},saveDraft:j,hasOtherValues:(0,s.useCallback)(e=>Object.entries(e).some(e=>{let[t,r]=e;return"profiles"!==t&&(Array.isArray(r)&&r.length>0&&("urls"!==t||r.some(e=>{var t;return(null==e?void 0:null===(t=e.value)||void 0===t?void 0:t.trim())!==""}))||"string"==typeof r&&""!==r.trim()||"number"==typeof r&&!isNaN(r))}),[]),hasProfiles:(0,s.useCallback)(e=>null==e?void 0:e.some(e=>Object.values(e).some(e=>Array.isArray(e)&&e.length>0||"string"==typeof e&&""!==e.trim()||"number"==typeof e&&!isNaN(e))),[])}}},38364:function(e,t,r){"use strict";r.d(t,{f:function(){return i}});var s=r(2265),n=r(18676),a=r(57437),l=s.forwardRef((e,t)=>(0,a.jsx)(n.WV.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,1374,2455,9726,2688,2971,7023,1744],function(){return e(e.s=92196)}),_N_E=e.O()}]);