"use strict";exports.id=3379,exports.ids=[3379],exports.modules={6343:(e,r,a)=>{a.d(r,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},47546:(e,r,a)=>{a.d(r,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},41137:(e,r,a)=>{a.d(r,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},40617:(e,r,a)=>{a.d(r,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},23015:(e,r,a)=>{a.d(r,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},48705:(e,r,a)=>{a.d(r,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},9969:(e,r,a)=>{a.d(r,{NI:()=>x,Wi:()=>f,l0:()=>d,lX:()=>h,pf:()=>y,xJ:()=>m,zG:()=>b});var t=a(10326),i=a(17577),l=a(99469),o=a(74723),n=a(51223),s=a(44794);let d=o.RV,c=i.createContext({}),f=({...e})=>t.jsx(c.Provider,{value:{name:e.name},children:t.jsx(o.Qr,{...e})}),u=()=>{let e=i.useContext(c),r=i.useContext(p),{getFieldState:a,formState:t}=(0,o.Gc)(),l=a(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=r;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...l}},p=i.createContext({}),m=i.forwardRef(({className:e,...r},a)=>{let l=i.useId();return t.jsx(p.Provider,{value:{id:l},children:t.jsx("div",{ref:a,className:(0,n.cn)("space-y-2",e),...r})})});m.displayName="FormItem";let h=i.forwardRef(({className:e,...r},a)=>{let{error:i,formItemId:l}=u();return t.jsx(s.Label,{ref:a,className:(0,n.cn)(i&&"text-destructive",e),htmlFor:l,...r})});h.displayName="FormLabel";let x=i.forwardRef(({...e},r)=>{let{error:a,formItemId:i,formDescriptionId:o,formMessageId:n}=u();return t.jsx(l.g7,{ref:r,id:i,"aria-describedby":a?`${o} ${n}`:`${o}`,"aria-invalid":!!a,...e})});x.displayName="FormControl";let y=i.forwardRef(({className:e,...r},a)=>{let{formDescriptionId:i}=u();return t.jsx("p",{ref:a,id:i,className:(0,n.cn)("text-sm text-muted-foreground",e),...r})});y.displayName="FormDescription";let b=i.forwardRef(({className:e,children:r,...a},i)=>{let{error:l,formMessageId:o}=u(),s=l?String(l?.message):r;return s?t.jsx("p",{ref:i,id:o,className:(0,n.cn)("text-sm font-medium text-destructive",e),...a,children:s}):null});b.displayName="FormMessage"},44794:(e,r,a)=>{a.r(r),a.d(r,{Label:()=>d});var t=a(10326),i=a(17577),l=a(34478),o=a(28671),n=a(51223);let s=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=i.forwardRef(({className:e,...r},a)=>t.jsx(l.f,{ref:a,className:(0,n.cn)(s(),e),...r}));d.displayName=l.f.displayName},2822:(e,r,a)=>{a.d(r,{E:()=>O,m:()=>z});var t=a(10326),i=a(17577),l=a(82561),o=a(48051),n=a(93095),s=a(77335),d=a(15594),c=a(52067),f=a(17124),u=a(2566),p=a(53405),m=a(9815),h="Radio",[x,y]=(0,n.b)(h),[b,v]=x(h),w=i.forwardRef((e,r)=>{let{__scopeRadio:a,name:n,checked:d=!1,required:c,disabled:f,value:u="on",onCheck:p,...m}=e,[h,x]=i.useState(null),y=(0,o.e)(r,e=>x(e)),v=i.useRef(!1),w=!h||!!h.closest("form");return(0,t.jsxs)(b,{scope:a,checked:d,disabled:f,children:[(0,t.jsx)(s.WV.button,{type:"button",role:"radio","aria-checked":d,"data-state":N(d),"data-disabled":f?"":void 0,disabled:f,value:u,...m,ref:y,onClick:(0,l.M)(e.onClick,e=>{d||p?.(),w&&(v.current=e.isPropagationStopped(),v.current||e.stopPropagation())})}),w&&(0,t.jsx)(E,{control:h,bubbles:!v.current,name:n,value:u,checked:d,required:c,disabled:f,style:{transform:"translateX(-100%)"}})]})});w.displayName=h;var g="RadioIndicator",k=i.forwardRef((e,r)=>{let{__scopeRadio:a,forceMount:i,...l}=e,o=v(g,a);return(0,t.jsx)(m.z,{present:i||o.checked,children:(0,t.jsx)(s.WV.span,{"data-state":N(o.checked),"data-disabled":o.disabled?"":void 0,...l,ref:r})})});k.displayName=g;var E=e=>{let{control:r,checked:a,bubbles:l=!0,...o}=e,n=i.useRef(null),s=(0,p.D)(a),d=(0,u.t)(r);return i.useEffect(()=>{let e=n.current,r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(s!==a&&r){let t=new Event("click",{bubbles:l});r.call(e,a),e.dispatchEvent(t)}},[s,a,l]),(0,t.jsx)("input",{type:"radio","aria-hidden":!0,defaultChecked:a,...o,tabIndex:-1,ref:n,style:{...e.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function N(e){return e?"checked":"unchecked"}var j=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],R="RadioGroup",[D,P]=(0,n.b)(R,[d.Pc,y]),C=(0,d.Pc)(),M=y(),[I,V]=D(R),L=i.forwardRef((e,r)=>{let{__scopeRadioGroup:a,name:i,defaultValue:l,value:o,required:n=!1,disabled:u=!1,orientation:p,dir:m,loop:h=!0,onValueChange:x,...y}=e,b=C(a),v=(0,f.gm)(m),[w,g]=(0,c.T)({prop:o,defaultProp:l,onChange:x});return(0,t.jsx)(I,{scope:a,name:i,required:n,disabled:u,value:w,onValueChange:g,children:(0,t.jsx)(d.fC,{asChild:!0,...b,orientation:p,dir:v,loop:h,children:(0,t.jsx)(s.WV.div,{role:"radiogroup","aria-required":n,"aria-orientation":p,"data-disabled":u?"":void 0,dir:v,...y,ref:r})})})});L.displayName=R;var Z="RadioGroupItem",F=i.forwardRef((e,r)=>{let{__scopeRadioGroup:a,disabled:n,...s}=e,c=V(Z,a),f=c.disabled||n,u=C(a),p=M(a),m=i.useRef(null),h=(0,o.e)(r,m),x=c.value===s.value,y=i.useRef(!1);return i.useEffect(()=>{let e=e=>{j.includes(e.key)&&(y.current=!0)},r=()=>y.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",r),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",r)}},[]),(0,t.jsx)(d.ck,{asChild:!0,...u,focusable:!f,active:x,children:(0,t.jsx)(w,{disabled:f,required:c.required,checked:x,...p,...s,name:c.name,ref:h,onCheck:()=>c.onValueChange(s.value),onKeyDown:(0,l.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,l.M)(s.onFocus,()=>{y.current&&m.current?.click()})})})});F.displayName=Z;var A=i.forwardRef((e,r)=>{let{__scopeRadioGroup:a,...i}=e,l=M(a);return(0,t.jsx)(k,{...l,...i,ref:r})});A.displayName="RadioGroupIndicator";var T=a(53982),G=a(51223);let O=i.forwardRef(({className:e,...r},a)=>t.jsx(L,{className:(0,G.cn)("grid gap-2",e),...r,ref:a}));O.displayName=L.displayName;let z=i.forwardRef(({className:e,...r},a)=>t.jsx(F,{ref:a,className:(0,G.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:t.jsx(A,{className:"flex items-center justify-center",children:t.jsx(T.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})}));z.displayName=F.displayName},34270:(e,r,a)=>{a.d(r,{$:()=>u,y:()=>f});var t=a(10326),i=a(95920),l=a(47546),o=a(79635),n=a(48705),s=a(6343),d=a(88378),c=a(46226);let f=[{href:"#",icon:t.jsx(c.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:t.jsx(i.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/oracleDashboard/businessVerification",icon:t.jsx(l.Z,{className:"h-5 w-5"}),label:"Business Verification"},{href:"/freelancer/oracleDashboard/workExpVerification",icon:t.jsx(o.Z,{className:"h-5 w-5"}),label:"Experience Verification"},{href:"/freelancer/oracleDashboard/projectVerification",icon:t.jsx(n.Z,{className:"h-5 w-5"}),label:"Project Verification"},{href:"/freelancer/oracleDashboard/educationVerification",icon:t.jsx(s.Z,{className:"h-5 w-5"}),label:"Education Verification"}],u=[{href:"/freelancer/settings/personal-info",icon:t.jsx(d.Z,{className:"h-5 w-5"}),label:"Settings"}]},39958:(e,r,a)=>{var t,i,l;a.d(r,{cd:()=>t,d8:()=>o,kJ:()=>i,sB:()=>l}),function(e){e.Mastery="Mastery",e.Proficient="Proficient",e.Beginner="Beginner"}(t||(t={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.REJECTED="Rejected",e.COMPLETED="Completed"}(i||(i={})),function(e){e.ACTIVE="ACTIVE",e.PENDING="PENDING",e.REJECTED="REJECTED",e.COMPLETED="COMPLETED"}(l||(l={}));let o={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},34478:(e,r,a)=>{a.d(r,{f:()=>n});var t=a(17577),i=a(77335),l=a(10326),o=t.forwardRef((e,r)=>(0,l.jsx)(i.WV.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));o.displayName="Label";var n=o}};