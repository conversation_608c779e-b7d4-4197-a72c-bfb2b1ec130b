(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9150],{18452:function(e,s,l){Promise.resolve().then(l.bind(l,25568))},25912:function(e,s,l){"use strict";l.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,l(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},24241:function(e,s,l){"use strict";l.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,l(33480).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},29406:function(e,s,l){"use strict";l.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,l(33480).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},92222:function(e,s,l){"use strict";l.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,l(33480).Z)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},25568:function(e,s,l){"use strict";l.r(s),l.d(s,{default:function(){return D}});var a=l(57437),t=l(2265),i=l(72377),n=l(54817),r=l(92222),c=l(29406),d=l(4867),m=l(11444),x=l(31590),o=l(62688),h=l(64797),u=l(59282),p=l(89733),j=l(25912),v=l(51077),N=l(24241),f=l(47304),w=l(79055),y=l(48185),g=e=>{let{filter:s,isTableView:l,searchQuery:t,skillData:i,domainData:n}=e,r=()=>{let e="All"===s?[...i,...n]:"Skills"===s?i:"Domain"===s?n:[];return t?e.filter(e=>{let{talentType:s}=e;return null==s?void 0:s.toLowerCase().includes(t.toLowerCase())}):e},c=e=>{if(!e)return"N/A";let s=new Date().getTime(),l=Math.ceil((new Date(e).getTime()-s)/864e5);return l>0?"".concat(l,"d"):"Today"};return(0,a.jsxs)("div",{className:"p-6 w-full",children:[(0,a.jsx)("div",{className:"mb-8  ml-0 md:ml-5 ",children:(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"Dehix-talent interview"})}),(0,a.jsx)("div",{className:" p-0 md:p-6 flex flex-col gap-4  sm:px-6 sm:py-0 md:gap-8  pt-2 pl-0 sm:pt-4 sm:pl-6 md:pt-6 md:pl-8  relative",children:(0,a.jsx)("div",{children:0===r().length?(0,a.jsx)("div",{className:"text-center text-gray-500 py-6",children:"No related data found."}):l?(0,a.jsx)("div",{className:"w-full bg-card  mx-auto px-4 md:px-10 py-6 border border-gray-200 rounded-xl shadow-md",children:(0,a.jsxs)(f.iA,{children:[(0,a.jsx)(f.xD,{children:(0,a.jsxs)(f.SC,{className:" hover:bg-[#09090B",children:[(0,a.jsx)(f.ss,{className:"w-[180px] text-center font-medium",children:"Interviwer"}),(0,a.jsx)(f.ss,{className:"w-[180px] text-center font-medium",children:"Talent Name"}),(0,a.jsx)(f.ss,{className:" font-medium text-center ",children:"Experience"}),(0,a.jsx)(f.ss,{className:" font-medium text-center",children:"Interview Fees"}),(0,a.jsx)(f.ss,{className:" font-medium text-center",children:"Level"}),(0,a.jsx)(f.ss,{className:" font-medium text-center",children:"Status"}),(0,a.jsx)(f.ss,{className:"  font-medium text-center",children:"Actions"})]})}),(0,a.jsx)(f.RM,{children:r().map(e=>Object.values((null==e?void 0:e.interviewBids)||{}).filter(e=>(null==e?void 0:e.status)==="ACCEPTED").map(s=>{var l,t;return(0,a.jsxs)(f.SC,{className:" transition",children:[(0,a.jsx)(f.pj,{className:"py-3 text-center",children:(null==s?void 0:null===(l=s.interviewer)||void 0===l?void 0:l.userName)||"Unknown"}),(0,a.jsx)(f.pj,{className:"py-3 text-center",children:e.talentType}),(0,a.jsx)(f.pj,{className:"py-3 text-center",children:null==s?void 0:null===(t=s.interviewer)||void 0===t?void 0:t.workExperience}),(0,a.jsx)(f.pj,{className:"py-3 text-center",children:null==s?void 0:s.fee}),(0,a.jsx)(f.pj,{className:"py-3 text-center",children:e.level}),(0,a.jsx)(f.pj,{className:"py-3 text-center",children:(0,a.jsx)(w.C,{variant:"default",className:"px-1 py-1 text-xs",children:null==e?void 0:e.InterviewStatus})}),(0,a.jsxs)(f.pj,{className:"text-right py-3",children:[(0,a.jsx)(p.z,{variant:"outline",size:"sm",className:"mr-3",children:"Edit"}),(0,a.jsx)(p.z,{size:"sm",children:"View"})]})]},e._id)}))})]})}):(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:r().map(e=>Object.values((null==e?void 0:e.interviewBids)||{}).filter(e=>(null==e?void 0:e.status)==="ACCEPTED").map(s=>{var l,t;return(0,a.jsxs)(y.Zb,{className:"p-6 relative rounded-2xl shadow-xl border border-gray-300 hover:shadow-2xl ",children:[(0,a.jsxs)(y.Ol,{className:"p-4 border-b  rounded-t-2xl",children:[(0,a.jsx)(y.ll,{className:"text-xl font-semibold ",children:(null==s?void 0:null===(l=s.interviewer)||void 0===l?void 0:l.userName)||"Unknown"}),(0,a.jsx)(y.SZ,{className:"text-sm ",children:null==e?void 0:e.level}),(0,a.jsx)("p",{className:"text-sm absolute top-1 right-3 flex items-center gap-2",children:(0,a.jsx)(w.C,{variant:"default",className:"px-1 py-1 text-xs",children:null==e?void 0:e.InterviewStatus})})]}),(0,a.jsxs)(y.aY,{className:"p-4 space-y-3",children:[(0,a.jsxs)("p",{className:"text-sm flex items-center gap-2",children:[(0,a.jsx)(j.Z,{size:16,className:""}),(0,a.jsx)("span",{className:"font-medium",children:"Experience :"}),null==s?void 0:null===(t=s.interviewer)||void 0===t?void 0:t.workExperience," years"]}),(0,a.jsxs)("p",{className:"text-sm flex items-center gap-2",children:[(0,a.jsx)(v.Z,{size:16,className:""}),(0,a.jsx)("span",{className:"font-medium",children:"Interview Fees :"}),"₹",null==s?void 0:s.fee]}),(0,a.jsxs)("p",{className:"text-sm flex items-center whitespace-nowrap gap-2",children:[(0,a.jsx)(N.Z,{size:16,className:""}),(0,a.jsx)("span",{className:"font-medium",children:"Schedule Date :"}),new Date(null==s?void 0:s.suggestedDateTime).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]})]}),(0,a.jsxs)(y.eW,{className:"flex  justify-between p-4 border-t  rounded-b-2xl",children:[(0,a.jsx)(p.z,{variant:"outline",size:"sm",className:"",children:"Edit"}),(0,a.jsx)(p.z,{size:"sm",className:"",children:"View"}),(0,a.jsx)("span",{className:"absolute bottom-3 right-4 text-xs font-medium text-gray-600  px-2 py-1 rounded-md",children:c(null==s?void 0:s.suggestedDateTime)})]})]},null==s?void 0:s._id)}))})})})]})},b=l(77209),k=l(15922),O=l(2183),S=e=>{let{isTableView:s}=e;return(0,a.jsxs)("div",{className:"space-y-4 w-full",children:[(0,a.jsx)(O.O,{className:"h-6 w-1/4 flex justify-start"}),s?(0,a.jsxs)("div",{className:"border rounded-lg shadow-sm p-4",children:[(0,a.jsx)(O.O,{className:"h-6 w-1/3 mb-3"})," ",(0,a.jsx)("div",{className:"border-t",children:[...Array(6)].map((e,s)=>(0,a.jsxs)("div",{className:"flex justify-between items-center py-3 border-b",children:[(0,a.jsx)(O.O,{className:"h-5 w-1/4"}),(0,a.jsx)(O.O,{className:"h-5 w-1/6"}),(0,a.jsx)(O.O,{className:"h-5 w-1/5"}),(0,a.jsx)(O.O,{className:"h-5 w-1/6"}),(0,a.jsx)(O.O,{className:"h-5 w-1/8"})]},s))})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)("div",{className:"border rounded-2xl shadow-md p-4 space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(O.O,{className:"h-5 w-1/3"}),(0,a.jsx)(O.O,{className:"h-5 w-1/6 rounded-full"})]}),(0,a.jsx)(O.O,{className:"h-4 w-1/2"}),(0,a.jsx)(O.O,{className:"h-4 w-3/4"}),(0,a.jsx)(O.O,{className:"h-4 w-3/4"}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)(O.O,{className:"h-7 w-1/5"}),(0,a.jsx)(O.O,{className:"h-7 w-1/5"})]})]},s))}),(0,a.jsxs)("div",{className:"flex justify-between items-center mt-10",children:[(0,a.jsx)(O.O,{className:"h-6 w-1/4 flex justify-start"}),(0,a.jsx)(O.O,{className:"h-6 w-1/12 flex justify-start"})]}),s?(0,a.jsx)("div",{className:"border rounded-lg shadow-sm p-4",children:(0,a.jsx)("div",{className:"border-t",children:[...Array(6)].map((e,s)=>(0,a.jsxs)("div",{className:"flex justify-between items-center py-3 border-b",children:[(0,a.jsx)(O.O,{className:"h-5 w-1/4"}),(0,a.jsx)(O.O,{className:"h-5 w-1/6"}),(0,a.jsx)(O.O,{className:"h-5 w-1/5"}),(0,a.jsx)(O.O,{className:"h-5 w-1/6"}),(0,a.jsx)(O.O,{className:"h-5 w-1/8"}),(0,a.jsx)(O.O,{className:"h-5 w-1/8"}),(0,a.jsx)(O.O,{className:"h-5 w-1/8"})]},s))})}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)("div",{className:"border rounded-2xl shadow-md p-4 space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(O.O,{className:"h-5 w-1/3"}),(0,a.jsx)(O.O,{className:"h-5 w-1/6 rounded-full"})]}),(0,a.jsx)(O.O,{className:"h-4 w-1/2"}),(0,a.jsx)(O.O,{className:"h-4 w-3/4"}),(0,a.jsx)(O.O,{className:"h-4 w-3/4"}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)(O.O,{className:"h-7 w-1/5"}),(0,a.jsx)(O.O,{className:"h-7 w-1/5"})]})]},s))})]})},z=e=>{let{filter:s,isTableView:l,searchQuery:t,skillData:i,domainData:n}=e,r=()=>{let e="All"===s?[...i,...n]:"Skills"===s?i:"Domain"===s?n:[];return t?e.filter(e=>{let{talentType:s}=e;return null==s?void 0:s.toLowerCase().includes(t.toLowerCase())}):e},c=e=>{if(!e)return"N/A";let s=new Date().getTime(),l=Math.ceil((new Date(e).getTime()-s)/864e5);return l>0?"".concat(l,"d"):"Today"};return(0,a.jsxs)("div",{className:"p-6 w-full",children:[(0,a.jsx)("div",{className:"mb-8 ml-0 md:ml-5 flex justify-between items-center",children:(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"Project interview"})}),(0,a.jsx)("div",{className:" p-0 md:p-6 flex flex-col gap-4  sm:px-6 sm:py-0 md:gap-8  pt-2 pl-0 sm:pt-4 sm:pl-6 md:pt-6 md:pl-8  relative",children:(0,a.jsx)("div",{children:0===r().length?(0,a.jsx)("div",{className:"text-center text-gray-500 py-6",children:"No related data found."}):l?(0,a.jsx)("div",{className:"w-full bg-card  mx-auto px-4 md:px-10 py-6 border border-gray-200 rounded-xl shadow-md",children:(0,a.jsxs)(f.iA,{children:[(0,a.jsx)(f.xD,{children:(0,a.jsxs)(f.SC,{className:" hover:bg-[#09090B",children:[(0,a.jsx)(f.ss,{className:"w-[180px] text-center font-medium",children:"Interviwer"}),(0,a.jsx)(f.ss,{className:"w-[180px] text-center font-medium",children:"Talent Name"}),(0,a.jsx)(f.ss,{className:" font-medium text-center ",children:"Experience"}),(0,a.jsx)(f.ss,{className:" font-medium text-center",children:"Interview Fees"}),(0,a.jsx)(f.ss,{className:" font-medium text-center",children:"Level"}),(0,a.jsx)(f.ss,{className:" font-medium text-center",children:"Status"}),(0,a.jsx)(f.ss,{className:"  font-medium text-center",children:"Actions"})]})}),(0,a.jsx)(f.RM,{children:r().map(e=>Object.values((null==e?void 0:e.interviewBids)||{}).filter(e=>(null==e?void 0:e.status)==="ACCEPTED").map(s=>{var l,t;return(0,a.jsxs)(f.SC,{className:" transition",children:[(0,a.jsx)(f.pj,{className:"py-3 text-center",children:(null==s?void 0:null===(l=s.interviewer)||void 0===l?void 0:l.userName)||"Unknown"}),(0,a.jsx)(f.pj,{className:"py-3 text-center",children:e.talentType}),(0,a.jsx)(f.pj,{className:"py-3 text-center",children:null==s?void 0:null===(t=s.interviewer)||void 0===t?void 0:t.workExperience}),(0,a.jsx)(f.pj,{className:"py-3 text-center",children:null==s?void 0:s.fee}),(0,a.jsx)(f.pj,{className:"py-3 text-center",children:e.level}),(0,a.jsx)(f.pj,{className:"py-3 text-center",children:(0,a.jsx)(w.C,{variant:"default",className:"px-1 py-1 text-xs",children:null==e?void 0:e.InterviewStatus})}),(0,a.jsxs)(f.pj,{className:"text-right py-3",children:[(0,a.jsx)(p.z,{variant:"outline",size:"sm",className:"mr-3",children:"Edit"}),(0,a.jsx)(p.z,{size:"sm",children:"View"})]})]},e._id)}))})]})}):(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:r().map(e=>Object.values((null==e?void 0:e.interviewBids)||{}).filter(e=>(null==e?void 0:e.status)==="ACCEPTED").map(s=>{var l,t;return(0,a.jsxs)(y.Zb,{className:"p-6 relative rounded-2xl shadow-xl border border-gray-300 hover:shadow-2xl ",children:[(0,a.jsxs)(y.Ol,{className:"p-4 border-b  rounded-t-2xl",children:[(0,a.jsx)(y.ll,{className:"text-xl font-semibold ",children:(null==s?void 0:null===(l=s.interviewer)||void 0===l?void 0:l.userName)||"Unknown"}),(0,a.jsx)(y.SZ,{className:"text-sm ",children:null==e?void 0:e.level}),(0,a.jsx)("p",{className:"text-sm absolute top-1 right-3 flex items-center gap-2",children:(0,a.jsx)(w.C,{variant:"default",className:"px-1 py-1 text-xs",children:null==e?void 0:e.InterviewStatus})})]}),(0,a.jsxs)(y.aY,{className:"p-4 space-y-3",children:[(0,a.jsxs)("p",{className:"text-sm flex items-center gap-2",children:[(0,a.jsx)(j.Z,{size:16,className:""}),(0,a.jsx)("span",{className:"font-medium",children:"Experience :"}),null==s?void 0:null===(t=s.interviewer)||void 0===t?void 0:t.workExperience," years"]}),(0,a.jsxs)("p",{className:"text-sm flex items-center gap-2",children:[(0,a.jsx)(v.Z,{size:16,className:""}),(0,a.jsx)("span",{className:"font-medium",children:"Interview Fees :"}),"₹",null==s?void 0:s.fee]}),(0,a.jsxs)("p",{className:"text-sm flex items-center whitespace-nowrap gap-2",children:[(0,a.jsx)(N.Z,{size:16,className:""}),(0,a.jsx)("span",{className:"font-medium",children:"Schedule Date :"}),new Date(null==s?void 0:s.suggestedDateTime).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]})]}),(0,a.jsxs)(y.eW,{className:"flex  justify-between p-4 border-t  rounded-b-2xl",children:[(0,a.jsx)(p.z,{variant:"outline",size:"sm",className:"",children:"Edit"}),(0,a.jsx)(p.z,{size:"sm",className:"",children:"View"}),(0,a.jsx)("span",{className:"absolute bottom-3 right-4 text-xs font-medium text-gray-600  px-2 py-1 rounded-md",children:c(null==s?void 0:s.suggestedDateTime)})]})]},null==s?void 0:s._id)}))})})})]})},C=l(78068);function D(){let[e,s]=t.useState("All"),[l,j]=(0,t.useState)(!1),[v,N]=(0,t.useState)(""),[f,w]=(0,t.useState)(!1),y=(0,t.useRef)(null),O=(0,m.v9)(e=>e.user),[D,Z]=(0,t.useState)([]),[T,I]=(0,t.useState)([]),[A,E]=(0,t.useState)([]),[M,L]=(0,t.useState)([]),[B,F]=(0,t.useState)(!1),[V,_]=(0,t.useState)(!1);return(0,t.useEffect)(()=>{f&&y.current&&y.current.focus()},[f]),(0,t.useEffect)(()=>{(async()=>{var e,s,l,a,t,i,n,r,c;try{F(!0),_(!1);let t=await k.b.get("/interview/current-interview",{params:{intervieweeId:O.uid}}),i=null!==(l=null===(e=t.data)||void 0===e?void 0:e.data.dehixTalent)&&void 0!==l?l:[],n=null!==(a=null===(s=t.data)||void 0===s?void 0:s.data.projects)&&void 0!==a?a:[],r=Array.isArray(i)?i:[i],c=Array.isArray(n)?n:[n],d=r.filter(e=>(null==e?void 0:e.talentType)==="SKILL"),m=r.filter(e=>(null==e?void 0:e.talentType)==="DOMAIN"),x=c.filter(e=>(null==e?void 0:e.talentType)==="SKILL"),o=c.filter(e=>(null==e?void 0:e.talentType)==="DOMAIN");Z(null!=d?d:[]),I(null!=m?m:[]),E(null!=x?x:[]),L(null!=o?o:[])}catch(e){(null===(t=e.response)||void 0===t?void 0:t.status)===404&&((null===(n=e.response)||void 0===n?void 0:null===(i=n.data)||void 0===i?void 0:i.message)==="Current Interview not found"||(null===(c=e.response)||void 0===c?void 0:null===(r=c.data)||void 0===r?void 0:r.code)==="NOT_FOUND")?_(!0):((0,C.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."}),console.error("Failed to load data. Please try again.",e)),Z([]),I([]),E([]),L([])}finally{F(!1)}})()},[null==O?void 0:O.uid]),D.length>0||T.length>0||A.length>0||M.length,(0,a.jsxs)("div",{className:"flex min-h-screen w-full bg-muted/40",children:[(0,a.jsx)(h.Z,{menuItemsTop:u.y,menuItemsBottom:u.$,active:"Current"}),(0,a.jsxs)("div",{className:"flex flex-col mb-8 sm:gap-4 sm:py-0 sm:pl-14 w-full",children:[(0,a.jsx)(o.Z,{breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Interview",link:"/freelancer/interview/profile"},{label:"Current Interviews",link:"#"}],menuItemsTop:u.y,menuItemsBottom:u.$,activeMenu:"Current"}),(0,a.jsxs)("div",{className:"ml-10",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Current Interviews"}),(0,a.jsx)("p",{className:"text-gray-400 mt-2",children:"View and manage your current interviews, and update skills for better matches."})]}),(0,a.jsxs)("div",{className:"flex flex-col flex-1 items-start gap-4 p-2 sm:px-6 sm:py-0 md:gap-8 lg:flex-col xl:flex-col pt-2 pl-4 sm:pt-4 sm:pl-6 md:pt-6 md:pl-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center w-full",children:[(0,a.jsxs)(x.h_,{children:[(0,a.jsx)(x.$F,{asChild:!0,children:(0,a.jsxs)(p.z,{variant:"outline",size:"sm",className:"h-7 gap-1  text-sm",children:[(0,a.jsx)(i.Z,{className:"h-3.5 w-3.5"}),(0,a.jsx)("span",{className:"sr-only sm:not-sr-only",children:"Filter"})]})}),(0,a.jsxs)(x.AW,{align:"end",children:[(0,a.jsx)(x.Ju,{children:"Filter by"}),(0,a.jsx)(x.VD,{}),(0,a.jsx)(x.bO,{checked:"All"===e,onSelect:()=>s("All"),children:"All"}),(0,a.jsx)(x.bO,{checked:"Skills"===e,onSelect:()=>s("Skills"),children:"Skills"}),(0,a.jsx)(x.bO,{checked:"Domain"===e,onSelect:()=>s("Domain"),children:"Domain"})]})]}),(0,a.jsxs)("div",{className:"flex justify-center gap-3 items-center",children:[(0,a.jsxs)("div",{className:"relative flex-1 mr-2",children:[!f&&(0,a.jsx)(n.Z,{size:"sm",className:"absolute left-2 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 sm:block md:hidden ml-0.5 cursor-pointer",onClick:()=>w(!0)}),(0,a.jsx)(n.Z,{size:"sm",className:"absolute h-7 gap-1 text-sm left-2 top-1/2 transform -translate-y-1/2 w-5 text-gray-400 cursor-pointer \n        ".concat(f?"sm:flex":"hidden md:flex")}),(0,a.jsx)(b.I,{placeholder:"Search interview",value:v,ref:y,onChange:e=>N(e.target.value),onFocus:()=>w(!0),onBlur:()=>w(!1),className:"pl-8 transition-all duration-300 ease-in-out\n          ".concat(f?"w-full sm:w-72":"w-0 sm:w-0 md:w-full"," sm:hidden ")}),(0,a.jsx)(b.I,{placeholder:"Search interview by...",value:v,onChange:e=>N(e.target.value),onFocus:()=>w(!0),onBlur:()=>w(!1),className:"pl-8 hidden md:flex border focus-visible:ring-1  focus:ring-0 "})]}),!f&&(0,a.jsxs)("div",{className:"gap-2 md:hidden flex",children:[(0,a.jsx)(p.z,{onClick:()=>j(!0),variant:"outline",size:"sm",className:"h-7 gap-1 text-sm",children:(0,a.jsx)(r.Z,{className:"h-3.5 w-3.5"})}),(0,a.jsx)(p.z,{onClick:()=>j(!1),variant:"outline",size:"sm",className:"h-7 gap-1 text-sm",children:(0,a.jsx)(d.kxK,{className:"h-3.5 w-3.5"})})]}),(0,a.jsxs)("div",{className:"gap-2 md:flex hidden",children:[(0,a.jsx)(p.z,{onClick:()=>j(!0),variant:"outline",size:"sm",className:"h-7 gap-1 text-sm",children:(0,a.jsx)(r.Z,{className:"h-3.5 w-3.5"})}),(0,a.jsx)(p.z,{onClick:()=>j(!1),variant:"outline",size:"sm",className:"h-7 gap-1 text-sm",children:(0,a.jsx)(d.kxK,{className:"h-3.5 w-3.5"})})]})]})]}),(0,a.jsx)("div",{className:"w-full flex justify-center items-center flex-col",children:B?(0,a.jsx)(S,{isTableView:l}):(0,a.jsxs)("div",{className:"w-full space-y-8",children:[(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("h2",{className:"text-2xl font-semibold ",children:"Dehix Talent Interviews"})}),0===D.length&&0===T.length?(0,a.jsxs)("div",{className:"text-center py-8 w-full ",children:[(0,a.jsx)(c.Z,{className:"mx-auto text-gray-400",size:"60"}),(0,a.jsx)("p",{className:"text-gray-500 text-base font-medium mt-3",children:"No Dehix talent interviews scheduled."}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Browse available talent opportunities to schedule new interviews."})]}):(0,a.jsx)(g,{skillData:D,domainData:T,searchQuery:v,isTableView:l,filter:e})]}),(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("h2",{className:"text-2xl font-semibold ",children:"Project Interviews"})}),0===A.length&&0===M.length?(0,a.jsxs)("div",{className:"text-center py-8 w-full",children:[(0,a.jsx)(c.Z,{className:"mx-auto text-gray-400",size:"60"}),(0,a.jsx)("p",{className:"text-gray-500 text-base font-medium mt-3",children:"No project interviews scheduled."}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Check your project applications for upcoming interview opportunities."})]}):(0,a.jsx)(z,{searchQuery:v,isTableView:l,skillData:A,domainData:M,filter:e})]})]})})]})]})]})}},2183:function(e,s,l){"use strict";l.d(s,{O:function(){return i}});var a=l(57437),t=l(49354);function i(e){let{className:s,...l}=e;return(0,a.jsx)("div",{className:(0,t.cn)("animate-pulse rounded-md bg-primary/10",s),...l})}},59282:function(e,s,l){"use strict";l.d(s,{$:function(){return h},y:function(){return o}});var a=l(57437),t=l(11005),i=l(38133),n=l(33480);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n.Z)("ListVideo",[["path",{d:"M12 12H3",key:"18klou"}],["path",{d:"M16 6H3",key:"1wxfjs"}],["path",{d:"M12 18H3",key:"11ftsu"}],["path",{d:"m16 12 5 3-5 3v-6Z",key:"zpskkp"}]]);var c=l(25912);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,n.Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var m=l(24258),x=l(66648);let o=[{href:"#",icon:(0,a.jsx)(x.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,a.jsx)(t.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/interview/profile",icon:(0,a.jsx)(i.Z,{className:"h-5 w-5"}),label:"Profile"},{href:"/freelancer/interview/current",icon:(0,a.jsx)(r,{className:"h-5 w-5"}),label:"Current"},{href:"/freelancer/interview/bids",icon:(0,a.jsx)(c.Z,{className:"h-5 w-5"}),label:"Bids"},{href:"/freelancer/interview/history",icon:(0,a.jsx)(d,{className:"h-5 w-5"}),label:"History"}],h=[{href:"/freelancer/settings/personal-info",icon:(0,a.jsx)(m.Z,{className:"h-5 w-5"}),label:"Settings"}]}},function(e){e.O(0,[4358,7481,9208,8310,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2455,9726,2688,2971,7023,1744],function(){return e(e.s=18452)}),_N_E=e.O()}]);