(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3185],{68357:function(e,t,r){Promise.resolve().then(r.t.bind(r,81034,23)),Promise.resolve().then(r.bind(r,54418)),Promise.resolve().then(r.t.bind(r,53054,23)),Promise.resolve().then(r.bind(r,76703)),Promise.resolve().then(r.bind(r,54858)),Promise.resolve().then(r.bind(r,60771)),Promise.resolve().then(r.bind(r,89736)),Promise.resolve().then(r.bind(r,13030))},92699:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(33480).Z)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},38296:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(33480).Z)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},54418:function(e,t,r){"use strict";r.d(t,{AuthProvider:function(){return p},a:function(){return h}});var n=r(57437),a=r(2265),o=r(11444),s=r(16463),i=r(3483),c=r(15922),l=r(42361);let d=(0,a.createContext)({user:null,loading:!0}),u=e=>localStorage.getItem(e),f=(e,t)=>localStorage.setItem(e,t),m=e=>localStorage.removeItem(e),p=e=>{let{children:t}=e,r=(0,s.useRouter)(),[p,h]=(0,a.useState)(null),[g,v]=(0,a.useState)(!0),y=(0,o.I0)();return(0,a.useEffect)(()=>{let e=u("user"),t=u("token");if(e&&t)try{let r=JSON.parse(e);h(r),(0,c.q)(t),y((0,i.av)(r))}catch(e){console.error("Failed to parse user:",e),m("user"),m("token")}let n=l.I8.onIdTokenChanged(async e=>{if(e&&navigator.onLine)try{let t=await e.getIdToken();if(t){let r=await e.getIdTokenResult(),n={...e,type:r.claims.type};f("user",JSON.stringify(n)),f("token",t),h(n),(0,c.q)(t),y((0,i.av)(n))}}catch(e){console.error("Token Refresh Error:",e)}else m("user"),m("token"),h(null),y((0,i.pn)()),r.replace("/auth/login")});return v(!1),()=>n()},[y,r]),(0,n.jsx)(d.Provider,{value:{user:p,loading:g},children:!g&&t})},h=()=>(0,a.useContext)(d)},76703:function(e,t,r){"use strict";r.d(t,{default:function(){return d}});var n=r(57437),a=r(2265),o=r(11444),s=r(69753),i=r(3483),c=r(62143);let l=()=>(0,s.xC)({reducer:{user:i.ZP,projectDraft:c.ZP}});function d(e){let{children:t}=e,r=(0,a.useRef)();return r.current||(r.current=l()),(0,n.jsx)(o.zt,{store:r.current,children:t})}},54858:function(e,t,r){"use strict";r.d(t,{ThemeProvider:function(){return o}});var n=r(57437);r(2265);var a=r(79512);function o(e){let{children:t,...r}=e;return(0,n.jsx)(a.f,{...r,children:t})}},60771:function(e,t,r){"use strict";r.d(t,{Toaster:function(){return v}});var n=r(57437),a=r(2265),o=r(44504),s=r(12218),i=r(74697),c=r(49354);let l=o.zt,d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.l_,{ref:t,className:(0,c.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...a})});d.displayName=o.l_.displayName;let u=(0,s.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),f=a.forwardRef((e,t)=>{let{className:r,variant:a,...s}=e;return(0,n.jsx)(o.fC,{ref:t,className:(0,c.cn)(u({variant:a}),r),...s})});f.displayName=o.fC.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.aU,{ref:t,className:(0,c.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...a})}).displayName=o.aU.displayName;let m=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.x8,{ref:t,className:(0,c.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...a,children:(0,n.jsx)(i.Z,{className:"h-4 w-4"})})});m.displayName=o.x8.displayName;let p=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.Dx,{ref:t,className:(0,c.cn)("text-sm font-semibold",r),...a})});p.displayName=o.Dx.displayName;let h=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.dk,{ref:t,className:(0,c.cn)("text-sm opacity-90",r),...a})});h.displayName=o.dk.displayName;var g=r(78068);function v(){let{toasts:e}=(0,g.pm)();return(0,n.jsxs)(l,{children:[e.map(function(e){let{id:t,title:r,description:a,action:o,...s}=e;return(0,n.jsxs)(f,{...s,children:[(0,n.jsxs)("div",{className:"grid gap-1",children:[r&&(0,n.jsx)(p,{children:r}),a&&(0,n.jsx)(h,{children:a})]}),o,(0,n.jsx)(m,{})]},t)}),(0,n.jsx)(d,{})]})}},89736:function(e,t,r){"use strict";r.d(t,{TooltipProvider:function(){return i},_v:function(){return d},aJ:function(){return l},u:function(){return c}});var n=r(57437),a=r(2265),o=r(60364),s=r(49354);let i=o.zt,c=o.fC,l=o.xz,d=a.forwardRef((e,t)=>{let{className:r,sideOffset:a=4,...i}=e;return(0,n.jsx)(o.VY,{ref:t,sideOffset:a,className:(0,s.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...i})});d.displayName=o.VY.displayName},78068:function(e,t,r){"use strict";r.d(t,{Am:function(){return u},pm:function(){return f}});var n=r(2265);let a=0,o=new Map,s=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?s(r):e.toasts.forEach(e=>{s(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},c=[],l={toasts:[]};function d(e){l=i(l,e),c.forEach(e=>{e(l)})}function u(e){let{...t}=e,r=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>d({type:"DISMISS_TOAST",toastId:r});return d({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||n()}}}),{id:r,dismiss:n,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function f(){let[e,t]=n.useState(l);return n.useEffect(()=>(c.push(t),()=>{let e=c.indexOf(t);e>-1&&c.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},42361:function(e,t,r){"use strict";r.d(t,{I8:function(){return d},Vv:function(){return u},db:function(){return l}});var n=r(15236),a=r(75735),o=r(60516),s=r(69842),i=r(99854);let c=(0,n.ZF)({apiKey:"AIzaSyBPTH9xikAUkgGof048klY6WGiZSmRoXXA",authDomain:"dehix-6c349.firebaseapp.com",databaseURL:"https://dehix-6c349-default-rtdb.firebaseio.com",projectId:"dehix-6c349",storageBucket:"dehix-6c349.appspot.com",messagingSenderId:"521082542540",appId:"1:521082542540:web:543857e713038c2927a569"}),l=(0,s.ad)(c),d=(0,a.v0)(c);d.useDeviceLanguage();let u=new a.hJ;(0,o.N8)(c),(0,i.cF)(c)},15922:function(e,t,r){"use strict";r.d(t,{b:function(){return a},q:function(){return o}});var n=r(38472);let a=n.Z.create({baseURL:"http://127.0.0.1:8080/"});console.log("Base URL:","http://127.0.0.1:8080/");let o=e=>{console.log("Initializing Axios with token:",e),a=n.Z.create({baseURL:"http://127.0.0.1:8080/",headers:{Authorization:"Bearer ".concat(e)}})};a.interceptors.request.use(e=>(console.log("Request config:",e),e),e=>(console.error("Request error:",e),Promise.reject(e))),a.interceptors.response.use(e=>(console.log("Response:",e.data),e),e=>(console.error("Response error:",e),Promise.reject(e)))},62143:function(e,t,r){"use strict";r.d(t,{Dj:function(){return i},MR:function(){return o},kd:function(){return a}});let n=(0,r(69753).oM)({name:"projectDraft",initialState:{draftedProjects:[]},reducers:{addDraftedProject:(e,t)=>{e.draftedProjects.includes(t.payload)||e.draftedProjects.push(t.payload)},removeDraftedProject:(e,t)=>{e.draftedProjects=e.draftedProjects.filter(e=>e!==t.payload)},clearDraftedProjects:e=>{e.draftedProjects=[]},setDraftedProjects:(e,t)=>{e.draftedProjects=t.payload}}}),{addDraftedProject:a,removeDraftedProject:o,clearDraftedProjects:s,setDraftedProjects:i}=n.actions;t.ZP=n.reducer},3483:function(e,t,r){"use strict";r.d(t,{av:function(){return a},pn:function(){return o}});let n=(0,r(69753).oM)({name:"user",initialState:{},reducers:{setUser:(e,t)=>({...e,...t.payload}),clearUser:()=>({})}}),{setUser:a,clearUser:o}=n.actions;t.ZP=n.reducer},49354:function(e,t,r){"use strict";r.d(t,{bx:function(){return f},c0:function(){return d},cn:function(){return l},is:function(){return m},pH:function(){return u}});var n=r(44839),a=r(96164),o=r(75735),s=r(44785),i=r(15922),c=r(42361);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,n.W)(t))}let d=async e=>{try{await (0,o.LS)(c.I8,e),console.log("Password reset email sent successfully.")}catch(r){let e=r.code,t=r.message;throw console.log(e,t),Error(t)}},u=async(e,t)=>{try{return await (0,o.e5)(c.I8,e,t)}catch(r){let e=r.code,t=r.message;throw console.log(e,t),Error(t)}},f=async()=>await (0,o.rh)(c.I8,c.Vv),m=async e=>{try{let t=e.user,r=await t.getIdToken();(0,i.q)(r);let n=(await t.getIdTokenResult()).claims,a={uid:t.uid,email:t.email,displayName:t.displayName,phoneNumber:t.phoneNumber,photoURL:t.photoURL,emailVerified:t.emailVerified},o=n.type;return s.Z.set("userType",o,{expires:1,path:"/"}),s.Z.set("token",r,{expires:1,path:"/"}),{user:a,claims:n}}catch(e){throw console.error("Error fetching user data:",e),e}}},13030:function(e,t,r){"use strict";r.d(t,{default:function(){return m}});var n=r(57437),a=r(2265);function o(){let[e,t]=(0,a.useState)(navigator.onLine);return(0,a.useEffect)(()=>{let e=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",e),window.addEventListener("offline",r),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",r)}},[]),e}var s=r(79512),i=r(38296),c=r(92699),l=r(33480);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,l.Z)("WifiOff",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),u=(0,l.Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var f=function(){let e=o(),[t,r]=(0,a.useState)(!1),{setTheme:l,resolvedTheme:f}=(0,s.F)(),m="dark"===f;return(0,n.jsxs)("div",{className:"min-h-screen transition-colors duration-300 ".concat(m?"bg-black":"bg-gradient-to-br from-gray-50 to-white"," flex items-center justify-center p-4"),children:[(0,n.jsx)("button",{onClick:()=>{l("dark"===f?"light":"dark")},className:"absolute top-4 right-4 p-3 rounded-full transition-all duration-300 ".concat(m?"bg-zinc-900 hover:bg-zinc-800":"bg-white hover:bg-gray-100"," shadow-lg"),children:m?(0,n.jsx)(i.Z,{className:"w-6 h-6"}):(0,n.jsx)(c.Z,{className:"w-6 h-6"})}),(0,n.jsx)("div",{className:"max-w-md w-full transform transition-all duration-500 ".concat(e?"scale-0 opacity-0":"scale-100 opacity-100"),children:(0,n.jsx)("div",{className:"".concat(m?"bg-zinc-900/80 backdrop-blur-lg border-zinc-800":"bg-white border-gray-200"," rounded-2xl p-8 shadow-2xl border transition-colors duration-300"),children:(0,n.jsxs)("div",{className:"flex flex-col items-center text-center space-y-6",children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:"absolute inset-0 animate-ping rounded-full ".concat(m?"bg-red-500":"bg-red-400"," opacity-20")}),(0,n.jsx)("div",{className:"relative",children:(0,n.jsx)(d,{className:"w-16 h-16 ".concat(m?"text-red-500":"text-red-400"," animate-pulse")})})]}),(0,n.jsx)("h1",{className:"text-3xl font-bold mt-4 ".concat(m?"text-zinc-100":"text-gray-900"),children:"You‘re Offline"}),(0,n.jsx)("p",{className:"text-lg ".concat(m?"text-zinc-400":"text-gray-600"),children:"Please check your internet connection and try again"}),(0,n.jsx)("div",{className:"w-full h-2 ".concat(m?"bg-zinc-800":"bg-gray-200"," rounded-full overflow-hidden"),children:(0,n.jsx)("div",{className:"h-full ".concat(m?"bg-red-500":"bg-red-400"," animate-[shimmer_2s_infinite]")})}),(0,n.jsxs)("button",{onClick:()=>{r(!0),navigator.onLine?window.location.reload():setTimeout(()=>{r(!1)},1500)},className:"group relative inline-flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 active:scale-95 ".concat(m?"bg-zinc-100 text-zinc-900 hover:bg-white":"bg-gray-900 text-white hover:bg-gray-800"),children:[(0,n.jsx)(u,{className:"w-5 h-5 transition-transform ".concat(t?"animate-spin":"group-hover:rotate-180")}),"Try Again"]})]})})})]})};function m(e){let{children:t}=e,r=o();return(0,n.jsx)(n.Fragment,{children:r?t:(0,n.jsx)(f,{})})}},53054:function(){},81034:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},921:function(e,t,r){"use strict";r.d(t,{B:function(){return u}});var n=r(2265),a=r(98324),o=r(1584),s=r(57437),i=n.forwardRef((e,t)=>{let{children:r,...a}=e,o=n.Children.toArray(r),i=o.find(d);if(i){let e=i.props.children,r=o.map(t=>t!==i?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(c,{...a,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,s.jsx)(c,{...a,ref:t,children:r})});i.displayName="Slot";var c=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){let e,s;let i=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref;return n.cloneElement(r,{...function(e,t){let r={...t};for(let n in t){let a=e[n],o=t[n];/^on[A-Z]/.test(n)?a&&o?r[n]=(...e)=>{o(...e),a(...e)}:a&&(r[n]=a):"style"===n?r[n]={...a,...o}:"className"===n&&(r[n]=[a,o].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props),ref:t?(0,o.F)(t,i):i})}return n.Children.count(r)>1?n.Children.only(null):null});c.displayName="SlotClone";var l=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});function d(e){return n.isValidElement(e)&&e.type===l}function u(e){let t=e+"CollectionProvider",[r,c]=(0,a.b)(t),[l,d]=r(t,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:t,children:r}=e,a=n.useRef(null),o=n.useRef(new Map).current;return(0,s.jsx)(l,{scope:t,itemMap:o,collectionRef:a,children:r})};u.displayName=t;let f=e+"CollectionSlot",m=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,a=d(f,r),c=(0,o.e)(t,a.collectionRef);return(0,s.jsx)(i,{ref:c,children:n})});m.displayName=f;let p=e+"CollectionItemSlot",h="data-radix-collection-item",g=n.forwardRef((e,t)=>{let{scope:r,children:a,...c}=e,l=n.useRef(null),u=(0,o.e)(t,l),f=d(p,r);return n.useEffect(()=>(f.itemMap.set(l,{ref:l,...c}),()=>void f.itemMap.delete(l))),(0,s.jsx)(i,{[h]:"",ref:u,children:a})});return g.displayName=p,[{Provider:u,Slot:m,ItemSlot:g},function(t){let r=d(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},c]}},79512:function(e,t,r){"use strict";r.d(t,{F:function(){return l},f:function(){return d}});var n=r(2265),a=["light","dark"],o="(prefers-color-scheme: dark)",s="undefined"==typeof window,i=n.createContext(void 0),c={setTheme:e=>{},themes:[]},l=()=>{var e;return null!=(e=n.useContext(i))?e:c},d=e=>n.useContext(i)?e.children:n.createElement(f,{...e}),u=["light","dark"],f=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:s=!0,enableColorScheme:c=!0,storageKey:l="theme",themes:d=u,defaultTheme:f=s?"system":"light",attribute:v="data-theme",value:y,children:x,nonce:b}=e,[w,j]=n.useState(()=>p(l,f)),[k,S]=n.useState(()=>p(l)),N=y?Object.values(y):d,T=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&s&&(t=g());let n=y?y[t]:t,o=r?h():null,i=document.documentElement;if("class"===v?(i.classList.remove(...N),n&&i.classList.add(n)):n?i.setAttribute(v,n):i.removeAttribute(v),c){let e=a.includes(f)?f:null,r=a.includes(t)?t:e;i.style.colorScheme=r}null==o||o()},[]),E=n.useCallback(e=>{let t="function"==typeof e?e(e):e;j(t);try{localStorage.setItem(l,t)}catch(e){}},[t]),R=n.useCallback(e=>{S(g(e)),"system"===w&&s&&!t&&T("system")},[w,t]);n.useEffect(()=>{let e=window.matchMedia(o);return e.addListener(R),R(e),()=>e.removeListener(R)},[R]),n.useEffect(()=>{let e=e=>{e.key===l&&E(e.newValue||f)};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[E]),n.useEffect(()=>{T(null!=t?t:w)},[t,w]);let C=n.useMemo(()=>({theme:w,setTheme:E,forcedTheme:t,resolvedTheme:"system"===w?k:w,themes:s?[...d,"system"]:d,systemTheme:s?k:void 0}),[w,E,t,k,s,d]);return n.createElement(i.Provider,{value:C},n.createElement(m,{forcedTheme:t,disableTransitionOnChange:r,enableSystem:s,enableColorScheme:c,storageKey:l,themes:d,defaultTheme:f,attribute:v,value:y,children:x,attrs:N,nonce:b}),x)},m=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:s,enableSystem:i,enableColorScheme:c,defaultTheme:l,value:d,attrs:u,nonce:f}=e,m="system"===l,p="class"===s?"var d=document.documentElement,c=d.classList;".concat("c.remove(".concat(u.map(e=>"'".concat(e,"'")).join(","),")"),";"):"var d=document.documentElement,n='".concat(s,"',s='setAttribute';"),h=c?(a.includes(l)?l:null)?"if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'".concat(l,"'"):"if(e==='light'||e==='dark')d.style.colorScheme=e":"",g=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=!(arguments.length>2)||void 0===arguments[2]||arguments[2],n=d?d[e]:e,o=t?e+"|| ''":"'".concat(n,"'"),i="";return c&&r&&!t&&a.includes(e)&&(i+="d.style.colorScheme = '".concat(e,"';")),"class"===s?t||n?i+="c.add(".concat(o,")"):i+="null":n&&(i+="d[s](n,".concat(o,")")),i},v=t?"!function(){".concat(p).concat(g(t),"}()"):i?"!function(){try{".concat(p,"var e=localStorage.getItem('").concat(r,"');if('system'===e||(!e&&").concat(m,")){var t='").concat(o,"',m=window.matchMedia(t);if(m.media!==t||m.matches){").concat(g("dark"),"}else{").concat(g("light"),"}}else if(e){").concat(d?"var x=".concat(JSON.stringify(d),";"):"").concat(g(d?"x[e]":"e",!0),"}").concat(m?"":"else{"+g(l,!1,!1)+"}").concat(h,"}catch(e){}}()"):"!function(){try{".concat(p,"var e=localStorage.getItem('").concat(r,"');if(e){").concat(d?"var x=".concat(JSON.stringify(d),";"):"").concat(g(d?"x[e]":"e",!0),"}else{").concat(g(l,!1,!1),";}").concat(h,"}catch(t){}}();");return n.createElement("script",{nonce:f,dangerouslySetInnerHTML:{__html:v}})}),p=(e,t)=>{let r;if(!s){try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t}},h=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},g=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")}},function(e){e.O(0,[5402,9141,4358,7481,9208,9668,9227,1444,9812,364,4504,2971,7023,1744],function(){return e(e.s=68357)}),_N_E=e.O()}]);