"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[253],{60253:function(e,t,n){n.d(t,{Z:function(){return b}});var a=n(57437),s=n(75735),r=n(2265),i=n(16463),l=n(11444),o=n(3274),d=n(54662),c=n(70402),u=n(77209),f=n(89733),m=n(78068),x=e=>{let{open:t,setOpen:n,onSubmit:s,setPhone:i}=e,[l,x]=(0,r.useState)(""),[p,h]=(0,r.useState)(!1),g=async e=>{if(e.preventDefault(),10!==l.length){(0,m.Am)({variant:"destructive",title:"Invalid Phone Number",description:"Please enter a valid 10-digit number."});return}try{h(!0);let e="+91".concat(l);i(e),await s(e),(0,m.Am)({title:"Sending OTP",description:"Sending OTP to ".concat(e,". Please wait...")}),n(!1)}catch(e){(0,m.Am)({variant:"destructive",title:"Failed to Send OTP",description:"Something went wrong. Please try again."})}finally{h(!1)}};return(0,a.jsx)(d.Vq,{open:t,onOpenChange:n,children:(0,a.jsxs)(d.cZ,{children:[(0,a.jsxs)(d.fK,{children:[(0,a.jsx)(d.$N,{children:"Change Phone Number"}),(0,a.jsx)(d.Be,{children:"Enter a new phone number to receive the OTP."})]}),(0,a.jsxs)("form",{onSubmit:g,className:"space-y-4 mt-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c.Label,{className:"mb-1 block",children:"New Phone Number"}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)(u.I,{type:"tel",value:l,onChange:e=>{let t=e.target.value.replace(/\D/g,"");t.length<=10&&x(t)},maxLength:10,placeholder:"Enter 10-digit number",disabled:p})})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,a.jsx)(f.z,{variant:"secondary",type:"button",onClick:()=>n(!1),disabled:p,children:"Cancel"}),(0,a.jsx)(f.z,{type:"submit",disabled:p,children:p?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Sending..."]}):"Send OTP"})]})]})]})})},p=n(55084),h=n(42361),g=n(3483),j=n(49354),y=n(15922),N=n(86763),b=function(e){var t,n;let{phoneNumber:o,isModalOpen:c,setIsModalOpen:u}=e,m=(0,i.useRouter)(),b=(0,l.I0)(),[v,T]=(0,r.useState)(""),[w,S]=(0,r.useState)(null),[C,P]=(0,r.useState)(""),[O,k]=(0,r.useState)(0),[E,I]=(0,r.useTransition)(),[A,R]=(0,r.useState)(!1),[_,D]=(0,r.useState)(o),[Z,M]=(0,r.useState)(null),[V,z]=(0,r.useState)(null);(0,r.useEffect)(()=>{let e;return O>0&&(e=setTimeout(()=>k(O-1),1e3)),()=>clearTimeout(e)},[O]),(0,r.useEffect)(()=>{let e=new s.lI(h.I8,"recaptcha-container",{size:"invisible"});return M(e),()=>{e.clear()}},[]);let Y=(0,r.useCallback)(async()=>{I(async()=>{if(S(""),!V){S("Please request OTP first.");return}try{let e=await (null==V?void 0:V.confirm(v)),{user:t,claims:n}=await (0,j.is)(e);await y.b.put("/".concat(n.type),{phone:_,phoneVerify:!0}),b((0,g.av)({...t,type:n.type})),m.replace("/dashboard/".concat(n.type))}catch(e){console.log(e),S("Failed to verify OTP. Please check the OTP."),(0,N.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}})},[V,v,b,m,_]);(0,r.useEffect)(()=>{6===v.length&&Y()},[v,Y]);let F=(0,r.useCallback)(async()=>{I(async()=>{if(S(""),k(60),!Z)return S("RecaptchaVerifier is not initialized.");try{if(o.length>0){let e=await (0,s.$g)(h.I8,o,Z);z(e),P("OTP sent successfully.")}}catch(e){console.error(e),k(0),"auth/invalid-phone-number"===e.code?S("Invalid phone number. Please check the number."):"auth/too-many-requests"===e.code?S("Too many requests. Please try again later."):S("Failed to send OTP. Please try again.")}})},[o,Z]);(0,r.useEffect)(()=>{c&&""!==o&&F()},[c,F,o]);let q=(0,a.jsxs)("div",{role:"status",className:"flex justify-center",children:[(0,a.jsxs)("svg",{"aria-hidden":"true",className:"w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-green-600",viewBox:"0 0 100 101",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z",fill:"currentColor"}),(0,a.jsx)("path",{d:"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z",fill:"currentFill"})]}),(0,a.jsx)("span",{className:"sr-only",children:"Loading..."})]});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.Vq,{open:c,onOpenChange:u,children:(0,a.jsxs)(d.cZ,{children:[(0,a.jsxs)(d.fK,{children:[(0,a.jsxs)("p",{className:"text-sm text-center text-gray-500",children:["OTP sent to"," ",(0,a.jsxs)("strong",{children:[null===(t=_||o)||void 0===t?void 0:t.substring(0,3)," ",null===(n=_||o)||void 0===n?void 0:n.substring(3)]})]}),(0,a.jsx)("button",{className:"text-blue-600 text-sm underline mt-1",onClick:()=>R(!0),children:"Not your number? Change it"}),(0,a.jsx)(d.$N,{children:"Enter OTP"}),(0,a.jsx)(d.Be,{children:"Please enter the OTP sent to your phone number."})]}),(0,a.jsxs)("div",{className:"flex flex-col justify-center items-center",children:[(0,a.jsxs)(p.Zn,{maxLength:6,value:v,onChange:e=>T(e),children:[(0,a.jsxs)(p.hf,{children:[(0,a.jsx)(p.cY,{index:0}),(0,a.jsx)(p.cY,{index:1}),(0,a.jsx)(p.cY,{index:2})]}),(0,a.jsx)(p.aM,{}),(0,a.jsxs)(p.hf,{children:[(0,a.jsx)(p.cY,{index:3}),(0,a.jsx)(p.cY,{index:4}),(0,a.jsx)(p.cY,{index:5})]})]}),(0,a.jsx)(f.z,{disabled:E||O>0,className:"mt-5",children:O>0?"Resend OTP in ".concat(O):E?"Sending OTP":"Send OTP"}),(0,a.jsxs)("div",{className:"p-10 text-center",children:[w&&(0,a.jsx)("p",{className:"text-red-500",children:w}),C&&(0,a.jsx)("p",{className:"text-green-500",children:C})]}),E&&q]})]})}),(0,a.jsx)(x,{open:A,setOpen:R,onSubmit:e=>{D(e),F(),R(!1)},setPhone:D}),(0,a.jsx)("div",{id:"recaptcha-container"})]})}},54662:function(e,t,n){n.d(t,{$N:function(){return h},Be:function(){return g},GG:function(){return u},Vq:function(){return o},cN:function(){return p},cZ:function(){return m},fK:function(){return x},hg:function(){return d}});var a=n(57437),s=n(2265),r=n(32226),i=n(74697),l=n(49354);let o=r.fC,d=r.xz,c=r.h_,u=r.x8,f=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.aV,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",n),...s})});f.displayName=r.aV.displayName;let m=s.forwardRef((e,t)=>{let{className:n,children:s,...o}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(f,{}),(0,a.jsxs)(r.VY,{ref:t,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",n),...o,children:[s,(0,a.jsxs)(r.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground text-red-500",children:[(0,a.jsx)(i.Z,{className:"h-4 w-4",strokeWidth:4}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=r.VY.displayName;let x=e=>{let{className:t,...n}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...n})};x.displayName="DialogHeader";let p=e=>{let{className:t,...n}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...n})};p.displayName="DialogFooter";let h=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.Dx,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",n),...s})});h.displayName=r.Dx.displayName;let g=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.dk,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",n),...s})});g.displayName=r.dk.displayName},55084:function(e,t,n){n.d(t,{Zn:function(){return o},aM:function(){return u},cY:function(){return c},hf:function(){return d}});var a=n(57437),s=n(2265),r=n(66431),i=n(32309),l=n(49354);let o=s.forwardRef((e,t)=>{let{className:n,containerClassName:s,...i}=e;return(0,a.jsx)(r.uZ,{ref:t,containerClassName:(0,l.cn)("flex items-center gap-2 has-[:disabled]:opacity-50",s),className:(0,l.cn)("disabled:cursor-not-allowed",n),...i})});o.displayName="InputOTP";let d=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center",n),...s})});d.displayName="InputOTPGroup";let c=s.forwardRef((e,t)=>{let{index:n,className:i,...o}=e,{char:d,hasFakeCaret:c,isActive:u}=s.useContext(r.VM).slots[n];return(0,a.jsxs)("div",{ref:t,className:(0,l.cn)("relative flex h-10 w-10 items-center justify-center border-y border-r border-input text-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md",u&&"z-10 ring-2 ring-ring ring-offset-background",i),...o,children:[d,c&&(0,a.jsx)("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"h-4 w-px animate-caret-blink bg-foreground duration-1000"})})]})});c.displayName="InputOTPSlot";let u=s.forwardRef((e,t)=>{let{...n}=e;return(0,a.jsx)("div",{ref:t,role:"separator",...n,children:(0,a.jsx)(i.Z,{})})});u.displayName="InputOTPSeparator"},70402:function(e,t,n){n.r(t),n.d(t,{Label:function(){return d}});var a=n(57437),s=n(2265),r=n(38364),i=n(12218),l=n(49354);let o=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.f,{ref:t,className:(0,l.cn)(o(),n),...s})});d.displayName=r.f.displayName},86763:function(e,t,n){n.d(t,{Am:function(){return c}}),n(2265);let a=0,s=new Map,r=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:n}=t;return n?r(n):e.toasts.forEach(e=>{r(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===n||void 0===n?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],o={toasts:[]};function d(e){o=i(o,e),l.forEach(e=>{e(o)})}function c(e){let{...t}=e,n=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>d({type:"DISMISS_TOAST",toastId:n});return d({type:"ADD_TOAST",toast:{...t,id:n,open:!0,onOpenChange:e=>{e||s()}}}),{id:n,dismiss:s,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:n}})}}},3483:function(e,t,n){n.d(t,{av:function(){return s},pn:function(){return r}});let a=(0,n(69753).oM)({name:"user",initialState:{},reducers:{setUser:(e,t)=>({...e,...t.payload}),clearUser:()=>({})}}),{setUser:s,clearUser:r}=a.actions;t.ZP=a.reducer}}]);