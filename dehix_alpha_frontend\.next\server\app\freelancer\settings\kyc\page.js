(()=>{var e={};e.id=9379,e.ids=[9379],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},28482:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),r(15187),r(54302),r(12523);var a=r(23191),s=r(88716),l=r(37922),n=r.n(l),i=r(95231),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let c=["",{children:["freelancer",{children:["settings",{children:["kyc",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,15187)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\settings\\kyc\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\settings\\kyc\\page.tsx"],u="/freelancer/settings/kyc/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/freelancer/settings/kyc/page",pathname:"/freelancer/settings/kyc",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},87667:(e,t,r)=>{Promise.resolve().then(r.bind(r,33153))},6343:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},47546:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},48705:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},33153:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>N});var a=r(10326),s=r(25842),l=r(92166),n=r(45175),i=r(40588),o=r(17577),c=r(74064),d=r(74723),u=r(27256),m=r(46226),p=r(29752),x=r(49261),h=r(6260),f=r(91664),g=r(9969),v=r(41190),b=r(56627),y=r(78062),j=r(38443),I=r(39958);let k=u.z.object({aadharOrGovtId:u.z.string().optional(),frontImageUrl:u.z.union([u.z.unknown(),u.z.string().url(),u.z.null()]).optional(),backImageUrl:u.z.union([u.z.unknown(),u.z.string().url(),u.z.null()]).optional(),liveCaptureUrl:u.z.union([u.z.unknown(),u.z.string().url(),u.z.null()]).optional()});function w({user_id:e}){let[t,r]=(0,o.useState)(!1),[s,l]=(0,o.useState)("PENDING"),[n,i]=(0,o.useState)(null),u=(0,d.cI)({resolver:(0,c.F)(k),defaultValues:{aadharOrGovtId:"",frontImageUrl:null,backImageUrl:null,liveCaptureUrl:null},mode:"all"});async function w(e){r(!0);try{let t={frontImageUrl:e.frontImageUrl,backImageUrl:e.backImageUrl,liveCaptureUrl:e.liveCaptureUrl};if(e.frontImageUrl instanceof File){let r=new FormData;r.append("frontImageUrl",e.frontImageUrl);let a=await h.b.post("/register/upload-image",r,{headers:{"Content-Type":"multipart/form-data"}});t.frontImageUrl=a.data.data.Location}if(e.backImageUrl instanceof File){let r=new FormData;r.append("backImageUrl",e.backImageUrl);let a=await h.b.post("/register/upload-image",r,{headers:{"Content-Type":"multipart/form-data"}});t.backImageUrl=a.data.data.Location}if(e.liveCaptureUrl instanceof File){let r=new FormData;r.append("liveCaptureUrl",e.liveCaptureUrl);let a=await h.b.post("/register/upload-image",r,{headers:{"Content-Type":"multipart/form-data"}});t.liveCaptureUrl=a.data.data.Location}let{aadharOrGovtId:r}=e,a={aadharOrGovtId:r,frontImageUrl:t.frontImageUrl,backImageUrl:t.backImageUrl,liveCaptureUrl:t.liveCaptureUrl,status:"APPLIED"};await h.b.put("/freelancer/kyc",{kyc:a}),i({...n,kyc:{...n?.kyc,aadharOrGovtId:e.aadharOrGovtId,frontImageUrl:t.frontImageUrl,backImageUrl:t.backImageUrl,liveCaptureUrl:t.liveCaptureUrl}}),(0,b.Am)({title:"KYC Updated",description:"Your KYC has been successfully updated."})}catch(e){console.error("API Error:",e),(0,b.Am)({variant:"destructive",title:"Error",description:"Failed to update KYC. Please try again later."})}finally{r(!1)}}return a.jsx(p.Zb,{className:"p-4 sm:p-6 md:p-10 shadow-md relative rounded-lg w-full max-w-3xl mx-auto",children:(0,a.jsxs)(g.l0,{...u,children:[a.jsx("div",{className:"flex flex-col mb-4 sm:mb-6 text-center sm:text-left",children:(0,a.jsxs)("div",{className:"text-sm sm:text-base font-medium",children:["KYC Status"," ",a.jsx(j.C,{className:`text-xs py-0.5 ${I.d8[s]||""}`,children:s.toLowerCase()})]})}),(0,a.jsxs)("form",{onSubmit:u.handleSubmit(w),className:"grid gap-4 sm:gap-6 md:gap-8 grid-cols-1 md:grid-cols-2",children:[a.jsx(y.Separator,{className:"col-span-1 md:col-span-2"}),a.jsx(g.Wi,{control:u.control,name:"aadharOrGovtId",render:({field:e})=>(0,a.jsxs)(g.xJ,{className:"w-full",children:[a.jsx(g.lX,{className:"font-semibold text-sm sm:text-base",children:"Aadhar or Govt Id"}),a.jsx(g.NI,{children:a.jsx(v.I,{placeholder:"Enter your Aadhar Id",...e,className:"w-full border rounded-md px-4 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"})}),a.jsx(g.zG,{})]})}),a.jsx(g.Wi,{control:u.control,name:"frontImageUrl",render:({field:e})=>(0,a.jsxs)(g.xJ,{className:"w-full",children:[a.jsx(g.lX,{className:"font-semibold text-sm sm:text-base",children:"Document Front Img"}),a.jsx(g.NI,{children:a.jsx("div",{className:"flex flex-col sm:flex-row items-center sm:items-start gap-3",children:e.value&&"string"==typeof e.value?(0,a.jsxs)(a.Fragment,{children:[a.jsx(m.default,{src:e.value,alt:"Front Document",width:128,height:128,className:"rounded-md object-cover border"}),a.jsx(f.z,{type:"button",variant:"outline",size:"sm",onClick:()=>e.onChange(""),children:"Change Image"})]}):a.jsx(v.I,{type:"file",onChange:t=>{let r=t.target.files?.[0];r&&e.onChange(r)},onBlur:e.onBlur,className:"w-full border rounded-md px-4 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"})})}),a.jsx(g.zG,{})]})}),a.jsx(g.Wi,{control:u.control,name:"backImageUrl",render:({field:e})=>(0,a.jsxs)(g.xJ,{className:"w-full",children:[a.jsx(g.lX,{className:"font-semibold text-sm sm:text-base",children:"Document Back Img"}),a.jsx(g.NI,{children:a.jsx("div",{className:"flex flex-col sm:flex-row items-center sm:items-start gap-3",children:e.value&&"string"==typeof e.value?(0,a.jsxs)(a.Fragment,{children:[a.jsx(m.default,{src:e.value,alt:"Back Document",width:128,height:128,className:"rounded-md object-cover border"}),a.jsx(f.z,{type:"button",variant:"outline",size:"sm",onClick:()=>e.onChange(""),children:"Change Image"})]}):a.jsx(v.I,{type:"file",onChange:t=>{let r=t.target.files?.[0];r&&e.onChange(r)},onBlur:e.onBlur,className:"w-full border rounded-md px-4 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"})})}),a.jsx(g.zG,{})]})}),a.jsx("div",{className:"col-span-1  md:col-span-2",children:a.jsx(x.Z,{form:u})}),a.jsx(y.Separator,{className:"col-span-1 md:col-span-2"}),a.jsx("div",{className:"col-span-1 md:col-span-2",children:a.jsx(f.z,{type:"submit",className:"w-full rounded-md px-6 py-3 text-sm font-semibold disabled:opacity-50",disabled:t,children:t?"Loading...":"Update KYC"})})]})]})})}function N(){let e=(0,s.v9)(e=>e.user);return(0,a.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[a.jsx(l.Z,{menuItemsTop:n.y,menuItemsBottom:n.$,active:"kyc",isKycCheck:!0}),(0,a.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[a.jsx(i.Z,{menuItemsTop:n.y,menuItemsBottom:n.$,activeMenu:"Personal Info",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Settings",link:"#"},{label:"kyc",link:"#"}]}),a.jsx("main",{className:"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8",children:a.jsx(w,{user_id:e.uid})})]})]})}},49261:(e,t,r)=>{"use strict";r.d(t,{Z:()=>c});var a=r(10326),s=r(17577),l=r(46226);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(80851).Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var i=r(91664),o=r(9969);let c=({form:e})=>{let t=(0,s.useRef)(null),r=(0,s.useRef)(null),[c,d]=(0,s.useState)(null),[u,m]=(0,s.useState)(!1),[p,x]=(0,s.useState)(null),h=async()=>{m(!0);try{let e=await navigator.mediaDevices.getUserMedia({video:!0});x(e),t.current&&(t.current.srcObject=e,await new Promise(e=>{t.current.onloadedmetadata=()=>e(!0)}))}catch(e){console.error("Error accessing camera:",e),m(!1)}},f=()=>{if(t.current&&r.current){let a=t.current,s=r.current,l=r.current.getContext("2d");if(l&&a.videoWidth>0&&a.videoHeight>0){s.width=a.videoWidth,s.height=a.videoHeight,l.drawImage(a,0,0,s.width,s.height);let t=s.toDataURL("image/jpeg");d(t),fetch(t).then(e=>e.blob()).then(t=>{let r=new File([t],"live-capture.jpg",{type:"image/jpeg"});e.setValue("liveCaptureUrl",r)}),p&&p.getTracks().forEach(e=>e.stop()),m(!1)}}};return a.jsx(o.Wi,{control:e.control,name:"liveCaptureUrl",render:({field:s})=>(0,a.jsxs)(o.xJ,{children:[a.jsx(o.lX,{children:"Live Capture"}),a.jsx(o.NI,{children:a.jsx("div",{className:"flex flex-col md:justify-start md:items-start sm:justify-center items-center gap-2",children:(0,a.jsxs)("div",{className:"flex s gap-2",children:[(c||s.value)&&a.jsx("div",{className:"flex flex-col items-center",children:a.jsx(l.default,{src:c||s.value,alt:"Live Capture",width:128,height:128,className:"rounded-md object-cover"})}),!s.value&&c&&a.jsx(i.z,{type:"button",variant:"outline",size:"sm",className:"mt-2",onClick:()=>{d(null),e.setValue("liveCaptureUrl",""),h()},children:"Retake Photo"}),!u&&(0,a.jsxs)(i.z,{type:"button",onClick:h,children:[a.jsx(n,{className:"w-4 h-4 mr-2"}),"Live Capture"]}),u&&a.jsx(i.z,{type:"button",onClick:f,children:"Take Photo"}),u&&a.jsx("video",{ref:t,autoPlay:!0,className:"w-64 h-48 border rounded-md",children:a.jsx("track",{kind:"captions",srcLang:"en",label:"English captions",src:""})}),a.jsx("canvas",{ref:r,className:"hidden",width:"640",height:"480"})]})})}),a.jsx(o.zG,{})]})})}},9969:(e,t,r)=>{"use strict";r.d(t,{NI:()=>f,Wi:()=>u,l0:()=>c,lX:()=>h,pf:()=>g,xJ:()=>x,zG:()=>v});var a=r(10326),s=r(17577),l=r(99469),n=r(74723),i=r(51223),o=r(44794);let c=n.RV,d=s.createContext({}),u=({...e})=>a.jsx(d.Provider,{value:{name:e.name},children:a.jsx(n.Qr,{...e})}),m=()=>{let e=s.useContext(d),t=s.useContext(p),{getFieldState:r,formState:a}=(0,n.Gc)(),l=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...l}},p=s.createContext({}),x=s.forwardRef(({className:e,...t},r)=>{let l=s.useId();return a.jsx(p.Provider,{value:{id:l},children:a.jsx("div",{ref:r,className:(0,i.cn)("space-y-2",e),...t})})});x.displayName="FormItem";let h=s.forwardRef(({className:e,...t},r)=>{let{error:s,formItemId:l}=m();return a.jsx(o.Label,{ref:r,className:(0,i.cn)(s&&"text-destructive",e),htmlFor:l,...t})});h.displayName="FormLabel";let f=s.forwardRef(({...e},t)=>{let{error:r,formItemId:s,formDescriptionId:n,formMessageId:i}=m();return a.jsx(l.g7,{ref:t,id:s,"aria-describedby":r?`${n} ${i}`:`${n}`,"aria-invalid":!!r,...e})});f.displayName="FormControl";let g=s.forwardRef(({className:e,...t},r)=>{let{formDescriptionId:s}=m();return a.jsx("p",{ref:r,id:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t})});g.displayName="FormDescription";let v=s.forwardRef(({className:e,children:t,...r},s)=>{let{error:l,formMessageId:n}=m(),o=l?String(l?.message):t;return o?a.jsx("p",{ref:s,id:n,className:(0,i.cn)("text-sm font-medium text-destructive",e),...r,children:o}):null});v.displayName="FormMessage"},44794:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Label:()=>c});var a=r(10326),s=r(17577),l=r(34478),n=r(28671),i=r(51223);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef(({className:e,...t},r)=>a.jsx(l.f,{ref:r,className:(0,i.cn)(o(),e),...t}));c.displayName=l.f.displayName},78062:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Separator:()=>i});var a=r(10326),s=r(17577),l=r(90220),n=r(51223);let i=s.forwardRef(({className:e,orientation:t="horizontal",decorative:r=!0,...s},i)=>a.jsx(l.f,{ref:i,decorative:r,orientation:t,className:(0,n.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...s}));i.displayName=l.f.displayName},45175:(e,t,r)=>{"use strict";r.d(t,{$:()=>m,y:()=>u});var a=r(10326),s=r(95920),l=r(79635),n=r(47546),i=r(48705),o=r(6343);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,r(80851).Z)("ImagePlus",[["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7",key:"31hg93"}],["line",{x1:"16",x2:"22",y1:"5",y2:"5",key:"ez7e4s"}],["line",{x1:"19",x2:"19",y1:"2",y2:"8",key:"1gkr8c"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var d=r(46226);let u=[{href:"#",icon:a.jsx(d.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:a.jsx(s.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/settings/personal-info",icon:a.jsx(l.Z,{className:"h-5 w-5"}),label:"Personal Info"},{href:"/freelancer/settings/professional-info",icon:a.jsx(n.Z,{className:"h-5 w-5"}),label:"Professional Info"},{href:"/freelancer/settings/projects",icon:a.jsx(i.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"/freelancer/settings/education-info",icon:a.jsx(o.Z,{className:"h-5 w-5"}),label:"Education"},{href:"/freelancer/settings/resume",icon:a.jsx(c,{className:"h-5 w-5"}),label:"Portfolio"}],m=[]},39958:(e,t,r)=>{"use strict";var a,s,l;r.d(t,{cd:()=>a,d8:()=>n,kJ:()=>s,sB:()=>l}),function(e){e.Mastery="Mastery",e.Proficient="Proficient",e.Beginner="Beginner"}(a||(a={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.REJECTED="Rejected",e.COMPLETED="Completed"}(s||(s={})),function(e){e.ACTIVE="ACTIVE",e.PENDING="PENDING",e.REJECTED="REJECTED",e.COMPLETED="COMPLETED"}(l||(l={}));let n={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},15187:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>l,default:()=>i});var a=r(68570);let s=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\settings\kyc\page.tsx`),{__esModule:l,$$typeof:n}=s;s.default;let i=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\settings\kyc\page.tsx#default`)},34478:(e,t,r)=>{"use strict";r.d(t,{f:()=>i});var a=r(17577),s=r(77335),l=r(10326),n=a.forwardRef((e,t)=>(0,l.jsx)(s.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var i=n},90220:(e,t,r)=>{"use strict";r.d(t,{f:()=>c});var a=r(17577),s=r(77335),l=r(10326),n="horizontal",i=["horizontal","vertical"],o=a.forwardRef((e,t)=>{let{decorative:r,orientation:a=n,...o}=e,c=i.includes(a)?a:n;return(0,l.jsx)(s.WV.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:t})});o.displayName="Separator";var c=o}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,6686,4736,6499,8066,588],()=>r(28482));module.exports=a})();