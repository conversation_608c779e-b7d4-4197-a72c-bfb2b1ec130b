{"/auth/forgot-password/page": "app/auth/forgot-password/page.js", "/_not-found/page": "app/_not-found/page.js", "/auth/sign-up/business/page": "app/auth/sign-up/business/page.js", "/auth/login/page": "app/auth/login/page.js", "/auth/sign-up/freelancer/page": "app/auth/sign-up/freelancer/page.js", "/bidmanagement/page": "app/bidmanagement/page.js", "/business/Projects/page": "app/business/Projects/page.js", "/business/add-project/page": "app/business/add-project/page.js", "/business/freelancerProfile/[freelancer_id]/page": "app/business/freelancerProfile/[freelancer_id]/page.js", "/business/market/accepted/page": "app/business/market/accepted/page.js", "/business/market/invited/page": "app/business/market/invited/page.js", "/business/market/page": "app/business/market/page.js", "/business/project/[project_id]/milestone/page": "app/business/project/[project_id]/milestone/page.js", "/business/market/rejected/page": "app/business/market/rejected/page.js", "/business/settings/business-info/page": "app/business/settings/business-info/page.js", "/business/project/[project_id]/page": "app/business/project/[project_id]/page.js", "/business/settings/kyc/page": "app/business/settings/kyc/page.js", "/business/talent/page": "app/business/talent/page.js", "/chat/page": "app/chat/page.js", "/consultancy/[freelancer_id]/page": "app/consultancy/[freelancer_id]/page.js", "/dashboard/business/talent/page": "app/dashboard/business/talent/page.js", "/dashboard/business/page": "app/dashboard/business/page.js", "/dashboard/freelancer/page": "app/dashboard/freelancer/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/freelancer/[freelancer_id]/freelancer-info/page": "app/freelancer/[freelancer_id]/freelancer-info/page.js", "/freelancer/businessProfile/[business_id]/page": "app/freelancer/businessProfile/[business_id]/page.js", "/freelancer/interview/bids/page": "app/freelancer/interview/bids/page.js", "/freelancer/interview/current/page": "app/freelancer/interview/current/page.js", "/freelancer/interview/start-interviewing/page": "app/freelancer/interview/start-interviewing/page.js", "/freelancer/interview/history/page": "app/freelancer/interview/history/page.js", "/freelancer/market/[business_id]/page": "app/freelancer/market/[business_id]/page.js", "/freelancer/interview/profile/page": "app/freelancer/interview/profile/page.js", "/freelancer/market/page": "app/freelancer/market/page.js", "/freelancer/market/project/[project_id]/apply/page": "app/freelancer/market/project/[project_id]/apply/page.js", "/freelancer/oracleDashboard/businessVerification/page": "app/freelancer/oracleDashboard/businessVerification/page.js", "/freelancer/oracleDashboard/educationVerification/page": "app/freelancer/oracleDashboard/educationVerification/page.js", "/freelancer/oracleDashboard/projectVerification/page": "app/freelancer/oracleDashboard/projectVerification/page.js", "/freelancer/oracleDashboard/workExpVerification/page": "app/freelancer/oracleDashboard/workExpVerification/page.js", "/freelancer/project/[project_id]/milestone/page": "app/freelancer/project/[project_id]/milestone/page.js", "/freelancer/project/[project_id]/page": "app/freelancer/project/[project_id]/page.js", "/freelancer/project/applied/page": "app/freelancer/project/applied/page.js", "/freelancer/project/completed/page": "app/freelancer/project/completed/page.js", "/freelancer/project/rejected/page": "app/freelancer/project/rejected/page.js", "/freelancer/scheduleInterview/page": "app/freelancer/scheduleInterview/page.js", "/freelancer/project/current/page": "app/freelancer/project/current/page.js", "/freelancer/settings/education-info/page": "app/freelancer/settings/education-info/page.js", "/freelancer/settings/kyc/page": "app/freelancer/settings/kyc/page.js", "/freelancer/settings/professional-info/page": "app/freelancer/settings/professional-info/page.js", "/freelancer/settings/personal-info/page": "app/freelancer/settings/personal-info/page.js", "/freelancer/settings/projects/page": "app/freelancer/settings/projects/page.js", "/freelancer/settings/resume/page": "app/freelancer/settings/resume/page.js", "/freelancer/talent/page": "app/freelancer/talent/page.js", "/home/<USER>/page": "app/home/<USER>/page.js", "/market/freelancer/project/page": "app/market/freelancer/project/page.js", "/notes/archive/page": "app/notes/archive/page.js", "/notes/trash/page": "app/notes/trash/page.js", "/notes/page": "app/notes/page.js", "/privacy/page": "app/privacy/page.js", "/page": "app/page.js", "/settings/support/page": "app/settings/support/page.js", "/profile/page": "app/profile/page.js"}