(()=>{var e={};e.id=5154,e.ids=[5154],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},82682:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c}),r(7851),r(54302),r(12523);var s=r(23191),a=r(88716),n=r(37922),i=r.n(n),l=r(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c=["",{children:["freelancer",{children:["settings",{children:["projects",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7851)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\settings\\projects\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\settings\\projects\\page.tsx"],m="/freelancer/settings/projects/page",x={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/freelancer/settings/projects/page",pathname:"/freelancer/settings/projects",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},1475:(e,t,r)=>{Promise.resolve().then(r.bind(r,3155))},6343:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},47546:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},37358:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},12893:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},40617:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},48705:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},3155:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>_});var s=r(10326),a=r(25842),n=r(17577),i=r(92166),l=r(6260),o=r(12893),c=r(40617),d=r(1370),m=r(38443),x=r(29752);let u=({projectName:e,description:t,verified:r,githubLink:a,start:n,end:i,refer:l,techUsed:u,role:h,projectType:p,comments:j})=>(0,s.jsxs)(x.Zb,{className:"w-full h-full mx-auto md:max-w-2xl ",children:[(0,s.jsxs)(x.Ol,{children:[(0,s.jsxs)(x.ll,{className:"flex",children:[e,a&&s.jsx("div",{className:"ml-auto",children:s.jsx("a",{href:a,className:"text-sm text-white underline",target:"_blank",rel:"noopener noreferrer",children:s.jsx(o.Z,{})})})]}),s.jsx(x.SZ,{className:"block mt-1 uppercase tracking-wide leading-tight font-medium text-white",children:p})]}),(0,s.jsxs)(x.aY,{children:[r?s.jsx(m.C,{className:"bg-success hover:bg-success",children:"VERIFIED"}):s.jsx(m.C,{className:"bg-warning hover:bg-warning",children:"PENDING"}),s.jsx("p",{className:"text-gray-300 pt-4",children:t}),(0,s.jsxs)("p",{className:"mt-2 flex text-gray-500 border p-3 rounded",children:[s.jsx(c.Z,{className:"pr-1"}),j]}),s.jsx("div",{className:"mt-4",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Reference: ",l]})}),s.jsx("div",{className:"my-4",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Role: ",h]})}),u.map((e,t)=>s.jsx(m.C,{className:"uppercase mx-1 text-xs font-normal bg-gray-300",children:e},t))]}),s.jsx(x.eW,{className:"flex",children:s.jsx(d.Z,{startDate:n,endDate:i})})]});var h=r(74064),p=r(74723),j=r(27256),f=r(83855),g=r(94019),y=r(86333),v=r(24230),b=r(35047),N=r(20495),D=r(24118),S=r(91664),w=r(9969),I=r(41190),k=r(56627),A=r(29280),E=r(30351);let P=j.z.object({projectName:j.z.string().min(1,{message:"Project name is required."}),description:j.z.string().min(1,{message:"Description is required."}),githubLink:j.z.string().url({message:"GitHub Repositry link must be a valid URL."}).optional().refine(e=>!e||e.startsWith("https://github.com/"),{message:"GitHub repository URL must start with https://github.com/"}),start:j.z.string().min(1,{message:"Start date is required."}),end:j.z.string().min(1,{message:"End date is required."}),refer:j.z.string().min(1,{message:"Reference is required."}),techUsed:j.z.array(j.z.string()).min(1,{message:"At least one technology is required."}),role:j.z.string().min(1,{message:"Role is required."}),projectType:j.z.string().optional(),verificationStatus:j.z.string().optional(),comments:j.z.string().optional()}).refine(e=>!e.start||!e.end||new Date(e.start)<new Date(e.end),{message:"Start Date must be before End Date",path:["end"]}),q=({onFormSubmit:e})=>{let[t,r]=(0,n.useState)(1),[a,i]=(0,n.useState)([]),[o,c]=(0,n.useState)([]),[d,x]=(0,n.useState)(""),[u,j]=(0,n.useState)(!1),[q,C]=(0,n.useState)(!1),Z=new Date().toISOString().split("T")[0],_=(0,n.useRef)(null),{freelancer_id:z}=(0,b.useParams)(),O=(0,p.cI)({resolver:(0,h.F)(P),defaultValues:{projectName:"",description:"",githubLink:"",start:"",end:"",refer:"",techUsed:[],role:"",projectType:"",verificationStatus:"ADDED",comments:""},mode:"all"}),R=()=>{let{projectName:e,description:t,start:r,end:s}=O.getValues(),a=new Date(r),n=new Date(s);return e&&t&&r&&s?a>=n?(O.setError("end",{type:"manual",message:"Start Date must be before End Date"}),!1):0!==o.length||((0,k.Am)({variant:"destructive",title:"Skills required",description:"Please add at least one skill."}),!1):((0,k.Am)({variant:"destructive",title:"Missing fields",description:"Please fill all required fields in Step 1."}),!1)},F=e=>{d.trim()&&!o.includes(d)&&(c([...o,d]),e.onChange([...o,d]),x(""))},G=e=>{c(o.filter(t=>t!==e))};(0,n.useEffect)(()=>{(async()=>{try{let e=await l.b.get("/skills"),t=e?.data?.data?.map(e=>({value:e.label,label:e.label}))||[];i(t)}catch(e){console.error("API Error:",e),(0,k.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}})()},[]),(0,n.useEffect)(()=>{q&&r(1)},[q,O]);let{handleSaveAndClose:T,showDraftDialog:M,setShowDraftDialog:X,confirmExitDialog:J,setConfirmExitDialog:V,handleDiscardAndClose:$,handleDialogClose:H,discardDraft:L,loadDraft:U}=(0,E.Z)({form:O,formSection:"projects",isDialogOpen:q,setIsDialogOpen:C,onSave:e=>{_.current={...e,techUsed:o}},onDiscard:()=>{_.current=null},setCurrSkills:c});async function W(t){j(!0);try{await l.b.post(`/freelancer/${z}/project`,{...t,techUsed:o,verified:!1,oracleAssigned:"",start:t.start?new Date(t.start).toISOString():null,end:t.end?new Date(t.end).toISOString():null,verificationUpdateTime:new Date().toISOString()}),e(),C(!1),(0,k.Am)({title:"Project Added",description:"The project has been successfully added."})}catch(e){console.error("API Error:",e),(0,k.Am)({variant:"destructive",title:"Error",description:"Failed to add project. Please try again later."})}finally{j(!1)}}return(0,s.jsxs)(D.Vq,{open:q,onOpenChange:e=>{C(e),e||H()},children:[s.jsx(D.hg,{asChild:!0,children:s.jsx(S.z,{variant:"outline",size:"icon",className:"my-auto",children:s.jsx(f.Z,{className:"h-4 w-4"})})}),(0,s.jsxs)(D.cZ,{className:"lg:max-w-screen-lg overflow-y-scroll max-h-screen no-scrollbar",children:[(0,s.jsxs)(D.fK,{children:[(0,s.jsxs)(D.$N,{children:["Add Project - Step ",t," of 2"]}),s.jsx(D.Be,{children:1===t?"Fill in the basic details of your project.":"Fill in the additional project details."})]}),s.jsx(w.l0,{...O,children:(0,s.jsxs)("form",{onSubmit:O.handleSubmit(W),className:"space-y-4",children:[1===t&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(w.Wi,{control:O.control,name:"projectName",render:({field:e})=>(0,s.jsxs)(w.xJ,{children:[s.jsx(w.lX,{children:"Project Name"}),s.jsx(w.NI,{children:s.jsx(I.I,{placeholder:"Enter project name",...e})}),s.jsx(w.pf,{children:"Enter the project name"}),s.jsx(w.zG,{})]})}),s.jsx(w.Wi,{control:O.control,name:"description",render:({field:e})=>(0,s.jsxs)(w.xJ,{children:[s.jsx(w.lX,{children:"Description"}),s.jsx(w.NI,{children:s.jsx(I.I,{placeholder:"Enter project description",...e})}),s.jsx(w.pf,{children:"Enter the project description"}),s.jsx(w.zG,{})]})}),s.jsx(w.Wi,{control:O.control,name:"start",render:({field:e})=>(0,s.jsxs)(w.xJ,{children:[s.jsx(w.lX,{children:"Start Date"}),s.jsx(w.NI,{children:s.jsx(I.I,{type:"date",max:Z,...e})}),s.jsx(w.pf,{children:"Select the start date"}),s.jsx(w.zG,{})]})}),s.jsx(w.Wi,{control:O.control,name:"end",render:({field:e})=>(0,s.jsxs)(w.xJ,{children:[s.jsx(w.lX,{children:"End Date"}),s.jsx(w.NI,{children:s.jsx(I.I,{type:"date",...e})}),s.jsx(w.pf,{children:"Select the end date"}),s.jsx(w.zG,{})]})}),s.jsx(w.Wi,{control:O.control,name:"techUsed",render:({field:e})=>(0,s.jsxs)(w.xJ,{className:"mb-4",children:[s.jsx(w.lX,{children:"Skills"}),s.jsx(w.NI,{children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center mt-2",children:[(0,s.jsxs)(A.Ph,{onValueChange:e=>{x(e)},value:d,children:[s.jsx(A.i4,{children:s.jsx(A.ki,{placeholder:"Select skill"})}),s.jsx(A.Bw,{children:a.map((e,t)=>s.jsx(A.Ql,{value:e.label,children:e.label},t))})]}),s.jsx(S.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",onClick:()=>F(e),children:s.jsx(f.Z,{className:"h-4 w-4"})})]}),s.jsx("div",{className:"flex flex-wrap mt-5",children:o.map((e,t)=>(0,s.jsxs)(m.C,{className:"uppercase mx-1 text-xs font-normal bg-gray-400 flex items-center my-2",children:[e,s.jsx("button",{type:"button",onClick:()=>G(e),className:"ml-2 text-red-500 hover:text-red-700",children:s.jsx(g.Z,{className:"h-4 w-4"})})]},t))})]})}),s.jsx(w.zG,{})]})})]}),2===t&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(w.Wi,{control:O.control,name:"githubLink",render:({field:e})=>(0,s.jsxs)(w.xJ,{children:[s.jsx(w.lX,{children:"GitHub Repo Link"}),s.jsx(w.NI,{children:s.jsx(I.I,{placeholder:"Enter GitHub repository link",...e})}),s.jsx(w.pf,{children:"Enter the GitHub repository link (optional)"}),s.jsx(w.zG,{})]})}),s.jsx(w.Wi,{control:O.control,name:"refer",render:({field:e})=>(0,s.jsxs)(w.xJ,{children:[s.jsx(w.lX,{children:"Reference"}),s.jsx(w.NI,{children:s.jsx(I.I,{placeholder:"Enter project reference",...e})}),s.jsx(w.pf,{children:"Enter the project reference"}),s.jsx(w.zG,{})]})}),s.jsx(w.Wi,{control:O.control,name:"role",render:({field:e})=>(0,s.jsxs)(w.xJ,{children:[s.jsx(w.lX,{children:"Role"}),s.jsx(w.NI,{children:s.jsx(I.I,{placeholder:"Enter role",...e})}),s.jsx(w.pf,{children:"Enter the role"}),s.jsx(w.zG,{})]})}),s.jsx(w.Wi,{control:O.control,name:"projectType",render:({field:e})=>(0,s.jsxs)(w.xJ,{children:[s.jsx(w.lX,{children:"Project Type"}),s.jsx(w.NI,{children:s.jsx(I.I,{placeholder:"Enter project type",...e})}),s.jsx(w.pf,{children:"Enter the project type (optional)"}),s.jsx(w.zG,{})]})}),s.jsx(w.Wi,{control:O.control,name:"comments",render:({field:e})=>(0,s.jsxs)(w.xJ,{children:[s.jsx(w.lX,{children:"Comments"}),s.jsx(w.NI,{children:s.jsx(I.I,{placeholder:"Enter any comments",...e})}),s.jsx(w.pf,{children:"Enter any comments (optional)"}),s.jsx(w.zG,{})]})})]}),s.jsx(D.cN,{className:"flex justify-between",children:2===t?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(S.z,{type:"button",variant:"outline",onClick:()=>{2===t&&r(1)},children:[s.jsx(y.Z,{className:"h-4 w-4 mr-2"}),"Back"]}),s.jsx(S.z,{type:"submit",disabled:u,children:u?"Loading...":"Add Project"})]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{})," ",(0,s.jsxs)(S.z,{type:"button",onClick:()=>{1===t&&R()&&r(2)},children:["Next",s.jsx(v.Z,{className:"h-4 w-4 ml-2"})]})]})})]})})]}),J&&s.jsx(N.Z,{dialogChange:J,setDialogChange:V,heading:"Save Draft?",desc:"Do you want to save your draft before leaving?",handleClose:$,handleSave:T,btn1Txt:"Don't save",btn2Txt:"Yes save"}),M&&s.jsx(N.Z,{dialogChange:M,setDialogChange:X,heading:"Load Draft?",desc:"You have unsaved data. Would you like to restore it?",handleClose:L,handleSave:U,btn1Txt:" No, start fresh",btn2Txt:"Yes, load draft"})]})};var C=r(45175),Z=r(40588);function _(){(0,a.v9)(e=>e.user);let[e,t]=(0,n.useState)(!1),[r,l]=(0,n.useState)([]);return(0,s.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[s.jsx(i.Z,{menuItemsTop:C.y,menuItemsBottom:C.$,active:"Projects",isKycCheck:!0}),(0,s.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[s.jsx(Z.Z,{menuItemsTop:C.y,menuItemsBottom:C.$,activeMenu:"Projects",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Settings",link:"#"},{label:"Projects",link:"#"}]}),(0,s.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8    grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3",children:[r.map((e,t)=>s.jsx(u,{...e},t)),s.jsx(q,{onFormSubmit:()=>{t(e=>!e)}})]})]})]})}},1370:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(10326);r(17577);var a=r(37358);let n=({startDate:e,endDate:t})=>{let r=e?new Date(e).toLocaleDateString():"Start Date N/A",n="current"!==t&&t?new Date(t).toLocaleDateString():"Still Going On!";return(0,s.jsxs)("div",{className:"flex relative whitespace-nowrap items-start sm:items-center gap-1 rounded-md ",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2 ",children:[s.jsx(a.Z,{className:"w-4 h-4 sm:w-5 sm:h-5 "}),s.jsx("span",{className:"text-xs sm:text-sm font-medium",children:`Start  ${r}`})]}),s.jsx("p",{children:"-"}),s.jsx("div",{className:"flex items-center ",children:s.jsx("span",{className:"text-xs sm:text-sm font-medium",children:` ${n}`})})]})}},20495:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var s=r(10326);r(17577);var a=r(24118),n=r(91664);let i=({dialogChange:e,setDialogChange:t,heading:r,desc:i,handleClose:l,handleSave:o,btn1Txt:c,btn2Txt:d})=>s.jsx(a.Vq,{open:e,onOpenChange:t,children:(0,s.jsxs)(a.cZ,{children:[(0,s.jsxs)(a.fK,{children:[s.jsx(a.$N,{children:r}),s.jsx(a.Be,{children:i})]}),(0,s.jsxs)(a.cN,{children:[s.jsx(n.z,{variant:"outline",onClick:l,children:c}),s.jsx(n.z,{onClick:o,children:d})]})]})})},9969:(e,t,r)=>{"use strict";r.d(t,{NI:()=>j,Wi:()=>m,l0:()=>c,lX:()=>p,pf:()=>f,xJ:()=>h,zG:()=>g});var s=r(10326),a=r(17577),n=r(99469),i=r(74723),l=r(51223),o=r(44794);let c=i.RV,d=a.createContext({}),m=({...e})=>s.jsx(d.Provider,{value:{name:e.name},children:s.jsx(i.Qr,{...e})}),x=()=>{let e=a.useContext(d),t=a.useContext(u),{getFieldState:r,formState:s}=(0,i.Gc)(),n=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...n}},u=a.createContext({}),h=a.forwardRef(({className:e,...t},r)=>{let n=a.useId();return s.jsx(u.Provider,{value:{id:n},children:s.jsx("div",{ref:r,className:(0,l.cn)("space-y-2",e),...t})})});h.displayName="FormItem";let p=a.forwardRef(({className:e,...t},r)=>{let{error:a,formItemId:n}=x();return s.jsx(o.Label,{ref:r,className:(0,l.cn)(a&&"text-destructive",e),htmlFor:n,...t})});p.displayName="FormLabel";let j=a.forwardRef(({...e},t)=>{let{error:r,formItemId:a,formDescriptionId:i,formMessageId:l}=x();return s.jsx(n.g7,{ref:t,id:a,"aria-describedby":r?`${i} ${l}`:`${i}`,"aria-invalid":!!r,...e})});j.displayName="FormControl";let f=a.forwardRef(({className:e,...t},r)=>{let{formDescriptionId:a}=x();return s.jsx("p",{ref:r,id:a,className:(0,l.cn)("text-sm text-muted-foreground",e),...t})});f.displayName="FormDescription";let g=a.forwardRef(({className:e,children:t,...r},a)=>{let{error:n,formMessageId:i}=x(),o=n?String(n?.message):t;return o?s.jsx("p",{ref:a,id:i,className:(0,l.cn)("text-sm font-medium text-destructive",e),...r,children:o}):null});g.displayName="FormMessage"},44794:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Label:()=>c});var s=r(10326),a=r(17577),n=r(34478),i=r(28671),l=r(51223);let o=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...t},r)=>s.jsx(n.f,{ref:r,className:(0,l.cn)(o(),e),...t}));c.displayName=n.f.displayName},45175:(e,t,r)=>{"use strict";r.d(t,{$:()=>x,y:()=>m});var s=r(10326),a=r(95920),n=r(79635),i=r(47546),l=r(48705),o=r(6343);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,r(80851).Z)("ImagePlus",[["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7",key:"31hg93"}],["line",{x1:"16",x2:"22",y1:"5",y2:"5",key:"ez7e4s"}],["line",{x1:"19",x2:"19",y1:"2",y2:"8",key:"1gkr8c"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var d=r(46226);let m=[{href:"#",icon:s.jsx(d.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:s.jsx(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/settings/personal-info",icon:s.jsx(n.Z,{className:"h-5 w-5"}),label:"Personal Info"},{href:"/freelancer/settings/professional-info",icon:s.jsx(i.Z,{className:"h-5 w-5"}),label:"Professional Info"},{href:"/freelancer/settings/projects",icon:s.jsx(l.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"/freelancer/settings/education-info",icon:s.jsx(o.Z,{className:"h-5 w-5"}),label:"Education"},{href:"/freelancer/settings/resume",icon:s.jsx(c,{className:"h-5 w-5"}),label:"Portfolio"}],x=[]},30351:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(17577),a=r(84097);let n=({form:e,formSection:t="",isDialogOpen:r,setIsDialogOpen:n,onSave:i,onDiscard:l,setCurrSkills:o})=>{let[c,d]=(0,s.useState)(!1),[m,x]=(0,s.useState)(!1),u=(0,s.useRef)(!1),h=(0,s.useRef)(null),p=e=>{if(!t)return;let r=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");r[t]&&(h.current=r[t]),Object.values(e).some(e=>void 0!==e&&""!==e)&&(r[t]=e,localStorage.setItem("DEHIX_DRAFT",JSON.stringify(r)),(0,a.Am)({title:"Draft Saved",description:`Your ${t} draft has been saved.`,duration:1500}))};(0,s.useEffect)(()=>{if(r&&!u.current&&t){let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");e&&e[t]&&d(!0),u.current=!0}},[r,t]);let j=e=>e&&"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"string"==typeof t?t.trim():t])):{};return{showDraftDialog:c,setShowDraftDialog:d,confirmExitDialog:m,setConfirmExitDialog:x,loadDraft:()=>{if(!t)return;let r=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");r&&r[t]&&(Object.keys(r[t]).forEach(e=>{void 0===r[t][e]&&delete r[t][e]}),"projects"===t&&(delete r[t].verificationStatus,Array.isArray(r[t].techUsed)&&o(r[t].techUsed)),Object.entries(r[t]).some(([,e])=>""!==e&&void 0!==e&&!(Array.isArray(e)&&0===e.length))&&e&&(e.reset(r[t]),h.current=r[t],(0,a.Am)({title:"Draft Loaded",description:`Your ${t} draft has been restored.`,duration:1500})),d(!1))},discardDraft:()=>{if(!t)return;let r=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");r&&(delete r[t],0===Object.keys(r).length?localStorage.removeItem("DEHIX_DRAFT"):localStorage.setItem("DEHIX_DRAFT",JSON.stringify(r))),e?.reset(),(0,a.Am)({title:"Draft Discarded",description:`Your ${t} draft has been discarded.`,duration:1500}),d(!1),l&&l()},handleSaveAndClose:()=>{if(!t)return;let r=e?.getValues();p(r),(0,a.Am)({title:"Draft Saved",description:"Your draft has been saved.",duration:1500}),h.current=r,x(!1),n&&n(!1),i&&i(r)},handleDiscardAndClose:()=>{if(!t)return;let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");delete e[t],0===Object.keys(e).length?localStorage.removeItem("DEHIX_DRAFT"):localStorage.setItem("DEHIX_DRAFT",JSON.stringify(e)),(0,a.Am)({title:"Draft Discarded",description:`Your ${t} draft has been discarded.`,duration:1500}),x(!1),n&&n(!1),l&&l()},handleDialogClose:()=>{if(!r||!t)return;let s=e?.getValues()||{},a=h.current||{},i=j(s),l=j(a),o=Object.entries(l).some(([e,t])=>{let r=i[e];return Array.isArray(t)&&Array.isArray(r)?JSON.stringify(t)!==JSON.stringify(r):t!==r}),c=Object.entries(i).some(([e,t])=>"verificationStatus"!==e&&void 0!==t&&""!==t&&void 0===l[e]);if(!o&&!c&&n){n(!1);return}Object.values(i).some(e=>e?.toString().trim())?x(!0):n&&n(!1)},saveDraft:p,hasOtherValues:(0,s.useCallback)(e=>Object.entries(e).some(([e,t])=>"profiles"!==e&&(Array.isArray(t)&&t.length>0&&("urls"!==e||t.some(e=>e?.value?.trim()!==""))||"string"==typeof t&&""!==t.trim()||"number"==typeof t&&!isNaN(t))),[]),hasProfiles:(0,s.useCallback)(e=>e?.some(e=>Object.values(e).some(e=>Array.isArray(e)&&e.length>0||"string"==typeof e&&""!==e.trim()||"number"==typeof e&&!isNaN(e))),[])}}},7851:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>l});var s=r(68570);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\settings\projects\page.tsx`),{__esModule:n,$$typeof:i}=a;a.default;let l=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\settings\projects\page.tsx#default`)},34478:(e,t,r)=>{"use strict";r.d(t,{f:()=>l});var s=r(17577),a=r(77335),n=r(10326),i=s.forwardRef((e,t)=>(0,n.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,6686,4736,6499,8066,588],()=>r(82682));module.exports=s})();