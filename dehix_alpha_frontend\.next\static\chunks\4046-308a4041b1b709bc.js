"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4046],{1282:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},92940:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Circle<PERSON>heckBig",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6884:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},45188:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},63550:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},60143:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},92513:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},71510:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},6649:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},68279:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},64756:function(e,t,n){n.d(t,{VY:function(){return el},h4:function(){return eo},ck:function(){return er},fC:function(){return en},xz:function(){return ei}});var r=n(2265),o=n(98324),i=n(921),l=n(1584),u=n(78149),a=n(91715),c=n(18676),s=n(1336),d=n(31383),f=n(53201),p=n(57437),v="Collapsible",[m,h]=(0,o.b)(v),[g,y]=m(v),b=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,open:o,defaultOpen:i,disabled:l,onOpenChange:u,...s}=e,[d=!1,v]=(0,a.T)({prop:o,defaultProp:i,onChange:u});return(0,p.jsx)(g,{scope:n,disabled:l,contentId:(0,f.M)(),open:d,onOpenToggle:r.useCallback(()=>v(e=>!e),[v]),children:(0,p.jsx)(c.WV.div,{"data-state":S(d),"data-disabled":l?"":void 0,...s,ref:t})})});b.displayName=v;var E="CollapsibleTrigger",w=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,...r}=e,o=y(E,n);return(0,p.jsx)(c.WV.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":S(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...r,ref:t,onClick:(0,u.M)(e.onClick,o.onOpenToggle)})});w.displayName=E;var x="CollapsibleContent",C=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=y(x,e.__scopeCollapsible);return(0,p.jsx)(d.z,{present:n||o.open,children:e=>{let{present:n}=e;return(0,p.jsx)(k,{...r,ref:t,present:n})}})});C.displayName=x;var k=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,present:o,children:i,...u}=e,a=y(x,n),[d,f]=r.useState(o),v=r.useRef(null),m=(0,l.e)(t,v),h=r.useRef(0),g=h.current,b=r.useRef(0),E=b.current,w=a.open||d,C=r.useRef(w),k=r.useRef();return r.useEffect(()=>{let e=requestAnimationFrame(()=>C.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.b)(()=>{let e=v.current;if(e){k.current=k.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();h.current=t.height,b.current=t.width,C.current||(e.style.transitionDuration=k.current.transitionDuration,e.style.animationName=k.current.animationName),f(o)}},[a.open,o]),(0,p.jsx)(c.WV.div,{"data-state":S(a.open),"data-disabled":a.disabled?"":void 0,id:a.contentId,hidden:!w,...u,ref:m,style:{"--radix-collapsible-content-height":g?"".concat(g,"px"):void 0,"--radix-collapsible-content-width":E?"".concat(E,"px"):void 0,...e.style},children:w&&i})});function S(e){return e?"open":"closed"}var R=n(87513),D="Accordion",O=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[P,N,A]=(0,i.B)(D),[I,M]=(0,o.b)(D,[A,h]),T=h(),L=r.forwardRef((e,t)=>{let{type:n,...r}=e;return(0,p.jsx)(P.Provider,{scope:e.__scopeAccordion,children:"multiple"===n?(0,p.jsx)(H,{...r,ref:t}):(0,p.jsx)(U,{...r,ref:t})})});L.displayName=D;var[F,j]=I(D),[_,V]=I(D,{collapsible:!1}),U=r.forwardRef((e,t)=>{let{value:n,defaultValue:o,onValueChange:i=()=>{},collapsible:l=!1,...u}=e,[c,s]=(0,a.T)({prop:n,defaultProp:o,onChange:i});return(0,p.jsx)(F,{scope:e.__scopeAccordion,value:c?[c]:[],onItemOpen:s,onItemClose:r.useCallback(()=>l&&s(""),[l,s]),children:(0,p.jsx)(_,{scope:e.__scopeAccordion,collapsible:l,children:(0,p.jsx)(z,{...u,ref:t})})})}),H=r.forwardRef((e,t)=>{let{value:n,defaultValue:o,onValueChange:i=()=>{},...l}=e,[u=[],c]=(0,a.T)({prop:n,defaultProp:o,onChange:i}),s=r.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...t,e]}),[c]),d=r.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(t=>t!==e)}),[c]);return(0,p.jsx)(F,{scope:e.__scopeAccordion,value:u,onItemOpen:s,onItemClose:d,children:(0,p.jsx)(_,{scope:e.__scopeAccordion,collapsible:!0,children:(0,p.jsx)(z,{...l,ref:t})})})}),[Z,$]=I(D),z=r.forwardRef((e,t)=>{let{__scopeAccordion:n,disabled:o,dir:i,orientation:a="vertical",...s}=e,d=r.useRef(null),f=(0,l.e)(d,t),v=N(n),m="ltr"===(0,R.gm)(i),h=(0,u.M)(e.onKeyDown,e=>{var t;if(!O.includes(e.key))return;let n=e.target,r=v().filter(e=>{var t;return!(null===(t=e.ref.current)||void 0===t?void 0:t.disabled)}),o=r.findIndex(e=>e.ref.current===n),i=r.length;if(-1===o)return;e.preventDefault();let l=o,u=i-1,c=()=>{(l=o+1)>u&&(l=0)},s=()=>{(l=o-1)<0&&(l=u)};switch(e.key){case"Home":l=0;break;case"End":l=u;break;case"ArrowRight":"horizontal"===a&&(m?c():s());break;case"ArrowDown":"vertical"===a&&c();break;case"ArrowLeft":"horizontal"===a&&(m?s():c());break;case"ArrowUp":"vertical"===a&&s()}null===(t=r[l%i].ref.current)||void 0===t||t.focus()});return(0,p.jsx)(Z,{scope:n,disabled:o,direction:i,orientation:a,children:(0,p.jsx)(P.Slot,{scope:n,children:(0,p.jsx)(c.WV.div,{...s,"data-orientation":a,ref:f,onKeyDown:o?void 0:h})})})}),W="AccordionItem",[B,q]=I(W),K=r.forwardRef((e,t)=>{let{__scopeAccordion:n,value:r,...o}=e,i=$(W,n),l=j(W,n),u=T(n),a=(0,f.M)(),c=r&&l.value.includes(r)||!1,s=i.disabled||e.disabled;return(0,p.jsx)(B,{scope:n,open:c,disabled:s,triggerId:a,children:(0,p.jsx)(b,{"data-orientation":i.orientation,"data-state":et(c),...u,...o,ref:t,disabled:s,open:c,onOpenChange:e=>{e?l.onItemOpen(r):l.onItemClose(r)}})})});K.displayName=W;var Y="AccordionHeader",X=r.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,o=$(D,n),i=q(Y,n);return(0,p.jsx)(c.WV.h3,{"data-orientation":o.orientation,"data-state":et(i.open),"data-disabled":i.disabled?"":void 0,...r,ref:t})});X.displayName=Y;var J="AccordionTrigger",G=r.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,o=$(D,n),i=q(J,n),l=V(J,n),u=T(n);return(0,p.jsx)(P.ItemSlot,{scope:n,children:(0,p.jsx)(w,{"aria-disabled":i.open&&!l.collapsible||void 0,"data-orientation":o.orientation,id:i.triggerId,...u,...r,ref:t})})});G.displayName=J;var Q="AccordionContent",ee=r.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,o=$(D,n),i=q(Q,n),l=T(n);return(0,p.jsx)(C,{role:"region","aria-labelledby":i.triggerId,"data-orientation":o.orientation,...l,...r,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function et(e){return e?"open":"closed"}ee.displayName=Q;var en=L,er=K,eo=X,ei=G,el=ee},68602:function(e,t,n){n.d(t,{VY:function(){return A},fC:function(){return P},xz:function(){return N}});var r,o=n(2265),i=n(78149),l=n(98324),u=n(91715),a=n(1584),c=n(25510),s=(n(56935),n(31383)),d=n(18676),f=n(53938),p=n(57437),v="HoverCard",[m,h]=(0,l.b)(v,[c.D7]),g=(0,c.D7)(),[y,b]=m(v),E=e=>{let{__scopeHoverCard:t,children:n,open:r,defaultOpen:i,onOpenChange:l,openDelay:a=700,closeDelay:s=300}=e,d=g(t),f=o.useRef(0),v=o.useRef(0),m=o.useRef(!1),h=o.useRef(!1),[b=!1,E]=(0,u.T)({prop:r,defaultProp:i,onChange:l}),w=o.useCallback(()=>{clearTimeout(v.current),f.current=window.setTimeout(()=>E(!0),a)},[a,E]),x=o.useCallback(()=>{clearTimeout(f.current),m.current||h.current||(v.current=window.setTimeout(()=>E(!1),s))},[s,E]),C=o.useCallback(()=>E(!1),[E]);return o.useEffect(()=>()=>{clearTimeout(f.current),clearTimeout(v.current)},[]),(0,p.jsx)(y,{scope:t,open:b,onOpenChange:E,onOpen:w,onClose:x,onDismiss:C,hasSelectionRef:m,isPointerDownOnContentRef:h,children:(0,p.jsx)(c.fC,{...d,children:n})})};E.displayName=v;var w="HoverCardTrigger",x=o.forwardRef((e,t)=>{let{__scopeHoverCard:n,...r}=e,o=b(w,n),l=g(n);return(0,p.jsx)(c.ee,{asChild:!0,...l,children:(0,p.jsx)(d.WV.a,{"data-state":o.open?"open":"closed",...r,ref:t,onPointerEnter:(0,i.M)(e.onPointerEnter,O(o.onOpen)),onPointerLeave:(0,i.M)(e.onPointerLeave,O(o.onClose)),onFocus:(0,i.M)(e.onFocus,o.onOpen),onBlur:(0,i.M)(e.onBlur,o.onClose),onTouchStart:(0,i.M)(e.onTouchStart,e=>e.preventDefault())})})});x.displayName=w;var[C,k]=m("HoverCardPortal",{forceMount:void 0}),S="HoverCardContent",R=o.forwardRef((e,t)=>{let n=k(S,e.__scopeHoverCard),{forceMount:r=n.forceMount,...o}=e,l=b(S,e.__scopeHoverCard);return(0,p.jsx)(s.z,{present:r||l.open,children:(0,p.jsx)(D,{"data-state":l.open?"open":"closed",...o,onPointerEnter:(0,i.M)(e.onPointerEnter,O(l.onOpen)),onPointerLeave:(0,i.M)(e.onPointerLeave,O(l.onClose)),ref:t})})});R.displayName=S;var D=o.forwardRef((e,t)=>{let{__scopeHoverCard:n,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:s,onInteractOutside:d,...v}=e,m=b(S,n),h=g(n),y=o.useRef(null),E=(0,a.e)(t,y),[w,x]=o.useState(!1);return o.useEffect(()=>{if(w){let e=document.body;return r=e.style.userSelect||e.style.webkitUserSelect,e.style.userSelect="none",e.style.webkitUserSelect="none",()=>{e.style.userSelect=r,e.style.webkitUserSelect=r}}},[w]),o.useEffect(()=>{if(y.current){let e=()=>{x(!1),m.isPointerDownOnContentRef.current=!1,setTimeout(()=>{var e;(null===(e=document.getSelection())||void 0===e?void 0:e.toString())!==""&&(m.hasSelectionRef.current=!0)})};return document.addEventListener("pointerup",e),()=>{document.removeEventListener("pointerup",e),m.hasSelectionRef.current=!1,m.isPointerDownOnContentRef.current=!1}}},[m.isPointerDownOnContentRef,m.hasSelectionRef]),o.useEffect(()=>{y.current&&(function(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP});for(;n.nextNode();)t.push(n.currentNode);return t})(y.current).forEach(e=>e.setAttribute("tabindex","-1"))}),(0,p.jsx)(f.XB,{asChild:!0,disableOutsidePointerEvents:!1,onInteractOutside:d,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:(0,i.M)(s,e=>{e.preventDefault()}),onDismiss:m.onDismiss,children:(0,p.jsx)(c.VY,{...h,...v,onPointerDown:(0,i.M)(v.onPointerDown,e=>{e.currentTarget.contains(e.target)&&x(!0),m.hasSelectionRef.current=!1,m.isPointerDownOnContentRef.current=!0}),ref:E,style:{...v.style,userSelect:w?"text":void 0,WebkitUserSelect:w?"text":void 0,"--radix-hover-card-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-hover-card-content-available-width":"var(--radix-popper-available-width)","--radix-hover-card-content-available-height":"var(--radix-popper-available-height)","--radix-hover-card-trigger-width":"var(--radix-popper-anchor-width)","--radix-hover-card-trigger-height":"var(--radix-popper-anchor-height)"}})})});function O(e){return t=>"touch"===t.pointerType?void 0:e()}o.forwardRef((e,t)=>{let{__scopeHoverCard:n,...r}=e,o=g(n);return(0,p.jsx)(c.Eh,{...o,...r,ref:t})}).displayName="HoverCardArrow";var P=E,N=x,A=R},35353:function(e,t,n){let r,o;n.d(t,{mY:function(){return e3}});var i=/[\\\/_+.#"@\[\(\{&]/,l=/[\\\/_+.#"@\[\(\{&]/g,u=/[\s-]/,a=/[\s-]/g;function c(e){return e.toLowerCase().replace(a," ")}function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}var d=n(2265),f=n.t(d,2);function p(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(null==e||e(r),!1===n||!r.defaultPrevented)return null==t?void 0:t(r)}}function v(...e){return t=>e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}function m(...e){return(0,d.useCallback)(v(...e),e)}let h=(null==globalThis?void 0:globalThis.document)?d.useLayoutEffect:()=>{},g=f["useId".toString()]||(()=>void 0),y=0;function b(e){let[t,n]=d.useState(g());return h(()=>{e||n(e=>null!=e?e:String(y++))},[e]),e||(t?`radix-${t}`:"")}function E(e){let t=(0,d.useRef)(e);return(0,d.useEffect)(()=>{t.current=e}),(0,d.useMemo)(()=>(...e)=>{var n;return null===(n=t.current)||void 0===n?void 0:n.call(t,...e)},[])}var w=n(54887);let x=(0,d.forwardRef)((e,t)=>{let{children:n,...r}=e,o=d.Children.toArray(n),i=o.find(S);if(i){let e=i.props.children,n=o.map(t=>t!==i?t:d.Children.count(e)>1?d.Children.only(null):(0,d.isValidElement)(e)?e.props.children:null);return(0,d.createElement)(C,s({},r,{ref:t}),(0,d.isValidElement)(e)?(0,d.cloneElement)(e,void 0,n):null)}return(0,d.createElement)(C,s({},r,{ref:t}),n)});x.displayName="Slot";let C=(0,d.forwardRef)((e,t)=>{let{children:n,...r}=e;return(0,d.isValidElement)(n)?(0,d.cloneElement)(n,{...function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props),ref:t?v(t,n.ref):n.ref}):d.Children.count(n)>1?d.Children.only(null):null});C.displayName="SlotClone";let k=({children:e})=>(0,d.createElement)(d.Fragment,null,e);function S(e){return(0,d.isValidElement)(e)&&e.type===k}let R=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=(0,d.forwardRef)((e,n)=>{let{asChild:r,...o}=e,i=r?x:t;return(0,d.useEffect)(()=>{window[Symbol.for("radix-ui")]=!0},[]),(0,d.createElement)(i,s({},o,{ref:n}))});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),D="dismissableLayer.update",O=(0,d.createContext)({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),P=(0,d.forwardRef)((e,t)=>{var n;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:u,onInteractOutside:a,onDismiss:c,...f}=e,v=(0,d.useContext)(O),[h,g]=(0,d.useState)(null),y=null!==(n=null==h?void 0:h.ownerDocument)&&void 0!==n?n:null==globalThis?void 0:globalThis.document,[,b]=(0,d.useState)({}),w=m(t,e=>g(e)),x=Array.from(v.layers),[C]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),k=x.indexOf(C),S=h?x.indexOf(h):-1,P=v.layersWithOutsidePointerEventsDisabled.size>0,I=S>=k,M=function(e,t=null==globalThis?void 0:globalThis.document){let n=E(e),r=(0,d.useRef)(!1),o=(0,d.useRef)(()=>{});return(0,d.useEffect)(()=>{let e=e=>{if(e.target&&!r.current){let r={originalEvent:e};function i(){A("dismissableLayer.pointerDownOutside",n,r,{discrete:!0})}"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=i,t.addEventListener("click",o.current,{once:!0})):i()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...v.branches].some(e=>e.contains(t));!I||n||(null==l||l(e),null==a||a(e),e.defaultPrevented||null==c||c())},y),T=function(e,t=null==globalThis?void 0:globalThis.document){let n=E(e),r=(0,d.useRef)(!1);return(0,d.useEffect)(()=>{let e=e=>{e.target&&!r.current&&A("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...v.branches].some(e=>e.contains(t))||(null==u||u(e),null==a||a(e),e.defaultPrevented||null==c||c())},y);return!function(e,t=null==globalThis?void 0:globalThis.document){let n=E(e);(0,d.useEffect)(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e),()=>t.removeEventListener("keydown",e)},[n,t])}(e=>{S!==v.layers.size-1||(null==i||i(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))},y),(0,d.useEffect)(()=>{if(h)return o&&(0===v.layersWithOutsidePointerEventsDisabled.size&&(r=y.body.style.pointerEvents,y.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(h)),v.layers.add(h),N(),()=>{o&&1===v.layersWithOutsidePointerEventsDisabled.size&&(y.body.style.pointerEvents=r)}},[h,y,o,v]),(0,d.useEffect)(()=>()=>{h&&(v.layers.delete(h),v.layersWithOutsidePointerEventsDisabled.delete(h),N())},[h,v]),(0,d.useEffect)(()=>{let e=()=>b({});return document.addEventListener(D,e),()=>document.removeEventListener(D,e)},[]),(0,d.createElement)(R.div,s({},f,{ref:w,style:{pointerEvents:P?I?"auto":"none":void 0,...e.style},onFocusCapture:p(e.onFocusCapture,T.onFocusCapture),onBlurCapture:p(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:p(e.onPointerDownCapture,M.onPointerDownCapture)}))});function N(){let e=new CustomEvent(D);document.dispatchEvent(e)}function A(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});(t&&o.addEventListener(e,t,{once:!0}),r)?o&&(0,w.flushSync)(()=>o.dispatchEvent(i)):o.dispatchEvent(i)}let I="focusScope.autoFocusOnMount",M="focusScope.autoFocusOnUnmount",T={bubbles:!1,cancelable:!0},L=(0,d.forwardRef)((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[u,a]=(0,d.useState)(null),c=E(o),f=E(i),p=(0,d.useRef)(null),v=m(t,e=>a(e)),h=(0,d.useRef)({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;(0,d.useEffect)(()=>{if(r){function e(e){if(h.paused||!u)return;let t=e.target;u.contains(t)?p.current=t:_(p.current,{select:!0})}function t(e){if(h.paused||!u)return;let t=e.relatedTarget;null===t||u.contains(t)||_(p.current,{select:!0})}document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&_(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,h.paused]),(0,d.useEffect)(()=>{if(u){V.add(h);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(I,T);u.addEventListener(I,c),u.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(_(r,{select:t}),document.activeElement!==n)return}(F(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&_(u))}return()=>{u.removeEventListener(I,c),setTimeout(()=>{let t=new CustomEvent(M,T);u.addEventListener(M,f),u.dispatchEvent(t),t.defaultPrevented||_(null!=e?e:document.body,{select:!0}),u.removeEventListener(M,f),V.remove(h)},0)}}},[u,c,f,h]);let g=(0,d.useCallback)(e=>{if(!n&&!r||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=F(e);return[j(t,e),j(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&_(i,{select:!0})):(e.preventDefault(),n&&_(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,h.paused]);return(0,d.createElement)(R.div,s({tabIndex:-1},l,{ref:v,onKeyDown:g}))});function F(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function j(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function _(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}let V=(o=[],{add(e){let t=o[0];e!==t&&(null==t||t.pause()),(o=U(o,e)).unshift(e)},remove(e){var t;null===(t=(o=U(o,e))[0])||void 0===t||t.resume()}});function U(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}let H=(0,d.forwardRef)((e,t)=>{var n;let{container:r=null==globalThis?void 0:null===(n=globalThis.document)||void 0===n?void 0:n.body,...o}=e;return r?w.createPortal((0,d.createElement)(R.div,s({},o,{ref:t})),r):null}),Z=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,o]=(0,d.useState)(),i=(0,d.useRef)({}),l=(0,d.useRef)(e),u=(0,d.useRef)("none"),[a,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},(0,d.useReducer)((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return(0,d.useEffect)(()=>{let e=$(i.current);u.current="mounted"===a?e:"none"},[a]),h(()=>{let t=i.current,n=l.current;if(n!==e){let r=u.current,o=$(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),l.current=e}},[e,c]),h(()=>{if(r){let e=e=>{let t=$(i.current).includes(e.animationName);e.target===r&&t&&(0,w.flushSync)(()=>c("ANIMATION_END"))},t=e=>{e.target===r&&(u.current=$(i.current))};return r.addEventListener("animationstart",t),r.addEventListener("animationcancel",e),r.addEventListener("animationend",e),()=>{r.removeEventListener("animationstart",t),r.removeEventListener("animationcancel",e),r.removeEventListener("animationend",e)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:(0,d.useCallback)(e=>{e&&(i.current=getComputedStyle(e)),o(e)},[])}}(t),o="function"==typeof n?n({present:r.isPresent}):d.Children.only(n),i=m(r.ref,o.ref);return"function"==typeof n||r.isPresent?(0,d.cloneElement)(o,{ref:i}):null};function $(e){return(null==e?void 0:e.animationName)||"none"}Z.displayName="Presence";let z=0;function W(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}var B=n(11735),q=n(91584),K=n(28215),Y=(0,n(15411)._)(),X=function(){},J=d.forwardRef(function(e,t){var n=d.useRef(null),r=d.useState({onScrollCapture:X,onWheelCapture:X,onTouchMoveCapture:X}),o=r[0],i=r[1],l=e.forwardProps,u=e.children,a=e.className,c=e.removeScrollBar,s=e.enabled,f=e.shards,p=e.sideCar,v=e.noIsolation,m=e.inert,h=e.allowPinchZoom,g=e.as,y=(0,B._T)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),b=(0,K.q)([n,t]),E=(0,B.pi)((0,B.pi)({},y),o);return d.createElement(d.Fragment,null,s&&d.createElement(p,{sideCar:Y,removeScrollBar:c,shards:f,noIsolation:v,inert:m,setCallbacks:i,allowPinchZoom:!!h,lockRef:n}),l?d.cloneElement(d.Children.only(u),(0,B.pi)((0,B.pi)({},E),{ref:b})):d.createElement(void 0===g?"div":g,(0,B.pi)({},E,{className:a,ref:b}),u))});J.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},J.classNames={fullWidth:q.zi,zeroRight:q.pF};var G=n(48776),Q=n(65973),ee=n(18039),et=!1;if("undefined"!=typeof window)try{var en=Object.defineProperty({},"passive",{get:function(){return et=!0,!0}});window.addEventListener("test",en,en),window.removeEventListener("test",en,en)}catch(e){et=!1}var er=!!et&&{passive:!1},eo=function(e,t){var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},ei=function(e,t){var n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),el(e,n)){var r=eu(e,n);if(r[1]>r[2])return!0}n=n.parentNode}while(n&&n!==document.body);return!1},el=function(e,t){return"v"===e?eo(t,"overflowY"):eo(t,"overflowX")},eu=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},ea=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),u=l*r,a=n.target,c=t.contains(a),s=!1,d=u>0,f=0,p=0;do{var v=eu(e,a),m=v[0],h=v[1]-v[2]-l*m;(m||h)&&el(e,a)&&(f+=h,p+=m),a=a.parentNode}while(!c&&a!==document.body||c&&(t.contains(a)||t===a));return d&&(o&&0===f||!o&&u>f)?s=!0:!d&&(o&&0===p||!o&&-u>p)&&(s=!0),s},ec=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},es=function(e){return[e.deltaX,e.deltaY]},ed=function(e){return e&&"current"in e?e.current:e},ef=0,ep=[],ev=(0,G.L)(Y,function(e){var t=d.useRef([]),n=d.useRef([0,0]),r=d.useRef(),o=d.useState(ef++)[0],i=d.useState(function(){return(0,ee.Ws)()})[0],l=d.useRef(e);d.useEffect(function(){l.current=e},[e]),d.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,B.ev)([e.lockRef.current],(e.shards||[]).map(ed),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=d.useCallback(function(e,t){if("touches"in e&&2===e.touches.length)return!l.current.allowPinchZoom;var o,i=ec(e),u=n.current,a="deltaX"in e?e.deltaX:u[0]-i[0],c="deltaY"in e?e.deltaY:u[1]-i[1],s=e.target,d=Math.abs(a)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=ei(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=ei(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(a||c)&&(r.current=o),!o)return!0;var p=r.current||o;return ea(p,t,e,"h"===p?a:c,!0)},[]),a=d.useCallback(function(e){if(ep.length&&ep[ep.length-1]===i){var n="deltaY"in e?es(e):ec(e),r=t.current.filter(function(t){var r;return t.name===e.type&&t.target===e.target&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(ed).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=d.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),s=d.useCallback(function(e){n.current=ec(e),r.current=void 0},[]),f=d.useCallback(function(t){c(t.type,es(t),t.target,u(t,e.lockRef.current))},[]),p=d.useCallback(function(t){c(t.type,ec(t),t.target,u(t,e.lockRef.current))},[]);d.useEffect(function(){return ep.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",a,er),document.addEventListener("touchmove",a,er),document.addEventListener("touchstart",s,er),function(){ep=ep.filter(function(e){return e!==i}),document.removeEventListener("wheel",a,er),document.removeEventListener("touchmove",a,er),document.removeEventListener("touchstart",s,er)}},[]);var v=e.removeScrollBar,m=e.inert;return d.createElement(d.Fragment,null,m?d.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?d.createElement(Q.jp,{gapMode:"margin"}):null)}),em=d.forwardRef(function(e,t){return d.createElement(J,(0,B.pi)({},e,{ref:t,sideCar:ev}))});em.classNames=J.classNames;var eh=n(78369);let eg="Dialog",[ey,eb]=function(e,t=[]){let n=[],r=()=>{let t=n.map(e=>(0,d.createContext)(e));return function(n){let r=(null==n?void 0:n[e])||t;return(0,d.useMemo)(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=(0,d.createContext)(r),i=n.length;function l(t){let{scope:n,children:r,...l}=t,u=(null==n?void 0:n[e][i])||o,a=(0,d.useMemo)(()=>l,Object.values(l));return(0,d.createElement)(u.Provider,{value:a},r)}return n=[...n,r],l.displayName=t+"Provider",[l,function(n,l){let u=(null==l?void 0:l[e][i])||o,a=(0,d.useContext)(u);if(a)return a;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return(0,d.useMemo)(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}(eg),[eE,ew]=ey(eg),ex="DialogPortal",[eC,ek]=ey(ex,{forceMount:void 0}),eS="DialogOverlay",eR=(0,d.forwardRef)((e,t)=>{let n=ek(eS,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=ew(eS,e.__scopeDialog);return i.modal?(0,d.createElement)(Z,{present:r||i.open},(0,d.createElement)(eD,s({},o,{ref:t}))):null}),eD=(0,d.forwardRef)((e,t)=>{let{__scopeDialog:n,...r}=e,o=ew(eS,n);return(0,d.createElement)(em,{as:x,allowPinchZoom:!0,shards:[o.contentRef]},(0,d.createElement)(R.div,s({"data-state":eM(o.open)},r,{ref:t,style:{pointerEvents:"auto",...r.style}})))}),eO="DialogContent",eP=(0,d.forwardRef)((e,t)=>{let n=ek(eO,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=ew(eO,e.__scopeDialog);return(0,d.createElement)(Z,{present:r||i.open},i.modal?(0,d.createElement)(eN,s({},o,{ref:t})):(0,d.createElement)(eA,s({},o,{ref:t})))}),eN=(0,d.forwardRef)((e,t)=>{let n=ew(eO,e.__scopeDialog),r=(0,d.useRef)(null),o=m(t,n.contentRef,r);return(0,d.useEffect)(()=>{let e=r.current;if(e)return(0,eh.Ry)(e)},[]),(0,d.createElement)(eI,s({},e,{ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:p(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:p(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:p(e.onFocusOutside,e=>e.preventDefault())}))}),eA=(0,d.forwardRef)((e,t)=>{let n=ew(eO,e.__scopeDialog),r=(0,d.useRef)(!1),o=(0,d.useRef)(!1);return(0,d.createElement)(eI,s({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var i,l;null===(i=e.onCloseAutoFocus)||void 0===i||i.call(e,t),t.defaultPrevented||(r.current||null===(l=n.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var i,l;null===(i=e.onInteractOutside)||void 0===i||i.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let u=t.target;(null===(l=n.triggerRef.current)||void 0===l?void 0:l.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}}))}),eI=(0,d.forwardRef)((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,...l}=e,u=ew(eO,n),a=m(t,(0,d.useRef)(null));return(0,d.useEffect)(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:W()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:W()),z++,()=>{1===z&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),z--}},[]),(0,d.createElement)(d.Fragment,null,(0,d.createElement)(L,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i},(0,d.createElement)(P,s({role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":eM(u.open)},l,{ref:a,onDismiss:()=>u.onOpenChange(!1)}))),!1)});function eM(e){return e?"open":"closed"}let[eT,eL]=function(e,t){let n=(0,d.createContext)(t);function r(e){let{children:t,...r}=e,o=(0,d.useMemo)(()=>r,Object.values(r));return(0,d.createElement)(n.Provider,{value:o},t)}return r.displayName=e+"Provider",[r,function(r){let o=(0,d.useContext)(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}("DialogTitleWarning",{contentName:eO,titleName:"DialogTitle",docsSlug:"dialog"}),eF=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:i,modal:l=!0}=e,u=(0,d.useRef)(null),a=(0,d.useRef)(null),[c=!1,s]=function({prop:e,defaultProp:t,onChange:n=()=>{}}){let[r,o]=function({defaultProp:e,onChange:t}){let n=(0,d.useState)(e),[r]=n,o=(0,d.useRef)(r),i=E(t);return(0,d.useEffect)(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}({defaultProp:t,onChange:n}),i=void 0!==e,l=i?e:r,u=E(n);return[l,(0,d.useCallback)(t=>{if(i){let n="function"==typeof t?t(e):t;n!==e&&u(n)}else o(t)},[i,e,o,u])]}({prop:r,defaultProp:o,onChange:i});return(0,d.createElement)(eE,{scope:t,triggerRef:u,contentRef:a,contentId:b(),titleId:b(),descriptionId:b(),open:c,onOpenChange:s,onOpenToggle:(0,d.useCallback)(()=>s(e=>!e),[s]),modal:l},n)},ej=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:o}=e,i=ew(ex,t);return(0,d.createElement)(eC,{scope:t,forceMount:n},d.Children.map(r,e=>(0,d.createElement)(Z,{present:n||i.open},(0,d.createElement)(H,{asChild:!0,container:o},e))))};var e_='[cmdk-group=""]',eV='[cmdk-group-items=""]',eU='[cmdk-item=""]',eH=`${eU}:not([aria-disabled="true"])`,eZ="cmdk-item-select",e$="data-value",ez=(e,t,n)=>{var r;return r=e,function e(t,n,r,o,c,s,d){if(s===n.length)return c===t.length?1:.99;var f=`${c},${s}`;if(void 0!==d[f])return d[f];for(var p,v,m,h,g=o.charAt(s),y=r.indexOf(g,c),b=0;y>=0;)(p=e(t,n,r,o,y+1,s+1,d))>b&&(y===c?p*=1:i.test(t.charAt(y-1))?(p*=.8,(m=t.slice(c,y-1).match(l))&&c>0&&(p*=Math.pow(.999,m.length))):u.test(t.charAt(y-1))?(p*=.9,(h=t.slice(c,y-1).match(a))&&c>0&&(p*=Math.pow(.999,h.length))):(p*=.17,c>0&&(p*=Math.pow(.999,y-c))),t.charAt(y)!==n.charAt(s)&&(p*=.9999)),(p<.1&&r.charAt(y-1)===o.charAt(s+1)||o.charAt(s+1)===o.charAt(s)&&r.charAt(y-1)!==o.charAt(s))&&.1*(v=e(t,n,r,o,y+1,s+2,d))>p&&(p=.1*v),p>b&&(b=p),y=r.indexOf(g,y+1);return d[f]=b,b}(r=n&&n.length>0?`${r+" "+n.join(" ")}`:r,t,c(r),c(t),0,0,{})},eW=d.createContext(void 0),eB=()=>d.useContext(eW),eq=d.createContext(void 0),eK=()=>d.useContext(eq),eY=d.createContext(void 0),eX=d.forwardRef((e,t)=>{let n=e9(()=>{var t,n;return{search:"",value:null!=(n=null!=(t=e.value)?t:e.defaultValue)?n:"",filtered:{count:0,items:new Map,groups:new Set}}}),r=e9(()=>new Set),o=e9(()=>new Map),i=e9(()=>new Map),l=e9(()=>new Set),u=e5(e),{label:a,children:c,value:s,onValueChange:f,filter:p,shouldFilter:v,loop:m,disablePointerSelection:h=!1,vimBindings:g=!0,...y}=e,b=d.useId(),E=d.useId(),w=d.useId(),x=d.useRef(null),C=te();e4(()=>{if(void 0!==s){let e=s.trim();n.current.value=e,k.emit()}},[s]),e4(()=>{C(6,A)},[]);let k=d.useMemo(()=>({subscribe:e=>(l.current.add(e),()=>l.current.delete(e)),snapshot:()=>n.current,setState:(e,t,r)=>{var o,i,l;if(!Object.is(n.current[e],t)){if(n.current[e]=t,"search"===e)N(),O(),C(1,P);else if("value"===e&&(r||C(5,A),(null==(o=u.current)?void 0:o.value)!==void 0)){null==(l=(i=u.current).onValueChange)||l.call(i,null!=t?t:"");return}k.emit()}},emit:()=>{l.current.forEach(e=>e())}}),[]),S=d.useMemo(()=>({value:(e,t,r)=>{var o;t!==(null==(o=i.current.get(e))?void 0:o.value)&&(i.current.set(e,{value:t,keywords:r}),n.current.filtered.items.set(e,D(t,r)),C(2,()=>{O(),k.emit()}))},item:(e,t)=>(r.current.add(e),t&&(o.current.has(t)?o.current.get(t).add(e):o.current.set(t,new Set([e]))),C(3,()=>{N(),O(),n.current.value||P(),k.emit()}),()=>{i.current.delete(e),r.current.delete(e),n.current.filtered.items.delete(e);let t=I();C(4,()=>{N(),(null==t?void 0:t.getAttribute("id"))===e&&P(),k.emit()})}),group:e=>(o.current.has(e)||o.current.set(e,new Set),()=>{i.current.delete(e),o.current.delete(e)}),filter:()=>u.current.shouldFilter,label:a||e["aria-label"],disablePointerSelection:h,listId:b,inputId:w,labelId:E,listInnerRef:x}),[]);function D(e,t){var r,o;let i=null!=(o=null==(r=u.current)?void 0:r.filter)?o:ez;return e?i(e,n.current.search,t):0}function O(){if(!n.current.search||!1===u.current.shouldFilter)return;let e=n.current.filtered.items,t=[];n.current.filtered.groups.forEach(n=>{let r=o.current.get(n),i=0;r.forEach(t=>{i=Math.max(e.get(t),i)}),t.push([n,i])});let r=x.current;M().sort((t,n)=>{var r,o;let i=t.getAttribute("id"),l=n.getAttribute("id");return(null!=(r=e.get(l))?r:0)-(null!=(o=e.get(i))?o:0)}).forEach(e=>{let t=e.closest(eV);t?t.appendChild(e.parentElement===t?e:e.closest(`${eV} > *`)):r.appendChild(e.parentElement===r?e:e.closest(`${eV} > *`))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{let t=x.current.querySelector(`${e_}[${e$}="${encodeURIComponent(e[0])}"]`);null==t||t.parentElement.appendChild(t)})}function P(){let e=M().find(e=>"true"!==e.getAttribute("aria-disabled")),t=null==e?void 0:e.getAttribute(e$);k.setState("value",t||void 0)}function N(){var e,t,l,a;if(!n.current.search||!1===u.current.shouldFilter){n.current.filtered.count=r.current.size;return}n.current.filtered.groups=new Set;let c=0;for(let o of r.current){let r=D(null!=(t=null==(e=i.current.get(o))?void 0:e.value)?t:"",null!=(a=null==(l=i.current.get(o))?void 0:l.keywords)?a:[]);n.current.filtered.items.set(o,r),r>0&&c++}for(let[e,t]of o.current)for(let r of t)if(n.current.filtered.items.get(r)>0){n.current.filtered.groups.add(e);break}n.current.filtered.count=c}function A(){var e,t,n;let r=I();r&&((null==(e=r.parentElement)?void 0:e.firstChild)===r&&(null==(n=null==(t=r.closest(e_))?void 0:t.querySelector('[cmdk-group-heading=""]'))||n.scrollIntoView({block:"nearest"})),r.scrollIntoView({block:"nearest"}))}function I(){var e;return null==(e=x.current)?void 0:e.querySelector(`${eU}[aria-selected="true"]`)}function M(){var e;return Array.from(null==(e=x.current)?void 0:e.querySelectorAll(eH))}function T(e){let t=M()[e];t&&k.setState("value",t.getAttribute(e$))}function L(e){var t;let n=I(),r=M(),o=r.findIndex(e=>e===n),i=r[o+e];null!=(t=u.current)&&t.loop&&(i=o+e<0?r[r.length-1]:o+e===r.length?r[0]:r[o+e]),i&&k.setState("value",i.getAttribute(e$))}function F(e){let t=I(),n=null==t?void 0:t.closest(e_),r;for(;n&&!r;)r=null==(n=e>0?function(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return n;n=n.nextElementSibling}}(n,e_):function(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return n;n=n.previousElementSibling}}(n,e_))?void 0:n.querySelector(eH);r?k.setState("value",r.getAttribute(e$)):L(e)}let j=()=>T(M().length-1),_=e=>{e.preventDefault(),e.metaKey?j():e.altKey?F(1):L(1)},V=e=>{e.preventDefault(),e.metaKey?T(0):e.altKey?F(-1):L(-1)};return d.createElement(R.div,{ref:t,tabIndex:-1,...y,"cmdk-root":"",onKeyDown:e=>{var t;if(null==(t=y.onKeyDown)||t.call(y,e),!e.defaultPrevented)switch(e.key){case"n":case"j":g&&e.ctrlKey&&_(e);break;case"ArrowDown":_(e);break;case"p":case"k":g&&e.ctrlKey&&V(e);break;case"ArrowUp":V(e);break;case"Home":e.preventDefault(),T(0);break;case"End":e.preventDefault(),j();break;case"Enter":if(!e.nativeEvent.isComposing&&229!==e.keyCode){e.preventDefault();let t=I();if(t){let e=new Event(eZ);t.dispatchEvent(e)}}}}},d.createElement("label",{"cmdk-label":"",htmlFor:S.inputId,id:S.labelId,style:tn},a),tt(e,e=>d.createElement(eq.Provider,{value:k},d.createElement(eW.Provider,{value:S},e))))}),eJ=d.forwardRef((e,t)=>{var n,r;let o=d.useId(),i=d.useRef(null),l=d.useContext(eY),u=eB(),a=e5(e),c=null!=(r=null==(n=a.current)?void 0:n.forceMount)?r:null==l?void 0:l.forceMount;e4(()=>{if(!c)return u.item(o,null==l?void 0:l.id)},[c]);let s=e7(o,i,[e.value,e.children,i],e.keywords),f=eK(),p=e6(e=>e.value&&e.value===s.current),v=e6(e=>!!c||!1===u.filter()||!e.search||e.filtered.items.get(o)>0);function m(){var e,t;h(),null==(t=(e=a.current).onSelect)||t.call(e,s.current)}function h(){f.setState("value",s.current,!0)}if(d.useEffect(()=>{let t=i.current;if(!(!t||e.disabled))return t.addEventListener(eZ,m),()=>t.removeEventListener(eZ,m)},[v,e.onSelect,e.disabled]),!v)return null;let{disabled:g,value:y,onSelect:b,forceMount:E,keywords:w,...x}=e;return d.createElement(R.div,{ref:e8([i,t]),...x,id:o,"cmdk-item":"",role:"option","aria-disabled":!!g,"aria-selected":!!p,"data-disabled":!!g,"data-selected":!!p,onPointerMove:g||u.disablePointerSelection?void 0:h,onClick:g?void 0:m},e.children)}),eG=d.forwardRef((e,t)=>{let{heading:n,children:r,forceMount:o,...i}=e,l=d.useId(),u=d.useRef(null),a=d.useRef(null),c=d.useId(),s=eB(),f=e6(e=>!!o||!1===s.filter()||!e.search||e.filtered.groups.has(l));e4(()=>s.group(l),[]),e7(l,u,[e.value,e.heading,a]);let p=d.useMemo(()=>({id:l,forceMount:o}),[o]);return d.createElement(R.div,{ref:e8([u,t]),...i,"cmdk-group":"",role:"presentation",hidden:!f||void 0},n&&d.createElement("div",{ref:a,"cmdk-group-heading":"","aria-hidden":!0,id:c},n),tt(e,e=>d.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":n?c:void 0},d.createElement(eY.Provider,{value:p},e))))}),eQ=d.forwardRef((e,t)=>{let{alwaysRender:n,...r}=e,o=d.useRef(null),i=e6(e=>!e.search);return n||i?d.createElement(R.div,{ref:e8([o,t]),...r,"cmdk-separator":"",role:"separator"}):null}),e0=d.forwardRef((e,t)=>{let{onValueChange:n,...r}=e,o=null!=e.value,i=eK(),l=e6(e=>e.search),u=e6(e=>e.value),a=eB(),c=d.useMemo(()=>{var e;let t=null==(e=a.listInnerRef.current)?void 0:e.querySelector(`${eU}[${e$}="${encodeURIComponent(u)}"]`);return null==t?void 0:t.getAttribute("id")},[]);return d.useEffect(()=>{null!=e.value&&i.setState("search",e.value)},[e.value]),d.createElement(R.input,{ref:t,...r,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":a.listId,"aria-labelledby":a.labelId,"aria-activedescendant":c,id:a.inputId,type:"text",value:o?e.value:l,onChange:e=>{o||i.setState("search",e.target.value),null==n||n(e.target.value)}})}),e1=d.forwardRef((e,t)=>{let{children:n,label:r="Suggestions",...o}=e,i=d.useRef(null),l=d.useRef(null),u=eB();return d.useEffect(()=>{if(l.current&&i.current){let e=l.current,t=i.current,n,r=new ResizeObserver(()=>{n=requestAnimationFrame(()=>{let n=e.offsetHeight;t.style.setProperty("--cmdk-list-height",n.toFixed(1)+"px")})});return r.observe(e),()=>{cancelAnimationFrame(n),r.unobserve(e)}}},[]),d.createElement(R.div,{ref:e8([i,t]),...o,"cmdk-list":"",role:"listbox","aria-label":r,id:u.listId},tt(e,e=>d.createElement("div",{ref:e8([l,u.listInnerRef]),"cmdk-list-sizer":""},e)))}),e2=d.forwardRef((e,t)=>{let{open:n,onOpenChange:r,overlayClassName:o,contentClassName:i,container:l,...u}=e;return d.createElement(eF,{open:n,onOpenChange:r},d.createElement(ej,{container:l},d.createElement(eR,{"cmdk-overlay":"",className:o}),d.createElement(eP,{"aria-label":e.label,"cmdk-dialog":"",className:i},d.createElement(eX,{ref:t,...u}))))}),e3=Object.assign(eX,{List:e1,Item:eJ,Input:e0,Group:eG,Separator:eQ,Dialog:e2,Empty:d.forwardRef((e,t)=>e6(e=>0===e.filtered.count)?d.createElement(R.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Loading:d.forwardRef((e,t)=>{let{progress:n,children:r,label:o="Loading...",...i}=e;return d.createElement(R.div,{ref:t,...i,"cmdk-loading":"",role:"progressbar","aria-valuenow":n,"aria-valuemin":0,"aria-valuemax":100,"aria-label":o},tt(e,e=>d.createElement("div",{"aria-hidden":!0},e)))})});function e5(e){let t=d.useRef(e);return e4(()=>{t.current=e}),t}var e4="undefined"==typeof window?d.useEffect:d.useLayoutEffect;function e9(e){let t=d.useRef();return void 0===t.current&&(t.current=e()),t}function e8(e){return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}}function e6(e){let t=eK(),n=()=>e(t.snapshot());return d.useSyncExternalStore(t.subscribe,n,n)}function e7(e,t,n,r=[]){let o=d.useRef(),i=eB();return e4(()=>{var l;let u=(()=>{var e;for(let t of n){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():o.current}})(),a=r.map(e=>e.trim());i.value(e,u,a),null==(l=t.current)||l.setAttribute(e$,u),o.current=u}),o}var te=()=>{let[e,t]=d.useState(),n=e9(()=>new Map);return e4(()=>{n.current.forEach(e=>e()),n.current=new Map},[e]),(e,r)=>{n.current.set(e,r),t({})}};function tt({asChild:e,children:t},n){let r;return e&&d.isValidElement(t)?d.cloneElement("function"==typeof(r=t.type)?r(t.props):"render"in r?r.render(t.props):t,{ref:t.ref},n(t.props.children)):n(t)}var tn={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"}},74300:function(e,t,n){n.d(t,{Z:function(){return S}});var r=n(2265);function o(e){return"[object Object]"===Object.prototype.toString.call(e)||Array.isArray(e)}function i(e,t){let n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&JSON.stringify(Object.keys(e.breakpoints||{}))===JSON.stringify(Object.keys(t.breakpoints||{}))&&n.every(n=>{let r=e[n],l=t[n];return"function"==typeof r?`${r}`==`${l}`:o(r)&&o(l)?i(r,l):r===l})}function l(e){return e.concat().sort((e,t)=>e.name>t.name?1:-1).map(e=>e.options)}function u(e){return"number"==typeof e}function a(e){return"string"==typeof e}function c(e){return"boolean"==typeof e}function s(e){return"[object Object]"===Object.prototype.toString.call(e)}function d(e){return Math.abs(e)}function f(e){return Math.sign(e)}function p(e){return g(e).map(Number)}function v(e){return e[m(e)]}function m(e){return Math.max(0,e.length-1)}function h(e,t=0){return Array.from(Array(e),(e,n)=>t+n)}function g(e){return Object.keys(e)}function y(e,t){return void 0!==t.MouseEvent&&e instanceof t.MouseEvent}function b(){let e=[],t={add:function(n,r,o,i={passive:!0}){let l;return"addEventListener"in n?(n.addEventListener(r,o,i),l=()=>n.removeEventListener(r,o,i)):(n.addListener(o),l=()=>n.removeListener(o)),e.push(l),t},clear:function(){e=e.filter(e=>e())}};return t}function E(e=0,t=0){let n=d(e-t);function r(n){return n<e||n>t}return{length:n,max:t,min:e,constrain:function(n){return r(n)?n<e?e:t:n},reachedAny:r,reachedMax:function(e){return e>t},reachedMin:function(t){return t<e},removeOffset:function(e){return n?e-n*Math.ceil((e-t)/n):e}}}function w(e){let t=e;function n(e){return u(e)?e:e.get()}return{get:function(){return t},set:function(e){t=n(e)},add:function(e){t+=n(e)},subtract:function(e){t-=n(e)}}}function x(e,t){let n="x"===e.scroll?function(e){return`translate3d(${e}px,0px,0px)`}:function(e){return`translate3d(0px,${e}px,0px)`},r=t.style,o=null,i=!1;return{clear:function(){i||(r.transform="",t.getAttribute("style")||t.removeAttribute("style"))},to:function(t){if(i)return;let l=Math.round(100*e.direction(t))/100;l!==o&&(r.transform=n(l),o=l)},toggleActive:function(e){i=!e}}}let C={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function k(e,t,n){let r,o,i,l,S;let R=e.ownerDocument,D=R.defaultView,O=function(e){function t(e,t){return function e(t,n){return[t,n].reduce((t,n)=>(g(n).forEach(r=>{let o=t[r],i=n[r],l=s(o)&&s(i);t[r]=l?e(o,i):i}),t),{})}(e,t||{})}return{mergeOptions:t,optionsAtMedia:function(n){let r=n.breakpoints||{},o=g(r).filter(t=>e.matchMedia(t).matches).map(e=>r[e]).reduce((e,n)=>t(e,n),{});return t(n,o)},optionsMediaQueries:function(t){return t.map(e=>g(e.breakpoints||{})).reduce((e,t)=>e.concat(t),[]).map(e.matchMedia)}}}(D),P=(S=[],{init:function(e,t){return(S=t.filter(({options:e})=>!1!==O.optionsAtMedia(e).active)).forEach(t=>t.init(e,O)),t.reduce((e,t)=>Object.assign(e,{[t.name]:t}),{})},destroy:function(){S=S.filter(e=>e.destroy())}}),N=b(),A=function(){let e,t={},n={init:function(t){e=t},emit:function(r){return(t[r]||[]).forEach(t=>t(e,r)),n},off:function(e,r){return t[e]=(t[e]||[]).filter(e=>e!==r),n},on:function(e,r){return t[e]=(t[e]||[]).concat([r]),n},clear:function(){t={}}};return n}(),{mergeOptions:I,optionsAtMedia:M,optionsMediaQueries:T}=O,{on:L,off:F,emit:j}=A,_=!1,V=I(C,k.globalOptions),U=I(V),H=[];function Z(t,n){!_&&(U=M(V=I(V,t)),H=n||H,function(){let{container:t,slides:n}=U;i=(a(t)?e.querySelector(t):t)||e.children[0];let r=a(n)?i.querySelectorAll(n):n;l=[].slice.call(r||i.children)}(),r=function t(n){let r=function(e,t,n,r,o,i,l){let s,C;let{align:k,axis:S,direction:R,startIndex:D,loop:O,duration:P,dragFree:N,dragThreshold:A,inViewThreshold:I,slidesToScroll:M,skipSnaps:T,containScroll:L,watchResize:F,watchSlides:j,watchDrag:_,watchFocus:V}=i,U={measure:function(e){let{offsetTop:t,offsetLeft:n,offsetWidth:r,offsetHeight:o}=e;return{top:t,right:n+r,bottom:t+o,left:n,width:r,height:o}}},H=U.measure(t),Z=n.map(U.measure),$=function(e,t){let n="rtl"===t,r="y"===e,o=!r&&n?-1:1;return{scroll:r?"y":"x",cross:r?"x":"y",startEdge:r?"top":n?"right":"left",endEdge:r?"bottom":n?"left":"right",measureSize:function(e){let{height:t,width:n}=e;return r?t:n},direction:function(e){return e*o}}}(S,R),z=$.measureSize(H),W={measure:function(e){return e/100*z}},B=function(e,t){let n={start:function(){return 0},center:function(e){return(t-e)/2},end:function(e){return t-e}};return{measure:function(r,o){return a(e)?n[e](r):e(t,r,o)}}}(k,z),q=!O&&!!L,{slideSizes:K,slideSizesWithGaps:Y,startGap:X,endGap:J}=function(e,t,n,r,o,i){let{measureSize:l,startEdge:u,endEdge:a}=e,c=n[0]&&o,s=function(){if(!c)return 0;let e=n[0];return d(t[u]-e[u])}(),f=c?parseFloat(i.getComputedStyle(v(r)).getPropertyValue(`margin-${a}`)):0,p=n.map(l),h=n.map((e,t,n)=>{let r=t===m(n);return t?r?p[t]+f:n[t+1][u]-e[u]:p[t]+s}).map(d);return{slideSizes:p,slideSizesWithGaps:h,startGap:s,endGap:f}}($,H,Z,n,O||!!L,o),G=function(e,t,n,r,o,i,l,a,c){let{startEdge:s,endEdge:f,direction:h}=e,g=u(n);return{groupSlides:function(e){return g?p(e).filter(e=>e%n==0).map(t=>e.slice(t,t+n)):e.length?p(e).reduce((n,u,c)=>{let p=v(n)||0,g=u===m(e),y=o[s]-i[p][s],b=o[s]-i[u][f],E=r||0!==p?0:h(l),w=d(b-(!r&&g?h(a):0)-(y+E));return c&&w>t+2&&n.push(u),g&&n.push(e.length),n},[]).map((t,n,r)=>{let o=Math.max(r[n-1]||0);return e.slice(o,t)}):[]}}}($,z,M,O,H,Z,X,J,0),{snaps:Q,snapsAligned:ee}=function(e,t,n,r,o){let{startEdge:i,endEdge:l}=e,{groupSlides:u}=o,a=u(r).map(e=>v(e)[l]-e[0][i]).map(d).map(t.measure),c=r.map(e=>n[i]-e[i]).map(e=>-d(e)),s=u(c).map(e=>e[0]).map((e,t)=>e+a[t]);return{snaps:c,snapsAligned:s}}($,B,H,Z,G),et=-v(Q)+v(Y),{snapsContained:en,scrollContainLimit:er}=function(e,t,n,r,o){let i=E(-t+e,0),l=n.map((e,t)=>{let{min:r,max:o}=i,l=i.constrain(e),u=t===m(n);return t?u||1>=d(r-l)?r:1>=d(o-l)?o:l:o}).map(e=>parseFloat(e.toFixed(3))),u=function(){let e=l[0],t=v(l);return E(l.lastIndexOf(e),l.indexOf(t)+1)}();return{snapsContained:function(){if(t<=e+2)return[i.max];if("keepSnaps"===r)return l;let{min:n,max:o}=u;return l.slice(n,o)}(),scrollContainLimit:u}}(z,et,ee,L,0),eo=q?en:ee,{limit:ei}=function(e,t,n){let r=t[0];return{limit:E(n?r-e:v(t),r)}}(et,eo,O),el=function e(t,n,r){let{constrain:o}=E(0,t),i=t+1,l=u(n);function u(e){return r?d((i+e)%i):o(e)}function a(){return e(t,l,r)}let c={get:function(){return l},set:function(e){return l=u(e),c},add:function(e){return a().set(l+e)},clone:a};return c}(m(eo),D,O),eu=el.clone(),ea=p(n),ec=({dragHandler:e,scrollBody:t,scrollBounds:n,options:{loop:r}})=>{r||n.constrain(e.pointerDown()),t.seek()},es=({scrollBody:e,translate:t,location:n,offsetLocation:r,previousLocation:o,scrollLooper:i,slideLooper:l,dragHandler:u,animation:a,eventHandler:c,scrollBounds:s,options:{loop:d}},f)=>{let p=e.settled(),v=!s.shouldConstrain(),m=d?p:p&&v;m&&!u.pointerDown()&&(a.stop(),c.emit("settle")),m||c.emit("scroll");let h=n.get()*f+o.get()*(1-f);r.set(h),d&&(i.loop(e.direction()),l.loop()),t.to(r.get())},ed=function(e,t,n,r){let o=b(),i=1e3/60,l=null,u=0,a=0;function c(e){if(!a)return;l||(l=e,n(),n());let o=e-l;for(l=e,u+=o;u>=i;)n(),u-=i;r(u/i),a&&(a=t.requestAnimationFrame(c))}function s(){t.cancelAnimationFrame(a),l=null,u=0,a=0}return{init:function(){o.add(e,"visibilitychange",()=>{e.hidden&&(l=null,u=0)})},destroy:function(){s(),o.clear()},start:function(){a||(a=t.requestAnimationFrame(c))},stop:s,update:n,render:r}}(r,o,()=>ec(eS),e=>es(eS,e)),ef=eo[el.get()],ep=w(ef),ev=w(ef),em=w(ef),eh=w(ef),eg=function(e,t,n,r,o,i){let l=0,u=0,a=o,c=.68,s=e.get(),p=0;function v(e){return a=e,h}function m(e){return c=e,h}let h={direction:function(){return u},duration:function(){return a},velocity:function(){return l},seek:function(){let t=r.get()-e.get(),o=0;return a?(n.set(e),l+=t/a,l*=c,s+=l,e.add(l),o=s-p):(l=0,n.set(r),e.set(r),o=t),u=f(o),p=s,h},settled:function(){return .001>d(r.get()-t.get())},useBaseFriction:function(){return m(.68)},useBaseDuration:function(){return v(o)},useFriction:m,useDuration:v};return h}(ep,em,ev,eh,P,0),ey=function(e,t,n,r,o){let{reachedAny:i,removeOffset:l,constrain:u}=r;function a(e){return e.concat().sort((e,t)=>d(e)-d(t))[0]}function c(t,r){let o=[t,t+n,t-n];if(!e)return t;if(!r)return a(o);let i=o.filter(e=>f(e)===r);return i.length?a(i):v(o)-n}return{byDistance:function(n,r){let a=o.get()+n,{index:s,distance:f}=function(n){let r=e?l(n):u(n),{index:o}=t.map((e,t)=>({diff:c(e-r,0),index:t})).sort((e,t)=>d(e.diff)-d(t.diff))[0];return{index:o,distance:r}}(a),p=!e&&i(a);if(!r||p)return{index:s,distance:n};let v=n+c(t[s]-f,0);return{index:s,distance:v}},byIndex:function(e,n){let r=c(t[e]-o.get(),n);return{index:e,distance:r}},shortcut:c}}(O,eo,et,ei,eh),eb=function(e,t,n,r,o,i,l){function u(o){let u=o.distance,a=o.index!==t.get();i.add(u),u&&(r.duration()?e.start():(e.update(),e.render(1),e.update())),a&&(n.set(t.get()),t.set(o.index),l.emit("select"))}return{distance:function(e,t){u(o.byDistance(e,t))},index:function(e,n){let r=t.clone().set(e);u(o.byIndex(r.get(),n))}}}(ed,el,eu,eg,ey,eh,l),eE=function(e){let{max:t,length:n}=e;return{get:function(e){return n?-((e-t)/n):0}}}(ei),ew=b(),ex=function(e,t,n,r){let o;let i={},l=null,u=null,a=!1;return{init:function(){o=new IntersectionObserver(e=>{a||(e.forEach(e=>{i[t.indexOf(e.target)]=e}),l=null,u=null,n.emit("slidesInView"))},{root:e.parentElement,threshold:r}),t.forEach(e=>o.observe(e))},destroy:function(){o&&o.disconnect(),a=!0},get:function(e=!0){if(e&&l)return l;if(!e&&u)return u;let t=g(i).reduce((t,n)=>{let r=parseInt(n),{isIntersecting:o}=i[r];return(e&&o||!e&&!o)&&t.push(r),t},[]);return e&&(l=t),e||(u=t),t}}}(t,n,l,I),{slideRegistry:eC}=function(e,t,n,r,o,i){let{groupSlides:l}=o,{min:u,max:a}=r;return{slideRegistry:function(){let r=l(i);return 1===n.length?[i]:e&&"keepSnaps"!==t?r.slice(u,a).map((e,t,n)=>{let r=t===m(n);return t?r?h(m(i)-v(n)[0]+1,v(n)[0]):e:h(v(n[0])+1)}):r}()}}(q,L,eo,er,G,ea),ek=function(e,t,n,r,o,i,l,a){let s={passive:!0,capture:!0},d=0;function f(e){"Tab"===e.code&&(d=new Date().getTime())}return{init:function(p){a&&(i.add(document,"keydown",f,!1),t.forEach((t,f)=>{i.add(t,"focus",t=>{(c(a)||a(p,t))&&function(t){if(new Date().getTime()-d>10)return;l.emit("slideFocusStart"),e.scrollLeft=0;let i=n.findIndex(e=>e.includes(t));u(i)&&(o.useDuration(0),r.index(i,0),l.emit("slideFocus"))}(f)},s)}))}}}(e,n,eC,eb,eg,ew,l,V),eS={ownerDocument:r,ownerWindow:o,eventHandler:l,containerRect:H,slideRects:Z,animation:ed,axis:$,dragHandler:function(e,t,n,r,o,i,l,u,a,s,p,v,m,h,g,w,x,C,k){let{cross:S,direction:R}=e,D=["INPUT","SELECT","TEXTAREA"],O={passive:!1},P=b(),N=b(),A=E(50,225).constrain(h.measure(20)),I={mouse:300,touch:400},M={mouse:500,touch:600},T=g?43:25,L=!1,F=0,j=0,_=!1,V=!1,U=!1,H=!1;function Z(e){if(!y(e,r)&&e.touches.length>=2)return $(e);let t=i.readPoint(e),n=i.readPoint(e,S),l=d(t-F),a=d(n-j);if(!V&&!H&&(!e.cancelable||!(V=l>a)))return $(e);let c=i.pointerMove(e);l>w&&(U=!0),s.useFriction(.3).useDuration(.75),u.start(),o.add(R(c)),e.preventDefault()}function $(e){let t=p.byDistance(0,!1).index!==v.get(),n=i.pointerUp(e)*(g?M:I)[H?"mouse":"touch"],r=function(e,t){let n=v.add(-1*f(e)),r=p.byDistance(e,!g).distance;return g||d(e)<A?r:x&&t?.5*r:p.byIndex(n.get(),0).distance}(R(n),t),o=function(e,t){var n,r;if(0===e||0===t||d(e)<=d(t))return 0;let o=(n=d(e),r=d(t),d(n-r));return d(o/e)}(n,r);V=!1,_=!1,N.clear(),s.useDuration(T-10*o).useFriction(.68+o/50),a.distance(r,!g),H=!1,m.emit("pointerUp")}function z(e){U&&(e.stopPropagation(),e.preventDefault(),U=!1)}return{init:function(e){k&&P.add(t,"dragstart",e=>e.preventDefault(),O).add(t,"touchmove",()=>void 0,O).add(t,"touchend",()=>void 0).add(t,"touchstart",u).add(t,"mousedown",u).add(t,"touchcancel",$).add(t,"contextmenu",$).add(t,"click",z,!0);function u(u){(c(k)||k(e,u))&&function(e){let u=y(e,r);H=u,U=g&&u&&!e.buttons&&L,L=d(o.get()-l.get())>=2,u&&0!==e.button||function(e){let t=e.nodeName||"";return D.includes(t)}(e.target)||(_=!0,i.pointerDown(e),s.useFriction(0).useDuration(0),o.set(l),function(){let e=H?n:t;N.add(e,"touchmove",Z,O).add(e,"touchend",$).add(e,"mousemove",Z,O).add(e,"mouseup",$)}(),F=i.readPoint(e),j=i.readPoint(e,S),m.emit("pointerDown"))}(u)}},destroy:function(){P.clear(),N.clear()},pointerDown:function(){return _}}}($,e,r,o,eh,function(e,t){let n,r;function o(e){return e.timeStamp}function i(n,r){let o=r||e.scroll,i=`client${"x"===o?"X":"Y"}`;return(y(n,t)?n:n.touches[0])[i]}return{pointerDown:function(e){return n=e,r=e,i(e)},pointerMove:function(e){let t=i(e)-i(r),l=o(e)-o(n)>170;return r=e,l&&(n=e),t},pointerUp:function(e){if(!n||!r)return 0;let t=i(r)-i(n),l=o(e)-o(n),u=o(e)-o(r)>170,a=t/l;return l&&!u&&d(a)>.1?a:0},readPoint:i}}($,o),ep,ed,eb,eg,ey,el,l,W,N,A,T,0,_),eventStore:ew,percentOfView:W,index:el,indexPrevious:eu,limit:ei,location:ep,offsetLocation:em,previousLocation:ev,options:i,resizeHandler:function(e,t,n,r,o,i,l){let u,a;let s=[e].concat(r),f=[],p=!1;function v(e){return o.measureSize(l.measure(e))}return{init:function(o){i&&(a=v(e),f=r.map(v),u=new ResizeObserver(n=>{(c(i)||i(o,n))&&function(n){for(let i of n){if(p)return;let n=i.target===e,l=r.indexOf(i.target),u=n?a:f[l];if(d(v(n?e:r[l])-u)>=.5){o.reInit(),t.emit("resize");break}}}(n)}),n.requestAnimationFrame(()=>{s.forEach(e=>u.observe(e))}))},destroy:function(){p=!0,u&&u.disconnect()}}}(t,l,o,n,$,F,U),scrollBody:eg,scrollBounds:function(e,t,n,r,o){let i=o.measure(10),l=o.measure(50),u=E(.1,.99),a=!1;function c(){return!!(!a&&e.reachedAny(n.get())&&e.reachedAny(t.get()))}return{shouldConstrain:c,constrain:function(o){if(!c())return;let a=e.reachedMin(t.get())?"min":"max",s=d(e[a]-t.get()),f=n.get()-t.get(),p=u.constrain(s/l);n.subtract(f*p),!o&&d(f)<i&&(n.set(e.constrain(n.get())),r.useDuration(25).useBaseFriction())},toggleActive:function(e){a=!e}}}(ei,em,eh,eg,W),scrollLooper:function(e,t,n,r){let{reachedMin:o,reachedMax:i}=E(t.min+.1,t.max+.1);return{loop:function(t){if(!(1===t?i(n.get()):-1===t&&o(n.get())))return;let l=-1*t*e;r.forEach(e=>e.add(l))}}}(et,ei,em,[ep,em,ev,eh]),scrollProgress:eE,scrollSnapList:eo.map(eE.get),scrollSnaps:eo,scrollTarget:ey,scrollTo:eb,slideLooper:function(e,t,n,r,o,i,l,u,a){let c=p(o),s=v(f(p(o).reverse(),l[0]),n,!1).concat(v(f(c,t-l[0]-1),-n,!0));function d(e,t){return e.reduce((e,t)=>e-o[t],t)}function f(e,t){return e.reduce((e,n)=>d(e,t)>0?e.concat([n]):e,[])}function v(o,l,c){let s=i.map((e,n)=>({start:e-r[n]+.5+l,end:e+t-.5+l}));return o.map(t=>{let r=c?0:-n,o=c?n:0,i=s[t][c?"end":"start"];return{index:t,loopPoint:i,slideLocation:w(-1),translate:x(e,a[t]),target:()=>u.get()>i?r:o}})}return{canLoop:function(){return s.every(({index:e})=>.1>=d(c.filter(t=>t!==e),t))},clear:function(){s.forEach(e=>e.translate.clear())},loop:function(){s.forEach(e=>{let{target:t,translate:n,slideLocation:r}=e,o=t();o!==r.get()&&(n.to(o),r.set(o))})},loopPoints:s}}($,z,et,K,Y,Q,eo,em,n),slideFocus:ek,slidesHandler:(C=!1,{init:function(e){j&&(s=new MutationObserver(t=>{!C&&(c(j)||j(e,t))&&function(t){for(let n of t)if("childList"===n.type){e.reInit(),l.emit("slidesChanged");break}}(t)})).observe(t,{childList:!0})},destroy:function(){s&&s.disconnect(),C=!0}}),slidesInView:ex,slideIndexes:ea,slideRegistry:eC,slidesToScroll:G,target:eh,translate:x($,t)};return eS}(e,i,l,R,D,n,A);return n.loop&&!r.slideLooper.canLoop()?t(Object.assign({},n,{loop:!1})):r}(U),T([V,...H.map(({options:e})=>e)]).forEach(e=>N.add(e,"change",$)),U.active&&(r.translate.to(r.location.get()),r.animation.init(),r.slidesInView.init(),r.slideFocus.init(q),r.eventHandler.init(q),r.resizeHandler.init(q),r.slidesHandler.init(q),r.options.loop&&r.slideLooper.loop(),i.offsetParent&&l.length&&r.dragHandler.init(q),o=P.init(q,H)))}function $(e,t){let n=B();z(),Z(I({startIndex:n},e),t),A.emit("reInit")}function z(){r.dragHandler.destroy(),r.eventStore.clear(),r.translate.clear(),r.slideLooper.clear(),r.resizeHandler.destroy(),r.slidesHandler.destroy(),r.slidesInView.destroy(),r.animation.destroy(),P.destroy(),N.clear()}function W(e,t,n){U.active&&!_&&(r.scrollBody.useBaseFriction().useDuration(!0===t?0:U.duration),r.scrollTo.index(e,n||0))}function B(){return r.index.get()}let q={canScrollNext:function(){return r.index.add(1).get()!==B()},canScrollPrev:function(){return r.index.add(-1).get()!==B()},containerNode:function(){return i},internalEngine:function(){return r},destroy:function(){_||(_=!0,N.clear(),z(),A.emit("destroy"),A.clear())},off:F,on:L,emit:j,plugins:function(){return o},previousScrollSnap:function(){return r.indexPrevious.get()},reInit:$,rootNode:function(){return e},scrollNext:function(e){W(r.index.add(1).get(),e,-1)},scrollPrev:function(e){W(r.index.add(-1).get(),e,1)},scrollProgress:function(){return r.scrollProgress.get(r.location.get())},scrollSnapList:function(){return r.scrollSnapList},scrollTo:W,selectedScrollSnap:B,slideNodes:function(){return l},slidesInView:function(){return r.slidesInView.get()},slidesNotInView:function(){return r.slidesInView.get(!1)}};return Z(t,n),setTimeout(()=>A.emit("init"),0),q}function S(e={},t=[]){let n=(0,r.useRef)(e),o=(0,r.useRef)(t),[u,a]=(0,r.useState)(),[c,s]=(0,r.useState)(),d=(0,r.useCallback)(()=>{u&&u.reInit(n.current,o.current)},[u]);return(0,r.useEffect)(()=>{i(n.current,e)||(n.current=e,d())},[e,d]),(0,r.useEffect)(()=>{!function(e,t){if(e.length!==t.length)return!1;let n=l(e),r=l(t);return n.every((e,t)=>i(e,r[t]))}(o.current,t)&&(o.current=t,d())},[t,d]),(0,r.useEffect)(()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&c){k.globalOptions=S.globalOptions;let e=k(c,n.current,o.current);return a(e),()=>e.destroy()}a(void 0)},[c,a]),[s,u]}k.globalOptions=void 0,S.globalOptions=void 0}}]);