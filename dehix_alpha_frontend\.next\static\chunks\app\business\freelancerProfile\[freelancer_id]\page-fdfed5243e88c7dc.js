(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[381],{93651:function(e,s,r){Promise.resolve().then(r.bind(r,99010))},5891:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},20897:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},6540:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},25912:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},13231:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},71935:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},7746:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},47390:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},98960:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},73347:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},10883:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},99010:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return k}});var a=r(57437),l=r(2265),t=r(16463),d=r(74697),i=r(33480);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,i.Z)("CircleUser",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}],["path",{d:"M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662",key:"154egf"}]]);var c=r(7746);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,i.Z)("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]]);var m=r(6540),h=r(25912);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let x=(0,i.Z)("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]);var u=r(66648),b=r(6460),f=r(78068),p=r(15922),j=r(82230),N=r(64797),v=r(62688),y=r(29973),g=r(48185),k=()=>{var e,s,r,i,k,w,Z;let{freelancer_id:M}=(0,t.useParams)(),[z,C]=(0,l.useState)(null),[D,_]=(0,l.useState)(!0),[I,S]=(0,l.useState)(null);(0,l.useEffect)(()=>{M&&(async()=>{try{_(!0);let e=await p.b.get("/freelancer/".concat(M,"/profile-info"));200===e.status&&C(e.data)}catch(e){console.error("Error fetching freelancer details",e),(0,f.Am)({variant:"destructive",title:"Error",description:"Failed to fetch freelancer details."})}finally{_(!1)}})()},[M]);let O=e=>{if(!e)return"";try{return(0,b.WU)(new Date(e),"MMM yyyy")}catch(s){return e}};return D?(0,a.jsx)("div",{className:"flex min-h-screen w-full flex-col bg-background",children:(0,a.jsx)("div",{className:"flex justify-center items-center h-screen",children:(0,a.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,a.jsx)("div",{className:"h-12 w-48 bg-muted rounded"}),(0,a.jsx)("div",{className:"h-64 w-full max-w-2xl bg-muted rounded"}),(0,a.jsx)("div",{className:"h-32 w-full max-w-2xl bg-muted rounded"})]})})}):(0,a.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-background",children:[(0,a.jsx)(N.Z,{menuItemsTop:j.yn,menuItemsBottom:j.$C,active:""}),(0,a.jsxs)("div",{className:"flex flex-col sm:gap-4 mb-8 sm:pl-14",children:[(0,a.jsx)(v.Z,{menuItemsTop:j.yn,menuItemsBottom:j.$C,activeMenu:"Projects",breadcrumbItems:[{label:"Business",link:"/dashboard/business"},{label:"Business Marketplace",link:"/business/market"},{label:"Freelancer Profile",link:"/business/market"},{label:"".concat((null==z?void 0:z.firstName)||""," ").concat((null==z?void 0:z.lastName)||""),link:"/dashboard/business/".concat(M)}]}),(0,a.jsx)("div",{className:"flex p-3 px-3 md:px-14 relative flex-col sm:gap-8 sm:py-0",children:(0,a.jsxs)("main",{className:"mt-8 max-w-4xl mx-auto",children:[(0,a.jsx)(g.Zb,{className:"mb-8 shadow-md",children:(0,a.jsx)(g.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-6",children:[(0,a.jsx)("div",{className:"relative h-24 w-24 rounded-full overflow-hidden bg-muted border-2 border-border shadow-md",children:(null==z?void 0:z.profilePic)?(0,a.jsx)(u.default,{src:z.profilePic,alt:"Profile",fill:!0,className:"object-cover"}):(0,a.jsx)("div",{className:"h-full w-full flex items-center justify-center bg-primary/10",children:(0,a.jsx)(n,{className:"h-12 w-12 text-primary/60"})})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-2xl font-bold text-foreground",children:[null==z?void 0:z.firstName," ",null==z?void 0:z.lastName]}),(0,a.jsx)("p",{className:"text-muted-foreground mt-1",children:(null==z?void 0:z.description)||"No description available"})]})]})})}),(0,a.jsxs)(g.Zb,{className:"mb-6 overflow-hidden border border-border shadow-md",children:[(0,a.jsx)(g.Ol,{className:"bg-primary/5 border-b border-border py-4",children:(0,a.jsxs)(g.ll,{className:"text-md font-semibold text-primary flex items-center gap-2",children:[(0,a.jsx)(c.Z,{className:"h-5 w-5"}),"Skills"]})}),(0,a.jsx)(g.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[null==z?void 0:null===(e=z.skills)||void 0===e?void 0:e.map(e=>(0,a.jsx)("div",{className:"flex items-center gap-1 px-3 py-1 bg-muted/50 border border-border rounded-full shadow-sm",children:(0,a.jsx)("span",{children:e.name})},e._id)),!(null==z?void 0:null===(s=z.skills)||void 0===s?void 0:s.length)&&(0,a.jsx)("p",{className:"text-muted-foreground italic",children:"No skills added"})]})})]}),(0,a.jsxs)(g.Zb,{className:"mb-6 overflow-hidden border border-border shadow-md",children:[(0,a.jsx)(g.Ol,{className:"bg-indigo-500/5 dark:bg-indigo-500/10 border-b border-border py-4",children:(0,a.jsxs)(g.ll,{className:"text-md font-semibold text-indigo-600 dark:text-indigo-400 flex items-center gap-2",children:[(0,a.jsx)(o,{className:"h-5 w-5"}),"Domain"]})}),(0,a.jsx)(g.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-1",children:[null==z?void 0:null===(r=z.domain)||void 0===r?void 0:r.map(e=>(0,a.jsx)("div",{className:"py-2 px-3 bg-muted/50 border border-border rounded-md mb-2 shadow-sm",children:e.name},e._id)),!(null==z?void 0:null===(i=z.domain)||void 0===i?void 0:i.length)&&(0,a.jsx)("p",{className:"text-muted-foreground italic",children:"No domains added"})]})})]}),(0,a.jsxs)(g.Zb,{className:"mb-6 overflow-hidden border border-border shadow-md",children:[(0,a.jsx)(g.Ol,{className:"bg-purple-500/5 dark:bg-purple-500/10 border-b border-border py-4",children:(0,a.jsxs)(g.ll,{className:"text-md font-semibold text-purple-600 dark:text-purple-400 flex items-center gap-2",children:[(0,a.jsx)(m.Z,{className:"h-5 w-5"}),"Project Domain"]})}),(0,a.jsx)(g.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-1",children:[null==z?void 0:null===(k=z.projectDomain)||void 0===k?void 0:k.map(e=>(0,a.jsx)("div",{className:"py-2 px-3 bg-muted/50 border border-border rounded-md mb-2 shadow-sm",children:e.name},e._id)),!(null==z?void 0:null===(w=z.projectDomain)||void 0===w?void 0:w.length)&&(0,a.jsx)("p",{className:"text-muted-foreground italic",children:"No project domains added"})]})})]}),(0,a.jsxs)(g.Zb,{className:"mb-6 overflow-hidden border border-border shadow-md",children:[(0,a.jsx)(g.Ol,{className:"bg-green-500/5 dark:bg-green-500/10 border-b border-border py-4",children:(0,a.jsxs)(g.ll,{className:"text-md font-semibold text-green-600 dark:text-green-400 flex items-center gap-2",children:[(0,a.jsx)(c.Z,{className:"h-5 w-5"}),"Projects"]})}),(0,a.jsx)(g.aY,{className:"p-4",children:(0,a.jsx)("div",{className:"space-y-6",children:z&&z.projects&&z.projects.length>0?z.projects.slice(0,3).map(e=>(0,a.jsx)("div",{className:"border-b border-border pb-4 last:border-b-0",children:(0,a.jsx)("div",{className:"flex justify-between items-start",children:(0,a.jsx)("h3",{className:"font-medium text-foreground hover:text-primary cursor-pointer",onClick:()=>S(e),children:e.projectName})})},e._id)):(0,a.jsx)("p",{className:"text-muted-foreground italic",children:"No projects added"})})})]}),(0,a.jsxs)(g.Zb,{className:"mb-6 overflow-hidden border border-border shadow-md",children:[(0,a.jsx)(g.Ol,{className:"bg-amber-500/5 dark:bg-amber-500/10 border-b border-border py-4",children:(0,a.jsxs)(g.ll,{className:"text-md font-semibold text-amber-600 dark:text-amber-400 flex items-center gap-2",children:[(0,a.jsx)(h.Z,{className:"h-5 w-5"}),"Professional Experience"]})}),(0,a.jsx)(g.aY,{className:"p-4",children:(0,a.jsx)("div",{className:"space-y-4",children:z&&z.professionalInfo&&z.professionalInfo.length>0?z.professionalInfo.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex gap-4 p-3 bg-muted/50 border border-border rounded-md shadow-sm",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-amber-100 dark:bg-amber-950/30 rounded-md flex items-center justify-center flex-shrink-0",children:(0,a.jsx)(h.Z,{className:"h-6 w-6 text-amber-600 dark:text-amber-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-foreground",children:e.jobTitle}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.company}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:[O(e.workFrom)," -"," ",O(e.workTo)]})]})]},e._id)):(0,a.jsx)("p",{className:"text-muted-foreground italic",children:"No professional experience added"})})})]}),(0,a.jsx)(y.Separator,{className:"h-px bg-border my-6"}),(0,a.jsxs)(g.Zb,{className:"mb-6 overflow-hidden border border-border shadow-md",children:[(0,a.jsx)(g.Ol,{className:"bg-cyan-500/5 dark:bg-cyan-500/10 border-b border-border py-4",children:(0,a.jsxs)(g.ll,{className:"text-md font-semibold text-cyan-600 dark:text-cyan-400 flex items-center gap-2",children:[(0,a.jsx)(x,{className:"h-5 w-5"}),"Education"]})}),(0,a.jsx)(g.aY,{className:"p-4",children:(0,a.jsx)("div",{className:"space-y-4",children:(null==z?void 0:null===(Z=z.education)||void 0===Z?void 0:Z.length)?z.education.slice(0,3).map(e=>(0,a.jsxs)("div",{className:"flex justify-between p-3 bg-muted/50 border border-border rounded-md shadow-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-foreground",children:e.degree}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.fieldOfStudy}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["Grade: ",e.grade]})]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground bg-cyan-500/5 dark:bg-cyan-500/10 px-3 py-1 rounded-md h-fit",children:[O(e.startDate)," -"," ",O(e.endDate)]})]},e._id)):(0,a.jsx)("p",{className:"text-muted-foreground italic",children:"No education details added"})})})]})]})}),I&&(0,a.jsx)(e=>{var s;let{project:r,onClose:l}=e;return(0,a.jsx)("div",{className:"fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-card text-card-foreground p-6 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-lg border",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold",children:r.projectName}),(0,a.jsx)("button",{onClick:l,className:"p-1 hover:bg-muted rounded-full",children:(0,a.jsx)(d.Z,{size:24})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:"Description"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:r.description})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:"Role"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:r.role})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:"Duration"}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:[O(r.start)," - ",O(r.end)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:"Technologies Used"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:null===(s=r.techUsed)||void 0===s?void 0:s.map((e,s)=>(0,a.jsx)("span",{className:"px-2 py-1 bg-muted rounded-full text-sm",children:e},s))})]}),r.githubLink&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:"GitHub"}),(0,a.jsx)("a",{href:r.githubLink,target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:r.githubLink})]}),r.projectType&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:"Project Type"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:r.projectType})]})]})]})})},{project:I,onClose:()=>S(null)})]})]})}},29973:function(e,s,r){"use strict";r.r(s),r.d(s,{Separator:function(){return i}});var a=r(57437),l=r(2265),t=r(48484),d=r(49354);let i=l.forwardRef((e,s)=>{let{className:r,orientation:l="horizontal",decorative:i=!0,...n}=e;return(0,a.jsx)(t.f,{ref:s,decorative:i,orientation:l,className:(0,d.cn)("shrink-0 bg-border","horizontal"===l?"h-[1px] w-full":"h-full w-[1px]",r),...n})});i.displayName=t.f.displayName},82230:function(e,s,r){"use strict";r.d(s,{$C:function(){return p},Ne:function(){return j},yn:function(){return f}});var a=r(57437),l=r(11005),t=r(98960),d=r(38133),i=r(20897),n=r(13231),c=r(71935),o=r(47390),m=r(73347),h=r(24258),x=r(5891),u=r(10883),b=r(66648);let f=[{href:"#",icon:(0,a.jsx)(b.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,a.jsx)(l.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:(0,a.jsx)(t.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:(0,a.jsx)(d.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:(0,a.jsx)(d.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:(0,a.jsx)(i.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:(0,a.jsx)(n.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:(0,a.jsx)(c.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:(0,a.jsx)(o.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,a.jsx)(m.Z,{className:"h-5 w-5"}),label:"Notes"}],p=[{href:"/business/settings/business-info",icon:(0,a.jsx)(h.Z,{className:"h-5 w-5"}),label:"Settings"}],j=[{href:"#",icon:(0,a.jsx)(b.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,a.jsx)(l.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:(0,a.jsx)(m.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:(0,a.jsx)(x.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:(0,a.jsx)(u.Z,{className:"h-5 w-5"}),label:"Trash"}]},48484:function(e,s,r){"use strict";r.d(s,{f:function(){return c}});var a=r(2265),l=r(18676),t=r(57437),d="horizontal",i=["horizontal","vertical"],n=a.forwardRef((e,s)=>{let{decorative:r,orientation:a=d,...n}=e,c=i.includes(a)?a:d;return(0,t.jsx)(l.WV.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...n,ref:s})});n.displayName="Separator";var c=n}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2455,9726,2688,2971,7023,1744],function(){return e(e.s=93651)}),_N_E=e.O()}]);