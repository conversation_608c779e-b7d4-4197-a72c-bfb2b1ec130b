(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[13],{58022:function(e,t,a){Promise.resolve().then(a.bind(a,16020))},6540:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(33480).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},25912:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},40036:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(33480).Z)("ImagePlus",[["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7",key:"31hg93"}],["line",{x1:"16",x2:"22",y1:"5",y2:"5",key:"ez7e4s"}],["line",{x1:"19",x2:"19",y1:"2",y2:"8",key:"1gkr8c"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},67524:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(33480).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},16020:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return T}});var r=a(57437),l=a(11444),s=a(64797),n=a(2265),i=a(31014),o=a(39343),c=a(59772),d=a(92513),m=a(74697),u=a(32226),x=a(48185),p=a(4919),h=a(74531),f=a(22023);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let v=(0,a(33480).Z)("CloudUpload",[["path",{d:"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242",key:"1pljnt"}],["path",{d:"M12 12v9",key:"192myk"}],["path",{d:"m16 16-4-4-4 4",key:"119tzi"}]]);var j=a(89733),b=a(78068),g=a(15922);let N=["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document"];var y=()=>{let[e,t]=(0,n.useState)(null),[a,l]=(0,n.useState)(null),[s,i]=(0,n.useState)(!1),[o,c]=(0,n.useState)(null),d=(0,n.useRef)(null),u=e=>{if(!e)return"";let t=e.includes(".")?e.substring(e.lastIndexOf(".")):"";return e.length>20?"".concat(e.substring(0,20-t.length),"...").concat(t):e},x=async t=>{if(t.preventDefault(),!e){(0,b.Am)({variant:"destructive",title:"No Resume Selected",description:"Please select a resume before uploading."});return}let a=new FormData;a.append("resume",e);try{i(!0);let{Location:t}=(await g.b.post("/register/upload-image",a,{headers:{"Content-Type":"multipart/form-data"}})).data.data;if(!t)throw Error("Failed to upload the resume.");let r=await g.b.put("/freelancer",{resume:t});if(200===r.status)l(e.name),(0,b.Am)({title:"Success",description:"Resume uploaded successfully!"});else throw Error("Failed to update resume.")}catch(e){(0,b.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}finally{i(!1)}};return(0,n.useEffect)(()=>{(async()=>{try{let e=await g.b.get("/freelancer");e.data.resume&&(l(e.data.resume),c(e.data.resume))}catch(e){console.error("Error fetching resume:",e)}})()},[]),(0,r.jsx)("div",{className:"upload-form max-w-md mx-auto rounded shadow-md p-4",children:(0,r.jsxs)("div",{className:"space-y-6 flex flex-col items-center",children:[(0,r.jsx)("div",{className:"flex flex-col items-center justify-center border-dashed border-2 border-gray-400 rounded-lg p-6 w-full cursor-pointer",onClick:()=>{var e;return null===(e=d.current)||void 0===e?void 0:e.click()},children:e?(0,r.jsxs)("div",{className:"w-full flex flex-col items-center gap-4 text-gray-700 text-center",children:[(0,r.jsxs)("div",{className:"flex flex-1 gap-6",children:[(0,r.jsx)("p",{className:"truncate",children:u(e.name)}),(0,r.jsx)("button",{className:"bg-red-600 text-white rounded-full p-1 hover:bg-red-700",onClick:()=>{t(null),c(null),l(null)},"aria-label":"Remove file",children:(0,r.jsx)(m.Z,{className:"w-4 h-4"})})]}),o?(0,r.jsx)("iframe",{src:o,title:"Resume Preview",className:"w-full h-40 border rounded"}):(0,r.jsxs)("div",{className:"flex items-center space-x-2 p-2 bg-gray-100 rounded",children:[(0,r.jsx)(f.Z,{className:"text-gray-500 w-6 h-6"}),(0,r.jsx)("span",{className:"text-gray-600 text-sm",children:u(e.name)})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v,{className:"text-gray-500 w-12 h-12 mb-2"}),(0,r.jsx)("p",{className:"text-gray-700 text-center",children:"Drag and drop your resume here or click to upload"}),(0,r.jsx)("div",{className:"flex items-center mt-2",children:(0,r.jsx)("span",{className:"text-gray-600 text-xs md:text-sm",children:"Supported formats: PDF, DOCX."})}),(0,r.jsx)("input",{type:"file",accept:N.join(","),onChange:e=>{var a;let r=null===(a=e.target.files)||void 0===a?void 0:a[0];r&&(N.includes(r.type)?r.size<=5242880?(t(r),l(r.name),"application/pdf"===r.type?c(URL.createObjectURL(r)):c(null)):(0,b.Am)({variant:"destructive",title:"File too large",description:"Resume size should not exceed 5MB."}):(0,b.Am)({variant:"destructive",title:"Invalid file type",description:"Supported formats: PDF, DOCX."}))},className:"hidden",ref:d})]})}),e&&(0,r.jsx)(j.z,{onClick:x,className:"w-full",disabled:s,children:s?"Uploading...":"Upload Resume"}),a&&(0,r.jsxs)("p",{className:"text-center text-gray-600",children:["Uploaded:"," ",(0,r.jsx)("strong",{children:u(a||"")})]})]})})};let w=["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document"];var E=()=>{let[e,t]=(0,n.useState)(null),[a,l]=(0,n.useState)(null),[s,i]=(0,n.useState)(!1),[o,c]=(0,n.useState)(null),d=(0,n.useRef)(null),u=e=>{if(!e)return"";let t=e.includes(".")?e.substring(e.lastIndexOf(".")):"";return e.length>20?"".concat(e.substring(0,20-t.length),"...").concat(t):e},x=async t=>{if(t.preventDefault(),!e){(0,b.Am)({variant:"destructive",title:"No Cover Letter Selected",description:"Please select a cover letter before uploading."});return}if(!w.includes(e.type)){(0,b.Am)({variant:"destructive",title:"Invalid File Type",description:"Please select a PDF or DOCX file."});return}if(e.size>5242880){(0,b.Am)({variant:"destructive",title:"File Too Large",description:"Cover letter size should not exceed 5MB."});return}let a=new FormData;a.append("file",e),console.log("Uploading cover letter:",{fileName:e.name,fileSize:e.size,fileType:e.type});try{i(!0);let t=await g.b.post("/register/upload-image",a,{headers:{"Content-Type":"multipart/form-data"}});console.log("Upload response:",t.data);let{Location:r}=t.data.data;if(!r)throw console.error("No Location in response:",t.data),Error("Failed to upload the cover letter - no URL returned.");console.log("Updating freelancer profile with cover letter URL:",r);let s=await g.b.put("/freelancer",{coverLetter:r});if(console.log("Profile update response:",s.data),200===s.status)l(e.name),(0,b.Am)({title:"Success",description:"Cover letter uploaded successfully!"});else throw console.error("Profile update failed:",s),Error("Failed to update cover letter in profile.")}catch(e){var r,s,n,o;console.error("Cover letter upload error:",e),console.error("Error response:",null===(r=e.response)||void 0===r?void 0:r.data),console.error("Error status:",null===(s=e.response)||void 0===s?void 0:s.status),(0,b.Am)({variant:"destructive",title:"Error",description:(null===(o=e.response)||void 0===o?void 0:null===(n=o.data)||void 0===n?void 0:n.message)||"Something went wrong. Please try again."})}finally{i(!1)}};return(0,n.useEffect)(()=>{(async()=>{try{let e=await g.b.get("/freelancer");console.log("Fetched freelancer data:",e.data),e.data.coverLetter&&(l(e.data.coverLetter),c(e.data.coverLetter))}catch(e){console.error("Error fetching cover letter:",e)}})()},[]),(0,r.jsx)("div",{className:"upload-form max-w-md mx-auto rounded shadow-md p-4",children:(0,r.jsxs)("div",{className:"space-y-6 flex flex-col items-center",children:[(0,r.jsx)("div",{className:"flex flex-col items-center justify-center border-dashed border-2 border-gray-400 rounded-lg p-6 w-full cursor-pointer",onClick:()=>{var e;return null===(e=d.current)||void 0===e?void 0:e.click()},children:e?(0,r.jsxs)("div",{className:"w-full flex flex-col items-center gap-4 text-gray-700 text-center",children:[(0,r.jsxs)("div",{className:"flex flex-1 gap-6",children:[(0,r.jsx)("p",{className:"truncate",children:u(e.name)}),(0,r.jsx)("button",{className:"bg-red-600 text-white rounded-full p-1 hover:bg-red-700",onClick:()=>{t(null),c(null),l(null)},"aria-label":"Remove file",children:(0,r.jsx)(m.Z,{className:"w-4 h-4"})})]}),o?(0,r.jsx)("iframe",{src:o,title:"Cover Letter Preview",className:"w-full h-40 border rounded"}):(0,r.jsxs)("div",{className:"flex items-center space-x-2 p-2 bg-gray-100 rounded",children:[(0,r.jsx)(f.Z,{className:"text-gray-500 w-6 h-6"}),(0,r.jsx)("span",{className:"text-gray-600 text-sm",children:u(e.name)})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v,{className:"text-gray-500 w-12 h-12 mb-2"}),(0,r.jsx)("p",{className:"text-gray-700 text-center",children:"Drag and drop your cover letter here or click to upload"}),(0,r.jsx)("div",{className:"flex items-center mt-2",children:(0,r.jsx)("span",{className:"text-gray-600 text-xs md:text-sm",children:"Supported formats: PDF, DOCX."})}),(0,r.jsx)("input",{type:"file",accept:w.join(","),onChange:e=>{var a;let r=null===(a=e.target.files)||void 0===a?void 0:a[0];r&&(w.includes(r.type)?r.size<=5242880?(t(r),l(r.name),"application/pdf"===r.type?c(URL.createObjectURL(r)):c(null)):(0,b.Am)({variant:"destructive",title:"File too large",description:"Cover letter size should not exceed 5MB."}):(0,b.Am)({variant:"destructive",title:"Invalid file type",description:"Supported formats: PDF, DOCX."}))},className:"hidden",ref:d})]})}),e&&(0,r.jsx)(j.z,{onClick:x,className:"w-full",disabled:s,children:s?"Uploading...":"Upload Cover Letter"}),a&&(0,r.jsxs)("p",{className:"text-center text-gray-600",children:["Uploaded:"," ",(0,r.jsx)("strong",{children:u(a||"")})]})]})})},I=a(93363),C=a(77209),P=a(29973),D=a(79055),k=a(2128),A=a(34859),L=a(97540);let R=c.z.object({firstName:c.z.string().min(2,{message:"First Name must be at least 2 characters."}),lastName:c.z.string().min(2,{message:"Last Name must be at least 2 characters."}),username:c.z.string().min(2,{message:"Username must be at least 2 characters."}).max(30,{message:"Username must not be longer than 30 characters."}),email:c.z.string().email(),phone:c.z.string().min(10,{message:"Phone number must be at least 10 digits."}),role:c.z.string(),personalWebsite:c.z.string().url().optional(),resume:c.z.string().url().optional(),coverLetter:c.z.string().url().optional(),description:c.z.string().max(500,{message:"Description cannot exceed 500 characters."})});function S(e){let{user_id:t}=e,[a,l]=(0,n.useState)({}),[s,c]=(0,n.useState)([]),[f,v]=(0,n.useState)([]),[N,w]=(0,n.useState)(""),[S,F]=(0,n.useState)([]),[z,T]=(0,n.useState)([]),[V,O]=(0,n.useState)(""),[U,Z]=(0,n.useState)([]),[B,W]=(0,n.useState)([]),[M,G]=(0,n.useState)(""),[_,X]=(0,n.useState)(!1),[J,q]=(0,n.useState)(!1),[,H]=(0,n.useState)({skills:[],projectsDomains:[],domains:[]}),[Q,K]=(0,n.useState)({label:"",description:""}),[$,Y]=(0,n.useState)({label:"",description:""}),[ee,et]=(0,n.useState)({label:"",description:""}),[ea]=(0,n.useState)(null),er=(0,o.cI)({resolver:(0,i.F)(R),defaultValues:{firstName:"",lastName:"",username:"",email:"",phone:"",role:""},mode:"all"}),el=()=>{!function(e,t,a){let r=e.trim();if(t.some(e=>e.label===r))return console.warn("".concat(r," already exists in the dropdown."));a([...t,{label:r}])}(N,s,c),N&&!f.some(e=>e.name===N)&&(v([...f,{name:N,level:"",experience:"",interviewStatus:L.sB.PENDING,interviewInfo:"",interviewerRating:0}]),H(e=>({...e,skills:[...e.skills,{name:N}]})),w(""))},es=async()=>{if(!Q.label.trim()){console.warn("Field is required.");return}let e={label:Q.label,interviewInfo:Q.description,createdBy:A.Dy.FREELANCER,createdById:t,status:L.sB.ACTIVE};try{await g.b.post("/skills",e);let t=[...s,{label:Q.label}];F(t),v([...f,{name:Q.label,level:"",experience:"",interviewStatus:"PENDING",interviewInfo:Q.description,interviewerRating:0}]),K({label:"",description:""}),q(!1)}catch(e){var a;console.error("Failed to add skill:",(null===(a=e.response)||void 0===a?void 0:a.data)||e.message),(0,b.Am)({variant:"destructive",title:"Error",description:"Failed to add skill. Please try again."})}finally{X(!1)}},en=async()=>{if(!$.label.trim()){console.warn("Field is required.");return}let e={label:$.label,interviewInfo:Q.description,createdBy:A.Dy.FREELANCER,createdById:t,status:L.sB.ACTIVE};try{await g.b.post("/domain",e);let t=[...S,{label:$.label}];F(t),T([...z,{name:$.label,level:"",experience:"",interviewStatus:"PENDING",interviewInfo:$.description,interviewerRating:0}]),Y({label:"",description:""}),q(!1)}catch(e){var a;console.error("Failed to add domain:",(null===(a=e.response)||void 0===a?void 0:a.data)||e.message),(0,b.Am)({variant:"destructive",title:"Error",description:"Failed to add domain. Please try again."})}finally{X(!1)}},ei=async()=>{if(!ee.label.trim()){console.warn("Field is required.");return}let e={label:ee.label,createdBy:A.Dy.FREELANCER,createdById:t,status:L.sB.ACTIVE};try{await g.b.post("/projectdomain",e);let t=[...U,{label:ee.label}];Z(t),W([...B,{name:ee.label,level:"",experience:"",interviewStatus:"PENDING",interviewInfo:ee.description,interviewerRating:0}]),et({label:"",description:""}),q(!1)}catch(e){var a;console.error("Failed to add project domain:",(null===(a=e.response)||void 0===a?void 0:a.data)||e.message),(0,b.Am)({variant:"destructive",title:"Error",description:"Failed to add project domain. Please try again."})}finally{X(!1)}},eo=()=>{!function(e,t,a){let r=e.trim();if(t.some(e=>e.label===r))return console.warn("".concat(r," already exists in the dropdown."));a([...t,{label:r}])}(V,S,F),V&&!z.some(e=>e.name===V)&&(T([...z,{name:V,level:"",experience:"",interviewStatus:L.sB.PENDING,interviewInfo:"",interviewerRating:0}]),H(e=>({...e,domains:[...e.domains,{name:V}]})),O(""))},ec=()=>{!function(e,t,a){let r=e.trim();if(t.some(e=>e.label===r))return console.warn("".concat(r," already exists in the dropdown."));a([...t,{label:r}])}(M,U,Z),M&&!B.some(e=>e.name===e)&&(W([...B,{name:M,level:"",experience:"",interviewStatus:L.sB.PENDING,interviewInfo:"",interviewerRating:0}]),H(e=>({...e,projectsDomains:[...e.projectsDomains,{name:M}]})),G(""))},ed=e=>{v(f.filter(t=>t.name!==e))},em=e=>{T(z.filter(t=>t.name!==e))},eu=e=>{W(B.filter(t=>t.name!==e))},[ex,ep]=(0,n.useState)("");async function eh(e){X(!0);try{let{...t}=e,r=f.map(e=>({...e,interviewInfo:e.interviewInfo||"",interviewerRating:e.interviewerRating||0,interviewStatus:e.interviewStatus||"PENDING"}));await g.b.put("/freelancer",{...t,resume:e.resume,coverLetter:e.coverLetter,skills:r,domain:z,projectDomain:B,description:e.description}),l({...a,firstName:e.firstName,lastName:e.lastName,userName:e.username,email:e.email,phone:e.phone,role:e.role,personalWebsite:e.personalWebsite,resume:e.resume,coverLetter:e.coverLetter,skills:r,domain:z,projectDomains:B}),(0,b.Am)({title:"Profile Updated",description:"Your profile has been successfully updated."})}catch(e){console.error("API Error:",e),(0,b.Am)({variant:"destructive",title:"Error",description:"Failed to update profile. Please try again later."})}finally{X(!1)}}return(0,n.useEffect)(()=>{(async()=>{try{let e=await g.b.get("/freelancer/".concat(t));l(e.data.data);let a=await g.b.get("/skills"),r=await g.b.get("/domain"),s=await g.b.get("/projectdomain");c(a.data.data),F(r.data.data),Z(s.data.data),v(e.data.data.skills),T(e.data.data.domain),W(e.data.data.projectDomain),er.reset({firstName:e.data.data.firstName||"",lastName:e.data.data.lastName||"",username:e.data.data.userName||"",email:e.data.data.email||"",phone:e.data.data.phone||"",role:e.data.data.role||"",personalWebsite:e.data.data.personalWebsite||"",resume:e.data.data.resume||"",coverLetter:e.data.data.coverLetter||"",description:e.data.data.description||""})}catch(e){console.error("API Error:",e),(0,b.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}})()},[t,er]),(0,n.useEffect)(()=>{er.reset({firstName:(null==a?void 0:a.firstName)||"",lastName:(null==a?void 0:a.lastName)||"",username:(null==a?void 0:a.userName)||"",email:(null==a?void 0:a.email)||"",phone:(null==a?void 0:a.phone)||"",role:(null==a?void 0:a.role)||"",personalWebsite:(null==a?void 0:a.personalWebsite)||"",resume:(null==a?void 0:a.resume)||"",coverLetter:(null==a?void 0:a.coverLetter)||"",description:(null==a?void 0:a.description)||""})},[a,er]),(0,r.jsx)(x.Zb,{className:"p-10",children:(0,r.jsxs)(I.l0,{...er,children:[(0,r.jsx)(h.Z,{profile:a.profilePic,entityType:A.Dy.FREELANCER}),(0,r.jsxs)("form",{onSubmit:er.handleSubmit(eh),className:"grid gap-10 grid-cols-1 sm:grid-cols-2 mt-4",children:[(0,r.jsx)(I.Wi,{control:er.control,name:"firstName",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.xJ,{children:[(0,r.jsx)(I.lX,{children:"First Name"}),(0,r.jsx)(I.NI,{children:(0,r.jsx)(C.I,{placeholder:"Enter your first name",...t})}),(0,r.jsx)(I.zG,{})]})}}),(0,r.jsx)(I.Wi,{control:er.control,name:"lastName",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.xJ,{children:[(0,r.jsx)(I.lX,{children:"Last Name"}),(0,r.jsx)(I.NI,{children:(0,r.jsx)(C.I,{placeholder:"Enter your last name",...t})}),(0,r.jsx)(I.zG,{})]})}}),(0,r.jsx)(I.Wi,{control:er.control,name:"username",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.xJ,{children:[(0,r.jsx)(I.lX,{children:"Username"}),(0,r.jsx)(I.NI,{children:(0,r.jsx)(C.I,{placeholder:"Enter your username",...t,readOnly:!0})}),(0,r.jsx)(I.zG,{}),(0,r.jsx)(I.pf,{children:"Non editable field"})]})}}),(0,r.jsx)(I.Wi,{control:er.control,name:"email",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.xJ,{children:[(0,r.jsx)(I.lX,{children:"Email"}),(0,r.jsx)(I.NI,{children:(0,r.jsx)(C.I,{placeholder:"Enter your email",...t,readOnly:!0})}),(0,r.jsx)(I.pf,{children:"Non editable field"}),(0,r.jsx)(I.zG,{})]})}}),(0,r.jsx)(I.Wi,{control:er.control,name:"description",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.xJ,{className:"sm:col-span-2",children:[(0,r.jsx)(I.lX,{children:"Description"}),(0,r.jsx)(I.NI,{children:(0,r.jsx)(p.g,{placeholder:"Enter description",...t})}),(0,r.jsx)(I.zG,{})]})}}),(0,r.jsx)(I.Wi,{control:er.control,name:"phone",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.xJ,{children:[(0,r.jsx)(I.lX,{children:"Phone Number"}),(0,r.jsx)(I.NI,{children:(0,r.jsx)(C.I,{placeholder:"+91",...t,readOnly:!0})}),(0,r.jsx)(I.zG,{}),(0,r.jsx)(I.pf,{children:"Non editable field"})]})}}),(0,r.jsx)(I.Wi,{control:er.control,name:"personalWebsite",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.xJ,{children:[(0,r.jsx)(I.lX,{children:"Personal Website URL"}),(0,r.jsx)(I.NI,{children:(0,r.jsx)(C.I,{placeholder:"Enter your LinkedIn URL",type:"url",...t})}),(0,r.jsx)(I.pf,{children:"Enter your Personal Website URL"}),(0,r.jsx)(I.zG,{})]})}}),(0,r.jsx)(I.Wi,{control:er.control,name:"resume",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.xJ,{children:[(0,r.jsx)(I.lX,{children:"Resume URL"}),(0,r.jsx)(I.NI,{children:(0,r.jsx)(C.I,{placeholder:"Enter your Resume URL",type:"url",...t})}),(0,r.jsx)(I.pf,{children:"Enter your Resume URL"}),(0,r.jsx)(I.zG,{})]})}}),(0,r.jsx)(P.Separator,{className:"col-span-2"}),(0,r.jsx)("div",{className:"sm:col-span-2",children:(0,r.jsxs)("div",{className:"grid gap-10 grid-cols-1 sm:grid-cols-6",children:[(0,r.jsx)("div",{className:"sm:col-span-2",children:(0,r.jsxs)("div",{className:"flex-1 min-w-[350px] max-w-[500px] mt-5",children:[(0,r.jsx)(I.lX,{children:"Skills"}),(0,r.jsxs)("div",{className:"flex items-center mt-2",children:[(0,r.jsxs)(k.Ph,{onValueChange:e=>{w(e),ep("")},value:N||"",onOpenChange:e=>{e||ep("")},children:[(0,r.jsx)(k.i4,{children:(0,r.jsx)(k.ki,{placeholder:N||"Select skill"})}),(0,r.jsxs)(k.Bw,{children:[(0,r.jsxs)("div",{className:"p-2 relative",children:[(0,r.jsx)("input",{type:"text",value:ex,onChange:e=>ep(e.target.value),className:"w-full p-2 border border-gray-300 rounded-lg text-sm",placeholder:"Search skills"}),ex&&(0,r.jsx)("button",{onClick:()=>ep(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2",children:"\xd7"})]}),s.filter(e=>e.label.toLowerCase().includes(ex.toLowerCase())&&!f.some(t=>t.name===e.label)).map((e,t)=>(0,r.jsx)(k.Ql,{value:e.label,children:e.label},t)),0===s.filter(e=>e.label.toLowerCase().includes(ex.toLowerCase())&&!f.some(t=>t.name===e.label)).length&&(0,r.jsx)("div",{className:"p-2 text-gray-500 italic text-center",children:"No matching skills"})]})]}),(0,r.jsx)(j.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",disabled:!N,onClick:()=>{el(),w(""),ep("")},children:(0,r.jsx)(d.Z,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-5",children:f.map((e,t)=>(0,r.jsxs)(D.C,{className:"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1",children:[e.name,(0,r.jsx)("button",{type:"button",onClick:()=>ed(e.name),className:"ml-2 text-red-500 hover:text-red-700",children:(0,r.jsx)(m.Z,{className:"h-4 w-4"})})]},t))})]})}),(0,r.jsx)("div",{className:"sm:col-span-2",children:(0,r.jsxs)("div",{className:"flex-1 min-w-[350px] max-w-[500px] mt-5",children:[(0,r.jsx)(I.lX,{children:"Domains"}),(0,r.jsxs)("div",{className:"flex items-center mt-2",children:[(0,r.jsxs)(k.Ph,{onValueChange:e=>{O(e),ep("")},value:V||"",onOpenChange:e=>{e||ep("")},children:[(0,r.jsx)(k.i4,{children:(0,r.jsx)(k.ki,{placeholder:V||"Select domain"})}),(0,r.jsxs)(k.Bw,{children:[(0,r.jsxs)("div",{className:"p-2 relative",children:[(0,r.jsx)("input",{type:"text",value:ex,onChange:e=>ep(e.target.value),className:"w-full p-2 border border-gray-300 rounded-lg text-sm",placeholder:"Search domains"}),ex&&(0,r.jsx)("button",{onClick:()=>ep(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2",children:"\xd7"})]}),S.filter(e=>e.label.toLowerCase().includes(ex.toLowerCase())&&!z.some(t=>t.name===e.label)).map((e,t)=>(0,r.jsx)(k.Ql,{value:e.label,children:e.label},t)),0===S.filter(e=>e.label.toLowerCase().includes(ex.toLowerCase())&&!z.some(e=>e.name===S.name)).length&&(0,r.jsx)("div",{className:"p-2 text-gray-500 italic text-center",children:"No matching domains"})]})]}),(0,r.jsx)(j.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",disabled:!V,onClick:()=>{eo(),O(""),ep("")},children:(0,r.jsx)(d.Z,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-5",children:z.map((e,t)=>(0,r.jsxs)(D.C,{className:"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1",children:[e.name,(0,r.jsx)("button",{type:"button",onClick:()=>em(e.name),className:"ml-2 text-red-500 hover:text-red-700",children:(0,r.jsx)(m.Z,{className:"h-4 w-4"})})]},t))})]})}),(0,r.jsx)("div",{className:"sm:col-span-2",children:(0,r.jsxs)("div",{className:"flex-1 min-w-[350px] max-w-[500px] mt-5",children:[(0,r.jsx)(I.lX,{children:"Project Domains"}),(0,r.jsxs)("div",{className:"flex items-center mt-2",children:[(0,r.jsxs)(k.Ph,{onValueChange:e=>{G(e),ep("")},value:M||"",onOpenChange:e=>{e||ep("")},children:[(0,r.jsx)(k.i4,{children:(0,r.jsx)(k.ki,{placeholder:M||"Select project domain"})}),(0,r.jsxs)(k.Bw,{children:[(0,r.jsxs)("div",{className:"p-2 relative",children:[(0,r.jsx)("input",{type:"text",value:ex,onChange:e=>ep(e.target.value),className:"w-full p-2 border border-gray-300 rounded-lg text-sm",placeholder:"Search project domains"}),ex&&(0,r.jsx)("button",{onClick:()=>ep(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2",children:"\xd7"})]}),U.filter(e=>e.label.toLowerCase().includes(ex.toLowerCase())&&!B.some(t=>t.name===e.label)).map((e,t)=>(0,r.jsx)(k.Ql,{value:e.label,children:e.label},t)),0===U.filter(e=>e.label.toLowerCase().includes(ex.toLowerCase())&&!B.some(e=>e.name===U.name)).length&&(0,r.jsx)("div",{className:"p-2 text-gray-500 italic text-center",children:"No matching domains"})]})]}),(0,r.jsx)(j.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",disabled:!M,onClick:()=>{ec(),G(""),ep("")},children:(0,r.jsx)(d.Z,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-5",children:B.map((e,t)=>(0,r.jsxs)(D.C,{className:"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1",children:[e.name,(0,r.jsx)("button",{type:"button",onClick:()=>eu(e.name),className:"ml-2 text-red-500 hover:text-red-700",children:(0,r.jsx)(m.Z,{className:"h-4 w-4"})})]},t))})]})})]})}),(0,r.jsx)(P.Separator,{className:"col-span-2 mt-0"}),(0,r.jsxs)("div",{className:"col-span-2",children:[(0,r.jsxs)("div",{className:"grid gap-10 grid-cols-1 sm:grid-cols-2",children:[(0,r.jsx)(I.Wi,{control:er.control,name:"resume",render:()=>(0,r.jsxs)(I.xJ,{className:"flex flex-col items-start",children:[(0,r.jsx)(I.lX,{className:"ml-2",children:"Upload Resume"}),(0,r.jsx)("div",{className:"w-full",children:(0,r.jsx)(y,{})})]})}),(0,r.jsx)(I.Wi,{control:er.control,name:"coverLetter",render:()=>(0,r.jsxs)(I.xJ,{className:"flex flex-col items-start",children:[(0,r.jsx)(I.lX,{className:"ml-2",children:"Upload Cover Letter"}),(0,r.jsx)("div",{className:"w-full",children:(0,r.jsx)(E,{})})]})})]}),(0,r.jsx)(P.Separator,{className:"sm:col-span-2 mt-0"})]}),(0,r.jsx)("div",{className:"col-span-2",children:(0,r.jsx)(j.z,{type:"submit",className:"sm:col-span-2 w-full",disabled:_,children:_?"Loading...":"Update Profile"})}),J&&(0,r.jsxs)(u.Vq,{open:J,onOpenChange:e=>q(e),children:[(0,r.jsx)(u.t9,{className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-40"}),(0,r.jsx)(u.cZ,{className:"fixed inset-0 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-black rounded-md shadow-xl p-6 w-[90%] max-w-md",children:["skill"===ea&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Add New Skill"}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),es()},children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{htmlFor:"skillLabel",className:"block text-sm font-medium text-white mb-1",children:"Skill Label"}),(0,r.jsx)("input",{type:"text",value:Q.label,onChange:e=>K({...Q,label:e.target.value}),placeholder:"Enter skill label",className:"w-full px-3 py-2 rounded-md text-white bg-black placeholder-gray-400 border border-white",required:!0})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)(j.z,{type:"button",variant:"ghost",onClick:()=>q(!1),className:"mt-3",children:"Cancel"}),(0,r.jsx)(j.z,{type:"button",className:"mt-3",onClick:()=>{es(),K({label:"",description:""})},children:"Add Skill"})]})]})]}),"domain"===ea&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Add New Domain"}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),en()},children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{htmlFor:"domainLabel",className:"block text-sm font-medium text-white mb-1",children:"Domain Label"}),(0,r.jsx)("input",{type:"text",value:$.label,onChange:e=>Y({...$,label:e.target.value}),placeholder:"Enter Domain label",className:"w-full px-3 py-2 rounded-md text-white bg-black placeholder-gray-400 border border-white",required:!0})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)(j.z,{type:"button",variant:"ghost",onClick:()=>q(!1),className:"mt-3",children:"Cancel"}),(0,r.jsx)(j.z,{type:"button",className:"mt-3",onClick:()=>{en(),Y({label:"",description:""})},children:"Add Domain"})]})]})]}),"projectDomain"===ea&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Add New Project Domain"}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),ei()},children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{htmlFor:"projectDomainLabel",className:"block text-sm font-medium text-white mb-1",children:"Project Domain Label"}),(0,r.jsx)("input",{type:"text",value:ee.label,onChange:e=>et({...ee,label:e.target.value}),placeholder:"Enter Project Domain label",className:"w-full px-3 py-2 rounded-md text-white bg-black placeholder-gray-400 border border-white",required:!0})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)(j.z,{type:"button",variant:"ghost",onClick:()=>q(!1),className:"mt-3",children:"Cancel"}),(0,r.jsx)(j.z,{type:"button",className:"mt-3",onClick:()=>{ei(),et({label:"",description:""})},children:"Add Project Domain"})]})]})]})]})})]})]})]})})}var F=a(27756),z=a(62688);function T(){let e=(0,l.v9)(e=>e.user);return(0,r.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,r.jsx)(s.Z,{menuItemsTop:F.y,menuItemsBottom:F.$,active:"Personal Info",isKycCheck:!0}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,r.jsx)(z.Z,{menuItemsTop:F.y,menuItemsBottom:F.$,activeMenu:"Personal Info",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Settings",link:"#"},{label:"Personal Info",link:"#"}]}),(0,r.jsx)("main",{className:"grid flex-1 items-start  sm:px-6 sm:py-0 md:gap-8",children:(0,r.jsx)(S,{user_id:e.uid})})]})]})}},74531:function(e,t,a){"use strict";a.d(t,{Z:function(){return f}});var r=a(57437),l=a(2265);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var n=a(92513),i=a(3274),o=a(66648),c=a(11444),d=a(78068),m=a(89733),u=a(15922),x=a(3483),p=a(34859);let h=["image/png","image/jpeg","image/jpg","image/gif","image/svg+xml"];var f=e=>{let{profile:t,entityType:a}=e,f=(0,c.v9)(e=>e.user),v=(0,c.I0)(),[j,b]=(0,l.useState)(null),[g,N]=(0,l.useState)(t),[y,w]=(0,l.useState)(!1),E=(0,l.useRef)(null),I=async e=>{if(e.preventDefault(),!j){(0,d.Am)({variant:"destructive",title:"No Image Selected",description:"Please select an image before submitting."});return}w(!0);let t=new FormData;t.append("profilePicture",j);try{let{Location:e}=(await u.b.post("/register/upload-image",t,{headers:{"Content-Type":"multipart/form-data"}})).data.data;v((0,x.av)({...f,photoURL:e}));let r=a===p.Dy.FREELANCER?"/freelancer":"/business",l=await u.b.put(r,{profilePic:e});if(200===l.status)(0,d.Am)({title:"Success",description:"Profile picture uploaded successfully!"});else throw Error("Failed to update profile picture")}catch(e){console.error("Error during upload:",e),(0,d.Am)({variant:"destructive",title:"Upload failed",description:"Image upload failed. Please try again."})}finally{w(!1)}};return(0,r.jsx)("div",{className:"upload-form max-w-md mx-auto rounded shadow-md",children:(0,r.jsxs)("form",{onSubmit:I,className:"space-y-6",children:[(0,r.jsx)("input",{type:"file",accept:h.join(","),onChange:e=>{var t;let a=null===(t=e.target.files)||void 0===t?void 0:t[0];a&&h.includes(a.type)?a.size<=1048576?(b(a),N(URL.createObjectURL(a))):(0,d.Am)({variant:"destructive",title:"File too large",description:"Image size should not exceed 1MB."}):(0,d.Am)({variant:"destructive",title:"Invalid file type",description:"Please upload a valid image file. Allowed formats: ".concat(h.join(", "))})},className:"hidden",ref:E}),(0,r.jsx)("div",{className:"relative flex flex-col items-center",children:(0,r.jsxs)("label",{htmlFor:"file-input",className:"cursor-pointer relative",children:[g?(0,r.jsx)(o.default,{width:28,height:28,src:g,alt:"Avatar Preview",className:"w-28 h-28 rounded-full object-cover border-2 border-black-300"}):(0,r.jsx)("div",{className:"w-28 h-28 rounded-full bg-gray-700 flex items-center justify-center",children:(0,r.jsx)(o.default,{width:112,height:112,src:t,alt:"Avatar Preview",className:"w-28 h-28 rounded-full object-cover border-2 border-black-300"})}),(0,r.jsx)(m.z,{variant:"outline",type:"button",size:"icon",className:"absolute bottom-0 right-0 w-10 h-10 rounded-full bg-black border border-gray-300 flex items-center justify-center shadow-md",onClick:()=>{if(g)N(null);else{var e;null===(e=E.current)||void 0===e||e.click()}},children:g?(0,r.jsx)(s,{className:"h-4 w-4 text-gray-400"}):(0,r.jsx)(n.Z,{className:"h-4 w-4 text-gray-400"})})]})}),g&&(0,r.jsx)(m.z,{type:"submit",className:"w-full",disabled:!j||y,children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Please wait"]}):"Upload Profile Picture"})]})})}},93363:function(e,t,a){"use strict";a.d(t,{NI:function(){return f},Wi:function(){return m},l0:function(){return c},lX:function(){return h},pf:function(){return v},xJ:function(){return p},zG:function(){return j}});var r=a(57437),l=a(2265),s=a(63355),n=a(39343),i=a(49354),o=a(70402);let c=n.RV,d=l.createContext({}),m=e=>{let{...t}=e;return(0,r.jsx)(d.Provider,{value:{name:t.name},children:(0,r.jsx)(n.Qr,{...t})})},u=()=>{let e=l.useContext(d),t=l.useContext(x),{getFieldState:a,formState:r}=(0,n.Gc)(),s=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...s}},x=l.createContext({}),p=l.forwardRef((e,t)=>{let{className:a,...s}=e,n=l.useId();return(0,r.jsx)(x.Provider,{value:{id:n},children:(0,r.jsx)("div",{ref:t,className:(0,i.cn)("space-y-2",a),...s})})});p.displayName="FormItem";let h=l.forwardRef((e,t)=>{let{className:a,...l}=e,{error:s,formItemId:n}=u();return(0,r.jsx)(o.Label,{ref:t,className:(0,i.cn)(s&&"text-destructive",a),htmlFor:n,...l})});h.displayName="FormLabel";let f=l.forwardRef((e,t)=>{let{...a}=e,{error:l,formItemId:n,formDescriptionId:i,formMessageId:o}=u();return(0,r.jsx)(s.g7,{ref:t,id:n,"aria-describedby":l?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!l,...a})});f.displayName="FormControl";let v=l.forwardRef((e,t)=>{let{className:a,...l}=e,{formDescriptionId:s}=u();return(0,r.jsx)("p",{ref:t,id:s,className:(0,i.cn)("text-sm text-muted-foreground",a),...l})});v.displayName="FormDescription";let j=l.forwardRef((e,t)=>{let{className:a,children:l,...s}=e,{error:n,formMessageId:o}=u(),c=n?String(null==n?void 0:n.message):l;return c?(0,r.jsx)("p",{ref:t,id:o,className:(0,i.cn)("text-sm font-medium text-destructive",a),...s,children:c}):null});j.displayName="FormMessage"},70402:function(e,t,a){"use strict";a.r(t),a.d(t,{Label:function(){return c}});var r=a(57437),l=a(2265),s=a(38364),n=a(12218),i=a(49354);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)(s.f,{ref:t,className:(0,i.cn)(o(),a),...l})});c.displayName=s.f.displayName},29973:function(e,t,a){"use strict";a.r(t),a.d(t,{Separator:function(){return i}});var r=a(57437),l=a(2265),s=a(48484),n=a(49354);let i=l.forwardRef((e,t)=>{let{className:a,orientation:l="horizontal",decorative:i=!0,...o}=e;return(0,r.jsx)(s.f,{ref:t,decorative:i,orientation:l,className:(0,n.cn)("shrink-0 bg-border","horizontal"===l?"h-[1px] w-full":"h-full w-[1px]",a),...o})});i.displayName=s.f.displayName},27756:function(e,t,a){"use strict";a.d(t,{$:function(){return u},y:function(){return m}});var r=a(57437),l=a(11005),s=a(52022),n=a(25912),i=a(67524),o=a(6540),c=a(40036),d=a(66648);let m=[{href:"#",icon:(0,r.jsx)(d.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,r.jsx)(l.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/settings/personal-info",icon:(0,r.jsx)(s.Z,{className:"h-5 w-5"}),label:"Personal Info"},{href:"/freelancer/settings/professional-info",icon:(0,r.jsx)(n.Z,{className:"h-5 w-5"}),label:"Professional Info"},{href:"/freelancer/settings/projects",icon:(0,r.jsx)(i.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"/freelancer/settings/education-info",icon:(0,r.jsx)(o.Z,{className:"h-5 w-5"}),label:"Education"},{href:"/freelancer/settings/resume",icon:(0,r.jsx)(c.Z,{className:"h-5 w-5"}),label:"Portfolio"}],u=[]},34859:function(e,t,a){"use strict";var r,l,s,n,i,o,c,d,m,u,x,p,h,f,v,j,b,g,N,y,w,E;a.d(t,{Dy:function(){return u},Dz:function(){return I}});let I={BATCH:3};(p=r||(r={})).PROJECT_HIRING="PROJECT_HIRING",p.SKILL_INTERVIEW="SKILL_INTERVIEW",p.DOMAIN_INTERVIEW="DOMAIN_INTERVIEW",p.TALENT_INTERVIEW="TALENT_INTERVIEW",(h=l||(l={})).ADDED="Added",h.APPROVED="Approved",h.CLOSED="Closed",h.COMPLETED="Completed",(f=s||(s={})).ACTIVE="Active",f.IN_ACTIVE="Inactive",f.NOT_VERIFIED="Not Verified",(v=n||(n={})).BUSINESS="Business",v.FREELANCER="Freelancer",v.BOTH="Both",(j=i||(i={})).ACTIVE="Active",j.IN_ACTIVE="Inactive",(b=o||(o={})).APPLIED="APPLIED",b.NOT_APPLIED="NOT_APPLIED",b.APPROVED="APPROVED",b.FAILED="FAILED",b.STOPPED="STOPPED",b.REAPPLIED="REAPPLIED",(g=c||(c={})).PENDING="Pending",g.ACCEPTED="Accepted",g.REJECTED="Rejected",g.PANEL="Panel",g.INTERVIEW="Interview",(N=d||(d={})).ACTIVE="ACTIVE",N.INACTIVE="INACTIVE",N.ARCHIVED="ARCHIVED",(y=m||(m={})).ACTIVE="Active",y.PENDING="Pending",y.INACTIVE="Inactive",y.CLOSED="Closed",(w=u||(u={})).FREELANCER="FREELANCER",w.ADMIN="ADMIN",w.BUSINESS="BUSINESS",(E=x||(x={})).CREATED="Created",E.CLOSED="Closed",E.ACTIVE="Active"},97540:function(e,t,a){"use strict";var r,l,s,n,i,o;a.d(t,{cd:function(){return r},d8:function(){return c},kJ:function(){return l},sB:function(){return s}}),(n=r||(r={})).Mastery="Mastery",n.Proficient="Proficient",n.Beginner="Beginner",(i=l||(l={})).ACTIVE="Active",i.PENDING="Pending",i.REJECTED="Rejected",i.COMPLETED="Completed",(o=s||(s={})).ACTIVE="ACTIVE",o.PENDING="PENDING",o.REJECTED="REJECTED",o.COMPLETED="COMPLETED";let c={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},38364:function(e,t,a){"use strict";a.d(t,{f:function(){return i}});var r=a(2265),l=a(18676),s=a(57437),n=r.forwardRef((e,t)=>(0,s.jsx)(l.WV.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var i=n},48484:function(e,t,a){"use strict";a.d(t,{f:function(){return c}});var r=a(2265),l=a(18676),s=a(57437),n="horizontal",i=["horizontal","vertical"],o=r.forwardRef((e,t)=>{let{decorative:a,orientation:r=n,...o}=e,c=i.includes(r)?r:n;return(0,s.jsx)(l.WV.div,{"data-orientation":c,...a?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:t})});o.displayName="Separator";var c=o}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,1374,2455,9726,2688,2971,7023,1744],function(){return e(e.s=58022)}),_N_E=e.O()}]);