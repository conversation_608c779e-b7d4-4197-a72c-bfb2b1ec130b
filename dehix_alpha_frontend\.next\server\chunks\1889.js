exports.id=1889,exports.ids=[1889],exports.modules={89542:(e,s,a)=>{Promise.resolve().then(a.bind(a,54352)),Promise.resolve().then(a.bind(a,3594))},54352:(e,s,a)=>{"use strict";a.d(s,{default:()=>N});var r=a(10326),t=a(94909),i=a(12070),n=a(66307),l=a(69669),c=a(41137),o=a(88307),d=a(941),h=a(17577),x=a(91664),m=a(29752),f=a(41190),u=a(44794),p=a(78062),b=a(73326),v=a(50384),g=a(92166),j=a(46319);let N=({children:e,activeTab:s})=>{let[a,N]=(0,h.useState)("Talent");return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-background",children:[r.jsx(g.Z,{menuItemsTop:j.yn,menuItemsBottom:j.$C,active:a,setActive:N}),(0,r.jsxs)("div",{className:"ml-14 flex flex-col min-h-screen",children:[r.jsx("header",{className:"border-b",children:(0,r.jsxs)("div",{className:"container flex h-16 items-center justify-between px-4",children:[r.jsx("div",{className:"flex items-center gap-4",children:r.jsx("h1",{className:"text-xl font-bold",children:"Talent Management"})}),r.jsx("div",{className:"flex items-center gap-4"})]})}),r.jsx("div",{className:"container px-4 py-4",children:r.jsx(v.mQ,{defaultValue:s,className:"w-full",children:(0,r.jsxs)(v.dr,{className:"grid w-full grid-cols-4",children:[r.jsx(v.SP,{value:"overview",className:"flex items-center gap-2",asChild:!0,children:(0,r.jsxs)("a",{href:"/business/talent",children:[r.jsx(t.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Overview"})]})}),r.jsx(v.SP,{value:"invited",className:"flex items-center gap-2",asChild:!0,children:(0,r.jsxs)("a",{href:"/business/market/invited",children:[r.jsx(i.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Invites"})]})}),r.jsx(v.SP,{value:"accepted",className:"flex items-center gap-2",asChild:!0,children:(0,r.jsxs)("a",{href:"/business/market/accepted",children:[r.jsx(n.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Accepted"})]})}),r.jsx(v.SP,{value:"rejected",className:"flex items-center gap-2",asChild:!0,children:(0,r.jsxs)("a",{href:"/business/market/rejected",children:[r.jsx(l.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Rejected"})]})})]})})}),r.jsx("div",{className:"container flex-1 items-start px-4 py-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-12 gap-6",children:[r.jsx("aside",{className:"col-span-3",children:(0,r.jsxs)(m.Zb,{className:"sticky top-20",children:[r.jsx(m.Ol,{children:(0,r.jsxs)(m.ll,{className:"flex items-center gap-2",children:[r.jsx(c.Z,{className:"h-4 w-4"}),"Filters"]})}),(0,r.jsxs)(m.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(u.Label,{htmlFor:"search",children:"Search"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(o.Z,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),r.jsx(f.I,{id:"search",placeholder:"Search by name, skills...",className:"pl-8"})]})]}),r.jsx(p.Separator,{}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(u.Label,{children:"Skills"}),r.jsx("div",{className:"space-y-1",children:["React","TypeScript","NextJS","UI/UX"].map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(b.r,{id:`skill-${e}`}),r.jsx(u.Label,{htmlFor:`skill-${e}`,className:"font-normal",children:e})]},e))})]}),r.jsx(p.Separator,{}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(u.Label,{children:"Experience"}),r.jsx("div",{className:"space-y-1",children:["Junior","Mid-level","Senior","Lead"].map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(b.r,{id:`exp-${e}`}),r.jsx(u.Label,{htmlFor:`exp-${e}`,className:"font-normal",children:e})]},e))})]}),r.jsx(p.Separator,{}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(u.Label,{children:"Location"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(f.I,{placeholder:"Select location"}),r.jsx(d.Z,{className:"absolute right-2 top-2.5 h-4 w-4 text-muted-foreground"})]})]}),(0,r.jsxs)("div",{className:"flex justify-between pt-4",children:[r.jsx(x.z,{variant:"outline",children:"Reset"}),r.jsx(x.z,{children:"Apply Filters"})]})]})]})}),r.jsx("div",{className:"col-span-9",children:e})]})})]})]})}},44794:(e,s,a)=>{"use strict";a.r(s),a.d(s,{Label:()=>o});var r=a(10326),t=a(17577),i=a(34478),n=a(28671),l=a(51223);let c=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=t.forwardRef(({className:e,...s},a)=>r.jsx(i.f,{ref:a,className:(0,l.cn)(c(),e),...s}));o.displayName=i.f.displayName},78062:(e,s,a)=>{"use strict";a.r(s),a.d(s,{Separator:()=>l});var r=a(10326),t=a(17577),i=a(90220),n=a(51223);let l=t.forwardRef(({className:e,orientation:s="horizontal",decorative:a=!0,...t},l)=>r.jsx(i.f,{ref:l,decorative:a,orientation:s,className:(0,n.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...t}));l.displayName=i.f.displayName},73326:(e,s,a)=>{"use strict";a.d(s,{r:()=>l});var r=a(10326),t=a(17577),i=a(41959),n=a(51223);let l=t.forwardRef(({className:e,...s},a)=>r.jsx(i.fC,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:a,children:r.jsx(i.bU,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));l.displayName=i.fC.displayName},50384:(e,s,a)=>{"use strict";a.d(s,{SP:()=>o,dr:()=>c,mQ:()=>l,nU:()=>d});var r=a(10326),t=a(17577),i=a(28407),n=a(51223);let l=i.fC,c=t.forwardRef(({className:e,...s},a)=>r.jsx(i.aV,{ref:a,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));c.displayName=i.aV.displayName;let o=t.forwardRef(({className:e,...s},a)=>r.jsx(i.xz,{ref:a,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));o.displayName=i.xz.displayName;let d=t.forwardRef(({className:e,...s},a)=>r.jsx(i.VY,{ref:a,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));d.displayName=i.VY.displayName},46319:(e,s,a)=>{"use strict";a.d(s,{$C:()=>b,Ne:()=>v,yn:()=>p});var r=a(10326),t=a(95920),i=a(57671),n=a(94909),l=a(12070),c=a(66307),o=a(69669),d=a(40617),h=a(69515),x=a(88378),m=a(40900),f=a(98091),u=a(46226);let p=[{href:"#",icon:r.jsx(u.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:r.jsx(t.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:r.jsx(i.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:r.jsx(n.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:r.jsx(n.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:r.jsx(l.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:r.jsx(c.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:r.jsx(o.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:r.jsx(d.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:r.jsx(h.Z,{className:"h-5 w-5"}),label:"Notes"}],b=[{href:"/business/settings/business-info",icon:r.jsx(x.Z,{className:"h-5 w-5"}),label:"Settings"}],v=[{href:"#",icon:r.jsx(u.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:r.jsx(t.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:r.jsx(h.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:r.jsx(m.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:r.jsx(f.Z,{className:"h-5 w-5"}),label:"Trash"}]},24767:(e,s,a)=>{"use strict";a.d(s,{ZP:()=>l});var r=a(68570);let t=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\marketComponents\TalentLayout.tsx`),{__esModule:i,$$typeof:n}=t;t.default;let l=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\marketComponents\TalentLayout.tsx#default`)},55116:(e,s,a)=>{"use strict";a.d(s,{F$:()=>c,Q5:()=>o,qE:()=>l});var r=a(68570);let t=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\ui\avatar.tsx`),{__esModule:i,$$typeof:n}=t;t.default;let l=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\ui\avatar.tsx#Avatar`),c=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\ui\avatar.tsx#AvatarImage`),o=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\ui\avatar.tsx#AvatarFallback`)},59558:(e,s,a)=>{"use strict";a.d(s,{C:()=>l});var r=a(19510);a(71159);var t=a(60791),i=a(3632);let n=(0,t.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:s,...a}){return r.jsx("div",{className:(0,i.cn)(n({variant:s}),e),...a})}},27039:(e,s,a)=>{"use strict";a.d(s,{z:()=>o});var r=a(19510),t=a(71159),i=a(22813),n=a(60791),l=a(3632);let c=(0,n.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=t.forwardRef(({className:e,variant:s,size:a,asChild:t=!1,...n},o)=>{let d=t?i.g7:"button";return r.jsx(d,{className:(0,l.cn)(c({variant:s,size:a,className:e})),ref:o,...n})});o.displayName="Button"},93804:(e,s,a)=>{"use strict";a.d(s,{Ol:()=>l,SZ:()=>o,Zb:()=>n,aY:()=>d,eW:()=>h,ll:()=>c});var r=a(19510),t=a(71159),i=a(3632);let n=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));n.displayName="Card";let l=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let c=t.forwardRef(({className:e,...s},a)=>r.jsx("h3",{ref:a,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let o=t.forwardRef(({className:e,...s},a)=>r.jsx("p",{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let d=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:(0,i.cn)("p-6 pt-0",e),...s}));d.displayName="CardContent";let h=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s}));h.displayName="CardFooter"},3632:(e,s,a)=>{"use strict";a.d(s,{cn:()=>x});var r=a(55761),t=a(62386),i=a(19095);a(13237);let n=a(29712).Z.create({baseURL:"http://127.0.0.1:8080/"});console.log("Base URL:","http://127.0.0.1:8080/"),n.interceptors.request.use(e=>(console.log("Request config:",e),e),e=>(console.error("Request error:",e),Promise.reject(e))),n.interceptors.response.use(e=>(console.log("Response:",e.data),e),e=>(console.error("Response error:",e),Promise.reject(e)));var l=a(29362),c=a(85500),o=a(93820),d=a(38192);let h=(0,l.ZF)({apiKey:"AIzaSyBPTH9xikAUkgGof048klY6WGiZSmRoXXA",authDomain:"dehix-6c349.firebaseapp.com",databaseURL:"https://dehix-6c349-default-rtdb.firebaseio.com",projectId:"dehix-6c349",storageBucket:"dehix-6c349.appspot.com",messagingSenderId:"521082542540",appId:"1:521082542540:web:543857e713038c2927a569"});function x(...e){return(0,t.m6)((0,r.W)(e))}(0,o.ad)(h),(0,i.v0)(h).useDeviceLanguage(),new i.hJ,(0,c.N8)(h),(0,d.cF)(h)}};