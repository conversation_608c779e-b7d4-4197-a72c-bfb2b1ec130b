(()=>{var e={};e.id=9161,e.ids=[9161],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},85439:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>o}),s(65945),s(54302),s(12523);var a=s(23191),r=s(88716),i=s(37922),l=s.n(i),n=s(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let o=["",{children:["business",{children:["project",{children:["[project_id]",{children:["milestone",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,65945)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\project\\[project_id]\\milestone\\page.tsx"]}]},{}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\project\\[project_id]\\milestone\\page.tsx"],u="/business/project/[project_id]/milestone/page",m={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/business/project/[project_id]/milestone/page",pathname:"/business/project/[project_id]/milestone",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},78689:(e,t,s)=>{Promise.resolve().then(s.bind(s,2485))},40900:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(80851).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},12070:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(80851).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},37358:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(80851).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},11890:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(80851).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},66307:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(80851).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69669:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(80851).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40617:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},57671:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(80851).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},69515:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(80851).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},98091:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2485:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>S});var a,r,i=s(10326),l=s(17577),n=s.n(l),c=s(35047),o=s(92166),d=s(40588),u=s(48474),m=s(46319),h=s(91664),x=s(83855),p=s(25842),f=s(41190),b=s(98181),j=s(37358),y=s(51223),v=s(33194),g=s(51027);function N({selected:e,onChange:t,placeholderText:s="Pick a date",className:a}){return(0,i.jsxs)(g.J2,{children:[i.jsx(g.xo,{asChild:!0,children:i.jsx(h.z,{variant:"outline",className:(0,y.cn)("w-[280px] justify-start text-left font-normal",!e&&"text-muted-foreground",a),children:(0,i.jsxs)("div",{className:"flex justify-center",children:[i.jsx(j.Z,{className:"mr-2 h-4 w-4"}),e?(0,b.WU)(e,"PPP"):i.jsx("span",{children:s})]})})}),i.jsx(g.yk,{className:"w-auto p-0 text-xs ",children:i.jsx(v.f,{mode:"single",selected:e??void 0,onSelect:e=>t(e??null),initialFocus:!0})})]})}var w=s(10143);let k=({options:e,value:t,onChange:s})=>{let a=e.find(e=>e.value===t);return(0,i.jsxs)(w.h_,{children:[i.jsx(w.$F,{className:"border rounded p-2 w-full text-left",children:a?.label||"Select an option"}),i.jsx(w.AW,{className:"mt-1 w-full border rounded shadow-md",children:e.map(e=>i.jsx(w.Xi,{className:"p-2 w-full cursor-pointer",onClick:()=>s(e.value),children:e.label},e.value))})]})};(function(e){e.NOT_STARTED="NOT_STARTED",e.ONGOING="ONGOING",e.COMPLETED="COMPLETED"})(a||(a={})),function(e){e.PENDING="PENDING",e.PAID="PAID"}(r||(r={}));let _=({setErrors:e})=>{let[t,s]=(0,l.useState)({title:"",description:"",startDate:{expected:""},endDate:{expected:""},amount:void 0,status:a.NOT_STARTED});return{formData:t,setFormData:s,handleChange:e=>{let{name:t,value:a}=e.target;s(e=>({...e,[t]:a}))},validateForm:()=>{let s={};return t.title.trim()||(s.title="Title is required."),t.description.trim()||(s.description="Description is required."),new Date(t.startDate.expected)>new Date(t.endDate.expected)&&(s.startDate="Start date cannot be after end date."),(t.amount??0)<0&&(s.amount="Amount must be a positive number."),e(s),0===Object.keys(s).length}}},D=(e,t)=>{let s=(e,s,a)=>{t(t=>({...t,[e]:{...t[e],[s]:a}}))};return{handleNestedChange:s,handleDateChange:(e,t,a)=>{s(e,t,a?a.toISOString():"")}}};var Z=s(6260),q=s(56627);let C=({projectId:e,fetchMilestones:t,closeDialog:s})=>{let r=(0,p.v9)(e=>e.user?.uid),[n,c]=(0,l.useState)({}),{formData:o,setFormData:d,handleChange:u,validateForm:m}=_({setErrors:c}),{handleDateChange:x}=D(o,d),b=async a=>{if(a.preventDefault(),m())try{await Z.b.post("/milestones",{...o,userId:r,projectId:e}),(0,q.Am)({title:"Success",description:"Milestone created successfully!",variant:"default"}),t()}catch(e){(0,q.Am)({title:"Error",description:"Failed to create milestone.",variant:"destructive"})}finally{s()}};return i.jsx("div",{className:"flex justify-center items-center py-4",children:(0,i.jsxs)("div",{className:"w-full max-w-lg shadow-lg",children:[i.jsx("div",{children:i.jsx("h2",{className:"text-xl font-semibold",children:"Create Milestone"})}),i.jsx("div",{children:(0,i.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,i.jsxs)("div",{children:[i.jsx("label",{htmlFor:"title",className:"block text-sm font-medium mb-2",children:"Title"}),i.jsx(f.I,{id:"title",name:"title",value:o.title,onChange:u,placeholder:"Title",required:!0,className:"mt-1 block w-full"}),n.title&&i.jsx("p",{className:"text-xs text-red-500 mt-1",children:n.title})]}),(0,i.jsxs)("div",{children:[i.jsx("label",{htmlFor:"description",className:"block text-sm font-medium mb-2",children:"Description"}),i.jsx(f.I,{id:"description",name:"description",value:o.description,onChange:u,placeholder:"Description",required:!0,className:"mt-1 block w-full"}),n.description&&i.jsx("p",{className:"text-xs text-red-500 mt-1",children:n.description})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[i.jsx("label",{htmlFor:"startDate",className:"block whitespace-nowrap text-sm font-medium mb-2",children:"Start Date (Expected)"}),i.jsx(N,{selected:o.startDate.expected?new Date(o.startDate.expected):null,onChange:e=>x("startDate","expected",e),placeholderText:"Start Date (Expected)",className:"mt-1 block text-xs w-full"}),n.startDate&&i.jsx("p",{className:"text-xs text-red-500 mt-1",children:n.startDate})]}),(0,i.jsxs)("div",{children:[i.jsx("label",{htmlFor:"endDate",className:"block whitespace-nowrap text-sm font-medium mb-2",children:"End Date (Expected)"}),i.jsx(N,{selected:o.endDate.expected?new Date(o.endDate.expected):null,onChange:e=>x("endDate","expected",e),placeholderText:"End Date (Expected)",className:"mt-1 block text-xs w-full"}),n.endDate&&i.jsx("p",{className:"text-xs text-red-500 mt-1",children:n.endDate})]})]}),(0,i.jsxs)("div",{children:[i.jsx("label",{htmlFor:"amount",className:"block text-sm font-medium mb-2",children:"Amount"}),i.jsx(f.I,{id:"amount",name:"amount",type:"number",value:o.amount,onChange:u,placeholder:"Amount",required:!0,className:"mt-1 block w-full"}),n.amount&&i.jsx("p",{className:"text-xs text-red-500 mt-1",children:n.amount})]}),(0,i.jsxs)("div",{children:[i.jsx("label",{htmlFor:"status",className:"block text-sm font-medium mb-2",children:"Status"}),i.jsx(k,{options:[{label:"Not Started",value:a.NOT_STARTED},{label:"Ongoing",value:a.ONGOING},{label:"Completed",value:a.COMPLETED}],value:o.status,onChange:e=>d(t=>({...t,status:e}))}),n.status&&i.jsx("p",{className:"text-xs text-red-500 mt-1",children:n.status})]}),i.jsx("div",{className:"mt-4",children:i.jsx(h.z,{type:"submit",className:"w-full py-2",variant:"default",children:"Create Milestone"})})]})})]})})};var M=s(24118);function P({projectId:e,fetchMilestones:t}){let[s,a]=n().useState(!1);return(0,i.jsxs)(M.Vq,{open:s,onOpenChange:a,children:[i.jsx(M.hg,{asChild:!0,children:(0,i.jsxs)(h.z,{variant:"default",className:"hover:bg-transparent",children:[i.jsx(x.Z,{className:"mr-2"}),"Milestone"]})}),(0,i.jsxs)(M.cZ,{className:"max-h-[80vh] sm:max-w-[80vw] md:w-1/3 p-4 no-scrollbar overflow-y-auto",children:[(0,i.jsxs)(M.fK,{children:[i.jsx(M.$N,{children:"Create Milestone"}),i.jsx(M.Be,{children:"Fill out the form below to create a new milestone."})]}),i.jsx(C,{projectId:e,fetchMilestones:t,closeDialog:()=>{a(!1)}})]})]})}let S=()=>{let{project_id:e}=(0,c.useParams)(),[t,s]=(0,l.useState)([]),[a,r]=(0,l.useState)(!0),n=(0,l.useCallback)(async()=>{try{let t=await Z.b.get("/milestones",{params:{projectId:e}}),a={},i=t.data?.data.map(e=>{let t=e.stories?.length?e.stories:null;return e._id&&(a[e._id]=t),{...e,stories:t||[]}});s(i),r(!1)}catch(e){(0,q.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.error("Error fetching milestones:",e),r(!1)}},[e]),x=async(e,t,s,a=!1,r=null)=>{if(e.preventDefault(),!s){console.error("Milestone ID is undefined.");return}let i=a&&r?(s.stories??[]).map(e=>e._id===r.storyId?{...e,tasks:[...e.tasks||[],r.formData]}:e):[...s.stories||[],t],l={...s,stories:i};try{await Z.b.put(`/milestones/${s._id}`,l),(0,q.Am)({title:"Success",description:a?"Task added successfully!":"Story added successfully!",duration:3e3}),n()}catch(e){console.error("Error updating milestone:",e.response?.data||e.message),(0,q.Am)({title:"Error",description:"Failed to update milestone.",variant:"destructive",duration:3e3})}};return(0,l.useEffect)(()=>{n()},[n]),(0,i.jsxs)("div",{className:"flex min-h-screen h-auto w-full flex-col bg-muted/40",children:[i.jsx(o.Z,{menuItemsTop:m.yn,menuItemsBottom:m.$C,active:""}),(0,i.jsxs)("div",{className:"flex flex-col sm:gap-4 sm:py-4 sm:pl-14 mb-8",children:[i.jsx(d.Z,{menuItemsTop:m.yn,menuItemsBottom:m.$C,activeMenu:"",breadcrumbItems:[{label:"Dashboard",link:"/dashboard/business"},{label:"Project",link:"/dashboard/business"},{label:e,link:`/business/project/${e}`},{label:"Milestone",link:"#"}]}),(0,i.jsxs)("div",{className:"py-8 px-2 md:px-4",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[i.jsx("h1",{className:"text-xl md:text-2xl font-bold",children:"Project Milestones"}),i.jsx(h.z,{className:"px-3 py-1 ",children:i.jsx(P,{projectId:e,fetchMilestones:n})})]}),i.jsx("div",{className:"w-full flex justify-center items-center",children:a?i.jsx("p",{children:"Loading milestones..."}):t.length>0?i.jsx(u.Z,{fetchMilestones:n,milestones:t,handleStorySubmit:x}):i.jsx("div",{className:"flex justify-center items-center h-[50vh]",children:"No milestones found."})})]})]})]})}},33194:(e,t,s)=>{"use strict";s.d(t,{f:()=>o});var a=s(10326);s(17577);var r=s(11890),i=s(39183),l=s(25579),n=s(51223),c=s(91664);function o({className:e,classNames:t,showOutsideDays:s=!0,...o}){return a.jsx(l._W,{showOutsideDays:s,className:(0,n.cn)("p-3",e),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,n.cn)((0,c.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,n.cn)((0,c.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:({...e})=>a.jsx(r.Z,{className:"h-4 w-4"}),IconRight:({...e})=>a.jsx(i.Z,{className:"h-4 w-4"})},...o})}o.displayName="Calendar"},46319:(e,t,s)=>{"use strict";s.d(t,{$C:()=>b,Ne:()=>j,yn:()=>f});var a=s(10326),r=s(95920),i=s(57671),l=s(94909),n=s(12070),c=s(66307),o=s(69669),d=s(40617),u=s(69515),m=s(88378),h=s(40900),x=s(98091),p=s(46226);let f=[{href:"#",icon:a.jsx(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:a.jsx(r.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:a.jsx(i.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:a.jsx(l.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:a.jsx(l.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:a.jsx(n.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:a.jsx(c.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:a.jsx(o.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:a.jsx(d.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:a.jsx(u.Z,{className:"h-5 w-5"}),label:"Notes"}],b=[{href:"/business/settings/business-info",icon:a.jsx(m.Z,{className:"h-5 w-5"}),label:"Settings"}],j=[{href:"#",icon:a.jsx(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:a.jsx(r.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:a.jsx(u.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:a.jsx(h.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:a.jsx(x.Z,{className:"h-5 w-5"}),label:"Trash"}]},65945:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>l,__esModule:()=>i,default:()=>n});var a=s(68570);let r=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\business\project\[project_id]\milestone\page.tsx`),{__esModule:i,$$typeof:l}=r;r.default;let n=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\business\project\[project_id]\milestone\page.tsx#default`)}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588],()=>s(85439));module.exports=a})();