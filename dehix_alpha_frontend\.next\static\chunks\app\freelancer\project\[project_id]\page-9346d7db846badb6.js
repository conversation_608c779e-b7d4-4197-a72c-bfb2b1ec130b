(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5041],{30536:function(e,t,a){Promise.resolve().then(a.bind(a,71717))},5891:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},76035:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},43193:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]])},24241:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},27419:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("CodeXml",[["path",{d:"m18 16 4-4-4-4",key:"1inbqp"}],["path",{d:"m6 8-4 4 4 4",key:"15zrgr"}],["path",{d:"m14.5 4-5 16",key:"e7oirm"}]])},92145:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]])},49100:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},4086:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},47390:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},36141:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},33907:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},73347:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},33149:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]])},40064:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("TabletSmartphone",[["rect",{width:"10",height:"14",x:"3",y:"8",rx:"2",key:"1vrsiq"}],["path",{d:"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4",key:"1j4zmg"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},16717:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},10883:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},71717:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return N}});var s=a(57437),r=a(3274),l=a(16463),n=a(2265),i=a(62688),c=a(48185),d=a(87593),o=a(11444),m=a(49354),h=a(89733),x=a(54662),u=a(77209),p=a(70402),f=a(15922),j=a(78068);function y(e){let{_id:t,domain:a,freelancersRequired:r,skills:i,experience:d,minConnect:y,description:v,email:g,status:N,startDate:b,endDate:w,className:k,domain_id:Z,...M}=e,S=(0,o.v9)(e=>e.user),[C,D]=(0,n.useState)(""),[z,A]=(0,n.useState)(""),[E,L]=(0,n.useState)(!1),P=(0,l.useParams)(),[V,I]=n.useState([]),[_,q]=(0,n.useState)(!1),B=async e=>{e.preventDefault();try{await f.b.post("/bid",{current_price:C,description:z,bidder_id:S.uid,project_id:P.project_id,domain_id:Z,profile_id:t}),D(""),A(""),L(!1),(0,j.Am)({title:"Bid Added",description:"The Bid has been successfully added."})}catch(e){console.error("Error submitting bid:",e),(0,j.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}};return(0,n.useEffect)(()=>{(async function(){try{let e=(await f.b.get("/bid/".concat(S.uid,"/bid"))).data.data.map(e=>e.profile_id);I(e)}catch(e){console.error("API Error:",e),(0,j.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}})()},[S.uid]),(0,n.useEffect)(()=>{q(V.includes(t))},[V,t]),(0,s.jsxs)(c.Zb,{className:(0,m.cn)("w-[350px]",k),...M,children:[(0,s.jsxs)(c.Ol,{children:[(0,s.jsxs)(c.ll,{children:[a," (",r,")"]}),(0,s.jsxs)(c.SZ,{className:"text-gray-600",children:["Requirement is of ",r," freelancer(s) for"," ",a.toLowerCase()," profile.",(0,s.jsx)("br",{}),(0,s.jsx)("p",{className:"break-words text-white",children:v})]})]}),(0,s.jsx)(c.aY,{className:"grid gap-4",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("ul",{className:"flex flex-wrap gap-2",children:[g&&(0,s.jsxs)("li",{className:"min-w-[45%]",children:[(0,s.jsx)("span",{className:"text-gray-700 font-semibold",children:"Email: "}),g]}),N&&(0,s.jsxs)("li",{className:"min-w-[45%]",children:[(0,s.jsx)("span",{className:"text-gray-700 font-semibold",children:"Status: "}),N]}),b&&(0,s.jsxs)("li",{className:"min-w-[45%]",children:[(0,s.jsxs)("span",{className:"text-gray-700 font-semibold",children:["Start Date:"," "]}),b]}),w&&(0,s.jsxs)("li",{className:"min-w-[45%]",children:[(0,s.jsx)("span",{className:"text-gray-400 font-semibold",children:"End Date: "}),w]}),(0,s.jsxs)("li",{className:"min-w-[45%]",children:[(0,s.jsx)("span",{className:"text-gray-400 font-semibold",children:"Experience: "}),d," years"]}),(0,s.jsxs)("li",{className:"min-w-[45%]",children:[(0,s.jsx)("span",{className:"text-gray-400 font-semibold",children:"Min Connect: "}),y]})]}),i.length>0&&(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)("span",{className:"text-gray-700 font-semibold",children:"Skills: "}),(0,s.jsx)("ul",{className:"flex flex-wrap gap-1 mt-1",children:i.map((e,t)=>(0,s.jsx)("li",{className:"bg-gray-200 text-black rounded px-2 py-1 text-sm",children:e},t))})]})]})}),(0,s.jsx)(c.eW,{children:(0,s.jsxs)(x.Vq,{open:E,onOpenChange:L,children:[(0,s.jsx)(x.hg,{asChild:!0,children:(0,s.jsx)(h.z,{variant:"outline",type:"button",disabled:_,children:_?"Applied":"Bid"})}),(0,s.jsxs)(x.cZ,{className:"sm:max-w-[425px]",children:[(0,s.jsxs)(x.fK,{children:[(0,s.jsx)(x.$N,{children:"Bid"}),(0,s.jsx)(x.Be,{children:"Click on bid if you want to bid for this profile"})]}),(0,s.jsxs)("form",{onSubmit:B,children:[(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(p.Label,{htmlFor:"amount",className:"text-center",children:"Amount"}),(0,s.jsx)(u.I,{id:"amount",type:"number",value:C,onChange:e=>D(e.target.value),className:"col-span-3",required:!0})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(p.Label,{htmlFor:"description",className:"text-right block",children:"Description"}),(0,s.jsx)(u.I,{id:"description",type:"text",value:z,onChange:e=>A(e.target.value),className:"col-span-3",required:!0})]})]}),(0,s.jsx)(x.cN,{children:(0,s.jsx)(h.z,{type:"submit",disabled:_,children:_?"Applied":"Bid"})})]})]})]})})]})}var v=a(64797),g=a(66227);function N(){var e;let{project_id:t}=(0,l.useParams)(),[a,o]=(0,n.useState)(null),[m,h]=(0,n.useState)(!0);return((0,n.useEffect)(()=>{(async()=>{try{var e,a,s;let r=await f.b.get("/project/".concat(t)),l=(null==r?void 0:null===(a=r.data)||void 0===a?void 0:null===(e=a.data)||void 0===e?void 0:e.data)||(null==r?void 0:null===(s=r.data)||void 0===s?void 0:s.data);l?o(l):console.error("Unexpected data structure:",r.data)}catch(e){console.error("API Error:",e),(0,j.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}finally{h(!1)}})()},[t]),m)?(0,s.jsx)("div",{className:"flex justify-center items-center h-screen",children:(0,s.jsx)(r.Z,{className:"my-4 h-8 w-8 animate-spin"})}):a?(0,s.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,s.jsx)(v.Z,{menuItemsTop:g.yn,menuItemsBottom:g.$C,active:"Market"}),(0,s.jsxs)("div",{className:"flex flex-col sm:gap-4 sm:py-4 sm:pl-14 mb-8",children:[(0,s.jsx)(i.Z,{menuItemsTop:g.yn,menuItemsBottom:g.$C,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Marketplace",link:"/freelancer/market"},{label:t,link:"#"}]}),(0,s.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8 lg:grid-cols-3 xl:grid-cols-3",children:[(0,s.jsx)("div",{className:"grid auto-rows-max items-start gap-4 md:gap-8 lg:col-span-2",children:(0,s.jsx)("div",{children:(0,s.jsx)(d.Z,{projectName:a.projectName,description:a.description,email:a.email,status:a.status,startDate:a.createdAt,endDate:a.end,projectDomain:a.projectDomain,projectId:a._id,skills:a.skillsRequired,userRole:"Freelancer"})})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(c.ll,{className:"group flex items-center gap-2 text-2xl",children:"Profiles"}),null==a?void 0:null===(e=a.profiles)||void 0===e?void 0:e.map((e,t)=>(0,s.jsx)(y,{className:"w-full min-w-full p-4 shadow-md rounded-lg",...e},t))]})]})]})]}):(0,s.jsx)("div",{children:"Project data not found."})}},68515:function(e,t,a){"use strict";var s=a(57437);a(2265);var r=a(24241);t.Z=e=>{let{startDate:t,endDate:a}=e,l=t?new Date(t).toLocaleDateString():"Start Date N/A",n="current"!==a&&a?new Date(a).toLocaleDateString():"Still Going On!";return(0,s.jsxs)("div",{className:"flex relative whitespace-nowrap items-start sm:items-center gap-1 rounded-md ",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2 ",children:[(0,s.jsx)(r.Z,{className:"w-4 h-4 sm:w-5 sm:h-5 "}),(0,s.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:"Start  ".concat(l)})]}),(0,s.jsx)("p",{children:"-"}),(0,s.jsx)("div",{className:"flex items-center ",children:(0,s.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:" ".concat(n)})})]})}},87593:function(e,t,a){"use strict";var s=a(57437);a(2265);var r=a(16717),l=a(27419),n=a(4086),i=a(92145),c=a(92940),d=a(87138),o=a(48185),m=a(79055),h=a(44475),x=a(29973),u=a(68515),p=a(89733);t.Z=function(e){let{projectName:t,description:a,email:f,status:j,startDate:y,endDate:v,projectDomain:g,skills:N,userRole:b="Business",projectId:w,handleCompleteProject:k}=e,{text:Z,className:M}=(0,h.S)(j),S="/".concat(b.toLowerCase(),"/project/").concat(w,"/milestone");return(0,s.jsxs)(o.Zb,{className:"shadow-lg border border-gray-800 rounded-lg",children:[(0,s.jsxs)(o.Ol,{children:[(0,s.jsxs)("div",{className:"flex flex-wrap justify-between items-center mb-0.5",children:[(0,s.jsx)(o.ll,{className:"text-xl md:text-2xl font-semibold",children:t}),(0,s.jsx)(m.C,{className:"".concat(M," px-1 py-0.5 text-xs md:text-sm rounded-md"),children:Z})]}),(0,s.jsx)(x.Separator,{className:"my-4"})]}),(0,s.jsxs)(o.aY,{className:"space-y-6",children:[(0,s.jsx)(u.Z,{startDate:y,endDate:v}),(0,s.jsx)("p",{className:"text-sm md:text-base leading-relaxed",children:a}),(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 md:gap-6",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-2 px-3 py-1 text-xs md:text-sm rounded-md shadow-inner w-full md:w-1/2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(r.Z,{className:"w-4 h-4 block md:hidden"}),(0,s.jsx)("p",{className:"font-medium",children:"Project Domain:"})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:g.map((e,t)=>(0,s.jsx)(m.C,{className:"bg-gray-200 text-gray-900 text-xs md:text-sm px-2 py-1 rounded-full",children:e},t))})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-2 px-3 py-1 text-xs md:text-sm rounded-md shadow-inner w-full md:w-1/2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l.Z,{className:"w-4 h-4 block md:hidden"}),(0,s.jsx)("p",{className:"font-medium",children:"Skills:"})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:N.map((e,t)=>(0,s.jsx)(m.C,{className:"bg-gray-200 text-gray-900 text-xs md:text-sm px-2 py-1 rounded-full",children:e},t))})]})]}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-4 px-3 py-1 text-xs md:text-sm rounded-md shadow-inner",children:[(0,s.jsx)(n.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{className:"text-sm",children:f})]}),(0,s.jsxs)("div",{className:"flex justify-between mt-4",children:[(0,s.jsx)(d.default,{href:S,children:(0,s.jsxs)(p.z,{className:"flex items-center px-4 py-2 text-xs md:text-sm font-medium text-white rounded-md bg-blue-600 hover:bg-blue-500",size:"sm",children:[(0,s.jsx)(i.Z,{className:"w-4 h-4 mr-1"}),"Milestone"]})}),(0,s.jsxs)(p.z,{className:"flex items-center px-4 py-2 text-xs md:text-sm font-medium text-white rounded-md ".concat("COMPLETED"===Z?"bg-green-600 hover:bg-green-500":"bg-blue-600 hover:bg-blue-500"),size:"sm",onClick:k,disabled:!k,children:[(0,s.jsx)(c.Z,{className:"w-4 h-4 mr-1"}),"COMPLETED"===Z?"Completed":"Mark complete"]})]})]})]})}},70402:function(e,t,a){"use strict";a.r(t),a.d(t,{Label:function(){return d}});var s=a(57437),r=a(2265),l=a(38364),n=a(12218),i=a(49354);let c=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.f,{ref:t,className:(0,i.cn)(c(),a),...r})});d.displayName=l.f.displayName},29973:function(e,t,a){"use strict";a.r(t),a.d(t,{Separator:function(){return i}});var s=a(57437),r=a(2265),l=a(48484),n=a(49354);let i=r.forwardRef((e,t)=>{let{className:a,orientation:r="horizontal",decorative:i=!0,...c}=e;return(0,s.jsx)(l.f,{ref:t,decorative:i,orientation:r,className:(0,n.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",a),...c})});i.displayName=l.f.displayName},66227:function(e,t,a){"use strict";a.d(t,{$C:function(){return v},yL:function(){return g},yn:function(){return y}});var s=a(57437),r=a(11005),l=a(33149),n=a(76035),i=a(49100),c=a(40064),d=a(43193),o=a(36141),m=a(33907),h=a(47390),x=a(73347),u=a(24258),p=a(5891),f=a(10883),j=a(66648);let y=[{href:"#",icon:(0,s.jsx)(j.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,s.jsx)(r.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:(0,s.jsx)(l.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:(0,s.jsx)(n.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:(0,s.jsx)(i.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:(0,s.jsx)(c.Z,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:(0,s.jsx)(d.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:(0,s.jsx)(o.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:(0,s.jsx)(m.Z,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:(0,s.jsx)(h.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,s.jsx)(x.Z,{className:"h-5 w-5"}),label:"Notes"}],v=[{href:"/freelancer/settings/personal-info",icon:(0,s.jsx)(u.Z,{className:"h-5 w-5"}),label:"Settings"}];j.default,r.Z,x.Z,p.Z,f.Z;let g=[{href:"#",icon:(0,s.jsx)(j.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,s.jsx)(r.Z,{className:"h-5 w-5"}),label:"Home"}]},38364:function(e,t,a){"use strict";a.d(t,{f:function(){return i}});var s=a(2265),r=a(18676),l=a(57437),n=s.forwardRef((e,t)=>(0,l.jsx)(r.WV.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var i=n},48484:function(e,t,a){"use strict";a.d(t,{f:function(){return d}});var s=a(2265),r=a(18676),l=a(57437),n="horizontal",i=["horizontal","vertical"],c=s.forwardRef((e,t)=>{let{decorative:a,orientation:s=n,...c}=e,d=i.includes(s)?s:n;return(0,l.jsx)(r.WV.div,{"data-orientation":d,...a?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:t})});c.displayName="Separator";var d=c}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2455,9726,2688,2971,7023,1744],function(){return e(e.s=30536)}),_N_E=e.O()}]);