"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/personal-info/page",{

/***/ "(app-pages-browser)/./src/components/form/CoverLetterTextarea.tsx":
/*!*****************************************************!*\
  !*** ./src/components/form/CoverLetterTextarea.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_textarea__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _ui_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst CoverLetterTextarea = (param)=>{\n    let { value = \"\", onChange, error } = param;\n    _s();\n    const [wordCount, setWordCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [charCount, setCharCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const words = value.trim().split(/\\s+/).filter((word)=>word.length > 0);\n        setWordCount(words.length);\n        setCharCount(value.length);\n    }, [\n        value\n    ]);\n    const handleChange = (e)=>{\n        const newValue = e.target.value;\n        onChange(newValue);\n    };\n    const isWordCountValid = wordCount >= 500;\n    const isCharCountValid = charCount >= 500;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_form__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_textarea__WEBPACK_IMPORTED_MODULE_2__.Textarea, {\n                    placeholder: \"Write your cover letter here... (optional - minimum 500 words if provided)\",\n                    value: value,\n                    onChange: handleChange,\n                    className: \"min-h-[200px] resize-y \".concat(error ? \"border-red-500 focus:border-red-500\" : \"\"),\n                    rows: 10\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            value && value.trim().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"\".concat(isWordCountValid ? \"text-green-600\" : \"text-orange-500\"),\n                                children: [\n                                    \"Words: \",\n                                    wordCount,\n                                    \"/500 \",\n                                    isWordCountValid ? \"✓\" : \"⚠\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"\".concat(isCharCountValid ? \"text-green-600\" : \"text-orange-500\"),\n                                children: [\n                                    \"Characters: \",\n                                    charCount,\n                                    \"/500 \",\n                                    isCharCountValid ? \"✓\" : \"⚠\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined),\n                    !isWordCountValid && wordCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-orange-500 text-xs\",\n                        children: [\n                            500 - wordCount,\n                            \" more words needed for complete cover letter\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined),\n            value && value.trim().length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Cover letter is optional. Leave empty to skip, or write at least 500 words for a complete cover letter.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_form__WEBPACK_IMPORTED_MODULE_3__.FormMessage, {\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                lineNumber: 80,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Cover letter is optional\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, undefined),\n                            \" - but if you choose to write one, here are some tips:\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mt-1 space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Introduce yourself and your relevant experience\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Explain why you're interested in this type of work\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Highlight your key skills and achievements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Mention specific technologies or tools you're proficient with\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Describe your work style and communication approach\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Write at least 500 words for a complete cover letter\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CoverLetterTextarea, \"pPd2u7CCW1Uh8UpOplFmywQOhgg=\");\n_c = CoverLetterTextarea;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CoverLetterTextarea);\nvar _c;\n$RefreshReg$(_c, \"CoverLetterTextarea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/form/CoverLetterTextarea.tsx\n"));

/***/ })

});