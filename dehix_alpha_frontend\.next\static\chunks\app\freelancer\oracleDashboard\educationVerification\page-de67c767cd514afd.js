(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5049],{38172:function(e,s,t){Promise.resolve().then(t.bind(t,20209))},20209:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return D}});var a=t(57437),l=t(404),r=t(29406),i=t(2265),n=t(89733),c=t(54662),d=t(97694),o=t(64797),m=t(746),x=t(15922);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let u=(0,t(33480).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var h=t(47390),f=t(31014),j=t(39343),p=t(59772),g=t(48185),v=t(79055),N=t(93363),y=t(89736),b=t(4919),w=t(78068);let S=p.z.object({type:p.z.enum(["Approved","Denied"],{required_error:"You need to select a type."}),comment:p.z.string().optional()});var C=e=>{let{_id:s,type:t,degree:l,location:r,startFrom:c,endTo:o,grade:m,fieldOfStudy:p,comments:C,status:E,onStatusUpdate:_,onCommentUpdate:D}=e,[k,I]=(0,i.useState)(E),z=(0,j.cI)({resolver:(0,f.F)(S),mode:"onChange"}),V=z.watch("type");async function Z(e){try{await x.b.put("/verification/".concat(s,"/oracle?doc_type=education"),{comments:e.comment,verification_status:e.type})}catch(e){(0,w.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}I(e.type),_(e.type),D(e.comment||"")}return(0,i.useEffect)(()=>{I(E)},[E]),(0,a.jsxs)(g.Zb,{className:"max-w-full md:max-w-2xl",children:[(0,a.jsxs)(g.Ol,{children:[(0,a.jsxs)(g.ll,{className:"flex justify-between",children:[(0,a.jsx)("span",{children:t}),"pending"===k||"added"===k?(0,a.jsx)(v.C,{className:"bg-warning-foreground text-white",children:"PENDING"}):"Approved"===k?(0,a.jsx)(v.C,{className:"bg-success text-white",children:"Approved"}):(0,a.jsx)(v.C,{className:"bg-red-500 text-white",children:"Denied"})]}),(0,a.jsxs)(g.SZ,{className:"text-justify text-gray-600",children:[(0,a.jsx)("br",{}),(0,a.jsxs)(y.u,{children:[(0,a.jsx)(y.aJ,{asChild:!0,children:(0,a.jsxs)("p",{className:"text-lg text-gray-600 flex items-center mt-3",children:[(0,a.jsx)(u,{className:"mr-2"}),r]})}),(0,a.jsx)(y._v,{side:"bottom",children:"Location"})]})]})]}),(0,a.jsx)(g.aY,{children:(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsxs)("p",{className:"text-m text-gray-600 mb-2",children:[(0,a.jsx)("span",{className:"text-gray-500 font-semibold",children:"degree:"})," ",l]}),(0,a.jsxs)("p",{className:"text-m text-gray-600 mb-2",children:[(0,a.jsx)("span",{className:"text-gray-500 font-semibold",children:"Field Of study:"})," ",p]}),(0,a.jsxs)("p",{className:"text-m text-gray-600 mb-2",children:[(0,a.jsx)("span",{className:"text-gray-500 font-semibold",children:"Grade:"})," ",m]}),C&&(0,a.jsxs)("p",{className:"mt-2 flex items-center text-gray-500 border p-3 rounded",children:[(0,a.jsx)(h.Z,{className:"mr-2"}),C]})]})}),(0,a.jsxs)(g.eW,{className:"flex flex-col items-center",children:[(0,a.jsxs)("div",{className:"flex flex-1 gap-4",children:[new Date(c).toLocaleDateString()," -","current"!==o?new Date(o).toLocaleDateString():"Current"]}),("pending"===k||"added"===k)&&(0,a.jsx)(N.l0,{...z,children:(0,a.jsxs)("form",{onSubmit:z.handleSubmit(Z),className:"w-full space-y-6 mt-6",children:[(0,a.jsx)(N.Wi,{control:z.control,name:"type",render:e=>{let{field:s}=e;return(0,a.jsxs)(N.xJ,{className:"space-y-3",children:[(0,a.jsx)(N.lX,{children:"Choose Verification Status:"}),(0,a.jsx)(N.NI,{children:(0,a.jsxs)(d.E,{onValueChange:s.onChange,defaultValue:s.value,className:"flex flex-row space-x-4",children:[(0,a.jsxs)(N.xJ,{className:"flex items-center space-x-3",children:[(0,a.jsx)(N.NI,{children:(0,a.jsx)(d.m,{value:"Approved"})}),(0,a.jsx)(N.lX,{className:"font-normal",children:"Approved"})]}),(0,a.jsxs)(N.xJ,{className:"flex items-center space-x-3",children:[(0,a.jsx)(N.NI,{children:(0,a.jsx)(d.m,{value:"Denied"})}),(0,a.jsx)(N.lX,{className:"font-normal",children:"Denied"})]})]})}),(0,a.jsx)(N.zG,{})]})}}),(0,a.jsx)(N.Wi,{control:z.control,name:"comment",render:e=>{let{field:s}=e;return(0,a.jsxs)(N.xJ,{children:[(0,a.jsx)(N.lX,{children:"Comments:"}),(0,a.jsx)(N.NI,{children:(0,a.jsx)(b.g,{placeholder:"Enter comments:",...s})}),(0,a.jsx)(N.zG,{})]})}}),(0,a.jsx)(n.z,{type:"submit",className:"w-full",disabled:!V||z.formState.isSubmitting,children:"Submit"})]})})]})]})},E=t(97540),_=t(62688);function D(){let[e,s]=(0,i.useState)([]),[t,u]=(0,i.useState)("all"),[h,f]=(0,i.useState)(!1),j=e=>{u(e),f(!1)},p=e.filter(e=>"all"===t||e.verificationStatus===t||"current"===t&&e.verificationStatus===E.sB.PENDING),g=(0,i.useCallback)(async()=>{try{let e=(await x.b.get("/verification/oracle?doc_type=education")).data.data.flatMap(e=>{var s;return(null===(s=e.result)||void 0===s?void 0:s.projects)?Object.values(e.result.projects).map(s=>({...s,verifier_id:e.verifier_id,verifier_username:e.verifier_username})):[]});s(e)}catch(e){console.log(e,"error in getting verification data"),(0,w.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}},[]);return(0,i.useEffect)(()=>{g()},[g]),(0,a.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,a.jsx)(o.Z,{menuItemsTop:m.y,menuItemsBottom:m.$,active:"Education Verification"}),(0,a.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,a.jsx)(_.Z,{menuItemsTop:m.y,menuItemsBottom:m.$,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Oracle",link:"#"},{label:"Education Verification",link:"#"}]}),(0,a.jsxs)("div",{className:"mb-8 ml-4 flex justify-between mt-8 md:mt-4 items-center",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Education Verification"}),(0,a.jsx)("p",{className:"text-gray-400 mt-2",children:"Monitor the status of your Education verifications."})]}),(0,a.jsx)(n.z,{variant:"outline",size:"icon",className:"mr-8 mb-12",onClick:()=>f(!0),children:(0,a.jsx)(l.Z,{className:"h-4 w-4"})})]}),(0,a.jsx)(c.Vq,{open:h,onOpenChange:f,children:(0,a.jsxs)(c.cZ,{children:[(0,a.jsx)(c.fK,{children:(0,a.jsx)(c.$N,{children:"Filter Education Status"})}),(0,a.jsxs)(d.E,{defaultValue:"all",value:t,onValueChange:e=>j(e),className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.m,{value:"all",id:"filter-all"}),(0,a.jsx)("label",{htmlFor:"filter-all",children:"All"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.m,{value:"current",id:"filter-current"}),(0,a.jsx)("label",{htmlFor:"filter-current",children:"Pending"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.m,{value:"verified",id:"filter-verified"}),(0,a.jsx)("label",{htmlFor:"filter-verified",children:"Verified"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.m,{value:"rejected",id:"filter-rejected"}),(0,a.jsx)("label",{htmlFor:"filter-rejected",children:"Rejected"})]})]}),(0,a.jsx)(c.cN,{children:(0,a.jsx)(n.z,{type:"button",onClick:()=>f(!1),children:"Close"})})]})}),(0,a.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8    grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3",children:[p.map((e,s)=>(0,a.jsx)(C,{type:"education",_id:e._id,degree:e.degree,location:e.universityName,startFrom:e.startDate,endTo:e.endDate,grade:e.grade,fieldOfStudy:e.fieldOfStudy,comments:e.comments,status:e.verificationStatus,onStatusUpdate:e=>{console.log("Status updated to:",e)},onCommentUpdate:e=>{console.log("Comment updated to:",e)}},s)),0===e.length?(0,a.jsxs)("div",{className:"text-center w-[90vw] px-auto mt-20 py-10",children:[(0,a.jsx)(r.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No Education verification for you now."})]}):null]})]})]})}}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,1374,2455,9726,2688,2480,2971,7023,1744],function(){return e(e.s=38172)}),_N_E=e.O()}]);