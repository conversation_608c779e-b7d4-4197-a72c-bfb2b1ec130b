(()=>{var e={};e.id=1341,e.ids=[1341],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},56523:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>h,originalPathname:()=>d,pages:()=>u,routeModule:()=>x,tree:()=>o}),r(27610),r(54302),r(12523);var t=r(23191),a=r(88716),i=r(37922),l=r.n(i),n=r(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);r.d(s,c);let o=["",{children:["freelancer",{children:["businessProfile",{children:["[business_id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,27610)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\businessProfile\\[business_id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\businessProfile\\[business_id]\\page.tsx"],d="/freelancer/businessProfile/[business_id]/page",h={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/freelancer/businessProfile/[business_id]/page",pathname:"/freelancer/businessProfile/[business_id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},90133:(e,s,r)=>{Promise.resolve().then(r.bind(r,72844))},40900:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(80851).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},12070:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(80851).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},66307:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(80851).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69669:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(80851).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40617:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},57671:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(80851).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},69515:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(80851).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},98091:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,r(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},72844:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>p});var t=r(10326),a=r(17577),i=r(35047),l=r(46319),n=r(92166),c=r(29752),o=r(56627),u=r(3594);r(6260);var d=r(38227),h=r(40588),x=r(91664);function p(){let{business_id:e}=(0,i.useParams)(),[s,r]=(0,a.useState)(null),[p,m]=(0,a.useState)(!0);return(0,t.jsxs)("div",{className:"flex min-h-screen bg-muted/40",children:[t.jsx(n.Z,{menuItemsTop:l.yn,menuItemsBottom:l.$C,active:"Business Profile"}),(0,t.jsxs)("div",{className:"flex mb-8 flex-1 flex-col sm:pl-14",children:[t.jsx(h.Z,{menuItemsTop:l.yn,menuItemsBottom:l.$C,activeMenu:"Business Profile",breadcrumbItems:[{label:"Dashboard",link:"/dashboard"},{label:"Business Profile",link:"/dashboard/business-profile"},{label:`#${e}`,link:"#"}]}),(0,t.jsxs)("main",{className:"flex flex-col items-center p-4 sm:px-6 gap-6 mt-7",children:[t.jsx("h1",{children:"Business Profile Overview"}),(0,t.jsxs)(c.Zb,{className:"w-full max-w-4xl bg-black text-white p-4 shadow-md",children:[t.jsx(c.Zb,{className:"p-14 flex items-center rounded-lg",children:p?t.jsx(d.O,{className:"w-24 h-24 rounded-full mr-6"}):(0,t.jsxs)(u.Avatar,{className:"w-24 h-24 rounded-full mr-6 relative overflow-hidden border-4 border-primary shadow-lg hover:scale-110 transition-transform duration-300 ease-in-out",children:[t.jsx(u.AvatarImage,{src:"/default-avatar.png",alt:`${s?.firstName} ${s?.lastName} Profile Picture`,className:"object-cover w-full h-full"}),t.jsx(u.AvatarFallback,{children:`${s?.firstName?.[0]||"J"}${s?.lastName?.[0]||"D"}`})]})}),(0,t.jsxs)(c.Zb,{className:"w-full max-w-4xl shadow-lg",children:[t.jsx(c.Ol,{children:t.jsx(c.ll,{children:"Projects"})}),t.jsx(c.aY,{children:p?t.jsx(d.O,{className:"h-6 w-48"}):s?.ProjectList?.length?t.jsx("ul",{className:"list-disc pl-5",children:s.ProjectList.map((e,s)=>t.jsx("li",{children:e},s))}):t.jsx("p",{children:"No projects listed."})})]}),(0,t.jsxs)(c.Zb,{className:"w-full max-w-4xl shadow-lg",children:[t.jsx(c.Ol,{children:t.jsx(c.ll,{children:"Contact Information"})}),t.jsx(c.aY,{children:p?t.jsx(d.O,{className:"h-6 w-48"}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("p",{children:["Email: ",s?.email]}),s?.linkedin&&(0,t.jsxs)("p",{children:["LinkedIn:"," ",t.jsx("a",{href:s.linkedin,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:s.linkedin})]}),s?.personalWebsite&&(0,t.jsxs)("p",{children:["Website:"," ",t.jsx("a",{href:s.personalWebsite,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:s.personalWebsite})]})]})})]})]}),t.jsx(x.z,{onClick:()=>{let e={title:"Business Profile",text:`Check out the profile of ${s?.firstName} ${s?.lastName} at ${s?.companyName}.`,url:window.location.href};navigator.share?navigator.share(e).catch(e=>console.error("Error sharing:",e)):(0,o.Am)({variant:"destructive",title:"Error",description:"Sharing is not supported on this browser."})},className:"mt-4",children:"Share Profile"})]})]})]})}},38227:(e,s,r)=>{"use strict";r.d(s,{O:()=>i});var t=r(10326),a=r(51223);function i({className:e,...s}){return t.jsx("div",{className:(0,a.cn)("animate-pulse rounded-md bg-primary/10",e),...s})}},46319:(e,s,r)=>{"use strict";r.d(s,{$C:()=>b,Ne:()=>j,yn:()=>f});var t=r(10326),a=r(95920),i=r(57671),l=r(94909),n=r(12070),c=r(66307),o=r(69669),u=r(40617),d=r(69515),h=r(88378),x=r(40900),p=r(98091),m=r(46226);let f=[{href:"#",icon:t.jsx(m.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:t.jsx(a.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:t.jsx(i.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:t.jsx(l.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:t.jsx(l.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:t.jsx(n.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:t.jsx(c.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:t.jsx(o.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:t.jsx(u.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:t.jsx(d.Z,{className:"h-5 w-5"}),label:"Notes"}],b=[{href:"/business/settings/business-info",icon:t.jsx(h.Z,{className:"h-5 w-5"}),label:"Settings"}],j=[{href:"#",icon:t.jsx(m.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:t.jsx(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:t.jsx(d.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:t.jsx(x.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:t.jsx(p.Z,{className:"h-5 w-5"}),label:"Trash"}]},27610:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>l,__esModule:()=>i,default:()=>n});var t=r(68570);let a=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\businessProfile\[business_id]\page.tsx`),{__esModule:i,$$typeof:l}=a;a.default;let n=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\businessProfile\[business_id]\page.tsx#default`)}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588],()=>r(56523));module.exports=t})();