(()=>{var e={};e.id=4441,e.ids=[4441],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},8742:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>d,routeModule:()=>x,tree:()=>o}),r(78761),r(54302),r(12523);var s=r(23191),a=r(88716),c=r(37922),i=r.n(c),l=r(95231),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let o=["",{children:["freelancer",{children:["project",{children:["completed",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,78761)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\project\\completed\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\project\\completed\\page.tsx"],p="/freelancer/project/completed/page",u={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/freelancer/project/completed/page",pathname:"/freelancer/project/completed",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},7428:(e,t,r)=>{Promise.resolve().then(r.bind(r,61415))},69669:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},23015:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},60763:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},61415:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(10326),a=r(77506),c=r(23015),i=r(25842),l=r(17577),n=r(92166),o=r(15634);r(6260);var d=r(21486),p=r(40588);function u(){let e=(0,i.v9)(e=>e.user),[t,r]=(0,l.useState)([]),[u,x]=(0,l.useState)(!1);return(0,s.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[s.jsx(n.Z,{menuItemsTop:o.y,menuItemsBottom:o.$,active:"Completed Projects"}),(0,s.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[s.jsx(p.Z,{menuItemsTop:o.y,menuItemsBottom:o.$,activeMenu:"Completed Projects",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Projects",link:"/freelancer/project/current"},{label:"Completed Projects",link:"#"}]}),(0,s.jsxs)("div",{className:"mb-8 ml-10",children:[s.jsx("h1",{className:"text-3xl font-bold",children:"Completed Projects"}),s.jsx("p",{className:"text-gray-400 mt-2",children:"Explore and manage your successfully completed freelance projects."})]}),u?s.jsx("div",{className:"flex justify-center items-center min-h-[50vh]",children:s.jsx(a.Z,{size:40,className:"animate-spin"})}):s.jsx("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8    grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3",children:0===t.length?(0,s.jsxs)("div",{className:"col-span-full text-center mt-20 w-full",children:[s.jsx(c.Z,{className:"mx-auto text-gray-500",size:"100"}),s.jsx("p",{className:"text-gray-500",children:"No projects available"})]}):t.map((t,r)=>s.jsx(d.t,{project:t,type:e.type},r))})]})]})}r(56627)},21486:(e,t,r)=>{"use strict";r.d(t,{t:()=>x});var s=r(10326),a=r(90434),c=r(60763),i=r(51223),l=r(91664),n=r(29752),o=r(38443),d=r(71064),p=r(58285),u=r(39958);function x({cardClassName:e,project:t,type:r=p.Dy.BUSINESS,...x}){let{text:h,className:m}=(0,d.S)(t.status);return(0,s.jsxs)(n.Zb,{className:(0,i.cn)("flex flex-col h-[400px]",e),...x,children:[(0,s.jsxs)(n.Ol,{children:[(0,s.jsxs)(n.ll,{className:"flex",children:[t.projectName,"\xa0",t.verified&&s.jsx(c.Z,{className:"text-success"})]}),(0,s.jsxs)(n.SZ,{className:"text-gray-600",children:[s.jsx("p",{className:"my-auto",children:t.createdAt?new Date(t.createdAt).toLocaleDateString():"N/A"}),s.jsx("br",{}),s.jsx(o.C,{className:m,children:h})]})]}),(0,s.jsxs)(n.aY,{className:"grid gap-4 mb-auto flex-grow",children:[(0,s.jsxs)("div",{className:"mb-4 items-start pb-4 last:mb-0 last:pb-0 w-full",children:[s.jsx("span",{className:"flex h-2 w-2 rounded-full"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:t.description?.length>40?`${t.description.slice(0,40)}...`:t.description||"No description available"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{children:[s.jsx("strong",{children:"Company:"})," ",t.companyName]}),(0,s.jsxs)("p",{children:[s.jsx("strong",{children:"Role:"})," ",t.role]}),(0,s.jsxs)("p",{children:[s.jsx("strong",{children:"Experience:"})," ",t.experience]}),s.jsx("div",{className:"flex flex-wrap gap-1 mt-2",children:t?.skillsRequired?.map((e,t)=>s.jsx(o.C,{className:"text-xs text-white bg-muted",children:e},t))})]})]}),s.jsx(n.eW,{children:s.jsx(a.default,{href:`/${r.toLocaleLowerCase()}/project/${t._id}`,className:"w-full",children:s.jsx(l.z,{className:`w-full ${t.status===u.sB.COMPLETED&&"bg-green-900 hover:bg-green-700"}`,children:"View full details"})})})]})}},15634:(e,t,r)=>{"use strict";r.d(t,{$:()=>x,y:()=>u});var s=r(10326),a=r(95920),c=r(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,c.Z)("FolderDot",[["path",{d:"M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z",key:"1fr9dc"}],["circle",{cx:"12",cy:"13",r:"1",key:"49l61u"}]]),l=(0,c.Z)("Pointer",[["path",{d:"M22 14a8 8 0 0 1-8 8",key:"56vcr3"}],["path",{d:"M18 11v-1a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v0",key:"1pp0yd"}],["path",{d:"M14 10V9a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v1",key:"u654g"}],["path",{d:"M10 9.5V4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v10",key:"1e2dtv"}],["path",{d:"M18 11a2 2 0 1 1 4 0v3a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15",key:"g6ys72"}]]),n=(0,c.Z)("FileCheck",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m9 15 2 2 4-4",key:"1grp1n"}]]);var o=r(69669),d=r(88378),p=r(46226);let u=[{href:"#",icon:s.jsx(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:s.jsx(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/project/current",icon:s.jsx(i,{className:"h-5 w-5"}),label:"Current Projects"},{href:"/freelancer/project/applied",icon:s.jsx(l,{className:"h-5 w-5"}),label:"Under Verification"},{href:"/freelancer/project/completed",icon:s.jsx(n,{className:"h-5 w-5"}),label:"Completed Projects"},{href:"/freelancer/project/rejected",icon:s.jsx(o.Z,{className:"h-5 w-5"}),label:"Rejected Projects"}],x=[{href:"/freelancer/settings/personal-info",icon:s.jsx(d.Z,{className:"h-5 w-5"}),label:"Settings"}]},58285:(e,t,r)=>{"use strict";var s,a,c,i,l,n,o,d,p,u,x;r.d(t,{Dy:()=>u,Dz:()=>h});let h={BATCH:3};(function(e){e.PROJECT_HIRING="PROJECT_HIRING",e.SKILL_INTERVIEW="SKILL_INTERVIEW",e.DOMAIN_INTERVIEW="DOMAIN_INTERVIEW",e.TALENT_INTERVIEW="TALENT_INTERVIEW"})(s||(s={})),function(e){e.ADDED="Added",e.APPROVED="Approved",e.CLOSED="Closed",e.COMPLETED="Completed"}(a||(a={})),function(e){e.ACTIVE="Active",e.IN_ACTIVE="Inactive",e.NOT_VERIFIED="Not Verified"}(c||(c={})),function(e){e.BUSINESS="Business",e.FREELANCER="Freelancer",e.BOTH="Both"}(i||(i={})),function(e){e.ACTIVE="Active",e.IN_ACTIVE="Inactive"}(l||(l={})),function(e){e.APPLIED="APPLIED",e.NOT_APPLIED="NOT_APPLIED",e.APPROVED="APPROVED",e.FAILED="FAILED",e.STOPPED="STOPPED",e.REAPPLIED="REAPPLIED"}(n||(n={})),function(e){e.PENDING="Pending",e.ACCEPTED="Accepted",e.REJECTED="Rejected",e.PANEL="Panel",e.INTERVIEW="Interview"}(o||(o={})),function(e){e.ACTIVE="ACTIVE",e.INACTIVE="INACTIVE",e.ARCHIVED="ARCHIVED"}(d||(d={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.INACTIVE="Inactive",e.CLOSED="Closed"}(p||(p={})),function(e){e.FREELANCER="FREELANCER",e.ADMIN="ADMIN",e.BUSINESS="BUSINESS"}(u||(u={})),function(e){e.CREATED="Created",e.CLOSED="Closed",e.ACTIVE="Active"}(x||(x={}))},39958:(e,t,r)=>{"use strict";var s,a,c;r.d(t,{cd:()=>s,d8:()=>i,kJ:()=>a,sB:()=>c}),function(e){e.Mastery="Mastery",e.Proficient="Proficient",e.Beginner="Beginner"}(s||(s={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.REJECTED="Rejected",e.COMPLETED="Completed"}(a||(a={})),function(e){e.ACTIVE="ACTIVE",e.PENDING="PENDING",e.REJECTED="REJECTED",e.COMPLETED="COMPLETED"}(c||(c={}));let i={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},78761:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>c,default:()=>l});var s=r(68570);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\project\completed\page.tsx`),{__esModule:c,$$typeof:i}=a;a.default;let l=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\project\completed\page.tsx#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588],()=>r(8742));module.exports=s})();