"use strict";(()=>{var e={};e.id=3610,e.ids=[3610],e.modules={47849:e=>{e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71396:e=>{e.exports=require("undici")},83122:e=>{e.exports=require("undici")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},85158:e=>{e.exports=require("http2")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},77282:e=>{e.exports=require("process")},12781:e=>{e.exports=require("stream")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},4061:(e,s,r)=>{r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>o}),r(96867),r(54302),r(12523);var a=r(23191),t=r(88716),i=r(37922),n=r.n(i),l=r(95231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(s,c);let o=["",{children:["business",{children:["market",{children:["accepted",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,96867)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\market\\accepted\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\market\\accepted\\page.tsx"],p="/business/market/accepted/page",x={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/business/market/accepted/page",pathname:"/business/market/accepted",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},96867:(e,s,r)=>{r.r(s),r.d(s,{default:()=>v});var a=r(19510);r(71159);var t=r(72301);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,t.Z)("CircleCheckBig",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var n=r(69013),l=r(90071);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,t.Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),o=(0,t.Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var d=r(80086),p=r(24767),x=r(93804),m=r(27039),h=r(55116),u=r(59558);let g=[{id:1,name:"James Wilson",avatar:"/avatars/james.jpg",role:"Senior Frontend Developer",experience:"8 years",skills:["React","Vue","Angular","TypeScript"],location:"Austin, TX",phone:"+****************",availableFrom:"March 15, 2025",acceptedDate:"Feb 20, 2025"},{id:2,name:"Olivia Parker",avatar:"/avatars/olivia.jpg",role:"Lead UI Designer",experience:"6 years",skills:["UI/UX","Design Systems","Figma","User Research"],location:"Toronto, Canada",phone:"+****************",availableFrom:"March 1, 2025",acceptedDate:"Feb 22, 2025"},{id:3,name:"Daniel Kim",avatar:"/avatars/daniel.jpg",role:"Backend Developer",experience:"5 years",skills:["Node.js","Python","AWS","Microservices"],location:"Seattle, WA",phone:"+****************",availableFrom:"March 10, 2025",acceptedDate:"Feb 21, 2025"}],v=()=>(0,a.jsxs)(p.ZP,{activeTab:"accepted",children:[(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[a.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Accepted Talents"}),(0,a.jsxs)("span",{className:"text-muted-foreground",children:["Showing ",g.length," results"]})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:g.map(e=>(0,a.jsxs)(x.Zb,{className:"overflow-hidden",children:[a.jsx(x.Ol,{className:"pb-2",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,a.jsxs)(h.qE,{className:"h-12 w-12",children:[a.jsx(h.F$,{src:e.avatar,alt:e.name}),a.jsx(h.Q5,{children:e.name.slice(0,2).toUpperCase()})]}),(0,a.jsxs)("div",{children:[a.jsx(x.ll,{children:e.name}),a.jsx(x.SZ,{children:e.role})]})]}),(0,a.jsxs)(u.C,{className:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100 flex items-center gap-1",children:[a.jsx(i,{className:"h-3 w-3"}),"Accepted"]})]})}),a.jsx(x.aY,{className:"pb-2",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[a.jsx(n.Z,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("span",{children:[e.experience," experience"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[a.jsx(l.Z,{className:"h-4 w-4 text-muted-foreground"}),a.jsx("span",{children:e.location})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[a.jsx(c,{className:"h-4 w-4 text-muted-foreground"}),a.jsx("span",{children:e.phone})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[a.jsx(o,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("span",{children:["Available from ",e.availableFrom]})]}),a.jsx("div",{className:"flex flex-wrap gap-2 pt-2",children:e.skills.map(e=>a.jsx(u.C,{variant:"secondary",children:e},e))})]})}),(0,a.jsxs)(x.eW,{className:"flex justify-between pt-2",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Accepted on ",e.acceptedDate]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[a.jsx(m.z,{size:"sm",variant:"outline",children:"Send Message"}),(0,a.jsxs)(m.z,{size:"sm",className:"flex items-center gap-1",children:[a.jsx(d.Z,{className:"h-3 w-3"}),"Contact"]})]})]})]},e.id))})]})}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[8948,4198,6034,4718,6226,495,8344,5165,4736,6499,1889],()=>r(4061));module.exports=a})();