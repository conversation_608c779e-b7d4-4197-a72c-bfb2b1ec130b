(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3977],{21096:function(e,t,s){Promise.resolve().then(s.bind(s,19381))},6540:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(33480).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},25912:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},40036:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(33480).Z)("ImagePlus",[["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7",key:"31hg93"}],["line",{x1:"16",x2:"22",y1:"5",y2:"5",key:"ez7e4s"}],["line",{x1:"19",x2:"19",y1:"2",y2:"8",key:"1gkr8c"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},67524:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(33480).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},19381:function(e,t,s){"use strict";s.r(t);var a=s(57437),r=s(2265),n=s(27720),l=s(48185),o=s(79055),i=s(64797),c=s(9753),d=s(52922),m=s(15699),h=s(27756),x=s(89859);let u=[{name:"JavaScript"},{name:"React"},{name:"Node.js"}],f=[{name:"Web Development"},{name:"Frontend"}],p=[{company:"Tech Solutions",jobTitle:"Senior Developer",workDescription:"Developed and maintained web applications using modern technologies.",workFrom:"2020-01-01",workTo:"2024-01-01",referencePersonName:"Jane Smith",referencePersonContact:"+**********",githubRepoLink:"https://github.com/username/repo",location:"India",status:"Verified"},{company:"Innovatech",jobTitle:"Software Engineer",workDescription:"Worked on various innovative projects and improved software performance.",workFrom:"2018-01-01",workTo:"2019-12-31",referencePersonName:"Mike Johnson",referencePersonContact:"+1234509876",githubRepoLink:"https://github.com/username/innovatech-repo",location:"India",status:"Pending"}],g=[{projectName:"Awesome Project",description:"A cutting-edge project involving the latest technologies.",githubRepoLink:"https://github.com/username/project-repo",startDate:"2023-01-01",endDate:"2023-12-31",reference:"Project reference details",technologiesUsed:"React, Node.js, Express",role:"Lead Developer",projectType:"Web Application",status:"Verified"},{projectName:"Another Cool Project",description:"An innovative project solving real-world problems.",githubRepoLink:"https://github.com/username/cool-project-repo",startDate:"2022-01-01",endDate:"2022-12-31",reference:"Cool project reference details",technologiesUsed:"Angular, Node.js, MongoDB",role:"Full Stack Developer",projectType:"Mobile Application",status:"Pending"}],j=[{degree:"Bachelor of Technology",universityName:"KJ Somaiya Institute of Technology",fieldOfStudy:"Information Technology",startDate:"2018-08-01",endDate:"2022-06-30",grade:"9.86 CGPA",status:"Pending"},{degree:"Master of Technology",universityName:"ABC University",fieldOfStudy:"Computer Science",startDate:"2023-08-01",endDate:"2025-06-30",grade:"9.95 CGPA",status:"Verified"}],N=e=>{let{title:t,children:s}=e;return(0,a.jsxs)(l.Zb,{className:"p-8 bg-black rounded-lg shadow-md",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:t}),(0,a.jsx)("div",{className:"space-y-6",children:s})]})},b=e=>{let{title:t,data:s}=e;return(0,a.jsx)("div",{children:(0,a.jsxs)("p",{children:[(0,a.jsxs)("strong",{className:"font-semibold",children:[t,":"]})," ",(0,a.jsx)("span",{className:"text-muted-foreground",children:s})]})})};t.default=()=>(0,a.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,a.jsx)(i.Z,{menuItemsTop:h.y,menuItemsBottom:h.$,active:"Personal Info"}),(0,a.jsxs)("div",{className:"flex flex-col mb-8 sm:gap-8 sm:py-0 sm:pl-14",children:[(0,a.jsxs)("header",{className:"sticky top-0 z-30 flex h-14 items-center gap-4 border-b bg-background px-4 sm:border-0  sm:px-6",children:[(0,a.jsx)(m.Z,{menuItemsTop:h.y,menuItemsBottom:h.$,active:"Personal Info"}),(0,a.jsx)(d.Z,{items:[{label:"Freelancer",link:"#"},{label:"Freelancer Info",link:"#"}]}),(0,a.jsx)("div",{className:"relative ml-auto flex-1 md:grow-0",children:(0,a.jsx)(n.o,{className:"w-full md:w-[200px] lg:w-[336px]"})}),(0,a.jsx)(c.Z,{})]}),(0,a.jsxs)("main",{className:"flex flex-1 flex-col md:flex-row items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8",children:[(0,a.jsxs)("div",{className:"flex-1 grid gap-4 md:grid-cols-1",children:[(0,a.jsx)(N,{title:"Profile Details",children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsx)(b,{title:"First Name",data:"John"}),(0,a.jsx)(b,{title:"Last Name",data:"Doe"}),(0,a.jsx)(b,{title:"Username",data:"johndoe"}),(0,a.jsx)(b,{title:"Email",data:"<EMAIL>"}),(0,a.jsx)(b,{title:"Phone",data:"+1234567890"}),(0,a.jsx)(b,{title:"Role",data:"Developer"})]})}),(0,a.jsx)(N,{title:"Professional Information",children:p.map((e,t)=>(0,a.jsxs)(r.Fragment,{children:[t>0&&(0,a.jsx)("hr",{className:"my-4 border-t border-gray-600"}),(0,a.jsxs)("div",{className:"relative space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-lg font-bold",children:e.jobTitle}),(0,a.jsx)(o.C,{className:(0,x.S)(e.status),children:e.status})]}),(0,a.jsxs)("p",{className:"font-semibold",children:[e.company," \xb7 Full-time"]}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:[new Date(e.workFrom).toLocaleDateString()," -"," ",new Date(e.workTo).toLocaleDateString()," \xb7"," ",(new Date(e.workTo).getFullYear()-new Date(e.workFrom).getFullYear())*12+(new Date(e.workTo).getMonth()-new Date(e.workFrom).getMonth())," ","mos"]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:e.location}),(0,a.jsx)("p",{className:"text-muted-foreground",children:e.workDescription}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:[(0,a.jsx)("strong",{children:"Reference:"})," ",e.referencePersonName,","," ",e.referencePersonContact]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:(0,a.jsx)("a",{href:e.githubRepoLink,className:"text-blue-400",children:e.githubRepoLink})})]})]},t))}),(0,a.jsx)(N,{title:"Project Information",children:g.map((e,t)=>(0,a.jsxs)(r.Fragment,{children:[t>0&&(0,a.jsx)("hr",{className:"my-4 border-t border-gray-600"}),(0,a.jsxs)("div",{className:"relative space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-lg font-bold",children:e.projectName}),(0,a.jsx)(o.C,{className:(0,x.S)(e.status),children:e.status})]}),(0,a.jsx)("p",{className:"font-semibold",children:e.projectType}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:[e.startDate," - ",e.endDate]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:e.description}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:[(0,a.jsx)("strong",{children:"Reference:"})," ",e.reference]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:(0,a.jsx)("a",{href:e.githubRepoLink,className:"text-blue-400",children:e.githubRepoLink})}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Technologies Used"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:e.technologiesUsed.split(", ").map((e,t)=>(0,a.jsx)(o.C,{className:"bg-gray-700 text-muted-foreground",children:e},t))})]})]})]},t))})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-4 w-full md:w-1/3",children:[(0,a.jsx)(N,{title:"Education Information",children:j.map((e,t)=>(0,a.jsxs)(r.Fragment,{children:[t>0&&(0,a.jsx)("hr",{className:"my-4 border-t border-gray-600"}),(0,a.jsxs)("div",{className:"relative space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-lg font-bold",children:e.universityName}),(0,a.jsx)(o.C,{className:(0,x.S)(e.status),children:e.status})]}),(0,a.jsx)("p",{className:"font-semibold",children:e.degree}),(0,a.jsx)("p",{className:"text-muted-foreground",children:e.fieldOfStudy}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:[e.startDate," - ",e.endDate]}),(0,a.jsxs)("p",{className:"text-white",children:[(0,a.jsx)("strong",{children:"Grade:"})," ",e.grade]})]})]},t))}),(0,a.jsx)(N,{title:"Skills and Domains",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Skills"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:u.map((e,t)=>(0,a.jsx)(o.C,{className:"bg-gray-700 text-muted-foreground",children:e.name},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Domains"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:f.map((e,t)=>(0,a.jsx)(o.C,{className:"bg-gray-700 text-muted-foreground",children:e.name},t))})]})]})})]})]})]})]})},27720:function(e,t,s){"use strict";s.d(t,{o:function(){return l}});var a=s(57437),r=s(54817),n=s(77209);function l(e){let{placeholder:t="Search...",className:s=""}=e;return(0,a.jsxs)("div",{className:"relative ".concat(s),children:[(0,a.jsx)(r.Z,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(n.I,{type:"search",placeholder:t,className:"w-full rounded-lg bg-background pl-8 md:w-[200px] lg:w-[336px]"})]})}},27756:function(e,t,s){"use strict";s.d(t,{$:function(){return h},y:function(){return m}});var a=s(57437),r=s(11005),n=s(52022),l=s(25912),o=s(67524),i=s(6540),c=s(40036),d=s(66648);let m=[{href:"#",icon:(0,a.jsx)(d.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,a.jsx)(r.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/settings/personal-info",icon:(0,a.jsx)(n.Z,{className:"h-5 w-5"}),label:"Personal Info"},{href:"/freelancer/settings/professional-info",icon:(0,a.jsx)(l.Z,{className:"h-5 w-5"}),label:"Professional Info"},{href:"/freelancer/settings/projects",icon:(0,a.jsx)(o.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"/freelancer/settings/education-info",icon:(0,a.jsx)(i.Z,{className:"h-5 w-5"}),label:"Education"},{href:"/freelancer/settings/resume",icon:(0,a.jsx)(c.Z,{className:"h-5 w-5"}),label:"Portfolio"}],h=[]},89859:function(e,t,s){"use strict";s.d(t,{S:function(){return a}});let a=e=>{switch(null==e?void 0:e.toLowerCase()){case"active":case"verified":case"added":return"bg-green-500 text-white";case"pending":return"bg-yellow-500 text-black";case"approved":return"bg-green-500 text-black";case"rejected":return"bg-red-500 text-black";default:return"bg-gray-500 text-white"}}}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,2455,9726,2971,7023,1744],function(){return e(e.s=21096)}),_N_E=e.O()}]);