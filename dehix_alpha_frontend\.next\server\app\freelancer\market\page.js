(()=>{var e={};e.id=8867,e.ids=[8867],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},26838:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>d}),a(96885),a(54302),a(12523);var t=a(23191),r=a(88716),l=a(37922),i=a.n(l),c=a(95231),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);a.d(s,n);let d=["",{children:["freelancer",{children:["market",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,96885)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\market\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\market\\page.tsx"],m="/freelancer/market/page",x={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/freelancer/market/page",pathname:"/freelancer/market",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},51934:(e,s,a)=>{Promise.resolve().then(a.bind(a,91645))},47546:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},38787:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},37358:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41291:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},48998:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},92498:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},7027:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},91216:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},12714:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},43727:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},5932:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},31215:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},60763:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},24061:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},91645:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>G});var t=a(10326),r=a(17577),l=a(25842),i=a(77506),c=a(94019),n=a(68005),d=a(42196),o=a(92166),m=a(48586),x=a(91664),h=a(6260),p=a(3236),u=a(56627),j=a(40588);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let f=(0,a(80851).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var y=a(91216),g=a(12714),b=a(90434),v=a(29752),N=a(38443),k=a(98181),w=a(38787),Z=a(5932),M=a(47546),C=a(37358),q=a(48998),S=a(92498),_=a(24061),z=a(71821),D=a(30361),P=a(76066),A=a(78062),E=a(47548);function V({project:e,text:s,icon:a,isSizeSmall:l}){let[i,c]=(0,r.useState)(!1),n=e=>(0,k.WU)(new Date(e),"MMM dd, yyyy");return(0,t.jsxs)(P.yo,{open:i,onOpenChange:c,children:[t.jsx(P.aM,{asChild:!0,children:(0,t.jsxs)(x.z,{variant:"outline",size:l?"sm":void 0,className:l?"":"w-full",children:[a,s]})}),(0,t.jsxs)(P.ue,{className:"w-full sm:max-w-md md:max-w-lg overflow-y-auto no-scrollbar",side:"left",children:[(0,t.jsxs)(P.Tu,{className:"pb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between mt-2 items-center",children:[t.jsx(P.bC,{className:"text-2xl font-bold",children:e.projectName}),t.jsx("div",{className:"flex items-center gap-2 mt-1",children:t.jsx(N.C,{className:(e=>{switch(e){case"COMPLETED":return"bg-green-100 text-green-800 hover:bg-green-100";case"IN_PROGRESS":return"bg-blue-100 text-blue-800 hover:bg-blue-100";case"PENDING":return"bg-yellow-100 text-yellow-800 hover:bg-yellow-100";default:return"bg-gray-100 text-gray-800 hover:bg-gray-100"}})(e.status),children:e.status})})]}),t.jsx(P.Ei,{className:"mt-2 text-base",children:e.description})]}),t.jsx(A.Separator,{className:"my-4"}),(0,t.jsxs)(v.Zb,{className:"mb-4 border-0 shadow-none",children:[t.jsx(v.Ol,{className:"p-0 pb-2",children:(0,t.jsxs)(v.ll,{className:"text-lg flex items-center gap-2",children:[t.jsx(w.Z,{className:"h-4 w-4"}),"Company Information"]})}),(0,t.jsxs)(v.aY,{className:"p-0 space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx("span",{className:"font-medium",children:"Company:"}),t.jsx("span",{children:e.companyName})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx(Z.Z,{className:"h-4 w-4 text-muted-foreground"}),t.jsx("span",{children:e.email})]})]})]}),(0,t.jsxs)(v.Zb,{className:"mb-4 border-0 shadow-none",children:[t.jsx(v.Ol,{className:"p-0 pb-2",children:(0,t.jsxs)(v.ll,{className:"text-lg flex items-center gap-2",children:[t.jsx(M.Z,{className:"h-4 w-4"}),"Project Details"]})}),(0,t.jsxs)(v.aY,{className:"p-0",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx(C.Z,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsxs)("span",{className:"text-sm",children:["Created: ",n(e.createdAt)]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx(q.Z,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsxs)("span",{className:"text-sm",children:["Updated: ",n(e.updatedAt)]})]})]}),e.skillsRequired.length>0&&(0,t.jsxs)("div",{className:"mb-3",children:[(0,t.jsxs)("div",{className:"font-medium mb-1 flex items-center gap-2",children:[t.jsx(S.Z,{className:"h-4 w-4"}),"Skills Required:"]}),t.jsx("div",{className:"flex flex-wrap gap-1.5",children:e.skillsRequired.map((e,s)=>t.jsx(N.C,{variant:"secondary",className:"font-normal",children:e},s))})]})]})]}),e.profiles.length>0&&(0,t.jsxs)(v.Zb,{className:"mb-4 border-0 shadow-none",children:[t.jsx(v.Ol,{className:"p-0 pb-2",children:(0,t.jsxs)(v.ll,{className:"text-lg flex items-center gap-2",children:[t.jsx(_.Z,{className:"h-4 w-4"}),"Profile Requirements (",e.profiles.length,")"]})}),t.jsx(v.aY,{className:"p-0",children:(0,t.jsxs)(E.lr,{className:"w-full mt-2",children:[t.jsx(E.KI,{children:e.profiles.map((e,s)=>t.jsx(E.d$,{className:"md:basis-full",children:(0,t.jsxs)("div",{className:"border rounded-lg p-3",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[t.jsx("h4",{className:"font-medium",children:e.domain}),(0,t.jsxs)(N.C,{variant:"outline",className:"font-normal",children:[e.freelancersRequired," freelancers"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2 mb-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[t.jsx(q.Z,{className:"h-3.5 w-3.5 text-muted-foreground"}),(0,t.jsxs)("span",{children:[e.experience,"+ years exp."]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[t.jsx(z.Z,{className:"h-3.5 w-3.5 text-muted-foreground"}),(0,t.jsxs)("span",{children:["$",e.rate,"/hr"]})]})]}),t.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:e.description}),(0,t.jsxs)("div",{className:"flex items-center gap-1 text-sm",children:[t.jsx(D.Z,{className:"h-3.5 w-3.5 text-green-600"}),(0,t.jsxs)("span",{children:[e.totalBid.length," bids received"]})]})]})},s))}),(0,t.jsxs)("div",{className:"flex items-center justify-end mt-6",children:[t.jsx(E.am,{className:"relative right-0 translate-x-0 mr-2"}),t.jsx(E.Pz,{className:"relative right-0 translate-x-0"})]})]})})]}),t.jsx("div",{className:"mt-6 flex justify-end",children:t.jsx(x.z,{variant:"outline",onClick:()=>c(!1),children:"Close"})})]})]})}var I=a(60502);let R=()=>t.jsx("div",{className:"w-5 h-5 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"}),O=({job:e,onApply:s,onNotInterested:a,bidExist:i})=>{let[c,n]=(0,r.useState)(!1),[d,o]=(0,r.useState)(!1),m=(0,l.I0)(),p=(0,l.v9)(e=>e.projectDraft.draftedProjects),u=p?.includes(e._id),j=async()=>{o(!0);try{let s=await h.b.put("/freelancer/draft",{project_id:e._id});200===s.status&&m((0,I.kd)(e._id))}catch(e){console.error("Failed to add project to draft:",e)}finally{o(!1)}},k=async()=>{o(!0);try{let s=await h.b.delete("/freelancer/draft",{data:{project_id:e._id}});200===s.status&&m((0,I.MR)(e._id))}catch(e){console.error("Failed to remove project from draft:",e)}finally{o(!1)}},w=e.profiles&&e.profiles.length>0?e.profiles[0]:null;return(0,t.jsxs)(v.Zb,{className:"w-[97%]",children:[t.jsx(v.Ol,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(v.ll,{className:"text-xl",children:[e.projectName," "]}),(0,t.jsxs)(v.SZ,{className:"mt-1",children:["Position: ",e.position||"Web developer"," \xb7 Exp:"," ",w?.years||"2"," yrs"]})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center gap-3",children:[e.status&&t.jsx(N.C,{variant:"outline",className:"pending"===e.status.toLowerCase()?"bg-amber-300/10 text-amber-500 border-amber-500/20":"bg-green-500/10 text-green-500 border-green-500/20",children:e.status}),d?t.jsx(R,{}):t.jsx(f,{className:`w-5 h-5 cursor-pointer ${u?"fill-red-600 text-red-600":"hover:fill-red-700 hover:text-red-700"}`,onClick:d?void 0:u?k:j})]})]})}),(0,t.jsxs)(v.aY,{children:[t.jsx("p",{className:`text-sm text-gray-500 ${!c&&"line-clamp-3"}`,children:e.description}),e.description&&e.description.length>150&&t.jsx("button",{onClick:()=>n(!c),className:"text-primary text-sm mt-1 hover:underline",children:c?"less":"more"}),(0,t.jsxs)("div",{className:"mt-4",children:[t.jsx("h4",{className:"text-sm font-medium mb-2",children:"Skills required"}),t.jsx("div",{className:"flex flex-wrap gap-2",children:e.skillsRequired&&e.skillsRequired.map((e,s)=>t.jsx(N.C,{variant:"secondary",className:"rounded-md",children:e},s))})]}),w&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[w.positions&&(0,t.jsxs)("span",{className:"bg-primary/10 text-primary px-2 py-1 rounded text-xs",children:[w.positions," Positions"]}),w.years&&(0,t.jsxs)("span",{className:"bg-primary/10 text-primary px-2 py-1 rounded text-xs",children:[w.years," Years"]})]}),w.connectsRequired&&(0,t.jsxs)("div",{className:"mt-2 text-sm",children:["Connects required:"," ",t.jsx("span",{className:"font-medium",children:w.connectsRequired})]})]})]}),(0,t.jsxs)(v.eW,{className:"flex justify-end gap-2",children:[(0,t.jsxs)(x.z,{variant:"outline",size:"sm",onClick:a,className:"text-gray-500",children:[t.jsx(y.Z,{className:"h-4 w-4 mr-1"}),"Not Interested"]}),t.jsx(V,{icon:t.jsx(g.Z,{className:"h-4 w-4 mr-1"}),project:e,text:"View",isSizeSmall:!0}),t.jsx(b.default,{href:`/freelancer/market/project/${e._id}/apply`,children:t.jsx(x.z,{type:"submit",className:"",size:"sm",disabled:i,children:i?"Applied":"Bid"})})]})]})};var F=a(41291),L=a(31215),$=a(7027),T=a(38227);function B({open:e,setOpen:s}){let[a,l]=(0,r.useState)([]),[i,c]=(0,r.useState)(!1),n=e=>{switch(e){case"COMPLETED":return(0,t.jsxs)(N.C,{variant:"outline",className:"flex bg-green-900 text-green-500 items-center gap-1",children:[t.jsx(D.Z,{className:"h-3 w-3"}),"Completed"]});case"PENDING":return(0,t.jsxs)(N.C,{variant:"outline",className:"flex items-center gap-1 bg-amber-100 text-amber-700 dark:bg-amber-900 dark:text-amber-300",children:[t.jsx(q.Z,{className:"h-3 w-3"}),"Pending"]});default:return(0,t.jsxs)(N.C,{variant:"secondary",className:"flex items-center gap-1",children:[t.jsx(F.Z,{className:"h-3 w-3"}),e]})}};return(0,t.jsxs)(P.yo,{open:e,onOpenChange:s,children:[t.jsx(P.aM,{asChild:!0,children:(0,t.jsxs)(x.z,{variant:"default",children:[t.jsx(L.Z,{className:"mr-2 h-4 w-4"})," Drafts"]})}),(0,t.jsxs)(P.ue,{className:"w-[90vw] sm:max-w-lg p-0",children:[(0,t.jsxs)(P.Tu,{className:"p-6 pb-2",children:[t.jsx(P.bC,{children:"Draft Project Details"}),t.jsx(P.Ei,{children:"These are your saved drafts."})]}),t.jsx(p.x,{className:"h-[calc(100vh-10rem)]",children:i?t.jsx("div",{className:"p-6 space-y-4",children:[1,2].map(e=>(0,t.jsxs)("div",{className:"space-y-3",children:[t.jsx(T.O,{className:"h-5 w-2/3"}),t.jsx(T.O,{className:"h-4 w-full"}),t.jsx(T.O,{className:"h-20 w-full"})]},e))}):t.jsx("div",{className:"p-6 pt-2 space-y-6",children:0===a.length?(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-10 text-center",children:[t.jsx(L.Z,{className:"h-12 w-12 text-muted-foreground mb-4"}),t.jsx("p",{className:"text-lg font-medium",children:"No drafts found"}),t.jsx("p",{className:"text-sm text-muted-foreground",children:"Your saved project drafts will appear here"})]}):a.map(e=>(0,t.jsxs)(v.Zb,{className:"overflow-hidden border shadow-sm",children:[(0,t.jsxs)(v.Ol,{className:"pb-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[t.jsx(v.ll,{className:"text-xl",children:e.projectName}),n(e.status)]}),t.jsx(v.SZ,{className:"line-clamp-2",children:e.description})]}),(0,t.jsxs)(v.aY,{className:"pb-2 space-y-3",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-x-4 gap-y-1 text-sm",children:[(0,t.jsxs)("div",{className:"flex flex-col",children:[t.jsx("span",{className:"text-muted-foreground",children:"Company"}),t.jsx("span",{className:"font-medium",children:e.companyName})]}),(0,t.jsxs)("div",{className:"flex flex-col",children:[t.jsx("span",{className:"text-muted-foreground",children:"Email"}),t.jsx("span",{className:"font-medium truncate",children:e.email})]})]}),e.skillsRequired&&e.skillsRequired.length>0&&t.jsx("div",{className:"flex flex-wrap gap-1 mt-2",children:e.skillsRequired.map((e,s)=>e&&t.jsx(N.C,{variant:"outline",children:e},s))}),e.profiles.length>0&&(0,t.jsxs)("div",{className:"mt-3",children:[(0,t.jsxs)("h4",{className:"text-sm font-semibold mb-2",children:["Profiles (",e.profiles.length,")"]}),(0,t.jsxs)("div",{className:"space-y-3",children:[e.profiles.slice(0,2).map(e=>(0,t.jsxs)("div",{className:"bg-muted/40 rounded-lg p-3 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-1",children:[t.jsx("span",{className:"font-medium",children:e.domain||"No Domain"}),(0,t.jsxs)(N.C,{variant:"secondary",className:"text-xs",children:["$",e.rate,"/hr"]})]}),t.jsx("p",{className:"text-xs text-muted-foreground line-clamp-2 mb-2",children:e.description}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-x-4 gap-y-1 text-xs",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("span",{className:"text-muted-foreground",children:["Required:"," "]}),t.jsx("span",{children:e.freelancersRequired})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("span",{className:"text-muted-foreground",children:["Experience:"," "]}),(0,t.jsxs)("span",{children:[e.experience," years"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("span",{className:"text-muted-foreground",children:["Bids:"," "]}),t.jsx("span",{children:e.totalBid?.length||0})]})]})]},e._id)),e.profiles.length>2&&(0,t.jsxs)(x.z,{variant:"ghost",size:"sm",className:"w-full text-xs",children:["Show ",e.profiles.length-2," more profiles"]})]})]})]}),t.jsx(v.eW,{className:"pt-2",children:t.jsx(V,{isSizeSmall:!1,icon:t.jsx($.Z,{className:"h-3 w-3 mr-1"}),project:e,text:"View Project Details"})})]},e._id))})}),(0,t.jsxs)("div",{className:"p-6 pt-0 mb-3",children:[t.jsx(A.Separator,{className:"my-2"}),t.jsx(P.FF,{children:t.jsx(P.sw,{asChild:!0,children:t.jsx(x.z,{type:"button",children:"Close"})})})]})]})]})}let G=()=>{let e=(0,l.v9)(e=>e.user),s=(0,l.I0)(),[a,f]=(0,r.useState)(!1),[y,g]=(0,r.useState)(!1),[b,v]=(0,r.useState)("Filter by Project Domains"),[N,k]=(0,r.useState)(!1),[w,Z]=(0,r.useState)({jobType:[],domain:[],skills:[],projects:[],projectDomain:[]}),[M,C]=(0,r.useState)([]),[q,S]=(0,r.useState)([]),[_,z]=(0,r.useState)([]),[D,P]=(0,r.useState)([]),[A,E]=(0,r.useState)([]),[V,R]=(0,r.useState)(!1);(0,r.useEffect)(()=>{f(!0)},[]);let F=(0,r.useCallback)(async()=>{try{let s=(await h.b.get(`/bid/${e.uid}/bid`)).data.data.map(e=>e.profile_id);E(s)}catch(e){console.error("API Error:",e),(0,u.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}},[e.uid]);(0,r.useEffect)(()=>{F()},[F]),(0,r.useEffect)(()=>{(async()=>{try{let e=await h.b.get("/freelancer/draft");s((0,I.Dj)(e.data.projectDraft))}catch(e){console.error(e)}})()},[s]),(0,r.useEffect)(()=>{(async()=>{try{R(!0);let e=await h.b.get("/skills");S(e.data.data.map(e=>e.label));let s=await h.b.get("/domain");z(s.data.data.map(e=>e.label));let a=await h.b.get("/projectdomain");P(a.data.data.map(e=>e.label))}catch(e){console.error("Error loading filters",e),(0,u.Am)({variant:"destructive",title:"Error",description:"Failed to load filter options."})}finally{R(!1)}})()},[]);let L=(e,s)=>{Z(a=>({...a,[e]:s}))},$=()=>{Z({jobType:[],domain:[],skills:[],projects:[],projectDomain:[]})},T=e=>Object.entries(e).filter(([e,s])=>s.length>0).map(([e,s])=>`${e}=${s.join(",")}`).join("&"),G=(0,r.useCallback)(async s=>{try{R(!0);let a=(await h.b.get("/freelancer")).data,t=T(s),r=(await h.b.get(`/project/freelancer/${e.uid}?${t}`)).data.data||[],l=a.notInterestedProject||[],i=r.filter(e=>!l.includes(e._id));C(i),console.log(i)}catch(e){console.error("Fetch jobs error:",e),(0,u.Am)({variant:"destructive",title:"Error",description:"Failed to load job listings."})}finally{R(!1)}},[e.uid]);(0,r.useEffect)(()=>{G(w)},[G]);let U=()=>{G(w)},H=()=>{window.innerWidth>=1024&&g(!1)};(0,r.useEffect)(()=>(window.addEventListener("resize",H),()=>window.removeEventListener("resize",H)),[]);let Y=()=>{g(e=>!e)},W=async e=>{try{await h.b.put(`/freelancer/${e}/not_interested_project`),C(s=>s.filter(s=>s._id!==e)),(0,u.Am)({title:"Success",description:"Project marked as not interested."})}catch(e){console.error("Remove job error:",e),(0,u.Am)({variant:"destructive",title:"Error",description:"Failed to update project status."})}},K=async e=>{try{await h.b.post(`/project/apply/${e}`),(0,u.Am)({title:"Success",description:"Application submitted successfully."})}catch(e){console.error("Application error:",e),(0,u.Am)({variant:"destructive",title:"Error",description:"Failed to apply to the project."})}};return a?(0,t.jsxs)("div",{className:"flex min-h-screen bg-muted  w-full flex-col  pb-10",children:[t.jsx(o.Z,{menuItemsTop:m.yn,menuItemsBottom:m.$C,active:"Market"}),(0,t.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[t.jsx(j.Z,{menuItemsTop:m.yn,menuItemsBottom:m.$C,activeMenu:"Market",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Marketplace",link:"#"}]}),(0,t.jsxs)("div",{className:"flex  items-start sm:items-center justify-between",children:[(0,t.jsxs)("div",{className:"w-full sm:w-[70%] mb-4 sm:mb-8 ml-4 sm:ml-8",children:[t.jsx("h1",{className:"text-2xl sm:text-3xl font-bold",children:"Freelancer Marketplace"}),t.jsx("p",{className:"text-gray-400 mt-2 hidden sm:block",children:"Discover and manage your freelance opportunities, connect with potential projects, and filter by skills, domains and project domains to enhance your portfolio."})]}),t.jsx("div",{className:"w-full sm:w-[30%] flex justify-end pr-4 sm:pr-8",children:t.jsx(B,{open:N,setOpen:k})})]})]}),(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row lg:space-x-6 px-4 lg:px-20 md:px-8",children:[t.jsx("div",{className:"hidden bg-background p-3 rounded-md lg:block lg:sticky lg:top-16 lg:w-1/3 xl:w-1/3 lg:self-start lg:h-[calc(100vh-4rem)]",children:(0,t.jsxs)(p.x,{className:"h-full no-scrollbar overflow-y-auto pr-4 space-y-4",children:[t.jsx(x.z,{onClick:U,className:"w-full",children:"Apply"}),t.jsx(x.z,{variant:"outline",onClick:$,className:"w-full mb-4 bg-gray dark:text-white",style:{marginTop:"1rem"},children:"Reset"}),t.jsx("div",{className:"my-4",children:t.jsx(n.Z,{heading:"Filter by Project Domains",checkboxLabels:D,selectedValues:w.projectDomain,setSelectedValues:e=>L("projectDomain",e),openItem:b,setOpenItem:v,useAccordion:!0})}),t.jsx("div",{className:"mb-4",children:t.jsx(n.Z,{heading:"Filter by Skills",checkboxLabels:q,selectedValues:w.skills,setSelectedValues:e=>L("skills",e),openItem:b,setOpenItem:v,useAccordion:!0})}),t.jsx("div",{className:"mb-4",children:t.jsx(n.Z,{heading:"Filter by Domains",checkboxLabels:_,selectedValues:w.domain,setSelectedValues:e=>L("domain",e),openItem:b,setOpenItem:v,useAccordion:!0})})]})}),V?t.jsx("div",{className:"mt-4 lg:mt-0 space-y-4 w-full flex justify-center items-center h-[60vh]",children:t.jsx(i.Z,{size:40,className:"text-primary animate-spin"})}):t.jsx("div",{className:"mt-4 lg:mt-0 w-full",children:t.jsx(p.x,{className:"h-[calc(100vh-8rem)] sm:h-[calc(100vh-4rem)] no-scrollbar overflow-y-auto",children:t.jsx("div",{className:"grid grid-cols-1 gap-6 pb-20 lg:pb-4",children:M.length>0?M.map(e=>t.jsx(O,{job:e,onApply:()=>K(e._id),onNotInterested:()=>W(e._id),bidExist:Array.isArray(e.profiles)&&e.profiles.some(e=>A.includes(e._id))},e._id)):t.jsx("div",{className:"text-center py-10",children:t.jsx("p",{className:"text-gray-400",children:"No projects found matching your filters."})})})})})]}),a&&y&&t.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4 overflow-hidden",children:(0,t.jsxs)("div",{className:"bg-secondary rounded-lg w-full max-w-screen-lg mx-auto h-[80vh] max-h-full flex flex-col",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center p-4 border-b border-gray-300",children:[t.jsx("h2",{className:"text-xl font-semibold",children:" Filters"}),t.jsx(x.z,{variant:"ghost",size:"sm",onClick:Y,children:t.jsx(c.Z,{className:"h-5 w-5"})})]}),(0,t.jsxs)("div",{className:"overflow-y-auto p-4 flex-grow",children:[t.jsx("div",{className:"border-b border-gray-300 pb-4",children:t.jsx(d.Z,{label:"Domains",heading:"Filter by domain",checkboxLabels:_,selectedValues:w.domain,setSelectedValues:e=>L("domain",e)})}),t.jsx("div",{className:"border-b border-gray-300 py-4",children:t.jsx(d.Z,{label:"Skills",heading:"Filter by skills",checkboxLabels:q,selectedValues:w.skills,setSelectedValues:e=>L("skills",e)})}),t.jsx("div",{className:"pt-4",children:t.jsx(d.Z,{label:"ProjectDomain",heading:"Filter by project-domain",checkboxLabels:D,selectedValues:w.projectDomain,setSelectedValues:e=>L("projectDomain",e)})})]}),t.jsx("div",{className:"p-4 border-t border-gray-300",children:(0,t.jsxs)("div",{className:"flex gap-3",children:[t.jsx(x.z,{onClick:U,className:"flex-1",children:"Apply"}),t.jsx(x.z,{variant:"outline",onClick:$,className:"flex-1",children:"Reset"})]})})]})}),a&&t.jsx("div",{className:"fixed bottom-0 left-0 right-0 lg:hidden p-4 flex justify-center z-40",children:t.jsx("button",{className:"w-full max-w-xs p-3 bg-primary text-white dark:text-black rounded-md hover:bg-primary/90 transition-colors duration-300 ease-in-out shadow-lg font-medium",onClick:Y,children:y?"Hide Filters":"Show Filters"})})]}):null}},78062:(e,s,a)=>{"use strict";a.r(s),a.d(s,{Separator:()=>c});var t=a(10326),r=a(17577),l=a(90220),i=a(51223);let c=r.forwardRef(({className:e,orientation:s="horizontal",decorative:a=!0,...r},c)=>t.jsx(l.f,{ref:c,decorative:a,orientation:s,className:(0,i.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));c.displayName=l.f.displayName},38227:(e,s,a)=>{"use strict";a.d(s,{O:()=>l});var t=a(10326),r=a(51223);function l({className:e,...s}){return t.jsx("div",{className:(0,r.cn)("animate-pulse rounded-md bg-primary/10",e),...s})}},48586:(e,s,a)=>{"use strict";a.d(s,{yL:()=>v,$C:()=>b,yn:()=>g});var t=a(10326),r=a(95920),l=a(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,l.Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]]),c=(0,l.Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);var n=a(43727);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,l.Z)("TabletSmartphone",[["rect",{width:"10",height:"14",x:"3",y:"8",rx:"2",key:"1vrsiq"}],["path",{d:"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4",key:"1j4zmg"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),o=(0,l.Z)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]]);var m=a(60763);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let x=(0,l.Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);var h=a(40617),p=a(69515),u=a(88378),j=a(40900),f=a(98091),y=a(46226);let g=[{href:"#",icon:t.jsx(y.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:t.jsx(r.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:t.jsx(i,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:t.jsx(c,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:t.jsx(n.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:t.jsx(d,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:t.jsx(o,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:t.jsx(m.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:t.jsx(x,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:t.jsx(h.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:t.jsx(p.Z,{className:"h-5 w-5"}),label:"Notes"}],b=[{href:"/freelancer/settings/personal-info",icon:t.jsx(u.Z,{className:"h-5 w-5"}),label:"Settings"}];y.default,r.Z,p.Z,j.Z,f.Z;let v=[{href:"#",icon:t.jsx(y.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:t.jsx(r.Z,{className:"h-5 w-5"}),label:"Home"}]},96885:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>c});var t=a(68570);let r=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\market\page.tsx`),{__esModule:l,$$typeof:i}=r;r.default;let c=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\market\page.tsx#default`)},90220:(e,s,a)=>{"use strict";a.d(s,{f:()=>d});var t=a(17577),r=a(77335),l=a(10326),i="horizontal",c=["horizontal","vertical"],n=t.forwardRef((e,s)=>{let{decorative:a,orientation:t=i,...n}=e,d=c.includes(t)?t:i;return(0,l.jsx)(r.WV.div,{"data-orientation":d,...a?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...n,ref:s})});n.displayName="Separator";var d=n}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588,4637],()=>a(26838));module.exports=t})();