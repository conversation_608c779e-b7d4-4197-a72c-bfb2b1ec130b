(()=>{var e={};e.id=1069,e.ids=[1069],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},95178:(e,s,l)=>{"use strict";l.r(s),l.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>o}),l(10718),l(54302),l(12523);var r=l(23191),t=l(88716),a=l(37922),i=l.n(a),n=l(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);l.d(s,c);let o=["",{children:["business",{children:["market",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(l.bind(l,10718)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\market\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(l.bind(l,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(l.bind(l,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(l.bind(l,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(l.bind(l,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\market\\page.tsx"],m="/business/market/page",x={require:l,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/business/market/page",pathname:"/business/market",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},17214:(e,s,l)=>{Promise.resolve().then(l.bind(l,50643))},12070:(e,s,l)=>{"use strict";l.d(s,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,l(80851).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},66307:(e,s,l)=>{"use strict";l.d(s,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,l(80851).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69669:(e,s,l)=>{"use strict";l.d(s,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,l(80851).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},57671:(e,s,l)=>{"use strict";l.d(s,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,l(80851).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},50643:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>$});var r=l(10326),t=l(17577),a=l.n(t),i=l(25842),n=l(77506),c=l(92166),o=l(46319),d=l(6260),m=l(40588);let x=()=>(0,r.jsxs)("div",{className:"flex flex-col sm:py-0 mb-8",children:[r.jsx(m.Z,{menuItemsTop:o.yn,menuItemsBottom:o.$C,activeMenu:"Dashboard",breadcrumbItems:[{label:"Business",link:"/dashboard/business"},{label:"Business Marketplace",link:"#"}]}),(0,r.jsxs)("div",{className:"mb-8 ml-6 mt-8",children:[r.jsx("h1",{className:"text-3xl font-bold",children:" Business Marketplace "}),r.jsx("p",{className:"text-gray-400 mt-2",children:"Discover a curated selection of business opportunities designed to connect freelancers with potential clients and projects."})]})]});var h=l(91664),u=l(29752),p=l(41190),b=l(44794);let f=({heading:e,setLimits:s})=>{let[l,a]=t.useState(""),[i,n]=t.useState(""),[c,o]=t.useState(""),d=(e,l)=>{let r=e?Number(e):0,t=l?Number(l):0;if(r<0||t<0){o("Experience cannot be negative."),setTimeout(()=>o(""),3e3);return}if(t>30){o("Maximum experience cannot exceed 30 years."),setTimeout(()=>o(""),3e3);return}if(r>t&&""!==l){o("Please enter the maximum experience first"),setTimeout(()=>o(""),3e3);return}o(""),a(e),n(l),s(`${e||0}-${l||0}`)};return(0,r.jsxs)(u.Zb,{className:"w-full",children:[r.jsx(u.Ol,{className:"pb-3",children:r.jsx(u.ll,{className:"text-lg",children:e})}),(0,r.jsxs)(u.aY,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"flex flex-col",children:[r.jsx(b.Label,{htmlFor:"lowerLimit",className:"text-sm",children:"Minimum"}),r.jsx(p.I,{id:"lowerLimit",type:"number",value:l,onChange:e=>d(e.target.value,i),className:"mt-1 w-full",placeholder:"0"})]}),(0,r.jsxs)("div",{className:"flex flex-col",children:[r.jsx(b.Label,{htmlFor:"higherLimit",className:"text-sm",children:"Maximum"}),r.jsx(p.I,{id:"higherLimit",type:"number",value:i,onChange:e=>d(l,e.target.value),className:"mt-1 w-full",placeholder:"30"})]})]}),c&&r.jsx("p",{className:"text-red-500 text-sm mt-2",children:c})]})]})};var j=l(68005);let g=({filters:e,domains:s,skills:l,handleFilterChange:t,handleApply:a,handleReset:i})=>{let n=()=>{a()};return r.jsx("div",{className:"hidden mb-10 lg:block lg:sticky lg:top-16 lg:w-[400px] lg:self-start lg:h-[calc(100vh-4rem)] lg:overflow-hidden lg:transition-all lg:duration-300 lg:scrollbar  no-scrollbar lg:scrollbar-thumb-gray-500 lg:scrollbar-track-gray-200 hover:lg:overflow-y-auto",children:(0,r.jsxs)("div",{className:"h-full px-4 flex flex-col space-y-4 ",children:[r.jsx(h.z,{onClick:()=>{n()},className:"w-full",children:"Apply"}),r.jsx(h.z,{onClick:i,variant:"outline",style:{marginTop:"1rem"},className:"w-full dark:text-white ",children:"Reset"}),r.jsx("div",{className:"mb-4",children:r.jsx(f,{heading:"Filter by Experience",setLimits:e=>t("experience",e)})}),r.jsx("div",{className:"mb-4",children:r.jsx(j.Z,{heading:"Filter by Domains",checkboxLabels:s,selectedValues:e.domain,setSelectedValues:e=>t("domain",e)})}),r.jsx("div",{className:"mb-12",children:r.jsx(j.Z,{heading:"Filter by Skills",checkboxLabels:l,selectedValues:e.skills,setSelectedValues:e=>t("skills",e)})})]})})};var N=l(4150),v=l(90434),y=l(38443),w=l(76066);let k=["left"],C=({name:e,skills:s,domains:l,experience:t,profile:i,userName:n,monthlyPay:c,Github:o,LinkedIn:d})=>{let[m,x]=a().useState(!1);return r.jsx("div",{className:" sm:mx-10 mb-3 max-w-3xl",children:r.jsx(u.Zb,{className:"flex justify-between mt-5  shadow-lg shadow-gray-500/20  ",children:(0,r.jsxs)("div",{className:"flex flex-col justify-between p-4",children:[r.jsx(u.Ol,{children:(0,r.jsxs)("div",{className:"flex flex-col item-center gap-4",children:[r.jsx(N.qE,{className:"rounded-full w-20 h-20 overflow-hidden border-2 border-gray-400 ",children:r.jsx(N.F$,{className:"w-full h-full object-cover",src:i,alt:"Profile Picture"})}),r.jsx("div",{className:"mt-2",children:r.jsx(u.ll,{className:"text-xl font-bold",children:e})})]})}),(0,r.jsxs)(u.aY,{children:[(0,r.jsxs)("p",{className:"text-sm",children:[r.jsx("span",{className:"font-medium text-gray-400",children:"Experience:"}),(0,r.jsxs)("span",{className:"font-bold",children:[" ",t," years"]})]}),s&&s.length>0&&(0,r.jsxs)("div",{className:"mt-2",children:[r.jsx("p",{className:"font-medium",children:"Skills:"}),r.jsx("div",{className:"flex flex-wrap gap-2",children:s?.map((e,s)=>r.jsx(y.C,{className:"bg-foreground text-background border border-white rounded-xl font-bold uppercase",children:e.name},s))})]}),l&&l.length>0&&(0,r.jsxs)("div",{className:"mt-2",children:[r.jsx("p",{className:"font-medium",children:"Domains:"}),r.jsx("div",{className:"flex flex-wrap gap-2",children:l?.map((e,s)=>r.jsx(y.C,{className:"bg-foreground text-background border border-white rounded-xl font-bold uppercase",children:e.name},s))})]}),r.jsx("div",{className:"py-4 mt-8",children:k.map(l=>(0,r.jsxs)(w.yo,{children:[r.jsx(w.aM,{asChild:!0,children:r.jsx(h.z,{className:"w-full sm:w-[350px] lg:w-[680px]",children:"View"})}),(0,r.jsxs)(w.ue,{side:l,className:"overflow-y-auto max-h-[100vh]",children:[r.jsx(w.Tu,{children:r.jsx(w.bC,{className:"text-center text-lg font-bold py-4",children:"View Talent Details"})}),(0,r.jsxs)("div",{className:"flex flex-col gap-4 items-center justify-center mt-2",children:[r.jsx(N.qE,{className:"rounded-full w-20 h-20 overflow-hidden border-2 border-gray-400 ",children:r.jsx(N.F$,{className:"w-full h-full object-cover",src:i,alt:"Profile Picture"})}),r.jsx("div",{className:"text-lg font-bold items-center justify-center mt-2",children:e}),r.jsx(u.Zb,{className:"w-full  shadow-lg shadow-gray-500/20 mt-4",children:r.jsx("table",{className:"min-w-full table-auto border-collapse ",children:(0,r.jsxs)("tbody",{children:[(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"border-b px-4 py-2 font-medium",children:"Username"}),r.jsx("td",{className:"border-b px-4 py-2",children:n||"N/A"})]}),(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"border-b px-4 py-2 font-medium",children:"Skill"}),r.jsx("td",{className:"border-b px-4 py-2",children:s?.length!==0&&s?(0,r.jsxs)(r.Fragment,{children:[s.slice(0,2).map((e,l)=>(0,r.jsxs)(y.C,{className:"bg-transparent text-foreground",children:[e.name,l<s.length-1&&","]},l)),s.length>2&&!m&&(0,r.jsxs)(y.C,{className:"bg-transparent border-none text-foreground",onClick:()=>x(!0),children:["+",s.length-2]},"extra"),m&&r.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:s.slice(2).map((e,l)=>(0,r.jsxs)(y.C,{className:"bg-transparent border-none text-foreground",children:[e.name,l<s.slice(2).length-1&&","]},l+2))})]}):"N/A"})]}),(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"border-b px-4 py-2 font-medium",children:"Experience"}),r.jsx("td",{className:"border-b px-4 py-2",children:t?`${t} Years`:"N/A"})]}),(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"border-b px-4 py-2 font-medium",children:"MonthlyPay"}),r.jsx("td",{className:"border-b px-4 py-2",children:c&&c.trim()?`${c}$`:"N/A"})]}),(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"border-b px-4 py-2 font-medium",children:"Github"}),r.jsx("td",{className:"border-b px-4 py-2",children:o&&o.trim()?r.jsx("a",{href:o,target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 hover:underline overflow-hidden whitespace-nowrap text-ellipsis max- sm:max-w-md lg:max-w-lg",title:o,children:o}):"N/A"})]}),(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"border-b px-4 py-2 font-medium",children:"LinkedIn"}),r.jsx("td",{className:"border-b px-4 py-2",children:d&&d.trim()?r.jsx("a",{href:d,target:"_blank",rel:"noopener noreferrer",className:"hover:underline overflow-hidden whitespace-nowrap text-ellipsis max-w-[120px] sm:max-w-[170px] block",title:d,children:d}):"N/A"})]})]})})}),r.jsx("div",{className:"w-full text-sm mt-6",children:r.jsx("div",{className:"w-full text-center",children:r.jsx(v.default,{href:`/business/freelancerProfile/${n}`,passHref:!0,children:r.jsx(h.z,{className:"w-full text-sm text-black rounded-md",children:"Expand"})})})})]})]})]},l))})]})]})})})},q=({freelancers:e})=>(console.log(e),r.jsx("div",{className:"mt-4 p-4  lg:ml-10 space-y-4 w-full",children:0===e.length?r.jsx("p",{className:"text-center text-xl flex justify-center items-center h-[55vh] font-semibold",children:"No talent found"}):e.map((e,s)=>r.jsx(C,{name:e.firstName+" "+e.lastName,skills:e.skills,domains:e.domains,experience:e.workExperience,profile:e.profilePic,userName:e.userName,monthlyPay:e.monthlyPay,Github:e.github,LinkedIn:e.linkedin},s))}));var Z=l(94019),P=l(42196);let _=({heading:e,setLimits:s})=>{let[l,a]=t.useState(0),[i,n]=t.useState(10);return(0,r.jsxs)("div",{className:"w-full",children:[r.jsx(u.Ol,{className:"pb-3",children:r.jsx(u.ll,{className:"text-lg",children:e})}),r.jsx(u.aY,{children:(0,r.jsxs)("div",{className:"flex space-x-4 p-2",children:[(0,r.jsxs)("div",{className:"flex flex-col",children:[r.jsx(b.Label,{htmlFor:"lowerLimit",className:"text-sm",children:"Minimum"}),r.jsx(p.I,{id:"lowerLimit",type:"number",value:l,onChange:e=>{let l=Number(e.target.value);a(l),s(`${l}-${i}`)},className:"w-20 mt-1"})]}),(0,r.jsxs)("div",{className:"flex flex-col",children:[r.jsx(b.Label,{htmlFor:"higherLimit",className:"text-sm",children:"Maximum"}),r.jsx(p.I,{id:"higherLimit",type:"number",value:i,onChange:e=>{let r=Number(e.target.value);n(r),s(`${l}-${r}`)},className:"w-20 mt-1"})]})]})})]})},S=({showFilters:e,filters:s,domains:l,skills:t,handleFilterChange:a,handleApply:i,handleModalToggle:n})=>(0,r.jsxs)(r.Fragment,{children:[e&&r.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4 overflow-hidden",children:(0,r.jsxs)("div",{className:"bg-black relative rounded-lg w-full max-w-screen-lg mx-auto h-[80vh] max-h-full flex flex-col",children:[r.jsx(Z.Z,{onClick:n,className:"absolute cursor-pointer top-1 right-6"}),(0,r.jsxs)("div",{className:"overflow-y-auto p-4 flex-grow",children:[r.jsx("div",{className:"border-b border-gray-300 pb-4 ",children:r.jsx(_,{heading:"Filter by Experience",setLimits:e=>a("experience",e)})}),r.jsx("div",{className:"border-b border-gray-300 pb-4",children:r.jsx(P.Z,{label:"Domains",heading:"Filter by domains",checkboxLabels:l,selectedValues:s.domain,setSelectedValues:e=>a("domain",e)})}),r.jsx("div",{className:"border-b border-gray-300 pb-4",children:r.jsx(P.Z,{label:"Skills",heading:"Filter by skills",checkboxLabels:t,selectedValues:s.skills,setSelectedValues:e=>a("skills",e)})})]}),r.jsx("div",{className:"p-4 border-t border-gray-300",children:r.jsx(h.z,{onClick:i,className:"w-full",children:"Apply"})})]})}),r.jsx("div",{className:"fixed bottom-0 left-0 right-0 lg:hidden p-4 flex justify-center z-50",children:r.jsx("button",{className:"w-full max-w-xs p-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors duration-300 ease-in-out",onClick:n,children:e?"Hide Filters":"Show Filters"})})]});var A=l(56627);let $=()=>{let e=(0,i.v9)(e=>e.user),[s,l]=(0,t.useState)(!1),[a,m]=(0,t.useState)([]),[h,u]=(0,t.useState)([]),[p,b]=(0,t.useState)([]),[f,j]=(0,t.useState)(!1),[N,v]=(0,t.useState)({location:[],jobType:[],experience:[],domain:[],skills:[]}),y=(e,s)=>{let l=s;l=(Array.isArray(s)?s:[s]).flatMap(e=>{if(e.includes("-")){let[s,l]=e.split("-").map(Number);return Array.from({length:l-s+1},(e,l)=>(s+l).toString())}return"7+"===e?["7","8","9","10"]:[e]}),v(s=>({...s,[e]:l}))},w=e=>{let s=[];if(Array.isArray(e.experience)&&e.experience.length>0){let l=e.experience.map(Number).sort((e,s)=>e-s),r=l[0],t=l[l.length-1];void 0!==r&&s.push(`workExperienceFrom=${r}`),void 0!==t&&s.push(`workExperienceTo=${t}`)}return Object.entries(e).forEach(([e,l])=>{if("experience"!==e){if(Array.isArray(l)&&l.length>0){let r=l.filter(e=>null!=e&&""!==e);r.length>0&&s.push(`${e}=${r.join(",")}`)}else"string"==typeof l&&s.push(`${e}=${l.split(",").map(e=>e.trim()).join(",")}`)}}),s.join("&")},k=(0,t.useCallback)(async e=>{try{j(!0);let s=w(e),l=await d.b.get(`/freelancer?${s}`);b(l.data.data)}catch(e){(0,A.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."}),console.error("API Error:",e)}finally{j(!1)}},[]);(0,t.useEffect)(()=>{(async function(){try{let e=(await d.b.get("/skills")).data.data.map(e=>e.label);m(e);let s=(await d.b.get("/domain")).data.data.map(e=>e.label);u(s)}catch(e){(0,A.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."}),console.error("Error fetching data:",e)}})()},[]),(0,t.useEffect)(()=>{k(N)},[e.uid,N,k]);let C=()=>{k(N)};return(0,r.jsxs)("section",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[r.jsx(c.Z,{menuItemsTop:o.yn,menuItemsBottom:o.$C,active:"Market"}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-4  sm:pl-14 mb-8",children:[r.jsx(x,{}),(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:space-x-5 md:-space-x-3 ml:20 sm:-space-x-4 md:ml-6 lg:ml-6",children:[r.jsx(g,{filters:N,domains:h,skills:a,handleFilterChange:y,handleApply:C,handleReset:()=>{v({location:[],jobType:[],domain:[],skills:[],experience:[]})}}),f?r.jsx("div",{className:"mt-4 lg:mt-0 lg:ml-10 space-y-4 w-full flex justify-center items-center h-[60vh]",children:r.jsx(n.Z,{size:40,className:" text-white animate-spin "})}):r.jsx(q,{freelancers:p})]})]}),r.jsx(S,{showFilters:s,filters:N,domains:h,skills:a,handleFilterChange:y,handleApply:C,handleModalToggle:()=>{l(!s)}})]})}},46319:(e,s,l)=>{"use strict";l.d(s,{$C:()=>f,Ne:()=>j,yn:()=>b});var r=l(10326),t=l(95920),a=l(57671),i=l(94909),n=l(12070),c=l(66307),o=l(69669),d=l(40617),m=l(69515),x=l(88378),h=l(40900),u=l(98091),p=l(46226);let b=[{href:"#",icon:r.jsx(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:r.jsx(t.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:r.jsx(a.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:r.jsx(i.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:r.jsx(i.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:r.jsx(n.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:r.jsx(c.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:r.jsx(o.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:r.jsx(d.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:r.jsx(m.Z,{className:"h-5 w-5"}),label:"Notes"}],f=[{href:"/business/settings/business-info",icon:r.jsx(x.Z,{className:"h-5 w-5"}),label:"Settings"}],j=[{href:"#",icon:r.jsx(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:r.jsx(t.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:r.jsx(m.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:r.jsx(h.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:r.jsx(u.Z,{className:"h-5 w-5"}),label:"Trash"}]},10718:(e,s,l)=>{"use strict";l.r(s),l.d(s,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});var r=l(68570);let t=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\business\market\page.tsx`),{__esModule:a,$$typeof:i}=t;t.default;let n=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\business\market\page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var l=e=>s(s.s=e),r=s.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588,4637],()=>l(95178));module.exports=r})();