"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6960],{25912:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},24241:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},42421:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},70518:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},13231:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},32309:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Dot",[["circle",{cx:"12.1",cy:"12.1",r:"1",key:"18d7e5"}]])},47019:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},75733:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},22757:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]])},20500:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},38364:function(e,t,n){n.d(t,{f:function(){return i}});var r=n(2265),o=n(18676),l=n(57437),a=r.forwardRef((e,t)=>(0,l.jsx)(o.WV.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var i=a},7568:function(e,t,n){n.d(t,{VY:function(){return G},h_:function(){return q},fC:function(){return L},xz:function(){return $}});var r=n(2265),o=n(78149),l=n(1584),a=n(98324),i=n(53938),u=n(20589),c=n(80467),s=n(53201),d=n(25510),p=n(56935),f=n(31383),v=n(18676),h=n(57437),m=r.forwardRef((e,t)=>{let{children:n,...o}=e,l=r.Children.toArray(n),a=l.find(w);if(a){let e=a.props.children,n=l.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,h.jsx)(g,{...o,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,h.jsx)(g,{...o,ref:t,children:n})});m.displayName="Slot";var g=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e,a;let i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref;return r.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...e)=>{l(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props),ref:t?(0,l.F)(t,i):i})}return r.Children.count(n)>1?r.Children.only(null):null});g.displayName="SlotClone";var y=({children:e})=>(0,h.jsx)(h.Fragment,{children:e});function w(e){return r.isValidElement(e)&&e.type===y}var b=n(91715),x=n(78369),C=n(9219),k="Popover",[E,P]=(0,a.b)(k,[d.D7]),M=(0,d.D7)(),[R,S]=E(k),j=e=>{let{__scopePopover:t,children:n,open:o,defaultOpen:l,onOpenChange:a,modal:i=!1}=e,u=M(t),c=r.useRef(null),[p,f]=r.useState(!1),[v=!1,m]=(0,b.T)({prop:o,defaultProp:l,onChange:a});return(0,h.jsx)(d.fC,{...u,children:(0,h.jsx)(R,{scope:t,contentId:(0,s.M)(),triggerRef:c,open:v,onOpenChange:m,onOpenToggle:r.useCallback(()=>m(e=>!e),[m]),hasCustomAnchor:p,onCustomAnchorAdd:r.useCallback(()=>f(!0),[]),onCustomAnchorRemove:r.useCallback(()=>f(!1),[]),modal:i,children:n})})};j.displayName=k;var D="PopoverAnchor";r.forwardRef((e,t)=>{let{__scopePopover:n,...o}=e,l=S(D,n),a=M(n),{onCustomAnchorAdd:i,onCustomAnchorRemove:u}=l;return r.useEffect(()=>(i(),()=>u()),[i,u]),(0,h.jsx)(d.ee,{...a,...o,ref:t})}).displayName=D;var O="PopoverTrigger",A=r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,a=S(O,n),i=M(n),u=(0,l.e)(t,a.triggerRef),c=(0,h.jsx)(v.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":H(a.open),...r,ref:u,onClick:(0,o.M)(e.onClick,a.onOpenToggle)});return a.hasCustomAnchor?c:(0,h.jsx)(d.ee,{asChild:!0,...i,children:c})});A.displayName=O;var W="PopoverPortal",[F,Z]=E(W,{forceMount:void 0}),_=e=>{let{__scopePopover:t,forceMount:n,children:r,container:o}=e,l=S(W,t);return(0,h.jsx)(F,{scope:t,forceMount:n,children:(0,h.jsx)(f.z,{present:n||l.open,children:(0,h.jsx)(p.h,{asChild:!0,container:o,children:r})})})};_.displayName=W;var T="PopoverContent",N=r.forwardRef((e,t)=>{let n=Z(T,e.__scopePopover),{forceMount:r=n.forceMount,...o}=e,l=S(T,e.__scopePopover);return(0,h.jsx)(f.z,{present:r||l.open,children:l.modal?(0,h.jsx)(B,{...o,ref:t}):(0,h.jsx)(I,{...o,ref:t})})});N.displayName=T;var B=r.forwardRef((e,t)=>{let n=S(T,e.__scopePopover),a=r.useRef(null),i=(0,l.e)(t,a),u=r.useRef(!1);return r.useEffect(()=>{let e=a.current;if(e)return(0,x.Ry)(e)},[]),(0,h.jsx)(C.Z,{as:m,allowPinchZoom:!0,children:(0,h.jsx)(V,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),u.current||null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;u.current=r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),I=r.forwardRef((e,t)=>{let n=S(T,e.__scopePopover),o=r.useRef(!1),l=r.useRef(!1);return(0,h.jsx)(V,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,a;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current||null===(a=n.triggerRef.current)||void 0===a||a.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{var r,a;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(l.current=!0));let i=t.target;(null===(a=n.triggerRef.current)||void 0===a?void 0:a.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),V=r.forwardRef((e,t)=>{let{__scopePopover:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:l,disableOutsidePointerEvents:a,onEscapeKeyDown:s,onPointerDownOutside:p,onFocusOutside:f,onInteractOutside:v,...m}=e,g=S(T,n),y=M(n);return(0,u.EW)(),(0,h.jsx)(c.M,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:l,children:(0,h.jsx)(i.XB,{asChild:!0,disableOutsidePointerEvents:a,onInteractOutside:v,onEscapeKeyDown:s,onPointerDownOutside:p,onFocusOutside:f,onDismiss:()=>g.onOpenChange(!1),children:(0,h.jsx)(d.VY,{"data-state":H(g.open),role:"dialog",id:g.contentId,...y,...m,ref:t,style:{...m.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),z="PopoverClose";function H(e){return e?"open":"closed"}r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,l=S(z,n);return(0,h.jsx)(v.WV.button,{type:"button",...r,ref:t,onClick:(0,o.M)(e.onClick,()=>l.onOpenChange(!1))})}).displayName=z,r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=M(n);return(0,h.jsx)(d.Eh,{...o,...r,ref:t})}).displayName="PopoverArrow";var L=j,$=A,q=_,G=N},47250:function(e,t,n){n.d(t,{D:function(){return o}});var r=n(2265);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},31725:function(e,t,n){n.d(t,{T:function(){return a},f:function(){return i}});var r=n(2265),o=n(18676),l=n(57437),a=r.forwardRef((e,t)=>(0,l.jsx)(o.WV.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));a.displayName="VisuallyHidden";var i=a},66431:function(e,t,n){n.d(t,{VM:function(){return v},uZ:function(){return h}});var r=n(2265),o=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,s=(e,t,n)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,d=(e,t)=>{for(var n in t||(t={}))u.call(t,n)&&s(e,n,t[n]);if(i)for(var n of i(t))c.call(t,n)&&s(e,n,t[n]);return e},p=(e,t)=>l(e,a(t)),f=(e,t)=>{var n={};for(var r in e)u.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&i)for(var r of i(e))0>t.indexOf(r)&&c.call(e,r)&&(n[r]=e[r]);return n},v=r.createContext({}),h=r.forwardRef((e,t)=>{let n;var o,l,a,i,u,{value:c,onChange:s,maxLength:h,textAlign:y="left",pattern:w="^\\d+$",inputMode:b="numeric",onComplete:x,pushPasswordManagerStrategy:C="increase-width",containerClassName:k,noScriptCSSFallback:E=g,render:P,children:M}=e,R=f(e,["value","onChange","maxLength","textAlign","pattern","inputMode","onComplete","pushPasswordManagerStrategy","containerClassName","noScriptCSSFallback","render","children"]);let[S,j]=r.useState("string"==typeof R.defaultValue?R.defaultValue:""),D=null!=c?c:S,O=(n=r.useRef(),r.useEffect(()=>{n.current=D}),n.current),A=r.useCallback(e=>{null==s||s(e),j(e)},[s]),W=r.useMemo(()=>w?"string"==typeof w?new RegExp(w):w:null,[w]),F=r.useRef(null),Z=r.useRef(null),_=r.useRef({value:D,onChange:A,isIOS:"undefined"!=typeof window&&(null==(l=null==(o=null==window?void 0:window.CSS)?void 0:o.supports)?void 0:l.call(o,"-webkit-touch-callout","none"))}),T=r.useRef({prev:[null==(a=F.current)?void 0:a.selectionStart,null==(i=F.current)?void 0:i.selectionEnd,null==(u=F.current)?void 0:u.selectionDirection]});r.useImperativeHandle(t,()=>F.current,[]),r.useEffect(()=>{let e=F.current,t=Z.current;if(!e||!t)return;function n(){if(document.activeElement!==e){H(null),$(null);return}let t=e.selectionStart,n=e.selectionEnd,r=e.selectionDirection,o=e.maxLength,l=e.value,a=T.current.prev,i=-1,u=-1,c;if(0!==l.length&&null!==t&&null!==n){let e=t===n,r=t===l.length&&l.length<o;if(e&&!r){if(0===t)i=0,u=1,c="forward";else if(t===o)i=t-1,u=t,c="backward";else if(o>1&&l.length>1){let e=0;if(null!==a[0]&&null!==a[1]){c=t<a[1]?"backward":"forward";let n=a[0]===a[1]&&a[0]<o;"backward"!==c||n||(e=-1)}i=e+t,u=e+t+1}}-1!==i&&-1!==u&&i!==u&&F.current.setSelectionRange(i,u,c)}let s=-1!==i?i:t,d=-1!==u?u:n,p=null!=c?c:r;H(s),$(d),T.current.prev=[s,d,p]}if(_.current.value!==e.value&&_.current.onChange(e.value),T.current.prev=[e.selectionStart,e.selectionEnd,e.selectionDirection],document.addEventListener("selectionchange",n,{capture:!0}),n(),document.activeElement===e&&V(!0),!document.getElementById("input-otp-style")){let e=document.createElement("style");if(e.id="input-otp-style",document.head.appendChild(e),e.sheet){let t="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";m(e.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),m(e.sheet,`[data-input-otp]:autofill { ${t} }`),m(e.sheet,`[data-input-otp]:-webkit-autofill { ${t} }`),m(e.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),m(e.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let r=()=>{t&&t.style.setProperty("--root-height",`${e.clientHeight}px`)};r();let o=new ResizeObserver(r);return o.observe(e),()=>{document.removeEventListener("selectionchange",n,{capture:!0}),o.disconnect()}},[]);let[N,B]=r.useState(!1),[I,V]=r.useState(!1),[z,H]=r.useState(null),[L,$]=r.useState(null);r.useEffect(()=>{var e;setTimeout(e=()=>{var e,t,n,r;null==(e=F.current)||e.dispatchEvent(new Event("input"));let o=null==(t=F.current)?void 0:t.selectionStart,l=null==(n=F.current)?void 0:n.selectionEnd,a=null==(r=F.current)?void 0:r.selectionDirection;null!==o&&null!==l&&(H(o),$(l),T.current.prev=[o,l,a])},0),setTimeout(e,10),setTimeout(e,50)},[D,I]),r.useEffect(()=>{void 0!==O&&D!==O&&O.length<h&&D.length===h&&(null==x||x(D))},[h,x,O,D]);let q=function({containerRef:e,inputRef:t,pushPasswordManagerStrategy:n,isFocused:o}){let l=r.useRef({done:!1,refocused:!1}),[a,i]=r.useState(!1),[u,c]=r.useState(!1),[s,d]=r.useState(!1),p=r.useMemo(()=>"none"!==n&&("increase-width"===n||"experimental-no-flickering"===n)&&a&&u,[a,u,n]),f=r.useCallback(()=>{let r=e.current,o=t.current;if(!r||!o||s||"none"===n)return;let a=r.getBoundingClientRect().left+r.offsetWidth,u=r.getBoundingClientRect().top+r.offsetHeight/2;if(!(0===document.querySelectorAll('[data-lastpass-icon-root],com-1password-button,[data-dashlanecreated],[style$="2147483647 !important;"]').length&&document.elementFromPoint(a-18,u)===r)&&(i(!0),d(!0),!l.current.refocused&&document.activeElement===o)){let e=[o.selectionStart,o.selectionEnd];o.blur(),o.focus(),o.setSelectionRange(e[0],e[1]),l.current.refocused=!0}},[e,t,s,n]);return r.useEffect(()=>{let t=e.current;if(!t||"none"===n)return;function r(){c(window.innerWidth-t.getBoundingClientRect().right>=40)}r();let o=setInterval(r,1e3);return()=>{clearInterval(o)}},[e,n]),r.useEffect(()=>{let e=o||document.activeElement===t.current;if("none"===n||!e)return;let r=setTimeout(f,0),l=setTimeout(f,2e3),a=setTimeout(f,5e3),i=setTimeout(()=>{d(!0)},6e3);return()=>{clearTimeout(r),clearTimeout(l),clearTimeout(a),clearTimeout(i)}},[t,o,n,f]),{hasPWMBadge:a,willPushPWMBadge:p,PWM_BADGE_SPACE_WIDTH:"40px"}}({containerRef:Z,inputRef:F,pushPasswordManagerStrategy:C,isFocused:I}),G=r.useCallback(e=>{let t=e.currentTarget.value.slice(0,h);if(t.length>0&&W&&!W.test(t)){e.preventDefault();return}"string"==typeof O&&t.length<O.length&&document.dispatchEvent(new Event("selectionchange")),A(t)},[h,A,O,W]),U=r.useCallback(()=>{var e;if(F.current){let t=Math.min(F.current.value.length,h-1),n=F.current.value.length;null==(e=F.current)||e.setSelectionRange(t,n),H(t),$(n)}V(!0)},[h]),Y=r.useCallback(e=>{var t,n;let r=F.current;if(!_.current.isIOS||!e.clipboardData||!r)return;let o=e.clipboardData.getData("text/plain");e.preventDefault();let l=null==(t=F.current)?void 0:t.selectionStart,a=null==(n=F.current)?void 0:n.selectionEnd,i=(l!==a?D.slice(0,l)+o+D.slice(a):D.slice(0,l)+o+D.slice(l)).slice(0,h);if(i.length>0&&W&&!W.test(i))return;r.value=i,A(i);let u=Math.min(i.length,h-1),c=i.length;r.setSelectionRange(u,c),H(u),$(c)},[h,A,W,D]),K=r.useMemo(()=>({position:"relative",cursor:R.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[R.disabled]),X=r.useMemo(()=>({position:"absolute",inset:0,width:q.willPushPWMBadge?`calc(100% + ${q.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:q.willPushPWMBadge?`inset(0 ${q.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:y,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[q.PWM_BADGE_SPACE_WIDTH,q.willPushPWMBadge,y]),J=r.useMemo(()=>r.createElement("input",p(d({autoComplete:R.autoComplete||"one-time-code"},R),{"data-input-otp":!0,"data-input-otp-mss":z,"data-input-otp-mse":L,inputMode:b,pattern:null==W?void 0:W.source,style:X,maxLength:h,value:D,ref:F,onPaste:e=>{var t;Y(e),null==(t=R.onPaste)||t.call(R,e)},onChange:G,onMouseOver:e=>{var t;B(!0),null==(t=R.onMouseOver)||t.call(R,e)},onMouseLeave:e=>{var t;B(!1),null==(t=R.onMouseLeave)||t.call(R,e)},onFocus:e=>{var t;U(),null==(t=R.onFocus)||t.call(R,e)},onBlur:e=>{var t;V(!1),null==(t=R.onBlur)||t.call(R,e)}})),[G,U,Y,b,X,h,L,z,R,null==W?void 0:W.source,D]),Q=r.useMemo(()=>({slots:Array.from({length:h}).map((e,t)=>{let n=I&&null!==z&&null!==L&&(z===L&&t===z||t>=z&&t<L),r=void 0!==D[t]?D[t]:null;return{char:r,isActive:n,hasFakeCaret:n&&null===r}}),isFocused:I,isHovering:!R.disabled&&N}),[I,N,h,L,z,R.disabled,D]),ee=r.useMemo(()=>P?P(Q):r.createElement(v.Provider,{value:Q},M),[M,Q,P]);return r.createElement(r.Fragment,null,null!==E&&r.createElement("noscript",null,r.createElement("style",null,E)),r.createElement("div",{ref:Z,"data-input-otp-container":!0,style:K,className:k},ee,r.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},J)))});function m(e,t){try{e.insertRule(t)}catch(e){console.error("input-otp could not insert CSS rule:",t)}}h.displayName="Input";var g=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`}}]);