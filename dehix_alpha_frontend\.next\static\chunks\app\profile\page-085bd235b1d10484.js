(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4178],{50859:function(e,r,t){Promise.resolve().then(t.bind(t,70760))},70760:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return z}});var s=t(57437),a=t(52022),n=t(24241),o=t(31014),l=t(6460),i=t(39343),d=t(59772),c=t(70402),u=t(77209),f=t(89733),m=t(2265),x=t(42873),p=t(87140),h=t(24258),g=t(89896),b=t(80023),y=t(29973);function j(){let[e,r]=(0,m.useState)(!1),t=()=>{r(!e)};return(0,s.jsxs)("div",{className:"",children:[(0,s.jsx)(f.z,{className:"md:hidden p-4",onClick:t,children:(0,s.jsx)(x.Z,{className:"h-6 w-6"})}),(0,s.jsx)("div",{className:"fixed inset-y-0 left-0 z-40\n        ".concat(e?"translate-x-0":"-translate-x-full"," md:fixed md:translate-x-0"),children:(0,s.jsx)(b.x,{className:"min-h-screen w-60 rounded-md border top-0 left-0",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("h4",{className:"mb-6 mt-4 text-xl font-bold leading-none text-center",children:"Profile"}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("div",{className:"text-lg font-medium text-gray-400 pb-2",children:"Contents"}),(0,s.jsx)("div",{className:"text-lg font-semibold",children:(0,s.jsxs)("div",{className:"flex flex-1 items-center gap-2 hover:bg-slate-500 cursor-pointer rounded-lg",children:[(0,s.jsx)(p.Z,{}),"Dashboard"]})})]}),(0,s.jsx)(y.Separator,{className:"-mt-2 mb-4"}),(0,s.jsx)("div",{className:"text-lg font-semibold mb-6 hover:bg-slate-500 cursor-pointer rounded-lg",children:"Profile Info"}),(0,s.jsx)(y.Separator,{className:"-mt-2 mb-4"}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("div",{className:"text-lg font-medium text-gray-400 pb-2",children:"Professional Info"}),(0,s.jsx)("div",{className:"text-lg font-semibold hover:bg-slate-500 cursor-pointer rounded-lg",children:"Freelancer"}),(0,s.jsx)("div",{className:"text-lg font-semibold hover:bg-slate-500 cursor-pointer rounded-lg",children:"Business"})]}),(0,s.jsx)(y.Separator,{className:"-mt-2 mb-4"}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("div",{className:"text-lg font-medium text-gray-400 pb-2",children:"Education Info"}),(0,s.jsx)("div",{className:"text-lg font-semibold hover:bg-slate-500 cursor-pointer rounded-lg",children:"Freelancer"})]}),(0,s.jsx)(y.Separator,{className:"-mt-2 mb-4"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-lg font-medium text-gray-400 pb-2",children:"Settings"}),(0,s.jsx)("div",{className:"text-lg font-semibold",children:(0,s.jsxs)("div",{className:"flex flex-1 items-center gap-2 hover:bg-slate-500 cursor-pointer rounded-lg",children:[(0,s.jsx)(h.Z,{}),"Account"]})})]})]})]}),(0,s.jsx)("div",{className:"m-10 mt-3",children:(0,s.jsxs)("div",{className:"flex flex-1 items-center gap-2",children:[(0,s.jsx)(g.Z,{}),(0,s.jsx)(f.z,{children:"Sign Out"})]})})]})})}),e&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black opacity-50 z-30",onClick:t})]})}var v=t(49354),N=t(69081),w=t(93363),I=t(21413),k=t(55084);let R=d.z.object({dob:d.z.date({required_error:"A date of birth is required."})});function z(){let e=(0,i.cI)({resolver:(0,o.F)(R)});return(0,s.jsxs)("div",{className:"flex flex-col md:flex-row",children:[(0,s.jsx)(j,{}),(0,s.jsx)("div",{className:"bg-gray-800 sm:min-h-screen w-full flex justify-center items-center py-6 md:py-0",children:(0,s.jsx)("div",{className:"bg-black w-full p-1rem rounded-lg flex flex-col items-center justify-center p-4 md:p-8",style:{height:"100%"},children:(0,s.jsx)("div",{className:"flex flex-col items-center justify-center",children:(0,s.jsxs)("section",{className:"flex flex-col items-center justify-center w-full p-6 mt-5 space-y-4 text-white rounded-lg shadow-lg md:ml-5",children:[(0,s.jsx)("div",{className:"rounded-full overflow-hidden w-24 h-24 md:w-32 md:h-32 mb-4 bg-gray-700 flex items-center justify-center",children:(0,s.jsx)(a.Z,{className:"w-16 h-16 md:w-20 md:h-20 text-white cursor-pointer"})}),(0,s.jsx)(w.l0,{...e,children:(0,s.jsxs)("form",{action:"#",className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.Label,{children:"First Name"}),(0,s.jsx)(u.I,{className:"block w-full rounded-md border border-gray-300 bg-gray-950 py-2 px-3 text-gray-400 placeholder-gray-500 focus:border-[#00b8d4] focus:outline-none focus:ring-[#00b8d4]",id:"first-name",name:"firstName",placeholder:"Enter your first name",required:!0,type:"text"}),(0,s.jsx)(w.pf,{children:"Enter your first name"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.Label,{children:"Last Name"}),(0,s.jsx)(u.I,{className:"block w-full rounded-md border border-gray-300 bg-gray-950 py-2 px-3 text-gray-400 placeholder-gray-500 focus:border-[#00b8d4] focus:outline-none focus:ring-[#00b8d4]",id:"last-name",name:"lastName",placeholder:"Enter your last name",required:!0,type:"text"}),(0,s.jsx)(w.pf,{children:"Enter your last name"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.Label,{children:"Username"}),(0,s.jsx)(u.I,{className:"block w-full rounded-md border border-gray-300 bg-gray-950 py-2 px-3 text-gray-400 placeholder-gray-500 focus:border-[#00b8d4] focus:outline-none focus:ring-[#00b8d4]",id:"user-name",name:"userName",placeholder:"Enter your username",required:!0,type:"text"}),(0,s.jsx)(w.pf,{children:"Enter your username"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.Label,{children:"Email"}),(0,s.jsx)(u.I,{className:"block w-full rounded-md border border-gray-300 bg-gray-950 py-2 px-3 text-gray-400 placeholder-gray-500 focus:border-[#00b8d4] focus:outline-none focus:ring-[#00b8d4]",id:"email",name:"email",placeholder:"Enter your email",required:!0,type:"email"}),(0,s.jsx)(w.pf,{children:"Enter your email"})]}),(0,s.jsxs)("div",{className:"flex items-end space-x-2",children:[(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)(c.Label,{children:"Phone Number"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"bg-gray-950 text-gray-400 py-2 px-3 rounded-md border border-gray-300",children:"+91"}),(0,s.jsx)(u.I,{className:"block w-full rounded-md border border-gray-300 bg-gray-950 py-2 px-3 text-gray-400 placeholder-gray-500 focus:border-[#00b8d4] focus:outline-none focus:ring-[#00b8d4]",id:"phone-number",name:"phoneNumber",placeholder:"Enter your phone number",required:!0,type:"number"})]})]}),(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsx)(f.z,{className:"bg-gray-600 text-white hover:bg-gray-800",children:"Send OTP"})})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(w.xJ,{children:[(0,s.jsx)(w.lX,{children:"One-Time Password"}),(0,s.jsx)(w.NI,{children:(0,s.jsx)(k.Zn,{maxLength:6,children:(0,s.jsxs)(k.hf,{children:[(0,s.jsx)(k.cY,{index:0}),(0,s.jsx)(k.cY,{index:1}),(0,s.jsx)(k.cY,{index:2}),(0,s.jsx)(k.cY,{index:3}),(0,s.jsx)(k.cY,{index:4}),(0,s.jsx)(k.cY,{index:5})]})})}),(0,s.jsx)(w.pf,{children:"Please enter the one-time password sent to your phone."}),(0,s.jsx)(w.zG,{})]}),(0,s.jsx)(f.z,{className:"bg-gray-600 text-white hover:bg-gray-800",children:"Verify"})]}),(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsx)(w.Wi,{control:e.control,name:"dob",render:e=>{let{field:r}=e;return(0,s.jsxs)(w.xJ,{className:"flex flex-col",children:[(0,s.jsx)(w.lX,{children:"Date of birth"}),(0,s.jsxs)(I.J2,{children:[(0,s.jsx)(I.xo,{asChild:!0,children:(0,s.jsx)(w.NI,{children:(0,s.jsxs)(f.z,{variant:"outline",className:(0,v.cn)("w-[240px] pl-3 text-left font-normal",!r.value&&"text-muted-foreground"),children:[r.value?(0,l.WU)(r.value,"PPP"):(0,s.jsx)("span",{children:"Pick a date"}),(0,s.jsx)(n.Z,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),(0,s.jsx)(I.yk,{className:"w-auto p-0",align:"start",children:(0,s.jsx)(N.f,{mode:"single",selected:r.value,onSelect:r.onChange,disabled:e=>e>new Date||e<new Date("1900-01-01"),initialFocus:!0})})]}),(0,s.jsx)(w.pf,{children:"Your date of birth is used to calculate your age."}),(0,s.jsx)(w.zG,{})]})}})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.Label,{children:"Enter your current role or position"}),(0,s.jsxs)("select",{className:"block w-full rounded-md border border-gray-300 bg-gray-950 py-2 px-3 text-gray-400 placeholder-gray-500 focus:border-[#00b8d4] focus:outline-none focus:ring-[#00b8d4]",id:"role",name:"role",defaultValue:"",required:!0,children:[(0,s.jsx)("option",{value:"",disabled:!0,hidden:!0,children:"Choose your role"}),(0,s.jsx)("option",{value:"role1",children:"Software Engineer"}),(0,s.jsx)("option",{value:"role2",children:"Data Scientist"}),(0,s.jsx)("option",{value:"role3",children:"UX UI Designer"}),(0,s.jsx)("option",{value:"role3",children:"Project Coordinator"}),(0,s.jsx)("option",{value:"role3",children:"Product Manager"}),(0,s.jsx)("option",{value:"role3",children:"Quality Assurance"})]}),(0,s.jsx)(w.pf,{children:"Enter your current role or position"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.Label,{children:"URLs"}),(0,s.jsx)(u.I,{className:"block w-full rounded-md border border-gray-300 bg-gray-950 py-2 px-3 text-gray-400 placeholder-gray-500 focus:border-[#00b8d4] focus:outline-none focus:ring-[#00b8d4]",id:"url",name:"ursl",placeholder:"Enter URL of your account",required:!0,type:"url"}),(0,s.jsx)(u.I,{className:"block w-full rounded-md border border-gray-300 bg-gray-950 py-2 px-3 text-gray-400 placeholder-gray-500 focus:border-[#00b8d4] focus:outline-none focus:ring-[#00b8d4]",id:"url",name:"ursl",placeholder:"Enter URL of your account",required:!0,type:"url"}),(0,s.jsx)(w.pf,{children:"Enter URL of your accounts"}),(0,s.jsx)(f.z,{className:"bg-gray-600 text-white hover:bg-gray-800",children:"Add URL"})]})]})})]})})})})]})}},89733:function(e,r,t){"use strict";t.d(r,{d:function(){return i},z:function(){return d}});var s=t(57437),a=t(2265),n=t(63355),o=t(12218),l=t(49354);let i=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,r)=>{let{className:t,variant:a,size:o,asChild:d=!1,...c}=e,u=d?n.g7:"button";return(0,s.jsx)(u,{className:(0,l.cn)(i({variant:a,size:o,className:t})),ref:r,...c})});d.displayName="Button"},69081:function(e,r,t){"use strict";t.d(r,{f:function(){return d}});var s=t(57437);t(2265);var a=t(70518),n=t(87592),o=t(13793),l=t(49354),i=t(89733);function d(e){let{className:r,classNames:t,showOutsideDays:d=!0,...c}=e;return(0,s.jsx)(o._W,{showOutsideDays:d,className:(0,l.cn)("p-3",r),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,l.cn)((0,i.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,l.cn)((0,i.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:e=>{let{...r}=e;return(0,s.jsx)(a.Z,{className:"h-4 w-4"})},IconRight:e=>{let{...r}=e;return(0,s.jsx)(n.Z,{className:"h-4 w-4"})}},...c})}d.displayName="Calendar"},93363:function(e,r,t){"use strict";t.d(r,{NI:function(){return h},Wi:function(){return u},l0:function(){return d},lX:function(){return p},pf:function(){return g},xJ:function(){return x},zG:function(){return b}});var s=t(57437),a=t(2265),n=t(63355),o=t(39343),l=t(49354),i=t(70402);let d=o.RV,c=a.createContext({}),u=e=>{let{...r}=e;return(0,s.jsx)(c.Provider,{value:{name:r.name},children:(0,s.jsx)(o.Qr,{...r})})},f=()=>{let e=a.useContext(c),r=a.useContext(m),{getFieldState:t,formState:s}=(0,o.Gc)(),n=t(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=r;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...n}},m=a.createContext({}),x=a.forwardRef((e,r)=>{let{className:t,...n}=e,o=a.useId();return(0,s.jsx)(m.Provider,{value:{id:o},children:(0,s.jsx)("div",{ref:r,className:(0,l.cn)("space-y-2",t),...n})})});x.displayName="FormItem";let p=a.forwardRef((e,r)=>{let{className:t,...a}=e,{error:n,formItemId:o}=f();return(0,s.jsx)(i.Label,{ref:r,className:(0,l.cn)(n&&"text-destructive",t),htmlFor:o,...a})});p.displayName="FormLabel";let h=a.forwardRef((e,r)=>{let{...t}=e,{error:a,formItemId:o,formDescriptionId:l,formMessageId:i}=f();return(0,s.jsx)(n.g7,{ref:r,id:o,"aria-describedby":a?"".concat(l," ").concat(i):"".concat(l),"aria-invalid":!!a,...t})});h.displayName="FormControl";let g=a.forwardRef((e,r)=>{let{className:t,...a}=e,{formDescriptionId:n}=f();return(0,s.jsx)("p",{ref:r,id:n,className:(0,l.cn)("text-sm text-muted-foreground",t),...a})});g.displayName="FormDescription";let b=a.forwardRef((e,r)=>{let{className:t,children:a,...n}=e,{error:o,formMessageId:i}=f(),d=o?String(null==o?void 0:o.message):a;return d?(0,s.jsx)("p",{ref:r,id:i,className:(0,l.cn)("text-sm font-medium text-destructive",t),...n,children:d}):null});b.displayName="FormMessage"},55084:function(e,r,t){"use strict";t.d(r,{Zn:function(){return i},aM:function(){return u},cY:function(){return c},hf:function(){return d}});var s=t(57437),a=t(2265),n=t(66431),o=t(32309),l=t(49354);let i=a.forwardRef((e,r)=>{let{className:t,containerClassName:a,...o}=e;return(0,s.jsx)(n.uZ,{ref:r,containerClassName:(0,l.cn)("flex items-center gap-2 has-[:disabled]:opacity-50",a),className:(0,l.cn)("disabled:cursor-not-allowed",t),...o})});i.displayName="InputOTP";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center",t),...a})});d.displayName="InputOTPGroup";let c=a.forwardRef((e,r)=>{let{index:t,className:o,...i}=e,{char:d,hasFakeCaret:c,isActive:u}=a.useContext(n.VM).slots[t];return(0,s.jsxs)("div",{ref:r,className:(0,l.cn)("relative flex h-10 w-10 items-center justify-center border-y border-r border-input text-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md",u&&"z-10 ring-2 ring-ring ring-offset-background",o),...i,children:[d,c&&(0,s.jsx)("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:(0,s.jsx)("div",{className:"h-4 w-px animate-caret-blink bg-foreground duration-1000"})})]})});c.displayName="InputOTPSlot";let u=a.forwardRef((e,r)=>{let{...t}=e;return(0,s.jsx)("div",{ref:r,role:"separator",...t,children:(0,s.jsx)(o.Z,{})})});u.displayName="InputOTPSeparator"},77209:function(e,r,t){"use strict";t.d(r,{I:function(){return o}});var s=t(57437),a=t(2265),n=t(49354);let o=a.forwardRef((e,r)=>{let{className:t,type:a,...o}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...o})});o.displayName="Input"},70402:function(e,r,t){"use strict";t.r(r),t.d(r,{Label:function(){return d}});var s=t(57437),a=t(2265),n=t(38364),o=t(12218),l=t(49354);let i=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)(n.f,{ref:r,className:(0,l.cn)(i(),t),...a})});d.displayName=n.f.displayName},21413:function(e,r,t){"use strict";t.d(r,{J2:function(){return l},xo:function(){return i},yk:function(){return d}});var s=t(57437),a=t(2265),n=t(7568),o=t(49354);let l=n.fC,i=n.xz,d=a.forwardRef((e,r)=>{let{className:t,align:a="center",sideOffset:l=4,...i}=e;return(0,s.jsx)(n.h_,{children:(0,s.jsx)(n.VY,{ref:r,align:a,sideOffset:l,className:(0,o.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...i})})});d.displayName=n.VY.displayName},80023:function(e,r,t){"use strict";t.d(r,{B:function(){return i},x:function(){return l}});var s=t(57437),a=t(2265),n=t(37193),o=t(49354);let l=a.forwardRef((e,r)=>{let{className:t,children:a,...l}=e;return(0,s.jsxs)(n.fC,{ref:r,className:(0,o.cn)("relative overflow-hidden",t),...l,children:[(0,s.jsx)(n.l_,{className:"h-full w-full rounded-[inherit]",children:a}),(0,s.jsx)(i,{}),(0,s.jsx)(n.Ns,{})]})});l.displayName=n.fC.displayName;let i=a.forwardRef((e,r)=>{let{className:t,orientation:a="vertical",...l}=e;return(0,s.jsx)(n.gb,{ref:r,orientation:a,className:(0,o.cn)("flex touch-none select-none transition-colors","vertical"===a&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",t),...l,children:(0,s.jsx)(n.q4,{className:"relative flex-1 rounded-full bg-border"})})});i.displayName=n.gb.displayName},29973:function(e,r,t){"use strict";t.r(r),t.d(r,{Separator:function(){return l}});var s=t(57437),a=t(2265),n=t(48484),o=t(49354);let l=a.forwardRef((e,r)=>{let{className:t,orientation:a="horizontal",decorative:l=!0,...i}=e;return(0,s.jsx)(n.f,{ref:r,decorative:l,orientation:a,className:(0,o.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",t),...i})});l.displayName=n.f.displayName},42361:function(e,r,t){"use strict";t.d(r,{I8:function(){return c},Vv:function(){return u},db:function(){return d}});var s=t(15236),a=t(75735),n=t(60516),o=t(69842),l=t(99854);let i=(0,s.ZF)({apiKey:"AIzaSyBPTH9xikAUkgGof048klY6WGiZSmRoXXA",authDomain:"dehix-6c349.firebaseapp.com",databaseURL:"https://dehix-6c349-default-rtdb.firebaseio.com",projectId:"dehix-6c349",storageBucket:"dehix-6c349.appspot.com",messagingSenderId:"521082542540",appId:"1:521082542540:web:543857e713038c2927a569"}),d=(0,o.ad)(i),c=(0,a.v0)(i);c.useDeviceLanguage();let u=new a.hJ;(0,n.N8)(i),(0,l.cF)(i)},15922:function(e,r,t){"use strict";t.d(r,{b:function(){return a},q:function(){return n}});var s=t(38472);let a=s.Z.create({baseURL:"http://127.0.0.1:8080/"});console.log("Base URL:","http://127.0.0.1:8080/");let n=e=>{console.log("Initializing Axios with token:",e),a=s.Z.create({baseURL:"http://127.0.0.1:8080/",headers:{Authorization:"Bearer ".concat(e)}})};a.interceptors.request.use(e=>(console.log("Request config:",e),e),e=>(console.error("Request error:",e),Promise.reject(e))),a.interceptors.response.use(e=>(console.log("Response:",e.data),e),e=>(console.error("Response error:",e),Promise.reject(e)))},49354:function(e,r,t){"use strict";t.d(r,{bx:function(){return f},c0:function(){return c},cn:function(){return d},is:function(){return m},pH:function(){return u}});var s=t(44839),a=t(96164),n=t(75735),o=t(44785),l=t(15922),i=t(42361);function d(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.m6)((0,s.W)(r))}let c=async e=>{try{await (0,n.LS)(i.I8,e),console.log("Password reset email sent successfully.")}catch(t){let e=t.code,r=t.message;throw console.log(e,r),Error(r)}},u=async(e,r)=>{try{return await (0,n.e5)(i.I8,e,r)}catch(t){let e=t.code,r=t.message;throw console.log(e,r),Error(r)}},f=async()=>await (0,n.rh)(i.I8,i.Vv),m=async e=>{try{let r=e.user,t=await r.getIdToken();(0,l.q)(t);let s=(await r.getIdTokenResult()).claims,a={uid:r.uid,email:r.email,displayName:r.displayName,phoneNumber:r.phoneNumber,photoURL:r.photoURL,emailVerified:r.emailVerified},n=s.type;return o.Z.set("userType",n,{expires:1,path:"/"}),o.Z.set("token",t,{expires:1,path:"/"}),{user:a,claims:s}}catch(e){throw console.error("Error fetching user data:",e),e}}}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,1974,7356,1374,2308,2971,7023,1744],function(){return e(e.s=50859)}),_N_E=e.O()}]);