(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2331],{47673:function(e,t,a){Promise.resolve().then(a.bind(a,2448))},5891:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},20897:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},43224:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("CalendarX2",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M21 13V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h8",key:"3spt84"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"m17 22 5-5",key:"1k6ppv"}],["path",{d:"m17 17 5 5",key:"p7ous7"}]])},13231:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},71935:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40933:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},47390:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},29406:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},36141:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},98960:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},73347:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},10883:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2448:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return E}});var s=a(57437),r=a(87138),n=a(92940),i=a(40933),o=a(29406),c=a(43224),l=a(11444),d=a(2265),m=a(64797),h=a(89733),p=a(48185),u=a(15642),x=a(29973),f=a(41969),g=a(21832),j=a(82230),b=a(15922),v=a(55235),N=a(97540),y=a(62688),k=a(78068);function E(){let e=(0,l.v9)(e=>e.user),[t,a]=(0,d.useState)([]);(0,d.useEffect)(()=>{(async()=>{try{if(null==e?void 0:e.uid){let e=await b.b.get("/project/business");a(e.data.data)}}catch(e){(0,k.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.error("API Error:",e)}})()},[e.uid]);let E=t.filter(e=>e.status==N.sB.COMPLETED),w=t.filter(e=>e.status!==N.sB.COMPLETED);return(0,s.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,s.jsx)(m.Z,{menuItemsTop:j.yn,menuItemsBottom:j.$C,active:"Dashboard"}),(0,s.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,s.jsx)(y.Z,{menuItemsTop:j.yn,menuItemsBottom:j.$C,activeMenu:"Dashboard",breadcrumbItems:[{label:"Business",link:"#"}]}),(0,s.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8 lg:grid-cols-3 xl:grid-cols-3",children:[(0,s.jsxs)("div",{className:"grid auto-rows-max items-start gap-4 md:gap-8 lg:col-span-2",children:[(0,s.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-2 xl:grid-cols-4",children:[(0,s.jsxs)(p.Zb,{className:"sm:col-span-2","x-chunk":"dashboard-05-chunk-0",children:[(0,s.jsxs)(p.Ol,{className:"pb-3",children:[(0,s.jsx)(p.ll,{children:"Your Projects"}),(0,s.jsx)(p.SZ,{className:"max-w-lg text-balance leading-relaxed",children:"Introducing Our Dynamic Projects Dashboard for Seamless Management and Insightful Analysis."})]}),(0,s.jsx)(p.eW,{children:(0,s.jsx)(r.default,{href:"/business/add-project",passHref:!0,children:(0,s.jsxs)(h.z,{className:"w-full",children:[" ","Create New Project"]})})})]}),(0,s.jsx)(f.Z,{title:"Completed Projects",value:E.length,icon:(0,s.jsx)(n.Z,{className:"h-6 w-6 text-success"}),additionalInfo:"Project stats will be here"}),(0,s.jsx)(f.Z,{title:"Pending Projects",value:w.length,icon:(0,s.jsx)(i.Z,{className:"h-6 w-6 text-warning"}),additionalInfo:"Pending project stats will be here"})]}),(0,s.jsx)(x.Separator,{className:"my-1"}),(0,s.jsxs)("h2",{className:"scroll-m-20 text-3xl font-semibold tracking-tight transition-colors first:mt-0",children:["Current Projects ","(".concat(w.length,")")]}),(0,s.jsx)("div",{className:"flex gap-4 overflow-x-scroll no-scrollbar pb-8 pt-5",children:(0,s.jsxs)(u.lr,{className:"w-full relative",children:[(0,s.jsx)(u.KI,{className:"flex mt-3 gap-4",children:w.length>0?w.map((e,t)=>(0,s.jsx)(u.d$,{className:"md:basis-1/2 lg:basis-1/2",children:(0,s.jsx)(g.t,{cardClassName:"min-w-[45%]",project:e},t)},t)):(0,s.jsxs)("div",{className:"text-center py-10 w-[100%] ",children:[(0,s.jsx)(o.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,s.jsx)("p",{className:"text-gray-500",children:"No projects available"})]})}),w.length>2&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)(u.am,{className:"absolute left-0 -top-1 transform -translate-y-1/2 p-2 shadow-md transition-colors",children:"Previous"}),(0,s.jsx)(u.Pz,{className:"absolute right-0 -top-1 transform -translate-y-1/2 p-2 shadow-md transition-colors",children:"Next"})]})})]})}),(0,s.jsx)(x.Separator,{className:"my-1"}),(0,s.jsxs)("h2",{className:"scroll-m-20 text-3xl font-semibold tracking-tight transition-colors first:mt-0",children:["Completed Projects ","(".concat(E.length,")")]}),(0,s.jsx)("div",{className:"flex relative gap-4 overflow-x-scroll no-scrollbar pb-8 pt-5",children:(0,s.jsxs)(u.lr,{className:"w-full relative pt-9",children:[(0,s.jsx)(u.KI,{className:"flex gap-4",children:E.length>0?E.map((e,t)=>(0,s.jsx)(u.d$,{className:"md:basis-1/2 lg:basis-1/2",children:(0,s.jsx)(g.t,{cardClassName:"min-w-full",project:e})},t)):(0,s.jsxs)("div",{className:"text-center py-10 w-full",children:[(0,s.jsx)(o.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,s.jsx)("p",{className:"text-gray-500",children:"No projects available"})]})}),E.length>2&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)(u.am,{className:"absolute left-0 top-1 transform -translate-y-1/2 p-2 shadow-md transition-colors",children:"Previous"}),(0,s.jsx)(u.Pz,{className:"absolute right-0 top-1 transform -translate-y-1/2 p-2 shadow-md transition-colors",children:"Next"})]})})]})})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(p.ll,{className:"group flex items-center gap-2 text-2xl",children:"Interviews"}),(null==v?void 0:v.freelancersampleInterview)?(0,s.jsxs)("div",{className:"text-center py-10",children:[(0,s.jsx)(c.Z,{className:"mx-auto mb-2 text-gray-500",size:"100"}),(0,s.jsx)("p",{className:"text-gray-500",children:"No interviews scheduled"})]}):(0,s.jsx)(s.Fragment,{})]})]})]})]})}},21832:function(e,t,a){"use strict";a.d(t,{t:function(){return p}});var s=a(57437),r=a(87138),n=a(36141),i=a(49354),o=a(89733),c=a(48185),l=a(79055),d=a(44475),m=a(34859),h=a(97540);function p(e){var t,a;let{cardClassName:p,project:u,type:x=m.Dy.BUSINESS,...f}=e,{text:g,className:j}=(0,d.S)(u.status);return(0,s.jsxs)(c.Zb,{className:(0,i.cn)("flex flex-col h-[400px]",p),...f,children:[(0,s.jsxs)(c.Ol,{children:[(0,s.jsxs)(c.ll,{className:"flex",children:[u.projectName,"\xa0",u.verified&&(0,s.jsx)(n.Z,{className:"text-success"})]}),(0,s.jsxs)(c.SZ,{className:"text-gray-600",children:[(0,s.jsx)("p",{className:"my-auto",children:u.createdAt?new Date(u.createdAt).toLocaleDateString():"N/A"}),(0,s.jsx)("br",{}),(0,s.jsx)(l.C,{className:j,children:g})]})]}),(0,s.jsxs)(c.aY,{className:"grid gap-4 mb-auto flex-grow",children:[(0,s.jsxs)("div",{className:"mb-4 items-start pb-4 last:mb-0 last:pb-0 w-full",children:[(0,s.jsx)("span",{className:"flex h-2 w-2 rounded-full"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:(null===(t=u.description)||void 0===t?void 0:t.length)>40?"".concat(u.description.slice(0,40),"..."):u.description||"No description available"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Company:"})," ",u.companyName]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Role:"})," ",u.role]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Experience:"})," ",u.experience]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-1 mt-2",children:null==u?void 0:null===(a=u.skillsRequired)||void 0===a?void 0:a.map((e,t)=>(0,s.jsx)(l.C,{className:"text-xs text-white bg-muted",children:e},t))})]})]}),(0,s.jsx)(c.eW,{children:(0,s.jsx)(r.default,{href:"/".concat(x.toLocaleLowerCase(),"/project/").concat(u._id),className:"w-full",children:(0,s.jsx)(o.z,{className:"w-full ".concat(u.status===h.sB.COMPLETED&&"bg-green-900 hover:bg-green-700"),children:"View full details"})})})]})}},41969:function(e,t,a){"use strict";a.d(t,{Z:function(){return n}});var s=a(57437),r=a(48185);function n(e){let{title:t,value:a,icon:n,additionalInfo:i}=e;return(0,s.jsxs)(r.Zb,{"x-chunk":"dashboard-05-chunk-1",children:[(0,s.jsxs)(r.Ol,{className:"pb-2",children:[n,(0,s.jsx)(r.SZ,{children:t}),(0,s.jsx)(r.ll,{className:"text-4xl",children:a})]}),(0,s.jsx)(r.aY,{children:(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:i})})]})}},29973:function(e,t,a){"use strict";a.r(t),a.d(t,{Separator:function(){return o}});var s=a(57437),r=a(2265),n=a(48484),i=a(49354);let o=r.forwardRef((e,t)=>{let{className:a,orientation:r="horizontal",decorative:o=!0,...c}=e;return(0,s.jsx)(n.f,{ref:t,decorative:o,orientation:r,className:(0,i.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",a),...c})});o.displayName=n.f.displayName},82230:function(e,t,a){"use strict";a.d(t,{$C:function(){return g},Ne:function(){return j},yn:function(){return f}});var s=a(57437),r=a(11005),n=a(98960),i=a(38133),o=a(20897),c=a(13231),l=a(71935),d=a(47390),m=a(73347),h=a(24258),p=a(5891),u=a(10883),x=a(66648);let f=[{href:"#",icon:(0,s.jsx)(x.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,s.jsx)(r.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:(0,s.jsx)(n.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:(0,s.jsx)(i.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:(0,s.jsx)(i.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:(0,s.jsx)(o.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:(0,s.jsx)(c.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:(0,s.jsx)(l.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:(0,s.jsx)(d.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,s.jsx)(m.Z,{className:"h-5 w-5"}),label:"Notes"}],g=[{href:"/business/settings/business-info",icon:(0,s.jsx)(h.Z,{className:"h-5 w-5"}),label:"Settings"}],j=[{href:"#",icon:(0,s.jsx)(x.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,s.jsx)(r.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:(0,s.jsx)(m.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:(0,s.jsx)(p.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:(0,s.jsx)(u.Z,{className:"h-5 w-5"}),label:"Trash"}]},34859:function(e,t,a){"use strict";var s,r,n,i,o,c,l,d,m,h,p,u,x,f,g,j,b,v,N,y,k,E;a.d(t,{Dy:function(){return h},Dz:function(){return w}});let w={BATCH:3};(u=s||(s={})).PROJECT_HIRING="PROJECT_HIRING",u.SKILL_INTERVIEW="SKILL_INTERVIEW",u.DOMAIN_INTERVIEW="DOMAIN_INTERVIEW",u.TALENT_INTERVIEW="TALENT_INTERVIEW",(x=r||(r={})).ADDED="Added",x.APPROVED="Approved",x.CLOSED="Closed",x.COMPLETED="Completed",(f=n||(n={})).ACTIVE="Active",f.IN_ACTIVE="Inactive",f.NOT_VERIFIED="Not Verified",(g=i||(i={})).BUSINESS="Business",g.FREELANCER="Freelancer",g.BOTH="Both",(j=o||(o={})).ACTIVE="Active",j.IN_ACTIVE="Inactive",(b=c||(c={})).APPLIED="APPLIED",b.NOT_APPLIED="NOT_APPLIED",b.APPROVED="APPROVED",b.FAILED="FAILED",b.STOPPED="STOPPED",b.REAPPLIED="REAPPLIED",(v=l||(l={})).PENDING="Pending",v.ACCEPTED="Accepted",v.REJECTED="Rejected",v.PANEL="Panel",v.INTERVIEW="Interview",(N=d||(d={})).ACTIVE="ACTIVE",N.INACTIVE="INACTIVE",N.ARCHIVED="ARCHIVED",(y=m||(m={})).ACTIVE="Active",y.PENDING="Pending",y.INACTIVE="Inactive",y.CLOSED="Closed",(k=h||(h={})).FREELANCER="FREELANCER",k.ADMIN="ADMIN",k.BUSINESS="BUSINESS",(E=p||(p={})).CREATED="Created",E.CLOSED="Closed",E.ACTIVE="Active"},97540:function(e,t,a){"use strict";var s,r,n,i,o,c;a.d(t,{cd:function(){return s},d8:function(){return l},kJ:function(){return r},sB:function(){return n}}),(i=s||(s={})).Mastery="Mastery",i.Proficient="Proficient",i.Beginner="Beginner",(o=r||(r={})).ACTIVE="Active",o.PENDING="Pending",o.REJECTED="Rejected",o.COMPLETED="Completed",(c=n||(n={})).ACTIVE="ACTIVE",c.PENDING="PENDING",c.REJECTED="REJECTED",c.COMPLETED="COMPLETED";let l={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},48484:function(e,t,a){"use strict";a.d(t,{f:function(){return l}});var s=a(2265),r=a(18676),n=a(57437),i="horizontal",o=["horizontal","vertical"],c=s.forwardRef((e,t)=>{let{decorative:a,orientation:s=i,...c}=e,l=o.includes(s)?s:i;return(0,n.jsx)(r.WV.div,{"data-orientation":l,...a?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...c,ref:t})});c.displayName="Separator";var l=c},55235:function(e){"use strict";e.exports=JSON.parse('{"freelancerEarnings":1000,"freelancersampleInterview":{"interviewer":"John Doe","interviewee":"Jane Smith","skill":"React Development","interviewDate":"2023-11-23T10:30:00Z","rating":4.5,"comments":"Great communication skills and technical expertise."},"dashboardactiveProject":{"heading":"Active Project","content":"+11 Current active projects"},"dashboardpendingProject":{"title":"Pending projects","itemCounts":{"total":15,"low":5,"medium":5,"high":5}},"dashboardBusinessCompleteProject":"+10% from last month","dashboardBusinessPendingProject":"+2 new projects this week","dashboardtotalRevenue":{"heading":"Total Revenue","content":"$45,231.89 +20.1% from last month"},"dashboardoracleWork":{"heading":"Oracle Work","content":"+11 projects"},"dashboardorderTable":{"customerName":"Jack smith","customerEmail":"<EMAIL>","customerType":"Sale","customerStatus":"Fulfilled","customerDate":"2023-06-23","customerAmount":"$250.00"},"dashboardorderDate":"Date: November 23, 2023","dashboardorderShippingAddress":{"name":"Liam Johnson","address":"1234 Main St.","state":"Anytown, CA 12345"},"dashboardorderCustomerInfo":{"customer":"Liam Johnson","email":"<EMAIL>","phone":"****** 567 890"},"dashboardorderCard":{"card":"Visa","cardNumber":"**** **** **** 4532"},"dashboardorderUpdateDate":"November 23, 2023","marketfreelancerJob":{"heading":"Arya.ai Data Scientist","content":"Arya is an autonomous AI operating platform for banks, insurers, and financial service providers that simplifies buildout and manages the...","skills":["Generative AI","Python","NLP","PyTorch","Transformers"],"location":"Mumbai","founded":"2013","employees":"10-50 employees"},"dashboardfreelancercurrentInterview":[{"reference":"Jane Smith","skill":"HTML/CSS","interviewDate":"2023-11-23T10:30:00Z","rating":9,"comments":"Great communication skills and technical expertise.","status":"Completed","description":"This interview focused on assessing proficiency in HTML/CSS and evaluating communication skills.","contact":"<EMAIL>"},{"reference":"Jane Smith","domain":"DevOps","interviewDate":"2023-11-23T10:30:00Z","rating":9,"comments":"Great communication skills and technical expertise.","status":"Pending","description":"This interview was scheduled to discuss the candidate\'s experience and skills in DevOps.","contact":"<EMAIL>"}],"dashboardfreelancerhistoryUserEmail":"<EMAIL>","dashboardfreelancerhistoryInterview":[{"reference":"Jane Smith","skill":"HTML/CSS","interviewDate":"2023-11-23T10:30:00Z","rating":9,"comments":"Great communication skills and technical expertise.","status":"Completed","description":"This interview focused on assessing proficiency in HTML/CSS and evaluating communication skills.","contact":"<EMAIL>"},{"reference":"Jane Smith","domain":"DevOps","interviewDate":"2023-11-23T10:30:00Z","rating":9,"comments":"Great communication skills and technical expertise.","status":"Completed","description":"This interview was scheduled to discuss the candidate\'s experience and skills in DevOps.","contact":"<EMAIL>"}],"dashboardFreelancerOracleBusiness":[{"firstName":"John","lastName":"Smith","email":"<EMAIL>","phone":"+*********0","companyName":"Tech Innovators Inc.","companySize":"500-1000 employees","referenceEmail":"<EMAIL>","websiteLink":"https://www.techinnovators.com","linkedInLink":"https://www.linkedin.com/in/johnsmith","githubLink":"https://github.com/johnsmith","comments":"","status":"pending"},{"firstName":"Alice","lastName":"Johnson","email":"<EMAIL>","phone":"+0*********","companyName":"Creative Solutions Ltd.","companySize":"100-500 employees","referenceEmail":"<EMAIL>","websiteLink":"https://www.creativesolutions.com","linkedInLink":"https://www.linkedin.com/in/alicejohnson","githubLink":"https://github.com/alicejohnson","comments":"","status":"pending"},{"firstName":"Robert","lastName":"Brown","email":"<EMAIL>","phone":"+1122334455","companyName":"Global Enterprises","companySize":"1000-5000 employees","referenceEmail":"<EMAIL>","websiteLink":"https://www.globalenterprises.com","linkedInLink":"https://www.linkedin.com/in/robertbrown","githubLink":"https://github.com/robertbrown","comments":"","status":"pending"}],"dashboardFreelancerOracleEducation":[{"type":"Bachelor\'s Degree","instituteName":"University of Example","location":"Example City, Example Country","startFrom":"2018-09-01","endTo":"2022-06-15","grade":"A","referencePersonName":"Dr. John Doe","degreeNumber":"*********","comments":"","status":"pending"},{"type":"Master\'s Degree","instituteName":"University of Example","location":"Example City, Example Country","startFrom":"2022-09-01","endTo":"2024-06-15","grade":"A+","referencePersonName":"Dr. Jane Smith","degreeNumber":"*********","comments":"","status":"pending"},{"type":"Ph.D.","instituteName":"University of Example","location":"Example City, Example Country","startFrom":"2024-09-01","endTo":"2028-06-15","grade":"A+","referencePersonName":"Dr. Emily Johnson","degreeNumber":"456789123","comments":"","status":"pending"}],"dashboardFreelancerOracleProject":[{"projectName":"Task Tracker","description":"A web application for managing and tracking daily tasks and projects.","githubLink":"https://github.com/yourusername/TaskTracker","startFrom":"2023-05-01","endTo":"2023-10-15","reference":"Mr. Alex Johnson, Senior Developer","techUsed":["Vue.js","JavaScript","Firebase","CSS"],"comments":"","status":"pending"},{"projectName":"Inventory Management System","description":"A system for managing inventory in warehouses.","githubLink":"https://github.com/yourusername/InventoryManagementSystem","startFrom":"2022-01-01","endTo":"2022-06-01","reference":"Ms. Maria Gonzalez, Project Manager","techUsed":["React","Node.js","MongoDB","Sass"],"comments":"","status":"pending"},{"projectName":"E-commerce Platform","description":"An online platform for buying and selling products.","githubLink":"https://github.com/yourusername/EcommercePlatform","startFrom":"2021-02-01","endTo":"2021-08-01","reference":"Mr. John Smith, Lead Developer","techUsed":["Angular","TypeScript","Firebase","Bootstrap"],"comments":"","status":"pending"}],"dashboardFreelancerOracleExperience":[{"jobTitle":"Frontend Developer","workDescription":"Responsible for developing user-friendly web applications using React and TypeScript.","startFrom":"2022-01-15","endTo":"2023-07-01","referencePersonName":"Jane Doe","referencePersonEmail":"<EMAIL>","githubRepoLink":"https://github.com/janedoe/project-repo","comments":"","status":"pending"},{"jobTitle":"Backend Developer","workDescription":"Developed and maintained server-side logic using Node.js and Express.","startFrom":"2021-02-01","endTo":"2021-12-31","referencePersonName":"John Smith","referencePersonEmail":"<EMAIL>","githubRepoLink":"https://github.com/johnsmith/backend-project","comments":"","status":"pending"},{"jobTitle":"Full Stack Developer","workDescription":"Worked on both frontend and backend development using MERN stack.","startFrom":"2020-03-01","endTo":"2021-01-31","referencePersonName":"Alice Johnson","referencePersonEmail":"<EMAIL>","githubRepoLink":"https://github.com/alicejohnson/fullstack-project","comments":"","status":"pending"}],"businessProjectDetailCard":{"description":"Welcome to our project! This initiative aims to [briefly describe the main goal or purpose of the project ]. Through thisproject, we intend to [mention key objectives or outcomes]. Our team is dedicated to [highlight any unique approaches ormethodologies]. We believe this project will [state the expected impact or benefits ]. Feel free to replace the placeholders with specific details about your project. \\nIf you need further customization or additional sections, let me know!  \\nWelcome to our project! This initiative aims to [briefly describe the main goal or purpose of the project ]. Through this project, we intend to [mention key objectives or outcomes ]. Our team is dedicated to [highlight any unique approaches or methodologies ]. We believe this project will [state the expected impact or benefits ]. Feel free to replace the placeholders with specific details about your project. If you need further customization or additional sections, let me know! ","email":"<EMAIL>","status":"Current","startDate":"22/22/2222","endDate":"24/22/2222"},"businessprojectCardDomains":["Frontend","Backend","Graphic Designer","3D artist","Fullstack"],"businessprojectCardSkills":["React","Mongo","Golang","Java","Html","Css"],"businessprojectProfileCard":{"heading":"Frontend Developer","description":"Your requirement is of 2 freelancers for this profile, 6 people have appplied via bid and 1 person is selected till now.","email":"<EMAIL>","status":"Current","startDate":"22/22/2222","endDate":"24/22/2222"},"marketFreelancerProject":{"project_name":"AI Development Project","project_id":"#12345","location":"Delhi, India","description":"We\'re looking for an experienced web developer who\'s really good at making interactive forms. The perfect candidate should know a lot about web development and have a bunch of cool forms they\'ve made before. Your main job will be making forms that people can use easily and that look nice.","email":"<EMAIL>","company_name":"Tech Innovators Inc.","start":"2023-01-01T00:00:00.000Z","end":"2023-12-31T00:00:00.000Z","skills_required":["JavaScript","React","Python","Machine Learning"],"experience":"2+ years of experience in AI development.","role":"Lead Developer","project_type":"Full-time"},"marketFreelancerProjectOtherBits":[{"username":"Alex004","bitAmount":100},{"username":"User2","bitAmount":150},{"username":"alen789","bitAmount":200}],"projectRejectedCard":{"companyName":"ABC Corporation","role":"Software Engineer","projectType":"Web Development","description":"This is a sample project description","skillsRequired":["JavaScript","React","Node.js"],"start":"2023-02-15","email":"<EMAIL>","experience":"5+ years"},"projectCurrentCard":{"companyName":"ABC Corporation","role":"Software Engineer","projectType":"Web Development","description":"This is a sample project description for a current ongoing project.","skillsRequired":["JavaScript","React","Node.js"],"start":"2023-02-15","end":"current","email":"<EMAIL>","experience":"5+ years"},"projectCompleteCard":{"companyName":"ABC Corporation","role":"Software Engineer","projectType":"Web Development","description":"This is a sample project description for a current ongoing project.","skillsRequired":["JavaScript","React","Node.js"],"start":"2023-02-15","end":"2023-09-24","email":"<EMAIL>","experience":"5+ years"},"projectUnderVerificatinCard":{"companyName":"ABC Corporation","role":"Software Engineer","projectType":"Web Development","description":"This is a sample project description for a current ongoing project.","skillsRequired":["JavaScript","React","Node.js"],"start":"2023-02-15","email":"<EMAIL>","experience":"5+ years"}}')}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2455,9726,2688,2971,7023,1744],function(){return e(e.s=47673)}),_N_E=e.O()}]);