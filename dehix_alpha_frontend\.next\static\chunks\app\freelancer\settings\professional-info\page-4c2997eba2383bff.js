(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7785],{48848:function(e,r,t){Promise.resolve().then(t.bind(t,77456))},6540:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(33480).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},25912:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},24241:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(33480).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},87055:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(33480).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},40036:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(33480).Z)("ImagePlus",[["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7",key:"31hg93"}],["line",{x1:"16",x2:"22",y1:"5",y2:"5",key:"ez7e4s"}],["line",{x1:"19",x2:"19",y1:"2",y2:"8",key:"1gkr8c"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},47390:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},67524:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(33480).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},77456:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return C}});var n=t(57437),s=t(11444),a=t(2265),o=t(64797),l=t(87055),i=t(47390),c=t(68515),d=t(79055),m=t(48185),u=e=>{let{company:r,jobTitle:t,workDescription:s,workFrom:a,workTo:o,referencePersonName:u,referencePersonContact:f,githubRepoLink:h,verificationStatus:x,comments:p}=e;return(0,n.jsxs)(m.Zb,{className:"w-full h-full mx-auto md:max-w-2xl",children:[(0,n.jsxs)(m.Ol,{children:[(0,n.jsxs)(m.ll,{className:"flex",children:[r,h&&(0,n.jsx)("div",{className:"ml-auto",children:(0,n.jsx)("a",{href:h,className:"text-sm text-white underline",children:(0,n.jsx)(l.Z,{})})})]}),(0,n.jsx)(m.SZ,{className:"block mt-1 uppercase tracking-wide leading-tight font-medium text-white",children:t})]}),(0,n.jsxs)(m.aY,{children:[(0,n.jsx)(d.C,{className:"px-3 py-1 text-xs font-bold rounded-full border transition ".concat((e=>{switch(e.toLowerCase()){case"pending":return"bg-yellow-500 hover:bg-yellow-600";case"verified":return"bg-green-500 hover:bg-green-600";default:return"bg-blue-500 hover:bg-blue-600"}})(x)),children:x.toUpperCase()}),(0,n.jsx)("p",{className:"text-gray-300 pt-4",children:s}),(0,n.jsxs)("p",{className:"mt-2 flex text-gray-500 border p-3 rounded",children:[(0,n.jsx)(i.Z,{className:"pr-1"}),p]}),(0,n.jsxs)("div",{className:"mt-4",children:[(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Reference: ",u]}),(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Contact: ",f]})]})]}),(0,n.jsx)(m.eW,{className:"flex",children:(0,n.jsx)(c.Z,{startDate:a,endDate:o})})]})},f=t(15922),h=t(31014),x=t(39343),p=t(59772),j=t(92513),g=t(95137),y=t(71976),b=t(55384),v=t(54662),w=t(89733),N=t(93363),k=t(77209),D=t(78068),I=t(9437);let S=(e,r)=>{let t=e.workFrom?new Date(e.workFrom):null,n=e.workTo?new Date(e.workTo):null;if(t&&n){t>n&&r.addIssue({code:"custom",message:"Work From date cannot be after Work To date.",path:["workFrom"]});let e=new Date(t);e.setMonth(e.getMonth()+1),n<e&&r.addIssue({code:"custom",message:"Work To date must be at least 1 month after Work From date.",path:["workTo"]})}},F=p.z.object({company:p.z.string().min(1,{message:"Company name is required."}),jobTitle:p.z.string().min(1,{message:"Job Title is required."}),workDescription:p.z.string().min(1,{message:"Work Description is required."}),workFrom:p.z.string().min(1,{message:"Work from is required."}),workTo:p.z.string().min(1,{message:"Work to is required."}),referencePersonName:p.z.string().min(1,{message:"Reference Person Name is required."}),referencePersonContact:p.z.string().min(1,{message:"Reference Person Contact is required."}),githubRepoLink:p.z.string().trim().transform(e=>""===e?void 0:e).optional().refine(e=>!e||e.startsWith("https://github.com/"),{message:"GitHub URL must start with https://github.com/"}),comments:p.z.string().optional()}).superRefine((e,r)=>{S(e,r)}),A=e=>{let{onFormSubmit:r}=e,[t,s]=(0,a.useState)(1),[o,l]=(0,a.useState)(!1),[i,c]=(0,a.useState)(!1),d=new Date().toISOString().split("T")[0],m=(0,a.useRef)(null),u=(0,x.cI)({resolver:(0,h.F)(F),defaultValues:{company:"",jobTitle:"",workDescription:"",workFrom:"",workTo:""}}),p=()=>{let{company:e,jobTitle:r,workDescription:t,workFrom:n,workTo:s}=u.getValues();if(!e||!r||!t||!n||!s)return(0,D.Am)({variant:"destructive",title:"Missing fields",description:"Please fill all required fields in Step 1."}),!1;let a=new Date(n),o=new Date(s);if(a>o)return u.setError("workFrom",{type:"manual",message:"Work From date cannot be after Work To date."}),!1;let l=new Date(a);return l.setMonth(l.getMonth()+1),!(o<l)||(u.setError("workTo",{type:"manual",message:"Work To date must be at least 1 month after Work From date."}),!1)};(0,a.useEffect)(()=>{i&&(s(1),u.reset({company:"",jobTitle:"",workDescription:"",workFrom:"",workTo:"",referencePersonName:"",referencePersonContact:"",githubRepoLink:"",comments:""}))},[i,u]);let{showDraftDialog:S,setShowDraftDialog:A,confirmExitDialog:O,setConfirmExitDialog:E,loadDraft:T,discardDraft:C,handleSaveAndClose:Z,handleDiscardAndClose:R,handleDialogClose:P}=(0,I.Z)({form:u,formSection:"experience",isDialogOpen:i,setIsDialogOpen:c,onSave:e=>{m.current={...e}},onDiscard:()=>{m.current=null}});async function z(e){l(!0);try{await f.b.post("/freelancer/experience",{company:e.company||"",jobTitle:e.jobTitle||"",workDescription:e.workDescription||"",workFrom:e.workFrom?new Date(e.workFrom).toISOString():null,workTo:e.workTo?new Date(e.workTo).toISOString():null,referencePersonName:e.referencePersonName||"",referencePersonContact:e.referencePersonContact||"",githubRepoLink:e.githubRepoLink||"",oracleAssigned:null,verificationStatus:"ADDED",verificationUpdateTime:new Date().toISOString(),comments:e.comments||""}),r(),c(!1),(0,D.Am)({title:"Experience Added",description:"The experience has been successfully added."})}catch(e){console.error("API Error:",e),(0,D.Am)({variant:"destructive",title:"Error",description:"Failed to add experience. Please try again later."})}finally{l(!1)}}return(0,n.jsxs)(v.Vq,{open:i,onOpenChange:e=>{c(e),e||P()},children:[(0,n.jsx)(v.hg,{asChild:!0,children:(0,n.jsx)(w.z,{variant:"outline",size:"icon",className:"my-auto",children:(0,n.jsx)(j.Z,{className:"h-4 w-4"})})}),(0,n.jsxs)(v.cZ,{className:"lg:max-w-screen-lg overflow-y-scroll max-h-screen no-scrollbar",children:[(0,n.jsxs)(v.fK,{children:[(0,n.jsxs)(v.$N,{children:["Add Experience - Step ",t," of 2"]}),(0,n.jsx)(v.Be,{children:1===t?"Fill in the basic details of your work experience.":"Fill in the reference and additional details."})]}),(0,n.jsx)(N.l0,{...u,children:(0,n.jsxs)("form",{onSubmit:u.handleSubmit(z),className:"space-y-4",children:[1===t&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(N.Wi,{control:u.control,name:"company",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{children:[(0,n.jsx)(N.lX,{children:"Company"}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(k.I,{placeholder:"Enter company name",...r})}),(0,n.jsx)(N.pf,{children:"Enter the company name"}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:u.control,name:"jobTitle",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{children:[(0,n.jsx)(N.lX,{children:"Job Title"}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(k.I,{placeholder:"Enter job title",...r})}),(0,n.jsx)(N.pf,{children:"Enter the job title"}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:u.control,name:"workDescription",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{children:[(0,n.jsx)(N.lX,{children:"Work Description"}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(k.I,{placeholder:"Enter work description",...r})}),(0,n.jsx)(N.pf,{children:"Enter the work description"}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:u.control,name:"workFrom",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{children:[(0,n.jsx)(N.lX,{children:"Work From"}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(k.I,{type:"date",max:d,...r})}),(0,n.jsx)(N.pf,{children:"Select the start date"}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:u.control,name:"workTo",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{children:[(0,n.jsx)(N.lX,{children:"Work To"}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(k.I,{type:"date",...r})}),(0,n.jsx)(N.pf,{children:"Select the end date"}),(0,n.jsx)(N.zG,{})]})}})]}),2===t&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(N.Wi,{control:u.control,name:"referencePersonName",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{children:[(0,n.jsx)(N.lX,{children:"Reference Person Name"}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(k.I,{placeholder:"Enter reference person name",...r})}),(0,n.jsx)(N.pf,{children:"Enter the reference person's name"}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:u.control,name:"referencePersonContact",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{children:[(0,n.jsx)(N.lX,{children:"Reference Person Contact"}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(k.I,{placeholder:"Enter reference person contact",...r})}),(0,n.jsx)(N.pf,{children:"Enter the reference person's contact"}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:u.control,name:"githubRepoLink",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{children:[(0,n.jsx)(N.lX,{children:"GitHub Repo Link"}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(k.I,{placeholder:"Enter GitHub repository link",...r})}),(0,n.jsx)(N.pf,{children:"Enter the GitHub repository link (optional)"}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:u.control,name:"comments",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{children:[(0,n.jsx)(N.lX,{children:"Comments"}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(k.I,{placeholder:"Enter any comments",...r})}),(0,n.jsx)(N.pf,{children:"Enter any comments (optional)"}),(0,n.jsx)(N.zG,{})]})}})]}),(0,n.jsx)(v.cN,{className:"flex justify-between",children:2===t?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(w.z,{type:"button",variant:"outline",onClick:()=>{2===t&&s(1)},children:[(0,n.jsx)(g.Z,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,n.jsx)(w.z,{type:"submit",disabled:o,children:o?"Loading...":"Add Experience"})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{})," ",(0,n.jsxs)(w.z,{type:"button",onClick:()=>{1===t&&p()&&s(2)},children:["Next",(0,n.jsx)(y.Z,{className:"h-4 w-4 ml-2"})]})]})})]})})]}),O&&(0,n.jsx)(b.Z,{dialogChange:O,setDialogChange:E,heading:"Save Draft?",desc:"Do you want to save your draft before leaving?",handleClose:R,handleSave:Z,btn1Txt:"Don't save",btn2Txt:"Yes save"}),S&&(0,n.jsx)(b.Z,{dialogChange:S,setDialogChange:A,heading:"Load Draft?",desc:"You have unsaved data. Would you like to restore it?",handleClose:C,handleSave:T,btn1Txt:" No, start fresh",btn2Txt:"Yes, load draft"})]})};var O=t(27756),E=t(62688),T=t(2183);function C(){let e=(0,s.v9)(e=>e.user),[r,t]=(0,a.useState)(!1),[l,i]=(0,a.useState)([]),[c,d]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{(async()=>{try{var r,t;d(!0);let n=await f.b.get("/freelancer/".concat(e.uid)),s=null===(t=n.data)||void 0===t?void 0:null===(r=t.data)||void 0===r?void 0:r.professionalInfo;if(!s||"object"!=typeof s){console.warn("No professional experience data found, setting empty array."),i([]);return}i(Object.values(s))}catch(e){(0,D.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."}),console.error("API Error:",e),i([])}finally{d(!1)}})()},[e.uid,r]),(0,n.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,n.jsx)(o.Z,{menuItemsTop:O.y,menuItemsBottom:O.$,active:"Professional Info",isKycCheck:!0}),(0,n.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,n.jsx)(E.Z,{menuItemsTop:O.y,menuItemsBottom:O.$,activeMenu:"Professional Info",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Settings",link:"#"},{label:"Professional Info",link:"#"}]}),(0,n.jsx)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8    grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3",children:c?(0,n.jsx)(Z,{}):(0,n.jsxs)(n.Fragment,{children:[l.map((e,r)=>(0,n.jsx)(u,{...e},r)),(0,n.jsx)(A,{onFormSubmit:()=>{t(e=>!e)}})]})})]})]})}let Z=()=>(0,n.jsxs)(n.Fragment,{children:[Array.from({length:3}).map((e,r)=>(0,n.jsx)(m.Zb,{className:"w-full mx-auto md:max-w-2xl p-4 bg-muted ",children:(0,n.jsxs)(m.aY,{className:"space-y-4",children:[(0,n.jsx)(T.O,{className:"h-6 w-32"}),(0,n.jsx)(T.O,{className:"h-4 w-40"}),(0,n.jsx)(T.O,{className:"h-6 w-16 rounded-md"}),(0,n.jsx)(T.O,{className:"h-4 w-full"}),(0,n.jsx)(T.O,{className:"h-10 w-full rounded-md"}),(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,n.jsx)(T.O,{className:"h-4 w-24"}),(0,n.jsx)(T.O,{className:"h-4 w-20"})]}),(0,n.jsx)(T.O,{className:"h-6 w-40"})]})},r)),(0,n.jsx)(T.O,{className:"h-10 w-10 rounded-md"})]})},68515:function(e,r,t){"use strict";var n=t(57437);t(2265);var s=t(24241);r.Z=e=>{let{startDate:r,endDate:t}=e,a=r?new Date(r).toLocaleDateString():"Start Date N/A",o="current"!==t&&t?new Date(t).toLocaleDateString():"Still Going On!";return(0,n.jsxs)("div",{className:"flex relative whitespace-nowrap items-start sm:items-center gap-1 rounded-md ",children:[(0,n.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2 ",children:[(0,n.jsx)(s.Z,{className:"w-4 h-4 sm:w-5 sm:h-5 "}),(0,n.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:"Start  ".concat(a)})]}),(0,n.jsx)("p",{children:"-"}),(0,n.jsx)("div",{className:"flex items-center ",children:(0,n.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:" ".concat(o)})})]})}},55384:function(e,r,t){"use strict";var n=t(57437);t(2265);var s=t(54662),a=t(89733);r.Z=e=>{let{dialogChange:r,setDialogChange:t,heading:o,desc:l,handleClose:i,handleSave:c,btn1Txt:d,btn2Txt:m}=e;return(0,n.jsx)(s.Vq,{open:r,onOpenChange:t,children:(0,n.jsxs)(s.cZ,{children:[(0,n.jsxs)(s.fK,{children:[(0,n.jsx)(s.$N,{children:o}),(0,n.jsx)(s.Be,{children:l})]}),(0,n.jsxs)(s.cN,{children:[(0,n.jsx)(a.z,{variant:"outline",onClick:i,children:d}),(0,n.jsx)(a.z,{onClick:c,children:m})]})]})})}},93363:function(e,r,t){"use strict";t.d(r,{NI:function(){return p},Wi:function(){return m},l0:function(){return c},lX:function(){return x},pf:function(){return j},xJ:function(){return h},zG:function(){return g}});var n=t(57437),s=t(2265),a=t(63355),o=t(39343),l=t(49354),i=t(70402);let c=o.RV,d=s.createContext({}),m=e=>{let{...r}=e;return(0,n.jsx)(d.Provider,{value:{name:r.name},children:(0,n.jsx)(o.Qr,{...r})})},u=()=>{let e=s.useContext(d),r=s.useContext(f),{getFieldState:t,formState:n}=(0,o.Gc)(),a=t(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=r;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...a}},f=s.createContext({}),h=s.forwardRef((e,r)=>{let{className:t,...a}=e,o=s.useId();return(0,n.jsx)(f.Provider,{value:{id:o},children:(0,n.jsx)("div",{ref:r,className:(0,l.cn)("space-y-2",t),...a})})});h.displayName="FormItem";let x=s.forwardRef((e,r)=>{let{className:t,...s}=e,{error:a,formItemId:o}=u();return(0,n.jsx)(i.Label,{ref:r,className:(0,l.cn)(a&&"text-destructive",t),htmlFor:o,...s})});x.displayName="FormLabel";let p=s.forwardRef((e,r)=>{let{...t}=e,{error:s,formItemId:o,formDescriptionId:l,formMessageId:i}=u();return(0,n.jsx)(a.g7,{ref:r,id:o,"aria-describedby":s?"".concat(l," ").concat(i):"".concat(l),"aria-invalid":!!s,...t})});p.displayName="FormControl";let j=s.forwardRef((e,r)=>{let{className:t,...s}=e,{formDescriptionId:a}=u();return(0,n.jsx)("p",{ref:r,id:a,className:(0,l.cn)("text-sm text-muted-foreground",t),...s})});j.displayName="FormDescription";let g=s.forwardRef((e,r)=>{let{className:t,children:s,...a}=e,{error:o,formMessageId:i}=u(),c=o?String(null==o?void 0:o.message):s;return c?(0,n.jsx)("p",{ref:r,id:i,className:(0,l.cn)("text-sm font-medium text-destructive",t),...a,children:c}):null});g.displayName="FormMessage"},70402:function(e,r,t){"use strict";t.r(r),t.d(r,{Label:function(){return c}});var n=t(57437),s=t(2265),a=t(38364),o=t(12218),l=t(49354);let i=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)(a.f,{ref:r,className:(0,l.cn)(i(),t),...s})});c.displayName=a.f.displayName},2183:function(e,r,t){"use strict";t.d(r,{O:function(){return a}});var n=t(57437),s=t(49354);function a(e){let{className:r,...t}=e;return(0,n.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-md bg-primary/10",r),...t})}},27756:function(e,r,t){"use strict";t.d(r,{$:function(){return u},y:function(){return m}});var n=t(57437),s=t(11005),a=t(52022),o=t(25912),l=t(67524),i=t(6540),c=t(40036),d=t(66648);let m=[{href:"#",icon:(0,n.jsx)(d.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,n.jsx)(s.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/settings/personal-info",icon:(0,n.jsx)(a.Z,{className:"h-5 w-5"}),label:"Personal Info"},{href:"/freelancer/settings/professional-info",icon:(0,n.jsx)(o.Z,{className:"h-5 w-5"}),label:"Professional Info"},{href:"/freelancer/settings/projects",icon:(0,n.jsx)(l.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"/freelancer/settings/education-info",icon:(0,n.jsx)(i.Z,{className:"h-5 w-5"}),label:"Education"},{href:"/freelancer/settings/resume",icon:(0,n.jsx)(c.Z,{className:"h-5 w-5"}),label:"Portfolio"}],u=[]},9437:function(e,r,t){"use strict";var n=t(2265),s=t(86763);r.Z=e=>{let{form:r,formSection:t="",isDialogOpen:a,setIsDialogOpen:o,onSave:l,onDiscard:i,setCurrSkills:c}=e,[d,m]=(0,n.useState)(!1),[u,f]=(0,n.useState)(!1),h=(0,n.useRef)(!1),x=(0,n.useRef)(null),p=e=>{if(!t)return;let r=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");r[t]&&(x.current=r[t]),Object.values(e).some(e=>void 0!==e&&""!==e)&&(r[t]=e,localStorage.setItem("DEHIX_DRAFT",JSON.stringify(r)),(0,s.Am)({title:"Draft Saved",description:"Your ".concat(t," draft has been saved."),duration:1500}))};(0,n.useEffect)(()=>{if(a&&!h.current&&t){let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");e&&e[t]&&m(!0),h.current=!0}},[a,t]);let j=e=>e&&"object"==typeof e?Object.fromEntries(Object.entries(e).map(e=>{let[r,t]=e;return[r,"string"==typeof t?t.trim():t]})):{};return{showDraftDialog:d,setShowDraftDialog:m,confirmExitDialog:u,setConfirmExitDialog:f,loadDraft:()=>{if(!t)return;let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");e&&e[t]&&(Object.keys(e[t]).forEach(r=>{void 0===e[t][r]&&delete e[t][r]}),"projects"===t&&(delete e[t].verificationStatus,Array.isArray(e[t].techUsed)&&c(e[t].techUsed)),Object.entries(e[t]).some(e=>{let[,r]=e;return""!==r&&void 0!==r&&!(Array.isArray(r)&&0===r.length)})&&r&&(r.reset(e[t]),x.current=e[t],(0,s.Am)({title:"Draft Loaded",description:"Your ".concat(t," draft has been restored."),duration:1500})),m(!1))},discardDraft:()=>{if(!t)return;let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");e&&(delete e[t],0===Object.keys(e).length?localStorage.removeItem("DEHIX_DRAFT"):localStorage.setItem("DEHIX_DRAFT",JSON.stringify(e))),null==r||r.reset(),(0,s.Am)({title:"Draft Discarded",description:"Your ".concat(t," draft has been discarded."),duration:1500}),m(!1),i&&i()},handleSaveAndClose:()=>{if(!t)return;let e=null==r?void 0:r.getValues();p(e),(0,s.Am)({title:"Draft Saved",description:"Your draft has been saved.",duration:1500}),x.current=e,f(!1),o&&o(!1),l&&l(e)},handleDiscardAndClose:()=>{if(!t)return;let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");delete e[t],0===Object.keys(e).length?localStorage.removeItem("DEHIX_DRAFT"):localStorage.setItem("DEHIX_DRAFT",JSON.stringify(e)),(0,s.Am)({title:"Draft Discarded",description:"Your ".concat(t," draft has been discarded."),duration:1500}),f(!1),o&&o(!1),i&&i()},handleDialogClose:()=>{if(!a||!t)return;let e=(null==r?void 0:r.getValues())||{},n=x.current||{},s=j(e),l=j(n),i=Object.entries(l).some(e=>{let[r,t]=e,n=s[r];return Array.isArray(t)&&Array.isArray(n)?JSON.stringify(t)!==JSON.stringify(n):t!==n}),c=Object.entries(s).some(e=>{let[r,t]=e;return"verificationStatus"!==r&&void 0!==t&&""!==t&&void 0===l[r]});if(!i&&!c&&o){o(!1);return}Object.values(s).some(e=>null==e?void 0:e.toString().trim())?f(!0):o&&o(!1)},saveDraft:p,hasOtherValues:(0,n.useCallback)(e=>Object.entries(e).some(e=>{let[r,t]=e;return"profiles"!==r&&(Array.isArray(t)&&t.length>0&&("urls"!==r||t.some(e=>{var r;return(null==e?void 0:null===(r=e.value)||void 0===r?void 0:r.trim())!==""}))||"string"==typeof t&&""!==t.trim()||"number"==typeof t&&!isNaN(t))}),[]),hasProfiles:(0,n.useCallback)(e=>null==e?void 0:e.some(e=>Object.values(e).some(e=>Array.isArray(e)&&e.length>0||"string"==typeof e&&""!==e.trim()||"number"==typeof e&&!isNaN(e))),[])}}},38364:function(e,r,t){"use strict";t.d(r,{f:function(){return l}});var n=t(2265),s=t(18676),a=t(57437),o=n.forwardRef((e,r)=>(0,a.jsx)(s.WV.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null===(t=e.onMouseDown)||void 0===t||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));o.displayName="Label";var l=o}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,1374,2455,9726,2688,2971,7023,1744],function(){return e(e.s=48848)}),_N_E=e.O()}]);