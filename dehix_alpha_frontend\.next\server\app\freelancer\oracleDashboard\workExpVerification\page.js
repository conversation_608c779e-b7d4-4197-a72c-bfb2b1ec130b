(()=>{var e={};e.id=8221,e.ids=[8221],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},41790:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>o}),r(73758),r(54302),r(12523);var s=r(23191),a=r(88716),i=r(37922),n=r.n(i),l=r(95231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(t,c);let o=["",{children:["freelancer",{children:["oracleDashboard",{children:["workExpVerification",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,73758)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\oracleDashboard\\workExpVerification\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\oracleDashboard\\workExpVerification\\page.tsx"],x="/freelancer/oracleDashboard/workExpVerification/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/freelancer/oracleDashboard/workExpVerification/page",pathname:"/freelancer/oracleDashboard/workExpVerification",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},86696:(e,t,r)=>{Promise.resolve().then(r.bind(r,81586))},38787:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},12893:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},42887:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},81586:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>S});var s=r(10326),a=r(41137),i=r(23015),n=r(17577),l=r(91664),c=r(24118),o=r(2822),d=r(92166),x=r(40588),m=r(34270),p=r(6260),u=r(12893),h=r(38787);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let f=(0,r(80851).Z)("UserRound",[["circle",{cx:"12",cy:"8",r:"5",key:"1hypcn"}],["path",{d:"M20 21a8 8 0 0 0-16 0",key:"rfgkzh"}]]);var j=r(42887),y=r(40617),g=r(74064),v=r(74723),b=r(27256),N=r(29752),w=r(38443),k=r(9969),_=r(82631),q=r(82015),D=r(56627);let P=b.z.object({type:b.z.enum(["Approved","Denied"],{required_error:"You need to select a type."}),comment:b.z.string().optional()}),C=({_id:e,jobTitle:t,workDescription:r,startFrom:a,company:i,endTo:c,referencePersonName:d,referencePersonContact:x,githubRepoLink:m,comments:b,status:C,onStatusUpdate:E,onCommentUpdate:S})=>{let[V,M]=(0,n.useState)(C),Z=(0,v.cI)({resolver:(0,g.F)(P)}),z=Z.watch("type");async function A(t){try{await p.b.put(`/verification/${e}/oracle?doc_type=experience`,{comments:t.comment,verification_status:t.type})}catch(e){(0,D.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}M(t.type),E(t.type),S(t.comment||"")}return(0,n.useEffect)(()=>{M(C)},[C]),(0,s.jsxs)(N.Zb,{className:"max-w-full mx-auto md:min-w-[30vw]",children:[(0,s.jsxs)(N.Ol,{children:[(0,s.jsxs)(N.ll,{className:"flex justify-between",children:[s.jsx("span",{children:t}),m&&s.jsx("a",{href:m,target:"_blank",rel:"noopener noreferrer",className:"text-sm text-white underline ml-auto",children:s.jsx(u.Z,{})})]}),(0,s.jsxs)(N.SZ,{className:"text-justify text-gray-600",children:["Pending"===V?s.jsx(w.C,{className:"bg-warning-foreground text-white my-2",children:"Pending"}):"Approved"===V?s.jsx(w.C,{className:"bg-success text-white my-2",children:"Approved"}):s.jsx(w.C,{className:"bg-red-500 text-white my-2",children:"Denied"}),s.jsx("br",{}),r]})]}),s.jsx(N.aY,{children:(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsxs)("div",{className:"mt-4",children:[s.jsx("p",{className:"mt-4 mb-3 text-m text-gray-600 flex items-center",children:(0,s.jsxs)("span",{className:"flex",children:[s.jsx(h.Z,{className:"mr-2"}),i]})}),(0,s.jsxs)(_.u,{children:[s.jsx(_.aJ,{asChild:!0,children:(0,s.jsxs)("p",{className:"text-sm text-gray-600 flex items-center",children:[s.jsx(f,{className:"mr-2"}),d]})}),s.jsx(_._v,{side:"bottom",children:d})]}),(0,s.jsxs)(_.u,{children:[s.jsx(_.aJ,{asChild:!0,children:(0,s.jsxs)("p",{className:"text-sm text-gray-600 flex items-center mt-2",children:[s.jsx(j.Z,{className:"mr-2"}),x]})}),s.jsx(_._v,{side:"bottom",children:x})]})]}),b&&(0,s.jsxs)("p",{className:"mt-2 flex items-center text-gray-500 border p-3 rounded",children:[s.jsx(y.Z,{className:"mr-2"}),b]})]})}),(0,s.jsxs)(N.eW,{className:"flex flex-col items-center",children:[(0,s.jsxs)("div",{className:"flex gap-4 text-gray-500",children:[new Date(a).toLocaleDateString()," -"," ","current"!==c?new Date(c).toLocaleDateString():"Current"]}),"Pending"===V&&s.jsx(k.l0,{...Z,children:(0,s.jsxs)("form",{onSubmit:Z.handleSubmit(A),className:"w-full space-y-6 mt-6",children:[s.jsx(k.Wi,{control:Z.control,name:"type",render:({field:e})=>(0,s.jsxs)(k.xJ,{className:"space-y-3",children:[s.jsx(k.lX,{children:"Choose Verification Status:"}),s.jsx(k.NI,{children:(0,s.jsxs)(o.E,{onValueChange:e.onChange,defaultValue:e.value,className:"flex flex-col space-y-1",children:[(0,s.jsxs)(k.xJ,{className:"flex items-center space-x-3",children:[s.jsx(k.NI,{children:s.jsx(o.m,{value:"Approved"})}),s.jsx(k.lX,{className:"font-normal",children:"Approved"})]}),(0,s.jsxs)(k.xJ,{className:"flex items-center space-x-3",children:[s.jsx(k.NI,{children:s.jsx(o.m,{value:"Denied"})}),s.jsx(k.lX,{className:"font-normal",children:"Denied"})]})]})}),s.jsx(k.zG,{})]})}),s.jsx(k.Wi,{control:Z.control,name:"comment",render:({field:e})=>(0,s.jsxs)(k.xJ,{children:[s.jsx(k.lX,{children:"Comments:"}),s.jsx(k.NI,{children:s.jsx(q.g,{placeholder:"Enter comments:",...e})}),s.jsx(k.zG,{})]})}),s.jsx(l.z,{type:"submit",className:"w-full",disabled:!z||Z.formState.isSubmitting,children:"Submit"})]})})]})]})};var E=r(39958);function S(){let[e,t]=(0,n.useState)([]),[r,u]=(0,n.useState)("all"),[h,f]=(0,n.useState)(!1),j=e=>{u(e),f(!1)},y=e.filter(e=>"all"===r||e.verificationStatus===r||"current"===r&&e.verificationStatus===E.sB.PENDING);(0,n.useCallback)(async()=>{try{let e=(await p.b.get("/verification/oracle?doc_type=experience")).data.data.flatMap(e=>e.result?.projects?Object.values(e.result.projects).map(t=>({...t,verifier_id:e.verifier_id,verifier_username:e.verifier_username})):[]);t(e)}catch(e){(0,D.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.log(e,"error in getting verification data")}},[]);let g=(r,s)=>{let a=[...e];a[r].verificationStatus=s,t(a)},v=(r,s)=>{let a=[...e];a[r].comments=s,t(a)};return(0,s.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[s.jsx(d.Z,{menuItemsTop:m.y,menuItemsBottom:m.$,active:"Experience Verification"}),(0,s.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[s.jsx(x.Z,{menuItemsTop:m.y,menuItemsBottom:m.$,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Oracle",link:"#"},{label:"Experience Verification",link:"#"}]}),s.jsx("div",{className:"mb-8 ml-4 flex justify-between mt-8 md:mt-4 items-center",children:(0,s.jsxs)("div",{className:"mb-8 ",children:[(0,s.jsxs)("div",{className:"mb-8 ml-4 flex justify-between mt-8 md:mt-4 items-center",children:[(0,s.jsxs)("div",{className:"mb-8 ",children:[s.jsx("h1",{className:"text-3xl font-bold",children:"Experience Verification"}),s.jsx("p",{className:"text-gray-400 mt-2",children:"Stay updated on your work experience verification status. Check back regularly for any new updates or requirements."})]}),s.jsx(l.z,{variant:"outline",size:"icon",className:"mr-8 mb-12",onClick:()=>f(!0),children:s.jsx(a.Z,{className:"h-4 w-4"})})]}),s.jsx(c.Vq,{open:h,onOpenChange:f,children:(0,s.jsxs)(c.cZ,{children:[s.jsx(c.fK,{children:s.jsx(c.$N,{children:"Filter Experience Status"})}),(0,s.jsxs)(o.E,{defaultValue:"all",value:r,onValueChange:e=>j(e),className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(o.m,{value:"all",id:"filter-all"}),s.jsx("label",{htmlFor:"filter-all",children:"All"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(o.m,{value:"current",id:"filter-current"}),s.jsx("label",{htmlFor:"filter-current",children:"Pending"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(o.m,{value:"verified",id:"filter-verified"}),s.jsx("label",{htmlFor:"filter-verified",children:"Verified"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(o.m,{value:"rejected",id:"filter-rejected"}),s.jsx("label",{htmlFor:"filter-rejected",children:"Rejected"})]})]}),s.jsx(c.cN,{children:s.jsx(l.z,{type:"button",onClick:()=>f(!1),children:"Close"})})]})}),(0,s.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8    grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3",children:[y.map((e,t)=>s.jsx(C,{_id:e._id,jobTitle:e.jobTitle,workDescription:e.workDescription,company:e.company,startFrom:e.workFrom,endTo:e.workTo,referencePersonName:e.referencePersonName,referencePersonContact:e.referencePersonContact,githubRepoLink:e.githubRepoLink,comments:e.comments,status:e.verificationStatus,onStatusUpdate:e=>g(t,e),onCommentUpdate:e=>v(t,e)},t)),0==e.length?(0,s.jsxs)("div",{className:"text-center w-[90vw] px-auto mt-20 py-10",children:[s.jsx(i.Z,{className:"mx-auto text-gray-500",size:"100"}),s.jsx("p",{className:"text-gray-500",children:"No Work Experience verification for you now."})]}):null]})]})})]})]})}},73758:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var s=r(68570);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\oracleDashboard\workExpVerification\page.tsx`),{__esModule:i,$$typeof:n}=a;a.default;let l=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\oracleDashboard\workExpVerification\page.tsx#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,6686,4736,6499,8066,588,3379],()=>r(41790));module.exports=s})();