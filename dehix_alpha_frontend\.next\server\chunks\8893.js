"use strict";exports.id=8893,exports.ids=[8893],exports.modules={47546:(e,a,n)=>{n.d(a,{Z:()=>d});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,n(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},941:(e,a,n)=>{n.d(a,{Z:()=>d});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,n(80851).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},66307:(e,a,n)=>{n.d(a,{Z:()=>d});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,n(80851).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},45691:(e,a,n)=>{n.d(a,{Z:()=>d});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,n(80851).Z)("Rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]])},58038:(e,a,n)=>{n.d(a,{Z:()=>d});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,n(80851).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},57776:(e,a,n)=>{n.d(a,{Z:()=>l});var d=n(10326);n(17577);var t=n(89124),i=n(29280),m=n(9969),o=n(41190);let l=({code:e,setCode:a,control:n})=>{let l=t.find(a=>a.code===e)||t[0],r=e=>{a(e)};return d.jsx(m.Wi,{control:n,name:"phone",render:({field:a})=>(0,d.jsxs)(m.xJ,{className:"flex-1",children:[d.jsx("div",{className:"flex flex-col items-start w-full",children:(0,d.jsxs)("div",{className:"flex items-center w-full space-x-2",children:[(0,d.jsxs)(i.Ph,{onValueChange:r,value:e,children:[d.jsx(i.i4,{className:"w-[75px] border",children:d.jsx("span",{children:l.dialCode})}),d.jsx(i.Bw,{children:d.jsx(i.DI,{children:t.map(e=>(0,d.jsxs)(i.Ql,{value:e.code,children:[e.name," (",e.dialCode,")"]},e.code))})})]}),d.jsx(m.NI,{children:d.jsx(o.I,{placeholder:"Enter your phone number",type:"text",...a,className:"w-full"})})]})}),d.jsx(m.zG,{})]})})}},26408:(e,a,n)=>{n.d(a,{Z:()=>o});var d=n(10326),t=n(9969),i=n(41190),m=n(44794);let o=({control:e,name:a,label:n,placeholder:o="Enter value",type:l="text",description:r="",className:s=""})=>(0,d.jsxs)("div",{className:"space-y-2",children:[d.jsx(m.Label,{children:n}),d.jsx(t.Wi,{control:e,name:a,render:({field:e})=>(0,d.jsxs)(t.xJ,{children:[d.jsx(t.NI,{children:d.jsx(i.I,{placeholder:o,type:l,...e,className:`p-2 border rounded-md ${s}`,onChange:a=>{let n=a.target.value;"number"===l?e.onChange(n?parseFloat(n):""):e.onChange(n)},value:"number"===l?e.value??"":e.value})}),d.jsx(t.pf,{children:r}),d.jsx(t.zG,{})]})})]})},9969:(e,a,n)=>{n.d(a,{NI:()=>p,Wi:()=>c,l0:()=>r,lX:()=>L,pf:()=>u,xJ:()=>x,zG:()=>f});var d=n(10326),t=n(17577),i=n(99469),m=n(74723),o=n(51223),l=n(44794);let r=m.RV,s=t.createContext({}),c=({...e})=>d.jsx(s.Provider,{value:{name:e.name},children:d.jsx(m.Qr,{...e})}),h=()=>{let e=t.useContext(s),a=t.useContext(g),{getFieldState:n,formState:d}=(0,m.Gc)(),i=n(e.name,d);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=a;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...i}},g=t.createContext({}),x=t.forwardRef(({className:e,...a},n)=>{let i=t.useId();return d.jsx(g.Provider,{value:{id:i},children:d.jsx("div",{ref:n,className:(0,o.cn)("space-y-2",e),...a})})});x.displayName="FormItem";let L=t.forwardRef(({className:e,...a},n)=>{let{error:t,formItemId:i}=h();return d.jsx(l.Label,{ref:n,className:(0,o.cn)(t&&"text-destructive",e),htmlFor:i,...a})});L.displayName="FormLabel";let p=t.forwardRef(({...e},a)=>{let{error:n,formItemId:t,formDescriptionId:m,formMessageId:o}=h();return d.jsx(i.g7,{ref:a,id:t,"aria-describedby":n?`${m} ${o}`:`${m}`,"aria-invalid":!!n,...e})});p.displayName="FormControl";let u=t.forwardRef(({className:e,...a},n)=>{let{formDescriptionId:t}=h();return d.jsx("p",{ref:n,id:t,className:(0,o.cn)("text-sm text-muted-foreground",e),...a})});u.displayName="FormDescription";let f=t.forwardRef(({className:e,children:a,...n},t)=>{let{error:i,formMessageId:m}=h(),l=i?String(i?.message):a;return l?d.jsx("p",{ref:t,id:m,className:(0,o.cn)("text-sm font-medium text-destructive",e),...n,children:l}):null});f.displayName="FormMessage"},29280:(e,a,n)=>{n.d(a,{Bw:()=>p,DI:()=>c,Ph:()=>s,Ql:()=>u,i4:()=>g,ki:()=>h});var d=n(10326),t=n(17577),i=n(75334),m=n(941),o=n(96633),l=n(32933),r=n(51223);let s=i.fC,c=i.ZA,h=i.B4,g=t.forwardRef(({className:e,children:a,...n},t)=>(0,d.jsxs)(i.xz,{ref:t,className:(0,r.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...n,children:[a,d.jsx(i.JO,{asChild:!0,children:d.jsx(m.Z,{className:"h-4 w-4 opacity-50"})})]}));g.displayName=i.xz.displayName;let x=t.forwardRef(({className:e,...a},n)=>d.jsx(i.u_,{ref:n,className:(0,r.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:d.jsx(o.Z,{className:"h-4 w-4"})}));x.displayName=i.u_.displayName;let L=t.forwardRef(({className:e,...a},n)=>d.jsx(i.$G,{ref:n,className:(0,r.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:d.jsx(m.Z,{className:"h-4 w-4"})}));L.displayName=i.$G.displayName;let p=t.forwardRef(({className:e,children:a,position:n="popper",...t},m)=>d.jsx(i.h_,{children:(0,d.jsxs)(i.VY,{ref:m,className:(0,r.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...t,children:[d.jsx(x,{}),d.jsx(i.l_,{className:(0,r.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),d.jsx(L,{})]})}));p.displayName=i.VY.displayName,t.forwardRef(({className:e,...a},n)=>d.jsx(i.__,{ref:n,className:(0,r.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...a})).displayName=i.__.displayName;let u=t.forwardRef(({className:e,children:a,...n},t)=>(0,d.jsxs)(i.ck,{ref:t,className:(0,r.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[d.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:d.jsx(i.wU,{children:d.jsx(l.Z,{className:"h-4 w-4"})})}),d.jsx(i.eT,{children:a})]}));u.displayName=i.ck.displayName,t.forwardRef(({className:e,...a},n)=>d.jsx(i.Z0,{ref:n,className:(0,r.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=i.Z0.displayName},53405:(e,a,n)=>{n.d(a,{D:()=>t});var d=n(17577);function t(e){let a=d.useRef({value:e,previous:e});return d.useMemo(()=>(a.current.value!==e&&(a.current.previous=a.current.value,a.current.value=e),a.current.previous),[e])}},89124:e=>{e.exports=JSON.parse('[{"code":"AF","name":"Afghanistan","dialCode":"+93","minLength":9,"maxLength":10},{"code":"AR","name":"Argentina","dialCode":"+54","minLength":10,"maxLength":10},{"code":"AU","name":"Australia","dialCode":"+61","minLength":9,"maxLength":9},{"code":"AT","name":"Austria","dialCode":"+43","minLength":10,"maxLength":10},{"code":"BD","name":"Bangladesh","dialCode":"+880","minLength":10,"maxLength":10},{"code":"BE","name":"Belgium","dialCode":"+32","minLength":9,"maxLength":9},{"code":"BR","name":"Brazil","dialCode":"+55","minLength":10,"maxLength":11},{"code":"BN","name":"Brunei","dialCode":"+673","minLength":7,"maxLength":7},{"code":"BT","name":"Bhutan","dialCode":"+975","minLength":9,"maxLength":10},{"code":"KH","name":"Cambodia","dialCode":"+855","minLength":9,"maxLength":10},{"code":"CA","name":"Canada","dialCode":"+1","minLength":10,"maxLength":10},{"code":"CN","name":"China","dialCode":"+86","minLength":11,"maxLength":11},{"code":"DK","name":"Denmark","dialCode":"+45","minLength":8,"maxLength":8},{"code":"EG","name":"Egypt","dialCode":"+20","minLength":9,"maxLength":10},{"code":"FI","name":"Finland","dialCode":"+358","minLength":10,"maxLength":10},{"code":"FR","name":"France","dialCode":"+33","minLength":9,"maxLength":9},{"code":"DE","name":"Germany","dialCode":"+49","minLength":10,"maxLength":11},{"code":"GR","name":"Greece","dialCode":"+30","minLength":10,"maxLength":10},{"code":"GH","name":"Ghana","dialCode":"+233","minLength":9,"maxLength":10},{"code":"HK","name":"Hong Kong","dialCode":"+852","minLength":8,"maxLength":8},{"code":"IN","name":"India","dialCode":"+91","minLength":10,"maxLength":10},{"code":"ID","name":"Indonesia","dialCode":"+62","minLength":10,"maxLength":12},{"code":"IR","name":"Iran","dialCode":"+98","minLength":10,"maxLength":10},{"code":"IQ","name":"Iraq","dialCode":"+964","minLength":9,"maxLength":10},{"code":"IE","name":"Ireland","dialCode":"+353","minLength":9,"maxLength":9},{"code":"IL","name":"Israel","dialCode":"+972","minLength":9,"maxLength":10},{"code":"IT","name":"Italy","dialCode":"+39","minLength":9,"maxLength":10},{"code":"JP","name":"Japan","dialCode":"+81","minLength":10,"maxLength":10},{"code":"KE","name":"Kenya","dialCode":"+254","minLength":9,"maxLength":10},{"code":"KR","name":"South Korea","dialCode":"+82","minLength":10,"maxLength":11},{"code":"LA","name":"Laos","dialCode":"+856","minLength":9,"maxLength":10},{"code":"MY","name":"Malaysia","dialCode":"+60","minLength":9,"maxLength":10},{"code":"MV","name":"Maldives","dialCode":"+960","minLength":7,"maxLength":7},{"code":"MX","name":"Mexico","dialCode":"+52","minLength":10,"maxLength":10},{"code":"MN","name":"Mongolia","dialCode":"+976","minLength":9,"maxLength":10},{"code":"MM","name":"Myanmar","dialCode":"+95","minLength":9,"maxLength":10},{"code":"NP","name":"Nepal","dialCode":"+977","minLength":10,"maxLength":10},{"code":"NL","name":"Netherlands","dialCode":"+31","minLength":9,"maxLength":9},{"code":"NZ","name":"New Zealand","dialCode":"+64","minLength":9,"maxLength":10},{"code":"NG","name":"Nigeria","dialCode":"+234","minLength":10,"maxLength":10},{"code":"NO","name":"Norway","dialCode":"+47","minLength":8,"maxLength":8},{"code":"PK","name":"Pakistan","dialCode":"+92","minLength":10,"maxLength":11},{"code":"PH","name":"Philippines","dialCode":"+63","minLength":10,"maxLength":10},{"code":"PL","name":"Poland","dialCode":"+48","minLength":9,"maxLength":9},{"code":"PT","name":"Portugal","dialCode":"+351","minLength":9,"maxLength":9},{"code":"RU","name":"Russia","dialCode":"+7","minLength":10,"maxLength":10},{"code":"SA","name":"Saudi Arabia","dialCode":"+966","minLength":9,"maxLength":9},{"code":"SG","name":"Singapore","dialCode":"+65","minLength":8,"maxLength":8},{"code":"ZA","name":"South Africa","dialCode":"+27","minLength":9,"maxLength":9},{"code":"ES","name":"Spain","dialCode":"+34","minLength":9,"maxLength":9},{"code":"SE","name":"Sweden","dialCode":"+46","minLength":9,"maxLength":9},{"code":"CH","name":"Switzerland","dialCode":"+41","minLength":9,"maxLength":9},{"code":"TZ","name":"Tanzania","dialCode":"+255","minLength":9,"maxLength":10},{"code":"TH","name":"Thailand","dialCode":"+66","minLength":9,"maxLength":10},{"code":"TR","name":"Turkey","dialCode":"+90","minLength":10,"maxLength":11},{"code":"UG","name":"Uganda","dialCode":"+256","minLength":9,"maxLength":10},{"code":"UA","name":"Ukraine","dialCode":"+380","minLength":9,"maxLength":9},{"code":"AE","name":"United Arab Emirates","dialCode":"+971","minLength":9,"maxLength":9},{"code":"GB","name":"United Kingdom","dialCode":"+44","minLength":10,"maxLength":11},{"code":"US","name":"United States","dialCode":"+1","minLength":10,"maxLength":10},{"code":"VN","name":"Vietnam","dialCode":"+84","minLength":9,"maxLength":10}]')}};