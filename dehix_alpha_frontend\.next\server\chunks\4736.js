exports.id=4736,exports.ids=[4736],exports.modules={59709:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},96364:(e,t,r)=>{Promise.resolve().then(r.bind(r,52792)),Promise.resolve().then(r.bind(r,49770)),Promise.resolve().then(r.bind(r,31062)),Promise.resolve().then(r.bind(r,40422)),Promise.resolve().then(r.bind(r,82631)),Promise.resolve().then(r.bind(r,73441))},70164:(e,t,r)=>{Promise.resolve().then(r.bind(r,83846))},52792:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>p,a:()=>h});var s=r(10326),a=r(17577),o=r(25842),i=r(35047),n=r(4594),d=r(6260),l=r(45722);let c=(0,a.createContext)({user:null,loading:!0}),u=e=>localStorage.getItem(e),f=(e,t)=>localStorage.setItem(e,t),m=e=>localStorage.removeItem(e),p=({children:e})=>{let t=(0,i.useRouter)(),[r,p]=(0,a.useState)(null),[h,x]=(0,a.useState)(!0),g=(0,o.I0)();return(0,a.useEffect)(()=>{let e=u("user"),r=u("token");if(e&&r)try{let t=JSON.parse(e);p(t),(0,d.q)(r),g((0,n.av)(t))}catch(e){console.error("Failed to parse user:",e),m("user"),m("token")}let s=l.I8.onIdTokenChanged(async e=>{if(e&&navigator.onLine)try{let t=await e.getIdToken();if(t){let r=await e.getIdTokenResult(),s={...e,type:r.claims.type};f("user",JSON.stringify(s)),f("token",t),p(s),(0,d.q)(t),g((0,n.av)(s))}}catch(e){console.error("Token Refresh Error:",e)}else m("user"),m("token"),p(null),g((0,n.pn)()),t.replace("/auth/login")});return x(!1),()=>s()},[g,t]),s.jsx(c.Provider,{value:{user:r,loading:h},children:!h&&e})},h=()=>(0,a.useContext)(c)},83846:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(10326),a=r(17577),o=r(94935),i=r(86333),n=r(35047),d=r(66562),l=r(29752),c=r(91664);let u=()=>{let e=(0,n.useRouter)(),[t,r]=(0,a.useState)(null);return((0,a.useEffect)(()=>{r(!!d.Z.get("token"))},[]),null===t)?null:s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:(0,s.jsxs)(l.Zb,{className:"w-full max-w-md shadow-lg",children:[(0,s.jsxs)(l.Ol,{className:"text-center space-y-2",children:[s.jsx("div",{className:"flex justify-center mb-4",children:s.jsx(o.Z,{className:"h-16 w-16 text-primary animate-pulse"})}),s.jsx(l.ll,{className:"text-4xl font-extrabold",children:"404"}),s.jsx("p",{className:"text-2xl font-semibold text-muted-foreground",children:"Page Not Found"})]}),(0,s.jsxs)(l.aY,{className:"text-center space-y-4",children:[s.jsx("p",{className:"text-lg text-muted-foreground",children:"We were unable to find the page you are looking for."}),s.jsx("div",{className:"border-t border-border pt-4",children:s.jsx("p",{className:"text-sm text-muted-foreground",children:"The page might have been moved, deleted, or never existed."})})]}),s.jsx(l.eW,{className:"flex justify-center",children:(0,s.jsxs)(c.z,{size:"lg",onClick:()=>{if(t)try{window.history.back()}catch{e.push("/")}else e.push("/auth/login")},className:"w-full sm:w-auto gap-2",children:[s.jsx(i.Z,{className:"h-4 w-4"}),t?"Go Back":"Login"]})})]})})}},49770:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var s=r(10326),a=r(17577),o=r(25842),i=r(73146),n=r(4594),d=r(60502);let l=()=>(0,i.xC)({reducer:{user:n.ZP,projectDraft:d.ZP}});function c({children:e}){let t=(0,a.useRef)();return t.current||(t.current=l()),s.jsx(o.zt,{store:t.current,children:e})}},31062:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>o});var s=r(10326);r(17577);var a=r(14831);function o({children:e,...t}){return s.jsx(a.f,{...t,children:e})}},91664:(e,t,r)=>{"use strict";r.d(t,{d:()=>d,z:()=>l});var s=r(10326),a=r(17577),o=r(99469),i=r(28671),n=r(51223);let d=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...i},l)=>{let c=a?o.g7:"button";return s.jsx(c,{className:(0,n.cn)(d({variant:t,size:r,className:e})),ref:l,...i})});l.displayName="Button"},29752:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>n,SZ:()=>l,Zb:()=>i,aY:()=>c,eW:()=>u,ll:()=>d});var s=r(10326),a=r(17577),o=r(51223);let i=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let n=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...t}));n.displayName="CardHeader";let d=a.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let l=a.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));l.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,o.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,o.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},40422:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>g});var s=r(10326),a=r(17577),o=r(10321),i=r(28671),n=r(94019),d=r(51223);let l=o.zt,c=a.forwardRef(({className:e,...t},r)=>s.jsx(o.l_,{ref:r,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));c.displayName=o.l_.displayName;let u=(0,i.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),f=a.forwardRef(({className:e,variant:t,...r},a)=>s.jsx(o.fC,{ref:a,className:(0,d.cn)(u({variant:t}),e),...r}));f.displayName=o.fC.displayName,a.forwardRef(({className:e,...t},r)=>s.jsx(o.aU,{ref:r,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=o.aU.displayName;let m=a.forwardRef(({className:e,...t},r)=>s.jsx(o.x8,{ref:r,className:(0,d.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:s.jsx(n.Z,{className:"h-4 w-4"})}));m.displayName=o.x8.displayName;let p=a.forwardRef(({className:e,...t},r)=>s.jsx(o.Dx,{ref:r,className:(0,d.cn)("text-sm font-semibold",e),...t}));p.displayName=o.Dx.displayName;let h=a.forwardRef(({className:e,...t},r)=>s.jsx(o.dk,{ref:r,className:(0,d.cn)("text-sm opacity-90",e),...t}));h.displayName=o.dk.displayName;var x=r(56627);function g(){let{toasts:e}=(0,x.pm)();return(0,s.jsxs)(l,{children:[e.map(function({id:e,title:t,description:r,action:a,...o}){return(0,s.jsxs)(f,{...o,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[t&&s.jsx(p,{children:t}),r&&s.jsx(h,{children:r})]}),a,s.jsx(m,{})]},e)}),s.jsx(c,{})]})}},82631:(e,t,r)=>{"use strict";r.d(t,{TooltipProvider:()=>n,_v:()=>c,aJ:()=>l,u:()=>d});var s=r(10326),a=r(17577),o=r(6218),i=r(51223);let n=o.zt,d=o.fC,l=o.xz,c=a.forwardRef(({className:e,sideOffset:t=4,...r},a)=>s.jsx(o.VY,{ref:a,sideOffset:t,className:(0,i.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r}));c.displayName=o.VY.displayName},56627:(e,t,r)=>{"use strict";r.d(t,{Am:()=>u,pm:()=>f});var s=r(17577);let a=0,o=new Map,i=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],l={toasts:[]};function c(e){l=n(l,e),d.forEach(e=>{e(l)})}function u({...e}){let t=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function f(){let[e,t]=s.useState(l);return s.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},45722:(e,t,r)=>{"use strict";r.d(t,{I8:()=>c,Vv:()=>u,db:()=>l});var s=r(42585),a=r(7558),o=r(18558),i=r(76),n=r(11552);let d=(0,s.ZF)({apiKey:"AIzaSyBPTH9xikAUkgGof048klY6WGiZSmRoXXA",authDomain:"dehix-6c349.firebaseapp.com",databaseURL:"https://dehix-6c349-default-rtdb.firebaseio.com",projectId:"dehix-6c349",storageBucket:"dehix-6c349.appspot.com",messagingSenderId:"521082542540",appId:"1:521082542540:web:543857e713038c2927a569"}),l=(0,i.ad)(d),c=(0,a.v0)(d);c.useDeviceLanguage();let u=new a.hJ;(0,o.N8)(d),(0,n.cF)(d)},6260:(e,t,r)=>{"use strict";r.d(t,{b:()=>a,q:()=>o});var s=r(44099);let a=s.Z.create({baseURL:"http://127.0.0.1:8080/"});console.log("Base URL:","http://127.0.0.1:8080/");let o=e=>{console.log("Initializing Axios with token:",e),a=s.Z.create({baseURL:"http://127.0.0.1:8080/",headers:{Authorization:`Bearer ${e}`}})};a.interceptors.request.use(e=>(console.log("Request config:",e),e),e=>(console.error("Request error:",e),Promise.reject(e))),a.interceptors.response.use(e=>(console.log("Response:",e.data),e),e=>(console.error("Response error:",e),Promise.reject(e)))},60502:(e,t,r)=>{"use strict";r.d(t,{Dj:()=>n,MR:()=>o,ZP:()=>d,kd:()=>a});let s=(0,r(73146).oM)({name:"projectDraft",initialState:{draftedProjects:[]},reducers:{addDraftedProject:(e,t)=>{e.draftedProjects.includes(t.payload)||e.draftedProjects.push(t.payload)},removeDraftedProject:(e,t)=>{e.draftedProjects=e.draftedProjects.filter(e=>e!==t.payload)},clearDraftedProjects:e=>{e.draftedProjects=[]},setDraftedProjects:(e,t)=>{e.draftedProjects=t.payload}}}),{addDraftedProject:a,removeDraftedProject:o,clearDraftedProjects:i,setDraftedProjects:n}=s.actions,d=s.reducer},4594:(e,t,r)=>{"use strict";r.d(t,{ZP:()=>i,av:()=>a,pn:()=>o});let s=(0,r(73146).oM)({name:"user",initialState:{},reducers:{setUser:(e,t)=>({...e,...t.payload}),clearUser:()=>({})}}),{setUser:a,clearUser:o}=s.actions,i=s.reducer},51223:(e,t,r)=>{"use strict";r.d(t,{bx:()=>f,c0:()=>c,cn:()=>l,is:()=>m,pH:()=>u});var s=r(41135),a=r(31009),o=r(7558),i=r(66562),n=r(6260),d=r(45722);function l(...e){return(0,a.m6)((0,s.W)(e))}let c=async e=>{try{await (0,o.LS)(d.I8,e),console.log("Password reset email sent successfully.")}catch(r){let e=r.code,t=r.message;throw console.log(e,t),Error(t)}},u=async(e,t)=>{try{return await (0,o.e5)(d.I8,e,t)}catch(r){let e=r.code,t=r.message;throw console.log(e,t),Error(t)}},f=async()=>await (0,o.rh)(d.I8,d.Vv),m=async e=>{try{let t=e.user,r=await t.getIdToken();(0,n.q)(r);let s=(await t.getIdTokenResult()).claims,a={uid:t.uid,email:t.email,displayName:t.displayName,phoneNumber:t.phoneNumber,photoURL:t.photoURL,emailVerified:t.emailVerified},o=s.type;return i.Z.set("userType",o,{expires:1,path:"/"}),i.Z.set("token",r,{expires:1,path:"/"}),{user:a,claims:s}}catch(e){throw console.error("Error fetching user data:",e),e}}},73441:(e,t,r)=>{"use strict";r.d(t,{default:()=>f});var s=r(10326),a=r(17577);function o(){let[e,t]=(0,a.useState)(!0);return e}var i=r(14831),n=r(60850),d=r(72607),l=r(62783),c=r(21405);let u=function(){let e=o(),[t,r]=(0,a.useState)(!1),{setTheme:u,resolvedTheme:f}=(0,i.F)(),m="dark"===f;return(0,s.jsxs)("div",{className:`min-h-screen transition-colors duration-300 ${m?"bg-black":"bg-gradient-to-br from-gray-50 to-white"} flex items-center justify-center p-4`,children:[s.jsx("button",{onClick:()=>{u("dark"===f?"light":"dark")},className:`absolute top-4 right-4 p-3 rounded-full transition-all duration-300 ${m?"bg-zinc-900 hover:bg-zinc-800":"bg-white hover:bg-gray-100"} shadow-lg`,children:m?s.jsx(n.Z,{className:"w-6 h-6"}):s.jsx(d.Z,{className:"w-6 h-6"})}),s.jsx("div",{className:`max-w-md w-full transform transition-all duration-500 ${e?"scale-0 opacity-0":"scale-100 opacity-100"}`,children:s.jsx("div",{className:`${m?"bg-zinc-900/80 backdrop-blur-lg border-zinc-800":"bg-white border-gray-200"} rounded-2xl p-8 shadow-2xl border transition-colors duration-300`,children:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center space-y-6",children:[(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:`absolute inset-0 animate-ping rounded-full ${m?"bg-red-500":"bg-red-400"} opacity-20`}),s.jsx("div",{className:"relative",children:s.jsx(l.Z,{className:`w-16 h-16 ${m?"text-red-500":"text-red-400"} animate-pulse`})})]}),s.jsx("h1",{className:`text-3xl font-bold mt-4 ${m?"text-zinc-100":"text-gray-900"}`,children:"You‘re Offline"}),s.jsx("p",{className:`text-lg ${m?"text-zinc-400":"text-gray-600"}`,children:"Please check your internet connection and try again"}),s.jsx("div",{className:`w-full h-2 ${m?"bg-zinc-800":"bg-gray-200"} rounded-full overflow-hidden`,children:s.jsx("div",{className:`h-full ${m?"bg-red-500":"bg-red-400"} animate-[shimmer_2s_infinite]`})}),(0,s.jsxs)("button",{onClick:()=>{r(!0),navigator.onLine?window.location.reload():setTimeout(()=>{r(!1)},1500)},className:`group relative inline-flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 active:scale-95 ${m?"bg-zinc-100 text-zinc-900 hover:bg-white":"bg-gray-900 text-white hover:bg-gray-800"}`,children:[s.jsx(c.Z,{className:`w-5 h-5 transition-transform ${t?"animate-spin":"group-hover:rotate-180"}`}),"Try Again"]})]})})})]})};function f({children:e}){let t=o();return s.jsx(s.Fragment,{children:t?e:s.jsx(u,{})})}},54302:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>U,metadata:()=>R});var s=r(19510),a=r(25384),o=r.n(a);r(5023);var i=r(68570);let n=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\storeProvider.tsx`),{__esModule:d,$$typeof:l}=n;n.default;let c=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\storeProvider.tsx#default`),u=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\AuthContext.tsx`),{__esModule:f,$$typeof:m}=u;u.default;let p=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\AuthContext.tsx#AuthProvider`);(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\AuthContext.tsx#useAuth`);let h=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\theme-provider.tsx`),{__esModule:x,$$typeof:g}=h;h.default;let v=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\theme-provider.tsx#ThemeProvider`),y=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\ui\tooltip.tsx`),{__esModule:b,$$typeof:w}=y;y.default,(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\ui\tooltip.tsx#Tooltip`),(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\ui\tooltip.tsx#TooltipTrigger`),(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\ui\tooltip.tsx#TooltipContent`);let j=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\ui\tooltip.tsx#TooltipProvider`),N=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\ui\toaster.tsx`),{__esModule:P,$$typeof:_}=N;N.default;let D=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\ui\toaster.tsx#Toaster`),S=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\utils\NetworkProvider.tsx`),{__esModule:T,$$typeof:k}=S;S.default;let C=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\utils\NetworkProvider.tsx#default`),R={title:"Dehix",description:"Freelancer platform"};function U({children:e}){return s.jsx(c,{children:s.jsx("html",{lang:"en",children:(0,s.jsxs)("body",{className:o().className,children:[" ",s.jsx(p,{children:(0,s.jsxs)(v,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:[s.jsx(j,{children:s.jsx(C,{children:e})}),s.jsx(D,{})]})})]})})})}},12523:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>o,default:()=>n});var s=r(68570);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\not-found.tsx`),{__esModule:o,$$typeof:i}=a;a.default;let n=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\not-found.tsx#default`)},73881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(66621);let a=e=>[{type:"image/x-icon",sizes:"512x601",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};