(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6047],{82634:function(e,s,a){Promise.resolve().then(a.bind(a,87949))},3274:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},16463:function(e,s,a){"use strict";var t=a(71169);a.o(t,"useParams")&&a.d(s,{useParams:function(){return t.useParams}}),a.o(t,"usePathname")&&a.d(s,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},87949:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return x}});var t=a(57437),r=a(3274),n=a(66648),l=a(87138),i=a(16463),u=a(2265),c=a(90564),d=a(89733),o=a(77209),m=a(70402),f=a(78068),h=a(49354);function x(){let[e,s]=(0,u.useState)(""),[a,x]=(0,u.useState)(!1),p=(0,i.useRouter)(),g=async s=>{s.preventDefault(),x(!0);try{await (0,h.c0)(e),(0,f.Am)({title:"Success",description:"Password reset email sent! Please check your inbox."}),p.push("/auth/login")}catch(e){(0,f.Am)({variant:"destructive",title:"Error",description:"Invalid Email or Password. Please try again."}),console.error(e.message)}finally{x(!1)}};return(0,t.jsxs)("div",{className:"w-full lg:grid lg:min-h-[600px] lg:grid-cols-2 xl:min-h-screen",children:[(0,t.jsx)("div",{className:"absolute left-10 top-10",children:(0,t.jsx)(c.T,{})}),(0,t.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,t.jsxs)("div",{className:"mx-auto grid w-[350px] gap-6",children:[(0,t.jsxs)("div",{className:"grid gap-2 text-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Forgot Password"}),(0,t.jsx)("p",{className:"text-balance text-muted-foreground",children:"Enter your email address below to reset your password"})]}),(0,t.jsx)("form",{onSubmit:g,children:(0,t.jsxs)("div",{className:"grid gap-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.Label,{htmlFor:"email",children:"Email"}),(0,t.jsx)(o.I,{id:"email",type:"email",placeholder:"<EMAIL>",value:e,onChange:e=>s(e.target.value),required:!0})]}),(0,t.jsx)(d.z,{type:"submit",className:"w-full",disabled:a,children:a?(0,t.jsx)(r.Z,{className:"mr-2 h-4 w-4 animate-spin"}):"Send Reset Link"})]})}),(0,t.jsxs)("div",{className:"mt-4 text-center text-sm",children:["Remembered your password?"," ",(0,t.jsx)(d.z,{variant:"outline",size:"sm",className:"ml-2",asChild:!0,children:(0,t.jsx)(l.default,{href:"/auth/sign-in",children:"Login"})})]})]})}),(0,t.jsx)("div",{className:"hidden lg:block",children:(0,t.jsx)(n.default,{src:"/bg.png",alt:"Image",width:"1920",height:"1080",className:"h-full w-full object-cover dark:brightness-[0.2] dark:invert"})})]})}},70402:function(e,s,a){"use strict";a.r(s),a.d(s,{Label:function(){return c}});var t=a(57437),r=a(2265),n=a(38364),l=a(12218),i=a(49354);let u=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(n.f,{ref:s,className:(0,i.cn)(u(),a),...r})});c.displayName=n.f.displayName},38364:function(e,s,a){"use strict";a.d(s,{f:function(){return i}});var t=a(2265),r=a(18676),n=a(57437),l=t.forwardRef((e,s)=>(0,n.jsx)(r.WV.label,{...e,ref:s,onMouseDown:s=>{var a;s.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var i=l}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,6648,2455,2971,7023,1744],function(){return e(e.s=82634)}),_N_E=e.O()}]);