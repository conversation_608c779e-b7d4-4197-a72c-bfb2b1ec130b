"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[571],{5891:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},47390:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},73347:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},10883:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},93943:function(e,t,n){n.d(t,{z$:function(){return O},fC:function(){return g}});var r=n(2265),o=n(1584),a=n(57437),u=n(78149),i=n(91715),l=n(47250),s=n(75238),c=n(1336),d=e=>{var t,n;let a,u;let{present:i,children:l}=e,s=function(e){var t,n;let[o,a]=r.useState(),u=r.useRef({}),i=r.useRef(e),l=r.useRef("none"),[s,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=f(u.current);l.current="mounted"===s?e:"none"},[s]),(0,c.b)(()=>{let t=u.current,n=i.current;if(n!==e){let r=l.current,o=f(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),i.current=e}},[e,d]),(0,c.b)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=f(u.current).includes(e.animationName);if(e.target===o&&r&&(d("ANIMATION_END"),!i.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(l.current=f(u.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:r.useCallback(e=>{e&&(u.current=getComputedStyle(e)),a(e)},[])}}(i),d="function"==typeof l?l({present:s.isPresent}):r.Children.only(l),p=(0,o.e)(s.ref,(a=null===(t=Object.getOwnPropertyDescriptor(d.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in a&&a.isReactWarning?d.ref:(a=null===(n=Object.getOwnPropertyDescriptor(d,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in a&&a.isReactWarning?d.props.ref:d.props.ref||d.ref);return"function"==typeof l||s.isPresent?r.cloneElement(d,{ref:p}):null};function f(e){return(null==e?void 0:e.animationName)||"none"}d.displayName="Presence";var p=n(18676),m="Checkbox",[v,y]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return o.scopeName=e,[function(t,o){let u=r.createContext(o),i=n.length;n=[...n,o];let l=t=>{let{scope:n,children:o,...l}=t,s=n?.[e]?.[i]||u,c=r.useMemo(()=>l,Object.values(l));return(0,a.jsx)(s.Provider,{value:c,children:o})};return l.displayName=t+"Provider",[l,function(n,a){let l=a?.[e]?.[i]||u,s=r.useContext(l);if(s)return s;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(o,...t)]}(m),[h,N]=v(m),k=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:l,checked:s,defaultChecked:c,required:d,disabled:f,value:m="on",onCheckedChange:v,form:y,...N}=e,[k,M]=r.useState(null),b=(0,o.e)(t,e=>M(e)),g=r.useRef(!1),O=!k||y||!!k.closest("form"),[T=!1,C]=(0,i.T)({prop:s,defaultProp:c,onChange:v}),j=r.useRef(T);return r.useEffect(()=>{let e=null==k?void 0:k.form;if(e){let t=()=>C(j.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[k,C]),(0,a.jsxs)(h,{scope:n,state:T,disabled:f,children:[(0,a.jsx)(p.WV.button,{type:"button",role:"checkbox","aria-checked":x(T)?"mixed":T,"aria-required":d,"data-state":E(T),"data-disabled":f?"":void 0,disabled:f,value:m,...N,ref:b,onKeyDown:(0,u.M)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,u.M)(e.onClick,e=>{C(e=>!!x(e)||!e),O&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})}),O&&(0,a.jsx)(w,{control:k,bubbles:!g.current,name:l,value:m,checked:T,required:d,disabled:f,form:y,style:{transform:"translateX(-100%)"},defaultChecked:!x(c)&&c})]})});k.displayName=m;var M="CheckboxIndicator",b=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,u=N(M,n);return(0,a.jsx)(d,{present:r||x(u.state)||!0===u.state,children:(0,a.jsx)(p.WV.span,{"data-state":E(u.state),"data-disabled":u.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});b.displayName=M;var w=e=>{let{control:t,checked:n,bubbles:o=!0,defaultChecked:u,...i}=e,c=r.useRef(null),d=(0,l.D)(n),f=(0,s.t)(t);r.useEffect(()=>{let e=c.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==n&&t){let r=new Event("click",{bubbles:o});e.indeterminate=x(n),t.call(e,!x(n)&&n),e.dispatchEvent(r)}},[d,n,o]);let p=r.useRef(!x(n)&&n);return(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:null!=u?u:p.current,...i,tabIndex:-1,ref:c,style:{...e.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function x(e){return"indeterminate"===e}function E(e){return x(e)?"indeterminate":e?"checked":"unchecked"}var g=k,O=b},38364:function(e,t,n){n.d(t,{f:function(){return i}});var r=n(2265),o=n(18676),a=n(57437),u=r.forwardRef((e,t)=>(0,a.jsx)(o.WV.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));u.displayName="Label";var i=u}}]);