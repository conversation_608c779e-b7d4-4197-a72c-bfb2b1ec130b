(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3259],{87613:function(e,s,t){Promise.resolve().then(t.bind(t,13994))},13994:function(e,s,t){"use strict";t.r(s);var l=t(57437),n=t(2265),i=t(11444),r=t(3274),c=t(85121),a=t(5498),h=t(64797),d=t(82230),m=t(22637),o=t(62688),u=t(66227);s.default=()=>{let e=(0,i.v9)(e=>{var s;return null===(s=e.user)||void 0===s?void 0:s.uid}),{trash:s,setTrash:t,isLoading:f,fetchNotes:x}=(0,m.Z)(e);return(0,n.useEffect)(()=>{e&&x()},[e,x]),(0,l.jsxs)("section",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,l.jsx)(h.Z,{menuItemsTop:d.Ne,menuItemsBottom:u.$C,active:"Trash"}),(0,l.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,l.jsx)("div",{children:(0,l.jsx)(o.Z,{menuItemsTop:d.Ne,menuItemsBottom:u.$C,activeMenu:"Trash",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Notes",link:"/notes"},{label:"Trash",link:"/trash"}]})}),(0,l.jsxs)("div",{children:[(0,l.jsx)(a.Z,{isTrash:!0,setNotes:t,notes:s}),(0,l.jsx)("div",{className:"p-6",children:(0,l.jsx)("div",{children:f?(0,l.jsx)("div",{className:"flex justify-center items-center h-[40vh]",children:(0,l.jsx)(r.Z,{className:"my-4 h-8 w-8 animate-spin"})}):(0,l.jsx)("div",{children:(null==s?void 0:s.length)>0?(0,l.jsx)(c.Z,{fetchNotes:x,isTrash:!0,notes:s,setNotes:t,isArchive:!0}):(0,l.jsx)("div",{className:"flex justify-center items-center h-[40vh] w-full",children:(0,l.jsx)("p",{children:"No trash here! Add some to get started!"})})})})})]})]})]})}}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2455,9726,2688,6755,2971,7023,1744],function(){return e(e.s=87613)}),_N_E=e.O()}]);