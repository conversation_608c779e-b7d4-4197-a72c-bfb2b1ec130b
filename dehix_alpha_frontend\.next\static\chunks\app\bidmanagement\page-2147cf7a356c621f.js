(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7878],{27644:function(e,t,r){Promise.resolve().then(r.bind(r,93522))},93522:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return p}});var n=r(57437),a=r(11444),s=r(2265),o=r(15922),i=r(89733),c=r(48185),l=r(86763),d=e=>{let{bid:t,onAction:r,actions:a}=e,[d,u]=(0,s.useState)(""),[f,p]=(0,s.useState)(""),[m,h]=(0,s.useState)(!0),{_id:g,current_price:b,project_id:y,bidder_id:v}=t,[x,S]=(0,s.useState)("");(0,s.useEffect)(()=>{(async()=>{try{let e=(await o.b.get("/project/".concat(y))).data.data.projectName;u(e)}catch(e){(0,l.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.error("Error fetching project name:",e)}})()},[y]),(0,s.useEffect)(()=>{(async()=>{try{let e=(await o.b.get("/freelancer/".concat(v))).data.userName;p(e)}catch(e){(0,l.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.error("Error fetching bidder name:",e)}})()},[v]);let w=async e=>{try{await r(g,e),S("Candidate ".concat(e,"ed")),h(!1)}catch(t){(0,l.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),S("Error performing ".concat(e," action.")),console.error("Error updating bid status:",t)}};return(0,n.jsxs)(c.Zb,{className:"p-4 mb-4 rounded-lg",children:[(0,n.jsxs)(c.Ol,{children:[(0,n.jsxs)(c.ll,{className:"text-xl font-bold",children:["Project: ",d]}),(0,n.jsxs)(c.SZ,{children:["Current Price: $",b]}),(0,n.jsxs)(c.SZ,{children:["Bidder: ",f]})]}),(0,n.jsx)(c.aY,{children:m&&(0,n.jsx)("div",{className:"actions mt-4",children:a.map(e=>(0,n.jsx)(i.z,{className:"bg-".concat(e.variant,"-500 hover:bg-").concat(e.variant,"-600 text-white font-bold py-2 px-4 rounded mr-2"),onClick:()=>w(e.label),children:e.label},e.type))})}),x&&(0,n.jsx)(c.eW,{children:(0,n.jsx)("p",{className:"text-lg font-semibold text-green-400",children:x})})]})},u=e=>{let{bids:t,onAction:r}=e,a=[{label:"Select",type:"select",variant:"success"},{label:"Reject",type:"reject",variant:"danger"},{label:"Schedule Interview",type:"schedule",variant:"primary"},{label:"Move to Lobby",type:"lobby",variant:"secondary"}];return(0,n.jsx)("div",{className:"applied-bids",children:t.map(e=>(0,n.jsx)(d,{bid:e,onAction:r,actions:a},e._id))})},f=r(78068),p=()=>{let e=(0,a.v9)(e=>e.user),[t,r]=(0,s.useState)([]),[i,c]=(0,s.useState)([]),l=(0,s.useMemo)(()=>({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."}),[]);(0,s.useEffect)(()=>{(async()=>{try{let e=(await o.b.get("/project/business/?status=Pending")).data.data.map(e=>e._id);r(e)}catch(e){console.error("Error fetching project IDs:",e)}})()},[e.uid]),(0,s.useEffect)(()=>{let e=async()=>{try{let e=[];for(let r of t)(await o.b.get("/bid/".concat(r,"/bids"))).data.data.forEach(t=>{"Pending"===t.bid_status&&e.push(t)});c(e)}catch(e){(0,f.Am)(l),console.error("Error fetching bids:",e)}};t.length&&e()},[t,l]);let d=async(e,t)=>{let r;"Accept"===t?r="Accepted":"Reject"===t?r="Rejected":"Schedule Interview"===t?r="Interview":"Lobby"===t&&(r="Lobby");try{await o.b.put("/bid/".concat(e,"/status"),{bid_status:r})}catch(e){(0,f.Am)(l),console.error("Error updating bid status:",e)}};return(0,n.jsxs)("div",{className:"bids-page max-w-6xl mx-auto p-8  mb-8",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Manage Bids"}),i.length?(0,n.jsx)(u,{bids:i,onAction:d}):(0,n.jsx)("p",{className:"",children:"No bids available."})]})}},89733:function(e,t,r){"use strict";r.d(t,{d:function(){return c},z:function(){return l}});var n=r(57437),a=r(2265),s=r(63355),o=r(12218),i=r(49354);let c=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef((e,t)=>{let{className:r,variant:a,size:o,asChild:l=!1,...d}=e,u=l?s.g7:"button";return(0,n.jsx)(u,{className:(0,i.cn)(c({variant:a,size:o,className:r})),ref:t,...d})});l.displayName="Button"},48185:function(e,t,r){"use strict";r.d(t,{Ol:function(){return i},SZ:function(){return l},Zb:function(){return o},aY:function(){return d},eW:function(){return u},ll:function(){return c}});var n=r(57437),a=r(2265),s=r(49354);let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});o.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("h3",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});c.displayName="CardTitle";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",r),...a})});l.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},78068:function(e,t,r){"use strict";r.d(t,{Am:function(){return u},pm:function(){return f}});var n=r(2265);let a=0,s=new Map,o=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?o(r):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},c=[],l={toasts:[]};function d(e){l=i(l,e),c.forEach(e=>{e(l)})}function u(e){let{...t}=e,r=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>d({type:"DISMISS_TOAST",toastId:r});return d({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||n()}}}),{id:r,dismiss:n,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function f(){let[e,t]=n.useState(l);return n.useEffect(()=>(c.push(t),()=>{let e=c.indexOf(t);e>-1&&c.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},42361:function(e,t,r){"use strict";r.d(t,{I8:function(){return d},Vv:function(){return u},db:function(){return l}});var n=r(15236),a=r(75735),s=r(60516),o=r(69842),i=r(99854);let c=(0,n.ZF)({apiKey:"AIzaSyBPTH9xikAUkgGof048klY6WGiZSmRoXXA",authDomain:"dehix-6c349.firebaseapp.com",databaseURL:"https://dehix-6c349-default-rtdb.firebaseio.com",projectId:"dehix-6c349",storageBucket:"dehix-6c349.appspot.com",messagingSenderId:"521082542540",appId:"1:521082542540:web:543857e713038c2927a569"}),l=(0,o.ad)(c),d=(0,a.v0)(c);d.useDeviceLanguage();let u=new a.hJ;(0,s.N8)(c),(0,i.cF)(c)},86763:function(e,t,r){"use strict";r.d(t,{Am:function(){return d}}),r(2265);let n=0,a=new Map,s=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),l({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?s(r):e.toasts.forEach(e=>{s(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},i=[],c={toasts:[]};function l(e){c=o(c,e),i.forEach(e=>{e(c)})}function d(e){let{...t}=e,r=(n=(n+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>l({type:"DISMISS_TOAST",toastId:r});return l({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||a()}}}),{id:r,dismiss:a,update:e=>l({type:"UPDATE_TOAST",toast:{...e,id:r}})}}},15922:function(e,t,r){"use strict";r.d(t,{b:function(){return a},q:function(){return s}});var n=r(38472);let a=n.Z.create({baseURL:"http://127.0.0.1:8080/"});console.log("Base URL:","http://127.0.0.1:8080/");let s=e=>{console.log("Initializing Axios with token:",e),a=n.Z.create({baseURL:"http://127.0.0.1:8080/",headers:{Authorization:"Bearer ".concat(e)}})};a.interceptors.request.use(e=>(console.log("Request config:",e),e),e=>(console.error("Request error:",e),Promise.reject(e))),a.interceptors.response.use(e=>(console.log("Response:",e.data),e),e=>(console.error("Response error:",e),Promise.reject(e)))},49354:function(e,t,r){"use strict";r.d(t,{bx:function(){return f},c0:function(){return d},cn:function(){return l},is:function(){return p},pH:function(){return u}});var n=r(44839),a=r(96164),s=r(75735),o=r(44785),i=r(15922),c=r(42361);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,n.W)(t))}let d=async e=>{try{await (0,s.LS)(c.I8,e),console.log("Password reset email sent successfully.")}catch(r){let e=r.code,t=r.message;throw console.log(e,t),Error(t)}},u=async(e,t)=>{try{return await (0,s.e5)(c.I8,e,t)}catch(r){let e=r.code,t=r.message;throw console.log(e,t),Error(t)}},f=async()=>await (0,s.rh)(c.I8,c.Vv),p=async e=>{try{let t=e.user,r=await t.getIdToken();(0,i.q)(r);let n=(await t.getIdTokenResult()).claims,a={uid:t.uid,email:t.email,displayName:t.displayName,phoneNumber:t.phoneNumber,photoURL:t.photoURL,emailVerified:t.emailVerified},s=n.type;return o.Z.set("userType",s,{expires:1,path:"/"}),o.Z.set("token",r,{expires:1,path:"/"}),{user:a,claims:n}}catch(e){throw console.error("Error fetching user data:",e),e}}},63355:function(e,t,r){"use strict";r.d(t,{g7:function(){return o}});var n=r(2265);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var s=r(57437),o=n.forwardRef((e,t)=>{let{children:r,...a}=e,o=n.Children.toArray(r),c=o.find(l);if(c){let e=c.props.children,r=o.map(t=>t!==c?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(i,{...a,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,s.jsx)(i,{...a,ref:t,children:r})});o.displayName="Slot";var i=n.forwardRef((e,t)=>{let{children:r,...s}=e;if(n.isValidElement(r)){let e,o;let i=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref;return n.cloneElement(r,{...function(e,t){let r={...t};for(let n in t){let a=e[n],s=t[n];/^on[A-Z]/.test(n)?a&&s?r[n]=(...e)=>{s(...e),a(...e)}:a&&(r[n]=a):"style"===n?r[n]={...a,...s}:"className"===n&&(r[n]=[a,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props),ref:t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}(t,i):i})}return n.Children.count(r)>1?n.Children.only(null):null});i.displayName="SlotClone";var c=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});function l(e){return n.isValidElement(e)&&e.type===c}}},function(e){e.O(0,[4358,7481,9208,9668,1444,2971,7023,1744],function(){return e(e.s=27644)}),_N_E=e.O()}]);