(()=>{var e={};e.id=3570,e.ids=[3570],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},99598:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),s(77122),s(54302),s(12523);var r=s(23191),a=s(88716),i=s(37922),n=s.n(i),l=s(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["business",{children:["add-project",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,77122)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\add-project\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\add-project\\page.tsx"],u="/business/add-project/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/business/add-project/page",pathname:"/business/add-project",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},41298:(e,t,s)=>{Promise.resolve().then(s.bind(s,91862))},40900:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},12070:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},66307:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69669:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40617:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},31215:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},57671:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},69515:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},98091:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91862:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Z});var r,a=s(10326),i=s(92166),n=s(17577),l=s(74064),o=s(74723),c=s(27256),d=s(83855),u=s(94019),m=s(31215),p=s(25842),x=s(29752),h=s(51400),f=s(20495),j=s(3236),g=s(51223),b=s(91664),y=s(9969),v=s(41190),N=s(29280),I=s(82015),C=s(56627),k=s(6260),D=s(38443),w=s(30351),z=s(2822);let S=c.z.object({projectName:c.z.string().min(2,{message:"Project Name must be at least 2 characters."}),email:c.z.string({required_error:"Email is required."}).email({message:"Please enter a valid email address."}),projectDomain:c.z.array(c.z.string().min(1,{message:"Project domain cannot be empty."})).min(1,{message:"At least one project domain is required."}),urls:c.z.array(c.z.object({value:c.z.string().url({message:"Please enter a valid URL."})})).optional(),description:c.z.string().min(4,{message:"Description must be at least 4 characters long."}).max(160,{message:"Description cannot exceed 160 characters."}).optional(),budget:c.z.object({type:c.z.enum(["FIXED","HOURLY"]),fixedAmount:c.z.string().optional(),hourly:c.z.object({minRate:c.z.string().optional(),maxRate:c.z.string().optional(),estimatedHours:c.z.string().optional()}).optional()}).superRefine((e,t)=>{if("FIXED"===e.type&&(e.fixedAmount?(!/^\d+(\.\d{1,2})?$/.test(e.fixedAmount)||0>=parseFloat(e.fixedAmount))&&t.addIssue({path:["fixedAmount"],code:c.z.ZodIssueCode.custom,message:"Enter a valid amount greater than 0"}):t.addIssue({path:["fixedAmount"],code:c.z.ZodIssueCode.custom,message:"Fixed amount is required"})),"HOURLY"===e.type){if(!e.hourly){t.addIssue({path:["hourly"],code:c.z.ZodIssueCode.custom,message:"Hourly details are required"});return}let{minRate:s,maxRate:r,estimatedHours:a}=e.hourly;if(s?(!/^\d+(\.\d{1,2})?$/.test(s)||0>=parseFloat(s))&&t.addIssue({path:["hourly","minRate"],code:c.z.ZodIssueCode.custom,message:"Enter a valid minimum rate > 0"}):t.addIssue({path:["hourly","minRate"],code:c.z.ZodIssueCode.custom,message:"Minimum rate is required"}),r?(!/^\d+(\.\d{1,2})?$/.test(r)||0>=parseFloat(r))&&t.addIssue({path:["hourly","maxRate"],code:c.z.ZodIssueCode.custom,message:"Enter a valid maximum rate > 0"}):t.addIssue({path:["hourly","maxRate"],code:c.z.ZodIssueCode.custom,message:"Maximum rate is required"}),a?(!/^\d+$/.test(a)||0>=parseInt(a))&&t.addIssue({path:["hourly","estimatedHours"],code:c.z.ZodIssueCode.custom,message:"Enter a valid number of hours > 0"}):t.addIssue({path:["hourly","estimatedHours"],code:c.z.ZodIssueCode.custom,message:"Estimated hours are required"}),s&&r&&/^\d+(\.\d{1,2})?$/.test(s)&&/^\d+(\.\d{1,2})?$/.test(r)){let e=parseFloat(s);parseFloat(r)<e&&t.addIssue({path:["hourly","maxRate"],code:c.z.ZodIssueCode.custom,message:"Maximum rate must be ≥ minimum rate"})}}}),profiles:c.z.array(c.z.object({domain:c.z.string().min(1,{message:"Domain is required."}),freelancersRequired:c.z.string().refine(e=>/^\d+$/.test(e)&&parseInt(e,10)>0,{message:"Number of freelancers required should be a positive number."}),skills:c.z.array(c.z.string().min(1,{message:"Skill name cannot be empty."})).min(1,{message:"At least one skill is required."}),experience:c.z.string().refine(e=>/^\d+$/.test(e)&&parseInt(e,10)>=0&&40>=parseInt(e,10),{message:"Experience must be a number between 0 and 40."}),domain_id:c.z.string().min(1,{message:"Domain ID is required."}),minConnect:c.z.string().refine(e=>/^\d+$/.test(e)&&parseInt(e,10)>0,{message:"Minimum connect must be at least 1."}),rate:c.z.string().refine(e=>/^\d+(\.\d{1,2})?$/.test(e)&&parseFloat(e)>=0,{message:"Per hour rate should be a valid non-negative number."}),description:c.z.string().min(4,{message:"Description must be at least 4 characters long."}).max(160,{message:"Description cannot exceed 160 characters."})})).optional()}),E={projectName:"",email:"",projectDomain:[],urls:[],description:"",budget:{type:"FIXED",fixedAmount:"",hourly:{minRate:"",maxRate:"",estimatedHours:""}},profiles:[{domain:"",freelancersRequired:"",skills:[],experience:"",minConnect:"",rate:"",description:"",domain_id:""}]},R="DEHIX-BUSINESS-DRAFT";function A(){let e=(0,p.v9)(e=>e.user),[t,s]=(0,n.useState)([]),[r,i]=(0,n.useState)([]),[c,A]=(0,n.useState)(""),[P,q]=(0,n.useState)([]),[Z,_]=(0,n.useState)([]),[F,O]=(0,n.useState)([]),[$,X]=(0,n.useState)(""),[T,H]=(0,n.useState)(!1),[V,M]=(0,n.useState)(!1),[J,G]=(0,n.useState)("ProjectInfo"),[L,U]=(0,n.useState)(0),{hasOtherValues:W,hasProfiles:B}=(0,w.Z)({}),Y=(0,o.cI)({resolver:(0,l.F)(S),defaultValues:E,mode:"onChange"}),{fields:Q,append:K}=(0,o.Dq)({name:"urls",control:Y.control}),{fields:ee,append:et,remove:es}=(0,o.Dq)({name:"profiles",control:Y.control}),er=()=>{if($&&!F.includes($)){let e=[...F,$];O(e),X(""),Y.setValue("projectDomain",e)}},ea=e=>{let t=F.filter(t=>t!==e);O(t),Y.setValue("projectDomain",t)},ei=e=>{""!==c.trim()&&(i(t=>{let s=[...t];return s[e]||(s[e]=[]),s[e].includes(c)||s[e].push(c),Y.setValue(`profiles.${e}.skills`,s[e]),s}),A(""))},en=(e,t)=>{i(s=>{let r=[...s];return r[e]&&(r[e]=r[e].filter(e=>e!==t),Y.setValue(`profiles.${e}.skills`,r[e])),r})},el=e=>"FIXED"===e.type?{type:"FIXED",fixedAmount:parseFloat(e.fixedAmount)}:{type:"HOURLY",hourly:{minRate:parseFloat(e.hourly.minRate),maxRate:parseFloat(e.hourly.maxRate),estimatedHours:parseInt(e.hourly.estimatedHours)}};async function eo(t){H(!0);try{let s=Array.from(new Set(r.flat().filter(Boolean))),a=el(t.budget),i={projectName:t.projectName,description:t.description,email:t.email,companyId:e.uid,companyName:e.displayName,skillsRequired:s,projectDomain:F,role:"",projectType:"FREELANCE",url:t.urls,budget:a,profiles:t.profiles||[]};await k.b.post("/project/business",i),(0,C.Am)({title:"Project Added",description:"Your project has been successfully added."});let n=parseInt(process.env.NEXT_PUBLIC__APP_PROJECT_CREATION_COST||"0",10),l=(parseInt(localStorage.getItem("DHX_CONNECTS")||"0")-n).toString();localStorage.setItem("DHX_CONNECTS",l),localStorage.removeItem(R),window.dispatchEvent(new Event("connectsUpdated"))}catch{(0,C.Am)({variant:"destructive",title:"Error",description:"Failed to add project. Please try again later."})}finally{H(!1)}Y.reset(E),O([]),i([])}let ec=async()=>{await Y.trigger(["urls","projectDomain","description","email","projectName","budget"])&&"ProjectInfo"===J&&G("ProfileInfo")},ed=e=>{1!==ee.length&&(Y.unregister(`profiles.${e}`),L>=e&&U(e=>Math.max(0,e-1)))};return(0,a.jsxs)(x.Zb,{className:"p-10",children:[a.jsx(y.l0,{...Y,children:(0,a.jsxs)("form",{onSubmit:Y.handleSubmit(eo),className:"gap-5 lg:grid lg:grid-cols-2 xl:grid-cols-2",children:["ProjectInfo"===J&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(y.Wi,{control:Y.control,name:"projectName",render:({field:e})=>(0,a.jsxs)(y.xJ,{children:[a.jsx(y.lX,{children:"Project Name"}),a.jsx(y.NI,{children:a.jsx(v.I,{placeholder:"Enter your Project Name",...e})}),a.jsx(y.pf,{children:"Enter your Project name"}),a.jsx(y.zG,{})]})}),a.jsx(y.Wi,{control:Y.control,name:"email",render:({field:e})=>(0,a.jsxs)(y.xJ,{children:[a.jsx(y.lX,{children:"Contact Email"}),a.jsx(y.NI,{children:a.jsx(v.I,{placeholder:"Enter your email",...e})}),a.jsx(y.pf,{children:"Enter your email"}),a.jsx(y.zG,{})]})}),a.jsx(y.Wi,{control:Y.control,name:"projectDomain",render:()=>(0,a.jsxs)(y.xJ,{className:"col-span-2",children:[a.jsx(y.lX,{children:"Project Domain"}),a.jsx(y.NI,{children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[(0,a.jsxs)(N.Ph,{onValueChange:X,value:$||"",children:[a.jsx(N.i4,{children:a.jsx(N.ki,{placeholder:$||"Select project domain"})}),a.jsx(N.Bw,{children:Z.filter(e=>!F.includes(e.label)).map((e,t)=>a.jsx(N.Ql,{value:e.label,children:e.label},t))})]}),a.jsx(b.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",onClick:er,children:a.jsx(d.Z,{className:"h-4 w-4"})})]}),a.jsx("div",{className:"flex flex-wrap mt-5",children:F.map((e,t)=>(0,a.jsxs)(D.C,{className:"uppercase mx-1 text-xs font-normal bg-gray-400 flex items-center",children:[e,a.jsx("button",{type:"button",onClick:()=>ea(e),className:"ml-2 text-red-500 hover:text-red-700",children:a.jsx(u.Z,{className:"h-4 w-4"})})]},t))})]})}),a.jsx(y.zG,{})]})}),a.jsx(y.Wi,{control:Y.control,name:"description",render:({field:e})=>(0,a.jsxs)(y.xJ,{className:"col-span-2",children:[a.jsx(y.lX,{children:"Profile Description"}),a.jsx(y.NI,{children:a.jsx(I.g,{placeholder:"Enter description",...e})}),a.jsx(y.zG,{})]})}),(()=>{let e=Y.watch("budget.type");return(0,a.jsxs)("div",{className:"lg:col-span-2 xl:col-span-2 border p-4 rounded-md mb-4",children:[a.jsx("h3",{className:"text-lg font-medium mb-4",children:"Project Budget"}),a.jsx(y.Wi,{control:Y.control,name:"budget.type",render:({field:e})=>(0,a.jsxs)(y.xJ,{className:"mb-6",children:[a.jsx(y.lX,{children:"Budget Type"}),a.jsx(y.NI,{children:(0,a.jsxs)(z.E,{onValueChange:e.onChange,defaultValue:e.value,className:"flex flex-col space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(z.m,{value:"FIXED",id:"fixed"}),a.jsx("label",{htmlFor:"fixed",className:"cursor-pointer",children:"Fixed Price"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(z.m,{value:"HOURLY",id:"hourly"}),a.jsx("label",{htmlFor:"hourly",className:"cursor-pointer",children:"Hourly Rate"})]})]})}),a.jsx(y.zG,{})]})}),"FIXED"===e&&a.jsx(y.Wi,{control:Y.control,name:"budget.fixedAmount",render:({field:e})=>(0,a.jsxs)(y.xJ,{children:[a.jsx(y.lX,{children:"Fixed Budget Amount ($)"}),a.jsx(y.NI,{children:a.jsx(v.I,{type:"number",placeholder:"Enter fixed amount",min:"1",step:"0.01",...e})}),a.jsx(y.pf,{children:"Enter the total fixed price for the project"}),a.jsx(y.zG,{})]})}),"HOURLY"===e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[a.jsx(y.Wi,{control:Y.control,name:"budget.hourly.minRate",render:({field:e})=>(0,a.jsxs)(y.xJ,{children:[a.jsx(y.lX,{children:"Minimum Rate ($/hour)"}),a.jsx(y.NI,{children:a.jsx(v.I,{type:"number",placeholder:"Min rate",min:"1",step:"0.01",...e})}),a.jsx(y.zG,{})]})}),a.jsx(y.Wi,{control:Y.control,name:"budget.hourly.maxRate",render:({field:e})=>(0,a.jsxs)(y.xJ,{children:[a.jsx(y.lX,{children:"Maximum Rate ($/hour)"}),a.jsx(y.NI,{children:a.jsx(v.I,{type:"number",placeholder:"Max rate",min:"1",step:"0.01",...e})}),a.jsx(y.zG,{})]})})]}),a.jsx(y.Wi,{control:Y.control,name:"budget.hourly.estimatedHours",render:({field:e})=>(0,a.jsxs)(y.xJ,{className:"mt-4",children:[a.jsx(y.lX,{children:"Estimated Hours"}),a.jsx(y.NI,{children:a.jsx(v.I,{type:"number",placeholder:"Estimated number of hours",min:"1",...e})}),a.jsx(y.pf,{children:"Estimated total hours required for project completion"}),a.jsx(y.zG,{})]})})]}),Y.formState.errors.budget?.message&&a.jsx("p",{className:"text-sm text-red-600 mt-4",children:Y.formState.errors.budget.message})]})})(),(0,a.jsxs)("div",{className:"lg:col-span-2 xl:col-span-2",children:[Q.map((e,t)=>a.jsx(y.Wi,{control:Y.control,name:`urls.${t}.value`,render:({field:e})=>a.jsx(y.xJ,{className:"flex items-center gap-2",children:(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx(y.lX,{className:(0,g.cn)(0!==t&&"sr-only"),children:"URLs"}),a.jsx(y.pf,{className:`${0!==t?"sr-only":""} mb-2`,children:"Enter URL of your account"}),a.jsx(y.NI,{children:(0,a.jsxs)("div",{className:"flex justify-center items-center  gap-3 mb-2",children:[a.jsx(v.I,{...e}),a.jsx(b.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",onClick:()=>Y.setValue("urls",(Y.getValues("urls")||[]).filter((e,s)=>s!==t)),children:a.jsx(u.Z,{className:"h-4 w-4"})})]})}),a.jsx(y.zG,{className:"my-2.5"})]})})},e.id)),a.jsx(b.z,{className:"mt-2",type:"button",variant:"outline",size:"sm",onClick:()=>K({value:""}),children:"Add URL"})]})]}),"ProfileInfo"===J&&(0,a.jsxs)("div",{className:"lg:col-span-2 xl:col-span-2",children:[a.jsx("div",{className:"my-4",children:a.jsx(()=>(0,a.jsxs)(j.x,{children:[a.jsx("div",{className:"flex gap-2 mb-4",children:ee.map((e,t)=>(0,a.jsxs)(b.z,{type:"button",size:"sm",variant:L===t?"default":"outline",onClick:()=>U(t),className:`px-4 py-2 ${L===t?"bg-blue-600 text-white hover:text-black":""}`,children:["Profile ",t+1]},t))}),a.jsx(j.B,{orientation:"horizontal"})]}),{})}),ee.map((e,s)=>s===L?(0,a.jsxs)("div",{className:"border p-4 mb-4 rounded-md relative",children:[a.jsx(u.Z,{onClick:()=>ed(s),className:"w-5 hover:text-red-600 h-5 absolute right-2 top-1 cursor-pointer"}),a.jsx(y.Wi,{control:Y.control,name:`profiles.${s}.domain`,render:({field:e})=>(0,a.jsxs)(y.xJ,{className:"mb-4",children:[a.jsx(y.lX,{children:"Profile Domain"}),a.jsx(y.NI,{children:(0,a.jsxs)(N.Ph,{onValueChange:e=>{let t=P.find(t=>t.label===e);Y.setValue(`profiles.${s}.domain`,t?.label||""),Y.setValue(`profiles.${s}.domain_id`,t?.domain_id||"")},defaultValue:e.value,children:[a.jsx(N.i4,{children:a.jsx(N.ki,{placeholder:"Select domain"})}),a.jsx(N.Bw,{children:P.map((e,t)=>a.jsx(N.Ql,{value:e.label,children:e.label},t))})]})}),a.jsx(y.zG,{})]})}),a.jsx(y.Wi,{control:Y.control,name:`profiles.${s}.freelancersRequired`,render:({field:e})=>(0,a.jsxs)(y.xJ,{className:"mb-4",children:[a.jsx(y.lX,{children:"Number of Freelancers Required"}),a.jsx(y.NI,{children:a.jsx(v.I,{type:"number",placeholder:"Enter number",min:1,...e})}),a.jsx(y.zG,{})]})}),a.jsx(y.Wi,{control:Y.control,name:`profiles.${s}.skills`,render:()=>(0,a.jsxs)(y.xJ,{className:"mb-4",children:[a.jsx(y.lX,{children:"Skills"}),a.jsx(y.NI,{children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[(0,a.jsxs)(N.Ph,{onValueChange:A,value:c||"",children:[a.jsx(N.i4,{children:a.jsx(N.ki,{placeholder:"Select skill"})}),a.jsx(N.Bw,{children:t.map((e,t)=>a.jsx(N.Ql,{value:e.label,children:e.label},t))})]}),a.jsx(b.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",onClick:()=>ei(s),children:a.jsx(d.Z,{className:"h-4 w-4"})})]}),a.jsx("div",{className:"flex flex-wrap mt-5",children:r[s]?.map((e,t)=>a.jsxs(D.C,{className:"uppercase mx-1 text-xs font-normal bg-gray-400 flex items-center",children:[e,a.jsx("button",{type:"button",onClick:()=>en(s,e),className:"ml-2 text-red-500 hover:text-red-700",children:a.jsx(u.Z,{className:"h-4 w-4"})})]},t))})]})}),a.jsx(y.zG,{})]})}),a.jsx(y.Wi,{control:Y.control,name:`profiles.${s}.experience`,render:({field:e})=>(0,a.jsxs)(y.xJ,{className:"mb-4",children:[a.jsx(y.lX,{children:"Experience"}),a.jsx(y.NI,{children:a.jsx(v.I,{type:"number",placeholder:"Enter experience",min:0,max:60,...e})}),a.jsx(y.zG,{})]})}),a.jsx(y.Wi,{control:Y.control,name:`profiles.${s}.minConnect`,render:({field:e})=>(0,a.jsxs)(y.xJ,{className:"mb-4",children:[a.jsx(y.lX,{children:"Min Connect"}),a.jsx(y.pf,{children:"Minimum number of connects for the project"}),a.jsx(y.NI,{children:a.jsx(v.I,{placeholder:"Enter Min Connects (Recommended: 10)",...e})}),a.jsx(y.zG,{})]})}),a.jsx(y.Wi,{control:Y.control,name:`profiles.${s}.rate`,render:({field:e})=>(0,a.jsxs)(y.xJ,{className:"mb-4",children:[a.jsx(y.lX,{children:"Per Hour Rate"}),a.jsx(y.NI,{children:a.jsx(v.I,{type:"number",placeholder:"Enter rate",min:0,max:200,...e})}),a.jsx(y.zG,{})]})}),a.jsx(y.Wi,{control:Y.control,name:`profiles.${s}.description`,render:({field:e})=>(0,a.jsxs)(y.xJ,{className:"mb-4",children:[a.jsx(y.lX,{children:"Description"}),a.jsx(y.NI,{children:a.jsx(I.g,{placeholder:"Enter description",...e})}),a.jsx(y.zG,{})]})}),a.jsx(b.z,{type:"button",variant:"outline",size:"sm",className:"mt-2",onClick:()=>es(s),children:"Remove Profile"})]},s):null),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx(b.z,{type:"button",variant:"outline",size:"sm",className:"mt-2",onClick:()=>et({domain:"",freelancersRequired:"",skills:[],experience:"",minConnect:"",rate:"",description:"",domain_id:""}),children:"Add Profile"}),a.jsx(b.z,{type:"button",size:"sm",variant:"outline",onClick:()=>{let e=Y.getValues(),t=W(e),s=B(e.profiles);if(!t&&!s){(0,C.Am)({variant:"destructive",title:"Empty Draft",description:"Cannot save an empty draft."});return}let a=e.profiles?.map((e,t)=>({...e,skills:Array.isArray(r[t])?r[t]:[]})),i={...e,profiles:a};localStorage.setItem(R,JSON.stringify(i)),(0,C.Am)({title:"Draft Saved",description:"Your form data has been saved as a draft."})},children:a.jsx(m.Z,{})})]}),a.jsx("div",{className:"lg:col-span-2 xl:col-span-2 mt-4",children:a.jsx(h.Z,{loading:T,isValidCheck:Y.trigger,onSubmit:Y.handleSubmit(eo),setLoading:H,userId:e.uid,buttonText:"Create Project",userType:"BUSINESS",requiredConnects:parseInt(process.env.NEXT_PUBLIC__APP_PROJECT_CREATION_COST||"0",10)})})]}),a.jsx("div",{className:"w-full mt-4 flex col-span-2 justify-end",children:"ProjectInfo"===J&&a.jsx(b.z,{type:"button",variant:"outline",onClick:ec,children:"Next"})}),a.jsx("div",{className:"w-full mt-4 flex col-span-2 justify-start",children:"ProfileInfo"===J&&a.jsx(b.z,{type:"button",variant:"outline",onClick:()=>{"ProfileInfo"===J&&G("ProjectInfo")},children:"Prev"})})]})}),V&&a.jsx(f.Z,{dialogChange:V,setDialogChange:M,heading:"Load Draft?",desc:"A saved draft was found. Do you want to load it?",handleClose:()=>{localStorage.removeItem(R),M(!1),(0,C.Am)({title:"Draft discarded",description:"Your saved draft has been discarded."})},handleSave:()=>{let e=localStorage.getItem(R);if(e)try{let t=JSON.parse(e);Y.reset(t),O(t.projectDomain||[]),i(t.profiles?.map(e=>Array.isArray(e.skills)?e.skills:[])||[]),(0,C.Am)({title:"Draft loaded",description:"Your saved draft has been loaded."})}catch{(0,C.Am)({title:"Error loading draft",description:"There was a problem loading your draft.",variant:"destructive"})}M(!1)},btn1Txt:" Discard",btn2Txt:"Load Draft"})]})}!function(e){e.ProjectInfo="ProjectInfo",e.ProfileInfo="ProfileInfo"}(r||(r={}));var P=s(46319),q=s(40588);function Z(){return(0,a.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[a.jsx(i.Z,{menuItemsTop:P.yn,menuItemsBottom:P.$C,active:""}),(0,a.jsxs)("div",{className:"flex flex-col sm:gap-4  sm:pl-14 mb-8",children:[a.jsx(q.Z,{menuItemsTop:P.yn,menuItemsBottom:P.$C,activeMenu:"",breadcrumbItems:[{label:"Business",link:"/dashboard/business"},{label:"Create Project",link:"#"}]}),a.jsx("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8",children:a.jsx(A,{})})]})]})}},51400:(e,t,s)=>{"use strict";s.d(t,{Z:()=>d});var r=s(10326),a=s(17577),i=s(56627),n=s(6260),l=s(91664),o=s(24118),c=s(41190);function d({loading:e,setLoading:t,onSubmit:s,isValidCheck:d,userId:u,buttonText:m,userType:p,requiredConnects:x,data:h}){let[f,j]=(0,a.useState)(!1),[g,b]=(0,a.useState)(!1),y=parseInt(localStorage.getItem("DHX_CONNECTS")||"0",10),v=async()=>{try{await n.b.post("/token-request",{userId:u,userType:p,amount:"100",status:"PENDING",dateTime:new Date().toISOString()}),(0,i.Am)({title:"Success!",description:"Request to add connects has been sent.",duration:3e3});let e={userId:u,amount:100,status:"PENDING",dateTime:new Date().toISOString()};window.dispatchEvent(new CustomEvent("newConnectRequest",{detail:e}))}catch(e){console.error("Error requesting more connects:",e.response),(0,i.Am)({variant:"destructive",title:"Error!",description:"Failed to request connects. Try again!",duration:3e3})}},N=async()=>{let e=await d();if(console.log(e),e){if(console.log(x),y<x){b(!0),j(!0);return}b(!1),j(!0)}},I=async()=>{if(console.log(g),!g){t(!0);try{h?await s(h):await s(),j(!1)}catch(e){console.error("Error deducting connects:",e),alert("Failed to deduct connects. Try again!")}finally{t(!1)}}};return(0,r.jsxs)("div",{children:[r.jsx(l.z,{type:"button",className:"lg:col-span-2 w-full xl:col-span-2 mt-4",disabled:e,onClick:N,children:e?"Loading...":m}),r.jsx(o.Vq,{open:f,onOpenChange:j,children:r.jsx(o.cZ,{children:g?(0,r.jsxs)(r.Fragment,{children:[r.jsx(o.$N,{children:"Insufficient Connects"}),(0,r.jsxs)(o.Be,{children:["You don't have enough connects to create a project.",r.jsx("br",{}),"Please"," ",r.jsx("span",{className:"text-blue-600 font-bold cursor-pointer",onClick:v,children:"Request Connects"})," ","to proceed."]}),r.jsx(o.cN,{children:r.jsx(l.z,{variant:"outline",onClick:()=>j(!1),children:"Close"})})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(o.$N,{children:"Confirm Deduction"}),r.jsx(c.I,{type:"text",value:x,disabled:!0}),(0,r.jsxs)(o.Be,{children:["Creating this project will deduct"," ",(0,r.jsxs)("span",{className:"font-extrabold",children:[" ",x," connects"]}),". Do you want to proceed?"]}),(0,r.jsxs)(o.cN,{children:[r.jsx(l.z,{variant:"outline",onClick:()=>j(!1),children:"Cancel"}),r.jsx(l.z,{onClick:I,disabled:e,children:e?"Processing...":"Confirm"})]})]})})})]})}},20495:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(10326);s(17577);var a=s(24118),i=s(91664);let n=({dialogChange:e,setDialogChange:t,heading:s,desc:n,handleClose:l,handleSave:o,btn1Txt:c,btn2Txt:d})=>r.jsx(a.Vq,{open:e,onOpenChange:t,children:(0,r.jsxs)(a.cZ,{children:[(0,r.jsxs)(a.fK,{children:[r.jsx(a.$N,{children:s}),r.jsx(a.Be,{children:n})]}),(0,r.jsxs)(a.cN,{children:[r.jsx(i.z,{variant:"outline",onClick:l,children:c}),r.jsx(i.z,{onClick:o,children:d})]})]})})},9969:(e,t,s)=>{"use strict";s.d(t,{NI:()=>f,Wi:()=>u,l0:()=>c,lX:()=>h,pf:()=>j,xJ:()=>x,zG:()=>g});var r=s(10326),a=s(17577),i=s(99469),n=s(74723),l=s(51223),o=s(44794);let c=n.RV,d=a.createContext({}),u=({...e})=>r.jsx(d.Provider,{value:{name:e.name},children:r.jsx(n.Qr,{...e})}),m=()=>{let e=a.useContext(d),t=a.useContext(p),{getFieldState:s,formState:r}=(0,n.Gc)(),i=s(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...i}},p=a.createContext({}),x=a.forwardRef(({className:e,...t},s)=>{let i=a.useId();return r.jsx(p.Provider,{value:{id:i},children:r.jsx("div",{ref:s,className:(0,l.cn)("space-y-2",e),...t})})});x.displayName="FormItem";let h=a.forwardRef(({className:e,...t},s)=>{let{error:a,formItemId:i}=m();return r.jsx(o.Label,{ref:s,className:(0,l.cn)(a&&"text-destructive",e),htmlFor:i,...t})});h.displayName="FormLabel";let f=a.forwardRef(({...e},t)=>{let{error:s,formItemId:a,formDescriptionId:n,formMessageId:l}=m();return r.jsx(i.g7,{ref:t,id:a,"aria-describedby":s?`${n} ${l}`:`${n}`,"aria-invalid":!!s,...e})});f.displayName="FormControl";let j=a.forwardRef(({className:e,...t},s)=>{let{formDescriptionId:a}=m();return r.jsx("p",{ref:s,id:a,className:(0,l.cn)("text-sm text-muted-foreground",e),...t})});j.displayName="FormDescription";let g=a.forwardRef(({className:e,children:t,...s},a)=>{let{error:i,formMessageId:n}=m(),o=i?String(i?.message):t;return o?r.jsx("p",{ref:a,id:n,className:(0,l.cn)("text-sm font-medium text-destructive",e),...s,children:o}):null});g.displayName="FormMessage"},44794:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Label:()=>c});var r=s(10326),a=s(17577),i=s(34478),n=s(28671),l=s(51223);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...t},s)=>r.jsx(i.f,{ref:s,className:(0,l.cn)(o(),e),...t}));c.displayName=i.f.displayName},2822:(e,t,s)=>{"use strict";s.d(t,{E:()=>$,m:()=>X});var r=s(10326),a=s(17577),i=s(82561),n=s(48051),l=s(93095),o=s(77335),c=s(15594),d=s(52067),u=s(17124),m=s(2566),p=s(53405),x=s(9815),h="Radio",[f,j]=(0,l.b)(h),[g,b]=f(h),y=a.forwardRef((e,t)=>{let{__scopeRadio:s,name:l,checked:c=!1,required:d,disabled:u,value:m="on",onCheck:p,...x}=e,[h,f]=a.useState(null),j=(0,n.e)(t,e=>f(e)),b=a.useRef(!1),y=!h||!!h.closest("form");return(0,r.jsxs)(g,{scope:s,checked:c,disabled:u,children:[(0,r.jsx)(o.WV.button,{type:"button",role:"radio","aria-checked":c,"data-state":C(c),"data-disabled":u?"":void 0,disabled:u,value:m,...x,ref:j,onClick:(0,i.M)(e.onClick,e=>{c||p?.(),y&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})}),y&&(0,r.jsx)(I,{control:h,bubbles:!b.current,name:l,value:m,checked:c,required:d,disabled:u,style:{transform:"translateX(-100%)"}})]})});y.displayName=h;var v="RadioIndicator",N=a.forwardRef((e,t)=>{let{__scopeRadio:s,forceMount:a,...i}=e,n=b(v,s);return(0,r.jsx)(x.z,{present:a||n.checked,children:(0,r.jsx)(o.WV.span,{"data-state":C(n.checked),"data-disabled":n.disabled?"":void 0,...i,ref:t})})});N.displayName=v;var I=e=>{let{control:t,checked:s,bubbles:i=!0,...n}=e,l=a.useRef(null),o=(0,p.D)(s),c=(0,m.t)(t);return a.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(o!==s&&t){let r=new Event("click",{bubbles:i});t.call(e,s),e.dispatchEvent(r)}},[o,s,i]),(0,r.jsx)("input",{type:"radio","aria-hidden":!0,defaultChecked:s,...n,tabIndex:-1,ref:l,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function C(e){return e?"checked":"unchecked"}var k=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],D="RadioGroup",[w,z]=(0,l.b)(D,[c.Pc,j]),S=(0,c.Pc)(),E=j(),[R,A]=w(D),P=a.forwardRef((e,t)=>{let{__scopeRadioGroup:s,name:a,defaultValue:i,value:n,required:l=!1,disabled:m=!1,orientation:p,dir:x,loop:h=!0,onValueChange:f,...j}=e,g=S(s),b=(0,u.gm)(x),[y,v]=(0,d.T)({prop:n,defaultProp:i,onChange:f});return(0,r.jsx)(R,{scope:s,name:a,required:l,disabled:m,value:y,onValueChange:v,children:(0,r.jsx)(c.fC,{asChild:!0,...g,orientation:p,dir:b,loop:h,children:(0,r.jsx)(o.WV.div,{role:"radiogroup","aria-required":l,"aria-orientation":p,"data-disabled":m?"":void 0,dir:b,...j,ref:t})})})});P.displayName=D;var q="RadioGroupItem",Z=a.forwardRef((e,t)=>{let{__scopeRadioGroup:s,disabled:l,...o}=e,d=A(q,s),u=d.disabled||l,m=S(s),p=E(s),x=a.useRef(null),h=(0,n.e)(t,x),f=d.value===o.value,j=a.useRef(!1);return a.useEffect(()=>{let e=e=>{k.includes(e.key)&&(j.current=!0)},t=()=>j.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,r.jsx)(c.ck,{asChild:!0,...m,focusable:!u,active:f,children:(0,r.jsx)(y,{disabled:u,required:d.required,checked:f,...p,...o,name:d.name,ref:h,onCheck:()=>d.onValueChange(o.value),onKeyDown:(0,i.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,i.M)(o.onFocus,()=>{j.current&&x.current?.click()})})})});Z.displayName=q;var _=a.forwardRef((e,t)=>{let{__scopeRadioGroup:s,...a}=e,i=E(s);return(0,r.jsx)(N,{...i,...a,ref:t})});_.displayName="RadioGroupIndicator";var F=s(53982),O=s(51223);let $=a.forwardRef(({className:e,...t},s)=>r.jsx(P,{className:(0,O.cn)("grid gap-2",e),...t,ref:s}));$.displayName=P.displayName;let X=a.forwardRef(({className:e,...t},s)=>r.jsx(Z,{ref:s,className:(0,O.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:r.jsx(_,{className:"flex items-center justify-center",children:r.jsx(F.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})}));X.displayName=Z.displayName},46319:(e,t,s)=>{"use strict";s.d(t,{$C:()=>j,Ne:()=>g,yn:()=>f});var r=s(10326),a=s(95920),i=s(57671),n=s(94909),l=s(12070),o=s(66307),c=s(69669),d=s(40617),u=s(69515),m=s(88378),p=s(40900),x=s(98091),h=s(46226);let f=[{href:"#",icon:r.jsx(h.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:r.jsx(a.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:r.jsx(i.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:r.jsx(n.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:r.jsx(n.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:r.jsx(l.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:r.jsx(o.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:r.jsx(c.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:r.jsx(d.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:r.jsx(u.Z,{className:"h-5 w-5"}),label:"Notes"}],j=[{href:"/business/settings/business-info",icon:r.jsx(m.Z,{className:"h-5 w-5"}),label:"Settings"}],g=[{href:"#",icon:r.jsx(h.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:r.jsx(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:r.jsx(u.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:r.jsx(p.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:r.jsx(x.Z,{className:"h-5 w-5"}),label:"Trash"}]},30351:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var r=s(17577),a=s(84097);let i=({form:e,formSection:t="",isDialogOpen:s,setIsDialogOpen:i,onSave:n,onDiscard:l,setCurrSkills:o})=>{let[c,d]=(0,r.useState)(!1),[u,m]=(0,r.useState)(!1),p=(0,r.useRef)(!1),x=(0,r.useRef)(null),h=e=>{if(!t)return;let s=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");s[t]&&(x.current=s[t]),Object.values(e).some(e=>void 0!==e&&""!==e)&&(s[t]=e,localStorage.setItem("DEHIX_DRAFT",JSON.stringify(s)),(0,a.Am)({title:"Draft Saved",description:`Your ${t} draft has been saved.`,duration:1500}))};(0,r.useEffect)(()=>{if(s&&!p.current&&t){let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");e&&e[t]&&d(!0),p.current=!0}},[s,t]);let f=e=>e&&"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"string"==typeof t?t.trim():t])):{};return{showDraftDialog:c,setShowDraftDialog:d,confirmExitDialog:u,setConfirmExitDialog:m,loadDraft:()=>{if(!t)return;let s=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");s&&s[t]&&(Object.keys(s[t]).forEach(e=>{void 0===s[t][e]&&delete s[t][e]}),"projects"===t&&(delete s[t].verificationStatus,Array.isArray(s[t].techUsed)&&o(s[t].techUsed)),Object.entries(s[t]).some(([,e])=>""!==e&&void 0!==e&&!(Array.isArray(e)&&0===e.length))&&e&&(e.reset(s[t]),x.current=s[t],(0,a.Am)({title:"Draft Loaded",description:`Your ${t} draft has been restored.`,duration:1500})),d(!1))},discardDraft:()=>{if(!t)return;let s=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");s&&(delete s[t],0===Object.keys(s).length?localStorage.removeItem("DEHIX_DRAFT"):localStorage.setItem("DEHIX_DRAFT",JSON.stringify(s))),e?.reset(),(0,a.Am)({title:"Draft Discarded",description:`Your ${t} draft has been discarded.`,duration:1500}),d(!1),l&&l()},handleSaveAndClose:()=>{if(!t)return;let s=e?.getValues();h(s),(0,a.Am)({title:"Draft Saved",description:"Your draft has been saved.",duration:1500}),x.current=s,m(!1),i&&i(!1),n&&n(s)},handleDiscardAndClose:()=>{if(!t)return;let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");delete e[t],0===Object.keys(e).length?localStorage.removeItem("DEHIX_DRAFT"):localStorage.setItem("DEHIX_DRAFT",JSON.stringify(e)),(0,a.Am)({title:"Draft Discarded",description:`Your ${t} draft has been discarded.`,duration:1500}),m(!1),i&&i(!1),l&&l()},handleDialogClose:()=>{if(!s||!t)return;let r=e?.getValues()||{},a=x.current||{},n=f(r),l=f(a),o=Object.entries(l).some(([e,t])=>{let s=n[e];return Array.isArray(t)&&Array.isArray(s)?JSON.stringify(t)!==JSON.stringify(s):t!==s}),c=Object.entries(n).some(([e,t])=>"verificationStatus"!==e&&void 0!==t&&""!==t&&void 0===l[e]);if(!o&&!c&&i){i(!1);return}Object.values(n).some(e=>e?.toString().trim())?m(!0):i&&i(!1)},saveDraft:h,hasOtherValues:(0,r.useCallback)(e=>Object.entries(e).some(([e,t])=>"profiles"!==e&&(Array.isArray(t)&&t.length>0&&("urls"!==e||t.some(e=>e?.value?.trim()!==""))||"string"==typeof t&&""!==t.trim()||"number"==typeof t&&!isNaN(t))),[]),hasProfiles:(0,r.useCallback)(e=>e?.some(e=>Object.values(e).some(e=>Array.isArray(e)&&e.length>0||"string"==typeof e&&""!==e.trim()||"number"==typeof e&&!isNaN(e))),[])}}},77122:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var r=s(68570);let a=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\business\add-project\page.tsx`),{__esModule:i,$$typeof:n}=a;a.default;let l=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\business\add-project\page.tsx#default`)},34478:(e,t,s)=>{"use strict";s.d(t,{f:()=>l});var r=s(17577),a=s(77335),i=s(10326),n=r.forwardRef((e,t)=>(0,i.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,6686,4736,6499,8066,588],()=>s(99598));module.exports=r})();