(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[99],{5092:function(e,t,s){Promise.resolve().then(s.bind(s,59029))},92222:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(33480).Z)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},54418:function(e,t,s){"use strict";s.d(t,{AuthProvider:function(){return p},a:function(){return x}});var a=s(57437),i=s(2265),l=s(11444),r=s(16463),n=s(3483),c=s(15922),d=s(42361);let o=(0,i.createContext)({user:null,loading:!0}),m=e=>localStorage.getItem(e),u=(e,t)=>localStorage.setItem(e,t),h=e=>localStorage.removeItem(e),p=e=>{let{children:t}=e,s=(0,r.useRouter)(),[p,x]=(0,i.useState)(null),[f,j]=(0,i.useState)(!0),g=(0,l.I0)();return(0,i.useEffect)(()=>{let e=m("user"),t=m("token");if(e&&t)try{let s=JSON.parse(e);x(s),(0,c.q)(t),g((0,n.av)(s))}catch(e){console.error("Failed to parse user:",e),h("user"),h("token")}let a=d.I8.onIdTokenChanged(async e=>{if(e&&navigator.onLine)try{let t=await e.getIdToken();if(t){let s=await e.getIdTokenResult(),a={...e,type:s.claims.type};u("user",JSON.stringify(a)),u("token",t),x(a),(0,c.q)(t),g((0,n.av)(a))}}catch(e){console.error("Token Refresh Error:",e)}else h("user"),h("token"),x(null),g((0,n.pn)()),s.replace("/auth/login")});return j(!1),()=>a()},[g,s]),(0,a.jsx)(o.Provider,{value:{user:p,loading:f},children:!f&&t})},x=()=>(0,i.useContext)(o)},59029:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return T}});var a=s(57437),i=s(2265),l=s(11444),r=s(44785),n=s(66648),c=s(92222),d=s(33480);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,d.Z)("LayoutGrid",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]]);var m=s(3274),u=s(6884),h=s(78068),p=s(15922),x=s(89733),f=s(77209),j=s(4919),g=s(54662),v=s(48185),y=s(54418),b=()=>{let{user:e}=(0,y.a)(),[t,s]=(0,i.useState)(""),[l,r]=(0,i.useState)(""),[d,b]=(0,i.useState)(null),[N]=(0,i.useState)((null==e?void 0:e.uid)||""),[w,k]=(0,i.useState)("BUSINESS"),[S,C]=(0,i.useState)([]),[A,T]=(0,i.useState)(!1),[F,I]=(0,i.useState)(!1),[D,E]=(0,i.useState)(!1),[Z,q]=(0,i.useState)(null),[z,P]=(0,i.useState)("card"),[_,O]=(0,i.useState)(null),[U,M]=(0,i.useState)(null),[B,L]=(0,i.useState)(!1),[R,H]=(0,i.useState)(!1);(0,i.useEffect)(()=>{(async()=>{try{H(!0);let t=(await p.b.get("/ticket?customerID=".concat(null==e?void 0:e.uid))).data.data.filter(t=>t.customerID===(null==e?void 0:e.uid));C(t)}catch(e){(0,h.Am)({variant:"destructive",title:"Error",description:"An error occurred while fetching tickets."})}finally{H(!1)}})()},[null==e?void 0:e.uid]);let V=async s=>{if(s.preventDefault(),!t||!l||!N){(0,h.Am)({variant:"destructive",title:"Missing Fields",description:"Please fill in all required fields."});return}let a="";if(d)try{let e=new FormData;e.append("file",d);let{Location:t}=(await p.b.post("/register/upload-image",e,{headers:{"Content-Type":"multipart/form-data"}})).data.data;a=t}catch(e){(0,h.Am)({variant:"destructive",title:"Upload Failed",description:"Failed to upload the file. Please try again."});return}let i={customerID:null==e?void 0:e.uid,customerType:w,description:l,status:"CREATED",subject:t,filesAttached:a};try{L(!0);let e=await p.b.post("/ticket",i);(0,h.Am)({title:"Ticket Submitted",description:"Ticket created successfully."}),C(t=>[...t,e.data.data]),T(!1),$()}catch(e){(0,h.Am)({variant:"destructive",title:"Error",description:"Failed to submit ticket."})}finally{L(!1)}},W=e=>{q(e._id),s(e.subject),r(e.description),I(!0)},$=()=>{s(""),r(""),k("BUSINESS"),b(null)},Y=async e=>{if(e.preventDefault(),!Z||!t||!l)return;L(!0);let s=(null==_?void 0:_.filesAttached)||"";if(d)try{let e=new FormData;e.append("file",d);let{Location:t}=(await p.b.post("/register/upload-image",e,{headers:{"Content-Type":"multipart/form-data"}})).data.data;s=t}catch(e){(0,h.Am)({variant:"destructive",title:"Upload Failed",description:"Failed to upload the file. Please try again."});return}try{let e=await p.b.put("/ticket/".concat(Z),{subject:t,description:l,filesAttached:s});200===e.status?(C(e=>e.map(e=>e._id===Z?{...e,subject:t,description:l,filesAttached:s}:e)),(0,h.Am)({title:"Ticket Updated",description:"The ticket has been successfully updated."}),I(!1),$()):(0,h.Am)({variant:"destructive",title:"Update Failed",description:"Could not update the ticket."})}catch(e){(0,h.Am)({variant:"destructive",title:"Error",description:"An error occurred while updating the ticket."})}finally{L(!1)}},Q=e=>{O(e),E(!0)},G=e=>{navigator.clipboard.writeText(e).then(()=>{(0,h.Am)({title:"ID Copied",description:"Ticket ID has been copied to your clipboard.",duration:1500})}).catch(()=>{(0,h.Am)({variant:"destructive",title:"Copy Failed",description:"Failed to copy the Ticket ID."})})};return(0,a.jsxs)("div",{className:"max-w-8xl mx-auto bg-background p-6 rounded shadow-sm",children:[(0,a.jsx)("h1",{className:" mt-3 sm:text-3xl",children:"Submit a Support Ticket"}),(0,a.jsxs)(g.Vq,{open:A,onOpenChange:T,children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)(g.hg,{asChild:!0,children:(0,a.jsx)(x.z,{onClick:()=>T(!0),className:"mt-6",children:"Create Ticket"})}),(0,a.jsx)("div",{className:"my-4",children:(0,a.jsx)(x.z,{onClick:()=>P("cards"===z?"table":"cards"),children:"cards"===z?(0,a.jsx)(c.Z,{className:"w-5 h-5"}):(0,a.jsx)(o,{className:"w-5 h-5"})})})]}),(0,a.jsxs)(g.cZ,{children:[(0,a.jsx)(g.$N,{children:"Create a New Ticket"}),(0,a.jsx)(g.Be,{children:"Fill out the form to submit a support ticket."}),(0,a.jsxs)("form",{onSubmit:V,className:"space-y-4 flex flex-col",children:[(0,a.jsx)(f.I,{id:"subject",placeholder:"Subject",value:t,onChange:e=>s(e.target.value),required:!0}),(0,a.jsx)(j.g,{id:"description",placeholder:"Description",value:l,onChange:e=>r(e.target.value),rows:4,required:!0}),(0,a.jsxs)("div",{className:"file-upload-container w-80 mx-auto mt-6",children:[(0,a.jsx)("div",{className:"file-upload p-4 border-2 border-dashed border-gray-400 rounded-lg text-center",style:{cursor:"pointer"},onClick:()=>{var e;return null===(e=document.getElementById("file-input"))||void 0===e?void 0:e.click()},children:d?(0,a.jsxs)(a.Fragment,{children:[d.type.startsWith("image/")?U?(0,a.jsx)(n.default,{src:U,alt:"File Preview",width:200,height:200,className:"rounded border mt-4"}):(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{children:"Loading preview..."})}):(0,a.jsxs)("div",{className:"p-4 border rounded bg-gray-100",children:[(0,a.jsx)("p",{className:"text-sm",children:"Preview not available for this file type"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:d.name})]}),(0,a.jsx)("button",{onClick:()=>{b(null),M(null)},className:"mt-4 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600",children:"Remove File"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("p",{className:"text-lg text-gray-500",children:"Click or drag to upload a file"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Supported formats: Images, PDFs, etc."})]})}),(0,a.jsx)("input",{id:"file-input",type:"file",className:"hidden",onChange:e=>{var t;let s=null===(t=e.target.files)||void 0===t?void 0:t[0];if(s){if(s.size>5242880){(0,h.Am)({variant:"destructive",title:"File Too Large",description:"The selected file exceeds the size limit of 5MB. Please select a smaller file."});return}if(!["image/jpeg","image/png","image/gif","application/pdf"].includes(s.type)){(0,h.Am)({variant:"destructive",title:"Unsupported File Type",description:"The selected file type is not supported. Please upload an image or PDF."});return}b(s),M(URL.createObjectURL(s))}else(0,h.Am)({variant:"destructive",title:"Invalid File",description:"Failed to load the file. Please try again."})},accept:"image/*,application/pdf"})]}),(0,a.jsx)(x.z,{disabled:B,type:"submit",children:B?(0,a.jsx)(m.Z,{className:"animate-spin w-8 h-8"}):"Submit Ticket"})]})]})]}),(0,a.jsx)(g.Vq,{open:F,onOpenChange:I,children:(0,a.jsxs)(g.cZ,{children:[(0,a.jsx)(g.$N,{children:"Edit Ticket"}),(0,a.jsx)(g.Be,{children:"Update the subject and description of the ticket."}),(0,a.jsxs)("form",{onSubmit:Y,className:"space-y-4 flex flex-col",children:[(0,a.jsx)(f.I,{id:"edit-subject",placeholder:"Subject",value:t,onChange:e=>s(e.target.value),required:!0}),(0,a.jsx)(j.g,{id:"edit-description",placeholder:"Description",value:l,onChange:e=>r(e.target.value),rows:4,required:!0}),(0,a.jsx)(x.z,{disabled:B,type:"submit",children:B?(0,a.jsx)(m.Z,{className:"animate-spin w-8 h-8"}):"Update Ticket"})]})]})}),(0,a.jsx)(g.Vq,{open:D,onOpenChange:E,children:(0,a.jsxs)(g.cZ,{children:[(0,a.jsx)(g.$N,{children:"Ticket Details"}),(0,a.jsx)(g.Be,{children:"View and edit the details of the ticket."}),(0,a.jsxs)(v.Zb,{className:"p-6 space-y-4 rounded-md shadow-md",children:[(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsxs)("p",{className:"text-lg font-semibold",children:[(0,a.jsx)("strong",{children:"Subject: "}),null==_?void 0:_.subject]})}),(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsxs)("p",{className:"text-sm",children:[(0,a.jsx)("strong",{children:"Description: "}),null==_?void 0:_.description]})}),(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsxs)("p",{className:"text-sm",children:[(0,a.jsx)("strong",{children:"Status: "}),null==_?void 0:_.status]})}),(null==_?void 0:_.filesAttached)&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:(0,a.jsx)("strong",{children:"Attached File:"})}),(0,a.jsx)("div",{className:"flex flex-col items-start space-y-2",children:_.filesAttached.match(/\.(jpeg|jpg|gif|png)$/)?(0,a.jsx)(n.default,{src:_.filesAttached,alt:"Attached file",width:200,height:200,className:"object-cover rounded-md shadow-md"}):(0,a.jsx)("a",{href:_.filesAttached,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 underline",children:"View/Download File"})})]})]})]})}),R?(0,a.jsx)("div",{className:"flex justify-center  items-center h-[60vh]",children:(0,a.jsx)(m.Z,{className:"h-10 w-10 animate-spin text-blue-500"})}):"cards"===z?(0,a.jsx)("div",{className:"mt-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:S.length>0?S.map(e=>(0,a.jsxs)(v.Zb,{className:"p-4 shadow-md rounded-lg border",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",children:e.subject}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:e._id}),(0,a.jsx)(x.z,{variant:"outline",onClick:()=>G(e._id),size:"icon",className:"p-1",children:(0,a.jsx)(u.Z,{className:"h-4 w-4"})})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-2",children:[(0,a.jsx)("strong",{children:"Description:"})," ",e.description]}),(0,a.jsxs)("p",{className:"text-sm font-medium mt-2",children:[(0,a.jsx)("strong",{children:"Status:"})," ",e.status]}),e.filesAttached&&(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Attached File:"}),e.filesAttached.match(/\.(jpeg|jpg|gif|png)$/)?(0,a.jsx)(n.default,{src:e.filesAttached,alt:"Attached file",width:80,height:80,className:"rounded-md object-cover mt-2"}):(0,a.jsx)("a",{href:e.filesAttached,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 underline text-sm",children:"View/Download File"})]}),(0,a.jsxs)("div",{className:"mt-4 flex justify-between items-center",children:[(0,a.jsx)(x.z,{onClick:()=>W(e),size:"sm",children:"Edit"}),(0,a.jsx)(x.z,{onClick:()=>Q(e),size:"sm",children:"Open Ticket"})]})]},e._id)):(0,a.jsx)("p",{className:"text-center text-gray-500",children:"No tickets available."})}):(0,a.jsx)("div",{className:"overflow-x-auto mt-6",children:(0,a.jsxs)("table",{className:"min-w-full table-auto",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"bg-white text-black",children:[(0,a.jsx)("th",{className:"px-4 py-2 text-left",children:"Subject"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left",children:"Ticket ID"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left",children:"Description"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left",children:"Status"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left"})]})}),(0,a.jsx)("tbody",{children:S.length>0?S.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-4 py-2 text-sm border-gray-300",children:e.subject.length>45?(0,a.jsxs)(a.Fragment,{children:[e.description.substring(0,45)," ",(0,a.jsx)("span",{onClick:()=>alert(e.subject),className:"text-white-500 cursor-pointer",children:"..."})]}):e.subject}),(0,a.jsx)("td",{className:"px-4 py-2",children:e._id}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm border-gray-300",children:e.description.length>45?(0,a.jsxs)(a.Fragment,{children:[e.description.substring(0,45)," ",(0,a.jsx)("span",{onClick:()=>alert(e.description),className:"text-white-500 cursor-pointer",children:"..."})]}):e.description}),(0,a.jsx)("td",{className:"px-4 py-2",children:e.status}),(0,a.jsxs)("td",{className:"px-4 py-2 flex justify-between",children:[(0,a.jsx)(x.z,{onClick:()=>W(e),size:"sm",className:"mr-2 mt-2",variant:"outline",children:"Edit"}),(0,a.jsx)(x.z,{onClick:()=>Q(e),size:"sm",className:"ml-2 mt-2",variant:"outline",children:"Open Ticket"})]})]},e._id)):(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:5,className:"px-4 py-2 text-center text-gray-500",children:"No tickets available."})})})]})})]})},N=s(95958),w=s(11005);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let k=(0,d.Z)("HeartHandshake",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}],["path",{d:"M12 5 9.04 7.96a2.17 2.17 0 0 0 0 3.08v0c.82.82 2.13.85 3 .07l2.07-1.9a2.82 2.82 0 0 1 3.79 0l2.96 2.66",key:"12sd6o"}],["path",{d:"m18 15-2-2",key:"60u0ii"}],["path",{d:"m15 18-2-2",key:"6p76be"}]]),S=[{href:"#",icon:(0,a.jsx)(n.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,a.jsx)(w.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/settings/support",icon:(0,a.jsx)(k,{className:"h-5 w-5"}),label:"support"}];var C=s(64797),A=s(62688),T=()=>{let e=(0,l.v9)(e=>e.user),[,t]=(0,i.useState)(null),[s,n]=(0,i.useState)({name:"",email:"",message:""}),c=[],d=e=>{let{id:t,value:s}=e.target;"message"===t&&s.length>500||n(e=>({...e,[t]:s}))};return(0,i.useEffect)(()=>{(null==e?void 0:e.type)?t(e.type):t(r.Z.get("userType")||null)},[e]),(0,a.jsxs)("div",{className:"",children:[(0,a.jsx)(C.Z,{menuItemsTop:S,menuItemsBottom:c,active:"support"}),(0,a.jsxs)("div",{className:"flex flex-col sm:gap-4 sm:py-4  sm:pl-14 mb-8",children:[(0,a.jsx)(A.Z,{menuItemsTop:S,menuItemsBottom:c,activeMenu:"Dashboard",breadcrumbItems:[{label:"Business",link:"/dashboard/business"},{label:"Support",link:"#"}]}),(0,a.jsx)("div",{className:"ml-2",children:(0,a.jsxs)("div",{className:"mb-8 ",children:[(0,a.jsx)(b,{})," "]})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsxs)("section",{className:"px-4 pt-20 md:px-6",children:[(0,a.jsx)("div",{className:"max-w-3xl mx-auto text-center",children:(0,a.jsx)("h2",{className:" sm:text-3xl",children:"FAQs"})}),(0,a.jsx)(N.Z,{})]}),(0,a.jsx)("section",{className:"px-4 py-20 md:px-6",children:(0,a.jsx)("div",{className:"max-w-3xl mx-auto text-center"})}),(0,a.jsx)("section",{id:"Contact",className:"px-4 py-20 md:px-6",children:(0,a.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[(0,a.jsx)("h2",{className:" sm:text-3xl",children:"Get in Touch"}),(0,a.jsx)("p",{className:"mt-4 md:text-xl",children:"Have a project in mind? Let's discuss how we can help."}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),(0,h.Am)({title:"Message sent successfully!",description:"Thanks for connecting! We will connect soon.",duration:1500}),n({name:"",email:"",message:""})},className:"mt-8 space-y-4 text-left",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-base font-medium ",htmlFor:"name",children:"Name"}),(0,a.jsx)(f.I,{className:"mt-2 w-full rounded-md  px-4 py-3  focus:outline-none focus:ring-2 focus:ring-[#00ffff]",id:"name",placeholder:"Enter your name",type:"text",required:!0,value:s.name,onChange:d})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-base font-medium ",htmlFor:"email",children:"Email"}),(0,a.jsx)(f.I,{className:"mt-2 w-full rounded-md  px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#00ffff]",id:"email",placeholder:"Enter your email",type:"email",required:!0,value:s.email,onChange:d})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-base font-medium ",htmlFor:"message",children:"Message"}),(0,a.jsx)(j.g,{className:"mt-2 w-full rounded-md px-4 py-3  focus:outline-none focus:ring-2 focus:ring-[#00ffff]",id:"message",required:!0,placeholder:"Enter your message",value:s.message,onChange:d}),(0,a.jsxs)("p",{className:"text-right text-sm text-gray-500",children:[s.message.length,"/",500," characters"]})]}),(0,a.jsx)(x.z,{type:"submit",className:"  px-8 py-3 rounded-md text-lg font-medium",children:"Send Message"})]})]})})]})]})]})}},95958:function(e,t,s){"use strict";var a=s(57437);s(2265);var i=s(35265);let l=[{question:"How can I start freelancing on our platform?",answer:"To start freelancing, you can create an account and set up your profile. Once done, you can browse available projects or create your own listings."},{question:"What types of projects are available?",answer:"Our platform hosts a wide range of projects including content creation, web development, graphic design, marketing, and more. You can find projects that match your skills and interests."},{question:"How are freelancers vetted on your platform?",answer:"We have a rigorous vetting process to ensure that freelancers have the necessary skills and experience. This includes reviewing portfolios, conducting interviews, and verifying credentials."},{question:"Can I hire freelancers from different countries?",answer:"Yes, our platform connects you with freelancers from around the world. You can choose freelancers based on their location, skills, and availability."},{question:"What payment methods are supported?",answer:"We support various payment methods including credit/debit cards, PayPal, and bank transfers. You can choose the payment method that is most convenient for you."}];t.Z=()=>(0,a.jsx)(i.UQ,{type:"single",collapsible:!0,className:"w-full md:w-3/4 lg:w-2/3 xl:w-1/2 mx-auto",children:l.map((e,t)=>(0,a.jsxs)(i.Qd,{value:"item-".concat(t),children:[(0,a.jsx)(i.o4,{children:e.question}),(0,a.jsx)(i.vF,{children:e.answer})]},t))})}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2455,9726,2688,2971,7023,1744],function(){return e(e.s=5092)}),_N_E=e.O()}]);