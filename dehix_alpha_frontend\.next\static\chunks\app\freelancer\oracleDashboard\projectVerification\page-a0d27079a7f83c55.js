(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9406],{38603:function(e,s,t){Promise.resolve().then(t.bind(t,79133))},87055:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(33480).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},4086:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(33480).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},79133:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return P}});var r=t(57437),l=t(404),i=t(29406),a=t(2265),c=t(89733),n=t(62688),d=t(54662),m=t(97694),o=t(64797),x=t(746),h=t(15922),j=t(87055),u=t(4086),f=t(47390),p=t(31014),v=t(39343),N=t(59772),g=t(48185),y=t(79055),b=t(93363),w=t(89736),S=t(4919),C=t(78068);let k=N.z.object({type:N.z.enum(["Approved","Denied"],{required_error:"You need to select a type."}),comment:N.z.string().optional()});var _=e=>{let{_id:s,projectName:t,description:l,githubLink:i,startFrom:n,endTo:d,reference:o,techUsed:x,comments:N,status:_,role:Z,projectType:P,onStatusUpdate:E,onCommentUpdate:I}=e,[V,z]=(0,a.useState)(_),D=(0,v.cI)({resolver:(0,p.F)(k)}),A=D.watch("type");async function F(e){try{await h.b.put("/verification/".concat(s,"/oracle?doc_type=project"),{comments:e.comment,verification_status:e.type})}catch(e){(0,C.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}z(e.type),E(e.type),I(e.comment||"")}return(0,a.useEffect)(()=>{z(_)},[_]),(0,r.jsxs)(g.Zb,{className:"min-w-[90vw] mx-auto md:min-w-[30vw] md:min-h-[65vh]",children:[(0,r.jsxs)(g.Ol,{children:[(0,r.jsxs)(g.ll,{className:"flex justify-between",children:[(0,r.jsx)("span",{children:t}),i&&(0,r.jsx)("div",{className:"ml-auto",children:(0,r.jsx)("a",{href:i,className:"text-sm text-white underline",children:(0,r.jsx)(j.Z,{})})})]}),(0,r.jsxs)(g.SZ,{className:"mt-1 text-justify text-gray-600",children:["Pending"===V||"added"===V||"reapplied"===V?(0,r.jsx)(y.C,{className:"bg-warning-foreground text-white my-2",children:V}):"Approved"===V||"Verified"===V||"verified"===V?(0,r.jsx)(y.C,{className:"bg-success text-white my-2",children:V}):(0,r.jsx)(y.C,{className:"bg-red-500 text-white my-2",children:"Denied"}),(0,r.jsx)("br",{}),l]})]}),(0,r.jsx)(g.aY,{children:(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("span",{className:"font-semibold",children:"Tech Used:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:(x||[]).map((e,s)=>(0,r.jsx)(y.C,{className:"uppercase",variant:"secondary",children:e},s))})]}),(0,r.jsx)("div",{className:"mt-3",children:(0,r.jsxs)("p",{className:"text-m text-gray-600 flex items-center",children:["Role: ",Z]})}),(0,r.jsx)("div",{className:"mt-3",children:(0,r.jsxs)("p",{className:"text-m text-gray-600 flex items-center",children:["Project Type:",P]})}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsxs)(w.u,{children:[(0,r.jsx)(w.aJ,{asChild:!0,children:(0,r.jsxs)("p",{className:"text-sm text-gray-600 flex items-center",children:[(0,r.jsx)(u.Z,{className:"mr-2"}),o]})}),(0,r.jsx)(w._v,{side:"bottom",children:o})]})}),N&&(0,r.jsxs)("p",{className:"mt-2 flex items-center text-gray-500 border p-3 rounded",children:[(0,r.jsx)(f.Z,{className:"mr-2"}),N]})]})}),(0,r.jsxs)(g.eW,{className:"flex flex-col items-center",children:[(0,r.jsxs)("div",{className:"flex gap-4 text-gray-500",children:[new Date(n).toLocaleDateString()," -"," ","current"!==d?new Date(d).toLocaleDateString():"Current"]}),("Pending"===V||"added"===V||"reapplied"===V)&&(0,r.jsx)(b.l0,{...D,children:(0,r.jsxs)("form",{onSubmit:D.handleSubmit(F),className:"w-full space-y-6 mt-6",children:[(0,r.jsx)(b.Wi,{control:D.control,name:"type",render:e=>{let{field:s}=e;return(0,r.jsxs)(b.xJ,{className:"space-y-3",children:[(0,r.jsx)(b.lX,{children:"Choose Verification Status:"}),(0,r.jsx)(b.NI,{children:(0,r.jsxs)(m.E,{onValueChange:s.onChange,defaultValue:s.value,className:"flex flex-col space-y-1",children:[(0,r.jsxs)(b.xJ,{className:"flex items-center space-x-3",children:[(0,r.jsx)(b.NI,{children:(0,r.jsx)(m.m,{value:"Approved"})}),(0,r.jsx)(b.lX,{className:"font-normal",children:"Approved"})]}),(0,r.jsxs)(b.xJ,{className:"flex items-center space-x-3",children:[(0,r.jsx)(b.NI,{children:(0,r.jsx)(m.m,{value:"Denied"})}),(0,r.jsx)(b.lX,{className:"font-normal",children:"Denied"})]})]})}),(0,r.jsx)(b.zG,{})]})}}),(0,r.jsx)(b.Wi,{control:D.control,name:"comment",render:e=>{let{field:s}=e;return(0,r.jsxs)(b.xJ,{children:[(0,r.jsx)(b.lX,{children:"Comments:"}),(0,r.jsx)(b.NI,{children:(0,r.jsx)(S.g,{placeholder:"Enter comments:",...s})}),(0,r.jsx)(b.zG,{})]})}}),(0,r.jsx)(c.z,{type:"submit",className:"w-full",disabled:!A||D.formState.isSubmitting,children:"Submit"})]})})]})]})},Z=t(97540);function P(){let[e,s]=(0,a.useState)([]),[t,j]=(0,a.useState)("all"),[u,f]=(0,a.useState)(!1),p=e=>{j(e),f(!1)},v=e.filter(e=>"all"===t||e.verificationStatus===t||"current"===t&&e.verificationStatus===Z.sB.PENDING),N=(0,a.useCallback)(async()=>{try{let e=(await h.b.get("/verification/oracle?doc_type=project")).data.data.flatMap(e=>{var s;return(null===(s=e.result)||void 0===s?void 0:s.projects)?Object.values(e.result.projects).map(s=>({...s,verifier_id:e.verifier_id,verifier_username:e.verifier_username})):[]});s(e)}catch(e){(0,C.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."}),console.log(e,"error in getting verification data")}},[]);(0,a.useEffect)(()=>{N()},[N]);let g=(t,r)=>{let l=[...e];l[t].verificationStatus=r,s(l)},y=(t,r)=>{let l=[...e];l[t].comments=r,s(l)};return(0,r.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,r.jsx)(o.Z,{menuItemsTop:x.y,menuItemsBottom:x.$,active:"Project Verification"}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,r.jsx)(n.Z,{menuItemsTop:x.y,menuItemsBottom:x.$,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Oracle",link:"#"},{label:"Project Verification",link:"#"}]}),(0,r.jsxs)("div",{className:"mb-8 ml-4 flex justify-between mt-8 md:mt-4 items-center",children:[(0,r.jsxs)("div",{className:"mb-8 ",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Project Verification"}),(0,r.jsx)("p",{className:"text-gray-400 mt-2",children:"Monitor the status of your project verifications."})]}),(0,r.jsx)(c.z,{variant:"outline",size:"icon",className:"mr-8 mb-12",onClick:()=>f(!0),children:(0,r.jsx)(l.Z,{className:"h-4 w-4"})})]}),(0,r.jsx)(d.Vq,{open:u,onOpenChange:f,children:(0,r.jsxs)(d.cZ,{children:[(0,r.jsx)(d.fK,{children:(0,r.jsx)(d.$N,{children:"Filter Project Status"})}),(0,r.jsxs)(m.E,{defaultValue:"all",value:t,onValueChange:e=>p(e),className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.m,{value:"all",id:"filter-all"}),(0,r.jsx)("label",{htmlFor:"filter-all",children:"All"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.m,{value:"current",id:"filter-current"}),(0,r.jsx)("label",{htmlFor:"filter-current",children:"Pending"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.m,{value:"verified",id:"filter-verified"}),(0,r.jsx)("label",{htmlFor:"filter-verified",children:"Verified"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.m,{value:"rejected",id:"filter-rejected"}),(0,r.jsx)("label",{htmlFor:"filter-rejected",children:"Rejected"})]})]}),(0,r.jsx)(d.cN,{children:(0,r.jsx)(c.z,{type:"button",onClick:()=>f(!1),children:"Close"})})]})}),(0,r.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8 grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3",children:[v.map((e,s)=>(0,r.jsx)(_,{_id:e._id,projectName:e.projectName,description:e.description,githubLink:e.githubLink,startFrom:e.start,endTo:e.end,role:e.role,projectType:e.projectType,reference:e.refer,techUsed:e.techUsed,comments:e.comments,status:e.verificationStatus,onStatusUpdate:e=>g(s,e),onCommentUpdate:e=>y(s,e)},s)),0===e.length?(0,r.jsxs)("div",{className:"text-center w-[90vw] px-auto mt-20 py-10",children:[(0,r.jsx)(i.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No Project verification for you now."})]}):null]})]})]})}}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,1374,2455,9726,2688,2480,2971,7023,1744],function(){return e(e.s=38603)}),_N_E=e.O()}]);