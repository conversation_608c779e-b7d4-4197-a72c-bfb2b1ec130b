(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2605],{8477:function(e,t,r){Promise.resolve().then(r.bind(r,32007))},6540:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},25912:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},24241:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},40036:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("ImagePlus",[["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7",key:"31hg93"}],["line",{x1:"16",x2:"22",y1:"5",y2:"5",key:"ez7e4s"}],["line",{x1:"19",x2:"19",y1:"2",y2:"8",key:"1gkr8c"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},67524:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},32007:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return w}});var a=r(57437),n=r(2265),s=r(11444),l=r(64797),i=r(27756),o=r(68515),c=r(48185),d=e=>{let{degree:t,universityName:r,fieldOfStudy:n,startDate:s,endDate:l,grade:i}=e;return(0,a.jsxs)(c.Zb,{className:"w-full h-full mx-auto md:max-w-2xl",children:[(0,a.jsxs)(c.Ol,{children:[(0,a.jsx)(c.ll,{className:"flex",children:r||"University Name"}),(0,a.jsxs)(c.SZ,{className:"block mt-1 uppercase tracking-wide leading-tight font-medium ",children:[t||"Degree"," in ",n||"Field of Study"]})]}),(0,a.jsx)(c.aY,{children:(0,a.jsxs)("p",{className:" pt-4",children:["Grade: ",i||"N/A"]})}),(0,a.jsx)(c.eW,{className:"flex",children:(0,a.jsx)(o.Z,{startDate:s,endDate:l})})]})},u=r(15922),f=r(59772),m=r(39343),h=r(31014),x=r(92513),g=r(78068),j=r(55384),p=r(77209),y=r(89733),v=r(93363),D=r(54662),N=r(9437);let b=f.z.object({degree:f.z.string().optional(),universityName:f.z.string().optional(),fieldOfStudy:f.z.string().optional(),startDate:f.z.string().optional(),endDate:f.z.string().optional(),grade:f.z.string().optional()}).refine(e=>!e.startDate||!e.endDate||new Date(e.startDate)<new Date(e.endDate),{message:"Start Date must be before End Date",path:["endDate"]}),S=e=>{let{onFormSubmit:t}=e,[r,s]=(0,n.useState)(!1),[l,i]=(0,n.useState)(!1),o=new Date().toISOString().split("T")[0],c=(0,n.useRef)(null),d=(0,m.cI)({resolver:(0,h.F)(b),defaultValues:{degree:"",universityName:"",fieldOfStudy:"",startDate:"",endDate:"",grade:""}}),{showDraftDialog:f,setShowDraftDialog:S,confirmExitDialog:I,setConfirmExitDialog:w,loadDraft:A,discardDraft:E,handleSaveAndClose:k,handleDiscardAndClose:O,handleDialogClose:Z}=(0,N.Z)({form:d,formSection:"education",isDialogOpen:l,setIsDialogOpen:i,onSave:e=>{c.current={...e}},onDiscard:()=>{c.current=null}});async function F(e){s(!0);try{let r={...e,startDate:e.startDate?new Date(e.startDate).toISOString():null,endDate:e.endDate?new Date(e.endDate).toISOString():null,oracleAssigned:e.oracleAssigned||"",verificationStatus:e.verificationStatus||"ADDED",verificationUpdateTime:e.verificationUpdateTime||new Date,comments:""};await u.b.post("/freelancer/education",r),t(),i(!1),(0,g.Am)({title:"Education Added",description:"The education has been successfully added.",duration:1500})}catch(e){console.error("API Error:",e),(0,g.Am)({variant:"destructive",title:"Error",description:"Failed to add education. Please try again later.",duration:1500})}finally{s(!1)}}return(0,a.jsxs)(D.Vq,{open:l,onOpenChange:e=>{i(e),e||Z()},children:[(0,a.jsx)(D.hg,{asChild:!0,children:(0,a.jsx)(y.z,{variant:"outline",size:"icon",className:"my-auto",children:(0,a.jsx)(x.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)(D.cZ,{className:"lg:max-w-screen-lg overflow-y-scroll max-h-screen no-scrollbar",children:[(0,a.jsxs)(D.fK,{children:[(0,a.jsx)(D.$N,{children:"Add Education"}),(0,a.jsx)(D.Be,{children:"Add your relevant Education."})]}),(0,a.jsx)(v.l0,{...d,children:(0,a.jsxs)("form",{onSubmit:d.handleSubmit(F),className:"space-y-4",children:[(0,a.jsx)(v.Wi,{control:d.control,name:"degree",render:e=>{let{field:t}=e;return(0,a.jsxs)(v.xJ,{children:[(0,a.jsx)(v.lX,{children:"Enter Degree"}),(0,a.jsx)(v.NI,{children:(0,a.jsx)(p.I,{placeholder:"Enter your degree title",...t})}),(0,a.jsx)(v.pf,{children:"Enter your degree title"}),(0,a.jsx)(v.zG,{})]})}}),(0,a.jsx)(v.Wi,{control:d.control,name:"universityName",render:e=>{let{field:t}=e;return(0,a.jsxs)(v.xJ,{children:[(0,a.jsx)(v.lX,{children:"University Name"}),(0,a.jsx)(v.NI,{children:(0,a.jsx)(p.I,{placeholder:"Enter your university name",...t})}),(0,a.jsx)(v.pf,{children:"Enter your university name"}),(0,a.jsx)(v.zG,{})]})}}),(0,a.jsx)(v.Wi,{control:d.control,name:"fieldOfStudy",render:e=>{let{field:t}=e;return(0,a.jsxs)(v.xJ,{children:[(0,a.jsx)(v.lX,{children:"Enter Field of Study"}),(0,a.jsx)(v.NI,{children:(0,a.jsx)(p.I,{placeholder:"Enter your field of study",...t})}),(0,a.jsx)(v.pf,{children:"Enter Field of Study"}),(0,a.jsx)(v.zG,{})]})}}),(0,a.jsx)(v.Wi,{control:d.control,name:"startDate",render:e=>{let{field:t}=e;return(0,a.jsxs)(v.xJ,{children:[(0,a.jsx)(v.lX,{children:"Start Date"}),(0,a.jsx)(v.NI,{children:(0,a.jsx)(p.I,{type:"date",max:o,...t})}),(0,a.jsx)(v.pf,{children:"Select the start date"}),(0,a.jsx)(v.zG,{})]})}}),(0,a.jsx)(v.Wi,{control:d.control,name:"endDate",render:e=>{let{field:t}=e;return(0,a.jsxs)(v.xJ,{children:[(0,a.jsx)(v.lX,{children:"End Date"}),(0,a.jsx)(v.NI,{children:(0,a.jsx)(p.I,{type:"date",...t})}),(0,a.jsx)(v.pf,{children:"Select the end date"}),(0,a.jsx)(v.zG,{})]})}}),(0,a.jsx)(v.Wi,{control:d.control,name:"grade",render:e=>{let{field:t}=e;return(0,a.jsxs)(v.xJ,{children:[(0,a.jsx)(v.lX,{children:"Grade"}),(0,a.jsx)(v.NI,{children:(0,a.jsx)(p.I,{placeholder:"Enter your grade",...t})}),(0,a.jsx)(v.pf,{children:"Enter your grade"}),(0,a.jsx)(v.zG,{})]})}}),(0,a.jsx)(D.cN,{children:(0,a.jsx)(y.z,{type:"submit",disabled:r,children:r?"Loading...":"Create"})})]})})]}),I&&(0,a.jsx)(j.Z,{dialogChange:I,setDialogChange:w,heading:"Save Draft?",desc:"Do you want to save your draft before leaving?",handleClose:O,handleSave:k,btn1Txt:"Don't save",btn2Txt:"Yes save"}),f&&(0,a.jsx)(j.Z,{dialogChange:f,setDialogChange:S,heading:"Load Draft?",desc:"You have unsaved data. Would you like to restore it?",handleClose:E,handleSave:A,btn1Txt:" No, start fresh",btn2Txt:"Yes, load draft"})]})};var I=r(62688);function w(){let e=(0,s.v9)(e=>e.user),[t,r]=(0,n.useState)(!1),[o,c]=(0,n.useState)([]);return(0,n.useEffect)(()=>{(async()=>{try{var t;let r=await u.b.get("/freelancer/".concat(e.uid)),a=null===(t=r.data)||void 0===t?void 0:t.data;if(!a||"object"!=typeof a){console.warn("No education data found, setting empty array."),c([]);return}c(Object.values(r.data.data.education))}catch(e){(0,g.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."}),console.error("API Error:",e),c([])}})()},[e.uid,t]),(0,a.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,a.jsx)(l.Z,{menuItemsTop:i.y,menuItemsBottom:i.$,active:"Education",isKycCheck:!0}),(0,a.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,a.jsx)(I.Z,{menuItemsTop:i.y,menuItemsBottom:i.$,activeMenu:"Education",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Settings",link:"#"},{label:"Educational Info",link:"#"}]}),(0,a.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8    grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3",children:[o.map((e,t)=>(0,a.jsx)(d,{...e},t)),(0,a.jsx)(S,{onFormSubmit:()=>{r(e=>!e)}})]})]})]})}},68515:function(e,t,r){"use strict";var a=r(57437);r(2265);var n=r(24241);t.Z=e=>{let{startDate:t,endDate:r}=e,s=t?new Date(t).toLocaleDateString():"Start Date N/A",l="current"!==r&&r?new Date(r).toLocaleDateString():"Still Going On!";return(0,a.jsxs)("div",{className:"flex relative whitespace-nowrap items-start sm:items-center gap-1 rounded-md ",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2 ",children:[(0,a.jsx)(n.Z,{className:"w-4 h-4 sm:w-5 sm:h-5 "}),(0,a.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:"Start  ".concat(s)})]}),(0,a.jsx)("p",{children:"-"}),(0,a.jsx)("div",{className:"flex items-center ",children:(0,a.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:" ".concat(l)})})]})}},55384:function(e,t,r){"use strict";var a=r(57437);r(2265);var n=r(54662),s=r(89733);t.Z=e=>{let{dialogChange:t,setDialogChange:r,heading:l,desc:i,handleClose:o,handleSave:c,btn1Txt:d,btn2Txt:u}=e;return(0,a.jsx)(n.Vq,{open:t,onOpenChange:r,children:(0,a.jsxs)(n.cZ,{children:[(0,a.jsxs)(n.fK,{children:[(0,a.jsx)(n.$N,{children:l}),(0,a.jsx)(n.Be,{children:i})]}),(0,a.jsxs)(n.cN,{children:[(0,a.jsx)(s.z,{variant:"outline",onClick:o,children:d}),(0,a.jsx)(s.z,{onClick:c,children:u})]})]})})}},93363:function(e,t,r){"use strict";r.d(t,{NI:function(){return g},Wi:function(){return u},l0:function(){return c},lX:function(){return x},pf:function(){return j},xJ:function(){return h},zG:function(){return p}});var a=r(57437),n=r(2265),s=r(63355),l=r(39343),i=r(49354),o=r(70402);let c=l.RV,d=n.createContext({}),u=e=>{let{...t}=e;return(0,a.jsx)(d.Provider,{value:{name:t.name},children:(0,a.jsx)(l.Qr,{...t})})},f=()=>{let e=n.useContext(d),t=n.useContext(m),{getFieldState:r,formState:a}=(0,l.Gc)(),s=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...s}},m=n.createContext({}),h=n.forwardRef((e,t)=>{let{className:r,...s}=e,l=n.useId();return(0,a.jsx)(m.Provider,{value:{id:l},children:(0,a.jsx)("div",{ref:t,className:(0,i.cn)("space-y-2",r),...s})})});h.displayName="FormItem";let x=n.forwardRef((e,t)=>{let{className:r,...n}=e,{error:s,formItemId:l}=f();return(0,a.jsx)(o.Label,{ref:t,className:(0,i.cn)(s&&"text-destructive",r),htmlFor:l,...n})});x.displayName="FormLabel";let g=n.forwardRef((e,t)=>{let{...r}=e,{error:n,formItemId:l,formDescriptionId:i,formMessageId:o}=f();return(0,a.jsx)(s.g7,{ref:t,id:l,"aria-describedby":n?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!n,...r})});g.displayName="FormControl";let j=n.forwardRef((e,t)=>{let{className:r,...n}=e,{formDescriptionId:s}=f();return(0,a.jsx)("p",{ref:t,id:s,className:(0,i.cn)("text-sm text-muted-foreground",r),...n})});j.displayName="FormDescription";let p=n.forwardRef((e,t)=>{let{className:r,children:n,...s}=e,{error:l,formMessageId:o}=f(),c=l?String(null==l?void 0:l.message):n;return c?(0,a.jsx)("p",{ref:t,id:o,className:(0,i.cn)("text-sm font-medium text-destructive",r),...s,children:c}):null});p.displayName="FormMessage"},70402:function(e,t,r){"use strict";r.r(t),r.d(t,{Label:function(){return c}});var a=r(57437),n=r(2265),s=r(38364),l=r(12218),i=r(49354);let o=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.f,{ref:t,className:(0,i.cn)(o(),r),...n})});c.displayName=s.f.displayName},27756:function(e,t,r){"use strict";r.d(t,{$:function(){return f},y:function(){return u}});var a=r(57437),n=r(11005),s=r(52022),l=r(25912),i=r(67524),o=r(6540),c=r(40036),d=r(66648);let u=[{href:"#",icon:(0,a.jsx)(d.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,a.jsx)(n.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/settings/personal-info",icon:(0,a.jsx)(s.Z,{className:"h-5 w-5"}),label:"Personal Info"},{href:"/freelancer/settings/professional-info",icon:(0,a.jsx)(l.Z,{className:"h-5 w-5"}),label:"Professional Info"},{href:"/freelancer/settings/projects",icon:(0,a.jsx)(i.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"/freelancer/settings/education-info",icon:(0,a.jsx)(o.Z,{className:"h-5 w-5"}),label:"Education"},{href:"/freelancer/settings/resume",icon:(0,a.jsx)(c.Z,{className:"h-5 w-5"}),label:"Portfolio"}],f=[]},9437:function(e,t,r){"use strict";var a=r(2265),n=r(86763);t.Z=e=>{let{form:t,formSection:r="",isDialogOpen:s,setIsDialogOpen:l,onSave:i,onDiscard:o,setCurrSkills:c}=e,[d,u]=(0,a.useState)(!1),[f,m]=(0,a.useState)(!1),h=(0,a.useRef)(!1),x=(0,a.useRef)(null),g=e=>{if(!r)return;let t=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");t[r]&&(x.current=t[r]),Object.values(e).some(e=>void 0!==e&&""!==e)&&(t[r]=e,localStorage.setItem("DEHIX_DRAFT",JSON.stringify(t)),(0,n.Am)({title:"Draft Saved",description:"Your ".concat(r," draft has been saved."),duration:1500}))};(0,a.useEffect)(()=>{if(s&&!h.current&&r){let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");e&&e[r]&&u(!0),h.current=!0}},[s,r]);let j=e=>e&&"object"==typeof e?Object.fromEntries(Object.entries(e).map(e=>{let[t,r]=e;return[t,"string"==typeof r?r.trim():r]})):{};return{showDraftDialog:d,setShowDraftDialog:u,confirmExitDialog:f,setConfirmExitDialog:m,loadDraft:()=>{if(!r)return;let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");e&&e[r]&&(Object.keys(e[r]).forEach(t=>{void 0===e[r][t]&&delete e[r][t]}),"projects"===r&&(delete e[r].verificationStatus,Array.isArray(e[r].techUsed)&&c(e[r].techUsed)),Object.entries(e[r]).some(e=>{let[,t]=e;return""!==t&&void 0!==t&&!(Array.isArray(t)&&0===t.length)})&&t&&(t.reset(e[r]),x.current=e[r],(0,n.Am)({title:"Draft Loaded",description:"Your ".concat(r," draft has been restored."),duration:1500})),u(!1))},discardDraft:()=>{if(!r)return;let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");e&&(delete e[r],0===Object.keys(e).length?localStorage.removeItem("DEHIX_DRAFT"):localStorage.setItem("DEHIX_DRAFT",JSON.stringify(e))),null==t||t.reset(),(0,n.Am)({title:"Draft Discarded",description:"Your ".concat(r," draft has been discarded."),duration:1500}),u(!1),o&&o()},handleSaveAndClose:()=>{if(!r)return;let e=null==t?void 0:t.getValues();g(e),(0,n.Am)({title:"Draft Saved",description:"Your draft has been saved.",duration:1500}),x.current=e,m(!1),l&&l(!1),i&&i(e)},handleDiscardAndClose:()=>{if(!r)return;let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");delete e[r],0===Object.keys(e).length?localStorage.removeItem("DEHIX_DRAFT"):localStorage.setItem("DEHIX_DRAFT",JSON.stringify(e)),(0,n.Am)({title:"Draft Discarded",description:"Your ".concat(r," draft has been discarded."),duration:1500}),m(!1),l&&l(!1),o&&o()},handleDialogClose:()=>{if(!s||!r)return;let e=(null==t?void 0:t.getValues())||{},a=x.current||{},n=j(e),i=j(a),o=Object.entries(i).some(e=>{let[t,r]=e,a=n[t];return Array.isArray(r)&&Array.isArray(a)?JSON.stringify(r)!==JSON.stringify(a):r!==a}),c=Object.entries(n).some(e=>{let[t,r]=e;return"verificationStatus"!==t&&void 0!==r&&""!==r&&void 0===i[t]});if(!o&&!c&&l){l(!1);return}Object.values(n).some(e=>null==e?void 0:e.toString().trim())?m(!0):l&&l(!1)},saveDraft:g,hasOtherValues:(0,a.useCallback)(e=>Object.entries(e).some(e=>{let[t,r]=e;return"profiles"!==t&&(Array.isArray(r)&&r.length>0&&("urls"!==t||r.some(e=>{var t;return(null==e?void 0:null===(t=e.value)||void 0===t?void 0:t.trim())!==""}))||"string"==typeof r&&""!==r.trim()||"number"==typeof r&&!isNaN(r))}),[]),hasProfiles:(0,a.useCallback)(e=>null==e?void 0:e.some(e=>Object.values(e).some(e=>Array.isArray(e)&&e.length>0||"string"==typeof e&&""!==e.trim()||"number"==typeof e&&!isNaN(e))),[])}}},38364:function(e,t,r){"use strict";r.d(t,{f:function(){return i}});var a=r(2265),n=r(18676),s=r(57437),l=a.forwardRef((e,t)=>(0,s.jsx)(n.WV.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,1374,2455,9726,2688,2971,7023,1744],function(){return e(e.s=8477)}),_N_E=e.O()}]);