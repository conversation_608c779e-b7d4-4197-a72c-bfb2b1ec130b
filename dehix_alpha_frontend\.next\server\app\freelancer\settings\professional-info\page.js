(()=>{var e={};e.id=7785,e.ids=[7785],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},84388:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c}),t(10836),t(54302),t(12523);var s=t(23191),a=t(88716),n=t(37922),i=t.n(n),o=t(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let c=["",{children:["freelancer",{children:["settings",{children:["professional-info",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,10836)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\settings\\professional-info\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\settings\\professional-info\\page.tsx"],m="/freelancer/settings/professional-info/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/freelancer/settings/professional-info/page",pathname:"/freelancer/settings/professional-info",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},20592:(e,r,t)=>{Promise.resolve().then(t.bind(t,75621))},6343:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},47546:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},37358:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},12893:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},40617:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},48705:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},75621:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>T});var s=t(10326),a=t(25842),n=t(17577),i=t(92166),o=t(12893),l=t(40617),c=t(1370),d=t(38443),m=t(29752);let u=({company:e,jobTitle:r,workDescription:t,workFrom:a,workTo:n,referencePersonName:i,referencePersonContact:u,githubRepoLink:x,verificationStatus:h,comments:p})=>(0,s.jsxs)(m.Zb,{className:"w-full h-full mx-auto md:max-w-2xl",children:[(0,s.jsxs)(m.Ol,{children:[(0,s.jsxs)(m.ll,{className:"flex",children:[e,x&&s.jsx("div",{className:"ml-auto",children:s.jsx("a",{href:x,className:"text-sm text-white underline",children:s.jsx(o.Z,{})})})]}),s.jsx(m.SZ,{className:"block mt-1 uppercase tracking-wide leading-tight font-medium text-white",children:r})]}),(0,s.jsxs)(m.aY,{children:[s.jsx(d.C,{className:`px-3 py-1 text-xs font-bold rounded-full border transition ${(e=>{switch(e.toLowerCase()){case"pending":return"bg-yellow-500 hover:bg-yellow-600";case"verified":return"bg-green-500 hover:bg-green-600";default:return"bg-blue-500 hover:bg-blue-600"}})(h)}`,children:h.toUpperCase()}),s.jsx("p",{className:"text-gray-300 pt-4",children:t}),(0,s.jsxs)("p",{className:"mt-2 flex text-gray-500 border p-3 rounded",children:[s.jsx(l.Z,{className:"pr-1"}),p]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Reference: ",i]}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Contact: ",u]})]})]}),s.jsx(m.eW,{className:"flex",children:s.jsx(c.Z,{startDate:a,endDate:n})})]});var x=t(6260),h=t(74064),p=t(74723),f=t(27256),j=t(83855),g=t(86333),y=t(24230),b=t(20495),v=t(24118),w=t(91664),N=t(9969),k=t(41190),D=t(56627),S=t(30351);let I=(e,r)=>{let t=e.workFrom?new Date(e.workFrom):null,s=e.workTo?new Date(e.workTo):null;if(t&&s){t>s&&r.addIssue({code:"custom",message:"Work From date cannot be after Work To date.",path:["workFrom"]});let e=new Date(t);e.setMonth(e.getMonth()+1),s<e&&r.addIssue({code:"custom",message:"Work To date must be at least 1 month after Work From date.",path:["workTo"]})}},C=f.z.object({company:f.z.string().min(1,{message:"Company name is required."}),jobTitle:f.z.string().min(1,{message:"Job Title is required."}),workDescription:f.z.string().min(1,{message:"Work Description is required."}),workFrom:f.z.string().min(1,{message:"Work from is required."}),workTo:f.z.string().min(1,{message:"Work to is required."}),referencePersonName:f.z.string().min(1,{message:"Reference Person Name is required."}),referencePersonContact:f.z.string().min(1,{message:"Reference Person Contact is required."}),githubRepoLink:f.z.string().trim().transform(e=>""===e?void 0:e).optional().refine(e=>!e||e.startsWith("https://github.com/"),{message:"GitHub URL must start with https://github.com/"}),comments:f.z.string().optional()}).superRefine((e,r)=>{I(e,r)}),F=({onFormSubmit:e})=>{let[r,t]=(0,n.useState)(1),[a,i]=(0,n.useState)(!1),[o,l]=(0,n.useState)(!1),c=new Date().toISOString().split("T")[0],d=(0,n.useRef)(null),m=(0,p.cI)({resolver:(0,h.F)(C),defaultValues:{company:"",jobTitle:"",workDescription:"",workFrom:"",workTo:""}}),u=()=>{let{company:e,jobTitle:r,workDescription:t,workFrom:s,workTo:a}=m.getValues();if(!e||!r||!t||!s||!a)return(0,D.Am)({variant:"destructive",title:"Missing fields",description:"Please fill all required fields in Step 1."}),!1;let n=new Date(s),i=new Date(a);if(n>i)return m.setError("workFrom",{type:"manual",message:"Work From date cannot be after Work To date."}),!1;let o=new Date(n);return o.setMonth(o.getMonth()+1),!(i<o)||(m.setError("workTo",{type:"manual",message:"Work To date must be at least 1 month after Work From date."}),!1)};(0,n.useEffect)(()=>{o&&(t(1),m.reset({company:"",jobTitle:"",workDescription:"",workFrom:"",workTo:"",referencePersonName:"",referencePersonContact:"",githubRepoLink:"",comments:""}))},[o,m]);let{showDraftDialog:f,setShowDraftDialog:I,confirmExitDialog:F,setConfirmExitDialog:P,loadDraft:A,discardDraft:q,handleSaveAndClose:T,handleDiscardAndClose:O,handleDialogClose:E}=(0,S.Z)({form:m,formSection:"experience",isDialogOpen:o,setIsDialogOpen:l,onSave:e=>{d.current={...e}},onDiscard:()=>{d.current=null}});async function Z(r){i(!0);try{await x.b.post("/freelancer/experience",{company:r.company||"",jobTitle:r.jobTitle||"",workDescription:r.workDescription||"",workFrom:r.workFrom?new Date(r.workFrom).toISOString():null,workTo:r.workTo?new Date(r.workTo).toISOString():null,referencePersonName:r.referencePersonName||"",referencePersonContact:r.referencePersonContact||"",githubRepoLink:r.githubRepoLink||"",oracleAssigned:null,verificationStatus:"ADDED",verificationUpdateTime:new Date().toISOString(),comments:r.comments||""}),e(),l(!1),(0,D.Am)({title:"Experience Added",description:"The experience has been successfully added."})}catch(e){console.error("API Error:",e),(0,D.Am)({variant:"destructive",title:"Error",description:"Failed to add experience. Please try again later."})}finally{i(!1)}}return(0,s.jsxs)(v.Vq,{open:o,onOpenChange:e=>{l(e),e||E()},children:[s.jsx(v.hg,{asChild:!0,children:s.jsx(w.z,{variant:"outline",size:"icon",className:"my-auto",children:s.jsx(j.Z,{className:"h-4 w-4"})})}),(0,s.jsxs)(v.cZ,{className:"lg:max-w-screen-lg overflow-y-scroll max-h-screen no-scrollbar",children:[(0,s.jsxs)(v.fK,{children:[(0,s.jsxs)(v.$N,{children:["Add Experience - Step ",r," of 2"]}),s.jsx(v.Be,{children:1===r?"Fill in the basic details of your work experience.":"Fill in the reference and additional details."})]}),s.jsx(N.l0,{...m,children:(0,s.jsxs)("form",{onSubmit:m.handleSubmit(Z),className:"space-y-4",children:[1===r&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(N.Wi,{control:m.control,name:"company",render:({field:e})=>(0,s.jsxs)(N.xJ,{children:[s.jsx(N.lX,{children:"Company"}),s.jsx(N.NI,{children:s.jsx(k.I,{placeholder:"Enter company name",...e})}),s.jsx(N.pf,{children:"Enter the company name"}),s.jsx(N.zG,{})]})}),s.jsx(N.Wi,{control:m.control,name:"jobTitle",render:({field:e})=>(0,s.jsxs)(N.xJ,{children:[s.jsx(N.lX,{children:"Job Title"}),s.jsx(N.NI,{children:s.jsx(k.I,{placeholder:"Enter job title",...e})}),s.jsx(N.pf,{children:"Enter the job title"}),s.jsx(N.zG,{})]})}),s.jsx(N.Wi,{control:m.control,name:"workDescription",render:({field:e})=>(0,s.jsxs)(N.xJ,{children:[s.jsx(N.lX,{children:"Work Description"}),s.jsx(N.NI,{children:s.jsx(k.I,{placeholder:"Enter work description",...e})}),s.jsx(N.pf,{children:"Enter the work description"}),s.jsx(N.zG,{})]})}),s.jsx(N.Wi,{control:m.control,name:"workFrom",render:({field:e})=>(0,s.jsxs)(N.xJ,{children:[s.jsx(N.lX,{children:"Work From"}),s.jsx(N.NI,{children:s.jsx(k.I,{type:"date",max:c,...e})}),s.jsx(N.pf,{children:"Select the start date"}),s.jsx(N.zG,{})]})}),s.jsx(N.Wi,{control:m.control,name:"workTo",render:({field:e})=>(0,s.jsxs)(N.xJ,{children:[s.jsx(N.lX,{children:"Work To"}),s.jsx(N.NI,{children:s.jsx(k.I,{type:"date",...e})}),s.jsx(N.pf,{children:"Select the end date"}),s.jsx(N.zG,{})]})})]}),2===r&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(N.Wi,{control:m.control,name:"referencePersonName",render:({field:e})=>(0,s.jsxs)(N.xJ,{children:[s.jsx(N.lX,{children:"Reference Person Name"}),s.jsx(N.NI,{children:s.jsx(k.I,{placeholder:"Enter reference person name",...e})}),s.jsx(N.pf,{children:"Enter the reference person's name"}),s.jsx(N.zG,{})]})}),s.jsx(N.Wi,{control:m.control,name:"referencePersonContact",render:({field:e})=>(0,s.jsxs)(N.xJ,{children:[s.jsx(N.lX,{children:"Reference Person Contact"}),s.jsx(N.NI,{children:s.jsx(k.I,{placeholder:"Enter reference person contact",...e})}),s.jsx(N.pf,{children:"Enter the reference person's contact"}),s.jsx(N.zG,{})]})}),s.jsx(N.Wi,{control:m.control,name:"githubRepoLink",render:({field:e})=>(0,s.jsxs)(N.xJ,{children:[s.jsx(N.lX,{children:"GitHub Repo Link"}),s.jsx(N.NI,{children:s.jsx(k.I,{placeholder:"Enter GitHub repository link",...e})}),s.jsx(N.pf,{children:"Enter the GitHub repository link (optional)"}),s.jsx(N.zG,{})]})}),s.jsx(N.Wi,{control:m.control,name:"comments",render:({field:e})=>(0,s.jsxs)(N.xJ,{children:[s.jsx(N.lX,{children:"Comments"}),s.jsx(N.NI,{children:s.jsx(k.I,{placeholder:"Enter any comments",...e})}),s.jsx(N.pf,{children:"Enter any comments (optional)"}),s.jsx(N.zG,{})]})})]}),s.jsx(v.cN,{className:"flex justify-between",children:2===r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(w.z,{type:"button",variant:"outline",onClick:()=>{2===r&&t(1)},children:[s.jsx(g.Z,{className:"h-4 w-4 mr-2"}),"Back"]}),s.jsx(w.z,{type:"submit",disabled:a,children:a?"Loading...":"Add Experience"})]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{})," ",(0,s.jsxs)(w.z,{type:"button",onClick:()=>{1===r&&u()&&t(2)},children:["Next",s.jsx(y.Z,{className:"h-4 w-4 ml-2"})]})]})})]})})]}),F&&s.jsx(b.Z,{dialogChange:F,setDialogChange:P,heading:"Save Draft?",desc:"Do you want to save your draft before leaving?",handleClose:O,handleSave:T,btn1Txt:"Don't save",btn2Txt:"Yes save"}),f&&s.jsx(b.Z,{dialogChange:f,setDialogChange:I,heading:"Load Draft?",desc:"You have unsaved data. Would you like to restore it?",handleClose:q,handleSave:A,btn1Txt:" No, start fresh",btn2Txt:"Yes, load draft"})]})};var P=t(45175),A=t(40588),q=t(38227);function T(){(0,a.v9)(e=>e.user);let[e,r]=(0,n.useState)(!1),[t,o]=(0,n.useState)([]),[l,c]=(0,n.useState)(!1);return(0,s.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[s.jsx(i.Z,{menuItemsTop:P.y,menuItemsBottom:P.$,active:"Professional Info",isKycCheck:!0}),(0,s.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[s.jsx(A.Z,{menuItemsTop:P.y,menuItemsBottom:P.$,activeMenu:"Professional Info",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Settings",link:"#"},{label:"Professional Info",link:"#"}]}),s.jsx("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8    grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3",children:l?s.jsx(O,{}):(0,s.jsxs)(s.Fragment,{children:[t.map((e,r)=>s.jsx(u,{...e},r)),s.jsx(F,{onFormSubmit:()=>{r(e=>!e)}})]})})]})]})}let O=()=>(0,s.jsxs)(s.Fragment,{children:[Array.from({length:3}).map((e,r)=>s.jsx(m.Zb,{className:"w-full mx-auto md:max-w-2xl p-4 bg-muted ",children:(0,s.jsxs)(m.aY,{className:"space-y-4",children:[s.jsx(q.O,{className:"h-6 w-32"}),s.jsx(q.O,{className:"h-4 w-40"}),s.jsx(q.O,{className:"h-6 w-16 rounded-md"}),s.jsx(q.O,{className:"h-4 w-full"}),s.jsx(q.O,{className:"h-10 w-full rounded-md"}),(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[s.jsx(q.O,{className:"h-4 w-24"}),s.jsx(q.O,{className:"h-4 w-20"})]}),s.jsx(q.O,{className:"h-6 w-40"})]})},r)),s.jsx(q.O,{className:"h-10 w-10 rounded-md"})]})},1370:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var s=t(10326);t(17577);var a=t(37358);let n=({startDate:e,endDate:r})=>{let t=e?new Date(e).toLocaleDateString():"Start Date N/A",n="current"!==r&&r?new Date(r).toLocaleDateString():"Still Going On!";return(0,s.jsxs)("div",{className:"flex relative whitespace-nowrap items-start sm:items-center gap-1 rounded-md ",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2 ",children:[s.jsx(a.Z,{className:"w-4 h-4 sm:w-5 sm:h-5 "}),s.jsx("span",{className:"text-xs sm:text-sm font-medium",children:`Start  ${t}`})]}),s.jsx("p",{children:"-"}),s.jsx("div",{className:"flex items-center ",children:s.jsx("span",{className:"text-xs sm:text-sm font-medium",children:` ${n}`})})]})}},20495:(e,r,t)=>{"use strict";t.d(r,{Z:()=>i});var s=t(10326);t(17577);var a=t(24118),n=t(91664);let i=({dialogChange:e,setDialogChange:r,heading:t,desc:i,handleClose:o,handleSave:l,btn1Txt:c,btn2Txt:d})=>s.jsx(a.Vq,{open:e,onOpenChange:r,children:(0,s.jsxs)(a.cZ,{children:[(0,s.jsxs)(a.fK,{children:[s.jsx(a.$N,{children:t}),s.jsx(a.Be,{children:i})]}),(0,s.jsxs)(a.cN,{children:[s.jsx(n.z,{variant:"outline",onClick:o,children:c}),s.jsx(n.z,{onClick:l,children:d})]})]})})},9969:(e,r,t)=>{"use strict";t.d(r,{NI:()=>f,Wi:()=>m,l0:()=>c,lX:()=>p,pf:()=>j,xJ:()=>h,zG:()=>g});var s=t(10326),a=t(17577),n=t(99469),i=t(74723),o=t(51223),l=t(44794);let c=i.RV,d=a.createContext({}),m=({...e})=>s.jsx(d.Provider,{value:{name:e.name},children:s.jsx(i.Qr,{...e})}),u=()=>{let e=a.useContext(d),r=a.useContext(x),{getFieldState:t,formState:s}=(0,i.Gc)(),n=t(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=r;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...n}},x=a.createContext({}),h=a.forwardRef(({className:e,...r},t)=>{let n=a.useId();return s.jsx(x.Provider,{value:{id:n},children:s.jsx("div",{ref:t,className:(0,o.cn)("space-y-2",e),...r})})});h.displayName="FormItem";let p=a.forwardRef(({className:e,...r},t)=>{let{error:a,formItemId:n}=u();return s.jsx(l.Label,{ref:t,className:(0,o.cn)(a&&"text-destructive",e),htmlFor:n,...r})});p.displayName="FormLabel";let f=a.forwardRef(({...e},r)=>{let{error:t,formItemId:a,formDescriptionId:i,formMessageId:o}=u();return s.jsx(n.g7,{ref:r,id:a,"aria-describedby":t?`${i} ${o}`:`${i}`,"aria-invalid":!!t,...e})});f.displayName="FormControl";let j=a.forwardRef(({className:e,...r},t)=>{let{formDescriptionId:a}=u();return s.jsx("p",{ref:t,id:a,className:(0,o.cn)("text-sm text-muted-foreground",e),...r})});j.displayName="FormDescription";let g=a.forwardRef(({className:e,children:r,...t},a)=>{let{error:n,formMessageId:i}=u(),l=n?String(n?.message):r;return l?s.jsx("p",{ref:a,id:i,className:(0,o.cn)("text-sm font-medium text-destructive",e),...t,children:l}):null});g.displayName="FormMessage"},44794:(e,r,t)=>{"use strict";t.r(r),t.d(r,{Label:()=>c});var s=t(10326),a=t(17577),n=t(34478),i=t(28671),o=t(51223);let l=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...r},t)=>s.jsx(n.f,{ref:t,className:(0,o.cn)(l(),e),...r}));c.displayName=n.f.displayName},38227:(e,r,t)=>{"use strict";t.d(r,{O:()=>n});var s=t(10326),a=t(51223);function n({className:e,...r}){return s.jsx("div",{className:(0,a.cn)("animate-pulse rounded-md bg-primary/10",e),...r})}},45175:(e,r,t)=>{"use strict";t.d(r,{$:()=>u,y:()=>m});var s=t(10326),a=t(95920),n=t(79635),i=t(47546),o=t(48705),l=t(6343);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,t(80851).Z)("ImagePlus",[["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7",key:"31hg93"}],["line",{x1:"16",x2:"22",y1:"5",y2:"5",key:"ez7e4s"}],["line",{x1:"19",x2:"19",y1:"2",y2:"8",key:"1gkr8c"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var d=t(46226);let m=[{href:"#",icon:s.jsx(d.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:s.jsx(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/settings/personal-info",icon:s.jsx(n.Z,{className:"h-5 w-5"}),label:"Personal Info"},{href:"/freelancer/settings/professional-info",icon:s.jsx(i.Z,{className:"h-5 w-5"}),label:"Professional Info"},{href:"/freelancer/settings/projects",icon:s.jsx(o.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"/freelancer/settings/education-info",icon:s.jsx(l.Z,{className:"h-5 w-5"}),label:"Education"},{href:"/freelancer/settings/resume",icon:s.jsx(c,{className:"h-5 w-5"}),label:"Portfolio"}],u=[]},30351:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var s=t(17577),a=t(84097);let n=({form:e,formSection:r="",isDialogOpen:t,setIsDialogOpen:n,onSave:i,onDiscard:o,setCurrSkills:l})=>{let[c,d]=(0,s.useState)(!1),[m,u]=(0,s.useState)(!1),x=(0,s.useRef)(!1),h=(0,s.useRef)(null),p=e=>{if(!r)return;let t=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");t[r]&&(h.current=t[r]),Object.values(e).some(e=>void 0!==e&&""!==e)&&(t[r]=e,localStorage.setItem("DEHIX_DRAFT",JSON.stringify(t)),(0,a.Am)({title:"Draft Saved",description:`Your ${r} draft has been saved.`,duration:1500}))};(0,s.useEffect)(()=>{if(t&&!x.current&&r){let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");e&&e[r]&&d(!0),x.current=!0}},[t,r]);let f=e=>e&&"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[e,"string"==typeof r?r.trim():r])):{};return{showDraftDialog:c,setShowDraftDialog:d,confirmExitDialog:m,setConfirmExitDialog:u,loadDraft:()=>{if(!r)return;let t=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");t&&t[r]&&(Object.keys(t[r]).forEach(e=>{void 0===t[r][e]&&delete t[r][e]}),"projects"===r&&(delete t[r].verificationStatus,Array.isArray(t[r].techUsed)&&l(t[r].techUsed)),Object.entries(t[r]).some(([,e])=>""!==e&&void 0!==e&&!(Array.isArray(e)&&0===e.length))&&e&&(e.reset(t[r]),h.current=t[r],(0,a.Am)({title:"Draft Loaded",description:`Your ${r} draft has been restored.`,duration:1500})),d(!1))},discardDraft:()=>{if(!r)return;let t=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");t&&(delete t[r],0===Object.keys(t).length?localStorage.removeItem("DEHIX_DRAFT"):localStorage.setItem("DEHIX_DRAFT",JSON.stringify(t))),e?.reset(),(0,a.Am)({title:"Draft Discarded",description:`Your ${r} draft has been discarded.`,duration:1500}),d(!1),o&&o()},handleSaveAndClose:()=>{if(!r)return;let t=e?.getValues();p(t),(0,a.Am)({title:"Draft Saved",description:"Your draft has been saved.",duration:1500}),h.current=t,u(!1),n&&n(!1),i&&i(t)},handleDiscardAndClose:()=>{if(!r)return;let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");delete e[r],0===Object.keys(e).length?localStorage.removeItem("DEHIX_DRAFT"):localStorage.setItem("DEHIX_DRAFT",JSON.stringify(e)),(0,a.Am)({title:"Draft Discarded",description:`Your ${r} draft has been discarded.`,duration:1500}),u(!1),n&&n(!1),o&&o()},handleDialogClose:()=>{if(!t||!r)return;let s=e?.getValues()||{},a=h.current||{},i=f(s),o=f(a),l=Object.entries(o).some(([e,r])=>{let t=i[e];return Array.isArray(r)&&Array.isArray(t)?JSON.stringify(r)!==JSON.stringify(t):r!==t}),c=Object.entries(i).some(([e,r])=>"verificationStatus"!==e&&void 0!==r&&""!==r&&void 0===o[e]);if(!l&&!c&&n){n(!1);return}Object.values(i).some(e=>e?.toString().trim())?u(!0):n&&n(!1)},saveDraft:p,hasOtherValues:(0,s.useCallback)(e=>Object.entries(e).some(([e,r])=>"profiles"!==e&&(Array.isArray(r)&&r.length>0&&("urls"!==e||r.some(e=>e?.value?.trim()!==""))||"string"==typeof r&&""!==r.trim()||"number"==typeof r&&!isNaN(r))),[]),hasProfiles:(0,s.useCallback)(e=>e?.some(e=>Object.values(e).some(e=>Array.isArray(e)&&e.length>0||"string"==typeof e&&""!==e.trim()||"number"==typeof e&&!isNaN(e))),[])}}},10836:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>i,__esModule:()=>n,default:()=>o});var s=t(68570);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\settings\professional-info\page.tsx`),{__esModule:n,$$typeof:i}=a;a.default;let o=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\settings\professional-info\page.tsx#default`)},34478:(e,r,t)=>{"use strict";t.d(r,{f:()=>o});var s=t(17577),a=t(77335),n=t(10326),i=s.forwardRef((e,r)=>(0,n.jsx)(a.WV.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var o=i}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,6686,4736,6499,8066,588],()=>t(84388));module.exports=s})();