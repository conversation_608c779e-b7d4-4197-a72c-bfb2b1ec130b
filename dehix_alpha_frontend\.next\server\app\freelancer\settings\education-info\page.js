(()=>{var e={};e.id=2605,e.ids=[2605],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},63479:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d}),r(67287),r(54302),r(12523);var s=r(23191),a=r(88716),i=r(37922),n=r.n(i),l=r(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d=["",{children:["freelancer",{children:["settings",{children:["education-info",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,67287)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\settings\\education-info\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\settings\\education-info\\page.tsx"],u="/freelancer/settings/education-info/page",m={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/freelancer/settings/education-info/page",pathname:"/freelancer/settings/education-info",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},32268:(e,t,r)=>{Promise.resolve().then(r.bind(r,81487))},6343:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},47546:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},37358:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48705:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},81487:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var s=r(10326),a=r(17577),i=r(25842),n=r(92166),l=r(45175),o=r(1370),d=r(29752);let c=({degree:e,universityName:t,fieldOfStudy:r,startDate:a,endDate:i,grade:n})=>(0,s.jsxs)(d.Zb,{className:"w-full h-full mx-auto md:max-w-2xl",children:[(0,s.jsxs)(d.Ol,{children:[s.jsx(d.ll,{className:"flex",children:t||"University Name"}),(0,s.jsxs)(d.SZ,{className:"block mt-1 uppercase tracking-wide leading-tight font-medium ",children:[e||"Degree"," in ",r||"Field of Study"]})]}),s.jsx(d.aY,{children:(0,s.jsxs)("p",{className:" pt-4",children:["Grade: ",n||"N/A"]})}),s.jsx(d.eW,{className:"flex",children:s.jsx(o.Z,{startDate:a,endDate:i})})]});var u=r(6260),m=r(27256),x=r(74723),h=r(74064),p=r(83855),f=r(56627),g=r(20495),j=r(41190),y=r(91664),v=r(9969),D=r(24118),b=r(30351);let N=m.z.object({degree:m.z.string().optional(),universityName:m.z.string().optional(),fieldOfStudy:m.z.string().optional(),startDate:m.z.string().optional(),endDate:m.z.string().optional(),grade:m.z.string().optional()}).refine(e=>!e.startDate||!e.endDate||new Date(e.startDate)<new Date(e.endDate),{message:"Start Date must be before End Date",path:["endDate"]}),S=({onFormSubmit:e})=>{let[t,r]=(0,a.useState)(!1),[i,n]=(0,a.useState)(!1),l=new Date().toISOString().split("T")[0],o=(0,a.useRef)(null),d=(0,x.cI)({resolver:(0,h.F)(N),defaultValues:{degree:"",universityName:"",fieldOfStudy:"",startDate:"",endDate:"",grade:""}}),{showDraftDialog:c,setShowDraftDialog:m,confirmExitDialog:S,setConfirmExitDialog:I,loadDraft:w,discardDraft:A,handleSaveAndClose:E,handleDiscardAndClose:k,handleDialogClose:_}=(0,b.Z)({form:d,formSection:"education",isDialogOpen:i,setIsDialogOpen:n,onSave:e=>{o.current={...e}},onDiscard:()=>{o.current=null}});async function q(t){r(!0);try{let r={...t,startDate:t.startDate?new Date(t.startDate).toISOString():null,endDate:t.endDate?new Date(t.endDate).toISOString():null,oracleAssigned:t.oracleAssigned||"",verificationStatus:t.verificationStatus||"ADDED",verificationUpdateTime:t.verificationUpdateTime||new Date,comments:""};await u.b.post("/freelancer/education",r),e(),n(!1),(0,f.Am)({title:"Education Added",description:"The education has been successfully added.",duration:1500})}catch(e){console.error("API Error:",e),(0,f.Am)({variant:"destructive",title:"Error",description:"Failed to add education. Please try again later.",duration:1500})}finally{r(!1)}}return(0,s.jsxs)(D.Vq,{open:i,onOpenChange:e=>{n(e),e||_()},children:[s.jsx(D.hg,{asChild:!0,children:s.jsx(y.z,{variant:"outline",size:"icon",className:"my-auto",children:s.jsx(p.Z,{className:"h-4 w-4"})})}),(0,s.jsxs)(D.cZ,{className:"lg:max-w-screen-lg overflow-y-scroll max-h-screen no-scrollbar",children:[(0,s.jsxs)(D.fK,{children:[s.jsx(D.$N,{children:"Add Education"}),s.jsx(D.Be,{children:"Add your relevant Education."})]}),s.jsx(v.l0,{...d,children:(0,s.jsxs)("form",{onSubmit:d.handleSubmit(q),className:"space-y-4",children:[s.jsx(v.Wi,{control:d.control,name:"degree",render:({field:e})=>(0,s.jsxs)(v.xJ,{children:[s.jsx(v.lX,{children:"Enter Degree"}),s.jsx(v.NI,{children:s.jsx(j.I,{placeholder:"Enter your degree title",...e})}),s.jsx(v.pf,{children:"Enter your degree title"}),s.jsx(v.zG,{})]})}),s.jsx(v.Wi,{control:d.control,name:"universityName",render:({field:e})=>(0,s.jsxs)(v.xJ,{children:[s.jsx(v.lX,{children:"University Name"}),s.jsx(v.NI,{children:s.jsx(j.I,{placeholder:"Enter your university name",...e})}),s.jsx(v.pf,{children:"Enter your university name"}),s.jsx(v.zG,{})]})}),s.jsx(v.Wi,{control:d.control,name:"fieldOfStudy",render:({field:e})=>(0,s.jsxs)(v.xJ,{children:[s.jsx(v.lX,{children:"Enter Field of Study"}),s.jsx(v.NI,{children:s.jsx(j.I,{placeholder:"Enter your field of study",...e})}),s.jsx(v.pf,{children:"Enter Field of Study"}),s.jsx(v.zG,{})]})}),s.jsx(v.Wi,{control:d.control,name:"startDate",render:({field:e})=>(0,s.jsxs)(v.xJ,{children:[s.jsx(v.lX,{children:"Start Date"}),s.jsx(v.NI,{children:s.jsx(j.I,{type:"date",max:l,...e})}),s.jsx(v.pf,{children:"Select the start date"}),s.jsx(v.zG,{})]})}),s.jsx(v.Wi,{control:d.control,name:"endDate",render:({field:e})=>(0,s.jsxs)(v.xJ,{children:[s.jsx(v.lX,{children:"End Date"}),s.jsx(v.NI,{children:s.jsx(j.I,{type:"date",...e})}),s.jsx(v.pf,{children:"Select the end date"}),s.jsx(v.zG,{})]})}),s.jsx(v.Wi,{control:d.control,name:"grade",render:({field:e})=>(0,s.jsxs)(v.xJ,{children:[s.jsx(v.lX,{children:"Grade"}),s.jsx(v.NI,{children:s.jsx(j.I,{placeholder:"Enter your grade",...e})}),s.jsx(v.pf,{children:"Enter your grade"}),s.jsx(v.zG,{})]})}),s.jsx(D.cN,{children:s.jsx(y.z,{type:"submit",disabled:t,children:t?"Loading...":"Create"})})]})})]}),S&&s.jsx(g.Z,{dialogChange:S,setDialogChange:I,heading:"Save Draft?",desc:"Do you want to save your draft before leaving?",handleClose:k,handleSave:E,btn1Txt:"Don't save",btn2Txt:"Yes save"}),c&&s.jsx(g.Z,{dialogChange:c,setDialogChange:m,heading:"Load Draft?",desc:"You have unsaved data. Would you like to restore it?",handleClose:A,handleSave:w,btn1Txt:" No, start fresh",btn2Txt:"Yes, load draft"})]})};var I=r(40588);function w(){(0,i.v9)(e=>e.user);let[e,t]=(0,a.useState)(!1),[r,o]=(0,a.useState)([]);return(0,s.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[s.jsx(n.Z,{menuItemsTop:l.y,menuItemsBottom:l.$,active:"Education",isKycCheck:!0}),(0,s.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[s.jsx(I.Z,{menuItemsTop:l.y,menuItemsBottom:l.$,activeMenu:"Education",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Settings",link:"#"},{label:"Educational Info",link:"#"}]}),(0,s.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8    grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3",children:[r.map((e,t)=>s.jsx(c,{...e},t)),s.jsx(S,{onFormSubmit:()=>{t(e=>!e)}})]})]})]})}},1370:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var s=r(10326);r(17577);var a=r(37358);let i=({startDate:e,endDate:t})=>{let r=e?new Date(e).toLocaleDateString():"Start Date N/A",i="current"!==t&&t?new Date(t).toLocaleDateString():"Still Going On!";return(0,s.jsxs)("div",{className:"flex relative whitespace-nowrap items-start sm:items-center gap-1 rounded-md ",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2 ",children:[s.jsx(a.Z,{className:"w-4 h-4 sm:w-5 sm:h-5 "}),s.jsx("span",{className:"text-xs sm:text-sm font-medium",children:`Start  ${r}`})]}),s.jsx("p",{children:"-"}),s.jsx("div",{className:"flex items-center ",children:s.jsx("span",{className:"text-xs sm:text-sm font-medium",children:` ${i}`})})]})}},20495:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(10326);r(17577);var a=r(24118),i=r(91664);let n=({dialogChange:e,setDialogChange:t,heading:r,desc:n,handleClose:l,handleSave:o,btn1Txt:d,btn2Txt:c})=>s.jsx(a.Vq,{open:e,onOpenChange:t,children:(0,s.jsxs)(a.cZ,{children:[(0,s.jsxs)(a.fK,{children:[s.jsx(a.$N,{children:r}),s.jsx(a.Be,{children:n})]}),(0,s.jsxs)(a.cN,{children:[s.jsx(i.z,{variant:"outline",onClick:l,children:d}),s.jsx(i.z,{onClick:o,children:c})]})]})})},9969:(e,t,r)=>{"use strict";r.d(t,{NI:()=>f,Wi:()=>u,l0:()=>d,lX:()=>p,pf:()=>g,xJ:()=>h,zG:()=>j});var s=r(10326),a=r(17577),i=r(99469),n=r(74723),l=r(51223),o=r(44794);let d=n.RV,c=a.createContext({}),u=({...e})=>s.jsx(c.Provider,{value:{name:e.name},children:s.jsx(n.Qr,{...e})}),m=()=>{let e=a.useContext(c),t=a.useContext(x),{getFieldState:r,formState:s}=(0,n.Gc)(),i=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...i}},x=a.createContext({}),h=a.forwardRef(({className:e,...t},r)=>{let i=a.useId();return s.jsx(x.Provider,{value:{id:i},children:s.jsx("div",{ref:r,className:(0,l.cn)("space-y-2",e),...t})})});h.displayName="FormItem";let p=a.forwardRef(({className:e,...t},r)=>{let{error:a,formItemId:i}=m();return s.jsx(o.Label,{ref:r,className:(0,l.cn)(a&&"text-destructive",e),htmlFor:i,...t})});p.displayName="FormLabel";let f=a.forwardRef(({...e},t)=>{let{error:r,formItemId:a,formDescriptionId:n,formMessageId:l}=m();return s.jsx(i.g7,{ref:t,id:a,"aria-describedby":r?`${n} ${l}`:`${n}`,"aria-invalid":!!r,...e})});f.displayName="FormControl";let g=a.forwardRef(({className:e,...t},r)=>{let{formDescriptionId:a}=m();return s.jsx("p",{ref:r,id:a,className:(0,l.cn)("text-sm text-muted-foreground",e),...t})});g.displayName="FormDescription";let j=a.forwardRef(({className:e,children:t,...r},a)=>{let{error:i,formMessageId:n}=m(),o=i?String(i?.message):t;return o?s.jsx("p",{ref:a,id:n,className:(0,l.cn)("text-sm font-medium text-destructive",e),...r,children:o}):null});j.displayName="FormMessage"},44794:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Label:()=>d});var s=r(10326),a=r(17577),i=r(34478),n=r(28671),l=r(51223);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...t},r)=>s.jsx(i.f,{ref:r,className:(0,l.cn)(o(),e),...t}));d.displayName=i.f.displayName},45175:(e,t,r)=>{"use strict";r.d(t,{$:()=>m,y:()=>u});var s=r(10326),a=r(95920),i=r(79635),n=r(47546),l=r(48705),o=r(6343);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,r(80851).Z)("ImagePlus",[["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7",key:"31hg93"}],["line",{x1:"16",x2:"22",y1:"5",y2:"5",key:"ez7e4s"}],["line",{x1:"19",x2:"19",y1:"2",y2:"8",key:"1gkr8c"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var c=r(46226);let u=[{href:"#",icon:s.jsx(c.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:s.jsx(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/settings/personal-info",icon:s.jsx(i.Z,{className:"h-5 w-5"}),label:"Personal Info"},{href:"/freelancer/settings/professional-info",icon:s.jsx(n.Z,{className:"h-5 w-5"}),label:"Professional Info"},{href:"/freelancer/settings/projects",icon:s.jsx(l.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"/freelancer/settings/education-info",icon:s.jsx(o.Z,{className:"h-5 w-5"}),label:"Education"},{href:"/freelancer/settings/resume",icon:s.jsx(d,{className:"h-5 w-5"}),label:"Portfolio"}],m=[]},30351:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var s=r(17577),a=r(84097);let i=({form:e,formSection:t="",isDialogOpen:r,setIsDialogOpen:i,onSave:n,onDiscard:l,setCurrSkills:o})=>{let[d,c]=(0,s.useState)(!1),[u,m]=(0,s.useState)(!1),x=(0,s.useRef)(!1),h=(0,s.useRef)(null),p=e=>{if(!t)return;let r=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");r[t]&&(h.current=r[t]),Object.values(e).some(e=>void 0!==e&&""!==e)&&(r[t]=e,localStorage.setItem("DEHIX_DRAFT",JSON.stringify(r)),(0,a.Am)({title:"Draft Saved",description:`Your ${t} draft has been saved.`,duration:1500}))};(0,s.useEffect)(()=>{if(r&&!x.current&&t){let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");e&&e[t]&&c(!0),x.current=!0}},[r,t]);let f=e=>e&&"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"string"==typeof t?t.trim():t])):{};return{showDraftDialog:d,setShowDraftDialog:c,confirmExitDialog:u,setConfirmExitDialog:m,loadDraft:()=>{if(!t)return;let r=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");r&&r[t]&&(Object.keys(r[t]).forEach(e=>{void 0===r[t][e]&&delete r[t][e]}),"projects"===t&&(delete r[t].verificationStatus,Array.isArray(r[t].techUsed)&&o(r[t].techUsed)),Object.entries(r[t]).some(([,e])=>""!==e&&void 0!==e&&!(Array.isArray(e)&&0===e.length))&&e&&(e.reset(r[t]),h.current=r[t],(0,a.Am)({title:"Draft Loaded",description:`Your ${t} draft has been restored.`,duration:1500})),c(!1))},discardDraft:()=>{if(!t)return;let r=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");r&&(delete r[t],0===Object.keys(r).length?localStorage.removeItem("DEHIX_DRAFT"):localStorage.setItem("DEHIX_DRAFT",JSON.stringify(r))),e?.reset(),(0,a.Am)({title:"Draft Discarded",description:`Your ${t} draft has been discarded.`,duration:1500}),c(!1),l&&l()},handleSaveAndClose:()=>{if(!t)return;let r=e?.getValues();p(r),(0,a.Am)({title:"Draft Saved",description:"Your draft has been saved.",duration:1500}),h.current=r,m(!1),i&&i(!1),n&&n(r)},handleDiscardAndClose:()=>{if(!t)return;let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");delete e[t],0===Object.keys(e).length?localStorage.removeItem("DEHIX_DRAFT"):localStorage.setItem("DEHIX_DRAFT",JSON.stringify(e)),(0,a.Am)({title:"Draft Discarded",description:`Your ${t} draft has been discarded.`,duration:1500}),m(!1),i&&i(!1),l&&l()},handleDialogClose:()=>{if(!r||!t)return;let s=e?.getValues()||{},a=h.current||{},n=f(s),l=f(a),o=Object.entries(l).some(([e,t])=>{let r=n[e];return Array.isArray(t)&&Array.isArray(r)?JSON.stringify(t)!==JSON.stringify(r):t!==r}),d=Object.entries(n).some(([e,t])=>"verificationStatus"!==e&&void 0!==t&&""!==t&&void 0===l[e]);if(!o&&!d&&i){i(!1);return}Object.values(n).some(e=>e?.toString().trim())?m(!0):i&&i(!1)},saveDraft:p,hasOtherValues:(0,s.useCallback)(e=>Object.entries(e).some(([e,t])=>"profiles"!==e&&(Array.isArray(t)&&t.length>0&&("urls"!==e||t.some(e=>e?.value?.trim()!==""))||"string"==typeof t&&""!==t.trim()||"number"==typeof t&&!isNaN(t))),[]),hasProfiles:(0,s.useCallback)(e=>e?.some(e=>Object.values(e).some(e=>Array.isArray(e)&&e.length>0||"string"==typeof e&&""!==e.trim()||"number"==typeof e&&!isNaN(e))),[])}}},67287:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var s=r(68570);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\settings\education-info\page.tsx`),{__esModule:i,$$typeof:n}=a;a.default;let l=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\settings\education-info\page.tsx#default`)},34478:(e,t,r)=>{"use strict";r.d(t,{f:()=>l});var s=r(17577),a=r(77335),i=r(10326),n=s.forwardRef((e,t)=>(0,i.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,6686,4736,6499,8066,588],()=>r(63479));module.exports=s})();