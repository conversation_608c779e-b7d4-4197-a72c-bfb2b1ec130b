(()=>{var e={};e.id=5041,e.ids=[5041],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},40262:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>d}),t(13110),t(54302),t(12523);var a=t(23191),r=t(88716),l=t(37922),i=t.n(l),n=t(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["freelancer",{children:["project",{children:["[project_id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,13110)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\project\\[project_id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\project\\[project_id]\\page.tsx"],x="/freelancer/project/[project_id]/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/freelancer/project/[project_id]/page",pathname:"/freelancer/project/[project_id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94501:(e,s,t)=>{Promise.resolve().then(t.bind(t,53060))},40900:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},37358:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},43727:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},5932:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},40617:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},60763:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69515:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},40765:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},98091:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},53060:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var a=t(10326),r=t(77506),l=t(35047),i=t(17577),n=t.n(i),c=t(40588),d=t(29752),o=t(43593),x=t(25842),m=t(51223),p=t(91664),h=t(24118),u=t(41190),f=t(44794),j=t(6260),y=t(56627);function g({_id:e,domain:s,freelancersRequired:t,skills:r,experience:c,minConnect:o,description:g,email:v,status:b,startDate:N,endDate:w,className:k,domain_id:Z,...M}){let _=(0,x.v9)(e=>e.user),[q,D]=(0,i.useState)(""),[C,S]=(0,i.useState)(""),[P,z]=(0,i.useState)(!1),V=(0,l.useParams)(),[A,L]=n().useState([]),[E,I]=(0,i.useState)(!1),$=async s=>{s.preventDefault();try{await j.b.post("/bid",{current_price:q,description:C,bidder_id:_.uid,project_id:V.project_id,domain_id:Z,profile_id:e}),D(""),S(""),z(!1),(0,y.Am)({title:"Bid Added",description:"The Bid has been successfully added."})}catch(e){console.error("Error submitting bid:",e),(0,y.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}};return(0,a.jsxs)(d.Zb,{className:(0,m.cn)("w-[350px]",k),...M,children:[(0,a.jsxs)(d.Ol,{children:[(0,a.jsxs)(d.ll,{children:[s," (",t,")"]}),(0,a.jsxs)(d.SZ,{className:"text-gray-600",children:["Requirement is of ",t," freelancer(s) for"," ",s.toLowerCase()," profile.",a.jsx("br",{}),a.jsx("p",{className:"break-words text-white",children:g})]})]}),a.jsx(d.aY,{className:"grid gap-4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("ul",{className:"flex flex-wrap gap-2",children:[v&&(0,a.jsxs)("li",{className:"min-w-[45%]",children:[a.jsx("span",{className:"text-gray-700 font-semibold",children:"Email: "}),v]}),b&&(0,a.jsxs)("li",{className:"min-w-[45%]",children:[a.jsx("span",{className:"text-gray-700 font-semibold",children:"Status: "}),b]}),N&&(0,a.jsxs)("li",{className:"min-w-[45%]",children:[(0,a.jsxs)("span",{className:"text-gray-700 font-semibold",children:["Start Date:"," "]}),N]}),w&&(0,a.jsxs)("li",{className:"min-w-[45%]",children:[a.jsx("span",{className:"text-gray-400 font-semibold",children:"End Date: "}),w]}),(0,a.jsxs)("li",{className:"min-w-[45%]",children:[a.jsx("span",{className:"text-gray-400 font-semibold",children:"Experience: "}),c," years"]}),(0,a.jsxs)("li",{className:"min-w-[45%]",children:[a.jsx("span",{className:"text-gray-400 font-semibold",children:"Min Connect: "}),o]})]}),r.length>0&&(0,a.jsxs)("div",{className:"mt-2",children:[a.jsx("span",{className:"text-gray-700 font-semibold",children:"Skills: "}),a.jsx("ul",{className:"flex flex-wrap gap-1 mt-1",children:r.map((e,s)=>a.jsx("li",{className:"bg-gray-200 text-black rounded px-2 py-1 text-sm",children:e},s))})]})]})}),a.jsx(d.eW,{children:(0,a.jsxs)(h.Vq,{open:P,onOpenChange:z,children:[a.jsx(h.hg,{asChild:!0,children:a.jsx(p.z,{variant:"outline",type:"button",disabled:E,children:E?"Applied":"Bid"})}),(0,a.jsxs)(h.cZ,{className:"sm:max-w-[425px]",children:[(0,a.jsxs)(h.fK,{children:[a.jsx(h.$N,{children:"Bid"}),a.jsx(h.Be,{children:"Click on bid if you want to bid for this profile"})]}),(0,a.jsxs)("form",{onSubmit:$,children:[(0,a.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[a.jsx(f.Label,{htmlFor:"amount",className:"text-center",children:"Amount"}),a.jsx(u.I,{id:"amount",type:"number",value:q,onChange:e=>D(e.target.value),className:"col-span-3",required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[a.jsx(f.Label,{htmlFor:"description",className:"text-right block",children:"Description"}),a.jsx(u.I,{id:"description",type:"text",value:C,onChange:e=>S(e.target.value),className:"col-span-3",required:!0})]})]}),a.jsx(h.cN,{children:a.jsx(p.z,{type:"submit",disabled:E,children:E?"Applied":"Bid"})})]})]})]})})]})}var v=t(92166),b=t(48586);function N(){let{project_id:e}=(0,l.useParams)(),[s,t]=(0,i.useState)(null),[n,x]=(0,i.useState)(!0);return n?a.jsx("div",{className:"flex justify-center items-center h-screen",children:a.jsx(r.Z,{className:"my-4 h-8 w-8 animate-spin"})}):s?(0,a.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[a.jsx(v.Z,{menuItemsTop:b.yn,menuItemsBottom:b.$C,active:"Market"}),(0,a.jsxs)("div",{className:"flex flex-col sm:gap-4 sm:py-4 sm:pl-14 mb-8",children:[a.jsx(c.Z,{menuItemsTop:b.yn,menuItemsBottom:b.$C,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Marketplace",link:"/freelancer/market"},{label:e,link:"#"}]}),(0,a.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8 lg:grid-cols-3 xl:grid-cols-3",children:[a.jsx("div",{className:"grid auto-rows-max items-start gap-4 md:gap-8 lg:col-span-2",children:a.jsx("div",{children:a.jsx(o.Z,{projectName:s.projectName,description:s.description,email:s.email,status:s.status,startDate:s.createdAt,endDate:s.end,projectDomain:s.projectDomain,projectId:s._id,skills:s.skillsRequired,userRole:"Freelancer"})})}),(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx(d.ll,{className:"group flex items-center gap-2 text-2xl",children:"Profiles"}),s?.profiles?.map((e,s)=>a.jsx(g,{className:"w-full min-w-full p-4 shadow-md rounded-lg",...e},s))]})]})]})]}):a.jsx("div",{children:"Project data not found."})}},1370:(e,s,t)=>{"use strict";t.d(s,{Z:()=>l});var a=t(10326);t(17577);var r=t(37358);let l=({startDate:e,endDate:s})=>{let t=e?new Date(e).toLocaleDateString():"Start Date N/A",l="current"!==s&&s?new Date(s).toLocaleDateString():"Still Going On!";return(0,a.jsxs)("div",{className:"flex relative whitespace-nowrap items-start sm:items-center gap-1 rounded-md ",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2 ",children:[a.jsx(r.Z,{className:"w-4 h-4 sm:w-5 sm:h-5 "}),a.jsx("span",{className:"text-xs sm:text-sm font-medium",children:`Start  ${t}`})]}),a.jsx("p",{children:"-"}),a.jsx("div",{className:"flex items-center ",children:a.jsx("span",{className:"text-xs sm:text-sm font-medium",children:` ${l}`})})]})}},43593:(e,s,t)=>{"use strict";t.d(s,{Z:()=>j});var a=t(10326);t(17577);var r=t(40765),l=t(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,l.Z)("CodeXml",[["path",{d:"m18 16 4-4-4-4",key:"1inbqp"}],["path",{d:"m6 8-4 4 4 4",key:"15zrgr"}],["path",{d:"m14.5 4-5 16",key:"e7oirm"}]]);var n=t(5932);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,l.Z)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]);var d=t(30361),o=t(90434),x=t(29752),m=t(38443),p=t(71064),h=t(78062),u=t(1370),f=t(91664);let j=function({projectName:e,description:s,email:t,status:l,startDate:j,endDate:y,projectDomain:g,skills:v,userRole:b="Business",projectId:N,handleCompleteProject:w}){let{text:k,className:Z}=(0,p.S)(l),M=`/${b.toLowerCase()}/project/${N}/milestone`;return(0,a.jsxs)(x.Zb,{className:"shadow-lg border border-gray-800 rounded-lg",children:[(0,a.jsxs)(x.Ol,{children:[(0,a.jsxs)("div",{className:"flex flex-wrap justify-between items-center mb-0.5",children:[a.jsx(x.ll,{className:"text-xl md:text-2xl font-semibold",children:e}),a.jsx(m.C,{className:`${Z} px-1 py-0.5 text-xs md:text-sm rounded-md`,children:k})]}),a.jsx(h.Separator,{className:"my-4"})]}),(0,a.jsxs)(x.aY,{className:"space-y-6",children:[a.jsx(u.Z,{startDate:j,endDate:y}),a.jsx("p",{className:"text-sm md:text-base leading-relaxed",children:s}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 md:gap-6",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2 px-3 py-1 text-xs md:text-sm rounded-md shadow-inner w-full md:w-1/2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(r.Z,{className:"w-4 h-4 block md:hidden"}),a.jsx("p",{className:"font-medium",children:"Project Domain:"})]}),a.jsx("div",{className:"flex flex-wrap gap-1",children:g.map((e,s)=>a.jsx(m.C,{className:"bg-gray-200 text-gray-900 text-xs md:text-sm px-2 py-1 rounded-full",children:e},s))})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2 px-3 py-1 text-xs md:text-sm rounded-md shadow-inner w-full md:w-1/2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(i,{className:"w-4 h-4 block md:hidden"}),a.jsx("p",{className:"font-medium",children:"Skills:"})]}),a.jsx("div",{className:"flex flex-wrap gap-1",children:v.map((e,s)=>a.jsx(m.C,{className:"bg-gray-200 text-gray-900 text-xs md:text-sm px-2 py-1 rounded-full",children:e},s))})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4 px-3 py-1 text-xs md:text-sm rounded-md shadow-inner",children:[a.jsx(n.Z,{className:"w-4 h-4"}),a.jsx("span",{className:"text-sm",children:t})]}),(0,a.jsxs)("div",{className:"flex justify-between mt-4",children:[a.jsx(o.default,{href:M,children:(0,a.jsxs)(f.z,{className:"flex items-center px-4 py-2 text-xs md:text-sm font-medium text-white rounded-md bg-blue-600 hover:bg-blue-500",size:"sm",children:[a.jsx(c,{className:"w-4 h-4 mr-1"}),"Milestone"]})}),(0,a.jsxs)(f.z,{className:`flex items-center px-4 py-2 text-xs md:text-sm font-medium text-white rounded-md ${"COMPLETED"===k?"bg-green-600 hover:bg-green-500":"bg-blue-600 hover:bg-blue-500"}`,size:"sm",onClick:w,disabled:!w,children:[a.jsx(d.Z,{className:"w-4 h-4 mr-1"}),"COMPLETED"===k?"Completed":"Mark complete"]})]})]})]})}},44794:(e,s,t)=>{"use strict";t.r(s),t.d(s,{Label:()=>d});var a=t(10326),r=t(17577),l=t(34478),i=t(28671),n=t(51223);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...s},t)=>a.jsx(l.f,{ref:t,className:(0,n.cn)(c(),e),...s}));d.displayName=l.f.displayName},78062:(e,s,t)=>{"use strict";t.r(s),t.d(s,{Separator:()=>n});var a=t(10326),r=t(17577),l=t(90220),i=t(51223);let n=r.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...r},n)=>a.jsx(l.f,{ref:n,decorative:t,orientation:s,className:(0,i.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));n.displayName=l.f.displayName},48586:(e,s,t)=>{"use strict";t.d(s,{yL:()=>b,$C:()=>v,yn:()=>g});var a=t(10326),r=t(95920),l=t(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,l.Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]]),n=(0,l.Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);var c=t(43727);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,l.Z)("TabletSmartphone",[["rect",{width:"10",height:"14",x:"3",y:"8",rx:"2",key:"1vrsiq"}],["path",{d:"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4",key:"1j4zmg"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),o=(0,l.Z)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]]);var x=t(60763);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,l.Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);var p=t(40617),h=t(69515),u=t(88378),f=t(40900),j=t(98091),y=t(46226);let g=[{href:"#",icon:a.jsx(y.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:a.jsx(r.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:a.jsx(i,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:a.jsx(n,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:a.jsx(c.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:a.jsx(d,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:a.jsx(o,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:a.jsx(x.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:a.jsx(m,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:a.jsx(p.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:a.jsx(h.Z,{className:"h-5 w-5"}),label:"Notes"}],v=[{href:"/freelancer/settings/personal-info",icon:a.jsx(u.Z,{className:"h-5 w-5"}),label:"Settings"}];y.default,r.Z,h.Z,f.Z,j.Z;let b=[{href:"#",icon:a.jsx(y.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:a.jsx(r.Z,{className:"h-5 w-5"}),label:"Home"}]},13110:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var a=t(68570);let r=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\project\[project_id]\page.tsx`),{__esModule:l,$$typeof:i}=r;r.default;let n=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\project\[project_id]\page.tsx#default`)},34478:(e,s,t)=>{"use strict";t.d(s,{f:()=>n});var a=t(17577),r=t(77335),l=t(10326),i=a.forwardRef((e,s)=>(0,l.jsx)(r.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var n=i},90220:(e,s,t)=>{"use strict";t.d(s,{f:()=>d});var a=t(17577),r=t(77335),l=t(10326),i="horizontal",n=["horizontal","vertical"],c=a.forwardRef((e,s)=>{let{decorative:t,orientation:a=i,...c}=e,d=n.includes(a)?a:i;return(0,l.jsx)(r.WV.div,{"data-orientation":d,...t?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:s})});c.displayName="Separator";var d=c}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588],()=>t(40262));module.exports=a})();