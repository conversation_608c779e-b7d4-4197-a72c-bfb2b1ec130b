(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8345],{98023:function(e,t,r){Promise.resolve().then(r.bind(r,93008))},71935:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},29406:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},36141:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},93008:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return m}});var a=r(57437),s=r(3274),c=r(29406),l=r(11444),n=r(2265),i=r(64797),o=r(24172),d=r(15922),E=r(21832),u=r(62688),h=r(78068);function m(){let e=(0,l.v9)(e=>e.user),[t,r]=(0,n.useState)([]),[m,x]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{(async()=>{try{x(!0);let t=await d.b.get("/freelancer/".concat(e.uid,"/project?status=ACTIVE"));r(t.data.data)}catch(e){(0,h.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.error("API Error:",e)}finally{x(!1)}})()},[e.uid]),(0,a.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,a.jsx)(i.Z,{menuItemsTop:o.y,menuItemsBottom:o.$,active:"Current Projects"}),(0,a.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,a.jsx)(u.Z,{menuItemsTop:o.y,menuItemsBottom:o.$,activeMenu:"Current Projects",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Projects",link:"/freelancer/project/current"},{label:"Current Projects",link:"#"}]}),(0,a.jsx)("div",{className:"mb-8 mx-4 lg:mx-10 flex flex-col lg:flex-row justify-between items-start lg:items-center p-3",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Current Projects"}),(0,a.jsx)("p",{className:"text-gray-400 mt-2",children:"Browse and manage your active freelance projects"})]})}),m?(0,a.jsx)("div",{className:"flex justify-center items-center min-h-[50vh]",children:(0,a.jsx)(s.Z,{size:40,className:"animate-spin"})}):(0,a.jsx)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8    grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3",children:(null==t?void 0:t.length)===0?(0,a.jsxs)("div",{className:"col-span-full text-center mt-20 w-full",children:[(0,a.jsx)(c.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No projects available"})]}):null==t?void 0:t.map((t,r)=>(0,a.jsx)(E.t,{project:t,type:e.type},r))})]})]})}},21832:function(e,t,r){"use strict";r.d(t,{t:function(){return h}});var a=r(57437),s=r(87138),c=r(36141),l=r(49354),n=r(89733),i=r(48185),o=r(79055),d=r(44475),E=r(34859),u=r(97540);function h(e){var t,r;let{cardClassName:h,project:m,type:x=E.Dy.BUSINESS,...f}=e,{text:p,className:N}=(0,d.S)(m.status);return(0,a.jsxs)(i.Zb,{className:(0,l.cn)("flex flex-col h-[400px]",h),...f,children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsxs)(i.ll,{className:"flex",children:[m.projectName,"\xa0",m.verified&&(0,a.jsx)(c.Z,{className:"text-success"})]}),(0,a.jsxs)(i.SZ,{className:"text-gray-600",children:[(0,a.jsx)("p",{className:"my-auto",children:m.createdAt?new Date(m.createdAt).toLocaleDateString():"N/A"}),(0,a.jsx)("br",{}),(0,a.jsx)(o.C,{className:N,children:p})]})]}),(0,a.jsxs)(i.aY,{className:"grid gap-4 mb-auto flex-grow",children:[(0,a.jsxs)("div",{className:"mb-4 items-start pb-4 last:mb-0 last:pb-0 w-full",children:[(0,a.jsx)("span",{className:"flex h-2 w-2 rounded-full"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:(null===(t=m.description)||void 0===t?void 0:t.length)>40?"".concat(m.description.slice(0,40),"..."):m.description||"No description available"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Company:"})," ",m.companyName]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Role:"})," ",m.role]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Experience:"})," ",m.experience]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1 mt-2",children:null==m?void 0:null===(r=m.skillsRequired)||void 0===r?void 0:r.map((e,t)=>(0,a.jsx)(o.C,{className:"text-xs text-white bg-muted",children:e},t))})]})]}),(0,a.jsx)(i.eW,{children:(0,a.jsx)(s.default,{href:"/".concat(x.toLocaleLowerCase(),"/project/").concat(m._id),className:"w-full",children:(0,a.jsx)(n.z,{className:"w-full ".concat(m.status===u.sB.COMPLETED&&"bg-green-900 hover:bg-green-700"),children:"View full details"})})})]})}},24172:function(e,t,r){"use strict";r.d(t,{$:function(){return h},y:function(){return u}});var a=r(57437),s=r(11005),c=r(33480);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,c.Z)("FolderDot",[["path",{d:"M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z",key:"1fr9dc"}],["circle",{cx:"12",cy:"13",r:"1",key:"49l61u"}]]),n=(0,c.Z)("Pointer",[["path",{d:"M22 14a8 8 0 0 1-8 8",key:"56vcr3"}],["path",{d:"M18 11v-1a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v0",key:"1pp0yd"}],["path",{d:"M14 10V9a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v1",key:"u654g"}],["path",{d:"M10 9.5V4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v10",key:"1e2dtv"}],["path",{d:"M18 11a2 2 0 1 1 4 0v3a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15",key:"g6ys72"}]]),i=(0,c.Z)("FileCheck",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m9 15 2 2 4-4",key:"1grp1n"}]]);var o=r(71935),d=r(24258),E=r(66648);let u=[{href:"#",icon:(0,a.jsx)(E.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,a.jsx)(s.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/project/current",icon:(0,a.jsx)(l,{className:"h-5 w-5"}),label:"Current Projects"},{href:"/freelancer/project/applied",icon:(0,a.jsx)(n,{className:"h-5 w-5"}),label:"Under Verification"},{href:"/freelancer/project/completed",icon:(0,a.jsx)(i,{className:"h-5 w-5"}),label:"Completed Projects"},{href:"/freelancer/project/rejected",icon:(0,a.jsx)(o.Z,{className:"h-5 w-5"}),label:"Rejected Projects"}],h=[{href:"/freelancer/settings/personal-info",icon:(0,a.jsx)(d.Z,{className:"h-5 w-5"}),label:"Settings"}]},34859:function(e,t,r){"use strict";var a,s,c,l,n,i,o,d,E,u,h,m,x,f,p,N,I,j,v,C,P,g;r.d(t,{Dy:function(){return u},Dz:function(){return A}});let A={BATCH:3};(m=a||(a={})).PROJECT_HIRING="PROJECT_HIRING",m.SKILL_INTERVIEW="SKILL_INTERVIEW",m.DOMAIN_INTERVIEW="DOMAIN_INTERVIEW",m.TALENT_INTERVIEW="TALENT_INTERVIEW",(x=s||(s={})).ADDED="Added",x.APPROVED="Approved",x.CLOSED="Closed",x.COMPLETED="Completed",(f=c||(c={})).ACTIVE="Active",f.IN_ACTIVE="Inactive",f.NOT_VERIFIED="Not Verified",(p=l||(l={})).BUSINESS="Business",p.FREELANCER="Freelancer",p.BOTH="Both",(N=n||(n={})).ACTIVE="Active",N.IN_ACTIVE="Inactive",(I=i||(i={})).APPLIED="APPLIED",I.NOT_APPLIED="NOT_APPLIED",I.APPROVED="APPROVED",I.FAILED="FAILED",I.STOPPED="STOPPED",I.REAPPLIED="REAPPLIED",(j=o||(o={})).PENDING="Pending",j.ACCEPTED="Accepted",j.REJECTED="Rejected",j.PANEL="Panel",j.INTERVIEW="Interview",(v=d||(d={})).ACTIVE="ACTIVE",v.INACTIVE="INACTIVE",v.ARCHIVED="ARCHIVED",(C=E||(E={})).ACTIVE="Active",C.PENDING="Pending",C.INACTIVE="Inactive",C.CLOSED="Closed",(P=u||(u={})).FREELANCER="FREELANCER",P.ADMIN="ADMIN",P.BUSINESS="BUSINESS",(g=h||(h={})).CREATED="Created",g.CLOSED="Closed",g.ACTIVE="Active"},97540:function(e,t,r){"use strict";var a,s,c,l,n,i;r.d(t,{cd:function(){return a},d8:function(){return o},kJ:function(){return s},sB:function(){return c}}),(l=a||(a={})).Mastery="Mastery",l.Proficient="Proficient",l.Beginner="Beginner",(n=s||(s={})).ACTIVE="Active",n.PENDING="Pending",n.REJECTED="Rejected",n.COMPLETED="Completed",(i=c||(c={})).ACTIVE="ACTIVE",i.PENDING="PENDING",i.REJECTED="REJECTED",i.COMPLETED="COMPLETED";let o={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2455,9726,2688,2971,7023,1744],function(){return e(e.s=98023)}),_N_E=e.O()}]);