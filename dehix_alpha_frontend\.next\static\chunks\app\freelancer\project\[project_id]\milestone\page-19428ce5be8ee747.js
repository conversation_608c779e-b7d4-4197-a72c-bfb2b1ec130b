(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7759],{29855:function(e,t,a){Promise.resolve().then(a.bind(a,97832))},5891:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},76035:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},43193:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]])},49100:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},47390:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},29406:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},36141:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},33907:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},73347:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},33149:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]])},40064:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("TabletSmartphone",[["rect",{width:"10",height:"14",x:"3",y:"8",rx:"2",key:"1vrsiq"}],["path",{d:"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4",key:"1j4zmg"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},10883:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},97832:function(e,t,a){"use strict";a.r(t);var s=a(57437),n=a(2265),r=a(16463),l=a(29406),i=a(64797),c=a(62688),o=a(56508),h=a(66227),d=a(78068),u=a(15922);t.default=()=>{let{project_id:e}=(0,r.useParams)(),[t,a]=(0,n.useState)([]),[f,m]=(0,n.useState)(!0),p=(0,n.useCallback)(async()=>{try{var t;let s=await u.b.get("/milestones",{params:{projectId:e}}),n={},r=null===(t=s.data)||void 0===t?void 0:t.data.map(e=>{var t;let a=(null===(t=e.stories)||void 0===t?void 0:t.length)?e.stories:null;return e._id&&(n[e._id]=a),{...e,stories:a||[]}});a(r),m(!1)}catch(e){(0,d.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.error("Error fetching milestones:",e),m(!1)}},[e]),y=async function(e,t,a){var s,n;let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],l=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;if(e.preventDefault(),!a){console.error("Milestone ID is undefined.");return}let i=r&&l?(null!==(s=a.stories)&&void 0!==s?s:[]).map(e=>e._id===l.storyId?{...e,tasks:[...e.tasks||[],l.formData]}:e):[...a.stories||[],t],c={...a,stories:i};try{await u.b.put("/milestones/".concat(a._id),c),(0,d.Am)({title:"Success",description:r?"Task added successfully!":"Story added successfully!",duration:3e3}),p()}catch(e){console.error("Error updating milestone:",(null===(n=e.response)||void 0===n?void 0:n.data)||e.message),(0,d.Am)({title:"Error",description:"Failed to update milestone.",variant:"destructive",duration:3e3})}};return(0,n.useEffect)(()=>{p()},[p]),(0,s.jsxs)("div",{className:"flex min-h-screen h-auto w-full flex-col bg-muted/40",children:[(0,s.jsx)(i.Z,{menuItemsTop:h.yn,menuItemsBottom:h.$C,active:""}),(0,s.jsxs)("div",{className:"flex flex-col sm:gap-4 md:py-0 sm:py-4 sm:pl-14",children:[(0,s.jsx)(c.Z,{menuItemsTop:h.yn,menuItemsBottom:h.$C,activeMenu:"",breadcrumbItems:[{label:"Dashboard",link:"/dashboard/freelancer"},{label:"Project",link:"/dashboard/freelancer"},{label:e,link:"/freelancer/project/".concat(e)},{label:"Milestone",link:"#"}]}),(0,s.jsxs)("div",{className:"py-8 px-2 md:px-4",children:[(0,s.jsx)("div",{className:"flex justify-between items-center",children:(0,s.jsx)("h1",{className:"text-xl md:text-2xl font-bold",children:"Project Milestones"})}),(0,s.jsx)("div",{className:"w-full flex justify-center items-center",children:f?(0,s.jsx)("p",{children:"Loading milestones..."}):t.length>0?(0,s.jsx)(o.Z,{milestones:t,handleStorySubmit:y,fetchMilestones:p,isFreelancer:!0}):(0,s.jsx)("div",{className:"flex justify-center items-center h-[50vh]",children:(0,s.jsxs)("div",{className:"col-span-full text-center mt-20 w-full",children:[(0,s.jsx)(l.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,s.jsx)("p",{className:"text-gray-500",children:"No Milestone created"})]})})})]})]})]})}},66227:function(e,t,a){"use strict";a.d(t,{$C:function(){return x},yL:function(){return Z},yn:function(){return v}});var s=a(57437),n=a(11005),r=a(33149),l=a(76035),i=a(49100),c=a(40064),o=a(43193),h=a(36141),d=a(33907),u=a(47390),f=a(73347),m=a(24258),p=a(5891),y=a(10883),k=a(66648);let v=[{href:"#",icon:(0,s.jsx)(k.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,s.jsx)(n.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:(0,s.jsx)(r.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:(0,s.jsx)(l.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:(0,s.jsx)(i.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:(0,s.jsx)(c.Z,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:(0,s.jsx)(o.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:(0,s.jsx)(h.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:(0,s.jsx)(d.Z,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:(0,s.jsx)(u.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,s.jsx)(f.Z,{className:"h-5 w-5"}),label:"Notes"}],x=[{href:"/freelancer/settings/personal-info",icon:(0,s.jsx)(m.Z,{className:"h-5 w-5"}),label:"Settings"}];k.default,n.Z,f.Z,p.Z,y.Z;let Z=[{href:"#",icon:(0,s.jsx)(k.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,s.jsx)(n.Z,{className:"h-5 w-5"}),label:"Home"}]}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2455,9726,2688,2971,7023,1744],function(){return e(e.s=29855)}),_N_E=e.O()}]);