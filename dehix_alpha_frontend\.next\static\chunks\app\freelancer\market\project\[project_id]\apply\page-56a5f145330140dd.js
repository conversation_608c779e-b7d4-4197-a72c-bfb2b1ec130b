(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6055],{96529:function(e,s,l){Promise.resolve().then(l.bind(l,61813))},61813:function(e,s,l){"use strict";l.r(s),l.d(s,{default:function(){return Y}});var t=l(57437),i=l(2265),a=l(16463),r=l(3274),n=l(78068),c=l(64797),d=l(66227),o=l(11444),m=l(52022),x=l(25912),h=l(51077),u=l(29338),p=l(75733),j=l(40933),v=l(24241),g=l(22468),N=l(4919),f=l(47390),b=l(92940),y=l(71935),w=l(48281),C=l(76780),S=l(38711);let k=e=>{let{percentage:s,size:l,strokeWidth:i}=e,a=(l-i)/2,r=2*a*Math.PI;return(0,t.jsxs)("div",{className:"relative",style:{width:l,height:l},children:[(0,t.jsxs)("svg",{width:l,height:l,viewBox:"0 0 ".concat(l," ").concat(l),className:"transform -rotate-90",children:[(0,t.jsx)("circle",{cx:l/2,cy:l/2,r:a,strokeWidth:i,className:"stroke-gray-800 fill-none"}),(0,t.jsx)("circle",{cx:l/2,cy:l/2,r:a,strokeWidth:i,className:"stroke-green-500 fill-none transition-all duration-500 ease-out",strokeDasharray:r,strokeDashoffset:r-s/100*r,strokeLinecap:"round"})]}),(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-3xl font-bold",children:s})})]})},Z=e=>{let{icon:s,label:l,value:i}=e;return(0,t.jsx)("div",{className:"bg-gray-800 p-4 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"text-gray-400",children:s}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold",children:i}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:l})]})]})})};var D=l(48185),A=l(79055),B=l(89733),E=l(61617),I=l(86864),R=e=>{var s;let{projectData:l,onClose:a,setShowAnalyticsDrawer:r}=e,[n,c]=(0,i.useState)("insights"),[d,o]=(0,i.useState)(!1),[h,p]=(0,i.useState)(null);if(!l)return(0,t.jsx)(D.Zb,{className:"flex flex-col h-full w-full overflow-auto text-white items-center justify-center",children:(0,t.jsx)("p",{children:"Loading project data..."})});let j=(()=>{var e,s,t,i,a,r,n,c,d;let o=(null===(e=l.profiles)||void 0===e?void 0:e.reduce((e,s)=>{var l;return e+((null===(l=s.totalBid)||void 0===l?void 0:l.length)||0)},0))||0,m=(null===(s=l.profiles)||void 0===s?void 0:s.reduce((e,s)=>{var l;return e+((null===(l=s.selectedFreelancer)||void 0===l?void 0:l.length)||0)},0))||0,x=(null===(t=l.profiles)||void 0===t?void 0:t.filter(e=>"number"==typeof e.rate&&!isNaN(e.rate)))||[],h=x.length>0?x.reduce((e,s)=>e+s.rate,0)/x.length:0,u=x.length>0?Math.max(...x.map(e=>e.rate)):0,p=x.length>0?Math.min(...x.map(e=>e.rate)):0,j=Math.min(((null===(i=l.profiles)||void 0===i?void 0:i.reduce((e,s)=>e+(s.experience||0),0))||0)/((null===(a=l.profiles)||void 0===a?void 0:a.length)||1)*10,100),v=u>0?Math.max(0,100-h/u*50):0;l.createdAt&&(new Date().getTime(),new Date(l.createdAt).getTime());let g=l.maxBidDate?Math.floor((new Date(l.maxBidDate).getTime()-new Date().getTime())/864e5):"N/A";return{score:Math.round((j+v)/2),applied:o,interviews:Math.floor(.15*o),hired:m,terminated:0,jobDuration:"N/A"!==g&&g>0?"".concat(g," days remaining"):"COMPLETED"===l.status?"Completed":"Deadline passed/N/A",startDate:l.startBidDate?new Date(l.startBidDate).toISOString().split("T")[0]:"N/A",endDate:l.maxBidDate?new Date(l.maxBidDate).toISOString().split("T")[0]:"N/A",avgBid:"$".concat(Math.round(h),"/hr"),topBid:"$".concat(u,"/hr"),lowBid:"$".concat(p,"/hr"),totalBids:o,status:l.status,clientResponsiveness:85+Math.floor(15*Math.random()),projectComplexity:Math.min(15*((null===(r=l.skillsRequired)||void 0===r?void 0:r.length)||0),100),rateCompetitiveness:Math.round(v),clientHistory:{totalProjects:8+Math.floor(12*Math.random()),completionRate:90+Math.floor(10*Math.random()),avgRating:4.2+.8*Math.random()},relevance:Math.min(20*((null===(n=l.skillsRequired)||void 0===n?void 0:n.length)||0),100),timeline:[l.createdAt&&{date:new Date(l.createdAt).toISOString().split("T")[0],event:"Project Posted"},l.startBidDate&&{date:new Date(l.startBidDate).toISOString().split("T")[0],event:"Bidding Started"},{date:new Date().toISOString().split("T")[0],event:"Current Status"},l.maxBidDate&&{date:new Date(l.maxBidDate).toISOString().split("T")[0],event:"Bidding Deadline"}].filter(Boolean),competitorInsights:{avgExperience:"".concat(Math.round(((null===(c=l.profiles)||void 0===c?void 0:c.reduce((e,s)=>e+(s.experience||0),0))||0)/((null===(d=l.profiles)||void 0===d?void 0:d.length)||1))," years"),topSkills:(l.skillsRequired||[]).slice(0,4),bidRange:"$".concat(p,"-").concat(u,"/hr")}}})(),v=[{icon:(0,t.jsx)(m.Z,{className:"h-4 w-4"}),label:"Applied",value:j.applied},{icon:(0,t.jsx)(f.Z,{className:"h-4 w-4"}),label:"Interviews",value:j.interviews},{icon:(0,t.jsx)(b.Z,{className:"h-4 w-4"}),label:"Hired",value:j.hired},{icon:(0,t.jsx)(y.Z,{className:"h-4 w-4"}),label:"Terminated",value:j.terminated}];return(0,t.jsxs)(D.Zb,{className:"flex flex-col h-full w-full overflow-auto text-white  ",children:[(0,t.jsx)("div",{className:"p-4 border-b ",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-lg font-semibold",children:[l.projectName," - Proposal Details"]}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:(null===(s=l.projectDomain)||void 0===s?void 0:s.join(", "))||"N/A"})]}),(0,t.jsx)(B.z,{variant:"outline",size:"sm",className:"text-gray-400 border-gray-700",onClick:()=>r(!1),children:"Close"})]})}),(0,t.jsxs)(I.mQ,{defaultValue:"insights",className:"w-full flex flex-col flex-grow",children:[(0,t.jsxs)(I.dr,{className:"w-full bg-[#09090B] gap-6 rounded-none",children:[(0,t.jsx)(I.SP,{value:"insights",className:"data-[state=active]:bg-gray-800 data-[state=active]:text-white hover:bg-gray-700",onClick:()=>c("insights"),children:"Insights"}),(0,t.jsx)(I.SP,{value:"timeline",className:"data-[state=active]:bg-gray-800 data-[state=active]:text-white hover:bg-gray-700",onClick:()=>c("timeline"),children:"Timeline"}),(0,t.jsx)(I.SP,{value:"competition",className:"data-[state=active]:bg-gray-800 data-[state=active]:text-white hover:bg-gray-700",onClick:()=>c("competition"),children:"Competition"})]}),(0,t.jsxs)("div",{className:"flex-grow overflow-y-auto p-4",children:[" ",(0,t.jsxs)(I.nU,{value:"insights",className:"mt-0",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)(D.Zb,{className:"  col-span-1 bg-gray-850",children:[(0,t.jsx)(D.Ol,{className:"pb-2",children:(0,t.jsx)(D.ll,{className:"text-sm font-medium text-gray-400",children:"Bid score"})}),(0,t.jsx)(D.aY,{className:"flex justify-center",children:(0,t.jsx)(k,{percentage:j.score,size:140,strokeWidth:12})})]}),(0,t.jsxs)(D.Zb,{className:"  md:col-span-2 bg-gray-850",children:[(0,t.jsx)(D.Ol,{className:"pb-2",children:(0,t.jsx)(D.ll,{className:"text-sm font-medium text-gray-400",children:"Metrics"})}),(0,t.jsx)(D.aY,{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:v.map((e,s)=>(0,t.jsx)(Z,{icon:e.icon,label:e.label,value:e.value},s))})]})]}),(0,t.jsx)("div",{className:"mt-4 grid grid-cols-1 gap-4",children:(0,t.jsxs)(D.Zb,{className:"  bg-gray-850",children:[(0,t.jsx)(D.Ol,{className:"pb-2",children:(0,t.jsx)(D.ll,{className:"text-sm font-medium text-gray-400",children:"Project Details"})}),(0,t.jsxs)(D.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Duration"}),(0,t.jsx)("p",{className:"font-medium",children:j.jobDuration})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Start date"}),(0,t.jsx)("p",{className:"font-medium",children:j.startDate})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Deadline"}),(0,t.jsx)("p",{className:"font-medium",children:j.endDate})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Status"}),(0,t.jsx)(A.C,{className:"".concat("COMPLETED"===l.status?"bg-green-600/20 text-green-500 hover:bg-green-600/30":"ACTIVE"===l.status?"bg-blue-600/20 text-blue-500 hover:bg-blue-600/30":"bg-yellow-600/20 text-yellow-500 hover:bg-yellow-600/30"),children:l.status})]})]}),l.budget&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Budget"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:"hourly"===l.budget.type&&l.budget.hourly?(0,t.jsxs)(A.C,{className:"bg-purple-600/20 text-purple-400",children:["Hourly: $",l.budget.hourly.minRate,"-$",l.budget.hourly.maxRate,"/hr (Est."," ",l.budget.hourly.estimatedHours," hrs)"]}):"fixed"===l.budget.type&&l.budget.fixedAmount?(0,t.jsxs)(A.C,{className:"bg-purple-600/20 text-purple-400",children:["Fixed: $",l.budget.fixedAmount]}):(0,t.jsx)(A.C,{className:"bg-gray-600/20 text-gray-400",children:"Budget not specified"})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Required Skills"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:l.skillsRequired&&l.skillsRequired.length>0?l.skillsRequired.map((e,s)=>(0,t.jsx)(A.C,{className:"bg-gray-800 text-gray-300",children:e},s)):(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"No specific skills listed."})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Project Profiles"}),l.profiles&&l.profiles.length>0?l.profiles.map((e,s)=>{var l,i;return(0,t.jsxs)("div",{className:"border border-gray-700 rounded-lg p-3",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.domain}),(0,t.jsxs)(A.C,{className:"bg-blue-600/20 text-blue-500",children:["$",e.rate,"/hr"]})]}),(0,t.jsx)("p",{className:"text-sm text-gray-400 mb-2",children:e.description||"No description provided."}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-2 text-xs",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Required: "}),(0,t.jsx)("span",{children:e.freelancersRequired||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("span",{className:"text-gray-500",children:["Experience:"," "]}),(0,t.jsxs)("span",{children:[e.experience||"N/A"," years"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Bids: "}),(0,t.jsx)("span",{children:(null===(l=e.totalBid)||void 0===l?void 0:l.length)||0})]})]}),(0,t.jsxs)("div",{className:"mt-2 text-xs",children:[(0,t.jsxs)("span",{className:"text-gray-500",children:["Selected Freelancers:"," "]}),(0,t.jsx)("span",{children:(null===(i=e.selectedFreelancer)||void 0===i?void 0:i.length)||0})]})]},e._id)}):(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"No project profiles defined."})]})]})]})}),(0,t.jsxs)("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)(D.Zb,{className:"  bg-gray-850",children:[(0,t.jsx)(D.Ol,{className:"pb-2",children:(0,t.jsx)(D.ll,{className:"text-sm font-medium text-gray-400",children:"About the Client"})}),(0,t.jsxs)(D.aY,{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:l.companyName||"N/A"}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:l.email||"N/A"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(x.Z,{className:"text-gray-500 h-4 w-4"}),(0,t.jsx)("div",{children:(0,t.jsxs)("p",{className:"text-sm",children:[j.clientHistory.totalProjects," projects posted"]})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(b.Z,{className:"text-gray-500 h-4 w-4"}),(0,t.jsx)("div",{children:(0,t.jsxs)("p",{className:"text-sm",children:[j.clientHistory.completionRate,"% completion rate"]})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(u.Z,{className:"text-yellow-500 h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm",children:j.clientHistory.avgRating.toFixed(1)}),(0,t.jsx)("div",{className:"flex ml-1",children:[,,,,,].fill(0).map((e,s)=>(0,t.jsx)(u.Z,{size:12,className:s<Math.floor(j.clientHistory.avgRating)?"text-yellow-500 fill-yellow-500":"text-gray-500"},s))})]})]})]}),(0,t.jsxs)(D.Zb,{className:"  md:col-span-2 bg-gray-850",children:[(0,t.jsx)(D.Ol,{className:"pb-2",children:(0,t.jsx)(D.ll,{className:"text-sm font-medium text-gray-400",children:"Bid Competitiveness"})}),(0,t.jsxs)(D.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Avg bid"}),(0,t.jsx)("p",{className:"font-medium text-green-500",children:j.avgBid})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Top bid"}),(0,t.jsx)("p",{className:"font-medium",children:j.topBid})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Low bid"}),(0,t.jsx)("p",{className:"font-medium",children:j.lowBid})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Relevance"}),(0,t.jsxs)("p",{className:"text-xs font-medium",children:[j.relevance,"%"]})]}),(0,t.jsx)(E.E,{value:j.relevance,className:"h-2 bg-gray-800"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Rate competitiveness"}),(0,t.jsxs)("p",{className:"text-xs font-medium",children:[j.rateCompetitiveness,"%"]})]}),(0,t.jsx)(E.E,{value:j.rateCompetitiveness,className:"h-2 bg-gray-800"})]})]})]})]})]}),(0,t.jsx)(I.nU,{value:"timeline",className:"mt-0",children:(0,t.jsxs)(D.Zb,{className:"  bg-gray-850",children:[(0,t.jsx)(D.Ol,{children:(0,t.jsx)(D.ll,{className:"text-md font-medium",children:"Project Timeline"})}),(0,t.jsx)(D.aY,{children:(0,t.jsx)("div",{className:"space-y-4",children:j.timeline&&j.timeline.length>0?j.timeline.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full flex-shrink-0"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium",children:e.event}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:e.date})]})]},s)):(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"No timeline data available."})})})]})}),(0,t.jsxs)(I.nU,{value:"competition",className:"mt-0",children:[(0,t.jsxs)(D.Zb,{className:"  bg-gray-850",children:[(0,t.jsx)(D.Ol,{children:(0,t.jsx)(D.ll,{className:"text-md font-medium",children:"Competitor Analysis"})}),(0,t.jsxs)(D.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium mb-2",children:"Average Experience"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:j.competitorInsights.avgExperience})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium mb-2",children:"Bid Range"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:j.competitorInsights.bidRange})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium mb-2",children:"Total Competitors"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:j.totalBids})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium mb-2",children:"Top Skills Among Competitors"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:j.competitorInsights.topSkills&&j.competitorInsights.topSkills.length>0?j.competitorInsights.topSkills.map((e,s)=>(0,t.jsx)(A.C,{className:"bg-gray-800 text-gray-300",children:e},s)):(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"No top skills identified among competitors."})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium mb-2",children:"Bid Distribution"}),l.profiles&&l.profiles.length>0?(0,t.jsx)("div",{className:"space-y-2",children:l.profiles.map((e,s)=>{var l;return(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm",children:e.domain}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)("span",{className:"text-sm text-gray-400",children:[(null===(l=e.totalBid)||void 0===l?void 0:l.length)||0," bids"]}),(0,t.jsxs)(A.C,{className:"bg-blue-600/20 text-blue-500",children:["$",e.rate||"N/A","/hr"]})]})]},e._id)})}):(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"No bid distribution data available."})]})]})]}),(0,t.jsxs)(D.Zb,{className:"  mt-4 bg-gray-850",children:[(0,t.jsx)(D.Ol,{children:(0,t.jsx)(D.ll,{className:"text-md font-medium",children:"Competitive Edge"})}),(0,t.jsx)(D.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-start gap-4",children:[(0,t.jsx)("div",{className:"bg-green-500/20 rounded-full p-2",children:(0,t.jsx)(w.Z,{className:"h-5 w-5 text-green-500"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Strong skill match"}),(0,t.jsxs)("p",{className:"text-sm text-gray-400",children:["Your skills match ",j.relevance,"% of the project requirements."]})]})]}),(0,t.jsxs)("div",{className:"flex items-start gap-4",children:[(0,t.jsx)("div",{className:"bg-yellow-500/20 rounded-full p-2",children:(0,t.jsx)(C.Z,{className:"h-5 w-5 text-yellow-500"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Rate consideration"}),(0,t.jsxs)("p",{className:"text-sm text-gray-400",children:["Your rate competitiveness is"," ",j.rateCompetitiveness,"%. Consider highlighting your unique value proposition."]})]})]}),(0,t.jsxs)("div",{className:"flex items-start gap-4",children:[(0,t.jsx)("div",{className:"bg-blue-500/20 rounded-full p-2",children:(0,t.jsx)(S.Z,{className:"h-5 w-5 text-blue-500"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Project complexity"}),(0,t.jsxs)("p",{className:"text-sm text-gray-400",children:["This project has a complexity score of"," ",j.projectComplexity,"% based on required skills."]})]})]})]})})]})]})]})]}),(0,t.jsx)("div",{className:"mt-auto p-4 border-t  ",children:(0,t.jsx)("div",{className:"flex justify-between",children:(0,t.jsx)(B.z,{variant:"outline",size:"sm",className:"text-gray-400 border-gray-700",onClick:()=>r(!1),children:"Back to projects"})})})]})},T=l(77209),_=l(54662),M=l(70402),P=l(15922),O=e=>{var s,l,a,c,d,f,b,y,w,C,S,k,Z,E,I,O,z,Y,$,q,H,L,F,U,V,X,W,K;let{project:Q,isLoading:G,onCancel:J}=e,[ee,es]=(0,i.useState)(""),[el,et]=(0,i.useState)(!1),[ei,ea]=(0,i.useState)(!1),[er,en]=(0,i.useState)(!1),[ec,ed]=(0,i.useState)(0),[eo,em]=(0,i.useState)(!1),[ex,eh]=(0,i.useState)(null),[eu,ep]=(0,i.useState)([]),[ej,ev]=(0,i.useState)(!1),eg=(0,o.v9)(e=>e.user),[eN,ef]=(0,i.useState)(0),[eb,ey]=(0,i.useState)([]);(0,i.useEffect)(()=>{ef(parseInt(localStorage.getItem("DHX_CONNECTS")||"0",10));let e=()=>{ef(parseInt(localStorage.getItem("DHX_CONNECTS")||"0",10))};return window.addEventListener("connectsUpdated",e),ew(),()=>{window.removeEventListener("connectsUpdated",e)}},[eg.uid]);let ew=(0,i.useCallback)(async()=>{try{let e=(await P.b.get("/bid/".concat(eg.uid,"/bid"))).data.data.filter(e=>e.project_id===Q._id&&e.bidder_id===eg.uid).map(e=>e.profile_id);ey(e);let s=Q.profiles.filter(s=>e.includes(s._id||""));s.length>0?(console.log("Applied profiles:",s),ep(s),en(!0)):(ep([]),en(!1))}catch(e){console.error("API Error fetching applied data:",e),(0,n.Am)({variant:"destructive",title:"Error",description:"Failed to retrieve application status. Please try again."})}},[eg.uid,Q._id,Q.profiles]),eC=(null==Q?void 0:null===(s=Q.description)||void 0===s?void 0:s.length)>100,eS=el||!eC?null==Q?void 0:Q.description:(null==Q?void 0:Q.description.slice(0,100))+"...",ek=async()=>{try{await P.b.patch("/public/connect?userId=".concat(eg.uid,"&isFreelancer=",!0)),(0,n.Am)({title:"Connects Requested",description:"Your request for more connects has been submitted."});let e=parseInt(localStorage.getItem("DHX_CONNECTS")||"0",10);localStorage.setItem("DHX_CONNECTS",Math.max(0,e+100).toString()),window.dispatchEvent(new Event("connectsUpdated"))}catch(e){console.error("Error requesting connects:",e),(0,n.Am)({title:"Something went wrong",description:"Please try again later."})}},eZ=async e=>{if(e.preventDefault(),!ex||!ex._id){(0,n.Am)({title:"Error",description:"No profile selected for bidding"});return}let s=parseInt(localStorage.getItem("DHX_CONNECTS")||"0",10);if("number"!=typeof ex.minConnect||isNaN(ec)||isNaN(s)||ec>s){(0,n.Am)({description:"Connects are insufficient"});return}if(ee.length<500){(0,n.Am)({title:"Cover letter too short",description:"Please write at least ".concat(500," characters.")});return}if(ee.length>2e3){(0,n.Am)({title:"Cover letter too long",description:"Maximum allowed is ".concat(2e3," characters.")});return}em(!0);try{await P.b.post("/bid",{current_price:ec,description:ee,bidder_id:eg.uid,profile_id:ex._id,project_id:Q._id,biddingValue:ec});let e=(s-ec).toString();localStorage.setItem("DHX_CONNECTS",e),window.dispatchEvent(new Event("connectsUpdated")),ed(0),ea(!1),en(!0),es(""),(0,n.Am)({title:"Application Submitted",description:"Your application has been successfully submitted."}),ew(),ev(!0)}catch(e){console.error("Error submitting bid:",e),(0,n.Am)({title:"Something went wrong",description:"Please try again later."})}finally{em(!1)}},eD=(null==Q?void 0:null===(l=Q.bids)||void 0===l?void 0:l.length)||0,eA=eD>0?((null==Q?void 0:null===(a=Q.bids)||void 0===a?void 0:a.reduce((e,s)=>e+((null==s?void 0:s.current_price)||0),0))/eD).toFixed(2):"N/A",eB=new Date((null==Q?void 0:Q.createdAt)||Date.now()).toLocaleDateString(),eE=eb.some(e=>Q.profiles.some(s=>s._id===e));return G?(0,t.jsx)("div",{className:"text-center py-10",children:(0,t.jsx)(r.Z,{className:"animate-spin w-5 h-5"})}):(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[!ej&&(0,t.jsxs)(t.Fragment,{children:[" ",(0,t.jsxs)("div",{className:"md:col-span-2 space-y-6",children:[(0,t.jsx)(D.Zb,{children:(0,t.jsxs)(D.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold",children:null==Q?void 0:Q.projectName}),(0,t.jsxs)("p",{className:"text-sm",children:["Posted on ",eB]})]}),(0,t.jsxs)("p",{className:"text-muted-foreground",children:["Position: ",null==Q?void 0:Q.projectType," Developer"]}),(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("p",{children:eS}),eC&&(0,t.jsx)("button",{onClick:()=>{et(!el)},className:"text-blue-500 hover:underline text-sm mt-2",children:el?"less":"more"})]})]}),(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("h2",{className:"text-lg font-medium mb-2",children:"Skills required"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:null==Q?void 0:null===(c=Q.skillsRequired)||void 0===c?void 0:c.map((e,s)=>(0,t.jsx)(A.C,{variant:"secondary",className:"px-3 py-1 rounded-full bg-gray-200 text-gray-700",children:e},s))})]})]})}),(0,t.jsx)(D.Zb,{children:(0,t.jsxs)(D.aY,{className:"p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium mb-4",children:"Client Information"}),(0,t.jsxs)("div",{className:"flex items-start gap-4",children:[(0,t.jsx)("div",{className:"bg-red-500 rounded-full w-8 h-8 flex items-center justify-center text-white",children:(0,t.jsx)(m.Z,{size:16})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:null==Q?void 0:Q.companyName}),(0,t.jsxs)("div",{className:"flex items-center gap-2 mt-2 text-sm",children:[(0,t.jsx)(x.Z,{size:14}),(0,t.jsx)("span",{children:"12 projects posted"}),(0,t.jsx)("span",{className:"mx-1",children:"|"}),(0,t.jsx)(h.Z,{size:14}),(0,t.jsx)("span",{children:"$3.5k spent"})]}),(0,t.jsxs)("div",{className:"flex items-center mt-2",children:[(0,t.jsx)(u.Z,{className:"text-yellow-400",size:16}),(0,t.jsx)("span",{className:"ml-1",children:"4.5"})]})]})]}),(0,t.jsxs)("p",{className:"mt-4 text-sm",children:[null==Q?void 0:Q.companyName," is a leading technology company focused on innovative AI solutions. They have a history of successful project completions with freelancers on our platform."]})]})}),(0,t.jsx)(D.Zb,{children:(0,t.jsxs)(D.aY,{className:"p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium mb-4",children:"Project Details"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Domains"}),(0,t.jsx)("p",{children:null==Q?void 0:null===(d=Q.projectDomain)||void 0===d?void 0:d.join(", ")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Status"}),(0,t.jsx)("p",{children:null==Q?void 0:Q.status})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Budget Type"}),(0,t.jsx)("p",{children:null==Q?void 0:null===(f=Q.budget)||void 0===f?void 0:f.type})]}),(null==Q?void 0:null===(b=Q.budget)||void 0===b?void 0:b.type.toUpperCase())==="HOURLY"?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Hourly Rate"}),(0,t.jsxs)("p",{children:["$",(null==Q?void 0:null===(w=Q.budget)||void 0===w?void 0:null===(y=w.hourly)||void 0===y?void 0:y.minRate)||0," - $",(null==Q?void 0:null===(S=Q.budget)||void 0===S?void 0:null===(C=S.hourly)||void 0===C?void 0:C.maxRate)||0," /hr"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Estimated Hours"}),(0,t.jsxs)("p",{children:[(null==Q?void 0:null===(Z=Q.budget)||void 0===Z?void 0:null===(k=Z.hourly)||void 0===k?void 0:k.estimatedHours)||0," hours"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Total Budget"}),(0,t.jsxs)("p",{children:["~$",((((null==Q?void 0:null===(I=Q.budget)||void 0===I?void 0:null===(E=I.hourly)||void 0===E?void 0:E.minRate)||0)+((null==Q?void 0:null===(z=Q.budget)||void 0===z?void 0:null===(O=z.hourly)||void 0===O?void 0:O.maxRate)||0))/2*((null==Q?void 0:null===($=Q.budget)||void 0===$?void 0:null===(Y=$.hourly)||void 0===Y?void 0:Y.estimatedHours)||0)).toLocaleString()]})]})]}):(null==Q?void 0:null===(q=Q.budget)||void 0===q?void 0:q.type.toUpperCase())==="FIXED"?(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Fixed Budget"}),(0,t.jsxs)("p",{children:["$",(null==Q?void 0:null===(L=Q.budget)||void 0===L?void 0:null===(H=L.fixedAmount)||void 0===H?void 0:H.toLocaleString())||0]})]}):null]})]})}),(0,t.jsx)(D.Zb,{children:(0,t.jsxs)(D.aY,{className:"p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium mb-4",children:"Bid Summary"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Total Bids"}),(0,t.jsx)("p",{children:eD})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Average Bid"}),(0,t.jsxs)("p",{children:["$",eA]})]})]})]})}),(0,t.jsxs)(D.Zb,{children:[(0,t.jsx)(D.Ol,{children:(0,t.jsx)(D.ll,{className:"text-lg font-medium",children:"Your Application"})}),(0,t.jsxs)(D.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{htmlFor:"coverLetter",className:"block mb-2 font-medium",children:"Cover Letter"}),(0,t.jsx)(N.g,{value:ee,onChange:e=>{let s=e.target.value.replace(/\s{10,}/g," ").replace(/^\s+/,"");s.length<=2e3&&es(s)},placeholder:"Write your cover letter...",rows:8,className:"w-full p-3 border rounded-md resize-none",disabled:eE}),(0,t.jsx)("div",{className:"text-sm mt-1 text-right",children:(0,t.jsxs)("span",{className:ee.length<500?"text-yellow-600":ee.length>2e3?"text-red-600":"",children:[ee.length<500&&"".concat(500-ee.length," characters left to reach minimum."),ee.length>=500&&ee.length<=2e3&&"".concat(2e3-ee.length," characters left."),ee.length>2e3&&"Character limit exceeded!"]})})]}),(0,t.jsxs)("div",{className:"flex gap-4 mt-4",children:[eE?(0,t.jsxs)(B.z,{onClick:()=>{ev(!0)},className:"w-full md:w-auto px-8",children:[(0,t.jsx)(p.Z,{className:"mr-2 h-4 w-4"})," View Proposal"]}):(0,t.jsxs)(_.Vq,{open:ei,onOpenChange:ea,children:[(0,t.jsx)(B.z,{onClick:()=>{(null==Q?void 0:Q.profiles)&&Q.profiles.length>0&&(1===Q.profiles.length?(eh(Q.profiles[0]),ed(Q.profiles[0].minConnect||0)):eh(null),ea(!0))},className:"w-full md:w-auto px-8",disabled:G||er||eE,children:G?"Submitting...":eE?"Applied":"Apply Now"}),ex&&eN<(ex.minConnect||0)?(0,t.jsxs)(_.cZ,{children:[(0,t.jsxs)(_.fK,{children:[(0,t.jsx)(_.$N,{children:"Insufficient Connects"}),(0,t.jsxs)(_.Be,{children:["You don't have enough connects to apply for this project.",(0,t.jsx)("br",{}),"Please"," ",(0,t.jsx)("span",{className:"text-blue-600 font-bold cursor-pointer",onClick:ek,children:"Request Connects"})," ","to proceed."]})]}),(0,t.jsx)(_.cN,{children:(0,t.jsx)(B.z,{variant:"outline",onClick:()=>ea(!1),children:"Close"})})]}):(0,t.jsxs)(_.cZ,{className:"sm:max-w-[425px]",children:[(0,t.jsxs)(_.fK,{children:[(0,t.jsxs)(_.$N,{children:["Apply for ",Q.projectName]}),(0,t.jsx)(_.Be,{children:"Submit your bid to apply for this project."})]}),(0,t.jsxs)("form",{onSubmit:eZ,children:[(0,t.jsx)("div",{className:"grid gap-4 py-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(M.Label,{htmlFor:"bidAmount",className:"text-center",children:"Connects"}),(0,t.jsxs)("div",{className:"col-span-3 relative",children:[(0,t.jsx)(T.I,{id:"bidAmount",type:"number",value:ec,onChange:e=>ed(Number(e.target.value)),className:"w-full pl-2 pr-1",required:!0,min:null==ex?void 0:ex.minConnect,placeholder:"Enter connects amount"}),(0,t.jsx)("div",{className:"absolute right-8 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"connects"})]})]})}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)(B.z,{type:"submit",disabled:er||eo,children:eo?(0,t.jsx)(r.Z,{className:"animate-spin w-6 h-6"}):er?"Applied":"Submit Bid"})})]})]})]}),(0,t.jsx)(B.z,{onClick:J,variant:"outline",className:"w-full md:w-auto px-8",children:"Back"})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(D.Zb,{children:(0,t.jsxs)(D.aY,{className:"p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium mb-4",children:"Experience"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(x.Z,{className:"text-gray-500",size:18}),(0,t.jsx)("div",{children:(0,t.jsx)("p",{className:"font-medium",children:"3+ yrs"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(h.Z,{className:"text-gray-500",size:18}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Hourly rate"}),(0,t.jsxs)("p",{children:["$",(null==Q?void 0:null===(U=Q.budget)||void 0===U?void 0:null===(F=U.hourly)||void 0===F?void 0:F.minRate)||0," - $",(null==Q?void 0:null===(X=Q.budget)||void 0===X?void 0:null===(V=X.hourly)||void 0===V?void 0:V.maxRate)||0]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(j.Z,{className:"text-gray-500",size:18}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Time per week"}),(0,t.jsx)("p",{children:"40 hours"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(v.Z,{className:"text-gray-500",size:18}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Project length"}),(0,t.jsx)("p",{children:"3 to 5 months"})]})]})]})]})}),(null==Q?void 0:null===(W=Q.profiles)||void 0===W?void 0:W.length)>0&&(0,t.jsxs)(D.Zb,{children:[(0,t.jsx)(D.Ol,{children:(0,t.jsx)(D.ll,{className:"text-lg font-medium",children:"Profiles Needed"})}),(0,t.jsx)(D.aY,{className:"p-6",children:(0,t.jsx)("div",{className:"space-y-4 h-[70vh] overflow-y-scroll no-scrollbar",children:null==Q?void 0:null===(K=Q.profiles)||void 0===K?void 0:K.map((e,s)=>{var l;return(0,t.jsx)(D.Zb,{className:"border border-gray-200",children:(0,t.jsxs)(D.aY,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,t.jsxs)("h3",{className:"font-medium text-xs",children:[null==e?void 0:e.domain," Developer"]}),(0,t.jsxs)(A.C,{variant:"outline",children:[null==e?void 0:e.freelancersRequired," Needed"]})]}),(0,t.jsx)("p",{className:"mb-3 text-sm",children:null==e?void 0:e.description}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 mb-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Experience"}),(0,t.jsxs)("p",{className:"font-medium",children:[null==e?void 0:e.experience,"+ years"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Rate"}),(0,t.jsxs)("p",{className:"font-medium",children:["$",null==e?void 0:e.rate,"/hr"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Minimum Connect"}),(0,t.jsx)("p",{className:"font-medium",children:null==e?void 0:e.minConnect})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:"Skills"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:null==e?void 0:null===(l=e.skills)||void 0===l?void 0:l.map((e,s)=>(0,t.jsx)(A.C,{variant:"secondary",className:"border border-gray-200",children:e},s))})]}),(0,t.jsx)(B.z,{size:"sm",className:"w-full mt-4 ".concat(eb.includes(e._id||"")?"cursor-not-allowed":""),onClick:()=>{eb.includes(e._id||"")?ev(!0):(eh(e),ed(e.minConnect||0),ea(!0))},disabled:eb.includes(e._id||"")||er,children:eb.includes(e._id||"")?(0,t.jsxs)("span",{className:"flex items-center justify-center",children:[(0,t.jsx)(g.Z,{className:"mr-2 h-4 w-4"})," Applied"]}):"Apply for this role"})]})},(null==e?void 0:e._id)||s)})})})]})]})]}),ej&&(0,t.jsxs)("div",{className:"col-span-1 md:col-span-3",children:[" ",(0,t.jsx)(R,{projectData:Q,setShowAnalyticsDrawer:ev})]})]})},z=l(62688),Y=()=>{let e=(0,a.useParams)(),s=(0,a.useRouter)(),{toast:l}=(0,n.pm)(),[o,m]=(0,i.useState)(!1),[x,h]=(0,i.useState)(null),[u,p]=(0,i.useState)(""),j=(null==e?void 0:e.project_id)||"123";return((0,i.useEffect)(()=>{(async()=>{try{m(!0);let e=await P.b.get("/project/project/".concat(j));h(e.data.data[0])}catch(e){console.error("Error fetching project details:",e),l({title:"Error",description:"Failed to load project details",variant:"destructive"})}finally{m(!1)}})()},[j,l]),o&&!x)?(0,t.jsx)("div",{className:"flex items-center justify-center h-screen",children:(0,t.jsx)("div",{className:"text-lg",children:(0,t.jsx)(r.Z,{className:"animate-spin w-5 h-5"})})}):(0,t.jsxs)("div",{className:"flex min-h-screen w-full bg-muted flex-col",children:[(0,t.jsx)(c.Z,{menuItemsTop:d.yn,menuItemsBottom:d.$C,active:"Market"}),(0,t.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,t.jsx)(z.Z,{menuItemsTop:d.yn,menuItemsBottom:d.$C,activeMenu:"Market",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Marketplace",link:"#"}]}),(0,t.jsx)("main",{className:"w-[85vw] mx-auto text-foreground",children:x&&(0,t.jsx)(O,{project:x,isLoading:o,onCancel:()=>{s.back()}})})]})]})}},70402:function(e,s,l){"use strict";l.r(s),l.d(s,{Label:function(){return d}});var t=l(57437),i=l(2265),a=l(38364),r=l(12218),n=l(49354);let c=(0,r.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=i.forwardRef((e,s)=>{let{className:l,...i}=e;return(0,t.jsx)(a.f,{ref:s,className:(0,n.cn)(c(),l),...i})});d.displayName=a.f.displayName},61617:function(e,s,l){"use strict";l.d(s,{E:function(){return n}});var t=l(57437),i=l(2265),a=l(52431),r=l(49354);let n=i.forwardRef((e,s)=>{let{className:l,value:i,...n}=e;return(0,t.jsx)(a.fC,{ref:s,className:(0,r.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",l),...n,children:(0,t.jsx)(a.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(i||0),"%)")}})})});n.displayName=a.fC.displayName},86864:function(e,s,l){"use strict";l.d(s,{SP:function(){return d},dr:function(){return c},mQ:function(){return n},nU:function(){return o}});var t=l(57437),i=l(2265),a=l(62447),r=l(49354);let n=a.fC,c=i.forwardRef((e,s)=>{let{className:l,...i}=e;return(0,t.jsx)(a.aV,{ref:s,className:(0,r.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",l),...i})});c.displayName=a.aV.displayName;let d=i.forwardRef((e,s)=>{let{className:l,...i}=e;return(0,t.jsx)(a.xz,{ref:s,className:(0,r.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",l),...i})});d.displayName=a.xz.displayName;let o=i.forwardRef((e,s)=>{let{className:l,...i}=e;return(0,t.jsx)(a.VY,{ref:s,className:(0,r.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",l),...i})});o.displayName=a.VY.displayName},66227:function(e,s,l){"use strict";l.d(s,{$C:function(){return N},yL:function(){return f},yn:function(){return g}});var t=l(57437),i=l(11005),a=l(33149),r=l(76035),n=l(49100),c=l(40064),d=l(43193),o=l(36141),m=l(33907),x=l(47390),h=l(73347),u=l(24258),p=l(5891),j=l(10883),v=l(66648);let g=[{href:"#",icon:(0,t.jsx)(v.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,t.jsx)(i.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:(0,t.jsx)(a.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:(0,t.jsx)(r.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:(0,t.jsx)(n.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:(0,t.jsx)(c.Z,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:(0,t.jsx)(d.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:(0,t.jsx)(o.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:(0,t.jsx)(m.Z,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:(0,t.jsx)(x.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,t.jsx)(h.Z,{className:"h-5 w-5"}),label:"Notes"}],N=[{href:"/freelancer/settings/personal-info",icon:(0,t.jsx)(u.Z,{className:"h-5 w-5"}),label:"Settings"}];v.default,i.Z,h.Z,p.Z,j.Z;let f=[{href:"#",icon:(0,t.jsx)(v.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,t.jsx)(i.Z,{className:"h-5 w-5"}),label:"Home"}]}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2636,2455,9726,2688,2971,7023,1744],function(){return e(e.s=96529)}),_N_E=e.O()}]);