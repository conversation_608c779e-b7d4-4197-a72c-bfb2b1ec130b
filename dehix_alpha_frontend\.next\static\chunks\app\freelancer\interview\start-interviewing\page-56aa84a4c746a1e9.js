(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1783],{93515:function(e,r,t){Promise.resolve().then(t.bind(t,26744))},25912:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},26744:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return l}});var n=t(57437);t(2265);var a=t(64797),s=t(59282),i=t(62688);function l(){return(0,n.jsxs)("div",{className:"flex min-h-screen w-full  bg-muted/40",children:[(0,n.jsx)(a.Z,{menuItemsTop:s.y,menuItemsBottom:s.$,active:"project"}),(0,n.jsxs)("div",{className:"flex mb-8 flex-col sm:pl-14 w-full",children:[(0,n.jsx)(i.Z,{menuItemsTop:s.y,menuItemsBottom:s.$,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Interview",link:"#"},{label:"start-interviewing",link:"#"}]}),(0,n.jsx)("div",{children:"TODO"})]})]})}},59282:function(e,r,t){"use strict";t.d(r,{$:function(){return d},y:function(){return u}});var n=t(57437),a=t(11005),s=t(38133),i=t(33480);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,i.Z)("ListVideo",[["path",{d:"M12 12H3",key:"18klou"}],["path",{d:"M16 6H3",key:"1wxfjs"}],["path",{d:"M12 18H3",key:"11ftsu"}],["path",{d:"m16 12 5 3-5 3v-6Z",key:"zpskkp"}]]);var c=t(25912);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let h=(0,i.Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var o=t(24258),f=t(66648);let u=[{href:"#",icon:(0,n.jsx)(f.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,n.jsx)(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/interview/profile",icon:(0,n.jsx)(s.Z,{className:"h-5 w-5"}),label:"Profile"},{href:"/freelancer/interview/current",icon:(0,n.jsx)(l,{className:"h-5 w-5"}),label:"Current"},{href:"/freelancer/interview/bids",icon:(0,n.jsx)(c.Z,{className:"h-5 w-5"}),label:"Bids"},{href:"/freelancer/interview/history",icon:(0,n.jsx)(h,{className:"h-5 w-5"}),label:"History"}],d=[{href:"/freelancer/settings/personal-info",icon:(0,n.jsx)(o.Z,{className:"h-5 w-5"}),label:"Settings"}]}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2455,9726,2688,2971,7023,1744],function(){return e(e.s=93515)}),_N_E=e.O()}]);