(()=>{var e={};e.id=9406,e.ids=[9406],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},16847:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>o}),s(92161),s(54302),s(12523);var r=s(23191),i=s(88716),a=s(37922),n=s.n(a),c=s(95231),l={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);s.d(t,l);let o=["",{children:["freelancer",{children:["oracleDashboard",{children:["projectVerification",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,92161)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\oracleDashboard\\projectVerification\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\oracleDashboard\\projectVerification\\page.tsx"],x="/freelancer/oracleDashboard/projectVerification/page",m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/freelancer/oracleDashboard/projectVerification/page",pathname:"/freelancer/oracleDashboard/projectVerification",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},3201:(e,t,s)=>{Promise.resolve().then(s.bind(s,18171))},12893:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},5932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},18171:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>V});var r=s(10326),i=s(41137),a=s(23015),n=s(17577),c=s(91664),l=s(40588),o=s(24118),d=s(2822),x=s(92166),m=s(34270),u=s(6260),p=s(12893),h=s(5932),f=s(40617),j=s(74064),v=s(74723),g=s(27256),y=s(29752),b=s(38443),N=s(9969),_=s(82631),w=s(82015),q=s(56627);let D=g.z.object({type:g.z.enum(["Approved","Denied"],{required_error:"You need to select a type."}),comment:g.z.string().optional()}),P=({_id:e,projectName:t,description:s,githubLink:i,startFrom:a,endTo:l,reference:o,techUsed:x,comments:m,status:g,role:P,projectType:C,onStatusUpdate:V,onCommentUpdate:S})=>{let[k,Z]=(0,n.useState)(g),A=(0,v.cI)({resolver:(0,j.F)(D)}),z=A.watch("type");async function E(t){try{await u.b.put(`/verification/${e}/oracle?doc_type=project`,{comments:t.comment,verification_status:t.type})}catch(e){(0,q.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}Z(t.type),V(t.type),S(t.comment||"")}return(0,n.useEffect)(()=>{Z(g)},[g]),(0,r.jsxs)(y.Zb,{className:"min-w-[90vw] mx-auto md:min-w-[30vw] md:min-h-[65vh]",children:[(0,r.jsxs)(y.Ol,{children:[(0,r.jsxs)(y.ll,{className:"flex justify-between",children:[r.jsx("span",{children:t}),i&&r.jsx("div",{className:"ml-auto",children:r.jsx("a",{href:i,className:"text-sm text-white underline",children:r.jsx(p.Z,{})})})]}),(0,r.jsxs)(y.SZ,{className:"mt-1 text-justify text-gray-600",children:["Pending"===k||"added"===k||"reapplied"===k?r.jsx(b.C,{className:"bg-warning-foreground text-white my-2",children:k}):"Approved"===k||"Verified"===k||"verified"===k?r.jsx(b.C,{className:"bg-success text-white my-2",children:k}):r.jsx(b.C,{className:"bg-red-500 text-white my-2",children:"Denied"}),r.jsx("br",{}),s]})]}),r.jsx(y.aY,{children:(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsxs)("div",{className:"mt-2",children:[r.jsx("span",{className:"font-semibold",children:"Tech Used:"}),r.jsx("div",{className:"flex flex-wrap gap-2 mt-1",children:(x||[]).map((e,t)=>r.jsx(b.C,{className:"uppercase",variant:"secondary",children:e},t))})]}),r.jsx("div",{className:"mt-3",children:(0,r.jsxs)("p",{className:"text-m text-gray-600 flex items-center",children:["Role: ",P]})}),r.jsx("div",{className:"mt-3",children:(0,r.jsxs)("p",{className:"text-m text-gray-600 flex items-center",children:["Project Type:",C]})}),r.jsx("div",{className:"mt-4",children:(0,r.jsxs)(_.u,{children:[r.jsx(_.aJ,{asChild:!0,children:(0,r.jsxs)("p",{className:"text-sm text-gray-600 flex items-center",children:[r.jsx(h.Z,{className:"mr-2"}),o]})}),r.jsx(_._v,{side:"bottom",children:o})]})}),m&&(0,r.jsxs)("p",{className:"mt-2 flex items-center text-gray-500 border p-3 rounded",children:[r.jsx(f.Z,{className:"mr-2"}),m]})]})}),(0,r.jsxs)(y.eW,{className:"flex flex-col items-center",children:[(0,r.jsxs)("div",{className:"flex gap-4 text-gray-500",children:[new Date(a).toLocaleDateString()," -"," ","current"!==l?new Date(l).toLocaleDateString():"Current"]}),("Pending"===k||"added"===k||"reapplied"===k)&&r.jsx(N.l0,{...A,children:(0,r.jsxs)("form",{onSubmit:A.handleSubmit(E),className:"w-full space-y-6 mt-6",children:[r.jsx(N.Wi,{control:A.control,name:"type",render:({field:e})=>(0,r.jsxs)(N.xJ,{className:"space-y-3",children:[r.jsx(N.lX,{children:"Choose Verification Status:"}),r.jsx(N.NI,{children:(0,r.jsxs)(d.E,{onValueChange:e.onChange,defaultValue:e.value,className:"flex flex-col space-y-1",children:[(0,r.jsxs)(N.xJ,{className:"flex items-center space-x-3",children:[r.jsx(N.NI,{children:r.jsx(d.m,{value:"Approved"})}),r.jsx(N.lX,{className:"font-normal",children:"Approved"})]}),(0,r.jsxs)(N.xJ,{className:"flex items-center space-x-3",children:[r.jsx(N.NI,{children:r.jsx(d.m,{value:"Denied"})}),r.jsx(N.lX,{className:"font-normal",children:"Denied"})]})]})}),r.jsx(N.zG,{})]})}),r.jsx(N.Wi,{control:A.control,name:"comment",render:({field:e})=>(0,r.jsxs)(N.xJ,{children:[r.jsx(N.lX,{children:"Comments:"}),r.jsx(N.NI,{children:r.jsx(w.g,{placeholder:"Enter comments:",...e})}),r.jsx(N.zG,{})]})}),r.jsx(c.z,{type:"submit",className:"w-full",disabled:!z||A.formState.isSubmitting,children:"Submit"})]})})]})]})};var C=s(39958);function V(){let[e,t]=(0,n.useState)([]),[s,p]=(0,n.useState)("all"),[h,f]=(0,n.useState)(!1),j=e=>{p(e),f(!1)},v=e.filter(e=>"all"===s||e.verificationStatus===s||"current"===s&&e.verificationStatus===C.sB.PENDING);(0,n.useCallback)(async()=>{try{let e=(await u.b.get("/verification/oracle?doc_type=project")).data.data.flatMap(e=>e.result?.projects?Object.values(e.result.projects).map(t=>({...t,verifier_id:e.verifier_id,verifier_username:e.verifier_username})):[]);t(e)}catch(e){(0,q.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."}),console.log(e,"error in getting verification data")}},[]);let g=(s,r)=>{let i=[...e];i[s].verificationStatus=r,t(i)},y=(s,r)=>{let i=[...e];i[s].comments=r,t(i)};return(0,r.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[r.jsx(x.Z,{menuItemsTop:m.y,menuItemsBottom:m.$,active:"Project Verification"}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[r.jsx(l.Z,{menuItemsTop:m.y,menuItemsBottom:m.$,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Oracle",link:"#"},{label:"Project Verification",link:"#"}]}),(0,r.jsxs)("div",{className:"mb-8 ml-4 flex justify-between mt-8 md:mt-4 items-center",children:[(0,r.jsxs)("div",{className:"mb-8 ",children:[r.jsx("h1",{className:"text-3xl font-bold",children:"Project Verification"}),r.jsx("p",{className:"text-gray-400 mt-2",children:"Monitor the status of your project verifications."})]}),r.jsx(c.z,{variant:"outline",size:"icon",className:"mr-8 mb-12",onClick:()=>f(!0),children:r.jsx(i.Z,{className:"h-4 w-4"})})]}),r.jsx(o.Vq,{open:h,onOpenChange:f,children:(0,r.jsxs)(o.cZ,{children:[r.jsx(o.fK,{children:r.jsx(o.$N,{children:"Filter Project Status"})}),(0,r.jsxs)(d.E,{defaultValue:"all",value:s,onValueChange:e=>j(e),className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(d.m,{value:"all",id:"filter-all"}),r.jsx("label",{htmlFor:"filter-all",children:"All"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(d.m,{value:"current",id:"filter-current"}),r.jsx("label",{htmlFor:"filter-current",children:"Pending"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(d.m,{value:"verified",id:"filter-verified"}),r.jsx("label",{htmlFor:"filter-verified",children:"Verified"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(d.m,{value:"rejected",id:"filter-rejected"}),r.jsx("label",{htmlFor:"filter-rejected",children:"Rejected"})]})]}),r.jsx(o.cN,{children:r.jsx(c.z,{type:"button",onClick:()=>f(!1),children:"Close"})})]})}),(0,r.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8 grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3",children:[v.map((e,t)=>r.jsx(P,{_id:e._id,projectName:e.projectName,description:e.description,githubLink:e.githubLink,startFrom:e.start,endTo:e.end,role:e.role,projectType:e.projectType,reference:e.refer,techUsed:e.techUsed,comments:e.comments,status:e.verificationStatus,onStatusUpdate:e=>g(t,e),onCommentUpdate:e=>y(t,e)},t)),0===e.length?(0,r.jsxs)("div",{className:"text-center w-[90vw] px-auto mt-20 py-10",children:[r.jsx(a.Z,{className:"mx-auto text-gray-500",size:"100"}),r.jsx("p",{className:"text-gray-500",children:"No Project verification for you now."})]}):null]})]})]})}},92161:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>c});var r=s(68570);let i=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\oracleDashboard\projectVerification\page.tsx`),{__esModule:a,$$typeof:n}=i;i.default;let c=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\oracleDashboard\projectVerification\page.tsx#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,6686,4736,6499,8066,588,3379],()=>s(16847));module.exports=r})();