(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3570],{23513:function(e,t,r){Promise.resolve().then(r.bind(r,14236))},5891:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},20897:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},13231:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},71935:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},47390:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},59061:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},98960:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},73347:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},10883:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},14236:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return X}});var s,a,n=r(57437),l=r(64797),i=r(2265),o=r(31014),c=r(39343),d=r(59772),u=r(92513),m=r(74697),h=r(59061),x=r(11444),p=r(48185),f=r(86074),j=r(55384),g=r(80023),b=r(49354),v=r(89733),y=r(93363),N=r(77209),I=r(2128),C=r(4919),k=r(78068),w=r(15922),D=r(79055),E=r(9437),S=r(97694),z=r(20357);let R=d.z.object({projectName:d.z.string().min(2,{message:"Project Name must be at least 2 characters."}),email:d.z.string({required_error:"Email is required."}).email({message:"Please enter a valid email address."}),projectDomain:d.z.array(d.z.string().min(1,{message:"Project domain cannot be empty."})).min(1,{message:"At least one project domain is required."}),urls:d.z.array(d.z.object({value:d.z.string().url({message:"Please enter a valid URL."})})).optional(),description:d.z.string().min(4,{message:"Description must be at least 4 characters long."}).max(160,{message:"Description cannot exceed 160 characters."}).optional(),budget:d.z.object({type:d.z.enum(["FIXED","HOURLY"]),fixedAmount:d.z.string().optional(),hourly:d.z.object({minRate:d.z.string().optional(),maxRate:d.z.string().optional(),estimatedHours:d.z.string().optional()}).optional()}).superRefine((e,t)=>{if("FIXED"===e.type&&(e.fixedAmount?(!/^\d+(\.\d{1,2})?$/.test(e.fixedAmount)||0>=parseFloat(e.fixedAmount))&&t.addIssue({path:["fixedAmount"],code:d.z.ZodIssueCode.custom,message:"Enter a valid amount greater than 0"}):t.addIssue({path:["fixedAmount"],code:d.z.ZodIssueCode.custom,message:"Fixed amount is required"})),"HOURLY"===e.type){if(!e.hourly){t.addIssue({path:["hourly"],code:d.z.ZodIssueCode.custom,message:"Hourly details are required"});return}let{minRate:r,maxRate:s,estimatedHours:a}=e.hourly;if(r?(!/^\d+(\.\d{1,2})?$/.test(r)||0>=parseFloat(r))&&t.addIssue({path:["hourly","minRate"],code:d.z.ZodIssueCode.custom,message:"Enter a valid minimum rate > 0"}):t.addIssue({path:["hourly","minRate"],code:d.z.ZodIssueCode.custom,message:"Minimum rate is required"}),s?(!/^\d+(\.\d{1,2})?$/.test(s)||0>=parseFloat(s))&&t.addIssue({path:["hourly","maxRate"],code:d.z.ZodIssueCode.custom,message:"Enter a valid maximum rate > 0"}):t.addIssue({path:["hourly","maxRate"],code:d.z.ZodIssueCode.custom,message:"Maximum rate is required"}),a?(!/^\d+$/.test(a)||0>=parseInt(a))&&t.addIssue({path:["hourly","estimatedHours"],code:d.z.ZodIssueCode.custom,message:"Enter a valid number of hours > 0"}):t.addIssue({path:["hourly","estimatedHours"],code:d.z.ZodIssueCode.custom,message:"Estimated hours are required"}),r&&s&&/^\d+(\.\d{1,2})?$/.test(r)&&/^\d+(\.\d{1,2})?$/.test(s)){let e=parseFloat(r);parseFloat(s)<e&&t.addIssue({path:["hourly","maxRate"],code:d.z.ZodIssueCode.custom,message:"Maximum rate must be ≥ minimum rate"})}}}),profiles:d.z.array(d.z.object({domain:d.z.string().min(1,{message:"Domain is required."}),freelancersRequired:d.z.string().refine(e=>/^\d+$/.test(e)&&parseInt(e,10)>0,{message:"Number of freelancers required should be a positive number."}),skills:d.z.array(d.z.string().min(1,{message:"Skill name cannot be empty."})).min(1,{message:"At least one skill is required."}),experience:d.z.string().refine(e=>/^\d+$/.test(e)&&parseInt(e,10)>=0&&40>=parseInt(e,10),{message:"Experience must be a number between 0 and 40."}),domain_id:d.z.string().min(1,{message:"Domain ID is required."}),minConnect:d.z.string().refine(e=>/^\d+$/.test(e)&&parseInt(e,10)>0,{message:"Minimum connect must be at least 1."}),rate:d.z.string().refine(e=>/^\d+(\.\d{1,2})?$/.test(e)&&parseFloat(e)>=0,{message:"Per hour rate should be a valid non-negative number."}),description:d.z.string().min(4,{message:"Description must be at least 4 characters long."}).max(160,{message:"Description cannot exceed 160 characters."})})).optional()}),A={projectName:"",email:"",projectDomain:[],urls:[],description:"",budget:{type:"FIXED",fixedAmount:"",hourly:{minRate:"",maxRate:"",estimatedHours:""}},profiles:[{domain:"",freelancersRequired:"",skills:[],experience:"",minConnect:"",rate:"",description:"",domain_id:""}]},P="DEHIX-BUSINESS-DRAFT";function Z(){let e=(0,x.v9)(e=>e.user),[t,r]=(0,i.useState)([]),[s,a]=(0,i.useState)([]),[l,d]=(0,i.useState)(""),[Z,F]=(0,i.useState)([]),[O,X]=(0,i.useState)([]),[H,T]=(0,i.useState)([]),[_,q]=(0,i.useState)(""),[M,V]=(0,i.useState)(!1),[J,L]=(0,i.useState)(!1),[G,W]=(0,i.useState)("ProjectInfo"),[B,$]=(0,i.useState)(0),{hasOtherValues:U,hasProfiles:Y}=(0,E.Z)({}),Q=(0,c.cI)({resolver:(0,o.F)(R),defaultValues:A,mode:"onChange"}),{fields:K,append:ee}=(0,c.Dq)({name:"urls",control:Q.control}),{fields:et,append:er,remove:es}=(0,c.Dq)({name:"profiles",control:Q.control});(0,i.useEffect)(()=>{let e=localStorage.getItem(P);if(e){let t=JSON.parse(e);(U(t)||Y(t.profiles))&&L(!0)}},[Y,U]),(0,i.useEffect)(()=>{(async()=>{try{let[e,t,s]=await Promise.all([w.b.get("/projectdomain"),w.b.get("/domain"),w.b.get("/skills")]);X(e.data.data.map(e=>({value:e.label,label:e.label}))),F(t.data.data.map(e=>({value:e.label,label:e.label,domain_id:e._id}))),r(s.data.data.map(e=>({value:e.label,label:e.label})))}catch(e){(0,k.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}})()},[]);let ea=()=>{if(_&&!H.includes(_)){let e=[...H,_];T(e),q(""),Q.setValue("projectDomain",e)}},en=e=>{let t=H.filter(t=>t!==e);T(t),Q.setValue("projectDomain",t)},el=e=>{""!==l.trim()&&(a(t=>{let r=[...t];return r[e]||(r[e]=[]),r[e].includes(l)||r[e].push(l),Q.setValue("profiles.".concat(e,".skills"),r[e]),r}),d(""))},ei=(e,t)=>{a(r=>{let s=[...r];return s[e]&&(s[e]=s[e].filter(e=>e!==t),Q.setValue("profiles.".concat(e,".skills"),s[e])),s})},eo=e=>"FIXED"===e.type?{type:"FIXED",fixedAmount:parseFloat(e.fixedAmount)}:{type:"HOURLY",hourly:{minRate:parseFloat(e.hourly.minRate),maxRate:parseFloat(e.hourly.maxRate),estimatedHours:parseInt(e.hourly.estimatedHours)}};async function ec(t){V(!0);try{let r=Array.from(new Set(s.flat().filter(Boolean))),a=eo(t.budget),n={projectName:t.projectName,description:t.description,email:t.email,companyId:e.uid,companyName:e.displayName,skillsRequired:r,projectDomain:H,role:"",projectType:"FREELANCE",url:t.urls,budget:a,profiles:t.profiles||[]};await w.b.post("/project/business",n),(0,k.Am)({title:"Project Added",description:"Your project has been successfully added."});let l=parseInt(z.env.NEXT_PUBLIC__APP_PROJECT_CREATION_COST||"0",10),i=(parseInt(localStorage.getItem("DHX_CONNECTS")||"0")-l).toString();localStorage.setItem("DHX_CONNECTS",i),localStorage.removeItem(P),window.dispatchEvent(new Event("connectsUpdated"))}catch(e){(0,k.Am)({variant:"destructive",title:"Error",description:"Failed to add project. Please try again later."})}finally{V(!1)}Q.reset(A),T([]),a([])}let ed=async()=>{await Q.trigger(["urls","projectDomain","description","email","projectName","budget"])&&"ProjectInfo"===G&&W("ProfileInfo")},eu=e=>{1!==et.length&&(Q.unregister("profiles.".concat(e)),B>=e&&$(e=>Math.max(0,e-1)))};return(0,n.jsxs)(p.Zb,{className:"p-10",children:[(0,n.jsx)(y.l0,{...Q,children:(0,n.jsxs)("form",{onSubmit:Q.handleSubmit(ec),className:"gap-5 lg:grid lg:grid-cols-2 xl:grid-cols-2",children:["ProjectInfo"===G&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(y.Wi,{control:Q.control,name:"projectName",render:e=>{let{field:t}=e;return(0,n.jsxs)(y.xJ,{children:[(0,n.jsx)(y.lX,{children:"Project Name"}),(0,n.jsx)(y.NI,{children:(0,n.jsx)(N.I,{placeholder:"Enter your Project Name",...t})}),(0,n.jsx)(y.pf,{children:"Enter your Project name"}),(0,n.jsx)(y.zG,{})]})}}),(0,n.jsx)(y.Wi,{control:Q.control,name:"email",render:e=>{let{field:t}=e;return(0,n.jsxs)(y.xJ,{children:[(0,n.jsx)(y.lX,{children:"Contact Email"}),(0,n.jsx)(y.NI,{children:(0,n.jsx)(N.I,{placeholder:"Enter your email",...t})}),(0,n.jsx)(y.pf,{children:"Enter your email"}),(0,n.jsx)(y.zG,{})]})}}),(0,n.jsx)(y.Wi,{control:Q.control,name:"projectDomain",render:()=>(0,n.jsxs)(y.xJ,{className:"col-span-2",children:[(0,n.jsx)(y.lX,{children:"Project Domain"}),(0,n.jsx)(y.NI,{children:(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex items-center mt-2",children:[(0,n.jsxs)(I.Ph,{onValueChange:q,value:_||"",children:[(0,n.jsx)(I.i4,{children:(0,n.jsx)(I.ki,{placeholder:_||"Select project domain"})}),(0,n.jsx)(I.Bw,{children:O.filter(e=>!H.includes(e.label)).map((e,t)=>(0,n.jsx)(I.Ql,{value:e.label,children:e.label},t))})]}),(0,n.jsx)(v.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",onClick:ea,children:(0,n.jsx)(u.Z,{className:"h-4 w-4"})})]}),(0,n.jsx)("div",{className:"flex flex-wrap mt-5",children:H.map((e,t)=>(0,n.jsxs)(D.C,{className:"uppercase mx-1 text-xs font-normal bg-gray-400 flex items-center",children:[e,(0,n.jsx)("button",{type:"button",onClick:()=>en(e),className:"ml-2 text-red-500 hover:text-red-700",children:(0,n.jsx)(m.Z,{className:"h-4 w-4"})})]},t))})]})}),(0,n.jsx)(y.zG,{})]})}),(0,n.jsx)(y.Wi,{control:Q.control,name:"description",render:e=>{let{field:t}=e;return(0,n.jsxs)(y.xJ,{className:"col-span-2",children:[(0,n.jsx)(y.lX,{children:"Profile Description"}),(0,n.jsx)(y.NI,{children:(0,n.jsx)(C.g,{placeholder:"Enter description",...t})}),(0,n.jsx)(y.zG,{})]})}}),(()=>{var e;let t=Q.watch("budget.type");return(0,n.jsxs)("div",{className:"lg:col-span-2 xl:col-span-2 border p-4 rounded-md mb-4",children:[(0,n.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Project Budget"}),(0,n.jsx)(y.Wi,{control:Q.control,name:"budget.type",render:e=>{let{field:t}=e;return(0,n.jsxs)(y.xJ,{className:"mb-6",children:[(0,n.jsx)(y.lX,{children:"Budget Type"}),(0,n.jsx)(y.NI,{children:(0,n.jsxs)(S.E,{onValueChange:t.onChange,defaultValue:t.value,className:"flex flex-col space-y-1",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(S.m,{value:"FIXED",id:"fixed"}),(0,n.jsx)("label",{htmlFor:"fixed",className:"cursor-pointer",children:"Fixed Price"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(S.m,{value:"HOURLY",id:"hourly"}),(0,n.jsx)("label",{htmlFor:"hourly",className:"cursor-pointer",children:"Hourly Rate"})]})]})}),(0,n.jsx)(y.zG,{})]})}}),"FIXED"===t&&(0,n.jsx)(y.Wi,{control:Q.control,name:"budget.fixedAmount",render:e=>{let{field:t}=e;return(0,n.jsxs)(y.xJ,{children:[(0,n.jsx)(y.lX,{children:"Fixed Budget Amount ($)"}),(0,n.jsx)(y.NI,{children:(0,n.jsx)(N.I,{type:"number",placeholder:"Enter fixed amount",min:"1",step:"0.01",...t})}),(0,n.jsx)(y.pf,{children:"Enter the total fixed price for the project"}),(0,n.jsx)(y.zG,{})]})}}),"HOURLY"===t&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsx)(y.Wi,{control:Q.control,name:"budget.hourly.minRate",render:e=>{let{field:t}=e;return(0,n.jsxs)(y.xJ,{children:[(0,n.jsx)(y.lX,{children:"Minimum Rate ($/hour)"}),(0,n.jsx)(y.NI,{children:(0,n.jsx)(N.I,{type:"number",placeholder:"Min rate",min:"1",step:"0.01",...t})}),(0,n.jsx)(y.zG,{})]})}}),(0,n.jsx)(y.Wi,{control:Q.control,name:"budget.hourly.maxRate",render:e=>{let{field:t}=e;return(0,n.jsxs)(y.xJ,{children:[(0,n.jsx)(y.lX,{children:"Maximum Rate ($/hour)"}),(0,n.jsx)(y.NI,{children:(0,n.jsx)(N.I,{type:"number",placeholder:"Max rate",min:"1",step:"0.01",...t})}),(0,n.jsx)(y.zG,{})]})}})]}),(0,n.jsx)(y.Wi,{control:Q.control,name:"budget.hourly.estimatedHours",render:e=>{let{field:t}=e;return(0,n.jsxs)(y.xJ,{className:"mt-4",children:[(0,n.jsx)(y.lX,{children:"Estimated Hours"}),(0,n.jsx)(y.NI,{children:(0,n.jsx)(N.I,{type:"number",placeholder:"Estimated number of hours",min:"1",...t})}),(0,n.jsx)(y.pf,{children:"Estimated total hours required for project completion"}),(0,n.jsx)(y.zG,{})]})}})]}),(null===(e=Q.formState.errors.budget)||void 0===e?void 0:e.message)&&(0,n.jsx)("p",{className:"text-sm text-red-600 mt-4",children:Q.formState.errors.budget.message})]})})(),(0,n.jsxs)("div",{className:"lg:col-span-2 xl:col-span-2",children:[K.map((e,t)=>(0,n.jsx)(y.Wi,{control:Q.control,name:"urls.".concat(t,".value"),render:e=>{let{field:r}=e;return(0,n.jsx)(y.xJ,{className:"flex items-center gap-2",children:(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)(y.lX,{className:(0,b.cn)(0!==t&&"sr-only"),children:"URLs"}),(0,n.jsx)(y.pf,{className:"".concat(0!==t?"sr-only":""," mb-2"),children:"Enter URL of your account"}),(0,n.jsx)(y.NI,{children:(0,n.jsxs)("div",{className:"flex justify-center items-center  gap-3 mb-2",children:[(0,n.jsx)(N.I,{...r}),(0,n.jsx)(v.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",onClick:()=>Q.setValue("urls",(Q.getValues("urls")||[]).filter((e,r)=>r!==t)),children:(0,n.jsx)(m.Z,{className:"h-4 w-4"})})]})}),(0,n.jsx)(y.zG,{className:"my-2.5"})]})})}},e.id)),(0,n.jsx)(v.z,{className:"mt-2",type:"button",variant:"outline",size:"sm",onClick:()=>ee({value:""}),children:"Add URL"})]})]}),"ProfileInfo"===G&&(0,n.jsxs)("div",{className:"lg:col-span-2 xl:col-span-2",children:[(0,n.jsx)("div",{className:"my-4",children:(0,n.jsx)(()=>(0,n.jsxs)(g.x,{children:[(0,n.jsx)("div",{className:"flex gap-2 mb-4",children:et.map((e,t)=>(0,n.jsxs)(v.z,{type:"button",size:"sm",variant:B===t?"default":"outline",onClick:()=>$(t),className:"px-4 py-2 ".concat(B===t?"bg-blue-600 text-white hover:text-black":""),children:["Profile ",t+1]},t))}),(0,n.jsx)(g.B,{orientation:"horizontal"})]}),{})}),et.map((e,r)=>r===B?(0,n.jsxs)("div",{className:"border p-4 mb-4 rounded-md relative",children:[(0,n.jsx)(m.Z,{onClick:()=>eu(r),className:"w-5 hover:text-red-600 h-5 absolute right-2 top-1 cursor-pointer"}),(0,n.jsx)(y.Wi,{control:Q.control,name:"profiles.".concat(r,".domain"),render:e=>{let{field:t}=e;return(0,n.jsxs)(y.xJ,{className:"mb-4",children:[(0,n.jsx)(y.lX,{children:"Profile Domain"}),(0,n.jsx)(y.NI,{children:(0,n.jsxs)(I.Ph,{onValueChange:e=>{let t=Z.find(t=>t.label===e);Q.setValue("profiles.".concat(r,".domain"),(null==t?void 0:t.label)||""),Q.setValue("profiles.".concat(r,".domain_id"),(null==t?void 0:t.domain_id)||"")},defaultValue:t.value,children:[(0,n.jsx)(I.i4,{children:(0,n.jsx)(I.ki,{placeholder:"Select domain"})}),(0,n.jsx)(I.Bw,{children:Z.map((e,t)=>(0,n.jsx)(I.Ql,{value:e.label,children:e.label},t))})]})}),(0,n.jsx)(y.zG,{})]})}}),(0,n.jsx)(y.Wi,{control:Q.control,name:"profiles.".concat(r,".freelancersRequired"),render:e=>{let{field:t}=e;return(0,n.jsxs)(y.xJ,{className:"mb-4",children:[(0,n.jsx)(y.lX,{children:"Number of Freelancers Required"}),(0,n.jsx)(y.NI,{children:(0,n.jsx)(N.I,{type:"number",placeholder:"Enter number",min:1,...t})}),(0,n.jsx)(y.zG,{})]})}}),(0,n.jsx)(y.Wi,{control:Q.control,name:"profiles.".concat(r,".skills"),render:()=>{var e;return(0,n.jsxs)(y.xJ,{className:"mb-4",children:[(0,n.jsx)(y.lX,{children:"Skills"}),(0,n.jsx)(y.NI,{children:(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex items-center mt-2",children:[(0,n.jsxs)(I.Ph,{onValueChange:d,value:l||"",children:[(0,n.jsx)(I.i4,{children:(0,n.jsx)(I.ki,{placeholder:"Select skill"})}),(0,n.jsx)(I.Bw,{children:t.map((e,t)=>(0,n.jsx)(I.Ql,{value:e.label,children:e.label},t))})]}),(0,n.jsx)(v.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",onClick:()=>el(r),children:(0,n.jsx)(u.Z,{className:"h-4 w-4"})})]}),(0,n.jsx)("div",{className:"flex flex-wrap mt-5",children:null===(e=s[r])||void 0===e?void 0:e.map((e,t)=>(0,n.jsxs)(D.C,{className:"uppercase mx-1 text-xs font-normal bg-gray-400 flex items-center",children:[e,(0,n.jsx)("button",{type:"button",onClick:()=>ei(r,e),className:"ml-2 text-red-500 hover:text-red-700",children:(0,n.jsx)(m.Z,{className:"h-4 w-4"})})]},t))})]})}),(0,n.jsx)(y.zG,{})]})}}),(0,n.jsx)(y.Wi,{control:Q.control,name:"profiles.".concat(r,".experience"),render:e=>{let{field:t}=e;return(0,n.jsxs)(y.xJ,{className:"mb-4",children:[(0,n.jsx)(y.lX,{children:"Experience"}),(0,n.jsx)(y.NI,{children:(0,n.jsx)(N.I,{type:"number",placeholder:"Enter experience",min:0,max:60,...t})}),(0,n.jsx)(y.zG,{})]})}}),(0,n.jsx)(y.Wi,{control:Q.control,name:"profiles.".concat(r,".minConnect"),render:e=>{let{field:t}=e;return(0,n.jsxs)(y.xJ,{className:"mb-4",children:[(0,n.jsx)(y.lX,{children:"Min Connect"}),(0,n.jsx)(y.pf,{children:"Minimum number of connects for the project"}),(0,n.jsx)(y.NI,{children:(0,n.jsx)(N.I,{placeholder:"Enter Min Connects (Recommended: 10)",...t})}),(0,n.jsx)(y.zG,{})]})}}),(0,n.jsx)(y.Wi,{control:Q.control,name:"profiles.".concat(r,".rate"),render:e=>{let{field:t}=e;return(0,n.jsxs)(y.xJ,{className:"mb-4",children:[(0,n.jsx)(y.lX,{children:"Per Hour Rate"}),(0,n.jsx)(y.NI,{children:(0,n.jsx)(N.I,{type:"number",placeholder:"Enter rate",min:0,max:200,...t})}),(0,n.jsx)(y.zG,{})]})}}),(0,n.jsx)(y.Wi,{control:Q.control,name:"profiles.".concat(r,".description"),render:e=>{let{field:t}=e;return(0,n.jsxs)(y.xJ,{className:"mb-4",children:[(0,n.jsx)(y.lX,{children:"Description"}),(0,n.jsx)(y.NI,{children:(0,n.jsx)(C.g,{placeholder:"Enter description",...t})}),(0,n.jsx)(y.zG,{})]})}}),(0,n.jsx)(v.z,{type:"button",variant:"outline",size:"sm",className:"mt-2",onClick:()=>es(r),children:"Remove Profile"})]},r):null),(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsx)(v.z,{type:"button",variant:"outline",size:"sm",className:"mt-2",onClick:()=>er({domain:"",freelancersRequired:"",skills:[],experience:"",minConnect:"",rate:"",description:"",domain_id:""}),children:"Add Profile"}),(0,n.jsx)(v.z,{type:"button",size:"sm",variant:"outline",onClick:()=>{var e;let t=Q.getValues(),r=U(t),a=Y(t.profiles);if(!r&&!a){(0,k.Am)({variant:"destructive",title:"Empty Draft",description:"Cannot save an empty draft."});return}let n=null===(e=t.profiles)||void 0===e?void 0:e.map((e,t)=>({...e,skills:Array.isArray(s[t])?s[t]:[]})),l={...t,profiles:n};localStorage.setItem(P,JSON.stringify(l)),(0,k.Am)({title:"Draft Saved",description:"Your form data has been saved as a draft."})},children:(0,n.jsx)(h.Z,{})})]}),(0,n.jsx)("div",{className:"lg:col-span-2 xl:col-span-2 mt-4",children:(0,n.jsx)(f.Z,{loading:M,isValidCheck:Q.trigger,onSubmit:Q.handleSubmit(ec),setLoading:V,userId:e.uid,buttonText:"Create Project",userType:"BUSINESS",requiredConnects:parseInt(z.env.NEXT_PUBLIC__APP_PROJECT_CREATION_COST||"0",10)})})]}),(0,n.jsx)("div",{className:"w-full mt-4 flex col-span-2 justify-end",children:"ProjectInfo"===G&&(0,n.jsx)(v.z,{type:"button",variant:"outline",onClick:ed,children:"Next"})}),(0,n.jsx)("div",{className:"w-full mt-4 flex col-span-2 justify-start",children:"ProfileInfo"===G&&(0,n.jsx)(v.z,{type:"button",variant:"outline",onClick:()=>{"ProfileInfo"===G&&W("ProjectInfo")},children:"Prev"})})]})}),J&&(0,n.jsx)(j.Z,{dialogChange:J,setDialogChange:L,heading:"Load Draft?",desc:"A saved draft was found. Do you want to load it?",handleClose:()=>{localStorage.removeItem(P),L(!1),(0,k.Am)({title:"Draft discarded",description:"Your saved draft has been discarded."})},handleSave:()=>{let e=localStorage.getItem(P);if(e)try{var t;let r=JSON.parse(e);Q.reset(r),T(r.projectDomain||[]),a((null===(t=r.profiles)||void 0===t?void 0:t.map(e=>Array.isArray(e.skills)?e.skills:[]))||[]),(0,k.Am)({title:"Draft loaded",description:"Your saved draft has been loaded."})}catch(e){(0,k.Am)({title:"Error loading draft",description:"There was a problem loading your draft.",variant:"destructive"})}L(!1)},btn1Txt:" Discard",btn2Txt:"Load Draft"})]})}(s=a||(a={})).ProjectInfo="ProjectInfo",s.ProfileInfo="ProfileInfo";var F=r(82230),O=r(62688);function X(){return(0,n.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,n.jsx)(l.Z,{menuItemsTop:F.yn,menuItemsBottom:F.$C,active:""}),(0,n.jsxs)("div",{className:"flex flex-col sm:gap-4  sm:pl-14 mb-8",children:[(0,n.jsx)(O.Z,{menuItemsTop:F.yn,menuItemsBottom:F.$C,activeMenu:"",breadcrumbItems:[{label:"Business",link:"/dashboard/business"},{label:"Create Project",link:"#"}]}),(0,n.jsx)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8",children:(0,n.jsx)(Z,{})})]})]})}},86074:function(e,t,r){"use strict";r.d(t,{Z:function(){return d}});var s=r(57437),a=r(2265),n=r(78068),l=r(15922),i=r(89733),o=r(54662),c=r(77209);function d(e){let{loading:t,setLoading:r,onSubmit:d,isValidCheck:u,userId:m,buttonText:h,userType:x,requiredConnects:p,data:f}=e,[j,g]=(0,a.useState)(!1),[b,v]=(0,a.useState)(!1),y=parseInt(localStorage.getItem("DHX_CONNECTS")||"0",10),N=async()=>{try{await l.b.post("/token-request",{userId:m,userType:x,amount:"100",status:"PENDING",dateTime:new Date().toISOString()}),(0,n.Am)({title:"Success!",description:"Request to add connects has been sent.",duration:3e3});let e={userId:m,amount:100,status:"PENDING",dateTime:new Date().toISOString()};window.dispatchEvent(new CustomEvent("newConnectRequest",{detail:e}))}catch(e){console.error("Error requesting more connects:",e.response),(0,n.Am)({variant:"destructive",title:"Error!",description:"Failed to request connects. Try again!",duration:3e3})}},I=async()=>{let e=await u();if(console.log(e),e){if(console.log(p),y<p){v(!0),g(!0);return}v(!1),g(!0)}},C=async()=>{if(console.log(b),!b){r(!0);try{f?await d(f):await d(),g(!1)}catch(e){console.error("Error deducting connects:",e),alert("Failed to deduct connects. Try again!")}finally{r(!1)}}};return(0,s.jsxs)("div",{children:[(0,s.jsx)(i.z,{type:"button",className:"lg:col-span-2 w-full xl:col-span-2 mt-4",disabled:t,onClick:I,children:t?"Loading...":h}),(0,s.jsx)(o.Vq,{open:j,onOpenChange:g,children:(0,s.jsx)(o.cZ,{children:b?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.$N,{children:"Insufficient Connects"}),(0,s.jsxs)(o.Be,{children:["You don't have enough connects to create a project.",(0,s.jsx)("br",{}),"Please"," ",(0,s.jsx)("span",{className:"text-blue-600 font-bold cursor-pointer",onClick:N,children:"Request Connects"})," ","to proceed."]}),(0,s.jsx)(o.cN,{children:(0,s.jsx)(i.z,{variant:"outline",onClick:()=>g(!1),children:"Close"})})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.$N,{children:"Confirm Deduction"}),(0,s.jsx)(c.I,{type:"text",value:p,disabled:!0}),(0,s.jsxs)(o.Be,{children:["Creating this project will deduct"," ",(0,s.jsxs)("span",{className:"font-extrabold",children:[" ",p," connects"]}),". Do you want to proceed?"]}),(0,s.jsxs)(o.cN,{children:[(0,s.jsx)(i.z,{variant:"outline",onClick:()=>g(!1),children:"Cancel"}),(0,s.jsx)(i.z,{onClick:C,disabled:t,children:t?"Processing...":"Confirm"})]})]})})})]})}},55384:function(e,t,r){"use strict";var s=r(57437);r(2265);var a=r(54662),n=r(89733);t.Z=e=>{let{dialogChange:t,setDialogChange:r,heading:l,desc:i,handleClose:o,handleSave:c,btn1Txt:d,btn2Txt:u}=e;return(0,s.jsx)(a.Vq,{open:t,onOpenChange:r,children:(0,s.jsxs)(a.cZ,{children:[(0,s.jsxs)(a.fK,{children:[(0,s.jsx)(a.$N,{children:l}),(0,s.jsx)(a.Be,{children:i})]}),(0,s.jsxs)(a.cN,{children:[(0,s.jsx)(n.z,{variant:"outline",onClick:o,children:d}),(0,s.jsx)(n.z,{onClick:c,children:u})]})]})})}},93363:function(e,t,r){"use strict";r.d(t,{NI:function(){return f},Wi:function(){return u},l0:function(){return c},lX:function(){return p},pf:function(){return j},xJ:function(){return x},zG:function(){return g}});var s=r(57437),a=r(2265),n=r(63355),l=r(39343),i=r(49354),o=r(70402);let c=l.RV,d=a.createContext({}),u=e=>{let{...t}=e;return(0,s.jsx)(d.Provider,{value:{name:t.name},children:(0,s.jsx)(l.Qr,{...t})})},m=()=>{let e=a.useContext(d),t=a.useContext(h),{getFieldState:r,formState:s}=(0,l.Gc)(),n=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...n}},h=a.createContext({}),x=a.forwardRef((e,t)=>{let{className:r,...n}=e,l=a.useId();return(0,s.jsx)(h.Provider,{value:{id:l},children:(0,s.jsx)("div",{ref:t,className:(0,i.cn)("space-y-2",r),...n})})});x.displayName="FormItem";let p=a.forwardRef((e,t)=>{let{className:r,...a}=e,{error:n,formItemId:l}=m();return(0,s.jsx)(o.Label,{ref:t,className:(0,i.cn)(n&&"text-destructive",r),htmlFor:l,...a})});p.displayName="FormLabel";let f=a.forwardRef((e,t)=>{let{...r}=e,{error:a,formItemId:l,formDescriptionId:i,formMessageId:o}=m();return(0,s.jsx)(n.g7,{ref:t,id:l,"aria-describedby":a?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!a,...r})});f.displayName="FormControl";let j=a.forwardRef((e,t)=>{let{className:r,...a}=e,{formDescriptionId:n}=m();return(0,s.jsx)("p",{ref:t,id:n,className:(0,i.cn)("text-sm text-muted-foreground",r),...a})});j.displayName="FormDescription";let g=a.forwardRef((e,t)=>{let{className:r,children:a,...n}=e,{error:l,formMessageId:o}=m(),c=l?String(null==l?void 0:l.message):a;return c?(0,s.jsx)("p",{ref:t,id:o,className:(0,i.cn)("text-sm font-medium text-destructive",r),...n,children:c}):null});g.displayName="FormMessage"},70402:function(e,t,r){"use strict";r.r(t),r.d(t,{Label:function(){return c}});var s=r(57437),a=r(2265),n=r(38364),l=r(12218),i=r(49354);let o=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.f,{ref:t,className:(0,i.cn)(o(),r),...a})});c.displayName=n.f.displayName},97694:function(e,t,r){"use strict";r.d(t,{E:function(){return T},m:function(){return _}});var s=r(57437),a=r(2265),n=r(78149),l=r(1584),i=r(98324),o=r(18676),c=r(53398),d=r(91715),u=r(87513),m=r(75238),h=r(47250),x=r(31383),p="Radio",[f,j]=(0,i.b)(p),[g,b]=f(p),v=a.forwardRef((e,t)=>{let{__scopeRadio:r,name:i,checked:c=!1,required:d,disabled:u,value:m="on",onCheck:h,...x}=e,[p,f]=a.useState(null),j=(0,l.e)(t,e=>f(e)),b=a.useRef(!1),v=!p||!!p.closest("form");return(0,s.jsxs)(g,{scope:r,checked:c,disabled:u,children:[(0,s.jsx)(o.WV.button,{type:"button",role:"radio","aria-checked":c,"data-state":C(c),"data-disabled":u?"":void 0,disabled:u,value:m,...x,ref:j,onClick:(0,n.M)(e.onClick,e=>{c||null==h||h(),v&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})}),v&&(0,s.jsx)(I,{control:p,bubbles:!b.current,name:i,value:m,checked:c,required:d,disabled:u,style:{transform:"translateX(-100%)"}})]})});v.displayName=p;var y="RadioIndicator",N=a.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:a,...n}=e,l=b(y,r);return(0,s.jsx)(x.z,{present:a||l.checked,children:(0,s.jsx)(o.WV.span,{"data-state":C(l.checked),"data-disabled":l.disabled?"":void 0,...n,ref:t})})});N.displayName=y;var I=e=>{let{control:t,checked:r,bubbles:n=!0,...l}=e,i=a.useRef(null),o=(0,h.D)(r),c=(0,m.t)(t);return a.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(o!==r&&t){let s=new Event("click",{bubbles:n});t.call(e,r),e.dispatchEvent(s)}},[o,r,n]),(0,s.jsx)("input",{type:"radio","aria-hidden":!0,defaultChecked:r,...l,tabIndex:-1,ref:i,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function C(e){return e?"checked":"unchecked"}var k=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],w="RadioGroup",[D,E]=(0,i.b)(w,[c.Pc,j]),S=(0,c.Pc)(),z=j(),[R,A]=D(w),P=a.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:a,defaultValue:n,value:l,required:i=!1,disabled:m=!1,orientation:h,dir:x,loop:p=!0,onValueChange:f,...j}=e,g=S(r),b=(0,u.gm)(x),[v,y]=(0,d.T)({prop:l,defaultProp:n,onChange:f});return(0,s.jsx)(R,{scope:r,name:a,required:i,disabled:m,value:v,onValueChange:y,children:(0,s.jsx)(c.fC,{asChild:!0,...g,orientation:h,dir:b,loop:p,children:(0,s.jsx)(o.WV.div,{role:"radiogroup","aria-required":i,"aria-orientation":h,"data-disabled":m?"":void 0,dir:b,...j,ref:t})})})});P.displayName=w;var Z="RadioGroupItem",F=a.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:i,...o}=e,d=A(Z,r),u=d.disabled||i,m=S(r),h=z(r),x=a.useRef(null),p=(0,l.e)(t,x),f=d.value===o.value,j=a.useRef(!1);return a.useEffect(()=>{let e=e=>{k.includes(e.key)&&(j.current=!0)},t=()=>j.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,s.jsx)(c.ck,{asChild:!0,...m,focusable:!u,active:f,children:(0,s.jsx)(v,{disabled:u,required:d.required,checked:f,...h,...o,name:d.name,ref:p,onCheck:()=>d.onValueChange(o.value),onKeyDown:(0,n.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,n.M)(o.onFocus,()=>{var e;j.current&&(null===(e=x.current)||void 0===e||e.click())})})})});F.displayName=Z;var O=a.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...a}=e,n=z(r);return(0,s.jsx)(N,{...n,...a,ref:t})});O.displayName="RadioGroupIndicator";var X=r(28165),H=r(49354);let T=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(P,{className:(0,H.cn)("grid gap-2",r),...a,ref:t})});T.displayName=P.displayName;let _=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(F,{ref:t,className:(0,H.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),...a,children:(0,s.jsx)(O,{className:"flex items-center justify-center",children:(0,s.jsx)(X.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})})});_.displayName=F.displayName},82230:function(e,t,r){"use strict";r.d(t,{$C:function(){return j},Ne:function(){return g},yn:function(){return f}});var s=r(57437),a=r(11005),n=r(98960),l=r(38133),i=r(20897),o=r(13231),c=r(71935),d=r(47390),u=r(73347),m=r(24258),h=r(5891),x=r(10883),p=r(66648);let f=[{href:"#",icon:(0,s.jsx)(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,s.jsx)(a.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:(0,s.jsx)(n.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:(0,s.jsx)(l.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:(0,s.jsx)(l.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:(0,s.jsx)(i.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:(0,s.jsx)(o.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:(0,s.jsx)(c.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:(0,s.jsx)(d.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,s.jsx)(u.Z,{className:"h-5 w-5"}),label:"Notes"}],j=[{href:"/business/settings/business-info",icon:(0,s.jsx)(m.Z,{className:"h-5 w-5"}),label:"Settings"}],g=[{href:"#",icon:(0,s.jsx)(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,s.jsx)(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:(0,s.jsx)(u.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:(0,s.jsx)(h.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:(0,s.jsx)(x.Z,{className:"h-5 w-5"}),label:"Trash"}]},9437:function(e,t,r){"use strict";var s=r(2265),a=r(86763);t.Z=e=>{let{form:t,formSection:r="",isDialogOpen:n,setIsDialogOpen:l,onSave:i,onDiscard:o,setCurrSkills:c}=e,[d,u]=(0,s.useState)(!1),[m,h]=(0,s.useState)(!1),x=(0,s.useRef)(!1),p=(0,s.useRef)(null),f=e=>{if(!r)return;let t=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");t[r]&&(p.current=t[r]),Object.values(e).some(e=>void 0!==e&&""!==e)&&(t[r]=e,localStorage.setItem("DEHIX_DRAFT",JSON.stringify(t)),(0,a.Am)({title:"Draft Saved",description:"Your ".concat(r," draft has been saved."),duration:1500}))};(0,s.useEffect)(()=>{if(n&&!x.current&&r){let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");e&&e[r]&&u(!0),x.current=!0}},[n,r]);let j=e=>e&&"object"==typeof e?Object.fromEntries(Object.entries(e).map(e=>{let[t,r]=e;return[t,"string"==typeof r?r.trim():r]})):{};return{showDraftDialog:d,setShowDraftDialog:u,confirmExitDialog:m,setConfirmExitDialog:h,loadDraft:()=>{if(!r)return;let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");e&&e[r]&&(Object.keys(e[r]).forEach(t=>{void 0===e[r][t]&&delete e[r][t]}),"projects"===r&&(delete e[r].verificationStatus,Array.isArray(e[r].techUsed)&&c(e[r].techUsed)),Object.entries(e[r]).some(e=>{let[,t]=e;return""!==t&&void 0!==t&&!(Array.isArray(t)&&0===t.length)})&&t&&(t.reset(e[r]),p.current=e[r],(0,a.Am)({title:"Draft Loaded",description:"Your ".concat(r," draft has been restored."),duration:1500})),u(!1))},discardDraft:()=>{if(!r)return;let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");e&&(delete e[r],0===Object.keys(e).length?localStorage.removeItem("DEHIX_DRAFT"):localStorage.setItem("DEHIX_DRAFT",JSON.stringify(e))),null==t||t.reset(),(0,a.Am)({title:"Draft Discarded",description:"Your ".concat(r," draft has been discarded."),duration:1500}),u(!1),o&&o()},handleSaveAndClose:()=>{if(!r)return;let e=null==t?void 0:t.getValues();f(e),(0,a.Am)({title:"Draft Saved",description:"Your draft has been saved.",duration:1500}),p.current=e,h(!1),l&&l(!1),i&&i(e)},handleDiscardAndClose:()=>{if(!r)return;let e=JSON.parse(localStorage.getItem("DEHIX_DRAFT")||"{}");delete e[r],0===Object.keys(e).length?localStorage.removeItem("DEHIX_DRAFT"):localStorage.setItem("DEHIX_DRAFT",JSON.stringify(e)),(0,a.Am)({title:"Draft Discarded",description:"Your ".concat(r," draft has been discarded."),duration:1500}),h(!1),l&&l(!1),o&&o()},handleDialogClose:()=>{if(!n||!r)return;let e=(null==t?void 0:t.getValues())||{},s=p.current||{},a=j(e),i=j(s),o=Object.entries(i).some(e=>{let[t,r]=e,s=a[t];return Array.isArray(r)&&Array.isArray(s)?JSON.stringify(r)!==JSON.stringify(s):r!==s}),c=Object.entries(a).some(e=>{let[t,r]=e;return"verificationStatus"!==t&&void 0!==r&&""!==r&&void 0===i[t]});if(!o&&!c&&l){l(!1);return}Object.values(a).some(e=>null==e?void 0:e.toString().trim())?h(!0):l&&l(!1)},saveDraft:f,hasOtherValues:(0,s.useCallback)(e=>Object.entries(e).some(e=>{let[t,r]=e;return"profiles"!==t&&(Array.isArray(r)&&r.length>0&&("urls"!==t||r.some(e=>{var t;return(null==e?void 0:null===(t=e.value)||void 0===t?void 0:t.trim())!==""}))||"string"==typeof r&&""!==r.trim()||"number"==typeof r&&!isNaN(r))}),[]),hasProfiles:(0,s.useCallback)(e=>null==e?void 0:e.some(e=>Object.values(e).some(e=>Array.isArray(e)&&e.length>0||"string"==typeof e&&""!==e.trim()||"number"==typeof e&&!isNaN(e))),[])}}},38364:function(e,t,r){"use strict";r.d(t,{f:function(){return i}});var s=r(2265),a=r(18676),n=r(57437),l=s.forwardRef((e,t)=>(0,n.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,1374,2455,9726,2688,2971,7023,1744],function(){return e(e.s=23513)}),_N_E=e.O()}]);