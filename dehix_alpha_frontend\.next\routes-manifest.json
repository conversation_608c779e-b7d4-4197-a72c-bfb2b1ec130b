{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}, {"source": "/", "destination": "/auth/login", "statusCode": 308, "regex": "^(?!/_next)/(?:/)?$"}], "headers": [], "dynamicRoutes": [{"page": "/business/freelancerProfile/[freelancer_id]", "regex": "^/business/freelancerProfile/([^/]+?)(?:/)?$", "routeKeys": {"nxtPfreelancer_id": "nxtPfreelancer_id"}, "namedRegex": "^/business/freelancerProfile/(?<nxtPfreelancer_id>[^/]+?)(?:/)?$"}, {"page": "/business/project/[project_id]", "regex": "^/business/project/([^/]+?)(?:/)?$", "routeKeys": {"nxtPproject_id": "nxtPproject_id"}, "namedRegex": "^/business/project/(?<nxtPproject_id>[^/]+?)(?:/)?$"}, {"page": "/business/project/[project_id]/milestone", "regex": "^/business/project/([^/]+?)/milestone(?:/)?$", "routeKeys": {"nxtPproject_id": "nxtPproject_id"}, "namedRegex": "^/business/project/(?<nxtPproject_id>[^/]+?)/milestone(?:/)?$"}, {"page": "/consultancy/[freelancer_id]", "regex": "^/consultancy/([^/]+?)(?:/)?$", "routeKeys": {"nxtPfreelancer_id": "nxtPfreelancer_id"}, "namedRegex": "^/consultancy/(?<nxtPfreelancer_id>[^/]+?)(?:/)?$"}, {"page": "/freelancer/businessProfile/[business_id]", "regex": "^/freelancer/businessProfile/([^/]+?)(?:/)?$", "routeKeys": {"nxtPbusiness_id": "nxtPbusiness_id"}, "namedRegex": "^/freelancer/businessProfile/(?<nxtPbusiness_id>[^/]+?)(?:/)?$"}, {"page": "/freelancer/market/project/[project_id]/apply", "regex": "^/freelancer/market/project/([^/]+?)/apply(?:/)?$", "routeKeys": {"nxtPproject_id": "nxtPproject_id"}, "namedRegex": "^/freelancer/market/project/(?<nxtPproject_id>[^/]+?)/apply(?:/)?$"}, {"page": "/freelancer/market/[business_id]", "regex": "^/freelancer/market/([^/]+?)(?:/)?$", "routeKeys": {"nxtPbusiness_id": "nxtPbusiness_id"}, "namedRegex": "^/freelancer/market/(?<nxtPbusiness_id>[^/]+?)(?:/)?$"}, {"page": "/freelancer/project/[project_id]", "regex": "^/freelancer/project/([^/]+?)(?:/)?$", "routeKeys": {"nxtPproject_id": "nxtPproject_id"}, "namedRegex": "^/freelancer/project/(?<nxtPproject_id>[^/]+?)(?:/)?$"}, {"page": "/freelancer/project/[project_id]/milestone", "regex": "^/freelancer/project/([^/]+?)/milestone(?:/)?$", "routeKeys": {"nxtPproject_id": "nxtPproject_id"}, "namedRegex": "^/freelancer/project/(?<nxtPproject_id>[^/]+?)/milestone(?:/)?$"}, {"page": "/freelancer/[freelancer_id]/freelancer-info", "regex": "^/freelancer/([^/]+?)/freelancer\\-info(?:/)?$", "routeKeys": {"nxtPfreelancer_id": "nxtPfreelancer_id"}, "namedRegex": "^/freelancer/(?<nxtPfreelancer_id>[^/]+?)/freelancer\\-info(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/auth/forgot-password", "regex": "^/auth/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/forgot\\-password(?:/)?$"}, {"page": "/auth/login", "regex": "^/auth/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/login(?:/)?$"}, {"page": "/auth/sign-up/business", "regex": "^/auth/sign\\-up/business(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/sign\\-up/business(?:/)?$"}, {"page": "/auth/sign-up/freelancer", "regex": "^/auth/sign\\-up/freelancer(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/sign\\-up/freelancer(?:/)?$"}, {"page": "/bidmanagement", "regex": "^/bidmanagement(?:/)?$", "routeKeys": {}, "namedRegex": "^/bidmanagement(?:/)?$"}, {"page": "/business/Projects", "regex": "^/business/Projects(?:/)?$", "routeKeys": {}, "namedRegex": "^/business/Projects(?:/)?$"}, {"page": "/business/add-project", "regex": "^/business/add\\-project(?:/)?$", "routeKeys": {}, "namedRegex": "^/business/add\\-project(?:/)?$"}, {"page": "/business/market", "regex": "^/business/market(?:/)?$", "routeKeys": {}, "namedRegex": "^/business/market(?:/)?$"}, {"page": "/business/market/accepted", "regex": "^/business/market/accepted(?:/)?$", "routeKeys": {}, "namedRegex": "^/business/market/accepted(?:/)?$"}, {"page": "/business/market/invited", "regex": "^/business/market/invited(?:/)?$", "routeKeys": {}, "namedRegex": "^/business/market/invited(?:/)?$"}, {"page": "/business/market/rejected", "regex": "^/business/market/rejected(?:/)?$", "routeKeys": {}, "namedRegex": "^/business/market/rejected(?:/)?$"}, {"page": "/business/settings/business-info", "regex": "^/business/settings/business\\-info(?:/)?$", "routeKeys": {}, "namedRegex": "^/business/settings/business\\-info(?:/)?$"}, {"page": "/business/settings/kyc", "regex": "^/business/settings/kyc(?:/)?$", "routeKeys": {}, "namedRegex": "^/business/settings/kyc(?:/)?$"}, {"page": "/business/talent", "regex": "^/business/talent(?:/)?$", "routeKeys": {}, "namedRegex": "^/business/talent(?:/)?$"}, {"page": "/chat", "regex": "^/chat(?:/)?$", "routeKeys": {}, "namedRegex": "^/chat(?:/)?$"}, {"page": "/dashboard/business", "regex": "^/dashboard/business(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/business(?:/)?$"}, {"page": "/dashboard/business/talent", "regex": "^/dashboard/business/talent(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/business/talent(?:/)?$"}, {"page": "/dashboard/freelancer", "regex": "^/dashboard/freelancer(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/freelancer(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/freelancer/interview/bids", "regex": "^/freelancer/interview/bids(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/interview/bids(?:/)?$"}, {"page": "/freelancer/interview/current", "regex": "^/freelancer/interview/current(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/interview/current(?:/)?$"}, {"page": "/freelancer/interview/history", "regex": "^/freelancer/interview/history(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/interview/history(?:/)?$"}, {"page": "/freelancer/interview/profile", "regex": "^/freelancer/interview/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/interview/profile(?:/)?$"}, {"page": "/freelancer/interview/start-interviewing", "regex": "^/freelancer/interview/start\\-interviewing(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/interview/start\\-interviewing(?:/)?$"}, {"page": "/freelancer/market", "regex": "^/freelancer/market(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/market(?:/)?$"}, {"page": "/freelancer/oracleDashboard/businessVerification", "regex": "^/freelancer/oracleDashboard/businessVerification(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/oracleDashboard/businessVerification(?:/)?$"}, {"page": "/freelancer/oracleDashboard/educationVerification", "regex": "^/freelancer/oracleDashboard/educationVerification(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/oracleDashboard/educationVerification(?:/)?$"}, {"page": "/freelancer/oracleDashboard/projectVerification", "regex": "^/freelancer/oracleDashboard/projectVerification(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/oracleDashboard/projectVerification(?:/)?$"}, {"page": "/freelancer/oracleDashboard/workExpVerification", "regex": "^/freelancer/oracleDashboard/workExpVerification(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/oracleDashboard/workExpVerification(?:/)?$"}, {"page": "/freelancer/project/applied", "regex": "^/freelancer/project/applied(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/project/applied(?:/)?$"}, {"page": "/freelancer/project/completed", "regex": "^/freelancer/project/completed(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/project/completed(?:/)?$"}, {"page": "/freelancer/project/current", "regex": "^/freelancer/project/current(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/project/current(?:/)?$"}, {"page": "/freelancer/project/rejected", "regex": "^/freelancer/project/rejected(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/project/rejected(?:/)?$"}, {"page": "/freelancer/scheduleInterview", "regex": "^/freelancer/scheduleInterview(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/scheduleInterview(?:/)?$"}, {"page": "/freelancer/settings/education-info", "regex": "^/freelancer/settings/education\\-info(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/settings/education\\-info(?:/)?$"}, {"page": "/freelancer/settings/kyc", "regex": "^/freelancer/settings/kyc(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/settings/kyc(?:/)?$"}, {"page": "/freelancer/settings/personal-info", "regex": "^/freelancer/settings/personal\\-info(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/settings/personal\\-info(?:/)?$"}, {"page": "/freelancer/settings/professional-info", "regex": "^/freelancer/settings/professional\\-info(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/settings/professional\\-info(?:/)?$"}, {"page": "/freelancer/settings/projects", "regex": "^/freelancer/settings/projects(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/settings/projects(?:/)?$"}, {"page": "/freelancer/settings/resume", "regex": "^/freelancer/settings/resume(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/settings/resume(?:/)?$"}, {"page": "/freelancer/talent", "regex": "^/freelancer/talent(?:/)?$", "routeKeys": {}, "namedRegex": "^/freelancer/talent(?:/)?$"}, {"page": "/home/<USER>", "regex": "^/home/<USER>/)?$", "routeKeys": {}, "namedRegex": "^/home/<USER>/)?$"}, {"page": "/home/<USER>", "regex": "^/home/<USER>/)?$", "routeKeys": {}, "namedRegex": "^/home/<USER>/)?$"}, {"page": "/home/<USER>", "regex": "^/home/<USER>/)?$", "routeKeys": {}, "namedRegex": "^/home/<USER>/)?$"}, {"page": "/market/freelancer/project", "regex": "^/market/freelancer/project(?:/)?$", "routeKeys": {}, "namedRegex": "^/market/freelancer/project(?:/)?$"}, {"page": "/notes", "regex": "^/notes(?:/)?$", "routeKeys": {}, "namedRegex": "^/notes(?:/)?$"}, {"page": "/notes/archive", "regex": "^/notes/archive(?:/)?$", "routeKeys": {}, "namedRegex": "^/notes/archive(?:/)?$"}, {"page": "/notes/trash", "regex": "^/notes/trash(?:/)?$", "routeKeys": {}, "namedRegex": "^/notes/trash(?:/)?$"}, {"page": "/privacy", "regex": "^/privacy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/settings/support", "regex": "^/settings/support(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/support(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}