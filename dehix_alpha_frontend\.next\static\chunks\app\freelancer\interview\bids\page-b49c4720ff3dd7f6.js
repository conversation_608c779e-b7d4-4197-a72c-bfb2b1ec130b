(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9301],{39189:function(e,i,t){Promise.resolve().then(t.bind(t,42939))},25912:function(e,i,t){"use strict";t.d(i,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},24241:function(e,i,t){"use strict";t.d(i,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71935:function(e,i,t){"use strict";t.d(i,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40933:function(e,i,t){"use strict";t.d(i,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},42939:function(e,i,t){"use strict";t.r(i),t.d(i,{default:function(){return _}});var l=t(57437),s=t(2265),n=t(11444),r=t(64797),a=t(59282),d=t(62688),o=t(2183),c=()=>(0,l.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,i)=>(0,l.jsx)(o.O,{className:"h-20 w-full rounded-lg"},i))}),u=t(92940),v=t(71935),m=t(40933),x=t(52022),h=t(25912),f=t(24241),w=t(51077),p=t(79055),g=t(89733),j=t(48185),N=t(80023),b=e=>{var i,t,s,n,r,a,d;let{bid:o,interviewId:c,setConfirmAction:b}=e;return(0,l.jsxs)(j.Zb,{className:"relative w-full max-w-lg mx-auto p-6 rounded-2xl border  shadow-lg hover:shadow-2xl transition-all space-y-4",children:[(0,l.jsxs)(p.C,{className:"absolute top-3 right-3 text-xs flex items-center gap-2 px-2 py-1 font-semibold rounded-full shadow-md transition-all \n      ".concat((null==o?void 0:o.status)==="ACCEPTED"?"text-green-800 bg-green-100":(null==o?void 0:o.status)==="REJECTED"?"text-red-800 bg-red-100":"text-yellow-800 bg-yellow-100"),children:[(null==o?void 0:o.status)==="ACCEPTED"?(0,l.jsx)(u.Z,{className:"w-4 h-4"}):(null==o?void 0:o.status)==="REJECTED"?(0,l.jsx)(v.Z,{className:"w-4 h-4"}):(0,l.jsx)(m.Z,{className:"w-4 h-4"}),(null==o?void 0:null===(i=o.status)||void 0===i?void 0:i.toUpperCase())||"PENDING"]}),(0,l.jsxs)(j.aY,{className:"flex flex-col gap-4",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 text-lg font-semibold ",children:[(0,l.jsx)(x.Z,{className:"w-5 h-5 "}),(null==o?void 0:null===(t=o.interviewer)||void 0===t?void 0:t.userName)||"Not Provided"]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("p",{className:"text-sm font-semibold  mr-2",children:"Skills:"}),(0,l.jsxs)(N.x,{className:"w-full whitespace-nowrap overflow-x-auto ",children:[(0,l.jsx)("div",{className:"flex items-center gap-2",children:(null==o?void 0:null===(n=o.interviewer)||void 0===n?void 0:null===(s=n.skills)||void 0===s?void 0:s.length)?o.interviewer.skills.map((e,i)=>(0,l.jsx)(p.C,{className:"px-3 py-1 text-xs  rounded-md shadow-sm",children:e},i)):(0,l.jsx)("p",{className:"text-xs text-gray-400",children:"Not Provided"})}),(null==o?void 0:null===(a=o.interviewer)||void 0===a?void 0:null===(r=a.skills)||void 0===r?void 0:r.length)?(0,l.jsx)("div",{className:"mt-2",children:(0,l.jsx)(N.B,{orientation:"horizontal"})}):null]})]}),(0,l.jsxs)("div",{className:"flex items-center gap-3 text-sm ",children:[(0,l.jsx)(h.Z,{className:"w-5 h-5 "}),(null==o?void 0:null===(d=o.interviewer)||void 0===d?void 0:d.workExperience)?"".concat(o.interviewer.workExperience," years"):"Not Provided"]}),(0,l.jsxs)("div",{className:"flex justify-between text-sm ",children:[(0,l.jsxs)("p",{className:"flex items-center gap-2",children:[(0,l.jsx)(f.Z,{className:"w-5 h-5 "}),(null==o?void 0:o.suggestedDateTime)?new Date(o.suggestedDateTime).toLocaleString():"Not Provided"]}),(0,l.jsxs)("p",{className:"flex items-center gap-2",children:[(0,l.jsx)(w.Z,{className:"w-5 h-5 "}),(null==o?void 0:o.fee)||"Not Provided"]})]}),(null==o?void 0:o.status)==="PENDING"&&(0,l.jsxs)("div",{className:"flex justify-center gap-4 mt-3",children:[(0,l.jsx)(g.z,{className:"px-5 py-2 text-sm font-semibold bg-green-600 hover:bg-green-700 text-white rounded-lg shadow-md transition-all",onClick:()=>b({interviewId:c,bidId:o._id,action:"ACCEPTED"}),children:"Accept"}),(0,l.jsx)(g.z,{className:"px-5 py-2 text-sm font-semibold bg-red-600 hover:bg-red-700 text-white rounded-lg shadow-md transition-all",onClick:()=>b({interviewId:c,bidId:o._id,action:"REJECTED"}),children:"Reject"})]})]})]})},y=e=>{let{interview:i,setConfirmAction:t}=e,s=Object.values((null==i?void 0:i.interviewBids)||{});return(0,l.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 mt-4",children:s.length>0?s.map(e=>(0,l.jsx)(b,{bid:e,interviewId:null==i?void 0:i._id,setConfirmAction:t},null==e?void 0:e._id)):(0,l.jsx)("div",{className:"col-span-full text-center text-lg mb-4 text-gray-500",children:"No bids are available for the interview"})})},k=t(15922),E=t(35265),C=t(54662),Z=t(78068),I=e=>{var i,t;let{userId:n}=e,[r,a]=(0,s.useState)([]),[d,o]=(0,s.useState)(!0),[u,v]=(0,s.useState)(null);(0,s.useEffect)(()=>{(async()=>{try{var e;o(!0);let i=await k.b.get("/interview",{params:{intervieweeId:n}});console.log(i),a((null==i?void 0:null===(e=i.data)||void 0===e?void 0:e.data)||[])}catch(e){console.error("Error fetching interview bids",e),(0,Z.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}finally{o(!1)}})()},[n]);let m=async()=>{var e;if(!u)return;let{interviewId:i,bidId:t,action:l}=u,s=r.find(e=>e._id===i);if(!s)return;let n=null===(e=s.talentId)||void 0===e?void 0:e.id,d="ACCEPTED"===l,o=Object.values(s.interviewBids||{}).map(e=>{var i;return{_id:e._id,interviewerId:(null===(i=e.interviewer)||void 0===i?void 0:i._id)||e.interviewerId,dateTimeAgreement:e.dateTimeAgreement||!1,suggestedDateTime:e.suggestedDateTime||null,fee:e.fee||"0",status:e._id===t?l:d?"REJECTED":e.status}}).filter(e=>e.interviewerId).reduce((e,i)=>(e[i._id]=i,e),{}),c=Object.values(o).some(e=>"ACCEPTED"===e.status)?"SCHEDULED":"BIDDING",m={_id:s._id,talentId:n,interviewBids:o,InterviewStatus:c};try{await k.b.put("/interview/".concat(i),m),a(e=>d?e.filter(e=>e._id!==i):e.map(e=>e._id===i?{...e,interviewBids:o,InterviewStatus:c}:e))}catch(e){console.error("Error updating interview bid:",e),(0,Z.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}v(null)};return(0,l.jsxs)("div",{className:"w-[84vw] mx-auto ",children:[d?(0,l.jsx)(c,{}):(null==r?void 0:r.length)>0?(0,l.jsx)(E.UQ,{type:"single",collapsible:!0,defaultValue:null==r?void 0:null===(i=r[0])||void 0===i?void 0:i._id,children:r.map(e=>(0,l.jsxs)(E.Qd,{value:(null==e?void 0:e._id)||"",children:[(0,l.jsx)(E.o4,{className:"text-xl w-full font-semibold hover:no-underline",children:(0,l.jsxs)("div",{className:"flex justify-between items-center w-full mx-3",children:[(0,l.jsx)("div",{children:(null==e?void 0:e.talentType)||"No Talent Label"}),(0,l.jsxs)("div",{children:[Object.keys((null==e?void 0:e.interviewBids)||{}).length," Bids"]})]})}),(0,l.jsx)(E.vF,{children:(0,l.jsx)(y,{interview:e,setConfirmAction:v})})]},null==e?void 0:e._id))}):(0,l.jsx)("div",{className:"text-center text-lg font-semibold mt-4",children:"No bids available"}),u&&(0,l.jsx)(C.Vq,{open:!!u,onOpenChange:()=>v(null),children:(0,l.jsxs)(C.cZ,{className:"m-2 w-[80vw] md:max-w-lg ",children:[(0,l.jsx)(C.fK,{children:(0,l.jsxs)(C.$N,{children:["Confirm ",null===(t=u.action)||void 0===t?void 0:t.toLowerCase()," action?"]})}),(0,l.jsxs)(C.cN,{children:[(0,l.jsx)(g.z,{variant:"outline",onClick:()=>v(null),children:"Cancel"}),(0,l.jsx)(g.z,{className:"mb-3",onClick:m,children:"Confirm"})]})]})})]})};function _(){let e=(0,n.v9)(e=>e.user);return(0,l.jsxs)("div",{className:"flex min-h-screen w-full  bg-muted/40",children:[(0,l.jsx)(r.Z,{menuItemsTop:a.y,menuItemsBottom:a.$,active:"Bids"}),(0,l.jsxs)("div",{className:"flex mb-8 flex-col sm:pl-14 w-full",children:[(0,l.jsx)(d.Z,{menuItemsTop:a.y,menuItemsBottom:a.$,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Interview",link:"/freelancer/interview/profile"},{label:"Bids",link:"#"}]}),(0,l.jsxs)("div",{className:"ml-10 mt-6 mb-10",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold",children:"Interview Bid's"}),(0,l.jsx)("p",{className:"text-gray-400 mt-2",children:"Select your bids strategically and secure your interview"})]}),(0,l.jsx)(I,{userId:e.uid})]})]})}},2183:function(e,i,t){"use strict";t.d(i,{O:function(){return n}});var l=t(57437),s=t(49354);function n(e){let{className:i,...t}=e;return(0,l.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-md bg-primary/10",i),...t})}},59282:function(e,i,t){"use strict";t.d(i,{$:function(){return m},y:function(){return v}});var l=t(57437),s=t(11005),n=t(38133),r=t(33480);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("ListVideo",[["path",{d:"M12 12H3",key:"18klou"}],["path",{d:"M16 6H3",key:"1wxfjs"}],["path",{d:"M12 18H3",key:"11ftsu"}],["path",{d:"m16 12 5 3-5 3v-6Z",key:"zpskkp"}]]);var d=t(25912);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,r.Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var c=t(24258),u=t(66648);let v=[{href:"#",icon:(0,l.jsx)(u.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,l.jsx)(s.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/interview/profile",icon:(0,l.jsx)(n.Z,{className:"h-5 w-5"}),label:"Profile"},{href:"/freelancer/interview/current",icon:(0,l.jsx)(a,{className:"h-5 w-5"}),label:"Current"},{href:"/freelancer/interview/bids",icon:(0,l.jsx)(d.Z,{className:"h-5 w-5"}),label:"Bids"},{href:"/freelancer/interview/history",icon:(0,l.jsx)(o,{className:"h-5 w-5"}),label:"History"}],m=[{href:"/freelancer/settings/personal-info",icon:(0,l.jsx)(c.Z,{className:"h-5 w-5"}),label:"Settings"}]}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2455,9726,2688,2971,7023,1744],function(){return e(e.s=39189)}),_N_E=e.O()}]);