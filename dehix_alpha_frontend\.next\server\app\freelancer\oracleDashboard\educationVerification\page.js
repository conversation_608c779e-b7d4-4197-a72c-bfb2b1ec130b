(()=>{var e={};e.id=5049,e.ids=[5049],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},3511:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>o}),s(55315),s(54302),s(12523);var r=s(23191),a=s(88716),i=s(37922),n=s.n(i),l=s(95231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let o=["",{children:["freelancer",{children:["oracleDashboard",{children:["educationVerification",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,55315)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\oracleDashboard\\educationVerification\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\oracleDashboard\\educationVerification\\page.tsx"],x="/freelancer/oracleDashboard/educationVerification/page",u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/freelancer/oracleDashboard/educationVerification/page",pathname:"/freelancer/oracleDashboard/educationVerification",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},85114:(e,t,s)=>{Promise.resolve().then(s.bind(s,66815))},66815:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>S});var r=s(10326),a=s(41137),i=s(23015),n=s(17577),l=s(91664),c=s(24118),o=s(2822),d=s(92166),x=s(34270),u=s(6260);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,s(80851).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var p=s(40617),h=s(74064),f=s(74723),j=s(27256),g=s(29752),v=s(38443),y=s(9969),b=s(82631),N=s(82015),_=s(56627);let q=j.z.object({type:j.z.enum(["Approved","Denied"],{required_error:"You need to select a type."}),comment:j.z.string().optional()}),w=({_id:e,type:t,degree:s,location:a,startFrom:i,endTo:c,grade:d,fieldOfStudy:x,comments:j,status:w,onStatusUpdate:D,onCommentUpdate:C})=>{let[S,P]=(0,n.useState)(w),V=(0,f.cI)({resolver:(0,h.F)(q),mode:"onChange"}),E=V.watch("type");async function A(t){try{await u.b.put(`/verification/${e}/oracle?doc_type=education`,{comments:t.comment,verification_status:t.type})}catch(e){(0,_.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}P(t.type),D(t.type),C(t.comment||"")}return(0,n.useEffect)(()=>{P(w)},[w]),(0,r.jsxs)(g.Zb,{className:"max-w-full md:max-w-2xl",children:[(0,r.jsxs)(g.Ol,{children:[(0,r.jsxs)(g.ll,{className:"flex justify-between",children:[r.jsx("span",{children:t}),"pending"===S||"added"===S?r.jsx(v.C,{className:"bg-warning-foreground text-white",children:"PENDING"}):"Approved"===S?r.jsx(v.C,{className:"bg-success text-white",children:"Approved"}):r.jsx(v.C,{className:"bg-red-500 text-white",children:"Denied"})]}),(0,r.jsxs)(g.SZ,{className:"text-justify text-gray-600",children:[r.jsx("br",{}),(0,r.jsxs)(b.u,{children:[r.jsx(b.aJ,{asChild:!0,children:(0,r.jsxs)("p",{className:"text-lg text-gray-600 flex items-center mt-3",children:[r.jsx(m,{className:"mr-2"}),a]})}),r.jsx(b._v,{side:"bottom",children:"Location"})]})]})]}),r.jsx(g.aY,{children:(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsxs)("p",{className:"text-m text-gray-600 mb-2",children:[r.jsx("span",{className:"text-gray-500 font-semibold",children:"degree:"})," ",s]}),(0,r.jsxs)("p",{className:"text-m text-gray-600 mb-2",children:[r.jsx("span",{className:"text-gray-500 font-semibold",children:"Field Of study:"})," ",x]}),(0,r.jsxs)("p",{className:"text-m text-gray-600 mb-2",children:[r.jsx("span",{className:"text-gray-500 font-semibold",children:"Grade:"})," ",d]}),j&&(0,r.jsxs)("p",{className:"mt-2 flex items-center text-gray-500 border p-3 rounded",children:[r.jsx(p.Z,{className:"mr-2"}),j]})]})}),(0,r.jsxs)(g.eW,{className:"flex flex-col items-center",children:[(0,r.jsxs)("div",{className:"flex flex-1 gap-4",children:[new Date(i).toLocaleDateString()," -","current"!==c?new Date(c).toLocaleDateString():"Current"]}),("pending"===S||"added"===S)&&r.jsx(y.l0,{...V,children:(0,r.jsxs)("form",{onSubmit:V.handleSubmit(A),className:"w-full space-y-6 mt-6",children:[r.jsx(y.Wi,{control:V.control,name:"type",render:({field:e})=>(0,r.jsxs)(y.xJ,{className:"space-y-3",children:[r.jsx(y.lX,{children:"Choose Verification Status:"}),r.jsx(y.NI,{children:(0,r.jsxs)(o.E,{onValueChange:e.onChange,defaultValue:e.value,className:"flex flex-row space-x-4",children:[(0,r.jsxs)(y.xJ,{className:"flex items-center space-x-3",children:[r.jsx(y.NI,{children:r.jsx(o.m,{value:"Approved"})}),r.jsx(y.lX,{className:"font-normal",children:"Approved"})]}),(0,r.jsxs)(y.xJ,{className:"flex items-center space-x-3",children:[r.jsx(y.NI,{children:r.jsx(o.m,{value:"Denied"})}),r.jsx(y.lX,{className:"font-normal",children:"Denied"})]})]})}),r.jsx(y.zG,{})]})}),r.jsx(y.Wi,{control:V.control,name:"comment",render:({field:e})=>(0,r.jsxs)(y.xJ,{children:[r.jsx(y.lX,{children:"Comments:"}),r.jsx(y.NI,{children:r.jsx(N.g,{placeholder:"Enter comments:",...e})}),r.jsx(y.zG,{})]})}),r.jsx(l.z,{type:"submit",className:"w-full",disabled:!E||V.formState.isSubmitting,children:"Submit"})]})})]})]})};var D=s(39958),C=s(40588);function S(){let[e,t]=(0,n.useState)([]),[s,m]=(0,n.useState)("all"),[p,h]=(0,n.useState)(!1),f=e=>{m(e),h(!1)},j=e.filter(e=>"all"===s||e.verificationStatus===s||"current"===s&&e.verificationStatus===D.sB.PENDING);return(0,n.useCallback)(async()=>{try{let e=(await u.b.get("/verification/oracle?doc_type=education")).data.data.flatMap(e=>e.result?.projects?Object.values(e.result.projects).map(t=>({...t,verifier_id:e.verifier_id,verifier_username:e.verifier_username})):[]);t(e)}catch(e){console.log(e,"error in getting verification data"),(0,_.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}},[]),(0,r.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[r.jsx(d.Z,{menuItemsTop:x.y,menuItemsBottom:x.$,active:"Education Verification"}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[r.jsx(C.Z,{menuItemsTop:x.y,menuItemsBottom:x.$,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Oracle",link:"#"},{label:"Education Verification",link:"#"}]}),(0,r.jsxs)("div",{className:"mb-8 ml-4 flex justify-between mt-8 md:mt-4 items-center",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold",children:"Education Verification"}),r.jsx("p",{className:"text-gray-400 mt-2",children:"Monitor the status of your Education verifications."})]}),r.jsx(l.z,{variant:"outline",size:"icon",className:"mr-8 mb-12",onClick:()=>h(!0),children:r.jsx(a.Z,{className:"h-4 w-4"})})]}),r.jsx(c.Vq,{open:p,onOpenChange:h,children:(0,r.jsxs)(c.cZ,{children:[r.jsx(c.fK,{children:r.jsx(c.$N,{children:"Filter Education Status"})}),(0,r.jsxs)(o.E,{defaultValue:"all",value:s,onValueChange:e=>f(e),className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(o.m,{value:"all",id:"filter-all"}),r.jsx("label",{htmlFor:"filter-all",children:"All"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(o.m,{value:"current",id:"filter-current"}),r.jsx("label",{htmlFor:"filter-current",children:"Pending"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(o.m,{value:"verified",id:"filter-verified"}),r.jsx("label",{htmlFor:"filter-verified",children:"Verified"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(o.m,{value:"rejected",id:"filter-rejected"}),r.jsx("label",{htmlFor:"filter-rejected",children:"Rejected"})]})]}),r.jsx(c.cN,{children:r.jsx(l.z,{type:"button",onClick:()=>h(!1),children:"Close"})})]})}),(0,r.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8    grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3",children:[j.map((e,t)=>r.jsx(w,{type:"education",_id:e._id,degree:e.degree,location:e.universityName,startFrom:e.startDate,endTo:e.endDate,grade:e.grade,fieldOfStudy:e.fieldOfStudy,comments:e.comments,status:e.verificationStatus,onStatusUpdate:e=>{console.log("Status updated to:",e)},onCommentUpdate:e=>{console.log("Comment updated to:",e)}},t)),0===e.length?(0,r.jsxs)("div",{className:"text-center w-[90vw] px-auto mt-20 py-10",children:[r.jsx(i.Z,{className:"mx-auto text-gray-500",size:"100"}),r.jsx("p",{className:"text-gray-500",children:"No Education verification for you now."})]}):null]})]})]})}},55315:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var r=s(68570);let a=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\oracleDashboard\educationVerification\page.tsx`),{__esModule:i,$$typeof:n}=a;a.default;let l=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\oracleDashboard\educationVerification\page.tsx#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,6686,4736,6499,8066,588,3379],()=>s(3511));module.exports=r})();