(()=>{var e={};e.id=4306,e.ids=[4306],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},46893:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>d}),s(36972),s(54302),s(12523);var r=s(23191),a=s(88716),i=s(37922),n=s.n(i),l=s(95231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let d=["",{children:["dashboard",{children:["freelancer",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,36972)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\dashboard\\freelancer\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\dashboard\\freelancer\\page.tsx"],u="/dashboard/freelancer/page",h={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/freelancer/page",pathname:"/dashboard/freelancer",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2909:(e,t,s)=>{Promise.resolve().then(s.bind(s,19173))},88295:function(e){var t;t=function(){"use strict";var e="millisecond",t="second",s="minute",r="hour",a="week",i="month",n="quarter",l="year",c="date",d="Invalid Date",o=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,u=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,h=function(e,t,s){var r=String(e);return!r||r.length>=t?e:""+Array(t+1-r.length).join(s)+e},m="en",x={};x[m]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],s=e%100;return"["+e+(t[(s-20)%10]||t[s]||"th")+"]"}};var p="$isDayjsObject",f=function(e){return e instanceof y||!(!e||!e[p])},j=function e(t,s,r){var a;if(!t)return m;if("string"==typeof t){var i=t.toLowerCase();x[i]&&(a=i),s&&(x[i]=s,a=i);var n=t.split("-");if(!a&&n.length>1)return e(n[0])}else{var l=t.name;x[l]=t,a=l}return!r&&a&&(m=a),a||!r&&m},g=function(e,t){if(f(e))return e.clone();var s="object"==typeof t?t:{};return s.date=e,s.args=arguments,new y(s)},v={s:h,z:function(e){var t=-e.utcOffset(),s=Math.abs(t);return(t<=0?"+":"-")+h(Math.floor(s/60),2,"0")+":"+h(s%60,2,"0")},m:function e(t,s){if(t.date()<s.date())return-e(s,t);var r=12*(s.year()-t.year())+(s.month()-t.month()),a=t.clone().add(r,i),n=s-a<0,l=t.clone().add(r+(n?-1:1),i);return+(-(r+(s-a)/(n?a-l:l-a))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(d){return({M:i,y:l,w:a,d:"day",D:c,h:r,m:s,s:t,ms:e,Q:n})[d]||String(d||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};v.l=j,v.i=f,v.w=function(e,t){return g(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var y=function(){function h(e){this.$L=j(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[p]=!0}var m=h.prototype;return m.parse=function(e){this.$d=function(e){var t=e.date,s=e.utc;if(null===t)return new Date(NaN);if(v.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var r=t.match(o);if(r){var a=r[2]-1||0,i=(r[7]||"0").substring(0,3);return s?new Date(Date.UTC(r[1],a,r[3]||1,r[4]||0,r[5]||0,r[6]||0,i)):new Date(r[1],a,r[3]||1,r[4]||0,r[5]||0,r[6]||0,i)}}return new Date(t)}(e),this.init()},m.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},m.$utils=function(){return v},m.isValid=function(){return this.$d.toString()!==d},m.isSame=function(e,t){var s=g(e);return this.startOf(t)<=s&&s<=this.endOf(t)},m.isAfter=function(e,t){return g(e)<this.startOf(t)},m.isBefore=function(e,t){return this.endOf(t)<g(e)},m.$g=function(e,t,s){return v.u(e)?this[t]:this.set(s,e)},m.unix=function(){return Math.floor(this.valueOf()/1e3)},m.valueOf=function(){return this.$d.getTime()},m.startOf=function(e,n){var d=this,o=!!v.u(n)||n,u=v.p(e),h=function(e,t){var s=v.w(d.$u?Date.UTC(d.$y,t,e):new Date(d.$y,t,e),d);return o?s:s.endOf("day")},m=function(e,t){return v.w(d.toDate()[e].apply(d.toDate("s"),(o?[0,0,0,0]:[23,59,59,999]).slice(t)),d)},x=this.$W,p=this.$M,f=this.$D,j="set"+(this.$u?"UTC":"");switch(u){case l:return o?h(1,0):h(31,11);case i:return o?h(1,p):h(0,p+1);case a:var g=this.$locale().weekStart||0,y=(x<g?x+7:x)-g;return h(o?f-y:f+(6-y),p);case"day":case c:return m(j+"Hours",0);case r:return m(j+"Minutes",1);case s:return m(j+"Seconds",2);case t:return m(j+"Milliseconds",3);default:return this.clone()}},m.endOf=function(e){return this.startOf(e,!1)},m.$set=function(a,n){var d,o=v.p(a),u="set"+(this.$u?"UTC":""),h=((d={}).day=u+"Date",d[c]=u+"Date",d[i]=u+"Month",d[l]=u+"FullYear",d[r]=u+"Hours",d[s]=u+"Minutes",d[t]=u+"Seconds",d[e]=u+"Milliseconds",d)[o],m="day"===o?this.$D+(n-this.$W):n;if(o===i||o===l){var x=this.clone().set(c,1);x.$d[h](m),x.init(),this.$d=x.set(c,Math.min(this.$D,x.daysInMonth())).$d}else h&&this.$d[h](m);return this.init(),this},m.set=function(e,t){return this.clone().$set(e,t)},m.get=function(e){return this[v.p(e)]()},m.add=function(e,n){var c,d=this;e=Number(e);var o=v.p(n),u=function(t){var s=g(d);return v.w(s.date(s.date()+Math.round(t*e)),d)};if(o===i)return this.set(i,this.$M+e);if(o===l)return this.set(l,this.$y+e);if("day"===o)return u(1);if(o===a)return u(7);var h=((c={})[s]=6e4,c[r]=36e5,c[t]=1e3,c)[o]||1,m=this.$d.getTime()+e*h;return v.w(m,this)},m.subtract=function(e,t){return this.add(-1*e,t)},m.format=function(e){var t=this,s=this.$locale();if(!this.isValid())return s.invalidDate||d;var r=e||"YYYY-MM-DDTHH:mm:ssZ",a=v.z(this),i=this.$H,n=this.$m,l=this.$M,c=s.weekdays,o=s.months,h=s.meridiem,m=function(e,s,a,i){return e&&(e[s]||e(t,r))||a[s].slice(0,i)},x=function(e){return v.s(i%12||12,e,"0")},p=h||function(e,t,s){var r=e<12?"AM":"PM";return s?r.toLowerCase():r};return r.replace(u,function(e,r){return r||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return v.s(t.$y,4,"0");case"M":return l+1;case"MM":return v.s(l+1,2,"0");case"MMM":return m(s.monthsShort,l,o,3);case"MMMM":return m(o,l);case"D":return t.$D;case"DD":return v.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return m(s.weekdaysMin,t.$W,c,2);case"ddd":return m(s.weekdaysShort,t.$W,c,3);case"dddd":return c[t.$W];case"H":return String(i);case"HH":return v.s(i,2,"0");case"h":return x(1);case"hh":return x(2);case"a":return p(i,n,!0);case"A":return p(i,n,!1);case"m":return String(n);case"mm":return v.s(n,2,"0");case"s":return String(t.$s);case"ss":return v.s(t.$s,2,"0");case"SSS":return v.s(t.$ms,3,"0");case"Z":return a}return null}(e)||a.replace(":","")})},m.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},m.diff=function(e,c,d){var o,u=this,h=v.p(c),m=g(e),x=(m.utcOffset()-this.utcOffset())*6e4,p=this-m,f=function(){return v.m(u,m)};switch(h){case l:o=f()/12;break;case i:o=f();break;case n:o=f()/3;break;case a:o=(p-x)/6048e5;break;case"day":o=(p-x)/864e5;break;case r:o=p/36e5;break;case s:o=p/6e4;break;case t:o=p/1e3;break;default:o=p}return d?o:v.a(o)},m.daysInMonth=function(){return this.endOf(i).$D},m.$locale=function(){return x[this.$L]},m.locale=function(e,t){if(!e)return this.$L;var s=this.clone(),r=j(e,t,!0);return r&&(s.$L=r),s},m.clone=function(){return v.w(this.$d,this)},m.toDate=function(){return new Date(this.valueOf())},m.toJSON=function(){return this.isValid()?this.toISOString():null},m.toISOString=function(){return this.$d.toISOString()},m.toString=function(){return this.$d.toUTCString()},h}(),N=y.prototype;return g.prototype=N,[["$ms",e],["$s",t],["$m",s],["$H",r],["$W","day"],["$M",i],["$y",l],["$D",c]].forEach(function(e){N[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),g.extend=function(e,t){return e.$i||(e(t,y,g),e.$i=!0),g},g.locale=j,g.isDayjs=f,g.unix=function(e){return g(1e3*e)},g.en=x[m],g.Ls=x,g.p={},g},e.exports=t()},40900:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},57248:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("CalendarX2",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M21 13V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h8",key:"3spt84"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"m17 22 5-5",key:"1k6ppv"}],["path",{d:"m17 17 5 5",key:"p7ous7"}]])},48998:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},43727:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},40617:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},23015:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},60763:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69515:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},98091:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},19173:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>_});var r=s(10326),a=s(39183),i=s(30361),n=s(48998),l=s(57248),c=s(25842),d=s(17577),o=s(29752),u=s(50384),h=s(97265),m=s(6260),x=s(92166),p=s(48586),f=s(23015),j=s(90434),g=s(71064),v=s(41156),y=s(38443),N=s(91664),b=s(38227),w=s(24118);let k=({projects:e,loading:t,type:s})=>{let[a,i]=(0,d.useState)(null),[n,l]=(0,d.useState)(!1),[c,u]=(0,d.useState)(!1),h=()=>{l(!1),i(null)},m=e=>{i(e),l(!0)};return(0,r.jsxs)(o.Zb,{children:[(0,r.jsxs)(o.Ol,{className:"px-7",children:[r.jsx(o.ll,{children:"Projects"}),r.jsx(o.SZ,{children:"Recent projects from your account."})]}),r.jsx(o.aY,{children:(0,r.jsxs)(v.iA,{children:[r.jsx(v.xD,{children:(0,r.jsxs)(v.SC,{children:[r.jsx(v.ss,{className:"text-start",children:"Project Name"}),r.jsx(v.ss,{className:"text-center",children:"Verification"}),r.jsx(v.ss,{className:"text-center",children:"Start Date"}),r.jsx(v.ss,{className:"text-center",children:"Status"}),r.jsx(v.ss,{className:"text-center",children:"Actions"})]})}),r.jsx(v.RM,{children:t?[void 0,void 0,void 0].map((e,t)=>(0,r.jsxs)(v.SC,{children:[r.jsx(v.pj,{children:r.jsx(b.O,{className:"h-4 w-32"})}),r.jsx(v.pj,{className:"text-center",children:r.jsx(b.O,{className:"h-4 w-20"})}),r.jsx(v.pj,{className:"text-center",children:r.jsx(b.O,{className:"h-4 w-16"})}),r.jsx(v.pj,{className:"text-center",children:r.jsx(b.O,{className:"h-4 w-20"})}),r.jsx(v.pj,{className:"text-center",children:r.jsx(b.O,{className:"h-8 w-24"})}),r.jsx(v.pj,{className:"text-center",children:r.jsx(b.O,{className:"h-4 w-20"})})]},t)):e.length>0?e.map(e=>(0,r.jsxs)(v.SC,{children:[r.jsx(v.pj,{children:r.jsx("div",{className:"font-medium",children:e.projectName})}),r.jsx(v.pj,{className:"text-center",children:r.jsx(y.C,{className:"text-xs",variant:e.verified?"secondary":"outline",children:e.verified?"Verified":"Not Verified"})}),r.jsx(v.pj,{className:"text-center",children:e.start?new Date(e.start).toLocaleDateString():"N/A"}),r.jsx(v.pj,{className:"text-center",children:e.status?r.jsx(y.C,{className:(0,g.S)(e.status).className,children:(0,g.S)(e.status).text}):"N/A"}),r.jsx(v.pj,{className:"text-center",children:"rejected"===s||"pending"===s?(0,r.jsxs)(w.Vq,{open:n,onOpenChange:e=>l(e),children:[r.jsx(w.hg,{asChild:!0,children:r.jsx(N.z,{size:"sm",variant:"outline",onClick:()=>m(e),className:"border border-gray-300 rounded-lg px-4 py-2 transition-all duration-300 shadow-sm hover:shadow-md",children:(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[r.jsx("i",{className:"fas fa-info-circle"}),r.jsx("span",{children:"View Status"})]})})}),(0,r.jsxs)(w.cZ,{className:"rounded-lg shadow-md p-6 w-96 mx-auto border border-gray-200",children:[r.jsx(w.fK,{className:"mb-4 text-center",children:(0,r.jsxs)(w.$N,{className:"text-lg font-semibold leading-tight flex items-center gap-2 justify-center",children:[r.jsx("i",{className:"fas fa-project-diagram"}),r.jsx("span",{children:"Project Details"})]})}),(0,r.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("strong",{children:"Project Name :"}),r.jsx("span",{children:a?.projectName})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("strong",{children:"Company :"}),r.jsx("span",{children:a?.companyName})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center gap-2",children:[r.jsx("strong",{className:"w-1/3",children:"Description :"}),r.jsx("p",{className:"w-2/3",children:a?.description&&a.description.length>55&&!c?(0,r.jsxs)("span",{children:[a?.description.substring(0,55),"...",r.jsx("button",{onClick:()=>u(!c),className:"ml-2 text-blue-500 cursor-pointer",children:"See More"})]}):(0,r.jsxs)("span",{children:[a?.description,a?.description&&a.description.length>55&&r.jsx("button",{onClick:()=>u(!c),className:"ml-2 text-blue-500 cursor-pointer",children:c?"See Less":"See More"})]})})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("strong",{children:"Skills Required :"}),r.jsx("span",{children:a?.skillsRequired?.length?a?.skillsRequired?.join(", ")??"Not specified":"Not specified"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("strong",{children:"Status :"}),r.jsx("span",{className:"font-medium",children:a?.status})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("strong",{children:"Created :"}),r.jsx("span",{children:a?.createdAt?new Date(a.createdAt).toLocaleDateString():"N/A"})]})]}),r.jsx("div",{className:"mt-6 flex justify-end",children:r.jsx(N.z,{className:"border border-gray-400 rounded-lg px-4 py-2 transition-transform transform hover:scale-105 shadow-sm",onClick:h,children:"Close"})})]})]}):r.jsx(j.default,{href:`/freelancer/project/${e._id}`,children:r.jsx(N.z,{size:"sm",variant:"outline",children:"View Details"})})})]},e._id)):r.jsx("tr",{children:(0,r.jsxs)("td",{colSpan:6,className:"text-center py-10",children:[r.jsx(f.Z,{className:"mx-auto text-gray-500",size:"100"}),r.jsx("p",{className:"text-gray-500",children:"No projects available"})]})})})]})})]})};var M=s(41190),D=s(44794),S=s(88295),$=s.n(S),C=s(83855),P=s(35047),Z=s(84097);let E=function({isOpen:e,onClose:t}){let s=(0,P.useRouter)(),a=(0,P.useSearchParams)(),[i,n]=(0,d.useState)(""),[l,c]=(0,d.useState)(""),[o,u]=(0,d.useState)($()().add(1,"day").format("YYYY-MM-DD")),[h,x]=(0,d.useState)($()().add(1,"day").add(1,"hour").format("YYYY-MM-DD")),[p,f]=(0,d.useState)([""]),j=e=>{let t=Object.fromEntries(a.entries());t.code?g(e,t.code):v()},g=(e,t)=>{m.b.post("/meeting",e,{params:{code:t}})},v=async()=>{try{let e=window.location.origin+window.location.pathname,t=(await m.b.get("/meeting/auth-url",{params:{redirectUri:e}})).data.url;t&&s.push(t)}catch(e){console.error("Error fetching Google Auth URL:",e),(0,Z.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}},y=()=>{f([...p,""])},b=(e,t)=>{let s=[...p];s[e]=t,f(s)};return r.jsx(w.Vq,{open:e,onOpenChange:t,children:(0,r.jsxs)(w.cZ,{className:"sm:max-w-[425px]",children:[(0,r.jsxs)(w.fK,{children:[r.jsx(w.$N,{children:"Create a Meeting"}),r.jsx(w.Be,{children:"Fill in the details below to schedule a new meeting."})]}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),j({summary:i,description:l,start:{dateTime:$()(o).toISOString(),timeZone:"Asia/Kolkata"},end:{dateTime:$()(h).toISOString(),timeZone:"Asia/Kolkata"},attendees:p})},className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[r.jsx(D.Label,{htmlFor:"summary",className:"text-right",children:"Summary"}),r.jsx(M.I,{id:"summary",value:i,onChange:e=>n(e.target.value),className:"col-span-3",placeholder:"Meeting Summary",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[r.jsx(D.Label,{htmlFor:"description",className:"text-right",children:"Description"}),r.jsx(M.I,{id:"description",value:l,onChange:e=>c(e.target.value),className:"col-span-3",placeholder:"Meeting Description",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[r.jsx(D.Label,{htmlFor:"start-date",className:"text-right",children:"Start Date"}),r.jsx(M.I,{type:"date",value:o,onChange:e=>u(e.target.value),className:"col-span-3",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[r.jsx(D.Label,{htmlFor:"end-date",className:"text-right",children:"End Date"}),r.jsx(M.I,{type:"date",value:h,onChange:e=>x(e.target.value),className:"col-span-3",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[r.jsx(D.Label,{htmlFor:"start-time",className:"text-right",children:"Start Time"}),r.jsx(M.I,{type:"time",className:"col-span-3",value:$()(o).format("HH:mm"),onChange:e=>{let[t,s]=e.target.value.split(":").map(Number);u($()(o).set("hour",t).set("minute",s).format("YYYY-MM-DDTHH:mm"))},required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[r.jsx(D.Label,{htmlFor:"start-time",className:"text-right",children:"End Time"}),r.jsx(M.I,{type:"time",className:"col-span-3",value:$()(o).format("HH:mm"),onChange:e=>{let[t,s]=e.target.value.split(":").map(Number);u($()(o).set("hour",t).set("minute",s).format("YYYY-MM-DDTHH:mm"))},required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-start gap-3",children:[r.jsx(D.Label,{htmlFor:"attendees",className:"text-right",children:"Attendees"}),r.jsx("div",{className:"col-span-3 space-y-2",children:p.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(M.I,{value:e,onChange:e=>b(t,e.target.value),placeholder:"Enter attendee email",className:"flex-grow",required:!0}),t===p.length-1&&r.jsx(N.z,{type:"button",onClick:y,className:"ml-2 flex-shrink-0",children:r.jsx(C.Z,{className:"h-4 w-4"})})]},t))})]}),r.jsx(w.cN,{className:"flex justify-center",children:r.jsx(N.z,{type:"submit",children:"Create Meeting"})})]})]})})};var A=s(39958),O=s(40588),V=s(56627);let I=({userId:e})=>{let[t,s]=(0,d.useState)(null),[a,i]=(0,d.useState)(0),[n,l]=(0,d.useState)({}),c=(0,P.useRouter)();(0,d.useEffect)(()=>{let t=async()=>{try{let t=(await m.b.get(`/freelancer/${e}`)).data.data;s(t);let{percentage:r,fields:a}=u(t);i(r),l(a)}catch(e){console.error("Error fetching user profile:",e),(0,V.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}};e&&t()},[e]);let u=e=>{let t={firstName:!!e.firstName?.trim(),lastName:!!e.lastName?.trim(),userName:!!e.userName?.trim(),email:!!e.email?.trim(),phone:!!e.phone?.trim(),profilePic:!!e.profilePic?.trim(),description:!!e.description?.trim(),skills:Array.isArray(e.skills)&&e.skills.length>0,domain:Array.isArray(e.domain)&&e.domain.length>0,projectDomain:Array.isArray(e.projectDomain)&&e.projectDomain.length>0,kycApplied:!!(e.kyc&&"NOT_APPLIED"!==e.kyc.status),kycVerified:!!(e.kyc&&"VERIFIED"===e.kyc.status)},s=Object.keys(t).length;return{percentage:Object.values(t).filter(Boolean).length/s*100,fields:t}};if(!t)return(0,r.jsxs)(o.Zb,{className:"w-full",children:[r.jsx(o.Ol,{className:"pb-2",children:r.jsx("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"space-y-1",children:[r.jsx(o.ll,{className:"text-2xl",children:"Profile Completion"}),r.jsx(o.SZ,{children:"Complete your profile to increase visibility"})]})})}),r.jsx(o.aY,{children:r.jsx(b.O,{className:"w-[100px] h-[20px] rounded-full"})})]});let h=n?Object.entries(n).filter(([,e])=>!e).map(([e])=>{let t=e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase());return"kycApplied"===e?"KYC Application":"kycVerified"===e?"KYC Verification":t}):[];return(0,r.jsxs)(o.Zb,{className:"w-full border-2",children:[r.jsx(o.Ol,{className:"pb-2",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[r.jsx(o.ll,{className:"text-2xl",children:"Profile Completion"}),r.jsx(o.SZ,{children:"Complete your profile to increase visibility"})]}),r.jsx(N.z,{className:"w-32",onClick:()=>c.push("/freelancer/settings/personal-info"),variant:"default",children:"Complete Profile"})]})}),r.jsx(o.aY,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("div",{className:"space-y-2",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("div",{className:"w-full mr-3",children:r.jsx("div",{className:"h-3 w-full overflow-hidden rounded-full bg-muted",children:r.jsx("div",{className:"h-full bg-primary transition-all duration-300 ease-in-out",style:{width:`${a}%`}})})}),(0,r.jsxs)("span",{className:"text-xl font-bold whitespace-nowrap",children:[a.toFixed(0),"%"]})]})}),h.length>0&&a<100&&(0,r.jsxs)("div",{className:"mt-2 text-sm text-muted-foreground",children:[r.jsx("p",{children:"Missing information:"}),(0,r.jsxs)("ul",{className:"list-disc pl-5 mt-1",children:[h.slice(0,3).map((e,t)=>r.jsx("li",{children:e},t)),h.length>3&&(0,r.jsxs)("li",{children:["And ",h.length-3," more..."]})]})]})]})})]})};function _(){let e=(0,c.v9)(e=>e.user);(0,c.I0)();let[t,s]=(0,d.useState)([]),[f,j]=(0,d.useState)([]),[g,v]=(0,d.useState)([]),[y,w]=(0,d.useState)(!1),[M,D]=(0,d.useState)("ACTIVE"),[S,$]=(0,d.useState)(!0),[C,P]=(0,d.useState)(!0),Z=async e=>{$(!0);try{let t=await m.b.get(`/freelancer/project?status=${e}`);200==t.status&&t?.data?.data&&s(t.data.data)}catch(e){(0,V.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.error("API Error:",e)}finally{$(!1)}},_=e=>{D(e),Z(e)};return(0,r.jsxs)("div",{className:"flex min-h-screen  w-full flex-col bg-muted/40",children:[r.jsx(x.Z,{menuItemsTop:p.yn,menuItemsBottom:p.$C,active:"Dashboard"}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[r.jsx(O.Z,{menuItemsTop:p.yn,menuItemsBottom:p.$C,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"}]}),(0,r.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8 lg:grid-cols-3 xl:grid-cols-3",children:[(0,r.jsxs)("div",{className:"grid auto-rows-max items-start gap-4 md:gap-8 lg:col-span-2",children:[r.jsx(I,{userId:e.uid}),(0,r.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-2 xl:grid-cols-4",children:[(0,r.jsxs)(o.Zb,{className:"sm:col-span-2 flex flex-col h-full",children:[r.jsx(o.Ol,{className:"pb-3",children:r.jsx(o.ll,{className:"text-4xl mb-3",children:S?r.jsx(b.O,{className:"h-10 w-20"}):"0"})}),(0,r.jsxs)(o.eW,{className:"grid gap-4 grid-cols-4",children:[(0,r.jsxs)("div",{className:"col-span-3",children:[r.jsx(o.ll,{children:"Total Earnings"}),r.jsx(o.SZ,{className:"max-w-lg text-balance leading-relaxed",children:S?r.jsx(b.O,{className:"h-5 w-40"}):"Your total earnings from projects."})]}),r.jsx("div",{className:"flex items-end justify-end",children:r.jsx(a.Z,{className:"h-12 w-12 text-muted-foreground"})})]})]}),r.jsx(h.Z,{title:"Active Projects",value:C?"...":f.length,icon:r.jsx(i.Z,{className:"h-6 w-6 text-success"}),additionalInfo:"Earning stats will be here"}),r.jsx(h.Z,{title:"Pending Projects",value:C?"...":g.length,icon:r.jsx(n.Z,{className:"h-6 w-6 text-warning"}),additionalInfo:C?"Loading...":"Project stats will be here"})]}),r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)(u.mQ,{value:M,onValueChange:e=>_(e),children:[r.jsx("div",{className:"flex items-center",children:(0,r.jsxs)(u.dr,{children:[r.jsx(u.SP,{value:A.sB.ACTIVE,children:"Active"}),r.jsx(u.SP,{value:A.sB.PENDING,children:"Pending"}),r.jsx(u.SP,{value:A.sB.COMPLETED,children:"Completed"}),r.jsx(u.SP,{value:A.sB.REJECTED,children:"Rejected"})]})}),r.jsx(u.nU,{value:A.sB.ACTIVE,children:r.jsx(k,{type:"active",projects:t,loading:S})}),r.jsx(u.nU,{value:A.sB.PENDING,children:r.jsx(k,{type:"pending",projects:t,loading:S})}),r.jsx(u.nU,{value:A.sB.COMPLETED,children:r.jsx(k,{type:"completed",projects:t,loading:S})}),r.jsx(u.nU,{value:A.sB.REJECTED,children:r.jsx(k,{type:"rejected",projects:t,loading:S})})]})})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx(o.ll,{className:"group flex items-center gap-2 text-2xl",children:"Interviews"}),(0,r.jsxs)("div",{className:"text-center py-10",children:[r.jsx(l.Z,{className:"mx-auto mb-2 text-gray-500",size:"100"}),r.jsx("p",{className:"text-gray-500",children:"No interviews scheduled"}),r.jsx(N.z,{className:"mt-3",onClick:()=>{w(!0)},disabled:!0,children:"Create Meet"})]})]})]})]}),r.jsx(E,{isOpen:y,onClose:()=>w(!1)})]})}s(60502)},97265:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var r=s(10326),a=s(29752);function i({title:e,value:t,icon:s,additionalInfo:i}){return(0,r.jsxs)(a.Zb,{"x-chunk":"dashboard-05-chunk-1",children:[(0,r.jsxs)(a.Ol,{className:"pb-2",children:[s,r.jsx(a.SZ,{children:e}),r.jsx(a.ll,{className:"text-4xl",children:t})]}),r.jsx(a.aY,{children:r.jsx("div",{className:"text-xs text-muted-foreground",children:i})})]})}},44794:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Label:()=>d});var r=s(10326),a=s(17577),i=s(34478),n=s(28671),l=s(51223);let c=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...t},s)=>r.jsx(i.f,{ref:s,className:(0,l.cn)(c(),e),...t}));d.displayName=i.f.displayName},38227:(e,t,s)=>{"use strict";s.d(t,{O:()=>i});var r=s(10326),a=s(51223);function i({className:e,...t}){return r.jsx("div",{className:(0,a.cn)("animate-pulse rounded-md bg-primary/10",e),...t})}},50384:(e,t,s)=>{"use strict";s.d(t,{SP:()=>d,dr:()=>c,mQ:()=>l,nU:()=>o});var r=s(10326),a=s(17577),i=s(28407),n=s(51223);let l=i.fC,c=a.forwardRef(({className:e,...t},s)=>r.jsx(i.aV,{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));c.displayName=i.aV.displayName;let d=a.forwardRef(({className:e,...t},s)=>r.jsx(i.xz,{ref:s,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));d.displayName=i.xz.displayName;let o=a.forwardRef(({className:e,...t},s)=>r.jsx(i.VY,{ref:s,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));o.displayName=i.VY.displayName},48586:(e,t,s)=>{"use strict";s.d(t,{yL:()=>N,$C:()=>y,yn:()=>v});var r=s(10326),a=s(95920),i=s(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,i.Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]]),l=(0,i.Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);var c=s(43727);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,i.Z)("TabletSmartphone",[["rect",{width:"10",height:"14",x:"3",y:"8",rx:"2",key:"1vrsiq"}],["path",{d:"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4",key:"1j4zmg"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),o=(0,i.Z)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]]);var u=s(60763);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let h=(0,i.Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);var m=s(40617),x=s(69515),p=s(88378),f=s(40900),j=s(98091),g=s(46226);let v=[{href:"#",icon:r.jsx(g.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:r.jsx(a.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:r.jsx(n,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:r.jsx(l,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:r.jsx(c.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:r.jsx(d,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:r.jsx(o,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:r.jsx(u.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:r.jsx(h,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:r.jsx(m.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:r.jsx(x.Z,{className:"h-5 w-5"}),label:"Notes"}],y=[{href:"/freelancer/settings/personal-info",icon:r.jsx(p.Z,{className:"h-5 w-5"}),label:"Settings"}];g.default,a.Z,x.Z,f.Z,j.Z;let N=[{href:"#",icon:r.jsx(g.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:r.jsx(a.Z,{className:"h-5 w-5"}),label:"Home"}]},39958:(e,t,s)=>{"use strict";var r,a,i;s.d(t,{cd:()=>r,d8:()=>n,kJ:()=>a,sB:()=>i}),function(e){e.Mastery="Mastery",e.Proficient="Proficient",e.Beginner="Beginner"}(r||(r={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.REJECTED="Rejected",e.COMPLETED="Completed"}(a||(a={})),function(e){e.ACTIVE="ACTIVE",e.PENDING="PENDING",e.REJECTED="REJECTED",e.COMPLETED="COMPLETED"}(i||(i={}));let n={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},36972:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var r=s(68570);let a=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\dashboard\freelancer\page.tsx`),{__esModule:i,$$typeof:n}=a;a.default;let l=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\dashboard\freelancer\page.tsx#default`)},34478:(e,t,s)=>{"use strict";s.d(t,{f:()=>l});var r=s(17577),a=s(77335),i=s(10326),n=r.forwardRef((e,t)=>(0,i.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n},28407:(e,t,s)=>{"use strict";s.d(t,{VY:()=>Z,aV:()=>C,fC:()=>$,xz:()=>P});var r=s(17577),a=s(82561),i=s(93095),n=s(15594),l=s(9815),c=s(77335),d=s(17124),o=s(52067),u=s(88957),h=s(10326),m="Tabs",[x,p]=(0,i.b)(m,[n.Pc]),f=(0,n.Pc)(),[j,g]=x(m),v=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,onValueChange:a,defaultValue:i,orientation:n="horizontal",dir:l,activationMode:m="automatic",...x}=e,p=(0,d.gm)(l),[f,g]=(0,o.T)({prop:r,onChange:a,defaultProp:i});return(0,h.jsx)(j,{scope:s,baseId:(0,u.M)(),value:f,onValueChange:g,orientation:n,dir:p,activationMode:m,children:(0,h.jsx)(c.WV.div,{dir:p,"data-orientation":n,...x,ref:t})})});v.displayName=m;var y="TabsList",N=r.forwardRef((e,t)=>{let{__scopeTabs:s,loop:r=!0,...a}=e,i=g(y,s),l=f(s);return(0,h.jsx)(n.fC,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:r,children:(0,h.jsx)(c.WV.div,{role:"tablist","aria-orientation":i.orientation,...a,ref:t})})});N.displayName=y;var b="TabsTrigger",w=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,disabled:i=!1,...l}=e,d=g(b,s),o=f(s),u=D(d.baseId,r),m=S(d.baseId,r),x=r===d.value;return(0,h.jsx)(n.ck,{asChild:!0,...o,focusable:!i,active:x,children:(0,h.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":m,"data-state":x?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...l,ref:t,onMouseDown:(0,a.M)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(r)}),onKeyDown:(0,a.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(r)}),onFocus:(0,a.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;x||i||!e||d.onValueChange(r)})})})});w.displayName=b;var k="TabsContent",M=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,forceMount:i,children:n,...d}=e,o=g(k,s),u=D(o.baseId,a),m=S(o.baseId,a),x=a===o.value,p=r.useRef(x);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,h.jsx)(l.z,{present:i||x,children:({present:s})=>(0,h.jsx)(c.WV.div,{"data-state":x?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":u,hidden:!s,id:m,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:s&&n})})});function D(e,t){return`${e}-trigger-${t}`}function S(e,t){return`${e}-content-${t}`}M.displayName=k;var $=v,C=N,P=w,Z=M}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588],()=>s(46893));module.exports=r})();