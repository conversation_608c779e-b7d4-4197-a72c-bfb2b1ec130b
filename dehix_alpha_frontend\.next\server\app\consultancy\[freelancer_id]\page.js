(()=>{var e={};e.id=9651,e.ids=[9651],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},28565:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>o}),t(52790),t(54302),t(12523);var r=t(23191),a=t(88716),l=t(37922),n=t.n(l),i=t(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let o=["",{children:["consultancy",{children:["[freelancer_id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,52790)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\consultancy\\[freelancer_id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\consultancy\\[freelancer_id]\\page.tsx"],x="/consultancy/[freelancer_id]/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/consultancy/[freelancer_id]/page",pathname:"/consultancy/[freelancer_id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},65315:(e,s,t)=>{Promise.resolve().then(t.bind(t,59355))},71475:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(80851).Z)("Boxes",[["path",{d:"M2.97 12.92A2 2 0 0 0 2 14.63v3.24a2 2 0 0 0 .97 1.71l3 1.8a2 2 0 0 0 2.06 0L12 19v-5.5l-5-3-4.03 2.42Z",key:"lc1i9w"}],["path",{d:"m7 16.5-4.74-2.85",key:"1o9zyk"}],["path",{d:"m7 16.5 5-3",key:"va8pkn"}],["path",{d:"M7 16.5v5.17",key:"jnp8gn"}],["path",{d:"M12 13.5V19l3.97 2.38a2 2 0 0 0 2.06 0l3-1.8a2 2 0 0 0 .97-1.71v-3.24a2 2 0 0 0-.97-1.71L17 10.5l-5 3Z",key:"8zsnat"}],["path",{d:"m17 16.5-5-3",key:"8arw3v"}],["path",{d:"m17 16.5 4.74-2.85",key:"8rfmw"}],["path",{d:"M17 16.5v5.17",key:"k6z78m"}],["path",{d:"M7.97 4.42A2 2 0 0 0 7 6.13v4.37l5 3 5-3V6.13a2 2 0 0 0-.97-1.71l-3-1.8a2 2 0 0 0-2.06 0l-3 1.8Z",key:"1xygjf"}],["path",{d:"M12 8 7.26 5.15",key:"1vbdud"}],["path",{d:"m12 8 4.74-2.85",key:"3rx089"}],["path",{d:"M12 13.5V8",key:"1io7kd"}]])},7027:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(80851).Z)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},23015:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(80851).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},60763:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(80851).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},59355:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var r=t(10326),a=t(17577),l=t(71475),n=t(95920),i=t(23015),c=t(94019),o=t(83855),d=t(74064),x=t(74723),m=t(27256),u=t(25842),p=t(7027),h=t(29752),j=t(38443);let f=({name:e,skills:s,domains:t,description:a,urls:l,perHourRate:n})=>{let i=s.split(",").map(e=>e.trim()),c=t.split(",").map(e=>e.trim());return(0,r.jsxs)(h.Zb,{className:"mb-4 bg-black text-white",children:[(0,r.jsxs)(h.Ol,{className:"p-4 border-b border-gray-700",children:[r.jsx(h.ll,{className:"text-xl font-semibold",children:e}),(0,r.jsxs)(h.SZ,{className:"text-sm text-gray-400 mt-1",children:[i.map(e=>r.jsx(j.C,{className:"mr-2",children:e},e)),c.map(e=>r.jsx(j.C,{className:"mr-2",children:e},e))]})]}),(0,r.jsxs)(h.aY,{className:"p-4",children:[a&&r.jsx("p",{className:"mb-2",children:a}),l&&l.length>0&&(0,r.jsxs)("div",{className:"mb-2",children:[r.jsx("p",{className:"font-semibold text-gray-300 mb-1",children:"URLs:"}),r.jsx("ul",{className:"list-none pl-0",children:l.map((e,s)=>r.jsx("li",{className:"mb-1",children:(0,r.jsxs)("a",{href:e.value,target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 flex items-center",children:[r.jsx(p.Z,{className:"mr-1 w-4 h-4 text-gray-400"}),e.value]})},s))})]})]}),r.jsx(h.eW,{className:"p-4 border-t border-gray-700",children:void 0!==n&&(0,r.jsxs)("p",{className:"mt-2",children:[r.jsx("strong",{children:"Per Hour Rate:"})," $",n]})})]})};var N=t(92166),g=t(91664),v=t(24118),E=t(9969),y=t(29280);t(6260);var b=t(21486),I=t(78062),C=t(39958),P=t(41190),D=t(40588),w=t(56627);let A=m.z.object({name:m.z.string().min(1,"Name is required"),skills:m.z.string().min(1,"Skill is required"),domains:m.z.string().min(1,"Domain is required"),description:m.z.string().optional(),urls:m.z.array(m.z.object({value:m.z.string().url({message:"Please enter a valid URL."})})).optional(),perHourRate:m.z.preprocess(e=>""===e?void 0:Number(e),m.z.number().min(0,"Per hour rate must be a positive number")).optional()});function k(){let[e,s]=(0,a.useState)(!1),[t,m]=(0,a.useState)([]),[p,j]=(0,a.useState)([]),[k,R]=(0,a.useState)([]);(0,u.v9)(e=>e.user);let[T,_]=(0,a.useState)([]),L=T.filter(e=>e.status==C.kJ.COMPLETED),V=T.filter(e=>e.status!==C.kJ.COMPLETED),S=(0,x.cI)({resolver:(0,d.F)(A),defaultValues:{name:"",skills:"",description:"",urls:[{value:""}],perHourRate:void 0},mode:"all"}),{fields:q,append:z,remove:M}=(0,x.Dq)({name:"urls",control:S.control}),O=[{href:"#",icon:r.jsx(l.Z,{className:"h-4 w-4 transition-all group-hover:scale-110"}),label:"Dehix"}],Z=[{href:"/dashboard/freelancer",icon:r.jsx(n.Z,{className:"h-5 w-5"}),label:"Settings"}],G=async e=>{try{m([...t,e]),S.reset(),s(!1)}catch(e){(0,w.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.error("Error:",e)}};return(0,r.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[r.jsx(N.Z,{menuItemsTop:O,menuItemsBottom:Z,active:"Consultancy Info"}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-4 sm:py-4 sm:pl-14 mb-8",children:[r.jsx(D.Z,{menuItemsTop:O,menuItemsBottom:Z,activeMenu:"Consultancy Info",breadcrumbItems:[{label:"Consultancy",link:"#"},{label:"Consultancy Info",link:"#"}]}),(0,r.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8 lg:grid-cols-3 xl:grid-cols-3",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 xl:col-span-2 space-y-4",children:[(0,r.jsxs)(v.Vq,{open:e,onOpenChange:s,children:[r.jsx(v.hg,{asChild:!0,children:r.jsx(g.z,{children:"Add Consultancy"})}),(0,r.jsxs)(v.cZ,{children:[(0,r.jsxs)(v.fK,{children:[r.jsx(v.$N,{children:"Add Consultancy"}),r.jsx(v.Be,{children:"Fill in the details of the consultancy below."})]}),r.jsx(E.l0,{...S,children:(0,r.jsxs)("form",{onSubmit:S.handleSubmit(G),className:"space-y-4",children:[r.jsx(E.Wi,{control:S.control,name:"name",render:({field:e})=>(0,r.jsxs)(E.xJ,{children:[r.jsx(E.lX,{children:"Title"}),r.jsx(E.NI,{children:r.jsx(P.I,{placeholder:"Enter Title",...e})}),r.jsx(E.zG,{})]})}),r.jsx(E.Wi,{control:S.control,name:"skills",render:({field:e})=>(0,r.jsxs)(E.xJ,{children:[r.jsx(E.lX,{children:"Skills"}),r.jsx(E.NI,{children:(0,r.jsxs)(y.Ph,{...e,onValueChange:s=>e.onChange(s),children:[r.jsx(y.i4,{children:r.jsx(y.ki,{placeholder:"Select a skill"})}),r.jsx(y.Bw,{children:p.map(e=>r.jsx(y.Ql,{value:e.label,children:e.label},e.label))})]})}),r.jsx(E.zG,{})]})}),r.jsx(E.Wi,{control:S.control,name:"domains",render:({field:e})=>(0,r.jsxs)(E.xJ,{children:[r.jsx(E.lX,{children:"Domains"}),r.jsx(E.NI,{children:(0,r.jsxs)(y.Ph,{...e,onValueChange:s=>e.onChange(s),children:[r.jsx(y.i4,{children:r.jsx(y.ki,{placeholder:"Select a domain"})}),r.jsx(y.Bw,{children:k.map(e=>r.jsx(y.Ql,{value:e.label,children:e.label},e.label))})]})}),r.jsx(E.zG,{})]})}),r.jsx(E.Wi,{control:S.control,name:"description",render:({field:e})=>(0,r.jsxs)(E.xJ,{children:[r.jsx(E.lX,{children:"Description"}),r.jsx(E.NI,{children:r.jsx(P.I,{placeholder:"Enter description",...e})}),r.jsx(E.zG,{})]})}),r.jsx(E.Wi,{control:S.control,name:"perHourRate",render:({field:e})=>(0,r.jsxs)(E.xJ,{children:[r.jsx(E.lX,{children:"Per Hour Rate"}),r.jsx(E.NI,{children:r.jsx(P.I,{type:"number",placeholder:"Enter per hour rate",...e,onChange:s=>e.onChange(s.target.value),value:e.value??""})}),r.jsx(E.zG,{})]})}),(0,r.jsxs)(E.xJ,{children:[r.jsx(E.lX,{children:"URLs"}),r.jsx("br",{}),q.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(E.NI,{children:r.jsx(x.Qr,{name:`urls.${s}.value`,control:S.control,render:({field:e})=>r.jsx(P.I,{placeholder:"Enter URL",...e})})}),r.jsx(g.z,{type:"button",onClick:()=>M(s),size:"icon",children:r.jsx(c.Z,{className:"h-4 w-4"})})]},e.id)),(0,r.jsxs)(g.z,{type:"button",variant:"outline",onClick:()=>z({value:""}),className:"mt-2",children:[r.jsx(o.Z,{className:"h-4 w-4 mr-2"}),"Add URL"]})]}),r.jsx(v.cN,{children:r.jsx(g.z,{type:"submit",children:"Submit"})})]})})]})]}),r.jsx("div",{className:"flex flex-wrap gap-8",children:0==t.length?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[r.jsx(i.Z,{className:"text-gray-500",size:"100"}),r.jsx("p",{className:"text-gray-500",children:"No consultancy added"})]}):r.jsx("div",{children:t.map((e,s)=>r.jsx(f,{name:e.name,skills:e.skills,domains:e.domains,description:e.description,urls:e.urls,perHourRate:e.perHourRate},s))})}),r.jsx(I.Separator,{className:"my-1"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,r.jsxs)("h2",{className:"scroll-m-20 text-3xl font-semibold tracking-tight transition-colors first:mt-0",children:["Current Projects ",`(${V.length})`]}),r.jsx("div",{className:"flex gap-4 overflow-x-scroll no-scrollbar pb-8",children:V.length>0?V.map((e,s)=>r.jsx(b.t,{cardClassName:"min-w-[45%]",project:e},s)):(0,r.jsxs)("div",{className:"text-center py-10 w-[100%] ",children:[r.jsx(i.Z,{className:"mx-auto text-gray-500",size:"100"}),r.jsx("p",{className:"text-gray-500",children:"No projects available"})]})}),r.jsx(I.Separator,{className:"my-1"}),(0,r.jsxs)("h2",{className:"scroll-m-20 text-3xl font-semibold tracking-tight transition-colors first:mt-0",children:["Completed Projects ",`(${L.length})`]}),r.jsx("div",{className:"flex gap-4 overflow-x-scroll no-scrollbar pb-8",children:L.length>0?L.map((e,s)=>r.jsx(b.t,{cardClassName:"min-w-[45%]",project:e},s)):(0,r.jsxs)("div",{className:"text-center py-10 w-[100%] ",children:[r.jsx(i.Z,{className:"mx-auto text-gray-500",size:"100"}),r.jsx("p",{className:"text-gray-500",children:"No projects available"})]})})]})]}),(0,r.jsxs)("div",{className:"lg:col-span-1 xl:col-span-1 space-y-4",children:[r.jsx(h.ll,{className:"group flex items-center gap-2 text-2xl",children:"Consultancy Invitations"}),(0,r.jsxs)("div",{className:"text-center py-10",children:[r.jsx(i.Z,{className:"mx-auto text-gray-500",size:"100"}),r.jsx("p",{className:"text-gray-500",children:"No Consultancy Invitation"})]})]})]})]})]})}},21486:(e,s,t)=>{"use strict";t.d(s,{t:()=>u});var r=t(10326),a=t(90434),l=t(60763),n=t(51223),i=t(91664),c=t(29752),o=t(38443),d=t(71064),x=t(58285),m=t(39958);function u({cardClassName:e,project:s,type:t=x.Dy.BUSINESS,...u}){let{text:p,className:h}=(0,d.S)(s.status);return(0,r.jsxs)(c.Zb,{className:(0,n.cn)("flex flex-col h-[400px]",e),...u,children:[(0,r.jsxs)(c.Ol,{children:[(0,r.jsxs)(c.ll,{className:"flex",children:[s.projectName,"\xa0",s.verified&&r.jsx(l.Z,{className:"text-success"})]}),(0,r.jsxs)(c.SZ,{className:"text-gray-600",children:[r.jsx("p",{className:"my-auto",children:s.createdAt?new Date(s.createdAt).toLocaleDateString():"N/A"}),r.jsx("br",{}),r.jsx(o.C,{className:h,children:p})]})]}),(0,r.jsxs)(c.aY,{className:"grid gap-4 mb-auto flex-grow",children:[(0,r.jsxs)("div",{className:"mb-4 items-start pb-4 last:mb-0 last:pb-0 w-full",children:[r.jsx("span",{className:"flex h-2 w-2 rounded-full"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:s.description?.length>40?`${s.description.slice(0,40)}...`:s.description||"No description available"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Company:"})," ",s.companyName]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Role:"})," ",s.role]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Experience:"})," ",s.experience]}),r.jsx("div",{className:"flex flex-wrap gap-1 mt-2",children:s?.skillsRequired?.map((e,s)=>r.jsx(o.C,{className:"text-xs text-white bg-muted",children:e},s))})]})]}),r.jsx(c.eW,{children:r.jsx(a.default,{href:`/${t.toLocaleLowerCase()}/project/${s._id}`,className:"w-full",children:r.jsx(i.z,{className:`w-full ${s.status===m.sB.COMPLETED&&"bg-green-900 hover:bg-green-700"}`,children:"View full details"})})})]})}},9969:(e,s,t)=>{"use strict";t.d(s,{NI:()=>j,Wi:()=>x,l0:()=>o,lX:()=>h,pf:()=>f,xJ:()=>p,zG:()=>N});var r=t(10326),a=t(17577),l=t(99469),n=t(74723),i=t(51223),c=t(44794);let o=n.RV,d=a.createContext({}),x=({...e})=>r.jsx(d.Provider,{value:{name:e.name},children:r.jsx(n.Qr,{...e})}),m=()=>{let e=a.useContext(d),s=a.useContext(u),{getFieldState:t,formState:r}=(0,n.Gc)(),l=t(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=s;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...l}},u=a.createContext({}),p=a.forwardRef(({className:e,...s},t)=>{let l=a.useId();return r.jsx(u.Provider,{value:{id:l},children:r.jsx("div",{ref:t,className:(0,i.cn)("space-y-2",e),...s})})});p.displayName="FormItem";let h=a.forwardRef(({className:e,...s},t)=>{let{error:a,formItemId:l}=m();return r.jsx(c.Label,{ref:t,className:(0,i.cn)(a&&"text-destructive",e),htmlFor:l,...s})});h.displayName="FormLabel";let j=a.forwardRef(({...e},s)=>{let{error:t,formItemId:a,formDescriptionId:n,formMessageId:i}=m();return r.jsx(l.g7,{ref:s,id:a,"aria-describedby":t?`${n} ${i}`:`${n}`,"aria-invalid":!!t,...e})});j.displayName="FormControl";let f=a.forwardRef(({className:e,...s},t)=>{let{formDescriptionId:a}=m();return r.jsx("p",{ref:t,id:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...s})});f.displayName="FormDescription";let N=a.forwardRef(({className:e,children:s,...t},a)=>{let{error:l,formMessageId:n}=m(),c=l?String(l?.message):s;return c?r.jsx("p",{ref:a,id:n,className:(0,i.cn)("text-sm font-medium text-destructive",e),...t,children:c}):null});N.displayName="FormMessage"},44794:(e,s,t)=>{"use strict";t.r(s),t.d(s,{Label:()=>o});var r=t(10326),a=t(17577),l=t(34478),n=t(28671),i=t(51223);let c=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=a.forwardRef(({className:e,...s},t)=>r.jsx(l.f,{ref:t,className:(0,i.cn)(c(),e),...s}));o.displayName=l.f.displayName},78062:(e,s,t)=>{"use strict";t.r(s),t.d(s,{Separator:()=>i});var r=t(10326),a=t(17577),l=t(90220),n=t(51223);let i=a.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...a},i)=>r.jsx(l.f,{ref:i,decorative:t,orientation:s,className:(0,n.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...a}));i.displayName=l.f.displayName},58285:(e,s,t)=>{"use strict";var r,a,l,n,i,c,o,d,x,m,u;t.d(s,{Dy:()=>m,Dz:()=>p});let p={BATCH:3};(function(e){e.PROJECT_HIRING="PROJECT_HIRING",e.SKILL_INTERVIEW="SKILL_INTERVIEW",e.DOMAIN_INTERVIEW="DOMAIN_INTERVIEW",e.TALENT_INTERVIEW="TALENT_INTERVIEW"})(r||(r={})),function(e){e.ADDED="Added",e.APPROVED="Approved",e.CLOSED="Closed",e.COMPLETED="Completed"}(a||(a={})),function(e){e.ACTIVE="Active",e.IN_ACTIVE="Inactive",e.NOT_VERIFIED="Not Verified"}(l||(l={})),function(e){e.BUSINESS="Business",e.FREELANCER="Freelancer",e.BOTH="Both"}(n||(n={})),function(e){e.ACTIVE="Active",e.IN_ACTIVE="Inactive"}(i||(i={})),function(e){e.APPLIED="APPLIED",e.NOT_APPLIED="NOT_APPLIED",e.APPROVED="APPROVED",e.FAILED="FAILED",e.STOPPED="STOPPED",e.REAPPLIED="REAPPLIED"}(c||(c={})),function(e){e.PENDING="Pending",e.ACCEPTED="Accepted",e.REJECTED="Rejected",e.PANEL="Panel",e.INTERVIEW="Interview"}(o||(o={})),function(e){e.ACTIVE="ACTIVE",e.INACTIVE="INACTIVE",e.ARCHIVED="ARCHIVED"}(d||(d={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.INACTIVE="Inactive",e.CLOSED="Closed"}(x||(x={})),function(e){e.FREELANCER="FREELANCER",e.ADMIN="ADMIN",e.BUSINESS="BUSINESS"}(m||(m={})),function(e){e.CREATED="Created",e.CLOSED="Closed",e.ACTIVE="Active"}(u||(u={}))},39958:(e,s,t)=>{"use strict";var r,a,l;t.d(s,{cd:()=>r,d8:()=>n,kJ:()=>a,sB:()=>l}),function(e){e.Mastery="Mastery",e.Proficient="Proficient",e.Beginner="Beginner"}(r||(r={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.REJECTED="Rejected",e.COMPLETED="Completed"}(a||(a={})),function(e){e.ACTIVE="ACTIVE",e.PENDING="PENDING",e.REJECTED="REJECTED",e.COMPLETED="COMPLETED"}(l||(l={}));let n={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},52790:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>l,default:()=>i});var r=t(68570);let a=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\consultancy\[freelancer_id]\page.tsx`),{__esModule:l,$$typeof:n}=a;a.default;let i=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\consultancy\[freelancer_id]\page.tsx#default`)},34478:(e,s,t)=>{"use strict";t.d(s,{f:()=>i});var r=t(17577),a=t(77335),l=t(10326),n=r.forwardRef((e,s)=>(0,l.jsx)(a.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var i=n},90220:(e,s,t)=>{"use strict";t.d(s,{f:()=>o});var r=t(17577),a=t(77335),l=t(10326),n="horizontal",i=["horizontal","vertical"],c=r.forwardRef((e,s)=>{let{decorative:t,orientation:r=n,...c}=e,o=i.includes(r)?r:n;return(0,l.jsx)(a.WV.div,{"data-orientation":o,...t?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...c,ref:s})});c.displayName="Separator";var o=c}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,6686,4736,6499,8066,588],()=>t(28565));module.exports=r})();