(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2167],{62737:function(t){var e;e=function(){"use strict";var t="millisecond",e="second",n="minute",r="hour",a="week",i="month",s="quarter",u="year",o="date",c="Invalid Date",d=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,h=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,f=function(t,e,n){var r=String(t);return!r||r.length>=e?t:""+Array(e+1-r.length).join(n)+t},l="en",y={};y[l]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(t){var e=["th","st","nd","rd"],n=t%100;return"["+t+(e[(n-20)%10]||e[n]||"th")+"]"}};var p="$isDayjsObject",v=function(t){return t instanceof m||!(!t||!t[p])},M=function t(e,n,r){var a;if(!e)return l;if("string"==typeof e){var i=e.toLowerCase();y[i]&&(a=i),n&&(y[i]=n,a=i);var s=e.split("-");if(!a&&s.length>1)return t(s[0])}else{var u=e.name;y[u]=e,a=u}return!r&&a&&(l=a),a||!r&&l},k=function(t,e){if(v(t))return t.clone();var n="object"==typeof e?e:{};return n.date=t,n.args=arguments,new m(n)},$={s:f,z:function(t){var e=-t.utcOffset(),n=Math.abs(e);return(e<=0?"+":"-")+f(Math.floor(n/60),2,"0")+":"+f(n%60,2,"0")},m:function t(e,n){if(e.date()<n.date())return-t(n,e);var r=12*(n.year()-e.year())+(n.month()-e.month()),a=e.clone().add(r,i),s=n-a<0,u=e.clone().add(r+(s?-1:1),i);return+(-(r+(n-a)/(s?a-u:u-a))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(c){return({M:i,y:u,w:a,d:"day",D:o,h:r,m:n,s:e,ms:t,Q:s})[c]||String(c||"").toLowerCase().replace(/s$/,"")},u:function(t){return void 0===t}};$.l=M,$.i=v,$.w=function(t,e){return k(t,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var m=function(){function f(t){this.$L=M(t.locale,null,!0),this.parse(t),this.$x=this.$x||t.x||{},this[p]=!0}var l=f.prototype;return l.parse=function(t){this.$d=function(t){var e=t.date,n=t.utc;if(null===e)return new Date(NaN);if($.u(e))return new Date;if(e instanceof Date)return new Date(e);if("string"==typeof e&&!/Z$/i.test(e)){var r=e.match(d);if(r){var a=r[2]-1||0,i=(r[7]||"0").substring(0,3);return n?new Date(Date.UTC(r[1],a,r[3]||1,r[4]||0,r[5]||0,r[6]||0,i)):new Date(r[1],a,r[3]||1,r[4]||0,r[5]||0,r[6]||0,i)}}return new Date(e)}(t),this.init()},l.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},l.$utils=function(){return $},l.isValid=function(){return this.$d.toString()!==c},l.isSame=function(t,e){var n=k(t);return this.startOf(e)<=n&&n<=this.endOf(e)},l.isAfter=function(t,e){return k(t)<this.startOf(e)},l.isBefore=function(t,e){return this.endOf(e)<k(t)},l.$g=function(t,e,n){return $.u(t)?this[e]:this.set(n,t)},l.unix=function(){return Math.floor(this.valueOf()/1e3)},l.valueOf=function(){return this.$d.getTime()},l.startOf=function(t,s){var c=this,d=!!$.u(s)||s,h=$.p(t),f=function(t,e){var n=$.w(c.$u?Date.UTC(c.$y,e,t):new Date(c.$y,e,t),c);return d?n:n.endOf("day")},l=function(t,e){return $.w(c.toDate()[t].apply(c.toDate("s"),(d?[0,0,0,0]:[23,59,59,999]).slice(e)),c)},y=this.$W,p=this.$M,v=this.$D,M="set"+(this.$u?"UTC":"");switch(h){case u:return d?f(1,0):f(31,11);case i:return d?f(1,p):f(0,p+1);case a:var k=this.$locale().weekStart||0,m=(y<k?y+7:y)-k;return f(d?v-m:v+(6-m),p);case"day":case o:return l(M+"Hours",0);case r:return l(M+"Minutes",1);case n:return l(M+"Seconds",2);case e:return l(M+"Milliseconds",3);default:return this.clone()}},l.endOf=function(t){return this.startOf(t,!1)},l.$set=function(a,s){var c,d=$.p(a),h="set"+(this.$u?"UTC":""),f=((c={}).day=h+"Date",c[o]=h+"Date",c[i]=h+"Month",c[u]=h+"FullYear",c[r]=h+"Hours",c[n]=h+"Minutes",c[e]=h+"Seconds",c[t]=h+"Milliseconds",c)[d],l="day"===d?this.$D+(s-this.$W):s;if(d===i||d===u){var y=this.clone().set(o,1);y.$d[f](l),y.init(),this.$d=y.set(o,Math.min(this.$D,y.daysInMonth())).$d}else f&&this.$d[f](l);return this.init(),this},l.set=function(t,e){return this.clone().$set(t,e)},l.get=function(t){return this[$.p(t)]()},l.add=function(t,s){var o,c=this;t=Number(t);var d=$.p(s),h=function(e){var n=k(c);return $.w(n.date(n.date()+Math.round(e*t)),c)};if(d===i)return this.set(i,this.$M+t);if(d===u)return this.set(u,this.$y+t);if("day"===d)return h(1);if(d===a)return h(7);var f=((o={})[n]=6e4,o[r]=36e5,o[e]=1e3,o)[d]||1,l=this.$d.getTime()+t*f;return $.w(l,this)},l.subtract=function(t,e){return this.add(-1*t,e)},l.format=function(t){var e=this,n=this.$locale();if(!this.isValid())return n.invalidDate||c;var r=t||"YYYY-MM-DDTHH:mm:ssZ",a=$.z(this),i=this.$H,s=this.$m,u=this.$M,o=n.weekdays,d=n.months,f=n.meridiem,l=function(t,n,a,i){return t&&(t[n]||t(e,r))||a[n].slice(0,i)},y=function(t){return $.s(i%12||12,t,"0")},p=f||function(t,e,n){var r=t<12?"AM":"PM";return n?r.toLowerCase():r};return r.replace(h,function(t,r){return r||function(t){switch(t){case"YY":return String(e.$y).slice(-2);case"YYYY":return $.s(e.$y,4,"0");case"M":return u+1;case"MM":return $.s(u+1,2,"0");case"MMM":return l(n.monthsShort,u,d,3);case"MMMM":return l(d,u);case"D":return e.$D;case"DD":return $.s(e.$D,2,"0");case"d":return String(e.$W);case"dd":return l(n.weekdaysMin,e.$W,o,2);case"ddd":return l(n.weekdaysShort,e.$W,o,3);case"dddd":return o[e.$W];case"H":return String(i);case"HH":return $.s(i,2,"0");case"h":return y(1);case"hh":return y(2);case"a":return p(i,s,!0);case"A":return p(i,s,!1);case"m":return String(s);case"mm":return $.s(s,2,"0");case"s":return String(e.$s);case"ss":return $.s(e.$s,2,"0");case"SSS":return $.s(e.$ms,3,"0");case"Z":return a}return null}(t)||a.replace(":","")})},l.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},l.diff=function(t,o,c){var d,h=this,f=$.p(o),l=k(t),y=(l.utcOffset()-this.utcOffset())*6e4,p=this-l,v=function(){return $.m(h,l)};switch(f){case u:d=v()/12;break;case i:d=v();break;case s:d=v()/3;break;case a:d=(p-y)/6048e5;break;case"day":d=(p-y)/864e5;break;case r:d=p/36e5;break;case n:d=p/6e4;break;case e:d=p/1e3;break;default:d=p}return c?d:$.a(d)},l.daysInMonth=function(){return this.endOf(i).$D},l.$locale=function(){return y[this.$L]},l.locale=function(t,e){if(!t)return this.$L;var n=this.clone(),r=M(t,e,!0);return r&&(n.$L=r),n},l.clone=function(){return $.w(this.$d,this)},l.toDate=function(){return new Date(this.valueOf())},l.toJSON=function(){return this.isValid()?this.toISOString():null},l.toISOString=function(){return this.$d.toISOString()},l.toString=function(){return this.$d.toUTCString()},f}(),g=m.prototype;return k.prototype=g,[["$ms",t],["$s",e],["$m",n],["$H",r],["$W","day"],["$M",i],["$y",u],["$D",o]].forEach(function(t){g[t[1]]=function(e){return this.$g(e,t[0],t[1])}}),k.extend=function(t,e){return t.$i||(t(e,m,k),t.$i=!0),k},k.locale=M,k.isDayjs=v,k.unix=function(t){return k(1e3*t)},k.en=y[l],k.Ls=y,k.p={},k},t.exports=e()},5891:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},76035:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},43193:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]])},43224:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("CalendarX2",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M21 13V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h8",key:"3spt84"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"m17 22 5-5",key:"1k6ppv"}],["path",{d:"m17 17 5 5",key:"p7ous7"}]])},40933:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49100:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},47390:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},29406:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},36141:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},33907:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},73347:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},33149:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]])},40064:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("TabletSmartphone",[["rect",{width:"10",height:"14",x:"3",y:"8",rx:"2",key:"1vrsiq"}],["path",{d:"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4",key:"1j4zmg"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},10883:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},38364:function(t,e,n){"use strict";n.d(e,{f:function(){return u}});var r=n(2265),a=n(18676),i=n(57437),s=r.forwardRef((t,e)=>(0,i.jsx)(a.WV.label,{...t,ref:e,onMouseDown:e=>{var n;e.target.closest("button, input, select, textarea")||(null===(n=t.onMouseDown)||void 0===n||n.call(t,e),!e.defaultPrevented&&e.detail>1&&e.preventDefault())}}));s.displayName="Label";var u=s},62447:function(t,e,n){"use strict";n.d(e,{VY:function(){return _},aV:function(){return V},fC:function(){return C},xz:function(){return O}});var r=n(2265),a=n(78149),i=n(98324),s=n(53398),u=n(31383),o=n(18676),c=n(87513),d=n(91715),h=n(53201),f=n(57437),l="Tabs",[y,p]=(0,i.b)(l,[s.Pc]),v=(0,s.Pc)(),[M,k]=y(l),$=r.forwardRef((t,e)=>{let{__scopeTabs:n,value:r,onValueChange:a,defaultValue:i,orientation:s="horizontal",dir:u,activationMode:l="automatic",...y}=t,p=(0,c.gm)(u),[v,k]=(0,d.T)({prop:r,onChange:a,defaultProp:i});return(0,f.jsx)(M,{scope:n,baseId:(0,h.M)(),value:v,onValueChange:k,orientation:s,dir:p,activationMode:l,children:(0,f.jsx)(o.WV.div,{dir:p,"data-orientation":s,...y,ref:e})})});$.displayName=l;var m="TabsList",g=r.forwardRef((t,e)=>{let{__scopeTabs:n,loop:r=!0,...a}=t,i=k(m,n),u=v(n);return(0,f.jsx)(s.fC,{asChild:!0,...u,orientation:i.orientation,dir:i.dir,loop:r,children:(0,f.jsx)(o.WV.div,{role:"tablist","aria-orientation":i.orientation,...a,ref:e})})});g.displayName=m;var b="TabsTrigger",w=r.forwardRef((t,e)=>{let{__scopeTabs:n,value:r,disabled:i=!1,...u}=t,c=k(b,n),d=v(n),h=S(c.baseId,r),l=Z(c.baseId,r),y=r===c.value;return(0,f.jsx)(s.ck,{asChild:!0,...d,focusable:!i,active:y,children:(0,f.jsx)(o.WV.button,{type:"button",role:"tab","aria-selected":y,"aria-controls":l,"data-state":y?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:h,...u,ref:e,onMouseDown:(0,a.M)(t.onMouseDown,t=>{i||0!==t.button||!1!==t.ctrlKey?t.preventDefault():c.onValueChange(r)}),onKeyDown:(0,a.M)(t.onKeyDown,t=>{[" ","Enter"].includes(t.key)&&c.onValueChange(r)}),onFocus:(0,a.M)(t.onFocus,()=>{let t="manual"!==c.activationMode;y||i||!t||c.onValueChange(r)})})})});w.displayName=b;var D="TabsContent",x=r.forwardRef((t,e)=>{let{__scopeTabs:n,value:a,forceMount:i,children:s,...c}=t,d=k(D,n),h=S(d.baseId,a),l=Z(d.baseId,a),y=a===d.value,p=r.useRef(y);return r.useEffect(()=>{let t=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(t)},[]),(0,f.jsx)(u.z,{present:i||y,children:n=>{let{present:r}=n;return(0,f.jsx)(o.WV.div,{"data-state":y?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":h,hidden:!r,id:l,tabIndex:0,...c,ref:e,style:{...t.style,animationDuration:p.current?"0s":void 0},children:r&&s})}})});function S(t,e){return"".concat(t,"-trigger-").concat(e)}function Z(t,e){return"".concat(t,"-content-").concat(e)}x.displayName=D;var C=$,V=g,O=w,_=x}}]);