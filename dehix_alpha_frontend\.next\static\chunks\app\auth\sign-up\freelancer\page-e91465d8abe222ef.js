(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1238],{76995:function(e,s,t){Promise.resolve().then(t.bind(t,53781))},53781:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return K}});var a=t(57437),r=t(87138),n=t(90564),i=t(2265),l=t(16463),o=t(59772),c=t(44504),d=t(39343),m=t(31014),u=t(52022),h=t(25912),x=t(20500),p=t(13231),f=t(75733),g=t(47019),j=t(3274),b=t(71976),w=t(95137),y=t(22757),v=t(67319),N=t(8185),k=t(49354),C=t(65438),z=t(89733),P=t(15922),Z=t(78068),S=t(70402),_=t(93363),A=t(77209),F=t(60253),Y=t(24241),D=t(6460),I=e=>{let{selectedMonth:s,onSelect:t}=e;return(0,a.jsx)("div",{className:"grid grid-cols-3 gap-4 mb-4",children:Array.from({length:12},(e,s)=>(0,D.WU)(new Date(2e3,s,1),"MMMM")).map((e,r)=>(0,a.jsx)("button",{onClick:()=>t(r),className:"p-2 text-sm rounded-lg hover:bg-blue-600 transition ".concat(s===r?"bg-blue-500 text-white":"text-white"),children:e},e))})},E=t(2128),L=e=>{let{selectedYear:s,onSelect:t}=e,r=new Date().getFullYear()-16,n=Array.from({length:r-1970+1},(e,s)=>r-s).reverse();return(0,a.jsxs)(E.Ph,{onValueChange:e=>t(Number(e)),children:[(0,a.jsx)(E.i4,{className:"w-full",children:(0,a.jsx)(E.ki,{placeholder:s})}),(0,a.jsx)(E.Bw,{children:n.map(e=>(0,a.jsx)(E.Ql,{value:String(e),children:e},e))})]})},R=e=>{let{onConfirm:s}=e;return(0,a.jsx)("button",{onClick:s,className:"mt-4 w-full bg-blue-600 text-white p-2 rounded-lg",children:"Confirm"})},T=t(69081),U=t(21413),M=t(54662),W=e=>{var s,t;let{field:r}=e,n=new Date,l=new Date(n.setFullYear(n.getFullYear()-16)),o=r.value?new Date(r.value):void 0,[c,d]=(0,i.useState)(!1),[m,u]=(0,i.useState)(null!==(s=null==o?void 0:o.getMonth())&&void 0!==s?s:l.getMonth()),[h,x]=(0,i.useState)(null!==(t=null==o?void 0:o.getFullYear())&&void 0!==t?t:l.getFullYear()),[p,f]=(0,i.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(U.J2,{open:p,onOpenChange:f,children:[(0,a.jsx)(U.xo,{asChild:!0,children:(0,a.jsx)(_.NI,{children:(0,a.jsxs)(z.z,{type:"button",variant:"outline",className:"w-full justify-start text-left font-normal ".concat(o?"":"text-muted-foreground"),onClick:()=>d(!0),children:[o?(0,D.WU)(o,"PPP"):"Pick a date",(0,a.jsx)(Y.Z,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),(0,a.jsx)(U.yk,{className:"w-auto p-0",children:(0,a.jsx)(T.f,{mode:"single",selected:o,onSelect:e=>{r.onChange(e),f(!1)},fromYear:1970,toDate:l,defaultMonth:new Date(h,m)})})]}),(0,a.jsx)(M.Vq,{open:c,onOpenChange:d,children:(0,a.jsxs)(M.cZ,{className:"max-w-sm rounded-lg bg-[#111] mx-1 shadow-xl p-6",children:[(0,a.jsx)("h2",{className:"text-lg text-white mb-4",children:"Select Month & Year"}),(0,a.jsx)(I,{selectedMonth:m,onSelect:u}),(0,a.jsx)(L,{selectedYear:h,onSelect:x}),(0,a.jsx)(R,{onConfirm:()=>{d(!1),f(!0)}})]})})]})};function V(e){let{open:s,setOpen:t,setIsChecked:r}=e;return(0,a.jsx)(M.Vq,{open:s,onOpenChange:t,children:(0,a.jsxs)(M.cZ,{className:"max-w-5xl sm:mx-4 max-h-screen overflow-y-auto rounded-2xl p-6 shadow-lg",children:[(0,a.jsx)(M.fK,{children:(0,a.jsx)(M.$N,{className:"text-2xl font-bold  mb-4 text-center",children:"Terms & Conditions for Freelancer"})}),(0,a.jsxs)("div",{className:"space-y-6 text-sm  leading-relaxed px-2 sm:px-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-base mb-1",children:"1. Registration and Account Management"}),(0,a.jsx)("p",{children:"By registering as a freelancer or client on the platform, users agree to abide by these terms and conditions. Users must provide accurate, complete, and up-to-date information during the registration process. Failure to do so may result in account suspension or termination. The platform reserves the right to approve, reject, or terminate user accounts at its sole discretion."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-base mb-1",children:"2. KYC Verification"}),(0,a.jsx)("p",{children:"All users are required to undergo KYC (Know Your Customer) verification to validate their profiles. Providing false or misleading information or documents during the KYC process may result in immediate account termination."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-base mb-1",children:"3. Platform Responsibilities"}),(0,a.jsx)("p",{children:"The platform acts as an intermediary connecting freelancers with potential clients. While the platform facilitates this connection, it does not guarantee job opportunities, successful contracts, or project outcomes. The platform is not liable for disputes, incomplete projects, or any issues arising from freelancer-client engagements."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-base mb-1",children:"4. User Conduct"}),(0,a.jsx)("p",{children:"Users are strictly prohibited from posting fraudulent job offers or misleading information. Spam, offensive language, and abusive behavior are not tolerated in any communications conducted through the platform. Violation of this rule may result in account suspension or termination."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-base mb-1",children:"5. Data Sharing and Privacy"}),(0,a.jsx)("p",{children:"By registering on the platform, users consent to sharing their contact details and profile information with clients seeking talent. The platform ensures that user data is shared only with verified clients for legitimate hiring purposes."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-base mb-1",children:"6. Termination of Accounts"}),(0,a.jsx)("p",{children:"Accounts may be terminated if users violate platform rules, provide false information, or fail to comply with KYC verification. The platform reserves the right to investigate and take appropriate action if fraudulent or suspicious activities are detected."})]}),(0,a.jsx)("div",{children:(0,a.jsx)("p",{children:"By using this platform, all users acknowledge and agree to these terms and conditions. The platform reserves the right to modify these terms at any time with prior notice to users."})})]}),(0,a.jsx)("div",{className:"mt-8 flex justify-end",children:(0,a.jsx)(z.z,{onClick:()=>{r(!0),t(!1)},className:"px-6 py-2 text-sm font-medium rounded-md bg-primary  hover:bg-primary/90 transition",children:"I Accept"})})]})})}let O=e=>{let{currentStep:s=0}=e,t=[{id:0,title:"Personal Info",icon:u.Z},{id:1,title:"Professional Info",icon:h.Z},{id:2,title:"Verification",icon:x.Z}];return(0,a.jsxs)("div",{className:"w-full max-w-5xl mx-auto py-4 sm:py-6 mb-10 sm:mb-8",children:[(0,a.jsxs)("div",{className:"text-center space-y-2 sm:space-y-4",children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold",children:["Create Your Freelancer ",(0,a.jsx)("span",{className:"block",children:"Account"})]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Join our community and start your Freelancing Journey."})]}),(0,a.jsxs)("div",{className:"my-4 text-center text-xs sm:text-sm",children:["Are you a business?"," ",(0,a.jsx)(z.z,{variant:"outline",size:"sm",className:"ml-2",asChild:!0,children:(0,a.jsx)(r.default,{href:"/auth/sign-up/business",children:"Register Business"})})]}),(0,a.jsx)("div",{className:"flex items-center justify-center mt-4 sm:mt-8 px-2 sm:px-0",children:t.map((e,r)=>(0,a.jsxs)(i.Fragment,{children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-8 h-8 sm:w-12 sm:h-12 flex items-center justify-center rounded-full border-2 transition-all duration-300\n                ".concat(s>e.id?"bg-primary border-primary":s===e.id?"border-primary bg-background text-primary":"border-muted bg-background text-muted"),children:s>e.id?(0,a.jsx)(p.Z,{className:"w-4 h-4 sm:w-6 sm:h-6 text-background"}):(0,a.jsx)(e.icon,{className:"w-4 h-4 sm:w-6 sm:h-6"})}),(0,a.jsx)("span",{className:"absolute -bottom-6 left-1/2 -translate-x-1/2 text-xs sm:text-sm whitespace-nowrap font-medium\n                ".concat(s>=e.id?"text-primary":"text-muted-foreground"),children:e.title})]}),r<t.length-1&&(0,a.jsx)("div",{className:"w-20 sm:w-40 mx-2 sm:mx-4 h-[2px] bg-muted",children:(0,a.jsx)("div",{className:"h-full bg-primary transition-all duration-500",style:{width:s>e.id?"100%":"0%"}})})]},e.id))})]})},$=(e,s)=>e.getFullYear()-s.getFullYear()-(e<new Date(e.getFullYear(),s.getMonth(),s.getDate())?1:0),H=o.z.object({firstName:o.z.string().min(2,{message:"First Name must be at least 2 characters."}),lastName:o.z.string().min(2,{message:"Last Name must be at least 2 characters."}),email:o.z.string().email({message:"Email must be a valid email address."}),userName:o.z.string().min(4,{message:"Username must be at least 4 characters long"}).max(20,{message:"Username must be less than 20 characters long"}).regex(/^[a-zA-Z0-9]{4}[a-zA-Z0-9_]*$/,{message:"Underscore allowed only after 4 letters/numbers"}),phone:o.z.string().min(10,{message:"Phone number must be at least 10 digits."}).regex(/^\d+$/,{message:"Phone number can only contain digits."}),githubLink:o.z.string().optional().refine(e=>!e||/^https?:\/\/(www\.)?github\.com\/[a-zA-Z0-9_-]+\/?$/.test(e),{message:'GitHub URL must start with "https://github.com/" or "www.github.com/" and have a valid username'}),resume:o.z.string().optional().refine(e=>!e||/^https?:\/\/[^\s$.?#].[^\s]*$/.test(e),{message:"Resume must be a valid URL."}),linkedin:o.z.string().optional().refine(e=>!e||/^https:\/\/www\.linkedin\.com\/in\/[a-zA-Z0-9_-]+\/?$/.test(e),{message:'LinkedIn URL must start with "https://www.linkedin.com/in/" and have a valid username'}),personalWebsite:o.z.string().optional().refine(e=>!e||/^(https?:\/\/|www\.)[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}.*[a-zA-Z0-9].*$/.test(e),{message:'Invalid website URL. Must start with "www." or "https://" and contain letters'}),password:o.z.string().min(6,{message:"Password must be at least 6 characters."}),perHourPrice:o.z.number().max(300,"Per hour price must not excedd 300").refine(e=>e>=0,{message:"Price must be a non-negative number."}),referralCode:o.z.string().optional(),workExperience:o.z.number().min(0,"Work experience must be at least 0 years").max(60,"Work experience must not exceed 60 years"),dob:o.z.union([o.z.string(),o.z.date()]).optional().refine(e=>{if(!e)return!0;let s=new Date(e),t=new Date,a=new Date;return a.setFullYear(t.getFullYear()-16),s<=a},{message:"You must be at least 16 years old"}),confirmPassword:o.z.string().min(6,"Confirm Password must be at least 6 characters long")}).refine(e=>e.password===e.confirmPassword,{path:["confirmPassword"],message:"Passwords do not match"}).refine(e=>{if(!e.dob)return!0;let s=new Date(e.dob),t=$(new Date,s);return e.workExperience<=t},{path:["workExperience"],message:"Work experience cannot be greater than your age"});function J(){let[e,s]=(0,i.useState)(0);return(0,a.jsx)("div",{className:"flex w-full items-center justify-center",children:(0,a.jsxs)("div",{className:"w-full max-w-5xl px-4 sm:px-6 lg:px-4",children:[(0,a.jsx)(O,{currentStep:e}),(0,a.jsx)("div",{className:"flex justify-center w-full",children:(0,a.jsx)("div",{className:"w-full max-w-4xl",children:(0,a.jsx)(B,{currentStep:e,setCurrentStep:s})})})]})})}function B(e){let{currentStep:s,setCurrentStep:t}=e,[r,n]=(0,i.useState)(!1),[o,u]=(0,i.useState)(!1),[h,x]=(0,i.useState)("IN"),[p,Y]=(0,i.useState)(""),[D,I]=(0,i.useState)(!1),[E,L]=(0,i.useState)(!1),[R,T]=(0,i.useState)(!1),U=(0,l.useSearchParams)(),[M,O]=(0,i.useState)(!1),[$,J]=(0,i.useState)(null),B=()=>{u(e=>!e)},K=(0,d.cI)({resolver:(0,m.F)(H),defaultValues:{firstName:"",lastName:"",email:"",userName:"",phone:"",githubLink:"",resume:"",linkedin:"",personalWebsite:"",password:"",perHourPrice:0,workExperience:0,referralCode:"",dob:""},mode:"all"}),q=async()=>{t(s-1)},G=async()=>{if(0===s)await K.trigger(["firstName","lastName","email","dob","userName","password","confirmPassword"])?t(s+1):(0,Z.Am)({variant:"destructive",title:"Validation Error",description:"Please fill in all required fields before proceeding."});else if(1===s){if(await K.trigger(["githubLink","linkedin","personalWebsite","perHourPrice","resume","workExperience"])){let{userName:e}=K.getValues();T(!0);try{if(e===$){t(s+1);return}let a=await P.b.get("/public/username/check-duplicate?username=".concat(e,"&is_freelancer=true"));!1===a.data.duplicate?t(s+1):((0,Z.Am)({variant:"destructive",title:"User Already Exists",description:"This username is already taken. Please choose another one."}),J(e))}catch(e){(0,Z.Am)({variant:"destructive",title:"API Error",description:"There was an error while checking the username."})}finally{T(!1)}}else(0,Z.Am)({variant:"destructive",title:"Validation Error",description:"Please fill in all required fields before proceeding."})}},Q=async e=>{var s,t,r,i;let l=U.get("referral"),o=e.referralCode,d=l||o||null;Y("".concat(null===(s=v.find(e=>e.code===h))||void 0===s?void 0:s.dialCode).concat(e.phone)),n(!0);let m={...e,phone:"".concat(null===(t=v.find(e=>e.code===h))||void 0===t?void 0:t.dialCode).concat(e.phone),phoneVerify:!1,role:"freelancer",connects:0,professionalInfo:{},skills:[],domain:[],education:{},projects:{},isFreelancer:!0,refer:{name:"string",contact:"string"},pendingProject:[],rejectedProject:[],acceptedProject:[],oracleProject:[],userDataForVerification:[],interviewsAligned:[],dob:e.dob?new Date(e.dob).toISOString():null};try{await P.b.post(d?"/register/freelancer?referralCode=".concat(d):"/register/freelancer",m),(0,Z.Am)({title:"Account created successfully!",description:"Redirecting to login page..."}),I(!0)}catch(s){let e=(null===(i=s.response)||void 0===i?void 0:null===(r=i.data)||void 0===r?void 0:r.message)||"Something went wrong!";console.error("API Error:",s),(0,Z.Am)({variant:"destructive",title:"Uh oh! Something went wrong.",description:e,action:(0,a.jsx)(c.gD,{altText:"Try again",children:"Try again"})})}finally{setTimeout(()=>n(!1),100)}};return(0,a.jsx)(_.l0,{...K,children:(0,a.jsx)("form",{onSubmit:K.handleSubmit(Q),className:"w-full max-w-3xl mx-auto",children:(0,a.jsx)("div",{className:"w-full p-4 sm:p-6 rounded-lg shadow-sm border",children:(0,a.jsxs)("div",{className:"grid gap-4 sm:gap-6 w-full",children:[(0,a.jsxs)("div",{className:(0,k.cn)("grid gap-4",0===s?"":"hidden"),children:[(0,a.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,a.jsx)(C.Z,{control:K.control,name:"firstName",label:"First Name",placeholder:"Max",className:"w-full"}),(0,a.jsx)(C.Z,{control:K.control,name:"lastName",label:"Last Name",placeholder:"Robinson",className:"w-full"})]}),(0,a.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,a.jsx)(C.Z,{control:K.control,name:"email",label:"Email",placeholder:"<EMAIL>",type:"email"}),(0,a.jsxs)("div",{className:"flex flex-col gap-2 mt-1",children:[(0,a.jsx)(S.Label,{className:"text-sm font-medium",children:"Date of Birth"}),(0,a.jsx)(d.Qr,{control:K.control,name:"dob",render:e=>{let{field:s}=e;return(0,a.jsx)(W,{field:s})}})]})]}),(0,a.jsx)(C.Z,{control:K.control,name:"userName",label:"Username",placeholder:"JohnDoe123"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.Label,{children:"Password"}),(0,a.jsx)(_.Wi,{control:K.control,name:"password",render:e=>{let{field:s}=e;return(0,a.jsxs)(_.xJ,{children:[(0,a.jsx)(_.NI,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(A.I,{placeholder:"Enter your password",type:o?"text":"password",className:"pr-10",...s}),(0,a.jsx)("button",{type:"button",onClick:B,className:"absolute inset-y-0 right-0 px-3 flex items-center",children:o?(0,a.jsx)(f.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"}):(0,a.jsx)(g.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"})})]})}),(0,a.jsx)(_.zG,{})]})}})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.Label,{children:"Confirm Password"}),(0,a.jsx)(_.Wi,{control:K.control,name:"confirmPassword",render:e=>{let{field:s}=e;return(0,a.jsxs)(_.xJ,{children:[(0,a.jsx)(_.NI,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(A.I,{placeholder:"Confirm your password",type:o?"text":"password",className:"pr-10",...s}),(0,a.jsx)("button",{type:"button",onClick:B,className:"absolute inset-y-0 right-0 px-3 flex items-center",children:o?(0,a.jsx)(f.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"}):(0,a.jsx)(g.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"})})]})}),(0,a.jsx)(_.zG,{})]})}})]}),(0,a.jsx)("div",{className:"flex gap-2 justify-end mt-4",children:(0,a.jsx)(z.z,{type:"button",onClick:G,className:"w-full sm:w-auto flex items-center justify-center",disabled:R,children:R?(0,a.jsx)(j.Z,{size:20,className:"animate-spin"}):(0,a.jsxs)(a.Fragment,{children:["Next",(0,a.jsx)(b.Z,{className:"w-4 h-4 ml-2"})]})})})]}),(0,a.jsxs)("div",{className:(0,k.cn)("grid gap-4",1===s?"":"hidden"),children:[(0,a.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,a.jsx)(C.Z,{control:K.control,name:"githubLink",label:"GitHub",type:"url",placeholder:"https://github.com/yourusername",className:"w-full"}),(0,a.jsx)(C.Z,{control:K.control,name:"referralCode",label:"Referral",type:"string",placeholder:"JOHN123",className:"w-full"})]}),(0,a.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,a.jsx)(C.Z,{control:K.control,name:"linkedin",label:"LinkedIn",type:"url",placeholder:"https://linkedin.com/in/yourprofile",className:"w-full"}),(0,a.jsx)(C.Z,{control:K.control,name:"personalWebsite",label:"Personal Website",type:"url",placeholder:"https://www.yourwebsite.com",className:"w-full"})]}),(0,a.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,a.jsx)(C.Z,{control:K.control,name:"perHourPrice",label:"Hourly Rate ($)",type:"number",placeholder:"0",className:"w-full"}),(0,a.jsx)(C.Z,{control:K.control,name:"resume",label:"Resume (URL)",type:"url",placeholder:"Enter Google Drive Resume Link",className:"w-full"})]}),(0,a.jsx)("div",{className:"grid gap-4 sm:grid-cols-2",children:(0,a.jsx)(C.Z,{control:K.control,name:"workExperience",label:"Work Experience (Years)",type:"number",placeholder:"0",className:"w-full"})}),(0,a.jsxs)("div",{className:"flex gap-2 justify-between mt-4",children:[(0,a.jsxs)(z.z,{type:"button",onClick:q,className:"w-full sm:w-auto",children:[(0,a.jsx)(w.Z,{className:"w-4 h-4 mr-2"}),"Previous"]}),(0,a.jsxs)(z.z,{type:"button",onClick:G,className:"w-full sm:w-auto",children:["Next",(0,a.jsx)(b.Z,{className:"w-4 h-4 ml-2"})]})]})]}),(0,a.jsxs)("div",{className:(0,k.cn)("grid gap-4",2===s?"":"hidden"),children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.Label,{htmlFor:"phone",children:"Phone Number"}),(0,a.jsx)(N.Z,{control:K.control,setCode:x,code:h})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-4",children:[(0,a.jsx)("input",{type:"checkbox",id:"terms",checked:E,onChange:()=>{M||L(!E)},className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,a.jsxs)("label",{htmlFor:"terms",className:"text-sm text-gray-600",children:["I agree to the"," ",(0,a.jsx)("span",{onClick:()=>O(!0),className:"text-primary hover:underline",children:"Terms and Conditions"})]}),(0,a.jsx)(V,{open:M,setOpen:O,setIsChecked:L})]}),(0,a.jsxs)("div",{className:"flex gap-2 flex-col sm:flex-row justify-between mt-4",children:[(0,a.jsxs)(z.z,{type:"button",onClick:q,className:"w-full sm:w-auto",children:[(0,a.jsx)(w.Z,{className:"w-4 h-4 mr-2"}),"Previous"]}),(0,a.jsxs)(z.z,{type:"submit",className:"w-full sm:w-auto",disabled:r||!E,children:[r?(0,a.jsx)(j.Z,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(y.Z,{className:"mr-2 h-4 w-4"}),"Create account"]})]})]}),(0,a.jsx)(F.Z,{phoneNumber:p,isModalOpen:D,setIsModalOpen:I})]})})})})}function K(){return(0,a.jsxs)("div",{className:"relative min-h-screen",children:[(0,a.jsx)("div",{className:"absolute left-4 top-4 sm:left-10 sm:top-10",children:(0,a.jsx)(n.T,{})}),(0,a.jsx)("div",{className:"flex items-center justify-center py-20 sm:py-12",children:(0,a.jsx)("div",{className:"mx-auto w-full px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"grid gap-6",children:[(0,a.jsx)(J,{}),(0,a.jsxs)("div",{className:"mt-4 text-center text-xs sm:text-sm",children:["Already have an account?"," ",(0,a.jsx)(z.z,{variant:"outline",size:"sm",className:"ml-2",asChild:!0,children:(0,a.jsx)(r.default,{href:"/auth/login",children:"Sign in"})})]}),(0,a.jsxs)("p",{className:"px-2 text-center text-xs text-muted-foreground sm:px-8 sm:text-sm",children:["By clicking continue, you agree to our"," ",(0,a.jsx)(z.z,{variant:"link",className:"p-0",asChild:!0,children:(0,a.jsx)(r.default,{href:"/terms",children:"Terms of Service"})})," ","and"," ",(0,a.jsx)(z.z,{variant:"link",className:"p-0",asChild:!0,children:(0,a.jsx)(r.default,{href:"/privacy",children:"Privacy Policy."})})]})]})})})]})}},69081:function(e,s,t){"use strict";t.d(s,{f:function(){return c}});var a=t(57437);t(2265);var r=t(70518),n=t(87592),i=t(13793),l=t(49354),o=t(89733);function c(e){let{className:s,classNames:t,showOutsideDays:c=!0,...d}=e;return(0,a.jsx)(i._W,{showOutsideDays:c,className:(0,l.cn)("p-3",s),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,l.cn)((0,o.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,l.cn)((0,o.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:e=>{let{...s}=e;return(0,a.jsx)(r.Z,{className:"h-4 w-4"})},IconRight:e=>{let{...s}=e;return(0,a.jsx)(n.Z,{className:"h-4 w-4"})}},...d})}c.displayName="Calendar"},21413:function(e,s,t){"use strict";t.d(s,{J2:function(){return l},xo:function(){return o},yk:function(){return c}});var a=t(57437),r=t(2265),n=t(7568),i=t(49354);let l=n.fC,o=n.xz,c=r.forwardRef((e,s)=>{let{className:t,align:r="center",sideOffset:l=4,...o}=e;return(0,a.jsx)(n.h_,{children:(0,a.jsx)(n.VY,{ref:s,align:r,sideOffset:l,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...o})})});c.displayName=n.VY.displayName}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,9812,1974,4022,1374,4504,6960,2455,253,2908,2971,7023,1744],function(){return e(e.s=76995)}),_N_E=e.O()}]);