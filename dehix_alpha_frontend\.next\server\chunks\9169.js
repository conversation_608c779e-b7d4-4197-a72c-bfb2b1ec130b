"use strict";exports.id=9169,exports.ids=[9169],exports.modules={91216:(e,t,a)=>{a.d(t,{Z:()=>n});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(80851).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},12714:(e,t,a)=>{a.d(t,{Z:()=>n});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(80851).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},27918:(e,t,a)=>{a.d(t,{Z:()=>j});var n=a(10326),r=a(7558),s=a(17577),l=a(35047),o=a(25842),i=a(77506),d=a(24118),c=a(44794),u=a(41190),p=a(91664),m=a(56627);let f=({open:e,setOpen:t,onSubmit:a,setPhone:r})=>{let[l,o]=(0,s.useState)(""),[f,h]=(0,s.useState)(!1),x=async e=>{if(e.preventDefault(),10!==l.length){(0,m.Am)({variant:"destructive",title:"Invalid Phone Number",description:"Please enter a valid 10-digit number."});return}try{h(!0);let e=`+91${l}`;r(e),await a(e),(0,m.Am)({title:"Sending OTP",description:`Sending OTP to ${e}. Please wait...`}),t(!1)}catch(e){(0,m.Am)({variant:"destructive",title:"Failed to Send OTP",description:"Something went wrong. Please try again."})}finally{h(!1)}};return n.jsx(d.Vq,{open:e,onOpenChange:t,children:(0,n.jsxs)(d.cZ,{children:[(0,n.jsxs)(d.fK,{children:[n.jsx(d.$N,{children:"Change Phone Number"}),n.jsx(d.Be,{children:"Enter a new phone number to receive the OTP."})]}),(0,n.jsxs)("form",{onSubmit:x,className:"space-y-4 mt-2",children:[(0,n.jsxs)("div",{children:[n.jsx(c.Label,{className:"mb-1 block",children:"New Phone Number"}),n.jsx("div",{className:"mt-2",children:n.jsx(u.I,{type:"tel",value:l,onChange:e=>{let t=e.target.value.replace(/\D/g,"");t.length<=10&&o(t)},maxLength:10,placeholder:"Enter 10-digit number",disabled:f})})]}),(0,n.jsxs)("div",{className:"flex justify-end gap-2",children:[n.jsx(p.z,{variant:"secondary",type:"button",onClick:()=>t(!1),disabled:f,children:"Cancel"}),n.jsx(p.z,{type:"submit",disabled:f,children:f?(0,n.jsxs)(n.Fragment,{children:[n.jsx(i.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Sending..."]}):"Send OTP"})]})]})]})})};var h=a(24516),x=a(45722),g=a(4594),v=a(51223),y=a(6260),b=a(84097);let j=function({phoneNumber:e,isModalOpen:t,setIsModalOpen:a}){let i=(0,l.useRouter)(),c=(0,o.I0)(),[u,m]=(0,s.useState)(""),[j,w]=(0,s.useState)(null),[N,S]=(0,s.useState)(""),[C,P]=(0,s.useState)(0),[k,E]=(0,s.useTransition)(),[T,O]=(0,s.useState)(!1),[R,M]=(0,s.useState)(e),[A,D]=(0,s.useState)(null),[I,_]=(0,s.useState)(null);(0,s.useCallback)(async()=>{E(async()=>{if(w(""),!I){w("Please request OTP first.");return}try{let e=await I?.confirm(u),{user:t,claims:a}=await (0,v.is)(e);await y.b.put(`/${a.type}`,{phone:R,phoneVerify:!0}),c((0,g.av)({...t,type:a.type})),i.replace(`/dashboard/${a.type}`)}catch(e){console.log(e),w("Failed to verify OTP. Please check the OTP."),(0,b.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}})},[I,u,c,i,R]);let Z=(0,s.useCallback)(async()=>{E(async()=>{if(w(""),P(60),!A)return w("RecaptchaVerifier is not initialized.");try{if(e.length>0){let t=await (0,r.$g)(x.I8,e,A);_(t),S("OTP sent successfully.")}}catch(e){console.error(e),P(0),"auth/invalid-phone-number"===e.code?w("Invalid phone number. Please check the number."):"auth/too-many-requests"===e.code?w("Too many requests. Please try again later."):w("Failed to send OTP. Please try again.")}})},[e,A]),z=(0,n.jsxs)("div",{role:"status",className:"flex justify-center",children:[(0,n.jsxs)("svg",{"aria-hidden":"true",className:"w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-green-600",viewBox:"0 0 100 101",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[n.jsx("path",{d:"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z",fill:"currentColor"}),n.jsx("path",{d:"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z",fill:"currentFill"})]}),n.jsx("span",{className:"sr-only",children:"Loading..."})]});return(0,n.jsxs)(n.Fragment,{children:[n.jsx(d.Vq,{open:t,onOpenChange:a,children:(0,n.jsxs)(d.cZ,{children:[(0,n.jsxs)(d.fK,{children:[(0,n.jsxs)("p",{className:"text-sm text-center text-gray-500",children:["OTP sent to"," ",(0,n.jsxs)("strong",{children:[(R||e)?.substring(0,3)," ",(R||e)?.substring(3)]})]}),n.jsx("button",{className:"text-blue-600 text-sm underline mt-1",onClick:()=>O(!0),children:"Not your number? Change it"}),n.jsx(d.$N,{children:"Enter OTP"}),n.jsx(d.Be,{children:"Please enter the OTP sent to your phone number."})]}),(0,n.jsxs)("div",{className:"flex flex-col justify-center items-center",children:[(0,n.jsxs)(h.Zn,{maxLength:6,value:u,onChange:e=>m(e),children:[(0,n.jsxs)(h.hf,{children:[n.jsx(h.cY,{index:0}),n.jsx(h.cY,{index:1}),n.jsx(h.cY,{index:2})]}),n.jsx(h.aM,{}),(0,n.jsxs)(h.hf,{children:[n.jsx(h.cY,{index:3}),n.jsx(h.cY,{index:4}),n.jsx(h.cY,{index:5})]})]}),n.jsx(p.z,{disabled:k||C>0,className:"mt-5",children:C>0?`Resend OTP in ${C}`:k?"Sending OTP":"Send OTP"}),(0,n.jsxs)("div",{className:"p-10 text-center",children:[j&&n.jsx("p",{className:"text-red-500",children:j}),N&&n.jsx("p",{className:"text-green-500",children:N})]}),k&&z]})]})}),n.jsx(f,{open:T,setOpen:O,onSubmit:e=>{M(e),Z(),O(!1)},setPhone:M}),n.jsx("div",{id:"recaptcha-container"})]})}},40603:(e,t,a)=>{a.d(t,{T:()=>d});var n=a(10326);a(17577);var r=a(60850),s=a(72607),l=a(14831),o=a(91664),i=a(10143);function d(){let{setTheme:e}=(0,l.F)();return(0,n.jsxs)(i.h_,{children:[n.jsx(i.$F,{asChild:!0,children:(0,n.jsxs)(o.z,{variant:"outline",size:"icon",children:[n.jsx(r.Z,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),n.jsx(s.Z,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),n.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,n.jsxs)(i.AW,{align:"end",children:[n.jsx(i.Xi,{onClick:()=>e("light"),children:"Light"}),n.jsx(i.Xi,{onClick:()=>e("dark"),children:"Dark"}),n.jsx(i.Xi,{onClick:()=>e("system"),children:"System"})]})]})}},24118:(e,t,a)=>{a.d(t,{$N:()=>x,Be:()=>g,GG:()=>u,Vq:()=>i,cN:()=>h,cZ:()=>m,fK:()=>f,hg:()=>d});var n=a(10326),r=a(17577),s=a(37956),l=a(94019),o=a(51223);let i=s.fC,d=s.xz,c=s.h_,u=s.x8,p=r.forwardRef(({className:e,...t},a)=>n.jsx(s.aV,{ref:a,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));p.displayName=s.aV.displayName;let m=r.forwardRef(({className:e,children:t,...a},r)=>(0,n.jsxs)(c,{children:[n.jsx(p,{}),(0,n.jsxs)(s.VY,{ref:r,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a,children:[t,(0,n.jsxs)(s.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground text-red-500",children:[n.jsx(l.Z,{className:"h-4 w-4",strokeWidth:4}),n.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=s.VY.displayName;let f=({className:e,...t})=>n.jsx("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});f.displayName="DialogHeader";let h=({className:e,...t})=>n.jsx("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});h.displayName="DialogFooter";let x=r.forwardRef(({className:e,...t},a)=>n.jsx(s.Dx,{ref:a,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));x.displayName=s.Dx.displayName;let g=r.forwardRef(({className:e,...t},a)=>n.jsx(s.dk,{ref:a,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));g.displayName=s.dk.displayName},10143:(e,t,a)=>{a.d(t,{$F:()=>u,AW:()=>p,Ju:()=>h,VD:()=>x,Xi:()=>m,bO:()=>f,h_:()=>c});var n=a(10326),r=a(17577),s=a(76234),l=a(39183),o=a(32933),i=a(53982),d=a(51223);let c=s.fC,u=s.xz;s.ZA,s.Uv,s.Tr,s.Ee,r.forwardRef(({className:e,inset:t,children:a,...r},o)=>(0,n.jsxs)(s.fF,{ref:o,className:(0,d.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...r,children:[a,n.jsx(l.Z,{className:"ml-auto h-4 w-4"})]})).displayName=s.fF.displayName,r.forwardRef(({className:e,...t},a)=>n.jsx(s.tu,{ref:a,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=s.tu.displayName;let p=r.forwardRef(({className:e,sideOffset:t=4,...a},r)=>n.jsx(s.Uv,{children:n.jsx(s.VY,{ref:r,sideOffset:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a})}));p.displayName=s.VY.displayName;let m=r.forwardRef(({className:e,inset:t,...a},r)=>n.jsx(s.ck,{ref:r,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...a}));m.displayName=s.ck.displayName;let f=r.forwardRef(({className:e,children:t,checked:a,...r},l)=>(0,n.jsxs)(s.oC,{ref:l,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:a,...r,children:[n.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:n.jsx(s.wU,{children:n.jsx(o.Z,{className:"h-4 w-4"})})}),t]}));f.displayName=s.oC.displayName,r.forwardRef(({className:e,children:t,...a},r)=>(0,n.jsxs)(s.Rk,{ref:r,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[n.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:n.jsx(s.wU,{children:n.jsx(i.Z,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=s.Rk.displayName;let h=r.forwardRef(({className:e,inset:t,...a},r)=>n.jsx(s.__,{ref:r,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...a}));h.displayName=s.__.displayName;let x=r.forwardRef(({className:e,...t},a)=>n.jsx(s.Z0,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t}));x.displayName=s.Z0.displayName},24516:(e,t,a)=>{a.d(t,{Zn:()=>j,hf:()=>w,aM:()=>S,cY:()=>N});var n=a(10326),r=a(17577),s=Object.defineProperty,l=Object.defineProperties,o=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,d=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,u=(e,t,a)=>t in e?s(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,p=(e,t)=>{for(var a in t||(t={}))d.call(t,a)&&u(e,a,t[a]);if(i)for(var a of i(t))c.call(t,a)&&u(e,a,t[a]);return e},m=(e,t)=>l(e,o(t)),f=(e,t)=>{var a={};for(var n in e)d.call(e,n)&&0>t.indexOf(n)&&(a[n]=e[n]);if(null!=e&&i)for(var n of i(e))0>t.indexOf(n)&&c.call(e,n)&&(a[n]=e[n]);return a},h=r.createContext({}),x=r.forwardRef((e,t)=>{var a,n,s,l,o,{value:i,onChange:d,maxLength:c,textAlign:u="left",pattern:x="^\\d+$",inputMode:y="numeric",onComplete:b,pushPasswordManagerStrategy:j="increase-width",containerClassName:w,noScriptCSSFallback:N=v,render:S,children:C}=e,P=f(e,["value","onChange","maxLength","textAlign","pattern","inputMode","onComplete","pushPasswordManagerStrategy","containerClassName","noScriptCSSFallback","render","children"]);let[k,E]=r.useState("string"==typeof P.defaultValue?P.defaultValue:""),T=null!=i?i:k,O=function(e){let t=r.useRef();return r.useEffect(()=>{t.current=e}),t.current}(T),R=r.useCallback(e=>{null==d||d(e),E(e)},[d]),M=r.useMemo(()=>x?"string"==typeof x?new RegExp(x):x:null,[x]),A=r.useRef(null),D=r.useRef(null),I=r.useRef({value:T,onChange:R,isIOS:"undefined"!=typeof window&&(null==(n=null==(a=null==window?void 0:window.CSS)?void 0:a.supports)?void 0:n.call(a,"-webkit-touch-callout","none"))}),_=r.useRef({prev:[null==(s=A.current)?void 0:s.selectionStart,null==(l=A.current)?void 0:l.selectionEnd,null==(o=A.current)?void 0:o.selectionDirection]});r.useImperativeHandle(t,()=>A.current,[]),r.useEffect(()=>{let e=A.current,t=D.current;if(!e||!t)return;function a(){if(document.activeElement!==e){V(null),L(null);return}let t=e.selectionStart,a=e.selectionEnd,n=e.selectionDirection,r=e.maxLength,s=e.value,l=_.current.prev,o=-1,i=-1,d;if(0!==s.length&&null!==t&&null!==a){let e=t===a,n=t===s.length&&s.length<r;if(e&&!n){if(0===t)o=0,i=1,d="forward";else if(t===r)o=t-1,i=t,d="backward";else if(r>1&&s.length>1){let e=0;if(null!==l[0]&&null!==l[1]){d=t<l[1]?"backward":"forward";let a=l[0]===l[1]&&l[0]<r;"backward"!==d||a||(e=-1)}o=e+t,i=e+t+1}}-1!==o&&-1!==i&&o!==i&&A.current.setSelectionRange(o,i,d)}let c=-1!==o?o:t,u=-1!==i?i:a,p=null!=d?d:n;V(c),L(u),_.current.prev=[c,u,p]}if(I.current.value!==e.value&&I.current.onChange(e.value),_.current.prev=[e.selectionStart,e.selectionEnd,e.selectionDirection],document.addEventListener("selectionchange",a,{capture:!0}),a(),document.activeElement===e&&W(!0),!document.getElementById("input-otp-style")){let e=document.createElement("style");if(e.id="input-otp-style",document.head.appendChild(e),e.sheet){let t="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";g(e.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),g(e.sheet,`[data-input-otp]:autofill { ${t} }`),g(e.sheet,`[data-input-otp]:-webkit-autofill { ${t} }`),g(e.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),g(e.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let n=()=>{t&&t.style.setProperty("--root-height",`${e.clientHeight}px`)};n();let r=new ResizeObserver(n);return r.observe(e),()=>{document.removeEventListener("selectionchange",a,{capture:!0}),r.disconnect()}},[]);let[Z,z]=r.useState(!1),[F,W]=r.useState(!1),[B,V]=r.useState(null),[$,L]=r.useState(null);r.useEffect(()=>{(function(e){setTimeout(e,0),setTimeout(e,10),setTimeout(e,50)})(()=>{var e,t,a,n;null==(e=A.current)||e.dispatchEvent(new Event("input"));let r=null==(t=A.current)?void 0:t.selectionStart,s=null==(a=A.current)?void 0:a.selectionEnd,l=null==(n=A.current)?void 0:n.selectionDirection;null!==r&&null!==s&&(V(r),L(s),_.current.prev=[r,s,l])})},[T,F]),r.useEffect(()=>{void 0!==O&&T!==O&&O.length<c&&T.length===c&&(null==b||b(T))},[c,b,O,T]);let Y=function({containerRef:e,inputRef:t,pushPasswordManagerStrategy:a,isFocused:n}){let s=r.useRef({done:!1,refocused:!1}),[l,o]=r.useState(!1),[i,d]=r.useState(!1),[c,u]=r.useState(!1),p=r.useMemo(()=>"none"!==a&&("increase-width"===a||"experimental-no-flickering"===a)&&l&&i,[l,i,a]),m=r.useCallback(()=>{let n=e.current,r=t.current;if(!n||!r||c||"none"===a)return;let l=n.getBoundingClientRect().left+n.offsetWidth,i=n.getBoundingClientRect().top+n.offsetHeight/2;if(!(0===document.querySelectorAll('[data-lastpass-icon-root],com-1password-button,[data-dashlanecreated],[style$="2147483647 !important;"]').length&&document.elementFromPoint(l-18,i)===n)&&(o(!0),u(!0),!s.current.refocused&&document.activeElement===r)){let e=[r.selectionStart,r.selectionEnd];r.blur(),r.focus(),r.setSelectionRange(e[0],e[1]),s.current.refocused=!0}},[e,t,c,a]);return r.useEffect(()=>{let t=e.current;if(!t||"none"===a)return;function n(){d(window.innerWidth-t.getBoundingClientRect().right>=40)}n();let r=setInterval(n,1e3);return()=>{clearInterval(r)}},[e,a]),r.useEffect(()=>{let e=n||document.activeElement===t.current;if("none"===a||!e)return;let r=setTimeout(m,0),s=setTimeout(m,2e3),l=setTimeout(m,5e3),o=setTimeout(()=>{u(!0)},6e3);return()=>{clearTimeout(r),clearTimeout(s),clearTimeout(l),clearTimeout(o)}},[t,n,a,m]),{hasPWMBadge:l,willPushPWMBadge:p,PWM_BADGE_SPACE_WIDTH:"40px"}}({containerRef:D,inputRef:A,pushPasswordManagerStrategy:j,isFocused:F}),H=r.useCallback(e=>{let t=e.currentTarget.value.slice(0,c);if(t.length>0&&M&&!M.test(t)){e.preventDefault();return}"string"==typeof O&&t.length<O.length&&document.dispatchEvent(new Event("selectionchange")),R(t)},[c,R,O,M]),q=r.useCallback(()=>{var e;if(A.current){let t=Math.min(A.current.value.length,c-1),a=A.current.value.length;null==(e=A.current)||e.setSelectionRange(t,a),V(t),L(a)}W(!0)},[c]),G=r.useCallback(e=>{var t,a;let n=A.current;if(!I.current.isIOS||!e.clipboardData||!n)return;let r=e.clipboardData.getData("text/plain");e.preventDefault();let s=null==(t=A.current)?void 0:t.selectionStart,l=null==(a=A.current)?void 0:a.selectionEnd,o=(s!==l?T.slice(0,s)+r+T.slice(l):T.slice(0,s)+r+T.slice(s)).slice(0,c);if(o.length>0&&M&&!M.test(o))return;n.value=o,R(o);let i=Math.min(o.length,c-1),d=o.length;n.setSelectionRange(i,d),V(i),L(d)},[c,R,M,T]),U=r.useMemo(()=>({position:"relative",cursor:P.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[P.disabled]),X=r.useMemo(()=>({position:"absolute",inset:0,width:Y.willPushPWMBadge?`calc(100% + ${Y.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:Y.willPushPWMBadge?`inset(0 ${Y.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:u,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[Y.PWM_BADGE_SPACE_WIDTH,Y.willPushPWMBadge,u]),K=r.useMemo(()=>r.createElement("input",m(p({autoComplete:P.autoComplete||"one-time-code"},P),{"data-input-otp":!0,"data-input-otp-mss":B,"data-input-otp-mse":$,inputMode:y,pattern:null==M?void 0:M.source,style:X,maxLength:c,value:T,ref:A,onPaste:e=>{var t;G(e),null==(t=P.onPaste)||t.call(P,e)},onChange:H,onMouseOver:e=>{var t;z(!0),null==(t=P.onMouseOver)||t.call(P,e)},onMouseLeave:e=>{var t;z(!1),null==(t=P.onMouseLeave)||t.call(P,e)},onFocus:e=>{var t;q(),null==(t=P.onFocus)||t.call(P,e)},onBlur:e=>{var t;W(!1),null==(t=P.onBlur)||t.call(P,e)}})),[H,q,G,y,X,c,$,B,P,null==M?void 0:M.source,T]),J=r.useMemo(()=>({slots:Array.from({length:c}).map((e,t)=>{let a=F&&null!==B&&null!==$&&(B===$&&t===B||t>=B&&t<$),n=void 0!==T[t]?T[t]:null;return{char:n,isActive:a,hasFakeCaret:a&&null===n}}),isFocused:F,isHovering:!P.disabled&&Z}),[F,Z,c,$,B,P.disabled,T]),Q=r.useMemo(()=>S?S(J):r.createElement(h.Provider,{value:J},C),[C,J,S]);return r.createElement(r.Fragment,null,null!==N&&r.createElement("noscript",null,r.createElement("style",null,N)),r.createElement("div",{ref:D,"data-input-otp-container":!0,style:U,className:w},Q,r.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},K)))});function g(e,t){try{e.insertRule(t)}catch(e){console.error("input-otp could not insert CSS rule:",t)}}x.displayName="Input";var v=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`;/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let y=(0,a(80851).Z)("Dot",[["circle",{cx:"12.1",cy:"12.1",r:"1",key:"18d7e5"}]]);var b=a(51223);let j=r.forwardRef(({className:e,containerClassName:t,...a},r)=>n.jsx(x,{ref:r,containerClassName:(0,b.cn)("flex items-center gap-2 has-[:disabled]:opacity-50",t),className:(0,b.cn)("disabled:cursor-not-allowed",e),...a}));j.displayName="InputOTP";let w=r.forwardRef(({className:e,...t},a)=>n.jsx("div",{ref:a,className:(0,b.cn)("flex items-center",e),...t}));w.displayName="InputOTPGroup";let N=r.forwardRef(({index:e,className:t,...a},s)=>{let{char:l,hasFakeCaret:o,isActive:i}=r.useContext(h).slots[e];return(0,n.jsxs)("div",{ref:s,className:(0,b.cn)("relative flex h-10 w-10 items-center justify-center border-y border-r border-input text-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md",i&&"z-10 ring-2 ring-ring ring-offset-background",t),...a,children:[l,o&&n.jsx("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:n.jsx("div",{className:"h-4 w-px animate-caret-blink bg-foreground duration-1000"})})]})});N.displayName="InputOTPSlot";let S=r.forwardRef(({...e},t)=>n.jsx("div",{ref:t,role:"separator",...e,children:n.jsx(y,{})}));S.displayName="InputOTPSeparator"},41190:(e,t,a)=>{a.d(t,{I:()=>l});var n=a(10326),r=a(17577),s=a(51223);let l=r.forwardRef(({className:e,type:t,...a},r)=>n.jsx("input",{type:t,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...a}));l.displayName="Input"},44794:(e,t,a)=>{a.r(t),a.d(t,{Label:()=>d});var n=a(10326),r=a(17577),s=a(34478),l=a(28671),o=a(51223);let i=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...t},a)=>n.jsx(s.f,{ref:a,className:(0,o.cn)(i(),e),...t}));d.displayName=s.f.displayName},84097:(e,t,a)=>{a.d(t,{Am:()=>c}),a(17577);let n=0,r=new Map,s=e=>{if(r.has(e))return;let t=setTimeout(()=>{r.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);r.set(e,t)},l=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?s(a):e.toasts.forEach(e=>{s(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],i={toasts:[]};function d(e){i=l(i,e),o.forEach(e=>{e(i)})}function c({...e}){let t=(n=(n+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>d({type:"DISMISS_TOAST",toastId:t});return d({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||a()}}}),{id:t,dismiss:a,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:t}})}}},34478:(e,t,a)=>{a.d(t,{f:()=>o});var n=a(17577),r=a(77335),s=a(10326),l=n.forwardRef((e,t)=>(0,s.jsx)(r.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var o=l}};