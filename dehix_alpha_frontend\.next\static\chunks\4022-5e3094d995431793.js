"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4022],{95137:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},71976:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},14392:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},3274:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},32226:function(e,t,n){n.d(t,{x8:function(){return ea},VY:function(){return el},dk:function(){return ei},Vq:function(){return E},cZ:function(){return A},t9:function(){return W},aV:function(){return er},h_:function(){return en},fC:function(){return ee},Dx:function(){return eo},xz:function(){return et}});var r=n(2265),l=n(78149),o=n(1584),i=n(98324),a=n(53201),s=n(91715),u=n(53938),d=n(80467),c=n(56935),p=n(31383),f=n(18676),v=n(20589),h=n(9219),g=n(78369),m=n(57437),w=r.forwardRef((e,t)=>{let{children:n,...l}=e,o=r.Children.toArray(n),i=o.find(b);if(i){let e=i.props.children,n=o.map(t=>t!==i?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,m.jsx)(x,{...l,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,m.jsx)(x,{...l,ref:t,children:n})});w.displayName="Slot";var x=r.forwardRef((e,t)=>{let{children:n,...l}=e;if(r.isValidElement(n)){let e,i;let a=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref;return r.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let l=e[r],o=t[r];/^on[A-Z]/.test(r)?l&&o?n[r]=(...e)=>{o(...e),l(...e)}:l&&(n[r]=l):"style"===r?n[r]={...l,...o}:"className"===r&&(n[r]=[l,o].filter(Boolean).join(" "))}return{...e,...n}}(l,n.props),ref:t?(0,o.F)(t,a):a})}return r.Children.count(n)>1?r.Children.only(null):null});x.displayName="SlotClone";var y=({children:e})=>(0,m.jsx)(m.Fragment,{children:e});function b(e){return r.isValidElement(e)&&e.type===y}var C="Dialog",[j,S]=(0,i.b)(C),[R,D]=j(C),E=e=>{let{__scopeDialog:t,children:n,open:l,defaultOpen:o,onOpenChange:i,modal:u=!0}=e,d=r.useRef(null),c=r.useRef(null),[p=!1,f]=(0,s.T)({prop:l,defaultProp:o,onChange:i});return(0,m.jsx)(R,{scope:t,triggerRef:d,contentRef:c,contentId:(0,a.M)(),titleId:(0,a.M)(),descriptionId:(0,a.M)(),open:p,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:u,children:n})};E.displayName=C;var M="DialogTrigger",k=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=D(M,n),a=(0,o.e)(t,i.triggerRef);return(0,m.jsx)(f.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":Y(i.open),...r,ref:a,onClick:(0,l.M)(e.onClick,i.onOpenToggle)})});k.displayName=M;var I="DialogPortal",[N,T]=j(I,{forceMount:void 0}),P=e=>{let{__scopeDialog:t,forceMount:n,children:l,container:o}=e,i=D(I,t);return(0,m.jsx)(N,{scope:t,forceMount:n,children:r.Children.map(l,e=>(0,m.jsx)(p.z,{present:n||i.open,children:(0,m.jsx)(c.h,{asChild:!0,container:o,children:e})}))})};P.displayName=I;var V="DialogOverlay",W=r.forwardRef((e,t)=>{let n=T(V,e.__scopeDialog),{forceMount:r=n.forceMount,...l}=e,o=D(V,e.__scopeDialog);return o.modal?(0,m.jsx)(p.z,{present:r||o.open,children:(0,m.jsx)(_,{...l,ref:t})}):null});W.displayName=V;var _=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=D(V,n);return(0,m.jsx)(h.Z,{as:w,allowPinchZoom:!0,shards:[l.contentRef],children:(0,m.jsx)(f.WV.div,{"data-state":Y(l.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),O="DialogContent",A=r.forwardRef((e,t)=>{let n=T(O,e.__scopeDialog),{forceMount:r=n.forceMount,...l}=e,o=D(O,e.__scopeDialog);return(0,m.jsx)(p.z,{present:r||o.open,children:o.modal?(0,m.jsx)(F,{...l,ref:t}):(0,m.jsx)(L,{...l,ref:t})})});A.displayName=O;var F=r.forwardRef((e,t)=>{let n=D(O,e.__scopeDialog),i=r.useRef(null),a=(0,o.e)(t,n.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,g.Ry)(e)},[]),(0,m.jsx)(B,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,l.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,l.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,l.M)(e.onFocusOutside,e=>e.preventDefault())})}),L=r.forwardRef((e,t)=>{let n=D(O,e.__scopeDialog),l=r.useRef(!1),o=r.useRef(!1);return(0,m.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,i;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(l.current||null===(i=n.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),l.current=!1,o.current=!1},onInteractOutside:t=>{var r,i;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(l.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let a=t.target;(null===(i=n.triggerRef.current)||void 0===i?void 0:i.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),B=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:l,onOpenAutoFocus:i,onCloseAutoFocus:a,...s}=e,c=D(O,n),p=r.useRef(null),f=(0,o.e)(t,p);return(0,v.EW)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(d.M,{asChild:!0,loop:!0,trapped:l,onMountAutoFocus:i,onUnmountAutoFocus:a,children:(0,m.jsx)(u.XB,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":Y(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)($,{titleId:c.titleId}),(0,m.jsx)(Q,{contentRef:p,descriptionId:c.descriptionId})]})]})}),H="DialogTitle",Z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=D(H,n);return(0,m.jsx)(f.WV.h2,{id:l.titleId,...r,ref:t})});Z.displayName=H;var z="DialogDescription",K=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=D(z,n);return(0,m.jsx)(f.WV.p,{id:l.descriptionId,...r,ref:t})});K.displayName=z;var U="DialogClose",q=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=D(U,n);return(0,m.jsx)(f.WV.button,{type:"button",...r,ref:t,onClick:(0,l.M)(e.onClick,()=>o.onOpenChange(!1))})});function Y(e){return e?"open":"closed"}q.displayName=U;var X="DialogTitleWarning",[G,J]=(0,i.k)(X,{contentName:O,titleName:H,docsSlug:"dialog"}),$=e=>{let{titleId:t}=e,n=J(X),l="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(l)},[l,t]),null},Q=e=>{let{contentRef:t,descriptionId:n}=e,l=J("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(l.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(o)},[o,t,n]),null},ee=E,et=k,en=P,er=W,el=A,eo=Z,ei=K,ea=q},48362:function(e,t,n){n.d(t,{VY:function(){return eO},ZA:function(){return eF},JO:function(){return eW},ck:function(){return eB},wU:function(){return eZ},eT:function(){return eH},__:function(){return eL},h_:function(){return e_},fC:function(){return eT},$G:function(){return eK},u_:function(){return ez},Z0:function(){return eU},xz:function(){return eP},B4:function(){return eV},l_:function(){return eA}});var r=n(2265),l=n(54887),o=n(62361),i=n(78149),a=n(921),s=n(1584),u=n(98324),d=n(87513),c=n(53938),p=n(20589),f=n(80467),v=n(53201),h=n(25510),g=n(56935),m=n(18676),w=n(57437),x=r.forwardRef((e,t)=>{let{children:n,...l}=e,o=r.Children.toArray(n),i=o.find(C);if(i){let e=i.props.children,n=o.map(t=>t!==i?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,w.jsx)(y,{...l,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,w.jsx)(y,{...l,ref:t,children:n})});x.displayName="Slot";var y=r.forwardRef((e,t)=>{let{children:n,...l}=e;if(r.isValidElement(n)){let e,o;let i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref;return r.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let l=e[r],o=t[r];/^on[A-Z]/.test(r)?l&&o?n[r]=(...e)=>{o(...e),l(...e)}:l&&(n[r]=l):"style"===r?n[r]={...l,...o}:"className"===r&&(n[r]=[l,o].filter(Boolean).join(" "))}return{...e,...n}}(l,n.props),ref:t?(0,s.F)(t,i):i})}return r.Children.count(n)>1?r.Children.only(null):null});y.displayName="SlotClone";var b=({children:e})=>(0,w.jsx)(w.Fragment,{children:e});function C(e){return r.isValidElement(e)&&e.type===b}var j=n(75137),S=n(91715),R=n(1336),D=n(47250),E=n(31725),M=n(78369),k=n(9219),I=[" ","Enter","ArrowUp","ArrowDown"],N=[" ","Enter"],T="Select",[P,V,W]=(0,a.B)(T),[_,O]=(0,u.b)(T,[W,h.D7]),A=(0,h.D7)(),[F,L]=_(T),[B,H]=_(T),Z=e=>{let{__scopeSelect:t,children:n,open:l,defaultOpen:o,onOpenChange:i,value:a,defaultValue:s,onValueChange:u,dir:c,name:p,autoComplete:f,disabled:g,required:m}=e,x=A(t),[y,b]=r.useState(null),[C,j]=r.useState(null),[R,D]=r.useState(!1),E=(0,d.gm)(c),[M=!1,k]=(0,S.T)({prop:l,defaultProp:o,onChange:i}),[I,N]=(0,S.T)({prop:a,defaultProp:s,onChange:u}),T=r.useRef(null),V=!y||!!y.closest("form"),[W,_]=r.useState(new Set),O=Array.from(W).map(e=>e.props.value).join(";");return(0,w.jsx)(h.fC,{...x,children:(0,w.jsxs)(F,{required:m,scope:t,trigger:y,onTriggerChange:b,valueNode:C,onValueNodeChange:j,valueNodeHasChildren:R,onValueNodeHasChildrenChange:D,contentId:(0,v.M)(),value:I,onValueChange:N,open:M,onOpenChange:k,dir:E,triggerPointerDownPosRef:T,disabled:g,children:[(0,w.jsx)(P.Provider,{scope:t,children:(0,w.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{_(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{_(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),V?(0,w.jsxs)(ek,{"aria-hidden":!0,required:m,tabIndex:-1,name:p,autoComplete:f,value:I,onChange:e=>N(e.target.value),disabled:g,children:[void 0===I?(0,w.jsx)("option",{value:""}):null,Array.from(W)]},O):null]})})};Z.displayName=T;var z="SelectTrigger",K=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...l}=e,o=A(n),a=L(z,n),u=a.disabled||r,d=(0,s.e)(t,a.onTriggerChange),c=V(n),[p,f,v]=eI(e=>{let t=c().filter(e=>!e.disabled),n=t.find(e=>e.value===a.value),r=eN(t,e,n);void 0!==r&&a.onValueChange(r.value)}),g=()=>{u||(a.onOpenChange(!0),v())};return(0,w.jsx)(h.ee,{asChild:!0,...o,children:(0,w.jsx)(m.WV.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eM(a.value)?"":void 0,...l,ref:d,onClick:(0,i.M)(l.onClick,e=>{e.currentTarget.focus()}),onPointerDown:(0,i.M)(l.onPointerDown,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&(g(),a.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)},e.preventDefault())}),onKeyDown:(0,i.M)(l.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||f(e.key),(!t||" "!==e.key)&&I.includes(e.key)&&(g(),e.preventDefault())})})})});K.displayName=z;var U="SelectValue",q=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:l,children:o,placeholder:i="",...a}=e,u=L(U,n),{onValueNodeHasChildrenChange:d}=u,c=void 0!==o,p=(0,s.e)(t,u.onValueNodeChange);return(0,R.b)(()=>{d(c)},[d,c]),(0,w.jsx)(m.WV.span,{...a,ref:p,style:{pointerEvents:"none"},children:eM(u.value)?(0,w.jsx)(w.Fragment,{children:i}):o})});q.displayName=U;var Y=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...l}=e;return(0,w.jsx)(m.WV.span,{"aria-hidden":!0,...l,ref:t,children:r||"▼"})});Y.displayName="SelectIcon";var X=e=>(0,w.jsx)(g.h,{asChild:!0,...e});X.displayName="SelectPortal";var G="SelectContent",J=r.forwardRef((e,t)=>{let n=L(G,e.__scopeSelect),[o,i]=r.useState();return((0,R.b)(()=>{i(new DocumentFragment)},[]),n.open)?(0,w.jsx)(ee,{...e,ref:t}):o?l.createPortal((0,w.jsx)($,{scope:e.__scopeSelect,children:(0,w.jsx)(P.Slot,{scope:e.__scopeSelect,children:(0,w.jsx)("div",{children:e.children})})}),o):null});J.displayName=G;var[$,Q]=_(G),ee=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:a,onPointerDownOutside:u,side:d,sideOffset:v,align:h,alignOffset:g,arrowPadding:m,collisionBoundary:y,collisionPadding:b,sticky:C,hideWhenDetached:j,avoidCollisions:S,...R}=e,D=L(G,n),[E,I]=r.useState(null),[N,T]=r.useState(null),P=(0,s.e)(t,e=>I(e)),[W,_]=r.useState(null),[O,A]=r.useState(null),F=V(n),[B,H]=r.useState(!1),Z=r.useRef(!1);r.useEffect(()=>{if(E)return(0,M.Ry)(E)},[E]),(0,p.EW)();let z=r.useCallback(e=>{let[t,...n]=F().map(e=>e.ref.current),[r]=n.slice(-1),l=document.activeElement;for(let n of e)if(n===l||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&N&&(N.scrollTop=0),n===r&&N&&(N.scrollTop=N.scrollHeight),null==n||n.focus(),document.activeElement!==l))return},[F,N]),K=r.useCallback(()=>z([W,E]),[z,W,E]);r.useEffect(()=>{B&&K()},[B,K]);let{onOpenChange:U,triggerPointerDownPosRef:q}=D;r.useEffect(()=>{if(E){let e={x:0,y:0},t=t=>{var n,r,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!==(l=null===(n=q.current)||void 0===n?void 0:n.x)&&void 0!==l?l:0)),y:Math.abs(Math.round(t.pageY)-(null!==(o=null===(r=q.current)||void 0===r?void 0:r.y)&&void 0!==o?o:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():E.contains(n.target)||U(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[E,U,q]),r.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[Y,X]=eI(e=>{let t=F().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eN(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),J=r.useCallback((e,t,n)=>{let r=!Z.current&&!n;(void 0!==D.value&&D.value===t||r)&&(_(e),r&&(Z.current=!0))},[D.value]),Q=r.useCallback(()=>null==E?void 0:E.focus(),[E]),ee=r.useCallback((e,t,n)=>{let r=!Z.current&&!n;(void 0!==D.value&&D.value===t||r)&&A(e)},[D.value]),er="popper"===l?en:et,el=er===en?{side:d,sideOffset:v,align:h,alignOffset:g,arrowPadding:m,collisionBoundary:y,collisionPadding:b,sticky:C,hideWhenDetached:j,avoidCollisions:S}:{};return(0,w.jsx)($,{scope:n,content:E,viewport:N,onViewportChange:T,itemRefCallback:J,selectedItem:W,onItemLeave:Q,itemTextRefCallback:ee,focusSelectedItem:K,selectedItemText:O,position:l,isPositioned:B,searchRef:Y,children:(0,w.jsx)(k.Z,{as:x,allowPinchZoom:!0,children:(0,w.jsx)(f.M,{asChild:!0,trapped:D.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,i.M)(o,e=>{var t;null===(t=D.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,w.jsx)(c.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>D.onOpenChange(!1),children:(0,w.jsx)(er,{role:"listbox",id:D.contentId,"data-state":D.open?"open":"closed",dir:D.dir,onContextMenu:e=>e.preventDefault(),...R,...el,onPlaced:()=>H(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...R.style},onKeyDown:(0,i.M)(R.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||X(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=F().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>z(t)),e.preventDefault()}})})})})})})});ee.displayName="SelectContentImpl";var et=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:l,...i}=e,a=L(G,n),u=Q(G,n),[d,c]=r.useState(null),[p,f]=r.useState(null),v=(0,s.e)(t,e=>f(e)),h=V(n),g=r.useRef(!1),x=r.useRef(!0),{viewport:y,selectedItem:b,selectedItemText:C,focusSelectedItem:j}=u,S=r.useCallback(()=>{if(a.trigger&&a.valueNode&&d&&p&&y&&b&&C){let e=a.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=C.getBoundingClientRect();if("rtl"!==a.dir){let l=r.left-t.left,i=n.left-l,a=e.left-i,s=e.width+a,u=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.u)(i,[10,c-u]);d.style.minWidth=s+"px",d.style.left=p+"px"}else{let l=t.right-r.right,i=window.innerWidth-n.right-l,a=window.innerWidth-e.right-i,s=e.width+a,u=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.u)(i,[10,c-u]);d.style.minWidth=s+"px",d.style.right=p+"px"}let i=h(),s=window.innerHeight-20,u=y.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),m=parseInt(c.borderBottomWidth,10),w=f+v+u+parseInt(c.paddingBottom,10)+m,x=Math.min(5*b.offsetHeight,w),j=window.getComputedStyle(y),S=parseInt(j.paddingTop,10),R=parseInt(j.paddingBottom,10),D=e.top+e.height/2-10,E=b.offsetHeight/2,M=f+v+(b.offsetTop+E);if(M<=D){let e=b===i[i.length-1].ref.current;d.style.bottom="0px";let t=p.clientHeight-y.offsetTop-y.offsetHeight;d.style.height=M+Math.max(s-D,E+(e?R:0)+t+m)+"px"}else{let e=b===i[0].ref.current;d.style.top="0px";let t=Math.max(D,f+y.offsetTop+(e?S:0)+E);d.style.height=t+(w-M)+"px",y.scrollTop=M-D+y.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=x+"px",d.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>g.current=!0)}},[h,a.trigger,a.valueNode,d,p,y,b,C,a.dir,l]);(0,R.b)(()=>S(),[S]);let[D,E]=r.useState();(0,R.b)(()=>{p&&E(window.getComputedStyle(p).zIndex)},[p]);let M=r.useCallback(e=>{e&&!0===x.current&&(S(),null==j||j(),x.current=!1)},[S,j]);return(0,w.jsx)(er,{scope:n,contentWrapper:d,shouldExpandOnScrollRef:g,onScrollButtonChange:M,children:(0,w.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:D},children:(0,w.jsx)(m.WV.div,{...i,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});et.displayName="SelectItemAlignedPosition";var en=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:l=10,...o}=e,i=A(n);return(0,w.jsx)(h.VY,{...i,...o,ref:t,align:r,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});en.displayName="SelectPopperPosition";var[er,el]=_(G,{}),eo="SelectViewport",ei=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:l,...o}=e,a=Q(eo,n),u=el(eo,n),d=(0,s.e)(t,a.onViewportChange),c=r.useRef(0);return(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,w.jsx)(P.Slot,{scope:n,children:(0,w.jsx)(m.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:d,style:{position:"relative",flex:1,overflow:"auto",...o.style},onScroll:(0,i.M)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if((null==r?void 0:r.current)&&n){let e=Math.abs(c.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,l=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(l<r){let o=l+e,i=Math.min(r,o),a=o-i;n.style.height=i+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});ei.displayName=eo;var ea="SelectGroup",[es,eu]=_(ea),ed=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=(0,v.M)();return(0,w.jsx)(es,{scope:n,id:l,children:(0,w.jsx)(m.WV.div,{role:"group","aria-labelledby":l,...r,ref:t})})});ed.displayName=ea;var ec="SelectLabel",ep=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=eu(ec,n);return(0,w.jsx)(m.WV.div,{id:l.id,...r,ref:t})});ep.displayName=ec;var ef="SelectItem",[ev,eh]=_(ef),eg=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:l,disabled:o=!1,textValue:a,...u}=e,d=L(ef,n),c=Q(ef,n),p=d.value===l,[f,h]=r.useState(null!=a?a:""),[g,x]=r.useState(!1),y=(0,s.e)(t,e=>{var t;return null===(t=c.itemRefCallback)||void 0===t?void 0:t.call(c,e,l,o)}),b=(0,v.M)(),C=()=>{o||(d.onValueChange(l),d.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,w.jsx)(ev,{scope:n,value:l,disabled:o,textId:b,isSelected:p,onItemTextChange:r.useCallback(e=>{h(t=>{var n;return t||(null!==(n=null==e?void 0:e.textContent)&&void 0!==n?n:"").trim()})},[]),children:(0,w.jsx)(P.ItemSlot,{scope:n,value:l,disabled:o,textValue:f,children:(0,w.jsx)(m.WV.div,{role:"option","aria-labelledby":b,"data-highlighted":g?"":void 0,"aria-selected":p&&g,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...u,ref:y,onFocus:(0,i.M)(u.onFocus,()=>x(!0)),onBlur:(0,i.M)(u.onBlur,()=>x(!1)),onPointerUp:(0,i.M)(u.onPointerUp,C),onPointerMove:(0,i.M)(u.onPointerMove,e=>{if(o){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}else e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,i.M)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}}),onKeyDown:(0,i.M)(u.onKeyDown,e=>{var t;(null===(t=c.searchRef)||void 0===t?void 0:t.current)!==""&&" "===e.key||(N.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});eg.displayName=ef;var em="SelectItemText",ew=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:o,style:i,...a}=e,u=L(em,n),d=Q(em,n),c=eh(em,n),p=H(em,n),[f,v]=r.useState(null),h=(0,s.e)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null===(t=d.itemTextRefCallback)||void 0===t?void 0:t.call(d,e,c.value,c.disabled)}),g=null==f?void 0:f.textContent,x=r.useMemo(()=>(0,w.jsx)("option",{value:c.value,disabled:c.disabled,children:g},c.value),[c.disabled,c.value,g]),{onNativeOptionAdd:y,onNativeOptionRemove:b}=p;return(0,R.b)(()=>(y(x),()=>b(x)),[y,b,x]),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(m.WV.span,{id:c.textId,...a,ref:h}),c.isSelected&&u.valueNode&&!u.valueNodeHasChildren?l.createPortal(a.children,u.valueNode):null]})});ew.displayName=em;var ex="SelectItemIndicator",ey=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return eh(ex,n).isSelected?(0,w.jsx)(m.WV.span,{"aria-hidden":!0,...r,ref:t}):null});ey.displayName=ex;var eb="SelectScrollUpButton",eC=r.forwardRef((e,t)=>{let n=Q(eb,e.__scopeSelect),l=el(eb,e.__scopeSelect),[o,i]=r.useState(!1),a=(0,s.e)(t,l.onScrollButtonChange);return(0,R.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,w.jsx)(eR,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eC.displayName=eb;var ej="SelectScrollDownButton",eS=r.forwardRef((e,t)=>{let n=Q(ej,e.__scopeSelect),l=el(ej,e.__scopeSelect),[o,i]=r.useState(!1),a=(0,s.e)(t,l.onScrollButtonChange);return(0,R.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,w.jsx)(eR,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eS.displayName=ej;var eR=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:l,...o}=e,a=Q("SelectScrollButton",n),s=r.useRef(null),u=V(n),d=r.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return r.useEffect(()=>()=>d(),[d]),(0,R.b)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[u]),(0,w.jsx)(m.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,i.M)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,i.M)(o.onPointerMove,()=>{var e;null===(e=a.onItemLeave)||void 0===e||e.call(a),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,i.M)(o.onPointerLeave,()=>{d()})})}),eD=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,w.jsx)(m.WV.div,{"aria-hidden":!0,...r,ref:t})});eD.displayName="SelectSeparator";var eE="SelectArrow";function eM(e){return""===e||void 0===e}r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=A(n),o=L(eE,n),i=Q(eE,n);return o.open&&"popper"===i.position?(0,w.jsx)(h.Eh,{...l,...r,ref:t}):null}).displayName=eE;var ek=r.forwardRef((e,t)=>{let{value:n,...l}=e,o=r.useRef(null),i=(0,s.e)(t,o),a=(0,D.D)(n);return r.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==n&&t){let r=new Event("change",{bubbles:!0});t.call(e,n),e.dispatchEvent(r)}},[a,n]),(0,w.jsx)(E.T,{asChild:!0,children:(0,w.jsx)("select",{...l,ref:i,defaultValue:n})})});function eI(e){let t=(0,j.W)(e),n=r.useRef(""),l=r.useRef(0),o=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),i=r.useCallback(()=>{n.current="",window.clearTimeout(l.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(l.current),[]),[n,o,i]}function eN(e,t,n){var r;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===l.length&&(o=o.filter(e=>e!==n));let i=o.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return i!==n?i:void 0}ek.displayName="BubbleSelect";var eT=Z,eP=K,eV=q,eW=Y,e_=X,eO=J,eA=ei,eF=ed,eL=ep,eB=eg,eH=ew,eZ=ey,ez=eC,eK=eS,eU=eD}}]);