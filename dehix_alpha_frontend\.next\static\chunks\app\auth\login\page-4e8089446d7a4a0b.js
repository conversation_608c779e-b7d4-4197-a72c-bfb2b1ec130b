(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6716],{15463:function(e,a,s){Promise.resolve().then(s.bind(s,54871))},54871:function(e,a,s){"use strict";s.r(a),s.d(a,{default:function(){return N}});var t=s(57437),l=s(2265),r=s(16463),i=s(11444),n=s(75733),c=s(47019),o=s(3274),d=s(25489),u=s(86494),m=s(66648),h=s(87138),g=s(78068),p=s(89733),x=s(77209),f=s(70402),v=s(90564),j=s(49354),y=s(3483),w=s(15922),b=s(60253);function N(){let e=(0,r.useRouter)(),a=(0,i.I0)(),[s,N]=(0,l.useState)(""),[k,E]=(0,l.useState)(""),[S,C]=(0,l.useState)(""),[L,F]=(0,l.useState)(!1),[P,I]=(0,l.useState)(!1),[Z,_]=(0,l.useState)(!1),[A,z]=(0,l.useState)(!1),O=async(e,a)=>{try{let s="";return"business"===a.toLowerCase()?s="/business/kyc":"freelancer"===a.toLowerCase()&&(s="/freelancer/".concat(e,"/kyc")),(await w.b.get(s)).data.status||null}catch(e){return console.error("KYC Fetch Error:",e),(0,g.Am)({variant:"destructive",title:"Error",description:"Failed to fetch KYC data. Please try again."}),null}},Y=async t=>{t.preventDefault(),_(!0);try{let t=await w.b.get("/public/user_email?user=".concat(s));if(C(t.data.phone),t.data.phoneVerify)try{let t=await (0,j.pH)(s,k),{user:l,claims:r}=await (0,j.is)(t),i=await O(l.uid,r.type);i?a((0,y.av)({...l,type:r.type,kycStatus:i})):a((0,y.av)({...l,type:r.type})),e.replace("/dashboard/".concat(r.type)),(0,g.Am)({title:"Login Successful",description:"You have successfully logged in."})}catch(e){(0,g.Am)({variant:"destructive",title:"Error",description:"Invalid Email or Password. Please try again."}),console.error(e.message)}else I(!0)}catch(e){(0,g.Am)({variant:"destructive",title:"Error",description:"Invalid Email or Password. Please try again."}),console.error(e.message)}finally{_(!1)}},D=async s=>{s.preventDefault(),z(!0);try{let s=await (0,j.bx)(),{user:t,claims:l}=await (0,j.is)(s);a((0,y.av)({...t,type:l.type})),e.replace("/dashboard/".concat(l.type)),(0,g.Am)({title:"Login Successful",description:"You have successfully logged in with Google."})}catch(e){(0,g.Am)({variant:"destructive",title:"Error",description:"Failed to login with Google. Please try again."}),console.error(e.message)}finally{z(!1)}};return(0,t.jsxs)("div",{className:"w-full lg:grid lg:min-h-[600px] lg:grid-cols-2 xl:min-h-screen",children:[(0,t.jsx)("div",{className:"absolute left-10 top-10",children:(0,t.jsx)(v.T,{})}),(0,t.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,t.jsxs)("div",{className:"mx-auto grid w-[350px] gap-6",children:[(0,t.jsxs)("div",{className:"grid gap-2 text-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Login"}),(0,t.jsx)("p",{className:"text-balance text-muted-foreground",children:"Enter your email below to login to your account"})]}),(0,t.jsx)("form",{onSubmit:Y,children:(0,t.jsxs)("div",{className:"grid gap-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(f.Label,{htmlFor:"email",children:"Email"}),(0,t.jsx)(x.I,{id:"email",type:"email",placeholder:"<EMAIL>",value:s,onChange:e=>N(e.target.value),required:!0})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.Label,{htmlFor:"password",children:"Password"}),(0,t.jsx)(h.default,{href:"/auth/forgot-password",className:"ml-auto inline-block text-sm underline",children:"Forgot your password?"})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(x.I,{id:"password",type:L?"text":"password",onChange:e=>E(e.target.value),required:!0}),(0,t.jsx)("button",{type:"button",onClick:()=>{F(e=>!e)},className:"absolute right-2 top-1/2 transform -translate-y-1/2",children:L?(0,t.jsx)(n.Z,{className:"h-4 w-4"}):(0,t.jsx)(c.Z,{className:"h-4 w-4"})})]})]}),(0,t.jsx)(p.z,{type:"submit",className:"w-full",disabled:Z,children:Z?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.Z,{className:"mr-2 h-4 w-4 animate-spin"})," ","Logging in..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.Z,{className:"mr-2 h-4 w-4"})," Login"]})}),(0,t.jsxs)(p.z,{variant:"outline",className:"w-full",disabled:A,onClick:D,children:[A?(0,t.jsx)(o.Z,{className:"mr-2 h-4 w-4 animate-spin"}):(0,t.jsx)(u.Z,{className:"mr-2 h-4 w-4"})," ","Google Login"]})]})}),(0,t.jsxs)("div",{className:"mt-4 text-center text-sm",children:["Don't have an account?"," ",(0,t.jsx)(p.z,{variant:"outline",size:"sm",className:"ml-2",asChild:!0,children:(0,t.jsx)(h.default,{href:"/auth/sign-up/freelancer",children:"Sign up"})})]})]})}),(0,t.jsx)("div",{className:"hidden lg:block",children:(0,t.jsx)(m.default,{src:"/bg.png",alt:"Image",width:"1920",height:"1080",className:"h-full w-full object-cover dark:brightness-[0.2] dark:invert"})}),(0,t.jsx)(b.Z,{phoneNumber:S,isModalOpen:P,setIsModalOpen:I})]})}}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,1272,2455,253,2971,7023,1744],function(){return e(e.s=15463)}),_N_E=e.O()}]);