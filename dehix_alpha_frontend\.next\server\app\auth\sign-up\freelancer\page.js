(()=>{var e={};e.id=1238,e.ids=[1238],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},45184:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),s(82487),s(54302),s(12523);var r=s(23191),a=s(88716),n=s(37922),i=s.n(n),o=s(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["auth",{children:["sign-up",{children:["freelancer",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,82487)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\auth\\sign-up\\freelancer\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\auth\\sign-up\\freelancer\\page.tsx"],u="/auth/sign-up/freelancer/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/sign-up/freelancer/page",pathname:"/auth/sign-up/freelancer",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},24069:(e,t,s)=>{Promise.resolve().then(s.bind(s,36866))},37358:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},11890:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},36866:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>J});var r=s(10326),a=s(90434),n=s(40603),i=s(17577),o=s.n(i),l=s(35047),c=s(27256),d=s(10321),u=s(74723),m=s(74064),p=s(79635),h=s(47546),x=s(58038),f=s(66307),g=s(12714),j=s(91216),b=s(77506),v=s(24230),w=s(86333),y=s(45691),N=s(89124),C=s(57776),P=s(51223),k=s(26408),_=s(91664),z=s(6260),D=s(56627),Z=s(44794),A=s(9969),R=s(41190),F=s(27918),S=s(37358),q=s(98181);let E=({selectedMonth:e,onSelect:t})=>r.jsx("div",{className:"grid grid-cols-3 gap-4 mb-4",children:Array.from({length:12},(e,t)=>(0,q.WU)(new Date(2e3,t,1),"MMMM")).map((s,a)=>r.jsx("button",{onClick:()=>t(a),className:`p-2 text-sm rounded-lg hover:bg-blue-600 transition ${e===a?"bg-blue-500 text-white":"text-white"}`,children:s},s))});var M=s(29280);let O=({selectedYear:e,onSelect:t})=>{let s=new Date().getFullYear()-16,a=Array.from({length:s-1970+1},(e,t)=>s-t).reverse();return(0,r.jsxs)(M.Ph,{onValueChange:e=>t(Number(e)),children:[r.jsx(M.i4,{className:"w-full",children:r.jsx(M.ki,{placeholder:e})}),r.jsx(M.Bw,{children:a.map(e=>r.jsx(M.Ql,{value:String(e),children:e},e))})]})},V=({onConfirm:e})=>r.jsx("button",{onClick:e,className:"mt-4 w-full bg-blue-600 text-white p-2 rounded-lg",children:"Confirm"});var I=s(33194),Y=s(51027),U=s(24118);let T=({field:e})=>{let t=new Date,s=new Date(t.setFullYear(t.getFullYear()-16)),a=e.value?new Date(e.value):void 0,[n,o]=(0,i.useState)(!1),[l,c]=(0,i.useState)(a?.getMonth()??s.getMonth()),[d,u]=(0,i.useState)(a?.getFullYear()??s.getFullYear()),[m,p]=(0,i.useState)(!1);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(Y.J2,{open:m,onOpenChange:p,children:[r.jsx(Y.xo,{asChild:!0,children:r.jsx(A.NI,{children:(0,r.jsxs)(_.z,{type:"button",variant:"outline",className:`w-full justify-start text-left font-normal ${a?"":"text-muted-foreground"}`,onClick:()=>o(!0),children:[a?(0,q.WU)(a,"PPP"):"Pick a date",r.jsx(S.Z,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),r.jsx(Y.yk,{className:"w-auto p-0",children:r.jsx(I.f,{mode:"single",selected:a,onSelect:t=>{e.onChange(t),p(!1)},fromYear:1970,toDate:s,defaultMonth:new Date(d,l)})})]}),r.jsx(U.Vq,{open:n,onOpenChange:o,children:(0,r.jsxs)(U.cZ,{className:"max-w-sm rounded-lg bg-[#111] mx-1 shadow-xl p-6",children:[r.jsx("h2",{className:"text-lg text-white mb-4",children:"Select Month & Year"}),r.jsx(E,{selectedMonth:l,onSelect:c}),r.jsx(O,{selectedYear:d,onSelect:u}),r.jsx(V,{onConfirm:()=>{o(!1),p(!0)}})]})})]})};function W({open:e,setOpen:t,setIsChecked:s}){return r.jsx(U.Vq,{open:e,onOpenChange:t,children:(0,r.jsxs)(U.cZ,{className:"max-w-5xl sm:mx-4 max-h-screen overflow-y-auto rounded-2xl p-6 shadow-lg",children:[r.jsx(U.fK,{children:r.jsx(U.$N,{className:"text-2xl font-bold  mb-4 text-center",children:"Terms & Conditions for Freelancer"})}),(0,r.jsxs)("div",{className:"space-y-6 text-sm  leading-relaxed px-2 sm:px-4",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-base mb-1",children:"1. Registration and Account Management"}),r.jsx("p",{children:"By registering as a freelancer or client on the platform, users agree to abide by these terms and conditions. Users must provide accurate, complete, and up-to-date information during the registration process. Failure to do so may result in account suspension or termination. The platform reserves the right to approve, reject, or terminate user accounts at its sole discretion."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-base mb-1",children:"2. KYC Verification"}),r.jsx("p",{children:"All users are required to undergo KYC (Know Your Customer) verification to validate their profiles. Providing false or misleading information or documents during the KYC process may result in immediate account termination."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-base mb-1",children:"3. Platform Responsibilities"}),r.jsx("p",{children:"The platform acts as an intermediary connecting freelancers with potential clients. While the platform facilitates this connection, it does not guarantee job opportunities, successful contracts, or project outcomes. The platform is not liable for disputes, incomplete projects, or any issues arising from freelancer-client engagements."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-base mb-1",children:"4. User Conduct"}),r.jsx("p",{children:"Users are strictly prohibited from posting fraudulent job offers or misleading information. Spam, offensive language, and abusive behavior are not tolerated in any communications conducted through the platform. Violation of this rule may result in account suspension or termination."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-base mb-1",children:"5. Data Sharing and Privacy"}),r.jsx("p",{children:"By registering on the platform, users consent to sharing their contact details and profile information with clients seeking talent. The platform ensures that user data is shared only with verified clients for legitimate hiring purposes."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-base mb-1",children:"6. Termination of Accounts"}),r.jsx("p",{children:"Accounts may be terminated if users violate platform rules, provide false information, or fail to comply with KYC verification. The platform reserves the right to investigate and take appropriate action if fraudulent or suspicious activities are detected."})]}),r.jsx("div",{children:r.jsx("p",{children:"By using this platform, all users acknowledge and agree to these terms and conditions. The platform reserves the right to modify these terms at any time with prior notice to users."})})]}),r.jsx("div",{className:"mt-8 flex justify-end",children:r.jsx(_.z,{onClick:()=>{s(!0),t(!1)},className:"px-6 py-2 text-sm font-medium rounded-md bg-primary  hover:bg-primary/90 transition",children:"I Accept"})})]})})}let $=({currentStep:e=0})=>{let t=[{id:0,title:"Personal Info",icon:p.Z},{id:1,title:"Professional Info",icon:h.Z},{id:2,title:"Verification",icon:x.Z}];return(0,r.jsxs)("div",{className:"w-full max-w-5xl mx-auto py-4 sm:py-6 mb-10 sm:mb-8",children:[(0,r.jsxs)("div",{className:"text-center space-y-2 sm:space-y-4",children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold",children:["Create Your Freelancer ",r.jsx("span",{className:"block",children:"Account"})]}),r.jsx("p",{className:"text-muted-foreground",children:"Join our community and start your Freelancing Journey."})]}),(0,r.jsxs)("div",{className:"my-4 text-center text-xs sm:text-sm",children:["Are you a business?"," ",r.jsx(_.z,{variant:"outline",size:"sm",className:"ml-2",asChild:!0,children:r.jsx(a.default,{href:"/auth/sign-up/business",children:"Register Business"})})]}),r.jsx("div",{className:"flex items-center justify-center mt-4 sm:mt-8 px-2 sm:px-0",children:t.map((s,a)=>(0,r.jsxs)(o().Fragment,{children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:`w-8 h-8 sm:w-12 sm:h-12 flex items-center justify-center rounded-full border-2 transition-all duration-300
                ${e>s.id?"bg-primary border-primary":e===s.id?"border-primary bg-background text-primary":"border-muted bg-background text-muted"}`,children:e>s.id?r.jsx(f.Z,{className:"w-4 h-4 sm:w-6 sm:h-6 text-background"}):r.jsx(s.icon,{className:"w-4 h-4 sm:w-6 sm:h-6"})}),r.jsx("span",{className:`absolute -bottom-6 left-1/2 -translate-x-1/2 text-xs sm:text-sm whitespace-nowrap font-medium
                ${e>=s.id?"text-primary":"text-muted-foreground"}`,children:s.title})]}),a<t.length-1&&r.jsx("div",{className:"w-20 sm:w-40 mx-2 sm:mx-4 h-[2px] bg-muted",children:r.jsx("div",{className:"h-full bg-primary transition-all duration-500",style:{width:e>s.id?"100%":"0%"}})})]},s.id))})]})},L=(e,t)=>e.getFullYear()-t.getFullYear()-(e<new Date(e.getFullYear(),t.getMonth(),t.getDate())?1:0),G=c.z.object({firstName:c.z.string().min(2,{message:"First Name must be at least 2 characters."}),lastName:c.z.string().min(2,{message:"Last Name must be at least 2 characters."}),email:c.z.string().email({message:"Email must be a valid email address."}),userName:c.z.string().min(4,{message:"Username must be at least 4 characters long"}).max(20,{message:"Username must be less than 20 characters long"}).regex(/^[a-zA-Z0-9]{4}[a-zA-Z0-9_]*$/,{message:"Underscore allowed only after 4 letters/numbers"}),phone:c.z.string().min(10,{message:"Phone number must be at least 10 digits."}).regex(/^\d+$/,{message:"Phone number can only contain digits."}),githubLink:c.z.string().optional().refine(e=>!e||/^https?:\/\/(www\.)?github\.com\/[a-zA-Z0-9_-]+\/?$/.test(e),{message:'GitHub URL must start with "https://github.com/" or "www.github.com/" and have a valid username'}),resume:c.z.string().optional().refine(e=>!e||/^https?:\/\/[^\s$.?#].[^\s]*$/.test(e),{message:"Resume must be a valid URL."}),linkedin:c.z.string().optional().refine(e=>!e||/^https:\/\/www\.linkedin\.com\/in\/[a-zA-Z0-9_-]+\/?$/.test(e),{message:'LinkedIn URL must start with "https://www.linkedin.com/in/" and have a valid username'}),personalWebsite:c.z.string().optional().refine(e=>!e||/^(https?:\/\/|www\.)[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}.*[a-zA-Z0-9].*$/.test(e),{message:'Invalid website URL. Must start with "www." or "https://" and contain letters'}),password:c.z.string().min(6,{message:"Password must be at least 6 characters."}),perHourPrice:c.z.number().max(300,"Per hour price must not excedd 300").refine(e=>e>=0,{message:"Price must be a non-negative number."}),referralCode:c.z.string().optional(),workExperience:c.z.number().min(0,"Work experience must be at least 0 years").max(60,"Work experience must not exceed 60 years"),dob:c.z.union([c.z.string(),c.z.date()]).optional().refine(e=>{if(!e)return!0;let t=new Date(e),s=new Date,r=new Date;return r.setFullYear(s.getFullYear()-16),t<=r},{message:"You must be at least 16 years old"}),confirmPassword:c.z.string().min(6,"Confirm Password must be at least 6 characters long")}).refine(e=>e.password===e.confirmPassword,{path:["confirmPassword"],message:"Passwords do not match"}).refine(e=>{if(!e.dob)return!0;let t=new Date(e.dob),s=L(new Date,t);return e.workExperience<=s},{path:["workExperience"],message:"Work experience cannot be greater than your age"});function B(){let[e,t]=(0,i.useState)(0);return r.jsx("div",{className:"flex w-full items-center justify-center",children:(0,r.jsxs)("div",{className:"w-full max-w-5xl px-4 sm:px-6 lg:px-4",children:[r.jsx($,{currentStep:e}),r.jsx("div",{className:"flex justify-center w-full",children:r.jsx("div",{className:"w-full max-w-4xl",children:r.jsx(H,{currentStep:e,setCurrentStep:t})})})]})})}function H({currentStep:e,setCurrentStep:t}){let[s,a]=(0,i.useState)(!1),[n,o]=(0,i.useState)(!1),[c,p]=(0,i.useState)("IN"),[h,x]=(0,i.useState)(""),[f,S]=(0,i.useState)(!1),[q,E]=(0,i.useState)(!1),[M,O]=(0,i.useState)(!1),V=(0,l.useSearchParams)(),[I,Y]=(0,i.useState)(!1),[U,$]=(0,i.useState)(null),L=()=>{o(e=>!e)},B=(0,u.cI)({resolver:(0,m.F)(G),defaultValues:{firstName:"",lastName:"",email:"",userName:"",phone:"",githubLink:"",resume:"",linkedin:"",personalWebsite:"",password:"",perHourPrice:0,workExperience:0,referralCode:"",dob:""},mode:"all"}),H=async()=>{t(e-1)},J=async()=>{if(0===e)await B.trigger(["firstName","lastName","email","dob","userName","password","confirmPassword"])?t(e+1):(0,D.Am)({variant:"destructive",title:"Validation Error",description:"Please fill in all required fields before proceeding."});else if(1===e){if(await B.trigger(["githubLink","linkedin","personalWebsite","perHourPrice","resume","workExperience"])){let{userName:s}=B.getValues();O(!0);try{if(s===U){t(e+1);return}let r=await z.b.get(`/public/username/check-duplicate?username=${s}&is_freelancer=true`);!1===r.data.duplicate?t(e+1):((0,D.Am)({variant:"destructive",title:"User Already Exists",description:"This username is already taken. Please choose another one."}),$(s))}catch(e){(0,D.Am)({variant:"destructive",title:"API Error",description:"There was an error while checking the username."})}finally{O(!1)}}else(0,D.Am)({variant:"destructive",title:"Validation Error",description:"Please fill in all required fields before proceeding."})}},K=async e=>{let t=V.get("referral"),s=e.referralCode,n=t||s||null;x(`${N.find(e=>e.code===c)?.dialCode}${e.phone}`),a(!0);let i={...e,phone:`${N.find(e=>e.code===c)?.dialCode}${e.phone}`,phoneVerify:!1,role:"freelancer",connects:0,professionalInfo:{},skills:[],domain:[],education:{},projects:{},isFreelancer:!0,refer:{name:"string",contact:"string"},pendingProject:[],rejectedProject:[],acceptedProject:[],oracleProject:[],userDataForVerification:[],interviewsAligned:[],dob:e.dob?new Date(e.dob).toISOString():null},o=n?`/register/freelancer?referralCode=${n}`:"/register/freelancer";try{await z.b.post(o,i),(0,D.Am)({title:"Account created successfully!",description:"Redirecting to login page..."}),S(!0)}catch(t){let e=t.response?.data?.message||"Something went wrong!";console.error("API Error:",t),(0,D.Am)({variant:"destructive",title:"Uh oh! Something went wrong.",description:e,action:r.jsx(d.gD,{altText:"Try again",children:"Try again"})})}finally{setTimeout(()=>a(!1),100)}};return r.jsx(A.l0,{...B,children:r.jsx("form",{onSubmit:B.handleSubmit(K),className:"w-full max-w-3xl mx-auto",children:r.jsx("div",{className:"w-full p-4 sm:p-6 rounded-lg shadow-sm border",children:(0,r.jsxs)("div",{className:"grid gap-4 sm:gap-6 w-full",children:[(0,r.jsxs)("div",{className:(0,P.cn)("grid gap-4",0===e?"":"hidden"),children:[(0,r.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[r.jsx(k.Z,{control:B.control,name:"firstName",label:"First Name",placeholder:"Max",className:"w-full"}),r.jsx(k.Z,{control:B.control,name:"lastName",label:"Last Name",placeholder:"Robinson",className:"w-full"})]}),(0,r.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[r.jsx(k.Z,{control:B.control,name:"email",label:"Email",placeholder:"<EMAIL>",type:"email"}),(0,r.jsxs)("div",{className:"flex flex-col gap-2 mt-1",children:[r.jsx(Z.Label,{className:"text-sm font-medium",children:"Date of Birth"}),r.jsx(u.Qr,{control:B.control,name:"dob",render:({field:e})=>r.jsx(T,{field:e})})]})]}),r.jsx(k.Z,{control:B.control,name:"userName",label:"Username",placeholder:"JohnDoe123"}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Z.Label,{children:"Password"}),r.jsx(A.Wi,{control:B.control,name:"password",render:({field:e})=>(0,r.jsxs)(A.xJ,{children:[r.jsx(A.NI,{children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx(R.I,{placeholder:"Enter your password",type:n?"text":"password",className:"pr-10",...e}),r.jsx("button",{type:"button",onClick:L,className:"absolute inset-y-0 right-0 px-3 flex items-center",children:n?r.jsx(g.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"}):r.jsx(j.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"})})]})}),r.jsx(A.zG,{})]})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Z.Label,{children:"Confirm Password"}),r.jsx(A.Wi,{control:B.control,name:"confirmPassword",render:({field:e})=>(0,r.jsxs)(A.xJ,{children:[r.jsx(A.NI,{children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx(R.I,{placeholder:"Confirm your password",type:n?"text":"password",className:"pr-10",...e}),r.jsx("button",{type:"button",onClick:L,className:"absolute inset-y-0 right-0 px-3 flex items-center",children:n?r.jsx(g.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"}):r.jsx(j.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"})})]})}),r.jsx(A.zG,{})]})})]}),r.jsx("div",{className:"flex gap-2 justify-end mt-4",children:r.jsx(_.z,{type:"button",onClick:J,className:"w-full sm:w-auto flex items-center justify-center",disabled:M,children:M?r.jsx(b.Z,{size:20,className:"animate-spin"}):(0,r.jsxs)(r.Fragment,{children:["Next",r.jsx(v.Z,{className:"w-4 h-4 ml-2"})]})})})]}),(0,r.jsxs)("div",{className:(0,P.cn)("grid gap-4",1===e?"":"hidden"),children:[(0,r.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[r.jsx(k.Z,{control:B.control,name:"githubLink",label:"GitHub",type:"url",placeholder:"https://github.com/yourusername",className:"w-full"}),r.jsx(k.Z,{control:B.control,name:"referralCode",label:"Referral",type:"string",placeholder:"JOHN123",className:"w-full"})]}),(0,r.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[r.jsx(k.Z,{control:B.control,name:"linkedin",label:"LinkedIn",type:"url",placeholder:"https://linkedin.com/in/yourprofile",className:"w-full"}),r.jsx(k.Z,{control:B.control,name:"personalWebsite",label:"Personal Website",type:"url",placeholder:"https://www.yourwebsite.com",className:"w-full"})]}),(0,r.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[r.jsx(k.Z,{control:B.control,name:"perHourPrice",label:"Hourly Rate ($)",type:"number",placeholder:"0",className:"w-full"}),r.jsx(k.Z,{control:B.control,name:"resume",label:"Resume (URL)",type:"url",placeholder:"Enter Google Drive Resume Link",className:"w-full"})]}),r.jsx("div",{className:"grid gap-4 sm:grid-cols-2",children:r.jsx(k.Z,{control:B.control,name:"workExperience",label:"Work Experience (Years)",type:"number",placeholder:"0",className:"w-full"})}),(0,r.jsxs)("div",{className:"flex gap-2 justify-between mt-4",children:[(0,r.jsxs)(_.z,{type:"button",onClick:H,className:"w-full sm:w-auto",children:[r.jsx(w.Z,{className:"w-4 h-4 mr-2"}),"Previous"]}),(0,r.jsxs)(_.z,{type:"button",onClick:J,className:"w-full sm:w-auto",children:["Next",r.jsx(v.Z,{className:"w-4 h-4 ml-2"})]})]})]}),(0,r.jsxs)("div",{className:(0,P.cn)("grid gap-4",2===e?"":"hidden"),children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Z.Label,{htmlFor:"phone",children:"Phone Number"}),r.jsx(C.Z,{control:B.control,setCode:p,code:c})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-4",children:[r.jsx("input",{type:"checkbox",id:"terms",checked:q,onChange:()=>{I||E(!q)},className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,r.jsxs)("label",{htmlFor:"terms",className:"text-sm text-gray-600",children:["I agree to the"," ",r.jsx("span",{onClick:()=>Y(!0),className:"text-primary hover:underline",children:"Terms and Conditions"})]}),r.jsx(W,{open:I,setOpen:Y,setIsChecked:E})]}),(0,r.jsxs)("div",{className:"flex gap-2 flex-col sm:flex-row justify-between mt-4",children:[(0,r.jsxs)(_.z,{type:"button",onClick:H,className:"w-full sm:w-auto",children:[r.jsx(w.Z,{className:"w-4 h-4 mr-2"}),"Previous"]}),(0,r.jsxs)(_.z,{type:"submit",className:"w-full sm:w-auto",disabled:s||!q,children:[s?r.jsx(b.Z,{className:"mr-2 h-4 w-4 animate-spin"}):r.jsx(y.Z,{className:"mr-2 h-4 w-4"}),"Create account"]})]})]}),r.jsx(F.Z,{phoneNumber:h,isModalOpen:f,setIsModalOpen:S})]})})})})}function J(){return(0,r.jsxs)("div",{className:"relative min-h-screen",children:[r.jsx("div",{className:"absolute left-4 top-4 sm:left-10 sm:top-10",children:r.jsx(n.T,{})}),r.jsx("div",{className:"flex items-center justify-center py-20 sm:py-12",children:r.jsx("div",{className:"mx-auto w-full px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"grid gap-6",children:[r.jsx(B,{}),(0,r.jsxs)("div",{className:"mt-4 text-center text-xs sm:text-sm",children:["Already have an account?"," ",r.jsx(_.z,{variant:"outline",size:"sm",className:"ml-2",asChild:!0,children:r.jsx(a.default,{href:"/auth/login",children:"Sign in"})})]}),(0,r.jsxs)("p",{className:"px-2 text-center text-xs text-muted-foreground sm:px-8 sm:text-sm",children:["By clicking continue, you agree to our"," ",r.jsx(_.z,{variant:"link",className:"p-0",asChild:!0,children:r.jsx(a.default,{href:"/terms",children:"Terms of Service"})})," ","and"," ",r.jsx(_.z,{variant:"link",className:"p-0",asChild:!0,children:r.jsx(a.default,{href:"/privacy",children:"Privacy Policy."})})]})]})})})]})}},33194:(e,t,s)=>{"use strict";s.d(t,{f:()=>c});var r=s(10326);s(17577);var a=s(11890),n=s(39183),i=s(25579),o=s(51223),l=s(91664);function c({className:e,classNames:t,showOutsideDays:s=!0,...c}){return r.jsx(i._W,{showOutsideDays:s,className:(0,o.cn)("p-3",e),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,o.cn)((0,l.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,o.cn)((0,l.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:({...e})=>r.jsx(a.Z,{className:"h-4 w-4"}),IconRight:({...e})=>r.jsx(n.Z,{className:"h-4 w-4"})},...c})}c.displayName="Calendar"},51027:(e,t,s)=>{"use strict";s.d(t,{J2:()=>o,xo:()=>l,yk:()=>c});var r=s(10326),a=s(17577),n=s(78728),i=s(51223);let o=n.fC,l=n.xz,c=a.forwardRef(({className:e,align:t="center",sideOffset:s=4,...a},o)=>r.jsx(n.h_,{children:r.jsx(n.VY,{ref:o,align:t,sideOffset:s,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a})}));c.displayName=n.VY.displayName},82487:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>o});var r=s(68570);let a=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\auth\sign-up\freelancer\page.tsx`),{__esModule:n,$$typeof:i}=a;a.default;let o=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\auth\sign-up\freelancer\page.tsx#default`)},78728:(e,t,s)=>{"use strict";s.d(t,{VY:()=>G,h_:()=>L,fC:()=>W,xz:()=>$});var r=s(17577),a=s(82561),n=s(48051),i=s(93095),o=s(825),l=s(80699),c=s(10441),d=s(88957),u=s(17103),m=s(83078),p=s(9815),h=s(77335),x=s(10326),f=r.forwardRef((e,t)=>{let{children:s,...a}=e,n=r.Children.toArray(s),i=n.find(b);if(i){let e=i.props.children,s=n.map(t=>t!==i?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,x.jsx)(g,{...a,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,s):null})}return(0,x.jsx)(g,{...a,ref:t,children:s})});f.displayName="Slot";var g=r.forwardRef((e,t)=>{let{children:s,...a}=e;if(r.isValidElement(s)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,s=t&&"isReactWarning"in t&&t.isReactWarning;return s?e.ref:(s=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s);return r.cloneElement(s,{...function(e,t){let s={...t};for(let r in t){let a=e[r],n=t[r];/^on[A-Z]/.test(r)?a&&n?s[r]=(...e)=>{n(...e),a(...e)}:a&&(s[r]=a):"style"===r?s[r]={...a,...n}:"className"===r&&(s[r]=[a,n].filter(Boolean).join(" "))}return{...e,...s}}(a,s.props),ref:t?(0,n.F)(t,e):e})}return r.Children.count(s)>1?r.Children.only(null):null});g.displayName="SlotClone";var j=({children:e})=>(0,x.jsx)(x.Fragment,{children:e});function b(e){return r.isValidElement(e)&&e.type===j}var v=s(52067),w=s(35664),y=s(17397),N="Popover",[C,P]=(0,i.b)(N,[u.D7]),k=(0,u.D7)(),[_,z]=C(N),D=e=>{let{__scopePopover:t,children:s,open:a,defaultOpen:n,onOpenChange:i,modal:o=!1}=e,l=k(t),c=r.useRef(null),[m,p]=r.useState(!1),[h=!1,f]=(0,v.T)({prop:a,defaultProp:n,onChange:i});return(0,x.jsx)(u.fC,{...l,children:(0,x.jsx)(_,{scope:t,contentId:(0,d.M)(),triggerRef:c,open:h,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),hasCustomAnchor:m,onCustomAnchorAdd:r.useCallback(()=>p(!0),[]),onCustomAnchorRemove:r.useCallback(()=>p(!1),[]),modal:o,children:s})})};D.displayName=N;var Z="PopoverAnchor";r.forwardRef((e,t)=>{let{__scopePopover:s,...a}=e,n=z(Z,s),i=k(s),{onCustomAnchorAdd:o,onCustomAnchorRemove:l}=n;return r.useEffect(()=>(o(),()=>l()),[o,l]),(0,x.jsx)(u.ee,{...i,...a,ref:t})}).displayName=Z;var A="PopoverTrigger",R=r.forwardRef((e,t)=>{let{__scopePopover:s,...r}=e,i=z(A,s),o=k(s),l=(0,n.e)(t,i.triggerRef),c=(0,x.jsx)(h.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":T(i.open),...r,ref:l,onClick:(0,a.M)(e.onClick,i.onOpenToggle)});return i.hasCustomAnchor?c:(0,x.jsx)(u.ee,{asChild:!0,...o,children:c})});R.displayName=A;var F="PopoverPortal",[S,q]=C(F,{forceMount:void 0}),E=e=>{let{__scopePopover:t,forceMount:s,children:r,container:a}=e,n=z(F,t);return(0,x.jsx)(S,{scope:t,forceMount:s,children:(0,x.jsx)(p.z,{present:s||n.open,children:(0,x.jsx)(m.h,{asChild:!0,container:a,children:r})})})};E.displayName=F;var M="PopoverContent",O=r.forwardRef((e,t)=>{let s=q(M,e.__scopePopover),{forceMount:r=s.forceMount,...a}=e,n=z(M,e.__scopePopover);return(0,x.jsx)(p.z,{present:r||n.open,children:n.modal?(0,x.jsx)(V,{...a,ref:t}):(0,x.jsx)(I,{...a,ref:t})})});O.displayName=M;var V=r.forwardRef((e,t)=>{let s=z(M,e.__scopePopover),i=r.useRef(null),o=(0,n.e)(t,i),l=r.useRef(!1);return r.useEffect(()=>{let e=i.current;if(e)return(0,w.Ry)(e)},[]),(0,x.jsx)(y.Z,{as:f,allowPinchZoom:!0,children:(0,x.jsx)(Y,{...e,ref:o,trapFocus:s.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),l.current||s.triggerRef.current?.focus()}),onPointerDownOutside:(0,a.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,s=0===t.button&&!0===t.ctrlKey,r=2===t.button||s;l.current=r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,a.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),I=r.forwardRef((e,t)=>{let s=z(M,e.__scopePopover),a=r.useRef(!1),n=r.useRef(!1);return(0,x.jsx)(Y,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(a.current||s.triggerRef.current?.focus(),t.preventDefault()),a.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(a.current=!0,"pointerdown"!==t.detail.originalEvent.type||(n.current=!0));let r=t.target;s.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),Y=r.forwardRef((e,t)=>{let{__scopePopover:s,trapFocus:r,onOpenAutoFocus:a,onCloseAutoFocus:n,disableOutsidePointerEvents:i,onEscapeKeyDown:d,onPointerDownOutside:m,onFocusOutside:p,onInteractOutside:h,...f}=e,g=z(M,s),j=k(s);return(0,l.EW)(),(0,x.jsx)(c.M,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:a,onUnmountAutoFocus:n,children:(0,x.jsx)(o.XB,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:h,onEscapeKeyDown:d,onPointerDownOutside:m,onFocusOutside:p,onDismiss:()=>g.onOpenChange(!1),children:(0,x.jsx)(u.VY,{"data-state":T(g.open),role:"dialog",id:g.contentId,...j,...f,ref:t,style:{...f.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),U="PopoverClose";function T(e){return e?"open":"closed"}r.forwardRef((e,t)=>{let{__scopePopover:s,...r}=e,n=z(U,s);return(0,x.jsx)(h.WV.button,{type:"button",...r,ref:t,onClick:(0,a.M)(e.onClick,()=>n.onOpenChange(!1))})}).displayName=U,r.forwardRef((e,t)=>{let{__scopePopover:s,...r}=e,a=k(s);return(0,x.jsx)(u.Eh,{...a,...r,ref:t})}).displayName="PopoverArrow";var W=D,$=R,L=E,G=O}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,4198,6034,4718,5645,2146,6686,4736,9169,8893],()=>s(45184));module.exports=r})();