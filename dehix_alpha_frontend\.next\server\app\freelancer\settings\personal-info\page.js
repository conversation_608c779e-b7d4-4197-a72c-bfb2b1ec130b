(()=>{var e={};e.id=13,e.ids=[13],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},18878:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c}),r(3517),r(54302),r(12523);var s=r(23191),a=r(88716),l=r(37922),i=r.n(l),n=r(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let c=["",{children:["freelancer",{children:["settings",{children:["personal-info",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3517)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\settings\\personal-info\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\settings\\personal-info\\page.tsx"],m="/freelancer/settings/personal-info/page",u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/freelancer/settings/personal-info/page",pathname:"/freelancer/settings/personal-info",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},95919:(e,t,r)=>{Promise.resolve().then(r.bind(r,8509))},6343:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},47546:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},48705:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},8509:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>T});var s=r(10326),a=r(25842),l=r(92166),i=r(17577),n=r(74064),o=r(74723),c=r(27256),d=r(83855),m=r(94019),u=r(37956),p=r(29752),x=r(82015),h=r(82287),f=r(36283);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let g=(0,r(80851).Z)("CloudUpload",[["path",{d:"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242",key:"1pljnt"}],["path",{d:"M12 12v9",key:"192myk"}],["path",{d:"m16 16-4-4-4 4",key:"119tzi"}]]);var j=r(91664),v=r(56627),b=r(6260);let N=["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],y=()=>{let[e,t]=(0,i.useState)(null),[r,a]=(0,i.useState)(null),[l,n]=(0,i.useState)(!1),[o,c]=(0,i.useState)(null),d=(0,i.useRef)(null),u=e=>{if(!e)return"";let t=e.includes(".")?e.substring(e.lastIndexOf(".")):"";return e.length>20?`${e.substring(0,20-t.length)}...${t}`:e},p=async t=>{if(t.preventDefault(),!e){(0,v.Am)({variant:"destructive",title:"No Resume Selected",description:"Please select a resume before uploading."});return}let r=new FormData;r.append("resume",e);try{n(!0);let{Location:t}=(await b.b.post("/register/upload-image",r,{headers:{"Content-Type":"multipart/form-data"}})).data.data;if(!t)throw Error("Failed to upload the resume.");let s=await b.b.put("/freelancer",{resume:t});if(200===s.status)a(e.name),(0,v.Am)({title:"Success",description:"Resume uploaded successfully!"});else throw Error("Failed to update resume.")}catch(e){(0,v.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}finally{n(!1)}};return(0,i.useEffect)(()=>{(async()=>{try{let e=await b.b.get("/freelancer");e.data.resume&&(a(e.data.resume),c(e.data.resume))}catch(e){console.error("Error fetching resume:",e)}})()},[]),s.jsx("div",{className:"upload-form max-w-md mx-auto rounded shadow-md p-4",children:(0,s.jsxs)("div",{className:"space-y-6 flex flex-col items-center",children:[s.jsx("div",{className:"flex flex-col items-center justify-center border-dashed border-2 border-gray-400 rounded-lg p-6 w-full cursor-pointer",onClick:()=>d.current?.click(),children:e?(0,s.jsxs)("div",{className:"w-full flex flex-col items-center gap-4 text-gray-700 text-center",children:[(0,s.jsxs)("div",{className:"flex flex-1 gap-6",children:[s.jsx("p",{className:"truncate",children:u(e.name)}),s.jsx("button",{className:"bg-red-600 text-white rounded-full p-1 hover:bg-red-700",onClick:()=>{t(null),c(null),a(null)},"aria-label":"Remove file",children:s.jsx(m.Z,{className:"w-4 h-4"})})]}),o?s.jsx("iframe",{src:o,title:"Resume Preview",className:"w-full h-40 border rounded"}):(0,s.jsxs)("div",{className:"flex items-center space-x-2 p-2 bg-gray-100 rounded",children:[s.jsx(f.Z,{className:"text-gray-500 w-6 h-6"}),s.jsx("span",{className:"text-gray-600 text-sm",children:u(e.name)})]})]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(g,{className:"text-gray-500 w-12 h-12 mb-2"}),s.jsx("p",{className:"text-gray-700 text-center",children:"Drag and drop your resume here or click to upload"}),s.jsx("div",{className:"flex items-center mt-2",children:s.jsx("span",{className:"text-gray-600 text-xs md:text-sm",children:"Supported formats: PDF, DOCX."})}),s.jsx("input",{type:"file",accept:N.join(","),onChange:e=>{let r=e.target.files?.[0];r&&(N.includes(r.type)?r.size<=5242880?(t(r),a(r.name),"application/pdf"===r.type?c(URL.createObjectURL(r)):c(null)):(0,v.Am)({variant:"destructive",title:"File too large",description:"Resume size should not exceed 5MB."}):(0,v.Am)({variant:"destructive",title:"Invalid file type",description:"Supported formats: PDF, DOCX."}))},className:"hidden",ref:d})]})}),e&&s.jsx(j.z,{onClick:p,className:"w-full",disabled:l,children:l?"Uploading...":"Upload Resume"}),r&&(0,s.jsxs)("p",{className:"text-center text-gray-600",children:["Uploaded:"," ",s.jsx("strong",{children:u(r||"")})]})]})})},w=["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],E=()=>{let[e,t]=(0,i.useState)(null),[r,a]=(0,i.useState)(null),[l,n]=(0,i.useState)(!1),[o,c]=(0,i.useState)(null),d=(0,i.useRef)(null),u=e=>{if(!e)return"";let t=e.includes(".")?e.substring(e.lastIndexOf(".")):"";return e.length>20?`${e.substring(0,20-t.length)}...${t}`:e},p=async t=>{if(t.preventDefault(),!e){(0,v.Am)({variant:"destructive",title:"No Cover Letter Selected",description:"Please select a cover letter before uploading."});return}if(!w.includes(e.type)){(0,v.Am)({variant:"destructive",title:"Invalid File Type",description:"Please select a PDF or DOCX file."});return}if(e.size>5242880){(0,v.Am)({variant:"destructive",title:"File Too Large",description:"Cover letter size should not exceed 5MB."});return}let r=new FormData;r.append("file",e),console.log("Uploading cover letter:",{fileName:e.name,fileSize:e.size,fileType:e.type});try{n(!0);let t=await b.b.post("/register/upload-image",r,{headers:{"Content-Type":"multipart/form-data"}});console.log("Upload response:",t.data);let{Location:s}=t.data.data;if(!s)throw console.error("No Location in response:",t.data),Error("Failed to upload the cover letter - no URL returned.");console.log("Updating freelancer profile with cover letter URL:",s);let l=await b.b.put("/freelancer",{coverLetter:s});if(console.log("Profile update response:",l.data),200===l.status)a(e.name),(0,v.Am)({title:"Success",description:"Cover letter uploaded successfully!"});else throw console.error("Profile update failed:",l),Error("Failed to update cover letter in profile.")}catch(e){console.error("Cover letter upload error:",e),console.error("Error response:",e.response?.data),console.error("Error status:",e.response?.status),(0,v.Am)({variant:"destructive",title:"Error",description:e.response?.data?.message||"Something went wrong. Please try again."})}finally{n(!1)}};return(0,i.useEffect)(()=>{(async()=>{try{let e=await b.b.get("/freelancer");console.log("Fetched freelancer data:",e.data),e.data.coverLetter&&(a(e.data.coverLetter),c(e.data.coverLetter))}catch(e){console.error("Error fetching cover letter:",e)}})()},[]),s.jsx("div",{className:"upload-form max-w-md mx-auto rounded shadow-md p-4",children:(0,s.jsxs)("div",{className:"space-y-6 flex flex-col items-center",children:[s.jsx("div",{className:"flex flex-col items-center justify-center border-dashed border-2 border-gray-400 rounded-lg p-6 w-full cursor-pointer",onClick:()=>d.current?.click(),children:e?(0,s.jsxs)("div",{className:"w-full flex flex-col items-center gap-4 text-gray-700 text-center",children:[(0,s.jsxs)("div",{className:"flex flex-1 gap-6",children:[s.jsx("p",{className:"truncate",children:u(e.name)}),s.jsx("button",{className:"bg-red-600 text-white rounded-full p-1 hover:bg-red-700",onClick:()=>{t(null),c(null),a(null)},"aria-label":"Remove file",children:s.jsx(m.Z,{className:"w-4 h-4"})})]}),o?s.jsx("iframe",{src:o,title:"Cover Letter Preview",className:"w-full h-40 border rounded"}):(0,s.jsxs)("div",{className:"flex items-center space-x-2 p-2 bg-gray-100 rounded",children:[s.jsx(f.Z,{className:"text-gray-500 w-6 h-6"}),s.jsx("span",{className:"text-gray-600 text-sm",children:u(e.name)})]})]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(g,{className:"text-gray-500 w-12 h-12 mb-2"}),s.jsx("p",{className:"text-gray-700 text-center",children:"Drag and drop your cover letter here or click to upload"}),s.jsx("div",{className:"flex items-center mt-2",children:s.jsx("span",{className:"text-gray-600 text-xs md:text-sm",children:"Supported formats: PDF, DOCX."})}),s.jsx("input",{type:"file",accept:w.join(","),onChange:e=>{let r=e.target.files?.[0];r&&(w.includes(r.type)?r.size<=5242880?(t(r),a(r.name),"application/pdf"===r.type?c(URL.createObjectURL(r)):c(null)):(0,v.Am)({variant:"destructive",title:"File too large",description:"Cover letter size should not exceed 5MB."}):(0,v.Am)({variant:"destructive",title:"Invalid file type",description:"Supported formats: PDF, DOCX."}))},className:"hidden",ref:d})]})}),e&&s.jsx(j.z,{onClick:p,className:"w-full",disabled:l,children:l?"Uploading...":"Upload Cover Letter"}),r&&(0,s.jsxs)("p",{className:"text-center text-gray-600",children:["Uploaded:"," ",s.jsx("strong",{children:u(r||"")})]})]})})};var I=r(9969),C=r(41190),P=r(78062),D=r(38443),k=r(29280),A=r(58285),R=r(39958);let S=c.z.object({firstName:c.z.string().min(2,{message:"First Name must be at least 2 characters."}),lastName:c.z.string().min(2,{message:"Last Name must be at least 2 characters."}),username:c.z.string().min(2,{message:"Username must be at least 2 characters."}).max(30,{message:"Username must not be longer than 30 characters."}),email:c.z.string().email(),phone:c.z.string().min(10,{message:"Phone number must be at least 10 digits."}),role:c.z.string(),personalWebsite:c.z.string().url().optional(),resume:c.z.string().url().optional(),coverLetter:c.z.string().url().optional(),description:c.z.string().max(500,{message:"Description cannot exceed 500 characters."})});function L({user_id:e}){let[t,r]=(0,i.useState)({}),[a,l]=(0,i.useState)([]),[c,f]=(0,i.useState)([]),[g,N]=(0,i.useState)(""),[w,L]=(0,i.useState)([]),[F,z]=(0,i.useState)([]),[T,V]=(0,i.useState)(""),[U,_]=(0,i.useState)([]),[O,q]=(0,i.useState)([]),[B,M]=(0,i.useState)(""),[Z,G]=(0,i.useState)(!1),[W,X]=(0,i.useState)(!1),[,$]=(0,i.useState)({skills:[],projectsDomains:[],domains:[]}),[J,H]=(0,i.useState)({label:"",description:""}),[Q,K]=(0,i.useState)({label:"",description:""}),[Y,ee]=(0,i.useState)({label:"",description:""}),[et]=(0,i.useState)(null),er=(0,o.cI)({resolver:(0,n.F)(S),defaultValues:{firstName:"",lastName:"",username:"",email:"",phone:"",role:""},mode:"all"}),es=()=>{(function(e,t,r){let s=e.trim();if(t.some(e=>e.label===s))return console.warn(`${s} already exists in the dropdown.`);r([...t,{label:s}])})(g,a,l),g&&!c.some(e=>e.name===g)&&(f([...c,{name:g,level:"",experience:"",interviewStatus:R.sB.PENDING,interviewInfo:"",interviewerRating:0}]),$(e=>({...e,skills:[...e.skills,{name:g}]})),N(""))},ea=async()=>{if(!J.label.trim()){console.warn("Field is required.");return}let t={label:J.label,interviewInfo:J.description,createdBy:A.Dy.FREELANCER,createdById:e,status:R.sB.ACTIVE};try{await b.b.post("/skills",t);let e=[...a,{label:J.label}];L(e),f([...c,{name:J.label,level:"",experience:"",interviewStatus:"PENDING",interviewInfo:J.description,interviewerRating:0}]),H({label:"",description:""}),X(!1)}catch(e){console.error("Failed to add skill:",e.response?.data||e.message),(0,v.Am)({variant:"destructive",title:"Error",description:"Failed to add skill. Please try again."})}finally{G(!1)}},el=async()=>{if(!Q.label.trim()){console.warn("Field is required.");return}let t={label:Q.label,interviewInfo:J.description,createdBy:A.Dy.FREELANCER,createdById:e,status:R.sB.ACTIVE};try{await b.b.post("/domain",t);let e=[...w,{label:Q.label}];L(e),z([...F,{name:Q.label,level:"",experience:"",interviewStatus:"PENDING",interviewInfo:Q.description,interviewerRating:0}]),K({label:"",description:""}),X(!1)}catch(e){console.error("Failed to add domain:",e.response?.data||e.message),(0,v.Am)({variant:"destructive",title:"Error",description:"Failed to add domain. Please try again."})}finally{G(!1)}},ei=async()=>{if(!Y.label.trim()){console.warn("Field is required.");return}let t={label:Y.label,createdBy:A.Dy.FREELANCER,createdById:e,status:R.sB.ACTIVE};try{await b.b.post("/projectdomain",t);let e=[...U,{label:Y.label}];_(e),q([...O,{name:Y.label,level:"",experience:"",interviewStatus:"PENDING",interviewInfo:Y.description,interviewerRating:0}]),ee({label:"",description:""}),X(!1)}catch(e){console.error("Failed to add project domain:",e.response?.data||e.message),(0,v.Am)({variant:"destructive",title:"Error",description:"Failed to add project domain. Please try again."})}finally{G(!1)}},en=()=>{(function(e,t,r){let s=e.trim();if(t.some(e=>e.label===s))return console.warn(`${s} already exists in the dropdown.`);r([...t,{label:s}])})(T,w,L),T&&!F.some(e=>e.name===T)&&(z([...F,{name:T,level:"",experience:"",interviewStatus:R.sB.PENDING,interviewInfo:"",interviewerRating:0}]),$(e=>({...e,domains:[...e.domains,{name:T}]})),V(""))},eo=()=>{(function(e,t,r){let s=e.trim();if(t.some(e=>e.label===s))return console.warn(`${s} already exists in the dropdown.`);r([...t,{label:s}])})(B,U,_),B&&!O.some(e=>e.name===e)&&(q([...O,{name:B,level:"",experience:"",interviewStatus:R.sB.PENDING,interviewInfo:"",interviewerRating:0}]),$(e=>({...e,projectsDomains:[...e.projectsDomains,{name:B}]})),M(""))},ec=e=>{f(c.filter(t=>t.name!==e))},ed=e=>{z(F.filter(t=>t.name!==e))},em=e=>{q(O.filter(t=>t.name!==e))},[eu,ep]=(0,i.useState)("");async function ex(e){G(!0);try{let{...s}=e,a=c.map(e=>({...e,interviewInfo:e.interviewInfo||"",interviewerRating:e.interviewerRating||0,interviewStatus:e.interviewStatus||"PENDING"}));await b.b.put("/freelancer",{...s,resume:e.resume,coverLetter:e.coverLetter,skills:a,domain:F,projectDomain:O,description:e.description}),r({...t,firstName:e.firstName,lastName:e.lastName,userName:e.username,email:e.email,phone:e.phone,role:e.role,personalWebsite:e.personalWebsite,resume:e.resume,coverLetter:e.coverLetter,skills:a,domain:F,projectDomains:O}),(0,v.Am)({title:"Profile Updated",description:"Your profile has been successfully updated."})}catch(e){console.error("API Error:",e),(0,v.Am)({variant:"destructive",title:"Error",description:"Failed to update profile. Please try again later."})}finally{G(!1)}}return s.jsx(p.Zb,{className:"p-10",children:(0,s.jsxs)(I.l0,{...er,children:[s.jsx(h.Z,{profile:t.profilePic,entityType:A.Dy.FREELANCER}),(0,s.jsxs)("form",{onSubmit:er.handleSubmit(ex),className:"grid gap-10 grid-cols-1 sm:grid-cols-2 mt-4",children:[s.jsx(I.Wi,{control:er.control,name:"firstName",render:({field:e})=>(0,s.jsxs)(I.xJ,{children:[s.jsx(I.lX,{children:"First Name"}),s.jsx(I.NI,{children:s.jsx(C.I,{placeholder:"Enter your first name",...e})}),s.jsx(I.zG,{})]})}),s.jsx(I.Wi,{control:er.control,name:"lastName",render:({field:e})=>(0,s.jsxs)(I.xJ,{children:[s.jsx(I.lX,{children:"Last Name"}),s.jsx(I.NI,{children:s.jsx(C.I,{placeholder:"Enter your last name",...e})}),s.jsx(I.zG,{})]})}),s.jsx(I.Wi,{control:er.control,name:"username",render:({field:e})=>(0,s.jsxs)(I.xJ,{children:[s.jsx(I.lX,{children:"Username"}),s.jsx(I.NI,{children:s.jsx(C.I,{placeholder:"Enter your username",...e,readOnly:!0})}),s.jsx(I.zG,{}),s.jsx(I.pf,{children:"Non editable field"})]})}),s.jsx(I.Wi,{control:er.control,name:"email",render:({field:e})=>(0,s.jsxs)(I.xJ,{children:[s.jsx(I.lX,{children:"Email"}),s.jsx(I.NI,{children:s.jsx(C.I,{placeholder:"Enter your email",...e,readOnly:!0})}),s.jsx(I.pf,{children:"Non editable field"}),s.jsx(I.zG,{})]})}),s.jsx(I.Wi,{control:er.control,name:"description",render:({field:e})=>(0,s.jsxs)(I.xJ,{className:"sm:col-span-2",children:[s.jsx(I.lX,{children:"Description"}),s.jsx(I.NI,{children:s.jsx(x.g,{placeholder:"Enter description",...e})}),s.jsx(I.zG,{})]})}),s.jsx(I.Wi,{control:er.control,name:"phone",render:({field:e})=>(0,s.jsxs)(I.xJ,{children:[s.jsx(I.lX,{children:"Phone Number"}),s.jsx(I.NI,{children:s.jsx(C.I,{placeholder:"+91",...e,readOnly:!0})}),s.jsx(I.zG,{}),s.jsx(I.pf,{children:"Non editable field"})]})}),s.jsx(I.Wi,{control:er.control,name:"personalWebsite",render:({field:e})=>(0,s.jsxs)(I.xJ,{children:[s.jsx(I.lX,{children:"Personal Website URL"}),s.jsx(I.NI,{children:s.jsx(C.I,{placeholder:"Enter your LinkedIn URL",type:"url",...e})}),s.jsx(I.pf,{children:"Enter your Personal Website URL"}),s.jsx(I.zG,{})]})}),s.jsx(I.Wi,{control:er.control,name:"resume",render:({field:e})=>(0,s.jsxs)(I.xJ,{children:[s.jsx(I.lX,{children:"Resume URL"}),s.jsx(I.NI,{children:s.jsx(C.I,{placeholder:"Enter your Resume URL",type:"url",...e})}),s.jsx(I.pf,{children:"Enter your Resume URL"}),s.jsx(I.zG,{})]})}),s.jsx(P.Separator,{className:"col-span-2"}),s.jsx("div",{className:"sm:col-span-2",children:(0,s.jsxs)("div",{className:"grid gap-10 grid-cols-1 sm:grid-cols-6",children:[s.jsx("div",{className:"sm:col-span-2",children:(0,s.jsxs)("div",{className:"flex-1 min-w-[350px] max-w-[500px] mt-5",children:[s.jsx(I.lX,{children:"Skills"}),(0,s.jsxs)("div",{className:"flex items-center mt-2",children:[(0,s.jsxs)(k.Ph,{onValueChange:e=>{N(e),ep("")},value:g||"",onOpenChange:e=>{e||ep("")},children:[s.jsx(k.i4,{children:s.jsx(k.ki,{placeholder:g||"Select skill"})}),(0,s.jsxs)(k.Bw,{children:[(0,s.jsxs)("div",{className:"p-2 relative",children:[s.jsx("input",{type:"text",value:eu,onChange:e=>ep(e.target.value),className:"w-full p-2 border border-gray-300 rounded-lg text-sm",placeholder:"Search skills"}),eu&&s.jsx("button",{onClick:()=>ep(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2",children:"\xd7"})]}),a.filter(e=>e.label.toLowerCase().includes(eu.toLowerCase())&&!c.some(t=>t.name===e.label)).map((e,t)=>s.jsx(k.Ql,{value:e.label,children:e.label},t)),0===a.filter(e=>e.label.toLowerCase().includes(eu.toLowerCase())&&!c.some(t=>t.name===e.label)).length&&s.jsx("div",{className:"p-2 text-gray-500 italic text-center",children:"No matching skills"})]})]}),s.jsx(j.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",disabled:!g,onClick:()=>{es(),N(""),ep("")},children:s.jsx(d.Z,{className:"h-4 w-4"})})]}),s.jsx("div",{className:"flex flex-wrap gap-2 mt-5",children:c.map((e,t)=>(0,s.jsxs)(D.C,{className:"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1",children:[e.name,s.jsx("button",{type:"button",onClick:()=>ec(e.name),className:"ml-2 text-red-500 hover:text-red-700",children:s.jsx(m.Z,{className:"h-4 w-4"})})]},t))})]})}),s.jsx("div",{className:"sm:col-span-2",children:(0,s.jsxs)("div",{className:"flex-1 min-w-[350px] max-w-[500px] mt-5",children:[s.jsx(I.lX,{children:"Domains"}),(0,s.jsxs)("div",{className:"flex items-center mt-2",children:[(0,s.jsxs)(k.Ph,{onValueChange:e=>{V(e),ep("")},value:T||"",onOpenChange:e=>{e||ep("")},children:[s.jsx(k.i4,{children:s.jsx(k.ki,{placeholder:T||"Select domain"})}),(0,s.jsxs)(k.Bw,{children:[(0,s.jsxs)("div",{className:"p-2 relative",children:[s.jsx("input",{type:"text",value:eu,onChange:e=>ep(e.target.value),className:"w-full p-2 border border-gray-300 rounded-lg text-sm",placeholder:"Search domains"}),eu&&s.jsx("button",{onClick:()=>ep(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2",children:"\xd7"})]}),w.filter(e=>e.label.toLowerCase().includes(eu.toLowerCase())&&!F.some(t=>t.name===e.label)).map((e,t)=>s.jsx(k.Ql,{value:e.label,children:e.label},t)),0===w.filter(e=>e.label.toLowerCase().includes(eu.toLowerCase())&&!F.some(e=>e.name===w.name)).length&&s.jsx("div",{className:"p-2 text-gray-500 italic text-center",children:"No matching domains"})]})]}),s.jsx(j.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",disabled:!T,onClick:()=>{en(),V(""),ep("")},children:s.jsx(d.Z,{className:"h-4 w-4"})})]}),s.jsx("div",{className:"flex flex-wrap gap-2 mt-5",children:F.map((e,t)=>(0,s.jsxs)(D.C,{className:"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1",children:[e.name,s.jsx("button",{type:"button",onClick:()=>ed(e.name),className:"ml-2 text-red-500 hover:text-red-700",children:s.jsx(m.Z,{className:"h-4 w-4"})})]},t))})]})}),s.jsx("div",{className:"sm:col-span-2",children:(0,s.jsxs)("div",{className:"flex-1 min-w-[350px] max-w-[500px] mt-5",children:[s.jsx(I.lX,{children:"Project Domains"}),(0,s.jsxs)("div",{className:"flex items-center mt-2",children:[(0,s.jsxs)(k.Ph,{onValueChange:e=>{M(e),ep("")},value:B||"",onOpenChange:e=>{e||ep("")},children:[s.jsx(k.i4,{children:s.jsx(k.ki,{placeholder:B||"Select project domain"})}),(0,s.jsxs)(k.Bw,{children:[(0,s.jsxs)("div",{className:"p-2 relative",children:[s.jsx("input",{type:"text",value:eu,onChange:e=>ep(e.target.value),className:"w-full p-2 border border-gray-300 rounded-lg text-sm",placeholder:"Search project domains"}),eu&&s.jsx("button",{onClick:()=>ep(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2",children:"\xd7"})]}),U.filter(e=>e.label.toLowerCase().includes(eu.toLowerCase())&&!O.some(t=>t.name===e.label)).map((e,t)=>s.jsx(k.Ql,{value:e.label,children:e.label},t)),0===U.filter(e=>e.label.toLowerCase().includes(eu.toLowerCase())&&!O.some(e=>e.name===U.name)).length&&s.jsx("div",{className:"p-2 text-gray-500 italic text-center",children:"No matching domains"})]})]}),s.jsx(j.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",disabled:!B,onClick:()=>{eo(),M(""),ep("")},children:s.jsx(d.Z,{className:"h-4 w-4"})})]}),s.jsx("div",{className:"flex flex-wrap gap-2 mt-5",children:O.map((e,t)=>(0,s.jsxs)(D.C,{className:"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1",children:[e.name,s.jsx("button",{type:"button",onClick:()=>em(e.name),className:"ml-2 text-red-500 hover:text-red-700",children:s.jsx(m.Z,{className:"h-4 w-4"})})]},t))})]})})]})}),s.jsx(P.Separator,{className:"col-span-2 mt-0"}),(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsxs)("div",{className:"grid gap-10 grid-cols-1 sm:grid-cols-2",children:[s.jsx(I.Wi,{control:er.control,name:"resume",render:()=>(0,s.jsxs)(I.xJ,{className:"flex flex-col items-start",children:[s.jsx(I.lX,{className:"ml-2",children:"Upload Resume"}),s.jsx("div",{className:"w-full",children:s.jsx(y,{})})]})}),s.jsx(I.Wi,{control:er.control,name:"coverLetter",render:()=>(0,s.jsxs)(I.xJ,{className:"flex flex-col items-start",children:[s.jsx(I.lX,{className:"ml-2",children:"Upload Cover Letter"}),s.jsx("div",{className:"w-full",children:s.jsx(E,{})})]})})]}),s.jsx(P.Separator,{className:"sm:col-span-2 mt-0"})]}),s.jsx("div",{className:"col-span-2",children:s.jsx(j.z,{type:"submit",className:"sm:col-span-2 w-full",disabled:Z,children:Z?"Loading...":"Update Profile"})}),W&&(0,s.jsxs)(u.Vq,{open:W,onOpenChange:e=>X(e),children:[s.jsx(u.t9,{className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-40"}),s.jsx(u.cZ,{className:"fixed inset-0 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-black rounded-md shadow-xl p-6 w-[90%] max-w-md",children:["skill"===et&&(0,s.jsxs)(s.Fragment,{children:[s.jsx("h2",{className:"text-lg font-semibold text-white mb-4",children:"Add New Skill"}),(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),ea()},children:[(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{htmlFor:"skillLabel",className:"block text-sm font-medium text-white mb-1",children:"Skill Label"}),s.jsx("input",{type:"text",value:J.label,onChange:e=>H({...J,label:e.target.value}),placeholder:"Enter skill label",className:"w-full px-3 py-2 rounded-md text-white bg-black placeholder-gray-400 border border-white",required:!0})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[s.jsx(j.z,{type:"button",variant:"ghost",onClick:()=>X(!1),className:"mt-3",children:"Cancel"}),s.jsx(j.z,{type:"button",className:"mt-3",onClick:()=>{ea(),H({label:"",description:""})},children:"Add Skill"})]})]})]}),"domain"===et&&(0,s.jsxs)(s.Fragment,{children:[s.jsx("h2",{className:"text-lg font-semibold text-white mb-4",children:"Add New Domain"}),(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),el()},children:[(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{htmlFor:"domainLabel",className:"block text-sm font-medium text-white mb-1",children:"Domain Label"}),s.jsx("input",{type:"text",value:Q.label,onChange:e=>K({...Q,label:e.target.value}),placeholder:"Enter Domain label",className:"w-full px-3 py-2 rounded-md text-white bg-black placeholder-gray-400 border border-white",required:!0})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[s.jsx(j.z,{type:"button",variant:"ghost",onClick:()=>X(!1),className:"mt-3",children:"Cancel"}),s.jsx(j.z,{type:"button",className:"mt-3",onClick:()=>{el(),K({label:"",description:""})},children:"Add Domain"})]})]})]}),"projectDomain"===et&&(0,s.jsxs)(s.Fragment,{children:[s.jsx("h2",{className:"text-lg font-semibold text-white mb-4",children:"Add New Project Domain"}),(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),ei()},children:[(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{htmlFor:"projectDomainLabel",className:"block text-sm font-medium text-white mb-1",children:"Project Domain Label"}),s.jsx("input",{type:"text",value:Y.label,onChange:e=>ee({...Y,label:e.target.value}),placeholder:"Enter Project Domain label",className:"w-full px-3 py-2 rounded-md text-white bg-black placeholder-gray-400 border border-white",required:!0})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[s.jsx(j.z,{type:"button",variant:"ghost",onClick:()=>X(!1),className:"mt-3",children:"Cancel"}),s.jsx(j.z,{type:"button",className:"mt-3",onClick:()=>{ei(),ee({label:"",description:""})},children:"Add Project Domain"})]})]})]})]})})]})]})]})})}var F=r(45175),z=r(40588);function T(){let e=(0,a.v9)(e=>e.user);return(0,s.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[s.jsx(l.Z,{menuItemsTop:F.y,menuItemsBottom:F.$,active:"Personal Info",isKycCheck:!0}),(0,s.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[s.jsx(z.Z,{menuItemsTop:F.y,menuItemsBottom:F.$,activeMenu:"Personal Info",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Settings",link:"#"},{label:"Personal Info",link:"#"}]}),s.jsx("main",{className:"grid flex-1 items-start  sm:px-6 sm:py-0 md:gap-8",children:s.jsx(L,{user_id:e.uid})})]})]})}},82287:(e,t,r)=>{"use strict";r.d(t,{Z:()=>f});var s=r(10326),a=r(17577);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,r(80851).Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var i=r(83855),n=r(77506),o=r(46226),c=r(25842),d=r(56627),m=r(91664),u=r(6260),p=r(4594),x=r(58285);let h=["image/png","image/jpeg","image/jpg","image/gif","image/svg+xml"],f=({profile:e,entityType:t})=>{let r=(0,c.v9)(e=>e.user),f=(0,c.I0)(),[g,j]=(0,a.useState)(null),[v,b]=(0,a.useState)(e),[N,y]=(0,a.useState)(!1),w=(0,a.useRef)(null),E=async e=>{if(e.preventDefault(),!g){(0,d.Am)({variant:"destructive",title:"No Image Selected",description:"Please select an image before submitting."});return}y(!0);let s=new FormData;s.append("profilePicture",g);try{let{Location:e}=(await u.b.post("/register/upload-image",s,{headers:{"Content-Type":"multipart/form-data"}})).data.data;f((0,p.av)({...r,photoURL:e}));let a=t===x.Dy.FREELANCER?"/freelancer":"/business",l=await u.b.put(a,{profilePic:e});if(200===l.status)(0,d.Am)({title:"Success",description:"Profile picture uploaded successfully!"});else throw Error("Failed to update profile picture")}catch(e){console.error("Error during upload:",e),(0,d.Am)({variant:"destructive",title:"Upload failed",description:"Image upload failed. Please try again."})}finally{y(!1)}};return s.jsx("div",{className:"upload-form max-w-md mx-auto rounded shadow-md",children:(0,s.jsxs)("form",{onSubmit:E,className:"space-y-6",children:[s.jsx("input",{type:"file",accept:h.join(","),onChange:e=>{let t=e.target.files?.[0];t&&h.includes(t.type)?t.size<=1048576?(j(t),b(URL.createObjectURL(t))):(0,d.Am)({variant:"destructive",title:"File too large",description:"Image size should not exceed 1MB."}):(0,d.Am)({variant:"destructive",title:"Invalid file type",description:`Please upload a valid image file. Allowed formats: ${h.join(", ")}`})},className:"hidden",ref:w}),s.jsx("div",{className:"relative flex flex-col items-center",children:(0,s.jsxs)("label",{htmlFor:"file-input",className:"cursor-pointer relative",children:[v?s.jsx(o.default,{width:28,height:28,src:v,alt:"Avatar Preview",className:"w-28 h-28 rounded-full object-cover border-2 border-black-300"}):s.jsx("div",{className:"w-28 h-28 rounded-full bg-gray-700 flex items-center justify-center",children:s.jsx(o.default,{width:112,height:112,src:e,alt:"Avatar Preview",className:"w-28 h-28 rounded-full object-cover border-2 border-black-300"})}),s.jsx(m.z,{variant:"outline",type:"button",size:"icon",className:"absolute bottom-0 right-0 w-10 h-10 rounded-full bg-black border border-gray-300 flex items-center justify-center shadow-md",onClick:()=>{v?b(null):w.current?.click()},children:v?s.jsx(l,{className:"h-4 w-4 text-gray-400"}):s.jsx(i.Z,{className:"h-4 w-4 text-gray-400"})})]})}),v&&s.jsx(m.z,{type:"submit",className:"w-full",disabled:!g||N,children:N?(0,s.jsxs)(s.Fragment,{children:[s.jsx(n.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Please wait"]}):"Upload Profile Picture"})]})})}},9969:(e,t,r)=>{"use strict";r.d(t,{NI:()=>f,Wi:()=>m,l0:()=>c,lX:()=>h,pf:()=>g,xJ:()=>x,zG:()=>j});var s=r(10326),a=r(17577),l=r(99469),i=r(74723),n=r(51223),o=r(44794);let c=i.RV,d=a.createContext({}),m=({...e})=>s.jsx(d.Provider,{value:{name:e.name},children:s.jsx(i.Qr,{...e})}),u=()=>{let e=a.useContext(d),t=a.useContext(p),{getFieldState:r,formState:s}=(0,i.Gc)(),l=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...l}},p=a.createContext({}),x=a.forwardRef(({className:e,...t},r)=>{let l=a.useId();return s.jsx(p.Provider,{value:{id:l},children:s.jsx("div",{ref:r,className:(0,n.cn)("space-y-2",e),...t})})});x.displayName="FormItem";let h=a.forwardRef(({className:e,...t},r)=>{let{error:a,formItemId:l}=u();return s.jsx(o.Label,{ref:r,className:(0,n.cn)(a&&"text-destructive",e),htmlFor:l,...t})});h.displayName="FormLabel";let f=a.forwardRef(({...e},t)=>{let{error:r,formItemId:a,formDescriptionId:i,formMessageId:n}=u();return s.jsx(l.g7,{ref:t,id:a,"aria-describedby":r?`${i} ${n}`:`${i}`,"aria-invalid":!!r,...e})});f.displayName="FormControl";let g=a.forwardRef(({className:e,...t},r)=>{let{formDescriptionId:a}=u();return s.jsx("p",{ref:r,id:a,className:(0,n.cn)("text-sm text-muted-foreground",e),...t})});g.displayName="FormDescription";let j=a.forwardRef(({className:e,children:t,...r},a)=>{let{error:l,formMessageId:i}=u(),o=l?String(l?.message):t;return o?s.jsx("p",{ref:a,id:i,className:(0,n.cn)("text-sm font-medium text-destructive",e),...r,children:o}):null});j.displayName="FormMessage"},44794:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Label:()=>c});var s=r(10326),a=r(17577),l=r(34478),i=r(28671),n=r(51223);let o=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...t},r)=>s.jsx(l.f,{ref:r,className:(0,n.cn)(o(),e),...t}));c.displayName=l.f.displayName},78062:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Separator:()=>n});var s=r(10326),a=r(17577),l=r(90220),i=r(51223);let n=a.forwardRef(({className:e,orientation:t="horizontal",decorative:r=!0,...a},n)=>s.jsx(l.f,{ref:n,decorative:r,orientation:t,className:(0,i.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...a}));n.displayName=l.f.displayName},45175:(e,t,r)=>{"use strict";r.d(t,{$:()=>u,y:()=>m});var s=r(10326),a=r(95920),l=r(79635),i=r(47546),n=r(48705),o=r(6343);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,r(80851).Z)("ImagePlus",[["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7",key:"31hg93"}],["line",{x1:"16",x2:"22",y1:"5",y2:"5",key:"ez7e4s"}],["line",{x1:"19",x2:"19",y1:"2",y2:"8",key:"1gkr8c"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var d=r(46226);let m=[{href:"#",icon:s.jsx(d.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:s.jsx(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/settings/personal-info",icon:s.jsx(l.Z,{className:"h-5 w-5"}),label:"Personal Info"},{href:"/freelancer/settings/professional-info",icon:s.jsx(i.Z,{className:"h-5 w-5"}),label:"Professional Info"},{href:"/freelancer/settings/projects",icon:s.jsx(n.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"/freelancer/settings/education-info",icon:s.jsx(o.Z,{className:"h-5 w-5"}),label:"Education"},{href:"/freelancer/settings/resume",icon:s.jsx(c,{className:"h-5 w-5"}),label:"Portfolio"}],u=[]},58285:(e,t,r)=>{"use strict";var s,a,l,i,n,o,c,d,m,u,p;r.d(t,{Dy:()=>u,Dz:()=>x});let x={BATCH:3};(function(e){e.PROJECT_HIRING="PROJECT_HIRING",e.SKILL_INTERVIEW="SKILL_INTERVIEW",e.DOMAIN_INTERVIEW="DOMAIN_INTERVIEW",e.TALENT_INTERVIEW="TALENT_INTERVIEW"})(s||(s={})),function(e){e.ADDED="Added",e.APPROVED="Approved",e.CLOSED="Closed",e.COMPLETED="Completed"}(a||(a={})),function(e){e.ACTIVE="Active",e.IN_ACTIVE="Inactive",e.NOT_VERIFIED="Not Verified"}(l||(l={})),function(e){e.BUSINESS="Business",e.FREELANCER="Freelancer",e.BOTH="Both"}(i||(i={})),function(e){e.ACTIVE="Active",e.IN_ACTIVE="Inactive"}(n||(n={})),function(e){e.APPLIED="APPLIED",e.NOT_APPLIED="NOT_APPLIED",e.APPROVED="APPROVED",e.FAILED="FAILED",e.STOPPED="STOPPED",e.REAPPLIED="REAPPLIED"}(o||(o={})),function(e){e.PENDING="Pending",e.ACCEPTED="Accepted",e.REJECTED="Rejected",e.PANEL="Panel",e.INTERVIEW="Interview"}(c||(c={})),function(e){e.ACTIVE="ACTIVE",e.INACTIVE="INACTIVE",e.ARCHIVED="ARCHIVED"}(d||(d={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.INACTIVE="Inactive",e.CLOSED="Closed"}(m||(m={})),function(e){e.FREELANCER="FREELANCER",e.ADMIN="ADMIN",e.BUSINESS="BUSINESS"}(u||(u={})),function(e){e.CREATED="Created",e.CLOSED="Closed",e.ACTIVE="Active"}(p||(p={}))},39958:(e,t,r)=>{"use strict";var s,a,l;r.d(t,{cd:()=>s,d8:()=>i,kJ:()=>a,sB:()=>l}),function(e){e.Mastery="Mastery",e.Proficient="Proficient",e.Beginner="Beginner"}(s||(s={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.REJECTED="Rejected",e.COMPLETED="Completed"}(a||(a={})),function(e){e.ACTIVE="ACTIVE",e.PENDING="PENDING",e.REJECTED="REJECTED",e.COMPLETED="COMPLETED"}(l||(l={}));let i={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},3517:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var s=r(68570);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\settings\personal-info\page.tsx`),{__esModule:l,$$typeof:i}=a;a.default;let n=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\settings\personal-info\page.tsx#default`)},34478:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});var s=r(17577),a=r(77335),l=r(10326),i=s.forwardRef((e,t)=>(0,l.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var n=i},90220:(e,t,r)=>{"use strict";r.d(t,{f:()=>c});var s=r(17577),a=r(77335),l=r(10326),i="horizontal",n=["horizontal","vertical"],o=s.forwardRef((e,t)=>{let{decorative:r,orientation:s=i,...o}=e,c=n.includes(s)?s:i;return(0,l.jsx)(a.WV.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:t})});o.displayName="Separator";var c=o}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,6686,4736,6499,8066,588],()=>r(18878));module.exports=s})();