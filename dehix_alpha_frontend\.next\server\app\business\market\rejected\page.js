"use strict";(()=>{var e={};e.id=202,e.ids=[202],e.modules={47849:e=>{e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71396:e=>{e.exports=require("undici")},83122:e=>{e.exports=require("undici")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},85158:e=>{e.exports=require("http2")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},77282:e=>{e.exports=require("process")},12781:e=>{e.exports=require("stream")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},66762:(e,s,r)=>{r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c}),r(7871),r(54302),r(12523);var t=r(23191),a=r(88716),i=r(37922),n=r.n(i),l=r(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let c=["",{children:["business",{children:["market",{children:["rejected",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7871)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\market\\rejected\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\market\\rejected\\page.tsx"],p="/business/market/rejected/page",x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/business/market/rejected/page",pathname:"/business/market/rejected",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7871:(e,s,r)=>{r.r(s),r.d(s,{default:()=>j});var t=r(19510);r(71159);var a=r(72301);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,a.Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var n=r(69013),l=r(90071);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,a.Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var c=r(80086),d=r(24767),p=r(93804),x=r(27039),m=r(55116),h=r(59558);let u=[{id:1,name:"Ryan Thompson",avatar:"/avatars/ryan.jpg",role:"DevOps Engineer",experience:"4 years",skills:["Docker","Kubernetes","AWS","CI/CD"],location:"Chicago, IL",reason:"Position filled",rejectedDate:"Feb 15, 2025"},{id:2,name:"Sophia Lee",avatar:"/avatars/sophia.jpg",role:"Mobile Developer",experience:"3 years",skills:["React Native","Swift","Kotlin","Firebase"],location:"Miami, FL",reason:"Skill mismatch",rejectedDate:"Feb 18, 2025"},{id:3,name:"Lucas Martinez",avatar:"/avatars/lucas.jpg",role:"QA Engineer",experience:"5 years",skills:["Selenium","Jest","Cypress","Manual Testing"],location:"Denver, CO",reason:"Salary expectations",rejectedDate:"Feb 20, 2025"},{id:4,name:"Emily Davis",avatar:"/avatars/emily.jpg",role:"Project Manager",experience:"7 years",skills:["Agile","Scrum","JIRA","Stakeholder Management"],location:"Portland, OR",reason:"Timeline conflict",rejectedDate:"Feb 17, 2025"}],j=()=>(0,t.jsxs)(d.ZP,{activeTab:"rejected",children:[(0,t.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[t.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Rejected Talents"}),(0,t.jsxs)("span",{className:"text-muted-foreground",children:["Showing ",u.length," results"]})]}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:u.map(e=>(0,t.jsxs)(p.Zb,{className:"overflow-hidden",children:[t.jsx(p.Ol,{className:"pb-2",children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,t.jsxs)(m.qE,{className:"h-12 w-12",children:[t.jsx(m.F$,{src:e.avatar,alt:e.name}),t.jsx(m.Q5,{children:e.name.slice(0,2).toUpperCase()})]}),(0,t.jsxs)("div",{children:[t.jsx(p.ll,{children:e.name}),t.jsx(p.SZ,{children:e.role})]})]}),(0,t.jsxs)(h.C,{variant:"destructive",className:"flex items-center gap-1",children:[t.jsx(i,{className:"h-3 w-3"}),"Rejected"]})]})}),t.jsx(p.aY,{className:"pb-2",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[t.jsx(n.Z,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsxs)("span",{children:[e.experience," experience"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[t.jsx(l.Z,{className:"h-4 w-4 text-muted-foreground"}),t.jsx("span",{children:e.location})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-destructive",children:[t.jsx(i,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Reason: ",e.reason]})]}),t.jsx("div",{className:"flex flex-wrap gap-2 pt-2",children:e.skills.map(e=>t.jsx(h.C,{variant:"secondary",children:e},e))})]})}),(0,t.jsxs)(p.eW,{className:"flex justify-between pt-2",children:[(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Rejected on ",e.rejectedDate]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(x.z,{size:"sm",variant:"outline",className:"flex items-center gap-1",children:[t.jsx(o,{className:"h-3 w-3"}),"Reconsider"]}),(0,t.jsxs)(x.z,{size:"sm",variant:"ghost",className:"flex items-center gap-1",children:[t.jsx(c.Z,{className:"h-3 w-3"}),"View Profile"]})]})]})]},e.id))})]})}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,4198,6034,4718,6226,495,8344,5165,4736,6499,1889],()=>r(66762));module.exports=t})();