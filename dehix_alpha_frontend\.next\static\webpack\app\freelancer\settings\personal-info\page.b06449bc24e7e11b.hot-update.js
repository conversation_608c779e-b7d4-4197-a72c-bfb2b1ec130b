"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/personal-info/page",{

/***/ "(app-pages-browser)/./src/components/form/CoverLetterTextarea.tsx":
/*!*****************************************************!*\
  !*** ./src/components/form/CoverLetterTextarea.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_textarea__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _ui_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst CoverLetterTextarea = (param)=>{\n    let { value = \"\", onChange, error } = param;\n    _s();\n    const [wordCount, setWordCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [charCount, setCharCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const words = value.trim().split(/\\s+/).filter((word)=>word.length > 0);\n        setWordCount(words.length);\n        setCharCount(value.length);\n    }, [\n        value\n    ]);\n    const handleChange = (e)=>{\n        const newValue = e.target.value;\n        onChange(newValue);\n    };\n    const isWordCountValid = wordCount >= 500;\n    const isCharCountValid = charCount >= 500;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_form__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_textarea__WEBPACK_IMPORTED_MODULE_2__.Textarea, {\n                    placeholder: \"Write your cover letter here... (optional - minimum 500 words if provided)\",\n                    value: value,\n                    onChange: handleChange,\n                    className: \"min-h-[200px] resize-y \".concat(error ? \"border-red-500 focus:border-red-500\" : \"\"),\n                    rows: 10\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            value && value.trim().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"\".concat(isWordCountValid ? \"text-green-600\" : \"text-orange-500\"),\n                                children: [\n                                    \"Words: \",\n                                    wordCount,\n                                    \"/500 \",\n                                    isWordCountValid ? \"✓\" : \"⚠\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"\".concat(isCharCountValid ? \"text-green-600\" : \"text-orange-500\"),\n                                children: [\n                                    \"Characters: \",\n                                    charCount,\n                                    \"/500 \",\n                                    isCharCountValid ? \"✓\" : \"⚠\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined),\n                    !isWordCountValid && wordCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-orange-500 text-xs\",\n                        children: [\n                            500 - wordCount,\n                            \" more words needed for complete cover letter\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined),\n            value && value.trim().length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Cover letter is optional. Leave empty to skip, or write at least 500 words for a complete cover letter.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_form__WEBPACK_IMPORTED_MODULE_3__.FormMessage, {\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                lineNumber: 80,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Tips for a great cover letter:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mt-1 space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Introduce yourself and your relevant experience\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Explain why you're interested in this type of work\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Highlight your key skills and achievements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Mention specific technologies or tools you're proficient with\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Describe your work style and communication approach\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\CoverLetterTextarea.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CoverLetterTextarea, \"pPd2u7CCW1Uh8UpOplFmywQOhgg=\");\n_c = CoverLetterTextarea;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CoverLetterTextarea);\nvar _c;\n$RefreshReg$(_c, \"CoverLetterTextarea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/form/CoverLetterTextarea.tsx\n"));

/***/ })

});