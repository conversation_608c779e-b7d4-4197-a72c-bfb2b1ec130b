(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6893],{2283:function(e,s,t){Promise.resolve().then(t.bind(t,55261))},55261:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return em}});var l,a,r,i,n=t(57437),c=t(43224),d=t(16463),o=t(2265),m=t(48185),x=t(15642),u=t(61617),h=t(87593),p=t(64797),f=t(82230),j=t(15922),N=t(92513),v=t(4086),g=t(29973),b=t(68515),y=t(8555),w=t(79055),C=function(e){let{domainName:s,description:t,email:l,status:a,startDate:r,endDate:i,domains:c=[],skills:d=[],isLastCard:o,onAddProfile:x}=e;return o?(0,n.jsx)(m.Zb,{className:"flex w-full items-center justify-center h-[430px] border border-dashed border-gray-400 rounded-lg cursor-pointer hover:border-gray-300 transition-colors",onClick:x,children:(0,n.jsx)(N.Z,{className:"w-12 h-12 text-gray-400"})}):(0,n.jsxs)("div",{className:"w-full min-h-[400px] bg-card relative border border-gray-700 rounded-lg shadow-md p-6 flex flex-col h-full",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,n.jsxs)(y.zs,{children:[(0,n.jsx)(y.Yi,{children:(0,n.jsx)("h2",{className:"text-lg cursor-pointer font-semibold",children:s&&s.length>10?"".concat(s.substring(0,10),"..."):s})}),(0,n.jsx)(y.bZ,{className:"py-2 w-auto",children:s})]}),(0,n.jsx)(w.C,{className:"bg-yellow-400 capitalize text-black text-xs px-2 py-1 rounded-md",children:(null==a?void 0:a.toLocaleLowerCase())||"Pending"})]}),(0,n.jsxs)("div",{className:"flex-grow",children:[(0,n.jsx)(g.Separator,{}),(0,n.jsx)("div",{className:"flex mt-6 mb-6 items-center text-sm",children:(0,n.jsx)(b.Z,{startDate:r,endDate:i})}),(0,n.jsx)("p",{className:"text-sm mb-4",children:t||"No description available."}),c.length>0&&(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("h3",{className:"text-sm font-semibold mb-2",children:"Project Domain:"}),(0,n.jsx)("div",{className:"flex flex-wrap gap-2",children:c.map((e,s)=>(0,n.jsx)(w.C,{className:"px-3 bg-gray-200 text-gray-900 py-1 text-xs rounded-full",children:e},s))})]}),d.length>0&&(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("h3",{className:"text-sm font-semibold mb-2",children:"Skills:"}),(0,n.jsx)("div",{className:"flex flex-wrap gap-2",children:d.map((e,s)=>(0,n.jsx)(w.C,{className:"px-3 bg-gray-200 text-gray-900 py-1 text-xs rounded-full",children:e},s))})]}),(0,n.jsxs)("div",{className:"flex mt-[28px] items-center text-sm",children:[(0,n.jsx)(v.Z,{className:"w-4 h-4 mr-2"}),(0,n.jsx)("span",{children:l||"No email provided."})]})]}),(0,n.jsx)("div",{className:"pt-3",children:(0,n.jsx)("button",{className:"w-auto bg-blue-600 text-white px-5 py-1 rounded-md hover:bg-blue-700",children:"Mark as completed"})})]})},E=t(86864),k=t(92940),P=t(71935),S=t(11240),D=t(68954),I=t(75733),A=t(29406),F=t(35265),T=t(89733),R=t(54662),Z=t(78068),z=t(2183),V=t(47304);(l=r||(r={})).TEXT="Text",l.CURRENCY="Currency",l.ACTION="Action",l.STATUS="Status",l.CUSTOM="Custom",(a=i||(i={})).INR="INR",a.USD="USD";var _=t(87138),L=t(4867),O=t(31590),B=t(49354);let M=(0,o.memo)(e=>{let{value:s}=e;return(0,n.jsx)("span",{children:s||"-"})});M.displayName="TextField";let U=(0,o.memo)(e=>{let{fieldData:s,value:t}=e,l=s.currency||i.USD,a=Number(t)||0,r=new Intl.NumberFormat("en-US",{style:"currency",currency:l,maximumFractionDigits:0}).format(a);return(0,n.jsx)("span",{className:"font-medium text-green-600",children:r})});U.displayName="CurrencyField";let q=(0,o.memo)(e=>{var s;let{value:t,fieldData:l}=e;if(!t)return(0,n.jsx)("span",{className:"text-gray-400",children:"-"});let a=null===(s=l.statusFormats)||void 0===s?void 0:s.find(e=>e.value.toLowerCase()===t.toLowerCase());return a?(0,n.jsx)("span",{style:{backgroundColor:a.bgColor,color:a.textColor},className:"px-2 py-1 rounded-sm text-xs font-medium text-center ".concat(a.isUppercase?"uppercase":""),children:a.textValue}):(0,n.jsx)("span",{className:"px-2 py-1 bg-gray-100 text-gray-800 rounded-sm text-xs font-medium",children:t})});q.displayName="StatusField";let G={[r.ACTION]:e=>{var s,t,l,a;let{id:r,fieldData:i,refetch:c,value:d}=e;if(!(null===(t=i.actions)||void 0===t?void 0:null===(s=t.options)||void 0===s?void 0:s.length)){let e=(null==d?void 0:d.bid_status)||(null==d?void 0:d.status);return"ACCEPTED"===e?(0,n.jsx)("span",{className:"text-sm font-medium text-green-600",children:"Bid has been accepted"}):"REJECTED"===e?(0,n.jsx)("span",{className:"text-sm font-medium text-red-600",children:"Bid has been rejected"}):(0,n.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"No actions available"})}return(0,n.jsxs)(O.h_,{children:[(0,n.jsx)(O.$F,{className:"text-sm text-white hover:bg-gray-700 p-1 rounded transition-colors",children:(null===(l=i.actions)||void 0===l?void 0:l.icon)||(0,n.jsx)(L.nWS,{})}),(0,n.jsx)(O.AW,{className:"bg-black border-gray-600",children:null===(a=i.actions)||void 0===a?void 0:a.options.map((e,s)=>{let{actionName:t,type:l,handler:a,href:i,className:d}=e;return(0,n.jsx)(O.Xi,{className:"px-0 py-0",children:"Button"===l?(0,n.jsx)("button",{onClick:()=>null==a?void 0:a({id:r,refetch:c}),className:(0,B.cn)("w-full py-2 px-3 text-left text-sm font-medium text-white hover:bg-gray-800",d),children:t}):(0,n.jsx)(_.default,{href:i||"#",className:(0,B.cn)("block w-full py-2 px-3 text-sm font-medium text-white hover:bg-gray-800",d),children:t})},s)})})]})},[r.CURRENCY]:U,[r.STATUS]:q,[r.CUSTOM]:e=>{let{fieldData:s,id:t,value:l,refetch:a}=e;return s.CustomComponent?(0,n.jsx)(s.CustomComponent,{id:t,data:l,refetch:a}):(0,n.jsx)("span",{className:"text-gray-400",children:"-"})},[r.TEXT]:M},J=e=>{let{value:s,fieldData:t,id:l,refetch:a}=e,r=G[t.type]||M;return(0,n.jsx)(r,{fieldData:t,value:s,id:l,refetch:a})},W=e=>{let{fields:s,data:t,uniqueId:l}=e,[a,i]=(0,o.useState)([]),[c,d]=(0,o.useState)(!0),x=()=>{};return(0,o.useEffect)(()=>{t&&(i(t),d(!1))},[t]),(0,n.jsx)("div",{className:"w-full",children:(0,n.jsx)("div",{className:"mb-8 mt-4",children:(0,n.jsx)(m.Zb,{children:(0,n.jsx)("div",{className:"lg:overflow-x-auto",children:(0,n.jsxs)(V.iA,{children:[(0,n.jsx)(V.xD,{children:(0,n.jsx)(V.SC,{children:s.map((e,s)=>(0,n.jsx)(V.ss,{className:"text-center",children:e.textValue},e.fieldName||s))})}),(0,n.jsx)(V.RM,{children:c?Array.from({length:3},(e,t)=>(0,n.jsx)(V.SC,{children:s.map((e,s)=>(0,n.jsx)(V.pj,{children:(0,n.jsx)(z.O,{className:"h-5 w-20"})},e.fieldName||s))},t)):(null==a?void 0:a.length)>0?a.map((e,t)=>{var a;return(0,n.jsx)(V.SC,{children:s.map((s,t)=>(0,n.jsx)(V.pj,{className:(0,B.cn)("text-gray-900 dark:text-gray-300 text-center",s.className),children:(0,n.jsx)(J,{fieldData:s,value:s.fieldName?e[s.fieldName]:s.type===r.CUSTOM?e:void 0,id:e[l],refetch:x})},s.fieldName||t))},String(null!==(a=e[l])&&void 0!==a?a:t))}):(0,n.jsx)(V.SC,{children:(0,n.jsx)(V.pj,{colSpan:s.length,className:"text-center py-10",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,n.jsx)(A.Z,{className:"text-gray-400",size:48}),(0,n.jsx)("p",{className:"text-gray-500 text-sm",children:"No data available"})]})})})})]})})})})})},X=["PENDING","ACCEPTED","REJECTED","PANEL","INTERVIEW"],$=[{value:"PENDING",textValue:"Pending",bgColor:"#D97706",textColor:"#FFFFFF"},{value:"ACCEPTED",textValue:"Accepted",bgColor:"#059669",textColor:"#FFFFFF"},{value:"REJECTED",textValue:"Rejected",bgColor:"#DC2626",textColor:"#FFFFFF"},{value:"PANEL",textValue:"Panel",bgColor:"#7C3AED",textColor:"#FFFFFF"},{value:"INTERVIEW",textValue:"Interview",bgColor:"#2563EB",textColor:"#FFFFFF"}],Q=o.memo(e=>{let{profilePic:s,userName:t}=e;return(0,n.jsxs)(n.Fragment,{children:[s?(0,n.jsx)("img",{src:s,alt:t,className:"w-10 h-10 rounded-full object-cover",onError:e=>{var s;e.currentTarget.style.display="none",null===(s=e.currentTarget.nextElementSibling)||void 0===s||s.classList.remove("hidden")}}):null,(0,n.jsx)("div",{className:"w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center ".concat(s?"hidden":""),children:(0,n.jsx)("span",{className:"text-white font-semibold text-sm",children:t.charAt(0).toUpperCase()})})]})}),H=o.memo(e=>{let{freelancer:s,bidData:t,isOpen:l,onClose:a}=e;if(!s)return null;let r=s.firstName&&s.lastName?"".concat(s.firstName," ").concat(s.lastName).trim():s.userName;return(0,n.jsx)(R.Vq,{open:l,onOpenChange:a,children:(0,n.jsxs)(R.cZ,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[(0,n.jsxs)(R.fK,{children:[(0,n.jsxs)(R.$N,{className:"flex items-center gap-3",children:[(0,n.jsx)(Q,{profilePic:s.profilePic,userName:s.userName}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"text-xl font-bold",children:r}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:["@",s.userName]})]})]}),(0,n.jsx)(R.Be,{children:"Freelancer Application Details"})]}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Basic Information"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Email"}),(0,n.jsx)("p",{className:"text-sm",children:s.email||"N/A"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Role"}),(0,n.jsx)("p",{className:"text-sm",children:s.role||"N/A"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Work Experience"}),(0,n.jsx)("p",{className:"text-sm",children:s.workExperience?"".concat(s.workExperience," years"):"N/A"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Hourly Rate"}),(0,n.jsx)("p",{className:"text-sm",children:s.perHourPrice?"$".concat(s.perHourPrice,"/hour"):"N/A"})]})]})]}),s.description&&(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"About"}),(0,n.jsx)("p",{className:"text-sm text-gray-700 leading-relaxed",children:s.description})]}),(null==t?void 0:t.description)&&(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Cover Letter"}),(0,n.jsx)("div",{className:"bg-gray-50 dark:bg-gray-800 p-4 rounded-lg",children:(0,n.jsx)("p",{className:"text-sm text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap",children:t.description})})]}),s.skills&&s.skills.length>0&&(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Skills"}),(0,n.jsx)("div",{className:"flex flex-wrap gap-2",children:s.skills.map((e,s)=>(0,n.jsxs)(w.C,{variant:"secondary",className:"text-xs",children:[e.name,e.level&&" (".concat(e.level,")")]},s))})]}),s.domain&&s.domain.length>0&&(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Domain Expertise"}),(0,n.jsx)("div",{className:"flex flex-wrap gap-2",children:s.domain.map((e,s)=>(0,n.jsxs)(w.C,{variant:"outline",className:"text-xs",children:[e.name,e.level&&" (".concat(e.level,")")]},s))})]})]})]})})});var Y=e=>{var s,t;let{id:l}=e,[a,i]=(0,o.useState)(null),[c,d]=(0,o.useState)(!0),[m,x]=(0,o.useState)(null),[u,h]=(0,o.useState)(),[p,f]=(0,o.useState)([]),[N,v]=(0,o.useState)({}),[g,b]=(0,o.useState)(!1),[y,w]=(0,o.useState)(null),[C,R]=(0,o.useState)(null),[z,V]=(0,o.useState)(!1),_=(0,o.useMemo)(()=>X.reduce((e,s)=>(e[s]=p.filter(e=>e.bid_status===s).length,e),{}),[p]);(0,o.useEffect)(()=>{(async()=>{if(l)try{let e=await j.b.get("/project/".concat(l));i(e.data),x(null)}catch(l){var e,s;let t=(null===(s=l.response)||void 0===s?void 0:null===(e=s.data)||void 0===e?void 0:e.message)||"Failed to fetch project data";x(t),(0,Z.Am)({variant:"destructive",title:"Error",description:t})}finally{d(!1)}})()},[l]);let L=(0,o.useCallback)((e,s,t)=>({_id:(null==e?void 0:e._id)||s,userName:(null==e?void 0:e.userName)||t||"",firstName:(null==e?void 0:e.firstName)||"",lastName:(null==e?void 0:e.lastName)||"",email:(null==e?void 0:e.email)||"",profilePic:null==e?void 0:e.profilePic,description:(null==e?void 0:e.description)||"Freelancer: ".concat(t||"Unknown"),skills:(null==e?void 0:e.skills)||[],domain:(null==e?void 0:e.domain)||[],workExperience:(null==e?void 0:e.workExperience)||0,perHourPrice:(null==e?void 0:e.perHourPrice)||0,role:(null==e?void 0:e.role)||""}),[]),O=(0,o.useCallback)(async e=>{var s,t,a;try{b(!0),x(null);let t=await j.b.get("/bid/project/".concat(l,"/profile/").concat(e,"/bid")),a=(null===(s=t.data)||void 0===s?void 0:s.data)||[];if(0===a.length){f([]);return}let r=Array.from(new Set(a.map(e=>e.bidder_id).filter(e=>e&&e.trim()))),i=new Map;if(r.length>0){let e=r.map(async e=>{try{var s;let t=await j.b.get("/public/freelancer/".concat(e));return{bidderId:e,data:(null===(s=t.data)||void 0===s?void 0:s.data)||t.data}}catch(s){return console.warn("Failed to fetch freelancer ".concat(e,":"),s.message),{bidderId:e,data:null}}});(await Promise.all(e)).forEach(e=>{let{bidderId:s,data:t}=e;i.set(s,t)})}let n=a.map(e=>({...e,freelancer:L(i.get(e.bidder_id),e.bidder_id,e.userName)}));f(n)}catch(s){let e=(null===(a=s.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"Failed to fetch bid details";x(e),(0,Z.Am)({variant:"destructive",title:"Error",description:e})}finally{b(!1)}},[l,L]);(0,o.useEffect)(()=>{u&&O(u)},[u,O]);let B=(0,o.useCallback)((e,s)=>{w(e),R(s),V(!0)},[]),M=(0,o.useCallback)(()=>{V(!1),w(null),R(null)},[]),U=(0,o.useCallback)(async(e,s)=>{try{v(s=>({...s,[e]:!0})),await j.b.put("/bid/".concat(e,"/status"),{bid_status:s}),f(t=>t.map(t=>t._id===e?{...t,bid_status:s}:t)),(0,Z.Am)({title:"Success",description:"Bid status updated to ".concat(s.toLowerCase(),".")})}catch(s){var t,l;let e=(null===(l=s.response)||void 0===l?void 0:null===(t=l.data)||void 0===t?void 0:t.message)||"Failed to update bid status";x(e),(0,Z.Am)({variant:"destructive",title:"Error",description:e})}finally{v(s=>({...s,[e]:!1}))}},[]),q=(0,o.useCallback)(e=>{let s={accept:{actionName:"Accept",actionIcon:(0,n.jsx)(k.Z,{className:"w-4 h-4 text-green-600"}),type:"Button",handler:e=>{let{id:s}=e;return U(s,"ACCEPTED")}},reject:{actionName:"Reject",actionIcon:(0,n.jsx)(P.Z,{className:"w-4 h-4 text-red-600"}),type:"Button",handler:e=>{let{id:s}=e;return U(s,"REJECTED")}},panel:{actionName:"Send to Panel",actionIcon:(0,n.jsx)(S.Z,{className:"w-4 h-4 text-yellow-600"}),type:"Button",handler:e=>{let{id:s}=e;return U(s,"PANEL")}},interview:{actionName:"Schedule Interview",actionIcon:(0,n.jsx)(D.Z,{className:"w-4 h-4 text-blue-600"}),type:"Button",handler:e=>{let{id:s}=e;return U(s,"INTERVIEW")}}};return({PENDING:[s.accept,s.reject,s.panel,s.interview],PANEL:[s.accept,s.reject,s.interview],INTERVIEW:[s.accept,s.reject],ACCEPTED:[],REJECTED:[]})[e]||[]},[U]),G=(0,o.useCallback)(e=>({uniqueId:"_id",data:p.filter(s=>s.bid_status===e),searchColumn:["userName","current_price","description"],searchPlaceholder:"Search by username, bid amount etc...",fields:[{textValue:"Freelancer",type:r.CUSTOM,CustomComponent:e=>{let{data:s}=e,t=null==s?void 0:s.freelancer,l=(null==s?void 0:s.userName)||(null==t?void 0:t.userName)||"Unknown",a=(null==t?void 0:t.firstName)&&(null==t?void 0:t.lastName)?"".concat(t.firstName," ").concat(t.lastName).trim():l;return(0,n.jsxs)("div",{className:"flex items-center gap-3 justify-center",children:[(0,n.jsx)(Q,{profilePic:null==t?void 0:t.profilePic,userName:l}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium text-white",children:a}),(0,n.jsxs)("p",{className:"text-sm text-gray-400",children:["@",l]})]})]})}},{textValue:"Bid Amount",type:r.CUSTOM,CustomComponent:e=>{let{data:s}=e;return(0,n.jsxs)("span",{className:"font-medium text-green-400",children:["$",(null==s?void 0:s.current_price)||"N/A"]})}},{fieldName:"bid_status",textValue:"Status",type:r.STATUS,statusFormats:$},{textValue:"Application",type:r.CUSTOM,CustomComponent:e=>{let{data:s}=e,t=null==s?void 0:s.freelancer;return(0,n.jsx)("div",{className:"flex justify-center",children:(0,n.jsxs)(T.z,{variant:"outline",size:"sm",onClick:()=>t&&B(t,s),className:"flex items-center gap-2",children:[(0,n.jsx)(I.Z,{className:"w-4 h-4"}),"View"]})})}},{textValue:"Actions",type:r.ACTION,actions:{options:q(e)}}]}),[p,q,B]);return c?(0,n.jsx)("div",{className:"max-w-5xl mx-auto p-4",children:(0,n.jsx)("div",{className:"text-center py-10",children:(0,n.jsx)("p",{children:"Loading..."})})}):m?(0,n.jsx)("div",{className:"max-w-5xl mx-auto p-4",children:(0,n.jsx)("div",{className:"text-center py-10",children:(0,n.jsx)("p",{className:"text-red-500",children:m})})}):(null==a?void 0:null===(t=a.data)||void 0===t?void 0:null===(s=t.profiles)||void 0===s?void 0:s.length)?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"max-w-5xl mx-auto p-4",children:(0,n.jsx)("div",{className:"mb-8 mt-4",children:(0,n.jsx)(F.UQ,{type:"single",collapsible:!0,children:a.data.profiles.map(e=>{var s,t,l,a,r;return(0,n.jsxs)(F.Qd,{value:e._id||"",onClick:()=>h(e._id),children:[(0,n.jsx)(F.o4,{children:(0,n.jsxs)("div",{className:"flex justify-between items-center w-full",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold",children:null!==(t=e.domain)&&void 0!==t?t:"N/A"}),(0,n.jsxs)("span",{className:"text-gray-500",children:["Rate: ",null!==(l=e.rate)&&void 0!==l?l:"N/A"]})]})}),(0,n.jsxs)(F.vF,{className:"p-0",children:[(0,n.jsxs)("div",{className:"px-6 py-4 flex flex-col gap-2",children:[(0,n.jsx)("div",{className:"flex gap-2 items-center",children:(0,n.jsxs)("p",{children:["Experience: ",null!==(a=e.experience)&&void 0!==a?a:"N/A"]})}),(0,n.jsx)("div",{className:"flex gap-2 items-center",children:(0,n.jsxs)("p",{children:["Min Connect: ",null!==(r=e.minConnect)&&void 0!==r?r:"N/A"]})}),(0,n.jsx)("div",{className:"flex gap-2 items-center",children:(0,n.jsxs)("p",{children:["Total Bids: ",(null===(s=e.totalBid)||void 0===s?void 0:s.length)||0]})})]}),(0,n.jsxs)(E.mQ,{defaultValue:"PENDING",className:"w-full",children:[(0,n.jsx)(E.dr,{className:"grid w-full grid-cols-5 mb-4",children:X.map(e=>(0,n.jsx)(E.SP,{value:e,children:"".concat(e.charAt(0)+e.slice(1).toLowerCase()," (").concat(_[e]||0,")")},e))}),X.map(e=>(0,n.jsx)(E.nU,{value:e,className:"mt-4",children:g?(0,n.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,n.jsx)("span",{className:"ml-2 text-gray-600",children:"Loading freelancer details..."})]}):(0,n.jsx)(W,{...G(e)})},e))]})]})]},e._id)})})})}),(0,n.jsx)(H,{freelancer:y,bidData:C,isOpen:z,onClose:M})]}):(0,n.jsx)("div",{className:"max-w-5xl mx-auto p-4",children:(0,n.jsxs)("div",{className:"text-center py-10 w-full mt-10",children:[(0,n.jsx)(A.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,n.jsx)("p",{className:"text-gray-500 text-lg",children:"No bid profiles found"})]})})},K=t(97540),ee=t(62688),es=t(31014),et=t(39343),el=t(59772),ea=t(74697),er=t(93363),ei=t(77209),en=t(4919),ec=t(2128);let ed=el.z.object({domain:el.z.string().min(1,"Domain is required"),freelancersRequired:el.z.string().min(1,"Number of freelancers is required"),skills:el.z.array(el.z.string()).min(1,"At least one skill is required"),experience:el.z.string().min(1,"Experience is required"),minConnect:el.z.string().min(1,"Minimum connects is required"),rate:el.z.string().min(1,"Rate is required"),description:el.z.string().min(1,"Description is required"),domain_id:el.z.string().min(1,"Domain ID is required")});var eo=e=>{let{projectId:s,onProfileAdded:t,trigger:l,open:a,onOpenChange:r}=e,[i,c]=(0,o.useState)(!1),d=void 0!==a?a:i,m=r||c,[x,u]=(0,o.useState)(!1),[h,p]=(0,o.useState)(!1),[f,v]=(0,o.useState)([]),[g,b]=(0,o.useState)([]),[y,C]=(0,o.useState)([]),[E,k]=(0,o.useState)(""),P=(0,et.cI)({resolver:(0,es.F)(ed),defaultValues:{domain:"",freelancersRequired:"",skills:[],experience:"",minConnect:"",rate:"",description:"",domain_id:""},mode:"onChange"});(0,o.useEffect)(()=>{let e=async()=>{p(!0);try{let[e,s]=await Promise.all([j.b.get("/domain"),j.b.get("/skills")]),t=e.data.data||[],l=s.data.data||[];v(t),b(l)}catch(l){var e,s;console.error("Error fetching data:",l);let t=(null===(s=l.response)||void 0===s?void 0:null===(e=s.data)||void 0===e?void 0:e.message)||l.message||"Failed to load domains and skills.";(0,Z.Am)({variant:"destructive",title:"Error",description:t})}finally{p(!1)}};d&&e()},[d]);let S=e=>{let s=y.filter(s=>s!==e);C(s),P.setValue("skills",s)},D=e=>{let s=f.find(s=>s.label===e);s&&(P.setValue("domain",s.label),P.setValue("domain_id",s._id))},I=async e=>{var l,a,r,i,n;u(!0);try{let i=await j.b.get("/project/".concat(s)),n=(null==i?void 0:null===(a=i.data)||void 0===a?void 0:null===(l=a.data)||void 0===l?void 0:l.data)||(null==i?void 0:null===(r=i.data)||void 0===r?void 0:r.data);if(!n)throw Error("Project not found");let c={_id:crypto.randomUUID(),domain:e.domain,freelancersRequired:e.freelancersRequired,skills:y,experience:parseInt(e.experience),minConnect:parseInt(e.minConnect),rate:parseInt(e.rate),description:e.description,domain_id:e.domain_id,selectedFreelancer:[],freelancers:[],totalBid:[]},d=[...n.profiles||[],c],o={projectName:n.projectName,description:n.description,email:n.email,companyName:n.companyName,skillsRequired:n.skillsRequired,role:n.role||"",projectType:n.projectType,profiles:d};await j.b.put("/project/".concat(s,"/update"),o),(0,Z.Am)({title:"Profile Added",description:"The profile has been successfully added to the project."}),P.reset(),C([]),k(""),m(!1),t()}catch(s){console.error("API Error:",s);let e=(null===(n=s.response)||void 0===n?void 0:null===(i=n.data)||void 0===i?void 0:i.message)||s.message||"Failed to add profile. Please try again.";(0,Z.Am)({variant:"destructive",title:"Error",description:e})}finally{u(!1)}};return(0,n.jsxs)(R.Vq,{open:d,onOpenChange:m,children:[l&&(0,n.jsx)(R.hg,{asChild:!0,children:l}),(0,n.jsxs)(R.cZ,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[(0,n.jsxs)(R.fK,{children:[(0,n.jsx)(R.$N,{children:"Add New Profile"}),(0,n.jsx)(R.Be,{children:"Add a new profile to this project with specific requirements."})]}),(0,n.jsx)(er.l0,{...P,children:(0,n.jsxs)("form",{onSubmit:P.handleSubmit(I),className:"space-y-4",children:[(0,n.jsx)(er.Wi,{control:P.control,name:"domain",render:e=>{let{field:s}=e;return(0,n.jsxs)(er.xJ,{children:[(0,n.jsx)(er.lX,{children:"Profile Domain"}),(0,n.jsx)(er.NI,{children:(0,n.jsxs)(ec.Ph,{onValueChange:D,value:s.value,disabled:h,children:[(0,n.jsx)(ec.i4,{children:(0,n.jsx)(ec.ki,{placeholder:h?"Loading domains...":"Select a domain"})}),(0,n.jsx)(ec.Bw,{children:f.map(e=>(0,n.jsx)(ec.Ql,{value:e.label,children:e.label},e._id))})]})}),(0,n.jsx)(er.zG,{})]})}}),(0,n.jsx)(er.Wi,{control:P.control,name:"freelancersRequired",render:e=>{let{field:s}=e;return(0,n.jsxs)(er.xJ,{children:[(0,n.jsx)(er.lX,{children:"Number of Freelancers Required"}),(0,n.jsx)(er.NI,{children:(0,n.jsx)(ei.I,{type:"number",placeholder:"Enter number of freelancers",...s})}),(0,n.jsx)(er.zG,{})]})}}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(er.lX,{children:"Skills Required"}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)(ec.Ph,{onValueChange:k,value:E,disabled:h,children:[(0,n.jsx)(ec.i4,{className:"flex-1",children:(0,n.jsx)(ec.ki,{placeholder:h?"Loading skills...":"Select a skill"})}),(0,n.jsx)(ec.Bw,{children:g.map(e=>(0,n.jsx)(ec.Ql,{value:e.talentName||e.label,children:e.talentName||e.label},e._id))})]}),(0,n.jsx)(T.z,{type:"button",onClick:()=>{if(E.trim()&&!y.includes(E.trim())){let e=[...y,E.trim()];C(e),P.setValue("skills",e),k("")}},disabled:!E,children:(0,n.jsx)(N.Z,{className:"w-4 h-4"})})]}),(0,n.jsx)("div",{className:"flex flex-wrap gap-2",children:y.map(e=>(0,n.jsxs)(w.C,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,n.jsx)(ea.Z,{className:"w-3 h-3 cursor-pointer",onClick:()=>S(e)})]},e))}),P.formState.isSubmitted&&0===y.length&&(0,n.jsx)("p",{className:"text-sm text-red-500",children:"At least one skill is required"})]}),(0,n.jsx)(er.Wi,{control:P.control,name:"experience",render:e=>{let{field:s}=e;return(0,n.jsxs)(er.xJ,{children:[(0,n.jsx)(er.lX,{children:"Experience Required (years)"}),(0,n.jsx)(er.NI,{children:(0,n.jsx)(ei.I,{type:"number",placeholder:"Enter years of experience",...s})}),(0,n.jsx)(er.zG,{})]})}}),(0,n.jsx)(er.Wi,{control:P.control,name:"minConnect",render:e=>{let{field:s}=e;return(0,n.jsxs)(er.xJ,{children:[(0,n.jsx)(er.lX,{children:"Minimum Connects Required"}),(0,n.jsx)(er.NI,{children:(0,n.jsx)(ei.I,{type:"number",placeholder:"Enter minimum connects",...s})}),(0,n.jsx)(er.zG,{})]})}}),(0,n.jsx)(er.Wi,{control:P.control,name:"rate",render:e=>{let{field:s}=e;return(0,n.jsxs)(er.xJ,{children:[(0,n.jsx)(er.lX,{children:"Rate (per hour/project)"}),(0,n.jsx)(er.NI,{children:(0,n.jsx)(ei.I,{type:"number",placeholder:"Enter rate",...s})}),(0,n.jsx)(er.zG,{})]})}}),(0,n.jsx)(er.Wi,{control:P.control,name:"description",render:e=>{let{field:s}=e;return(0,n.jsxs)(er.xJ,{children:[(0,n.jsx)(er.lX,{children:"Profile Description"}),(0,n.jsx)(er.NI,{children:(0,n.jsx)(en.g,{placeholder:"Describe the requirements for this profile...",...s})}),(0,n.jsx)(er.zG,{})]})}}),(0,n.jsxs)("div",{className:"flex justify-end gap-2 pt-4",children:[(0,n.jsx)(T.z,{type:"button",variant:"outline",onClick:()=>m(!1),children:"Cancel"}),(0,n.jsx)(T.z,{type:"submit",disabled:x||0===y.length,children:x?"Adding...":"Add Profile"})]})]})})]})]})};function em(){var e;let{project_id:s}=(0,d.useParams)(),[t,l]=(0,o.useState)(null),[a,r]=(0,o.useState)(!1);(0,o.useEffect)(()=>{(async()=>{try{var e,t,a;let r=await j.b.get("/project/".concat(s)),i=(null==r?void 0:null===(t=r.data)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.data)||(null==r?void 0:null===(a=r.data)||void 0===a?void 0:a.data);i&&l(i)}catch(e){(0,Z.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.error("API Error:",e)}})()},[s]);let i=async()=>{try{var e,t,a;let r=await j.b.get("/project/".concat(s)),i=(null==r?void 0:null===(t=r.data)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.data)||(null==r?void 0:null===(a=r.data)||void 0===a?void 0:a.data);i&&l(i)}catch(e){console.error("Error refetching project:",e),(0,Z.Am)({variant:"destructive",title:"Error",description:"Failed to refresh project data."})}};return t?(0,n.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,n.jsx)(p.Z,{menuItemsTop:f.yn,menuItemsBottom:f.$C,active:""}),(0,n.jsxs)("div",{className:"flex flex-col sm:gap-4 sm:py-4 md:py-0 sm:pl-14 mb-8",children:[(0,n.jsx)(ee.Z,{menuItemsTop:f.yn,menuItemsBottom:f.$C,activeMenu:"",breadcrumbItems:[{label:"Dashboard",link:"/dashboard/business"},{label:"Project",link:"/dashboard/business"},{label:t.projectName,link:"#"}]}),(0,n.jsxs)("main",{className:"flex flex-col lg:grid lg:grid-cols-4 xl:grid-cols-4 flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8",children:[(0,n.jsx)("div",{className:"w-full lg:col-span-3 space-y-4 md:space-y-8",children:(0,n.jsxs)(E.mQ,{defaultValue:"Project-Info",children:[(0,n.jsxs)(E.dr,{className:"grid w-full grid-cols-2",children:[(0,n.jsx)(E.SP,{value:"Project-Info",children:"Project-Info"}),(0,n.jsx)(E.SP,{value:"Profiles",children:"Profile Bids"})]}),(0,n.jsx)(E.nU,{value:"Project-Info",children:(0,n.jsxs)("div",{className:"space-y-4 md:space-y-8",children:[(0,n.jsx)("div",{children:(0,n.jsx)(h.Z,{projectName:t.projectName,description:t.description,email:t.email,status:t.status,startDate:t.createdAt,endDate:t.end,projectDomain:t.projectDomain,skills:t.skillsRequired,projectId:t._id,handleCompleteProject:()=>{if(!s){(0,Z.Am)({title:"Error",description:"Project ID is missing.",variant:"destructive"});return}j.b.put("/project/".concat(s),{status:K.sB.COMPLETED}).then(e=>{200===e.status?(l(e=>e?{...e,status:K.sB.COMPLETED}:e),(0,Z.Am)({title:"Success",description:"Project marked as completed!"})):(console.error("Unexpected response:",e),(0,Z.Am)({title:"Failed",description:"Failed to mark project as completed.",variant:"destructive"}))}).catch(e=>{console.error("Error updating project status:",e),(0,Z.Am)({title:"Error",description:"An error occurred while updating the project status.",variant:"destructive"})})},userRole:"Business"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)(m.Ol,{className:"pl-0 ",children:(0,n.jsx)(m.ll,{className:"pb-4",children:"Profiles"})}),(0,n.jsxs)(x.lr,{className:"w-full relative pt-3",children:[(0,n.jsxs)(x.KI,{className:"flex mt-3 -ml-2",children:[null===(e=t.profiles)||void 0===e?void 0:e.map((e,s)=>(0,n.jsx)(x.d$,{className:"basis-full md:basis-1/2 lg:basis-1/2 xl:basis-1/3 pl-2",children:(0,n.jsx)(C,{domainName:e.domain,description:e.description,email:t.email,status:t.status,startDate:t.createdAt,endDate:t.end,domains:[],skills:e.skills})},s)),t.status!==K.sB.COMPLETED&&t.status!==K.sB.REJECTED&&(0,n.jsx)(x.d$,{className:"basis-full md:basis-1/2 lg:basis-1/2 xl:basis-1/3 pl-2",children:(0,n.jsx)(C,{isLastCard:!0,onAddProfile:()=>{r(!0)}})})]}),t.profiles&&t.profiles.length>0&&(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{className:"flex",children:[(0,n.jsx)(x.am,{className:"absolute  left-0 top-1 transform -translate-y-1/2 p-2 shadow-md transition-colors",children:"Previous"}),(0,n.jsx)(x.Pz,{className:"absolute right-0 top-1 transform -translate-y-1/2 p-2 shadow-md transition-colors",children:"Next"})]})})]})]})]})}),(0,n.jsx)(E.nU,{value:"Profiles",children:(0,n.jsx)(Y,{id:s||""})})]})}),(0,n.jsxs)("div",{className:"w-full lg:col-span-1 lg:w-auto mt-8 lg:mt-0 space-y-6 min-w-0",children:[(0,n.jsx)(m.ll,{className:"group flex items-center gap-2 text-xl",children:"Interviews"}),(0,n.jsxs)("div",{className:"text-center py-6",children:[(0,n.jsx)(c.Z,{className:"mx-auto mb-2 text-gray-500",size:"80"}),(0,n.jsx)("p",{className:"text-gray-500 text-sm",children:"No interviews scheduled"})]})]})]}),(0,n.jsx)(eo,{projectId:s||"",onProfileAdded:i,open:a,onOpenChange:r})]})]}):(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,n.jsx)(u.E,{className:"w-1/2",value:50})})}},68515:function(e,s,t){"use strict";var l=t(57437);t(2265);var a=t(24241);s.Z=e=>{let{startDate:s,endDate:t}=e,r=s?new Date(s).toLocaleDateString():"Start Date N/A",i="current"!==t&&t?new Date(t).toLocaleDateString():"Still Going On!";return(0,l.jsxs)("div",{className:"flex relative whitespace-nowrap items-start sm:items-center gap-1 rounded-md ",children:[(0,l.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2 ",children:[(0,l.jsx)(a.Z,{className:"w-4 h-4 sm:w-5 sm:h-5 "}),(0,l.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:"Start  ".concat(r)})]}),(0,l.jsx)("p",{children:"-"}),(0,l.jsx)("div",{className:"flex items-center ",children:(0,l.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:" ".concat(i)})})]})}},87593:function(e,s,t){"use strict";var l=t(57437);t(2265);var a=t(16717),r=t(27419),i=t(4086),n=t(92145),c=t(92940),d=t(87138),o=t(48185),m=t(79055),x=t(44475),u=t(29973),h=t(68515),p=t(89733);s.Z=function(e){let{projectName:s,description:t,email:f,status:j,startDate:N,endDate:v,projectDomain:g,skills:b,userRole:y="Business",projectId:w,handleCompleteProject:C}=e,{text:E,className:k}=(0,x.S)(j),P="/".concat(y.toLowerCase(),"/project/").concat(w,"/milestone");return(0,l.jsxs)(o.Zb,{className:"shadow-lg border border-gray-800 rounded-lg",children:[(0,l.jsxs)(o.Ol,{children:[(0,l.jsxs)("div",{className:"flex flex-wrap justify-between items-center mb-0.5",children:[(0,l.jsx)(o.ll,{className:"text-xl md:text-2xl font-semibold",children:s}),(0,l.jsx)(m.C,{className:"".concat(k," px-1 py-0.5 text-xs md:text-sm rounded-md"),children:E})]}),(0,l.jsx)(u.Separator,{className:"my-4"})]}),(0,l.jsxs)(o.aY,{className:"space-y-6",children:[(0,l.jsx)(h.Z,{startDate:N,endDate:v}),(0,l.jsx)("p",{className:"text-sm md:text-base leading-relaxed",children:t}),(0,l.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 md:gap-6",children:[(0,l.jsxs)("div",{className:"flex flex-col gap-2 px-3 py-1 text-xs md:text-sm rounded-md shadow-inner w-full md:w-1/2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(a.Z,{className:"w-4 h-4 block md:hidden"}),(0,l.jsx)("p",{className:"font-medium",children:"Project Domain:"})]}),(0,l.jsx)("div",{className:"flex flex-wrap gap-1",children:g.map((e,s)=>(0,l.jsx)(m.C,{className:"bg-gray-200 text-gray-900 text-xs md:text-sm px-2 py-1 rounded-full",children:e},s))})]}),(0,l.jsxs)("div",{className:"flex flex-col gap-2 px-3 py-1 text-xs md:text-sm rounded-md shadow-inner w-full md:w-1/2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(r.Z,{className:"w-4 h-4 block md:hidden"}),(0,l.jsx)("p",{className:"font-medium",children:"Skills:"})]}),(0,l.jsx)("div",{className:"flex flex-wrap gap-1",children:b.map((e,s)=>(0,l.jsx)(m.C,{className:"bg-gray-200 text-gray-900 text-xs md:text-sm px-2 py-1 rounded-full",children:e},s))})]})]}),(0,l.jsxs)("div",{className:"flex flex-wrap items-center gap-4 px-3 py-1 text-xs md:text-sm rounded-md shadow-inner",children:[(0,l.jsx)(i.Z,{className:"w-4 h-4"}),(0,l.jsx)("span",{className:"text-sm",children:f})]}),(0,l.jsxs)("div",{className:"flex justify-between mt-4",children:[(0,l.jsx)(d.default,{href:P,children:(0,l.jsxs)(p.z,{className:"flex items-center px-4 py-2 text-xs md:text-sm font-medium text-white rounded-md bg-blue-600 hover:bg-blue-500",size:"sm",children:[(0,l.jsx)(n.Z,{className:"w-4 h-4 mr-1"}),"Milestone"]})}),(0,l.jsxs)(p.z,{className:"flex items-center px-4 py-2 text-xs md:text-sm font-medium text-white rounded-md ".concat("COMPLETED"===E?"bg-green-600 hover:bg-green-500":"bg-blue-600 hover:bg-blue-500"),size:"sm",onClick:C,disabled:!C,children:[(0,l.jsx)(c.Z,{className:"w-4 h-4 mr-1"}),"COMPLETED"===E?"Completed":"Mark complete"]})]})]})]})}},93363:function(e,s,t){"use strict";t.d(s,{NI:function(){return f},Wi:function(){return m},l0:function(){return d},lX:function(){return p},pf:function(){return j},xJ:function(){return h},zG:function(){return N}});var l=t(57437),a=t(2265),r=t(63355),i=t(39343),n=t(49354),c=t(70402);let d=i.RV,o=a.createContext({}),m=e=>{let{...s}=e;return(0,l.jsx)(o.Provider,{value:{name:s.name},children:(0,l.jsx)(i.Qr,{...s})})},x=()=>{let e=a.useContext(o),s=a.useContext(u),{getFieldState:t,formState:l}=(0,i.Gc)(),r=t(e.name,l);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=s;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...r}},u=a.createContext({}),h=a.forwardRef((e,s)=>{let{className:t,...r}=e,i=a.useId();return(0,l.jsx)(u.Provider,{value:{id:i},children:(0,l.jsx)("div",{ref:s,className:(0,n.cn)("space-y-2",t),...r})})});h.displayName="FormItem";let p=a.forwardRef((e,s)=>{let{className:t,...a}=e,{error:r,formItemId:i}=x();return(0,l.jsx)(c.Label,{ref:s,className:(0,n.cn)(r&&"text-destructive",t),htmlFor:i,...a})});p.displayName="FormLabel";let f=a.forwardRef((e,s)=>{let{...t}=e,{error:a,formItemId:i,formDescriptionId:n,formMessageId:c}=x();return(0,l.jsx)(r.g7,{ref:s,id:i,"aria-describedby":a?"".concat(n," ").concat(c):"".concat(n),"aria-invalid":!!a,...t})});f.displayName="FormControl";let j=a.forwardRef((e,s)=>{let{className:t,...a}=e,{formDescriptionId:r}=x();return(0,l.jsx)("p",{ref:s,id:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})});j.displayName="FormDescription";let N=a.forwardRef((e,s)=>{let{className:t,children:a,...r}=e,{error:i,formMessageId:c}=x(),d=i?String(null==i?void 0:i.message):a;return d?(0,l.jsx)("p",{ref:s,id:c,className:(0,n.cn)("text-sm font-medium text-destructive",t),...r,children:d}):null});N.displayName="FormMessage"},70402:function(e,s,t){"use strict";t.r(s),t.d(s,{Label:function(){return d}});var l=t(57437),a=t(2265),r=t(38364),i=t(12218),n=t(49354);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,l.jsx)(r.f,{ref:s,className:(0,n.cn)(c(),t),...a})});d.displayName=r.f.displayName},61617:function(e,s,t){"use strict";t.d(s,{E:function(){return n}});var l=t(57437),a=t(2265),r=t(52431),i=t(49354);let n=a.forwardRef((e,s)=>{let{className:t,value:a,...n}=e;return(0,l.jsx)(r.fC,{ref:s,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...n,children:(0,l.jsx)(r.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(a||0),"%)")}})})});n.displayName=r.fC.displayName},29973:function(e,s,t){"use strict";t.r(s),t.d(s,{Separator:function(){return n}});var l=t(57437),a=t(2265),r=t(48484),i=t(49354);let n=a.forwardRef((e,s)=>{let{className:t,orientation:a="horizontal",decorative:n=!0,...c}=e;return(0,l.jsx)(r.f,{ref:s,decorative:n,orientation:a,className:(0,i.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",t),...c})});n.displayName=r.f.displayName},2183:function(e,s,t){"use strict";t.d(s,{O:function(){return r}});var l=t(57437),a=t(49354);function r(e){let{className:s,...t}=e;return(0,l.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-primary/10",s),...t})}},86864:function(e,s,t){"use strict";t.d(s,{SP:function(){return d},dr:function(){return c},mQ:function(){return n},nU:function(){return o}});var l=t(57437),a=t(2265),r=t(62447),i=t(49354);let n=r.fC,c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,l.jsx)(r.aV,{ref:s,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...a})});c.displayName=r.aV.displayName;let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,l.jsx)(r.xz,{ref:s,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...a})});d.displayName=r.xz.displayName;let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,l.jsx)(r.VY,{ref:s,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...a})});o.displayName=r.VY.displayName},82230:function(e,s,t){"use strict";t.d(s,{$C:function(){return j},Ne:function(){return N},yn:function(){return f}});var l=t(57437),a=t(11005),r=t(98960),i=t(38133),n=t(20897),c=t(13231),d=t(71935),o=t(47390),m=t(73347),x=t(24258),u=t(5891),h=t(10883),p=t(66648);let f=[{href:"#",icon:(0,l.jsx)(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,l.jsx)(a.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:(0,l.jsx)(r.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:(0,l.jsx)(i.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:(0,l.jsx)(i.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:(0,l.jsx)(n.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:(0,l.jsx)(c.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:(0,l.jsx)(d.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:(0,l.jsx)(o.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,l.jsx)(m.Z,{className:"h-5 w-5"}),label:"Notes"}],j=[{href:"/business/settings/business-info",icon:(0,l.jsx)(x.Z,{className:"h-5 w-5"}),label:"Settings"}],N=[{href:"#",icon:(0,l.jsx)(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,l.jsx)(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:(0,l.jsx)(m.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:(0,l.jsx)(u.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:(0,l.jsx)(h.Z,{className:"h-5 w-5"}),label:"Trash"}]},97540:function(e,s,t){"use strict";var l,a,r,i,n,c;t.d(s,{cd:function(){return l},d8:function(){return d},kJ:function(){return a},sB:function(){return r}}),(i=l||(l={})).Mastery="Mastery",i.Proficient="Proficient",i.Beginner="Beginner",(n=a||(a={})).ACTIVE="Active",n.PENDING="Pending",n.REJECTED="Rejected",n.COMPLETED="Completed",(c=r||(r={})).ACTIVE="ACTIVE",c.PENDING="PENDING",c.REJECTED="REJECTED",c.COMPLETED="COMPLETED";let d={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}}},function(e){e.O(0,[4358,7481,9208,8310,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,1374,5984,2455,9726,2688,2971,7023,1744],function(){return e(e.s=2283)}),_N_E=e.O()}]);