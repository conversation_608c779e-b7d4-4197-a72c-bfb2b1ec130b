(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2914],{14148:function(e,s,a){Promise.resolve().then(a.bind(a,1760))},1760:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return Q}});var t=a(57437),l=a(2265),r=a(11444),c=a(64797),n=a(62688),i=a(70518),o=a(87592),d=a(81173),m=a.n(d),x=a(97501),h=a(89733),u=a(74697),j=a(96273),p=a(77209),b=a(70402);let g=e=>{let{projectData:s,setProjectData:a}=e,l=(e,t,l)=>{let r=[...s];r[e]={...r[e],[t]:l},a(r)},r=e=>{a(s.filter((s,a)=>a!==e))};return(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"space-y-1.5 ml-5 mb-5",children:[(0,t.jsx)("h2",{className:"text-2xl",children:"Project Info"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"This will not appear on your resume."})]}),(0,t.jsx)("form",{className:"space-y-5",children:s.map((e,s)=>(0,t.jsxs)("div",{className:"relative space-y-4 p-6 shadow-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("h3",{className:"text-sm font-semibold",children:["Project ",s+1]}),s>0&&(0,t.jsx)(h.z,{variant:"outline",onClick:()=>r(s),className:"p-1 rounded-full",children:(0,t.jsx)(u.Z,{className:"h-5 w-5 text-red-500"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"project-title-".concat(s),children:"Project Name"}),(0,t.jsx)(p.I,{id:"project-title-".concat(s),name:"title",value:e.title,onChange:e=>l(s,"title",e.target.value),placeholder:"My cool project",className:"block w-full border-gray-300 rounded-md shadow-sm sm:text-sm"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"project-description-".concat(s),children:"Describe what this project is for."}),(0,t.jsx)(p.I,{id:"project-description-".concat(s),name:"description",value:e.description,onChange:e=>l(s,"description",e.target.value),placeholder:"A project for learning purposes",className:"block w-full border-gray-300 rounded-md shadow-sm sm:text-sm"})]})]},s))}),(0,t.jsx)("div",{className:"flex justify-center mt-4",children:(0,t.jsx)(h.z,{onClick:()=>{a([...s,{title:"",description:""}])},className:"text-center justify-items-center dark:text-black  light:bg-black",children:(0,t.jsx)(j.Z,{})})})]})},f=e=>{let{personalData:s,setPersonalData:a}=e,l=(e,t,l)=>{let r=[...s];r[e]={...r[e],[t]:l},a(r)},r=e=>{a(s.filter((s,a)=>a!==e))};return(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"space-y-1.5 ml-5 mb-5",children:[(0,t.jsx)("h2",{className:"text-2xl",children:"Personal Info"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Tell us about yourself."})]}),(0,t.jsx)("form",{className:"space-y-5",children:s.map((e,s)=>(0,t.jsxs)("div",{className:"relative space-y-4 p-6 shadow-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("h3",{className:"text-sm font-semibold",children:["Personal Info ",s+1]}),s>0&&(0,t.jsx)(h.z,{variant:"outline",onClick:()=>r(s),className:"p-1 rounded-full",children:(0,t.jsx)(u.Z,{className:"h-5 w-5 text-red-500"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"firstName-".concat(s),children:"First Name"}),(0,t.jsx)(p.I,{id:"firstName-".concat(s),value:e.firstName,onChange:e=>l(s,"firstName",e.target.value),placeholder:"Enter your first name",className:"border border-gray-300 rounded-md p-2"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"lastName-".concat(s),children:"Last Name"}),(0,t.jsx)(p.I,{id:"lastName-".concat(s),value:e.lastName,onChange:e=>l(s,"lastName",e.target.value),placeholder:"Enter your last name",className:"border border-gray-300 rounded-md p-2"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"city-".concat(s),children:"City"}),(0,t.jsx)(p.I,{id:"city-".concat(s),value:e.city,onChange:e=>l(s,"city",e.target.value),placeholder:"Enter your city",className:"border border-gray-300 rounded-md p-2"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"country-".concat(s),children:"Country"}),(0,t.jsx)(p.I,{id:"country-".concat(s),value:e.country,onChange:e=>l(s,"country",e.target.value),placeholder:"Enter your country",className:"border border-gray-300 rounded-md p-2"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"phoneNumber-".concat(s),children:"Phone"}),(0,t.jsx)(p.I,{id:"phoneNumber-".concat(s),value:e.phoneNumber,onChange:e=>l(s,"phoneNumber",e.target.value),placeholder:"Enter your phone number",className:"border border-gray-300 rounded-md p-2"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"email-".concat(s),children:"Email"}),(0,t.jsx)(p.I,{id:"email-".concat(s),value:e.email,onChange:e=>l(s,"email",e.target.value),placeholder:"Enter your email address",className:"border border-gray-300 rounded-md p-2"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"github-".concat(s),children:"Github Id"}),(0,t.jsx)(p.I,{id:"github-".concat(s),value:e.github,onChange:e=>l(s,"github",e.target.value),placeholder:"Enter your github id",className:"border border-gray-300 rounded-md p-2"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"linkedin-".concat(s),children:"LinkedIn Id"}),(0,t.jsx)(p.I,{id:"linkedin-".concat(s),value:e.linkedin,onChange:e=>l(s,"linkedin",e.target.value),placeholder:"Enter your linkedin id",className:"border border-gray-300 rounded-md p-2"})]})]},s))}),(0,t.jsx)("div",{className:"flex justify-center mt-4",children:(0,t.jsx)(h.z,{onClick:()=>{a([...s,{firstName:"",lastName:"",city:"",country:"",phoneNumber:"",email:"",github:"",linkedin:""}])},className:"text-center  dark:text-black  light:bg-black",children:(0,t.jsx)(j.Z,{})})})]})};var N=a(49354);let v=e=>{let{educationData:s,setEducationData:a}=e,l=(e,t,l)=>{let r=[...s];r[e]={...r[e],[t]:l},a(r)},r=e=>{a(s.filter((s,a)=>a!==e))};return(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:(0,N.cn)("space-y-1.5 mb-5 ml-5"),children:[(0,t.jsx)("h2",{className:(0,N.cn)("text-2xl"),children:"Education Info"}),(0,t.jsx)("p",{className:(0,N.cn)("text-sm text-gray-500"),children:"Add details about your educational background."})]}),(0,t.jsx)("form",{className:(0,N.cn)("space-y-5"),children:s.map((e,s)=>(0,t.jsxs)("div",{className:(0,N.cn)("relative space-y-4 p-6 shadow-lg"),children:[(0,t.jsxs)("div",{className:(0,N.cn)("flex justify-between items-center"),children:[(0,t.jsxs)("h3",{className:(0,N.cn)("text-sm font-semibold"),children:["Education ",s+1]}),s>0&&(0,t.jsx)(h.z,{variant:"outline",onClick:()=>r(s),className:(0,N.cn)("p-1 rounded-full"),children:(0,t.jsx)(u.Z,{className:(0,N.cn)("h-5 w-5 text-red-500")})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"degree-".concat(s),children:"Degree"}),(0,t.jsx)(p.I,{id:"degree-".concat(s),type:"text",value:e.degree,onChange:e=>l(s,"degree",e.target.value),placeholder:"e.g., Bachelor of Science",className:(0,N.cn)("block w-full border-gray-300 rounded-md shadow-sm sm:text-sm")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"school-".concat(s),children:"School/University"}),(0,t.jsx)(p.I,{id:"school-".concat(s),type:"text",value:e.school,onChange:e=>l(s,"school",e.target.value),placeholder:"e.g., XYZ University",className:(0,N.cn)("block w-full border-gray-300 rounded-md shadow-sm sm:text-sm")})]}),(0,t.jsxs)("div",{className:(0,N.cn)("grid grid-cols-2 gap-3"),children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"startDate-".concat(s),children:"Start Date"}),(0,t.jsx)(p.I,{id:"startDate-".concat(s),type:"date",value:e.startDate,onChange:e=>l(s,"startDate",e.target.value),className:(0,N.cn)("block w-full border-gray-300 rounded-md shadow-sm sm:text-sm")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"endDate-".concat(s),children:"End Date"}),(0,t.jsx)(p.I,{id:"endDate-".concat(s),type:"date",value:e.endDate,onChange:e=>l(s,"endDate",e.target.value),className:(0,N.cn)("block w-full border-gray-300 rounded-md shadow-sm sm:text-sm")})]})]})]},s))}),(0,t.jsx)("div",{className:(0,N.cn)("flex justify-center mt-4"),children:(0,t.jsx)(h.z,{onClick:()=>{a([...s,{degree:"",school:"",startDate:"",endDate:""}])},className:(0,N.cn)("text-center justify-items-center dark:text-black  light:bg-black"),children:(0,t.jsx)(j.Z,{})})})]})};var y=a(4919);let k=e=>{let{skillData:s,setSkillData:a}=e,[r,c]=(0,l.useState)(""),n=(e,t)=>{let l=[...s];l[e]={skillName:t},a(l)},i=e=>{a(s.filter((s,a)=>a!==e))};return(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"space-y-1.5 ml-5 mb-5",children:[(0,t.jsx)("h2",{className:"text-2xl",children:"Skills"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"What are you good at?"})]}),(0,t.jsx)("form",{className:"space-y-5 mt-5",children:s.map((e,s)=>(0,t.jsxs)("div",{className:"relative space-y-4 p-6 shadow-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("h3",{className:"text-sm font-semibold",children:["Skill ",s+1]}),s>0&&(0,t.jsx)(h.z,{variant:"outline",onClick:()=>i(s),className:"p-1 rounded-full",children:(0,t.jsx)(u.Z,{className:"h-5 w-5 text-red-500"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"skillName-".concat(s),className:"block text-sm font-medium text-gray-500",children:"Skill Name"}),(0,t.jsx)(p.I,{id:"skillName-".concat(s),type:"text",value:e.skillName,onChange:e=>n(s,e.target.value),placeholder:"e.g., React.js, Node.js, graphic design",className:"block w-full border-gray-300 rounded-md shadow-sm sm:text-sm"})]})]},s))}),(0,t.jsx)("div",{className:"flex justify-center mt-4",children:(0,t.jsx)(h.z,{onClick:()=>{a([...s,{skillName:""}])},className:"text-center justify-items-centerdark:text-black  light:bg-black",children:(0,t.jsx)(j.Z,{})})}),r&&(0,t.jsxs)("div",{className:"mt-8 p-6 shadow-lg",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"AI Suggestion"}),(0,t.jsx)(y.g,{value:r,readOnly:!0,className:"w-full border-4 border-transparent rounded-xl shadow-sm sm:text-sm    bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 p-1    focus:outline-none",style:{backgroundClip:"padding-box",backgroundColor:"white"},placeholder:"AI-generated skills will appear here..."})]})]})},w=e=>{let{workExperienceData:s,setWorkExperienceData:a}=e,r=(e,t,l)=>{if(("startDate"===t||"endDate"===t)&&l&&!/^\d{4}-\d{2}-\d{2}$/.test(l))return;let r=[...s];r[e]={...r[e],[t]:l},a(r)};(0,l.useEffect)(()=>{console.log("Updated Work Experience Data in SummaryInfo:",s)},[s]);let c=e=>{a(s.filter((s,a)=>a!==e))};return(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"space-y-1.5 ml-5 mb-5",children:[(0,t.jsx)("h2",{className:"text-2xl font-normal",children:"Work Experience"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Add your work experience details."})]}),(0,t.jsx)("form",{className:"space-y-5",children:s.map((e,s)=>(0,t.jsxs)("div",{className:"relative space-y-4 p-6 shadow-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("h3",{className:"text-sm font-semibold",children:["Work Experience ",s+1]}),s>0&&(0,t.jsx)(h.z,{variant:"outline",onClick:()=>c(s),className:"p-1 rounded-full",children:(0,t.jsx)(u.Z,{className:"h-5 w-5 text-red-500"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"jobTitle-".concat(s),children:"Job Title"}),(0,t.jsx)(p.I,{id:"jobTitle-".concat(s),type:"text",value:e.jobTitle,onChange:e=>r(s,"jobTitle",e.target.value),placeholder:"e.g., Software Engineer",className:"border border-gray-300 rounded-md p-2"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"company-".concat(s),children:"Company"}),(0,t.jsx)(p.I,{id:"company-".concat(s),type:"text",value:e.company,onChange:e=>r(s,"company",e.target.value),placeholder:"e.g., Tech Solutions Inc.",className:"border border-gray-300 rounded-md p-2"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"startDate-".concat(s),children:"Start Date"}),(0,t.jsx)(p.I,{id:"startDate-".concat(s),type:"date",value:e.startDate,onChange:e=>r(s,"startDate",e.target.value),className:"border border-gray-300 rounded-md p-2"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"endDate-".concat(s),children:"End Date"}),(0,t.jsx)(p.I,{id:"endDate-".concat(s),type:"date",value:e.endDate,onChange:e=>r(s,"endDate",e.target.value),className:"border border-gray-300 rounded-md p-2"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"description-".concat(s),children:"Description"}),(0,t.jsx)("textarea",{id:"description-".concat(s),className:"block w-full p-2 border border-gray-300 rounded-md shadow-sm sm:text-sm",rows:4,value:e.description,onChange:e=>r(s,"description",e.target.value),placeholder:"Describe your role and responsibilities"})]})]},s))}),(0,t.jsx)("div",{className:"flex justify-center mt-4",children:(0,t.jsx)(h.z,{onClick:()=>{a([...s,{jobTitle:"",company:"",startDate:"",endDate:"",description:""}])},className:"text-center dark:text-black  light:bg-black",children:(0,t.jsx)(j.Z,{})})})]})},D=e=>{let{summaryData:s,setSummaryData:a,workExperienceData:r}=e,c=(e,t)=>{let l=[...s];l[e]=t,a(l)};(0,l.useEffect)(()=>{},[r]);let n=e=>{a(s.filter((s,a)=>a!==e))};return(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"space-y-1.5 ml-5 mb-5",children:[(0,t.jsx)("h2",{className:"text-2xl",children:"Professional Summary"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Write a short introduction for your resume."})]}),(0,t.jsx)("form",{className:"space-y-5",children:s.map((e,s)=>(0,t.jsxs)("div",{className:"relative space-y-4 p-6 shadow-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("h3",{className:"text-sm font-semibold",children:["Summary ",s+1]}),s>0&&(0,t.jsx)(h.z,{variant:"outline",onClick:()=>n(s),className:"p-1 rounded-full",children:(0,t.jsx)(u.Z,{className:"h-5 w-5 text-red-500"})})]}),(0,t.jsx)(y.g,{value:e,onChange:e=>c(s,e.target.value),placeholder:"A brief, engaging text about yourself",className:"w-full border-gray-300 rounded-md shadow-sm sm:text-sm"})]},s))}),(0,t.jsx)("div",{className:"flex justify-center mt-4",children:(0,t.jsx)(h.z,{onClick:()=>{a([...s,""])},className:"text-center dark:text-black  light:bg-black",children:(0,t.jsx)(j.Z,{})})})]})},S=e=>{let{achievementData:s,setAchievementData:a}=e,l=(e,t)=>{let l=[...s];l[e]={achievementName:t},a(l)},r=e=>{a(s.filter((s,a)=>a!==e))};return(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"space-y-1.5 ml-5 mb-5",children:[(0,t.jsx)("h2",{className:"text-2xl",children:"Achievements"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"What accomplishments are you proud of?"})]}),(0,t.jsx)("form",{className:"space-y-5 mt-5",children:s.map((e,s)=>(0,t.jsxs)("div",{className:"relative space-y-4 p-6 shadow-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("h3",{className:"text-sm font-semibold",children:["Achievement ",s+1]}),s>0&&(0,t.jsx)(h.z,{variant:"outline",onClick:()=>r(s),className:"p-1 rounded-full",children:(0,t.jsx)(u.Z,{className:"h-5 w-5 text-red-500"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.Label,{htmlFor:"achievementName-".concat(s),className:"block text-sm font-medium text-gray-500",children:"Achievement Description"}),(0,t.jsx)(p.I,{id:"achievementName-".concat(s),type:"text",value:e.achievementName,onChange:e=>l(s,e.target.value),placeholder:"e.g., Increased sales by 20%, Led a team of 5, etc.",className:"block w-full border-gray-300 rounded-md shadow-sm sm:text-sm"})]})]},s))}),(0,t.jsx)("div",{className:"flex justify-center mt-4",children:(0,t.jsx)(h.z,{onClick:()=>{a([...s,{achievementName:""}])},className:"text-center justify-items-center dark:text-black light:bg-black",children:(0,t.jsx)(j.Z,{})})})]})};var C=a(29973);let I=e=>{let{educationData:s=[],workExperienceData:a=[],personalData:r=[],projectData:c=[],skillData:n=[],achievementData:i=[],summaryData:o=[],headingColor:d="#1A73E8"}=e,m=(0,l.useRef)(null);return(0,t.jsx)("div",{className:"flex justify-center w-full h-full py-10 ",children:(0,t.jsxs)("div",{ref:m,className:"bg-white w-[900px] p-10 shadow-lg flex flex-col rounded-md border border-gray-300",style:{boxShadow:"0px 4px 10px rgba(0, 0, 0, 0.1)"},children:[(0,t.jsx)("div",{className:"w-full text-center",children:r.map((e,s)=>(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",style:{color:d},children:"".concat(e.firstName," ").concat(e.lastName)}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.email," • ",e.phoneNumber]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.github," • ",e.linkedin]})]},s))}),(0,t.jsx)(C.Separator,{className:"border-gray-300"}),o.length>0&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-blue-800",style:{color:d},children:"Summary"}),(0,t.jsx)(C.Separator,{className:"mb-2"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:o.join(" ")})]}),(0,t.jsx)(C.Separator,{className:"border-gray-300"}),a.length>0&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-blue-800",style:{color:d},children:"Work Experience"}),(0,t.jsx)(C.Separator,{className:"mb-2"}),a.map((e,s)=>(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsxs)("p",{className:"text-sm font-medium text-black",children:[e.jobTitle," - ",e.company]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[e.startDate," to ",e.endDate]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:e.description})]},s))]}),(0,t.jsx)(C.Separator,{className:"border-gray-300"}),s.length>0&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-blue-800",style:{color:d},children:"Education"}),(0,t.jsx)(C.Separator,{className:"mb-2"}),s.map((e,s)=>(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsxs)("p",{className:"text-sm font-medium text-black",children:[e.degree," - ",e.school]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[e.startDate," to ",e.endDate]})]},s))]}),(0,t.jsx)(C.Separator,{className:"border-gray-300"}),c.length>0&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-blue-800",style:{color:d},children:"Projects"}),(0,t.jsx)(C.Separator,{className:"mb-2"}),c.map((e,s)=>(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-black",children:e.title}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]},s))]}),(0,t.jsx)(C.Separator,{className:"border-gray-300"}),n.length>0&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-blue-800",style:{color:d},children:"Skills"}),(0,t.jsx)(C.Separator,{className:"mb-2"}),(0,t.jsx)("ul",{className:"list-disc list-inside text-sm text-gray-600",children:n.map((e,s)=>(0,t.jsx)("li",{children:e.skillName},s))})]}),(0,t.jsx)(C.Separator,{className:"border-gray-300"}),i.length>0&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-blue-800",style:{color:d},children:"Achievements"}),(0,t.jsx)(C.Separator,{className:"mb-2"}),(0,t.jsx)("ul",{className:"list-disc list-inside text-sm text-gray-600",children:i.map((e,s)=>(0,t.jsx)("li",{children:e.achievementName},s))})]})]})})},E=e=>{let{educationData:s=[{degree:"Bachelor of Science in Computer Science",school:"ABC University",startDate:"2015",endDate:"2019"},{degree:"Master of Science in Software Engineering",school:"XYZ University",startDate:"2020",endDate:"2022"}],workExperienceData:a=[{jobTitle:"english teacher",company:"TechCorp Solutions",startDate:"2019",endDate:"2021",description:"Developed scalable web applications and optimized system performance."},{jobTitle:"Sql developer",company:"Innovatech",startDate:"2021",endDate:"2023",description:"Led a team of developers to build cloud-based enterprise software."}],personalData:r=[],skillData:c=[],achievementData:n=[],projectData:i=[],summaryData:o=[],headingColor:d="#1A73E8"}=e,m=(0,l.useRef)(null);return(0,t.jsx)("div",{className:"flex justify-center w-full h-full py-10",children:(0,t.jsxs)("div",{ref:m,className:"bg-white w-[900px] p-6 shadow-lg flex",style:{boxShadow:"0px 4px 10px rgba(0, 0, 0, 0.1)"},children:[(0,t.jsxs)("div",{className:"w-1/3 bg-gray-200 dark:bg-gray-200 text-black p-6 flex flex-col",children:[r.map((e,s)=>(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",style:{color:d},children:"".concat(e.firstName," ").concat(e.lastName)}),o.length>0&&(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-blue-800 mt-4",style:{color:d},children:"Profile Summary"}),(0,t.jsx)(C.Separator,{className:"mb-2"}),(0,t.jsx)("p",{className:"text-sm leading-relaxed",children:o.join(" ")})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-blue-800 ",style:{color:d},children:"Contact Details"}),(0,t.jsx)(C.Separator,{className:"mb-2"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:e.email}),(0,t.jsx)("p",{className:"text-sm",children:e.phoneNumber}),(0,t.jsx)("p",{className:"text-sm mt-2",children:e.linkedin}),(0,t.jsx)("p",{className:"text-sm",children:e.github})]})]},s)),c.length>0&&(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-blue-800 ",style:{color:d},children:"Skills"}),(0,t.jsx)(C.Separator,{className:"mb-2"}),(0,t.jsx)("ul",{className:"list-disc list-inside text-sm",children:c.map((e,s)=>(0,t.jsx)("li",{children:e.skillName},s))})]})]}),(0,t.jsxs)("div",{className:"w-2/3 p-6",children:[a.length>0&&(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-blue-800 mb-2",style:{color:d},children:"Experience"}),(0,t.jsx)(C.Separator,{className:"mb-2"}),a.map((e,s)=>(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsxs)("p",{className:"text-sm font-medium text-black",children:[e.jobTitle," - ",e.company]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[e.startDate," to ",e.endDate]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-2 leading-relaxed",children:e.description})]},s))]}),i.length>0&&(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-blue-800  mb-2",style:{color:d},children:"Projects"}),(0,t.jsx)(C.Separator,{className:"mb-2"}),(0,t.jsxs)("div",{children:[i.map((e,s)=>(0,t.jsxs)("div",{className:"space-y-1 mb-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-black ",children:e.title}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:e.description})]},s)),s.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-blue-800 mb-2",style:{color:d},children:"Education"}),(0,t.jsx)(C.Separator,{className:"mb-2"}),s.map((e,s)=>(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsxs)("p",{className:"text-sm font-medium text-black",children:[e.degree," - ",e.school]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[e.startDate," to ",e.endDate]})]},s))]}),n.length>0&&(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-blue-800 ",style:{color:d},children:"Achievements"}),(0,t.jsx)(C.Separator,{className:"mb-2"}),(0,t.jsx)("ul",{className:"list-disc list-inside text-sm text-gray-600",children:n.map((e,s)=>(0,t.jsx)("li",{children:e.achievementName},s))})]})]})]})]})]})})};var T=a(37346),P=a(79512),L=a(55211),R=a(48185),Z=a(9266);let A=e=>(0,Z.Z)(e).terms().out("array").map(e=>e.toLowerCase()),F=e=>{let s=A(e),a=["javascript","react","developer","experience","project","resume"];return s.filter(e=>!a.includes(e))},z=(e,s)=>{let a=A(e);return Math.round(s.map(e=>e.toLowerCase()).filter(e=>a.includes(e)).length/s.length*100)},B=e=>A(e).length,M=e=>e.split(/[.!?]/).map(e=>e.trim()).filter(e=>e.length>0).filter(e=>e.split("").length>20),W=e=>["education","experience","skills","projects","certifications"].filter(s=>!e.toLowerCase().includes(s)),U=(e,s)=>{let a=M(e),t=B(e),l=W(e),r=z(e,s),c=F(e),n=Math.max(0,100-5*a.length),i=Math.max(0,100-Math.abs(t-150)),o=Math.max(0,(5-l.length)*20);return{totalScore:Math.round((n+i+r+o)/4),grammarScore:n,brevityScore:i,impactScore:r,sectionsScore:o,spellingMistakes:c}};L.kL.register(L.ZL,L.uw,L.f$,L.u,L.De);let Y=e=>{let{name:s,resumeText:a,jobKeywords:r=[]}=e,[c,n]=(0,l.useState)(null),{theme:i}=(0,P.F)(),[o,d]=(0,l.useState)({grammar:0,brevity:0,impact:0,sections:0}),m={labels:["Grammar","Brevity","Impact","Sections"],datasets:[{label:"Score",data:[o.grammar,o.brevity,o.impact,o.sections],backgroundColor:"dark"===i?["#ffffff","#ffffff","#ffffff","#ffffff"]:["#00000","#00000","#00000","#00000"],borderRadius:6}]};return(0,t.jsx)("div",{className:"flex flex-col items-center justify-center p-8",children:(0,t.jsxs)(R.Zb,{className:"w-full max-w-3xl p-8 shadow-lg rounded-lg text-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"".concat(s,"'s Resume")}),null!==c&&(0,t.jsxs)("div",{className:"flex flex-col items-center mb-6",children:[(0,t.jsx)("div",{className:"w-32 h-32 flex items-center justify-center rounded-full bg-gray-300",children:(0,t.jsx)("span",{className:"text-3xl text-black font-bold",children:c})}),(0,t.jsxs)("p",{className:"mt-4 text-lg",children:["Your resume scored ",(0,t.jsxs)("span",{className:"font-bold",children:[c," out of 100"]}),". You're doing well, but a few tweaks can boost your score!"]})]}),(0,t.jsx)(h.z,{onClick:()=>{let e=U(a,r);n(e.totalScore),d({grammar:e.grammarScore,brevity:e.brevityScore,impact:e.impactScore,sections:e.sectionsScore})},className:"mb-6",children:"Analyze Resume"}),null!==c&&(0,t.jsx)("div",{className:"w-full h-66 flex items-center justify-center",children:(0,t.jsx)(T.$Q,{data:m,options:{responsive:!0,scales:{y:{beginAtZero:!0,max:100}}}})})]})})};var $=a(27756);function O(){var e,s,a,r;let[d,u]=(0,l.useState)(0),[j,p]=(0,l.useState)("ResumePreview2"),[b,N]=(0,l.useState)(!1),[y,C]=(0,l.useState)([{degree:"Bachelor of Science in Computer Science",school:"ABC University",startDate:"2023-12-31",endDate:"2023-12-31"},{degree:"Master of Science in Software Engineering",school:"XYZ University",startDate:"2023-12-31",endDate:"2023-12-31"}]),[T,P]=(0,l.useState)([{jobTitle:"Software Developer",company:"TechCorp Solutions",startDate:"2023-12-31",endDate:"2023-12-31",description:"Engineered scalable web solutions, optimized system efficiency, and enhanced software reliability."},{jobTitle:"Senior Developer",company:"Innovatech",startDate:"2023-12-31",endDate:"2023-12-31",description:"Spearheaded the development of cloud-based enterprise platforms, driving innovation and operational excellence."}]),[L,R]=(0,l.useState)([{firstName:"John",lastName:"Doe",city:"New York",country:"USA",phoneNumber:"************",email:"<EMAIL>",github:"github.com/john",linkedin:"linkedin.com/in/john"}]),[Z,A]=(0,l.useState)([{title:"AI-Powered Resume Builder",description:"Developed a full-stack platform integrating OpenAI’s GPT-4 to dynamically generate personalized resume content. Implemented secure authentication and cloud data management using Firebase."},{title:"E-Commerce Platform with Real-Time Analytics",description:"Built a scalable e-commerce platform using Next.js and PostgreSQL. Implemented real-time analytics dashboards with Socket.IO and Chart.js to track user behavior and sales trends. Deployed the application on AWS."},{title:"IoT-Based Smart Home Automation System",description:"Designed and implemented an IoT solution for smart home automation using Raspberry Pi, Python, and MQTT protocol. Developed a mobile app with Flutter for remote control and monitoring of home devices."}]),[F,z]=(0,l.useState)([{skillName:"Database Engineering (SQL Server, T-SQL), Data Visualization (Kibana, Eclipse Birt), ETL Pipelines"},{skillName:"Strategic Communication & Cross-Functional Team Collaboration"},{skillName:"Advanced SQL Query Optimization & Performance Tuning"},{skillName:"Full-Stack Web Development"},{skillName:"Agile Software Development & DevOps Integration"}]),[B,M]=(0,l.useState)([{achievementName:"Published Research on AI-Powered Automation in a Peer-Reviewed Journal"},{achievementName:"Delivered Keynote Speech on Emerging Tech Trends at a Global Conference"}]),[W,U]=(0,l.useState)(["Results-driven software engineer with a passion for building scalable, high-performance applications while ensuring security, efficiency, and a seamless user experience."]),[O,Q]=(0,l.useState)("#000000"),_="\n  ".concat((null===(e=L[0])||void 0===e?void 0:e.firstName)||""," ").concat((null===(s=L[0])||void 0===s?void 0:s.lastName)||"","\n  ").concat(W.join(" "),"\n\n  Work Experience:\n  ").concat(T.map(e=>"".concat(e.jobTitle," at ").concat(e.company,". ").concat(e.description)).join("\n"),"\n\n  Education:\n  ").concat(y.map(e=>"".concat(e.degree," from ").concat(e.school)).join("\n"),"\n\n  Skills: ").concat(F.map(e=>e.skillName).join(", "),"\n  Achievements: ").concat(B.map(e=>e.achievementName).join(", "),"\n  Projects:\n  ").concat(Z.map(e=>"".concat(e.title,": ").concat(e.description)).join("\n"),"\n").trim(),G=(0,l.useRef)(null),H=[(0,t.jsx)(f,{personalData:L,setPersonalData:R},"personal"),(0,t.jsx)(w,{workExperienceData:T,setWorkExperienceData:P},"workexperience"),(0,t.jsx)(v,{educationData:y,setEducationData:C},"education"),(0,t.jsx)(g,{projectData:Z,setProjectData:A},"general"),(0,t.jsx)(k,{skillData:F,setSkillData:z,projectData:Z},"skill"),(0,t.jsx)(S,{achievementData:B,setAchievementData:M},"achievement"),(0,t.jsx)(D,{summaryData:W,setSummaryData:U,workExperienceData:T},"summary")],J=e=>{p(1===e?"ResumePreview1":"ResumePreview2")},K=async()=>{let e=G.current;if(e){let s=e.querySelector(".resumeContent");if(s){let e=(await m()(s,{scale:2})).toDataURL("image/png"),a=new x.kH("portrait","mm","a4"),t=a.getImageProperties(e),l=a.internal.pageSize.getWidth(),r=t.height*l/t.width;a.addImage(e,"PNG",0,0,l,r),a.save("Resume.pdf")}else console.error("No .resumeContent element found.")}else console.error("Resume element is not available.")};return(0,t.jsxs)("div",{className:"flex min-h-screen flex-col bg-muted/40",children:[(0,t.jsx)(c.Z,{menuItemsTop:$.y,menuItemsBottom:$.$,active:"Resume Editor"}),(0,t.jsxs)("div",{className:"flex flex-1 flex-col sm:gap-8 sm:py-0 sm:pl-14",children:[(0,t.jsx)(n.Z,{menuItemsTop:$.y,menuItemsBottom:$.$,activeMenu:"Resume Editor",breadcrumbItems:[{label:"Settings",link:"#"},{label:"Resume Building",link:"#"},{label:"Resume Editor",link:"#"}]}),(0,t.jsxs)("main",{className:"flex-1 p-4 sm:px-6 sm:py-0 md:gap-6 lg:grid lg:grid-cols-2",children:[(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Design your Resume"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-5",children:"Follow the steps below to create your resume."}),(0,t.jsx)("div",{className:"mt-5",children:(0,t.jsx)("div",{className:"relative",children:b?(0,t.jsx)(Y,{name:"".concat(null===(a=L[0])||void 0===a?void 0:a.firstName," ").concat(null===(r=L[0])||void 0===r?void 0:r.lastName).trim()||"Your Name",resumeText:_,jobKeywords:["React","JavaScript","Developer"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"absolute top-0 right-0 flex mb-5 p-2 space-x-2",children:[(0,t.jsx)(h.z,{onClick:()=>u(e=>Math.max(e-1,0)),disabled:0===d,className:"p-2",children:(0,t.jsx)(i.Z,{className:"w-5 h-5"})}),(0,t.jsx)(h.z,{onClick:()=>u(e=>Math.min(e+1,H.length-1)),disabled:d===H.length-1,className:"p-2",children:(0,t.jsx)(o.Z,{className:"w-5 h-5"})})]}),H[d]]})})})]}),(0,t.jsxs)("div",{ref:G,className:"p-6 ",style:{minHeight:"1100px"},children:[(0,t.jsxs)("div",{className:"flex justify-end gap-3 mb-4",children:[(0,t.jsx)(h.z,{onClick:()=>N(!b),className:"p-2",children:b?"Hide ATS Score":"Check ATS Score"}),(0,t.jsx)(h.z,{onClick:K,className:"p-2",children:"Download PDF"})]}),(0,t.jsx)("section",{className:"flex justify-start gap-3",children:["#000000","#31572c","#1e40af","#9d0208","#fb8500"].map((e,s)=>(0,t.jsx)("div",{onClick:()=>Q(e),className:"w-8 h-8 rounded-full cursor-pointer",style:{backgroundColor:e}},s))}),(0,t.jsx)("div",{className:"resumeContent ",children:"ResumePreview1"===j?(0,t.jsx)(I,{educationData:y,workExperienceData:T,personalData:L,projectData:Z,skillData:F,achievementData:B,headingColor:O,summaryData:W}):(0,t.jsx)(E,{educationData:y,workExperienceData:T,personalData:L,projectData:Z,skillData:F,achievementData:B,headingColor:O,summaryData:W})}),(0,t.jsxs)("div",{className:"flex justify-center gap-3",children:[(0,t.jsx)(h.z,{onClick:()=>J(1),className:"p-2 ".concat("ResumePreview1"===j?"dark:bg-white light:text-black":"bg-gray-500 text-white"),children:"Template 1"}),(0,t.jsx)(h.z,{onClick:()=>J(2),className:"p-2 ".concat("ResumePreview2"===j?"dark:bg-white light:text-black":"bg-gray-500 text-white"),children:"Template 2"})]})]})]})]})]})}function Q(){let e=(0,r.v9)(e=>e.user),[s]=(0,l.useState)(!1),[a,i]=(0,l.useState)(!1);return((0,l.useEffect)(()=>{},[e.uid,s]),a)?(0,t.jsx)(O,{}):(0,t.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,t.jsx)(c.Z,{menuItemsTop:$.y,menuItemsBottom:$.$,active:"Resume",isKycCheck:!0}),(0,t.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,t.jsx)(n.Z,{menuItemsTop:$.y,menuItemsBottom:$.$,activeMenu:"Resume",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Settings",link:"#"},{label:"Resume Building",link:"#"}]}),(0,t.jsxs)("div",{className:"flex flex-col h-screen",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-6 mt-5 ml-8",children:"Start Building Your Resume"}),(0,t.jsx)("div",{className:"ml-10",children:(0,t.jsx)("button",{onClick:()=>i(!0),className:"w-20 h-20 flex items-center justify-center border-2 border-dashed border-gray-400 rounded-lg cursor-pointer hover:bg-gray-200",children:(0,t.jsx)("span",{className:"text-4xl text-gray-600",children:"+"})})})]})]})]})}a(78068)},70402:function(e,s,a){"use strict";a.r(s),a.d(s,{Label:function(){return o}});var t=a(57437),l=a(2265),r=a(38364),c=a(12218),n=a(49354);let i=(0,c.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.f,{ref:s,className:(0,n.cn)(i(),a),...l})});o.displayName=r.f.displayName},29973:function(e,s,a){"use strict";a.r(s),a.d(s,{Separator:function(){return n}});var t=a(57437),l=a(2265),r=a(48484),c=a(49354);let n=l.forwardRef((e,s)=>{let{className:a,orientation:l="horizontal",decorative:n=!0,...i}=e;return(0,t.jsx)(r.f,{ref:s,decorative:n,orientation:l,className:(0,c.cn)("shrink-0 bg-border","horizontal"===l?"h-[1px] w-full":"h-full w-[1px]",a),...i})});n.displayName=r.f.displayName},27756:function(e,s,a){"use strict";a.d(s,{$:function(){return x},y:function(){return m}});var t=a(57437),l=a(11005),r=a(52022),c=a(25912),n=a(67524),i=a(6540),o=a(40036),d=a(66648);let m=[{href:"#",icon:(0,t.jsx)(d.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,t.jsx)(l.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/settings/personal-info",icon:(0,t.jsx)(r.Z,{className:"h-5 w-5"}),label:"Personal Info"},{href:"/freelancer/settings/professional-info",icon:(0,t.jsx)(c.Z,{className:"h-5 w-5"}),label:"Professional Info"},{href:"/freelancer/settings/projects",icon:(0,t.jsx)(n.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"/freelancer/settings/education-info",icon:(0,t.jsx)(i.Z,{className:"h-5 w-5"}),label:"Education"},{href:"/freelancer/settings/resume",icon:(0,t.jsx)(o.Z,{className:"h-5 w-5"}),label:"Portfolio"}],x=[]}},function(e){e.O(0,[4358,7481,9208,7674,2505,7337,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,7308,2455,9726,2688,2971,7023,1744],function(){return e(e.s=14148)}),_N_E=e.O()}]);