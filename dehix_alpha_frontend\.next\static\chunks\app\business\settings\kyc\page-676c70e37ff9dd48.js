(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9805],{63440:function(e,t,r){Promise.resolve().then(r.bind(r,41095))},41095:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return N}});var a=r(57437),l=r(11444),n=r(64797),s=r(43595),i=r(62688),o=r(2265),c=r(31014),d=r(39343),u=r(59772),m=r(66648),f=r(93033),h=r(48185),v=r(78068),g=r(79055),x=r(15922),p=r(89733),j=r(93363),b=r(77209),I=r(97540);let y=u.z.object({aadharOrGovtId:u.z.string().optional(),frontImageUrl:u.z.union([u.z.instanceof(File),u.z.string().url(),u.z.null()]).optional(),backImageUrl:u.z.union([u.z.instanceof(File),u.z.string().url(),u.z.null()]).optional(),liveCaptureUrl:u.z.union([u.z.instanceof(File),u.z.string().url(),u.z.null()]).optional()});function C(e){let{user_id:t}=e,[r,l]=(0,o.useState)({}),[n,s]=(0,o.useState)(!1),[i,u]=(0,o.useState)("PENDING"),C=(0,d.cI)({resolver:(0,c.F)(y),defaultValues:{aadharOrGovtId:"",frontImageUrl:"",backImageUrl:"",liveCaptureUrl:""},mode:"all"});async function N(e){s(!0);try{let t={frontImageUrl:e.frontImageUrl,backImageUrl:e.backImageUrl,liveCaptureUrl:e.liveCaptureUrl};if(e.frontImageUrl instanceof File){let r=new FormData;r.append("frontImageUrl",e.frontImageUrl);let a=await x.b.post("/register/upload-image",r,{headers:{"Content-Type":"multipart/form-data"}});t.frontImageUrl=a.data.data.Location}if(e.backImageUrl instanceof File){let r=new FormData;r.append("backImageUrl",e.backImageUrl);let a=await x.b.post("/register/upload-image",r,{headers:{"Content-Type":"multipart/form-data"}});t.backImageUrl=a.data.data.Location}if(e.liveCaptureUrl instanceof File){let r=new FormData;r.append("liveCaptureUrl",e.liveCaptureUrl);let a=await x.b.post("/register/upload-image",r,{headers:{"Content-Type":"multipart/form-data"}});t.liveCaptureUrl=a.data.data.Location}let a={aadharOrGovtId:e.aadharOrGovtId,frontImageUrl:t.frontImageUrl,backImageUrl:t.backImageUrl,liveCaptureUrl:t.liveCaptureUrl,status:"APPLIED"};await x.b.put("/business/kyc",{...r,kyc:a}),l({...r,kyc:a}),(0,v.Am)({title:"KYC Updated",description:"Your KYC has been successfully updated."})}catch(e){console.error("API Error:",e),(0,v.Am)({variant:"destructive",title:"Error",description:"Failed to update KYC. Please try again later."})}finally{s(!1)}}return(0,o.useEffect)(()=>{(async()=>{try{var e,r,a,n;let s=await x.b.get("/business/".concat(t));l(s.data),(null==s?void 0:null===(r=s.data)||void 0===r?void 0:null===(e=r.kyc)||void 0===e?void 0:e.status)&&u(null==s?void 0:null===(n=s.data)||void 0===n?void 0:null===(a=n.kyc)||void 0===a?void 0:a.status)}catch(e){console.error("API Error:",e),(0,v.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}})()},[t]),(0,o.useEffect)(()=>{var e,t,a,l;C.reset({aadharOrGovtId:(null==r?void 0:null===(e=r.kyc)||void 0===e?void 0:e.aadharOrGovtId)||"",frontImageUrl:(null==r?void 0:null===(t=r.kyc)||void 0===t?void 0:t.frontImageUrl)||"",backImageUrl:(null==r?void 0:null===(a=r.kyc)||void 0===a?void 0:a.backImageUrl)||"",liveCaptureUrl:(null==r?void 0:null===(l=r.kyc)||void 0===l?void 0:l.liveCaptureUrl)||""})},[r,C]),(0,a.jsx)(h.Zb,{className:"p-10",children:(0,a.jsx)(j.l0,{...C,children:(0,a.jsxs)("form",{onSubmit:C.handleSubmit(N),className:"space-y-6",children:[(0,a.jsxs)("div",{children:["KYC Status"," ",(0,a.jsx)(g.C,{className:"text-xs py-0.5 ".concat(I.d8[i]||" "),children:i.toLowerCase()})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsx)(j.Wi,{control:C.control,name:"aadharOrGovtId",render:e=>{let{field:t}=e;return(0,a.jsxs)(j.xJ,{children:[(0,a.jsx)(j.lX,{children:"Aadhar or Govt Id"}),(0,a.jsx)(j.NI,{children:(0,a.jsx)(b.I,{placeholder:"Enter your Aadhar Id",...t})}),(0,a.jsx)(j.zG,{})]})}})}),(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsx)(j.Wi,{control:C.control,name:"frontImageUrl",render:e=>{let{field:t}=e;return(0,a.jsxs)(j.xJ,{children:[(0,a.jsx)(j.lX,{children:"Document Front Img"}),(0,a.jsx)(j.NI,{children:(0,a.jsx)("div",{className:"flex items-center gap-4",children:t.value&&"string"==typeof t.value?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.default,{src:t.value,alt:"Front Document",width:128,height:128,className:"rounded-md object-cover"}),(0,a.jsx)(p.z,{type:"button",variant:"outline",size:"sm",onClick:()=>t.onChange(""),className:"ml-auto",children:"Change Image"})]}):(0,a.jsx)(b.I,{type:"file",onChange:e=>{var r;let a=null===(r=e.target.files)||void 0===r?void 0:r[0];a&&t.onChange(a)},onBlur:t.onBlur})})}),(0,a.jsx)(j.zG,{})]})}})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsx)(j.Wi,{control:C.control,name:"backImageUrl",render:e=>{let{field:t}=e;return(0,a.jsxs)(j.xJ,{children:[(0,a.jsx)(j.lX,{children:"Document Back Img"}),(0,a.jsx)(j.NI,{children:(0,a.jsx)("div",{className:"flex items-center gap-4",children:t.value&&"string"==typeof t.value?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.default,{src:t.value,alt:"Back Document",width:128,height:128,className:"rounded-md object-cover"}),(0,a.jsx)(p.z,{type:"button",variant:"outline",size:"sm",onClick:()=>t.onChange(""),className:"ml-auto",children:"Change Image"})]}):(0,a.jsx)(b.I,{type:"file",onChange:e=>{var r;let a=null===(r=e.target.files)||void 0===r?void 0:r[0];a&&t.onChange(a)},onBlur:t.onBlur})})}),(0,a.jsx)(j.zG,{})]})}})}),(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsx)(f.Z,{form:C})})]}),(0,a.jsx)(p.z,{className:"w-full",type:"submit",disabled:n,children:n?"Loading...":"Save KYC"})]})})})}function N(){let e=(0,l.v9)(e=>e.user);return(0,a.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,a.jsx)(n.Z,{menuItemsTop:s.y,menuItemsBottom:s.$,active:"kyc",isKycCheck:!0}),(0,a.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 mb-8 sm:pl-14",children:[(0,a.jsx)(i.Z,{menuItemsTop:s.y,menuItemsBottom:s.$,activeMenu:"Personal Info",breadcrumbItems:[{label:"Business",link:"/dashboard/business"},{label:"Settings",link:"#"},{label:"kyc",link:"#"}]}),(0,a.jsx)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8",children:(0,a.jsx)(C,{user_id:e.uid})})]})]})}},93033:function(e,t,r){"use strict";r.d(t,{Z:function(){return c}});var a=r(57437),l=r(2265),n=r(66648);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(33480).Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var i=r(89733),o=r(93363),c=e=>{let{form:t}=e,r=(0,l.useRef)(null),c=(0,l.useRef)(null),[d,u]=(0,l.useState)(null),[m,f]=(0,l.useState)(!1),[h,v]=(0,l.useState)(null),g=async()=>{f(!0);try{let e=await navigator.mediaDevices.getUserMedia({video:!0});v(e),r.current&&(r.current.srcObject=e,await new Promise(e=>{r.current.onloadedmetadata=()=>e(!0)}))}catch(e){console.error("Error accessing camera:",e),f(!1)}},x=()=>{if(r.current&&c.current){let e=r.current,a=c.current,l=c.current.getContext("2d");if(l&&e.videoWidth>0&&e.videoHeight>0){a.width=e.videoWidth,a.height=e.videoHeight,l.drawImage(e,0,0,a.width,a.height);let r=a.toDataURL("image/jpeg");u(r),fetch(r).then(e=>e.blob()).then(e=>{let r=new File([e],"live-capture.jpg",{type:"image/jpeg"});t.setValue("liveCaptureUrl",r)}),h&&h.getTracks().forEach(e=>e.stop()),f(!1)}}};return(0,a.jsx)(o.Wi,{control:t.control,name:"liveCaptureUrl",render:e=>{let{field:l}=e;return(0,a.jsxs)(o.xJ,{children:[(0,a.jsx)(o.lX,{children:"Live Capture"}),(0,a.jsx)(o.NI,{children:(0,a.jsx)("div",{className:"flex flex-col md:justify-start md:items-start sm:justify-center items-center gap-2",children:(0,a.jsxs)("div",{className:"flex s gap-2",children:[(d||l.value)&&(0,a.jsx)("div",{className:"flex flex-col items-center",children:(0,a.jsx)(n.default,{src:d||l.value,alt:"Live Capture",width:128,height:128,className:"rounded-md object-cover"})}),!l.value&&d&&(0,a.jsx)(i.z,{type:"button",variant:"outline",size:"sm",className:"mt-2",onClick:()=>{u(null),t.setValue("liveCaptureUrl",""),g()},children:"Retake Photo"}),!m&&(0,a.jsxs)(i.z,{type:"button",onClick:g,children:[(0,a.jsx)(s,{className:"w-4 h-4 mr-2"}),"Live Capture"]}),m&&(0,a.jsx)(i.z,{type:"button",onClick:x,children:"Take Photo"}),m&&(0,a.jsx)("video",{ref:r,autoPlay:!0,className:"w-64 h-48 border rounded-md",children:(0,a.jsx)("track",{kind:"captions",srcLang:"en",label:"English captions",src:""})}),(0,a.jsx)("canvas",{ref:c,className:"hidden",width:"640",height:"480"})]})})}),(0,a.jsx)(o.zG,{})]})}})}},93363:function(e,t,r){"use strict";r.d(t,{NI:function(){return g},Wi:function(){return u},l0:function(){return c},lX:function(){return v},pf:function(){return x},xJ:function(){return h},zG:function(){return p}});var a=r(57437),l=r(2265),n=r(63355),s=r(39343),i=r(49354),o=r(70402);let c=s.RV,d=l.createContext({}),u=e=>{let{...t}=e;return(0,a.jsx)(d.Provider,{value:{name:t.name},children:(0,a.jsx)(s.Qr,{...t})})},m=()=>{let e=l.useContext(d),t=l.useContext(f),{getFieldState:r,formState:a}=(0,s.Gc)(),n=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...n}},f=l.createContext({}),h=l.forwardRef((e,t)=>{let{className:r,...n}=e,s=l.useId();return(0,a.jsx)(f.Provider,{value:{id:s},children:(0,a.jsx)("div",{ref:t,className:(0,i.cn)("space-y-2",r),...n})})});h.displayName="FormItem";let v=l.forwardRef((e,t)=>{let{className:r,...l}=e,{error:n,formItemId:s}=m();return(0,a.jsx)(o.Label,{ref:t,className:(0,i.cn)(n&&"text-destructive",r),htmlFor:s,...l})});v.displayName="FormLabel";let g=l.forwardRef((e,t)=>{let{...r}=e,{error:l,formItemId:s,formDescriptionId:i,formMessageId:o}=m();return(0,a.jsx)(n.g7,{ref:t,id:s,"aria-describedby":l?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!l,...r})});g.displayName="FormControl";let x=l.forwardRef((e,t)=>{let{className:r,...l}=e,{formDescriptionId:n}=m();return(0,a.jsx)("p",{ref:t,id:n,className:(0,i.cn)("text-sm text-muted-foreground",r),...l})});x.displayName="FormDescription";let p=l.forwardRef((e,t)=>{let{className:r,children:l,...n}=e,{error:s,formMessageId:o}=m(),c=s?String(null==s?void 0:s.message):l;return c?(0,a.jsx)("p",{ref:t,id:o,className:(0,i.cn)("text-sm font-medium text-destructive",r),...n,children:c}):null});p.displayName="FormMessage"},70402:function(e,t,r){"use strict";r.r(t),r.d(t,{Label:function(){return c}});var a=r(57437),l=r(2265),n=r(38364),s=r(12218),i=r(49354);let o=(0,s.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)(n.f,{ref:t,className:(0,i.cn)(o(),r),...l})});c.displayName=n.f.displayName},43595:function(e,t,r){"use strict";r.d(t,{$:function(){return o},y:function(){return i}});var a=r(57437),l=r(11005),n=r(52022),s=r(66648);let i=[{href:"#",icon:(0,a.jsx)(s.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,a.jsx)(l.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/business/settings/business-info",icon:(0,a.jsx)(n.Z,{className:"h-5 w-5"}),label:"Business Info"}],o=[]},97540:function(e,t,r){"use strict";var a,l,n,s,i,o;r.d(t,{cd:function(){return a},d8:function(){return c},kJ:function(){return l},sB:function(){return n}}),(s=a||(a={})).Mastery="Mastery",s.Proficient="Proficient",s.Beginner="Beginner",(i=l||(l={})).ACTIVE="Active",i.PENDING="Pending",i.REJECTED="Rejected",i.COMPLETED="Completed",(o=n||(n={})).ACTIVE="ACTIVE",o.PENDING="PENDING",o.REJECTED="REJECTED",o.COMPLETED="COMPLETED";let c={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},38364:function(e,t,r){"use strict";r.d(t,{f:function(){return i}});var a=r(2265),l=r(18676),n=r(57437),s=a.forwardRef((e,t)=>(0,n.jsx)(l.WV.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var i=s}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,1374,2455,9726,2688,2971,7023,1744],function(){return e(e.s=63440)}),_N_E=e.O()}]);