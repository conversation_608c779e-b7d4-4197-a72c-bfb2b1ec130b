"use strict";exports.id=4718,exports.ids=[4718],exports.modules={32933:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(80851).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},53982:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(80851).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},90434:(e,t,r)=>{r.d(t,{default:()=>o.a});var n=r(79404),o=r.n(n)},53416:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return n}}),r(23658);let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},39683:(e,t,r)=>{function n(e,t,r,n){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return n}}),r(23658),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79404:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return y}});let n=r(91174),o=r(10326),a=n._(r(17577)),l=r(25619),u=r(60944),i=r(43071),s=r(51348),c=r(53416),d=r(50131),f=r(52413),p=r(49408),h=r(39683),m=r(3486),g=r(57767);function v(e){return"string"==typeof e?e:(0,i.formatUrl)(e)}let y=a.default.forwardRef(function(e,t){let r,n;let{href:i,as:y,children:b,prefetch:w=null,passHref:x,replace:M,shallow:R,scroll:j,locale:_,onClick:C,onMouseEnter:P,onTouchStart:E,legacyBehavior:O=!1,...S}=e;r=b,O&&("string"==typeof r||"number"==typeof r)&&(r=(0,o.jsx)("a",{children:r}));let I=a.default.useContext(d.RouterContext),k=a.default.useContext(f.AppRouterContext),D=null!=I?I:k,N=!I,T=!1!==w,A=null===w?g.PrefetchKind.AUTO:g.PrefetchKind.FULL,{href:L,as:F}=a.default.useMemo(()=>{if(!I){let e=v(i);return{href:e,as:y?v(y):e}}let[e,t]=(0,l.resolveHref)(I,i,!0);return{href:e,as:y?(0,l.resolveHref)(I,y):t||e}},[I,i,y]),U=a.default.useRef(L),W=a.default.useRef(F);O&&(n=a.default.Children.only(r));let K=O?n&&"object"==typeof n&&n.ref:t,[V,B,z]=(0,p.useIntersection)({rootMargin:"200px"}),G=a.default.useCallback(e=>{(W.current!==F||U.current!==L)&&(z(),W.current=F,U.current=L),V(e),K&&("function"==typeof K?K(e):"object"==typeof K&&(K.current=e))},[F,K,L,z,V]);a.default.useEffect(()=>{},[F,L,B,_,T,null==I?void 0:I.locale,D,N,A]);let Z={ref:G,onClick(e){O||"function"!=typeof C||C(e),O&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),D&&!e.defaultPrevented&&function(e,t,r,n,o,l,i,s,c){let{nodeName:d}=e.currentTarget;if("A"===d.toUpperCase()&&(function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!c&&!(0,u.isLocalURL)(r)))return;e.preventDefault();let f=()=>{let e=null==i||i;"beforePopState"in t?t[o?"replace":"push"](r,n,{shallow:l,locale:s,scroll:e}):t[o?"replace":"push"](n||r,{scroll:e})};c?a.default.startTransition(f):f()}(e,D,L,F,M,R,j,_,N)},onMouseEnter(e){O||"function"!=typeof P||P(e),O&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e)},onTouchStart:function(e){O||"function"!=typeof E||E(e),O&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e)}};if((0,s.isAbsoluteUrl)(F))Z.href=F;else if(!O||x||"a"===n.type&&!("href"in n.props)){let e=void 0!==_?_:null==I?void 0:I.locale,t=(null==I?void 0:I.isLocaleDomain)&&(0,h.getDomainLocale)(F,e,null==I?void 0:I.locales,null==I?void 0:I.domainLocales);Z.href=t||(0,m.addBasePath)((0,c.addLocale)(F,e,null==I?void 0:I.defaultLocale))}return O?a.default.cloneElement(n,Z):(0,o.jsx)("a",{...S,...Z,children:r})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10956:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25619:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return d}});let n=r(72149),o=r(43071),a=r(20757),l=r(51348),u=r(23658),i=r(60944),s=r(94903),c=r(81394);function d(e,t,r){let d;let f="string"==typeof t?t:(0,o.formatWithValidation)(t),p=f.match(/^[a-zA-Z]{1,}:\/\//),h=p?f.slice(p[0].length):f;if((h.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+f+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,l.normalizeRepeatedSlashes)(h);f=(p?p[0]:"")+t}if(!(0,i.isLocalURL)(f))return r?[f]:f;try{d=new URL(f.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(f,d);e.pathname=(0,u.normalizePathTrailingSlash)(e.pathname);let t="";if((0,s.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:l,params:u}=(0,c.interpolateAs)(e.pathname,e.pathname,r);l&&(t=(0,o.formatWithValidation)({pathname:l,hash:e.hash,query:(0,a.omit)(r,u)}))}let l=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return r?[l,t||l]:l}catch(e){return r?[f]:f}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49408:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return i}});let n=r(17577),o=r(10956),a="function"==typeof IntersectionObserver,l=new Map,u=[];function i(e){let{rootRef:t,rootMargin:r,disabled:i}=e,s=i||!a,[c,d]=(0,n.useState)(!1),f=(0,n.useRef)(null),p=(0,n.useCallback)(e=>{f.current=e},[]);return(0,n.useEffect)(()=>{if(a){if(s||c)return;let e=f.current;if(e&&e.tagName)return function(e,t,r){let{id:n,observer:o,elements:a}=function(e){let t;let r={root:e.root||null,margin:e.rootMargin||""},n=u.find(e=>e.root===r.root&&e.margin===r.margin);if(n&&(t=l.get(n)))return t;let o=new Map;return t={id:r,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)})},e),elements:o},u.push(r),l.set(r,t),t}(r);return a.set(e,t),o.observe(e),function(){if(a.delete(e),o.unobserve(e),0===a.size){o.disconnect(),l.delete(n);let e=u.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&u.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:r})}else if(!c){let e=(0,o.requestIdleCallback)(()=>d(!0));return()=>(0,o.cancelIdleCallback)(e)}},[s,r,t,c,f.current]),[p,c,(0,n.useCallback)(()=>{d(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},50131:(e,t,r)=>{e.exports=r(81616).vendored.contexts.RouterContext},2451:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function o(e){return r.test(e)?e.replace(n,"\\$&"):e}},43071:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return u},urlObjectKeys:function(){return l}});let n=r(58374)._(r(72149)),o=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",l=e.pathname||"",u=e.hash||"",i=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:r&&(s=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(s+=":"+e.port)),i&&"object"==typeof i&&(i=String(n.urlQueryToSearchParams(i)));let c=e.search||i&&"?"+i||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||o.test(a))&&!1!==s?(s="//"+(s||""),l&&"/"!==l[0]&&(l="/"+l)):s||(s=""),u&&"#"!==u[0]&&(u="#"+u),c&&"?"!==c[0]&&(c="?"+c),""+a+s+(l=l.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+u}let l=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function u(e){return a(e)}},94903:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRoutes:function(){return n.getSortedRoutes},isDynamicRoute:function(){return o.isDynamicRoute}});let n=r(44712),o=r(45541)},81394:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return a}});let n=r(9966),o=r(37249);function a(e,t,r){let a="",l=(0,o.getRouteRegex)(e),u=l.groups,i=(t!==e?(0,n.getRouteMatcher)(l)(t):"")||r;a=e;let s=Object.keys(u);return s.every(e=>{let t=i[e]||"",{repeat:r,optional:n}=u[e],o="["+(r?"...":"")+e+"]";return n&&(o=(t?"":"/")+"["+o+"]"),r&&!Array.isArray(t)&&(t=[t]),(n||e in i)&&(a=a.replace(o,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(a=""),{params:s,result:a}}},45541:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return a}});let n=r(87356),o=/\/\[[^/]+?\](?=\/|$)/;function a(e){return(0,n.isInterceptionRouteAppPath)(e)&&(e=(0,n.extractInterceptionRouteInformation)(e).interceptedRoute),o.test(e)}},60944:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=r(51348),o=r(37929);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},20757:(e,t)=>{function r(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},72149:(e,t)=>{function r(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,o]=e;Array.isArray(o)?o.forEach(e=>t.append(r,n(e))):t.set(r,n(o))}),t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},9966:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let n=r(51348);function o(e){let{re:t,groups:r}=e;return e=>{let o=t.exec(e);if(!o)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},l={};return Object.keys(r).forEach(e=>{let t=r[e],n=o[t.pos];void 0!==n&&(l[e]=~n.indexOf("/")?n.split("/").map(e=>a(e)):t.repeat?[a(n)]:a(n))}),l}}},37249:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return f},getNamedRouteRegex:function(){return d},getRouteRegex:function(){return i}});let n=r(87356),o=r(2451),a=r(83236);function l(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function u(e){let t=(0,a.removeTrailingSlash)(e).slice(1).split("/"),r={},u=1;return{parameterizedRoute:t.map(e=>{let t=n.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&a){let{key:e,optional:n,repeat:i}=l(a[1]);return r[e]={pos:u++,repeat:i,optional:n},"/"+(0,o.escapeStringRegexp)(t)+"([^/]+?)"}if(!a)return"/"+(0,o.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:n}=l(a[1]);return r[e]={pos:u++,repeat:t,optional:n},t?n?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}function i(e){let{parameterizedRoute:t,groups:r}=u(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}function s(e){let{interceptionMarker:t,getSafeRouteKey:r,segment:n,routeKeys:a,keyPrefix:u}=e,{key:i,optional:s,repeat:c}=l(n),d=i.replace(/\W/g,"");u&&(d=""+u+d);let f=!1;(0===d.length||d.length>30)&&(f=!0),isNaN(parseInt(d.slice(0,1)))||(f=!0),f&&(d=r()),u?a[d]=""+u+i:a[d]=i;let p=t?(0,o.escapeStringRegexp)(t):"";return c?s?"(?:/"+p+"(?<"+d+">.+?))?":"/"+p+"(?<"+d+">.+?)":"/"+p+"(?<"+d+">[^/]+?)"}function c(e,t){let r;let l=(0,a.removeTrailingSlash)(e).slice(1).split("/"),u=(r=0,()=>{let e="",t=++r;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),i={};return{namedParameterizedRoute:l.map(e=>{let r=n.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);if(r&&a){let[r]=e.split(a[0]);return s({getSafeRouteKey:u,interceptionMarker:r,segment:a[1],routeKeys:i,keyPrefix:t?"nxtI":void 0})}return a?s({getSafeRouteKey:u,segment:a[1],routeKeys:i,keyPrefix:t?"nxtP":void 0}):"/"+(0,o.escapeStringRegexp)(e)}).join(""),routeKeys:i}}function d(e,t){let r=c(e,t);return{...i(e),namedRegex:"^"+r.namedParameterizedRoute+"(?:/)?$",routeKeys:r.routeKeys}}function f(e,t){let{parameterizedRoute:r}=u(e),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:o}=c(e,!1);return{namedRegex:"^"+o+(n?"(?:(/.*)?)":"")+"$"}}},44712:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSortedRoutes",{enumerable:!0,get:function(){return n}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").');r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Error("Catch-all must be the last part of the URL.");let o=e[0];if(o.startsWith("[")&&o.endsWith("]")){let r=o.slice(1,-1),l=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),l=!0),r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+r+"').");if(r.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+r+"').");function a(e,r){if(null!==e&&e!==r)throw Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"').");t.forEach(e=>{if(e===r)throw Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path');if(e.replace(/\W/g,"")===o.replace(/\W/g,""))throw Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path')}),t.push(r)}if(n){if(l){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).');a(this.optionalRestSlugName,r),this.optionalRestSlugName=r,o="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").');a(this.restSlugName,r),this.restSlugName=r,o="[...]"}}else{if(l)throw Error('Optional route parameters are not yet supported ("'+e[0]+'").');a(this.slugName,r),this.slugName=r,o="[]"}}this.children.has(o)||this.children.set(o,new r),this.children.get(o)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function n(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}},51348:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return v},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return i},getLocationOrigin:function(){return l},getURL:function(){return u},isAbsoluteUrl:function(){return a},isResSent:function(){return s},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>o.test(e);function l(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function u(){let{href:e}=window.location,t=l();return e.substring(t.length)}function i(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&s(r))return n;if(!n)throw Error('"'+i(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.');return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},17124:(e,t,r)=>{r.d(t,{gm:()=>a});var n=r(17577);r(10326);var o=n.createContext(void 0);function a(e){let t=n.useContext(o);return e||t||"ltr"}},76234:(e,t,r)=>{r.d(t,{oC:()=>e8,VY:()=>e3,ZA:()=>e9,ck:()=>e6,wU:()=>tr,__:()=>e2,Uv:()=>e7,Ee:()=>te,Rk:()=>tt,fC:()=>e5,Z0:()=>tn,Tr:()=>to,tu:()=>tl,fF:()=>ta,xz:()=>e4});var n=r(17577),o=r(82561),a=r(48051),l=r(93095),u=r(52067),i=r(77335),s=r(73866),c=r(17124),d=r(825),f=r(80699),p=r(10441),h=r(88957),m=r(17103),g=r(83078),v=r(9815),y=r(15594),b=r(10326),w=n.forwardRef((e,t)=>{let{children:r,...o}=e,a=n.Children.toArray(r),l=a.find(R);if(l){let e=l.props.children,r=a.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,b.jsx)(x,{...o,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,b.jsx)(x,{...o,ref:t,children:r})});w.displayName="Slot";var x=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return n.cloneElement(r,{...function(e,t){let r={...t};for(let n in t){let o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=(...e)=>{a(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...a}:"className"===n&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props),ref:t?(0,a.F)(t,e):e})}return n.Children.count(r)>1?n.Children.only(null):null});x.displayName="SlotClone";var M=({children:e})=>(0,b.jsx)(b.Fragment,{children:e});function R(e){return n.isValidElement(e)&&e.type===M}var j=r(55049),_=r(35664),C=r(17397),P=["Enter"," "],E=["ArrowUp","PageDown","End"],O=["ArrowDown","PageUp","Home",...E],S={ltr:[...P,"ArrowRight"],rtl:[...P,"ArrowLeft"]},I={ltr:["ArrowLeft"],rtl:["ArrowRight"]},k="Menu",[D,N,T]=(0,s.B)(k),[A,L]=(0,l.b)(k,[T,m.D7,y.Pc]),F=(0,m.D7)(),U=(0,y.Pc)(),[W,K]=A(k),[V,B]=A(k),z=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:a,onOpenChange:l,modal:u=!0}=e,i=F(t),[s,d]=n.useState(null),f=n.useRef(!1),p=(0,j.W)(l),h=(0,c.gm)(a);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,b.jsx)(m.fC,{...i,children:(0,b.jsx)(W,{scope:t,open:r,onOpenChange:p,content:s,onContentChange:d,children:(0,b.jsx)(V,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:u,children:o})})})};z.displayName=k;var G=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=F(r);return(0,b.jsx)(m.ee,{...o,...n,ref:t})});G.displayName="MenuAnchor";var Z="MenuPortal",[Y,q]=A(Z,{forceMount:void 0}),$=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,a=K(Z,t);return(0,b.jsx)(Y,{scope:t,forceMount:r,children:(0,b.jsx)(v.z,{present:r||a.open,children:(0,b.jsx)(g.h,{asChild:!0,container:o,children:n})})})};$.displayName=Z;var H="MenuContent",[X,Q]=A(H),J=n.forwardRef((e,t)=>{let r=q(H,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,a=K(H,e.__scopeMenu),l=B(H,e.__scopeMenu);return(0,b.jsx)(D.Provider,{scope:e.__scopeMenu,children:(0,b.jsx)(v.z,{present:n||a.open,children:(0,b.jsx)(D.Slot,{scope:e.__scopeMenu,children:l.modal?(0,b.jsx)(ee,{...o,ref:t}):(0,b.jsx)(et,{...o,ref:t})})})})}),ee=n.forwardRef((e,t)=>{let r=K(H,e.__scopeMenu),l=n.useRef(null),u=(0,a.e)(t,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,_.Ry)(e)},[]),(0,b.jsx)(er,{...e,ref:u,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),et=n.forwardRef((e,t)=>{let r=K(H,e.__scopeMenu);return(0,b.jsx)(er,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),er=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:l=!1,trapFocus:u,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:c,onEntryFocus:h,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:x,onInteractOutside:M,onDismiss:R,disableOutsideScroll:j,..._}=e,P=K(H,r),S=B(H,r),I=F(r),k=U(r),D=N(r),[T,A]=n.useState(null),L=n.useRef(null),W=(0,a.e)(t,L,P.onContentChange),V=n.useRef(0),z=n.useRef(""),G=n.useRef(0),Z=n.useRef(null),Y=n.useRef("right"),q=n.useRef(0),$=j?C.Z:n.Fragment,Q=e=>{let t=z.current+e,r=D().filter(e=>!e.disabled),n=document.activeElement,o=r.find(e=>e.ref.current===n)?.textValue,a=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(a=a.filter(e=>e!==r));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==r?l:void 0}(r.map(e=>e.textValue),t,o),l=r.find(e=>e.textValue===a)?.ref.current;(function e(t){z.current=t,window.clearTimeout(V.current),""!==t&&(V.current=window.setTimeout(()=>e(""),1e3))})(t),l&&setTimeout(()=>l.focus())};n.useEffect(()=>()=>window.clearTimeout(V.current),[]),(0,f.EW)();let J=n.useCallback(e=>Y.current===Z.current?.side&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let l=t[e].x,u=t[e].y,i=t[a].x,s=t[a].y;u>n!=s>n&&r<(i-l)*(n-u)/(s-u)+l&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,Z.current?.area),[]);return(0,b.jsx)(X,{scope:r,searchRef:z,onItemEnter:n.useCallback(e=>{J(e)&&e.preventDefault()},[J]),onItemLeave:n.useCallback(e=>{J(e)||(L.current?.focus(),A(null))},[J]),onTriggerLeave:n.useCallback(e=>{J(e)&&e.preventDefault()},[J]),pointerGraceTimerRef:G,onPointerGraceIntentChange:n.useCallback(e=>{Z.current=e},[]),children:(0,b.jsx)($,{...j?{as:w,allowPinchZoom:!0}:void 0,children:(0,b.jsx)(p.M,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.M)(i,e=>{e.preventDefault(),L.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,b.jsx)(d.XB,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:x,onInteractOutside:M,onDismiss:R,children:(0,b.jsx)(y.fC,{asChild:!0,...k,dir:S.dir,orientation:"vertical",loop:l,currentTabStopId:T,onCurrentTabStopIdChange:A,onEntryFocus:(0,o.M)(h,e=>{S.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,b.jsx)(m.VY,{role:"menu","aria-orientation":"vertical","data-state":eS(P.open),"data-radix-menu-content":"",dir:S.dir,...I,..._,ref:W,style:{outline:"none",..._.style},onKeyDown:(0,o.M)(_.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&Q(e.key));let o=L.current;if(e.target!==o||!O.includes(e.key))return;e.preventDefault();let a=D().filter(e=>!e.disabled).map(e=>e.ref.current);E.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(V.current),z.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,eD(e=>{let t=e.target,r=q.current!==e.clientX;if(e.currentTarget.contains(t)&&r){let t=e.clientX>q.current?"right":"left";Y.current=t,q.current=e.clientX}}))})})})})})})});J.displayName=H;var en=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,b.jsx)(i.WV.div,{role:"group",...n,ref:t})});en.displayName="MenuGroup";var eo=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,b.jsx)(i.WV.div,{...n,ref:t})});eo.displayName="MenuLabel";var ea="MenuItem",el="menu.itemSelect",eu=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:l,...u}=e,s=n.useRef(null),c=B(ea,e.__scopeMenu),d=Q(ea,e.__scopeMenu),f=(0,a.e)(t,s),p=n.useRef(!1);return(0,b.jsx)(ei,{...u,ref:f,disabled:r,onClick:(0,o.M)(e.onClick,()=>{let e=s.current;if(!r&&e){let t=new CustomEvent(el,{bubbles:!0,cancelable:!0});e.addEventListener(el,e=>l?.(e),{once:!0}),(0,i.jH)(e,t),t.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!r&&(!t||" "!==e.key)&&P.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eu.displayName=ea;var ei=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:l=!1,textValue:u,...s}=e,c=Q(ea,r),d=U(r),f=n.useRef(null),p=(0,a.e)(t,f),[h,m]=n.useState(!1),[g,v]=n.useState("");return n.useEffect(()=>{let e=f.current;e&&v((e.textContent??"").trim())},[s.children]),(0,b.jsx)(D.ItemSlot,{scope:r,disabled:l,textValue:u??g,children:(0,b.jsx)(y.ck,{asChild:!0,...d,focusable:!l,children:(0,b.jsx)(i.WV.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...s,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,eD(e=>{l?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eD(e=>c.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>m(!0)),onBlur:(0,o.M)(e.onBlur,()=>m(!1))})})})}),es=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,b.jsx)(ev,{scope:e.__scopeMenu,checked:r,children:(0,b.jsx)(eu,{role:"menuitemcheckbox","aria-checked":eI(r)?"mixed":r,...a,ref:t,"data-state":ek(r),onSelect:(0,o.M)(a.onSelect,()=>n?.(!!eI(r)||!r),{checkForDefaultPrevented:!1})})})});es.displayName="MenuCheckboxItem";var ec="MenuRadioGroup",[ed,ef]=A(ec,{value:void 0,onValueChange:()=>{}}),ep=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,a=(0,j.W)(n);return(0,b.jsx)(ed,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,b.jsx)(en,{...o,ref:t})})});ep.displayName=ec;var eh="MenuRadioItem",em=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=ef(eh,e.__scopeMenu),l=r===a.value;return(0,b.jsx)(ev,{scope:e.__scopeMenu,checked:l,children:(0,b.jsx)(eu,{role:"menuitemradio","aria-checked":l,...n,ref:t,"data-state":ek(l),onSelect:(0,o.M)(n.onSelect,()=>a.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});em.displayName=eh;var eg="MenuItemIndicator",[ev,ey]=A(eg,{checked:!1}),eb=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,a=ey(eg,r);return(0,b.jsx)(v.z,{present:n||eI(a.checked)||!0===a.checked,children:(0,b.jsx)(i.WV.span,{...o,ref:t,"data-state":ek(a.checked)})})});eb.displayName=eg;var ew=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,b.jsx)(i.WV.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});ew.displayName="MenuSeparator";var ex=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=F(r);return(0,b.jsx)(m.Eh,{...o,...n,ref:t})});ex.displayName="MenuArrow";var eM="MenuSub",[eR,ej]=A(eM),e_=e=>{let{__scopeMenu:t,children:r,open:o=!1,onOpenChange:a}=e,l=K(eM,t),u=F(t),[i,s]=n.useState(null),[c,d]=n.useState(null),f=(0,j.W)(a);return n.useEffect(()=>(!1===l.open&&f(!1),()=>f(!1)),[l.open,f]),(0,b.jsx)(m.fC,{...u,children:(0,b.jsx)(W,{scope:t,open:o,onOpenChange:f,content:c,onContentChange:d,children:(0,b.jsx)(eR,{scope:t,contentId:(0,h.M)(),triggerId:(0,h.M)(),trigger:i,onTriggerChange:s,children:r})})})};e_.displayName=eM;var eC="MenuSubTrigger",eP=n.forwardRef((e,t)=>{let r=K(eC,e.__scopeMenu),l=B(eC,e.__scopeMenu),u=ej(eC,e.__scopeMenu),i=Q(eC,e.__scopeMenu),s=n.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=i,f={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,b.jsx)(G,{asChild:!0,...f,children:(0,b.jsx)(ei,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":u.contentId,"data-state":eS(r.open),...e,ref:(0,a.F)(t,u.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,eD(t=>{i.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||s.current||(i.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eD(e=>{p();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,o="right"===n,a=t[o?"left":"right"],l=t[o?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:t.top},{x:l,y:t.top},{x:l,y:t.bottom},{x:a,y:t.bottom}],side:n}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,t=>{let n=""!==i.searchRef.current;!e.disabled&&(!n||" "!==t.key)&&S[l.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});eP.displayName=eC;var eE="MenuSubContent",eO=n.forwardRef((e,t)=>{let r=q(H,e.__scopeMenu),{forceMount:l=r.forceMount,...u}=e,i=K(H,e.__scopeMenu),s=B(H,e.__scopeMenu),c=ej(eE,e.__scopeMenu),d=n.useRef(null),f=(0,a.e)(t,d);return(0,b.jsx)(D.Provider,{scope:e.__scopeMenu,children:(0,b.jsx)(v.z,{present:l||i.open,children:(0,b.jsx)(D.Slot,{scope:e.__scopeMenu,children:(0,b.jsx)(er,{id:c.contentId,"aria-labelledby":c.triggerId,...u,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{s.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==c.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=I[s.dir].includes(e.key);t&&r&&(i.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function eS(e){return e?"open":"closed"}function eI(e){return"indeterminate"===e}function ek(e){return eI(e)?"indeterminate":e?"checked":"unchecked"}function eD(e){return t=>"mouse"===t.pointerType?e(t):void 0}eO.displayName=eE;var eN="DropdownMenu",[eT,eA]=(0,l.b)(eN,[L]),eL=L(),[eF,eU]=eT(eN),eW=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:a,defaultOpen:l,onOpenChange:i,modal:s=!0}=e,c=eL(t),d=n.useRef(null),[f=!1,p]=(0,u.T)({prop:a,defaultProp:l,onChange:i});return(0,b.jsx)(eF,{scope:t,triggerId:(0,h.M)(),triggerRef:d,contentId:(0,h.M)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,b.jsx)(z,{...c,open:f,onOpenChange:p,dir:o,modal:s,children:r})})};eW.displayName=eN;var eK="DropdownMenuTrigger",eV=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...l}=e,u=eU(eK,r),s=eL(r);return(0,b.jsx)(G,{asChild:!0,...s,children:(0,b.jsx)(i.WV.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...l,ref:(0,a.F)(t,u.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eV.displayName=eK;var eB=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eL(t);return(0,b.jsx)($,{...n,...r})};eB.displayName="DropdownMenuPortal";var ez="DropdownMenuContent",eG=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,l=eU(ez,r),u=eL(r),i=n.useRef(!1);return(0,b.jsx)(J,{id:l.contentId,"aria-labelledby":l.triggerId,...u,...a,ref:t,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{i.current||l.triggerRef.current?.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!l.modal||n)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eG.displayName=ez;var eZ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,b.jsx)(en,{...o,...n,ref:t})});eZ.displayName="DropdownMenuGroup";var eY=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,b.jsx)(eo,{...o,...n,ref:t})});eY.displayName="DropdownMenuLabel";var eq=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,b.jsx)(eu,{...o,...n,ref:t})});eq.displayName="DropdownMenuItem";var e$=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,b.jsx)(es,{...o,...n,ref:t})});e$.displayName="DropdownMenuCheckboxItem";var eH=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,b.jsx)(ep,{...o,...n,ref:t})});eH.displayName="DropdownMenuRadioGroup";var eX=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,b.jsx)(em,{...o,...n,ref:t})});eX.displayName="DropdownMenuRadioItem";var eQ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,b.jsx)(eb,{...o,...n,ref:t})});eQ.displayName="DropdownMenuItemIndicator";var eJ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,b.jsx)(ew,{...o,...n,ref:t})});eJ.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,b.jsx)(ex,{...o,...n,ref:t})}).displayName="DropdownMenuArrow";var e0=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,b.jsx)(eP,{...o,...n,ref:t})});e0.displayName="DropdownMenuSubTrigger";var e1=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,b.jsx)(eO,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e1.displayName="DropdownMenuSubContent";var e5=eW,e4=eV,e7=eB,e3=eG,e9=eZ,e2=eY,e6=eq,e8=e$,te=eH,tt=eX,tr=eQ,tn=eJ,to=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:o,defaultOpen:a}=e,l=eL(t),[i=!1,s]=(0,u.T)({prop:n,defaultProp:a,onChange:o});return(0,b.jsx)(e_,{...l,open:i,onOpenChange:s,children:r})},ta=e0,tl=e1},15594:(e,t,r)=>{r.d(t,{Pc:()=>x,ck:()=>I,fC:()=>S});var n=r(17577),o=r(82561),a=r(73866),l=r(48051),u=r(93095),i=r(88957),s=r(77335),c=r(55049),d=r(52067),f=r(17124),p=r(10326),h="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[v,y,b]=(0,a.B)(g),[w,x]=(0,u.b)(g,[b]),[M,R]=w(g),j=n.forwardRef((e,t)=>(0,p.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(_,{...e,ref:t})})}));j.displayName=g;var _=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:u=!1,dir:i,currentTabStopId:g,defaultCurrentTabStopId:v,onCurrentTabStopIdChange:b,onEntryFocus:w,preventScrollOnEntryFocus:x=!1,...R}=e,j=n.useRef(null),_=(0,l.e)(t,j),C=(0,f.gm)(i),[P=null,E]=(0,d.T)({prop:g,defaultProp:v,onChange:b}),[S,I]=n.useState(!1),k=(0,c.W)(w),D=y(r),N=n.useRef(!1),[T,A]=n.useState(0);return n.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(h,k),()=>e.removeEventListener(h,k)},[k]),(0,p.jsx)(M,{scope:r,orientation:a,dir:C,loop:u,currentTabStopId:P,onItemFocus:n.useCallback(e=>E(e),[E]),onItemShiftTab:n.useCallback(()=>I(!0),[]),onFocusableItemAdd:n.useCallback(()=>A(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>A(e=>e-1),[]),children:(0,p.jsx)(s.WV.div,{tabIndex:S||0===T?-1:0,"data-orientation":a,...R,ref:_,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{N.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!N.current;if(e.target===e.currentTarget&&t&&!S){let t=new CustomEvent(h,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable);O([e.find(e=>e.active),e.find(e=>e.id===P),...e].filter(Boolean).map(e=>e.ref.current),x)}}N.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>I(!1))})})}),C="RovingFocusGroupItem",P=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:l=!1,tabStopId:u,...c}=e,d=(0,i.M)(),f=u||d,h=R(C,r),m=h.currentTabStopId===f,g=y(r),{onFocusableItemAdd:b,onFocusableItemRemove:w}=h;return n.useEffect(()=>{if(a)return b(),()=>w()},[a,b,w]),(0,p.jsx)(v.ItemSlot,{scope:r,id:f,focusable:a,active:l,children:(0,p.jsx)(s.WV.span,{tabIndex:m?0:-1,"data-orientation":h.orientation,...c,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?h.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>h.onItemFocus(f)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){h.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return E[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=h.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>O(r))}})})})});P.displayName=C;var E={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function O(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var S=j,I=P}};