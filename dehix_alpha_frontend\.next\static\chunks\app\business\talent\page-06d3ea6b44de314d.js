(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1942],{87710:function(e,t,s){Promise.resolve().then(s.bind(s,51859))},5891:function(e,t,s){"use strict";s.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s(33480).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},20897:function(e,t,s){"use strict";s.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s(33480).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},13231:function(e,t,s){"use strict";s.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s(33480).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},71935:function(e,t,s){"use strict";s.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s(33480).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},87055:function(e,t,s){"use strict";s.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s(33480).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},27218:function(e,t,s){"use strict";s.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s(33480).Z)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},47390:function(e,t,s){"use strict";s.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},29406:function(e,t,s){"use strict";s.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s(33480).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},60994:function(e,t,s){"use strict";s.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s(33480).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},98960:function(e,t,s){"use strict";s.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s(33480).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},73347:function(e,t,s){"use strict";s.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s(33480).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},10883:function(e,t,s){"use strict";s.d(t,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s(33480).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},51859:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return $}});var l=s(57437),a=s(2265),i=s(64797),n=s(82230),r=s(48185),d=s(29406),c=s(11444),o=s(92513),u=s(39343),h=s(31014),m=s(59772),x=s(54662),p=s(89733),b=s(2128),f=s(15922),v=s(78068),j=s(86074),N=s(20357);let g=m.z.object({label:m.z.string().nonempty("Please select a domain"),skillId:m.z.string().nonempty("Domain ID is required"),experience:m.z.string().nonempty("Please enter your experience").regex(/^\d+$/,"Experience must be a number"),description:m.z.string().nonempty("Please enter description"),visible:m.z.boolean(),status:m.z.string()});var y=e=>{let{skills:t,onSubmitSkill:s}=e,i=(0,c.v9)(e=>e.user),[n,r]=(0,a.useState)(!1),[d,m]=(0,a.useState)(!1),{control:y,handleSubmit:k,formState:{errors:E},reset:w,setValue:I,getValues:C,trigger:S}=(0,u.cI)({resolver:(0,h.F)(g),defaultValues:{skillId:"",label:"",experience:"",description:"",visible:!1,status:"ADDED"}}),A=async e=>{m(!0);try{let l=await f.b.post("/business/hire-dehixtalent",{skillId:e.skillId,skillName:e.label,businessId:i.uid,experience:e.experience,description:e.description,status:e.status,visible:e.visible});if(200===l.status){var t;if(null==l?void 0:null===(t=l.data)||void 0===t?void 0:t.data){let t=l.data.data;s({...e,uid:t._id}),w(),r(!1),(0,v.Am)({title:"Talent Added",description:"The Hire Talent has been successfully added."});let a=parseInt(N.env.NEXT_PUBLIC__APP_HIRE_TALENT_COST||"0",10),i=Number(localStorage.getItem("DHX_CONNECTS"))||0;localStorage.setItem("DHX_CONNECTS",Math.max(0,i-a).toString()),window.dispatchEvent(new Event("connectsUpdated"))}else throw Error("Failed to add hire talen")}}catch(e){console.error("Error submitting skill data",e),w(),(0,v.Am)({variant:"destructive",title:"Error",description:"Failed to add hire talent. Please try again."})}finally{m(!1)}};return(0,l.jsxs)(x.Vq,{open:n,onOpenChange:r,children:[(0,l.jsx)(x.hg,{asChild:!0,children:(0,l.jsxs)(p.z,{onClick:()=>r(!0),className:"w-full sm:w-auto",children:[(0,l.jsx)(o.Z,{className:"mr-2 h-4 w-4"}),"Add Skill"]})}),(0,l.jsxs)(x.cZ,{children:[(0,l.jsxs)(x.fK,{children:[(0,l.jsx)(x.$N,{children:"Add Skill"}),(0,l.jsx)(x.Be,{children:"Select a skill, enter your experience and monthly pay."})]}),(0,l.jsxs)("form",{onSubmit:k(A),children:[(0,l.jsx)("div",{className:"mb-3",children:(0,l.jsx)(u.Qr,{control:y,name:"label",render:e=>{let{field:s}=e;return(0,l.jsxs)(b.Ph,{value:s.value,onValueChange:e=>{let l=t.find(t=>t.label===e);s.onChange(e),I("skillId",(null==l?void 0:l._id)||"")},children:[(0,l.jsx)(b.i4,{children:(0,l.jsx)(b.ki,{placeholder:"Select a skill"})}),(0,l.jsx)(b.Bw,{children:t.map(e=>(0,l.jsx)(b.Ql,{value:e.label,children:e.label},e.label))})]})}})}),E.label&&(0,l.jsx)("p",{className:"text-red-600",children:E.label.message}),(0,l.jsx)("div",{className:"mb-3",children:(0,l.jsx)(u.Qr,{control:y,name:"experience",render:e=>{let{field:t}=e;return(0,l.jsxs)("div",{className:"col-span-3 relative",children:[(0,l.jsx)("input",{type:"number",placeholder:"Experience (years)",min:0,max:50,step:.1,...t,className:"border p-2 rounded mt-0 w-full"}),(0,l.jsx)("span",{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"YEARS"})]})}})}),E.experience&&(0,l.jsx)("p",{className:"text-red-600",children:E.experience.message}),(0,l.jsx)(u.Qr,{control:y,name:"description",render:e=>{let{field:t}=e;return(0,l.jsx)("input",{type:"text",placeholder:"Description",...t,className:"border p-2 rounded mt-2 w-full"})}}),E.description&&(0,l.jsx)("p",{className:"text-red-600",children:E.description.message}),(0,l.jsx)(j.Z,{loading:d,setLoading:m,onSubmit:A,isValidCheck:S,userId:i.uid,buttonText:"Submit",userType:"BUSINESS",requiredConnects:parseInt(N.env.NEXT_PUBLIC__APP_HIRE_TALENT_COST||"0",10),data:C()})]})]})]})},k=s(20357);let E=m.z.object({label:m.z.string().nonempty("Please select a domain"),domainId:m.z.string().nonempty("Domain ID is required"),experience:m.z.string().nonempty("Please enter your experience").regex(/^\d+$/,"Experience must be a number"),description:m.z.string().nonempty("Please enter description"),visible:m.z.boolean(),status:m.z.string()});var w=e=>{let{domains:t,onSubmitDomain:s}=e,i=(0,c.v9)(e=>e.user),[n,r]=(0,a.useState)(!1),[d,m]=(0,a.useState)(!1),{control:N,handleSubmit:g,formState:{errors:y},reset:w,setValue:I,getValues:C,trigger:S}=(0,u.cI)({resolver:(0,h.F)(E),defaultValues:{domainId:"",label:"",experience:"",description:"",visible:!1,status:"ADDED"}}),A=async e=>{m(!0);try{let t=await f.b.post("/business/hire-dehixtalent",{domainId:e.domainId,domainName:e.label,businessId:i.uid,experience:e.experience,description:e.description,status:e.status,visible:e.visible});if(200===t.status){let l=t.data.data;s({...e,uid:l._id}),w(),r(!1),(0,v.Am)({title:"Talent Added",description:"The Talent has been successfully added."});let a=parseInt(k.env.NEXT_PUBLIC__APP_HIRE_TALENT_COST||"0",10),i=Number(localStorage.getItem("DHX_CONNECTS"))||0;localStorage.setItem("DHX_CONNECTS",Math.max(0,i-a).toString()),window.dispatchEvent(new Event("connectsUpdated"))}}catch(e){console.error("Error submitting domain data",e),w(),(0,v.Am)({variant:"destructive",title:"Error",description:"Failed to add talent. Please try again."})}finally{m(!1)}};return(0,l.jsxs)(x.Vq,{open:n,onOpenChange:r,children:[(0,l.jsx)(x.hg,{asChild:!0,children:(0,l.jsxs)(p.z,{onClick:()=>r(!0),className:"w-full sm:w-auto",children:[(0,l.jsx)(o.Z,{className:"mr-2 h-4 w-4"}),"Add Domain"]})}),(0,l.jsxs)(x.cZ,{children:[(0,l.jsxs)(x.fK,{children:[(0,l.jsx)(x.$N,{children:"Add Domain"}),(0,l.jsx)(x.Be,{children:"Select a domain, enter your experience and monthly pay."})]}),(0,l.jsxs)("form",{onSubmit:g(A),children:[(0,l.jsx)("div",{className:"mb-3",children:(0,l.jsx)(u.Qr,{control:N,name:"label",render:e=>{let{field:s}=e;return(0,l.jsxs)(b.Ph,{value:s.value,onValueChange:e=>{let l=t.find(t=>t.label===e);s.onChange(e),I("domainId",(null==l?void 0:l._id)||"")},children:[(0,l.jsx)(b.i4,{children:(0,l.jsx)(b.ki,{placeholder:"Select a domain"})}),(0,l.jsx)(b.Bw,{children:t.map(e=>(0,l.jsx)(b.Ql,{value:e.label,children:e.label},e._id))})]})}})}),y.label&&(0,l.jsx)("p",{className:"text-red-600",children:y.label.message}),(0,l.jsx)("div",{className:"mb-3",children:(0,l.jsx)(u.Qr,{control:N,name:"experience",render:e=>{let{field:t}=e;return(0,l.jsxs)("div",{className:"col-span-3 relative",children:[(0,l.jsx)("input",{type:"number",placeholder:"Experience (years)",min:0,max:50,step:.1,...t,className:"border p-2 rounded mt-0 w-full"}),(0,l.jsx)("span",{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"YEARS"})]})}})}),y.experience&&(0,l.jsx)("p",{className:"text-red-600",children:y.experience.message}),(0,l.jsx)(u.Qr,{control:N,name:"description",render:e=>{let{field:t}=e;return(0,l.jsx)("input",{type:"text",placeholder:"Description",...t,className:"border p-2 rounded mt-2 w-full"})}}),y.description&&(0,l.jsx)("p",{className:"text-red-600",children:y.description.message}),(0,l.jsx)(j.Z,{loading:d,setLoading:m,onSubmit:A,isValidCheck:S,userId:i.uid,buttonText:"Submit",userType:"BUSINESS",requiredConnects:parseInt(k.env.NEXT_PUBLIC__APP_HIRE_TALENT_COST||"0",10),data:C()})]})]})]})},I=s(47304),C=s(60343),S=s(79055),A=s(89859),D=e=>{let{setFilterSkill:t,setFilterDomain:s}=e,[i,n]=(0,a.useState)([]),[o,u]=(0,a.useState)([]),[h,m]=(0,a.useState)([]),[x,p]=(0,a.useState)([]),b=(0,c.v9)(e=>e.user);(0,a.useEffect)(()=>{(async()=>{try{var e,t;let[s,l]=await Promise.all([f.b.get("/skills"),f.b.get("/domain")]);n((null===(e=s.data)||void 0===e?void 0:e.data)||[]),u((null===(t=l.data)||void 0===t?void 0:t.data)||[])}catch(e){console.error("Error fetching skills and domains:",e),(0,v.Am)({variant:"destructive",title:"Error",description:"Failed to load skills and domains. Please try again."})}})()},[]);let j=(0,a.useCallback)(async()=>{try{var e,l,a,i,r;let d=await f.b.get("/skills");if(null==d?void 0:null===(e=d.data)||void 0===e?void 0:e.data)n(d.data.data);else throw Error("Skills response is null or invalid");let c=await f.b.get("/domain");if(null==c?void 0:null===(l=c.data)||void 0===l?void 0:l.data)u(c.data.data);else throw Error("Domains response is null or invalid");if(null==b?void 0:b.uid){let e=await f.b.get("/business/hire-dehixtalent"),l=(null===(a=e.data)||void 0===a?void 0:a.data)||{},d=l.filter(e=>e.skillName&&e.visible).map(e=>({_id:e.skillId,label:e.skillName})),c=l.filter(e=>e.domainName&&e.visible).map(e=>({_id:e.domainId,label:e.domainName}));t(d),s(c);let o=Object.values(l).map(e=>({uid:e._id,label:e.skillName||e.domainName||"N/A",experience:e.experience||"N/A",description:e.description||"N/A",status:e.status,visible:e.visible}));m(o),p(o.map(e=>e.visible));let h=l.filter(e=>e.skillName).map(e=>({_id:e.skillId,label:e.skillName})),x=l.filter(e=>e.domainName).map(e=>({_id:e.domainId,label:e.domainName})),b=await f.b.get("/skills");if(null==b?void 0:null===(i=b.data)||void 0===i?void 0:i.data){let e=b.data.data.filter(e=>!h.some(t=>t._id===e._id));n(e)}else throw Error("Skills response is null or invalid");let v=await f.b.get("/domain");if(null==v?void 0:null===(r=v.data)||void 0===r?void 0:r.data){let e=v.data.data.filter(e=>!x.some(t=>t._id===e._id));u(e)}else throw Error("Domains response is null or invalid")}}catch(e){console.error("Error fetching user data:",e),(0,v.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}},[null==b?void 0:b.uid,t,s]);(0,a.useEffect)(()=>{j()},[j]);let N=async(e,t,s)=>{try{let l=await f.b.patch("/business/hire-dehixtalent/".concat(s),{visible:t});if(200===l.status){let s=[...x];s[e]=t,p(s),await j()}}catch(e){console.error("Error updating visibility:",e),(0,v.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}};return(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsxs)("div",{className:"mb-8 ",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold",children:" Hire Talent "}),(0,l.jsx)("p",{className:"text-gray-400 mt-2",children:"Help us understand the skills and domain you are looking for in potential hires.Enter the required experience and a short description to refine your talent search."})]}),(0,l.jsx)("div",{className:"",children:(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,l.jsxs)("div",{className:"flex space-x-4",children:[(0,l.jsx)(y,{skills:i,onSubmitSkill:e=>{m([...h,{...e,status:"ADDED",visible:!1}]),p([...x,!1])}}),(0,l.jsx)(w,{domains:o,onSubmitDomain:e=>{m([...h,{...e,status:"ADDED",visible:!1}]),p([...x,!1])}})]})}),(0,l.jsx)(r.Zb,{className:"h-[65.4vh] overflow-auto no-scrollbar",children:(0,l.jsxs)(I.iA,{className:"w-full",children:[(0,l.jsx)(I.xD,{className:"sticky top-0 z-10",children:(0,l.jsxs)(I.SC,{children:[(0,l.jsx)(I.ss,{children:"Label"}),(0,l.jsx)(I.ss,{children:"Experience"}),(0,l.jsx)(I.ss,{children:"Description"}),(0,l.jsx)(I.ss,{children:"Status"}),(0,l.jsx)(I.ss,{})]})}),(0,l.jsx)(I.RM,{children:h.length>0?h.map((e,t)=>(0,l.jsxs)(I.SC,{children:[(0,l.jsx)(I.pj,{children:e.label}),(0,l.jsxs)(I.pj,{children:[e.experience," years"]}),(0,l.jsx)(I.pj,{children:e.description}),(0,l.jsx)(I.pj,{children:(0,l.jsx)(S.C,{className:(0,A.S)(e.status),children:e.status.toUpperCase()})}),(0,l.jsx)(I.pj,{children:(0,l.jsx)(C.r,{checked:x[t],onCheckedChange:s=>e.uid?N(t,s,e.uid):console.error("UID missing for item",e)})})]},t)):(0,l.jsx)("tr",{children:(0,l.jsxs)("td",{colSpan:5,className:"text-center py-10",children:[(0,l.jsx)(d.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,l.jsxs)("p",{className:"text-gray-500",children:["No data available.",(0,l.jsx)("br",{})," This feature will be available soon.",(0,l.jsx)("br",{}),"Here you can directly hire freelancer for different roles."]})]})})})]})})]})})]})};/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let T=(0,s(33480).Z)("Expand",[["path",{d:"m21 21-6-6m6 6v-4.8m0 4.8h-4.8",key:"1c15vz"}],["path",{d:"M3 16.2V21m0 0h4.8M3 21l6-6",key:"1fsnz2"}],["path",{d:"M21 7.8V3m0 0h-4.8M21 3l-6 6",key:"hawz9i"}],["path",{d:"M3 7.8V3m0 0h4.8M3 3l6 6",key:"u9ee12"}]]);var P=s(87055),_=s(27218),Z=s(60994),L=s(3274),V=s(87138),R=s(35265),O=s(89736),z=s(80420);function M(e){let{isLoading:t,hasMore:s,next:i,threshold:n=1,root:r=null,rootMargin:d="0px",reverse:c,children:o}=e,u=a.useRef(),h=a.useCallback(e=>{let l=n;(n<0||n>1)&&(console.warn("threshold should be between 0 and 1. You are exceed the range. will use default value: 1"),l=1),!t&&(u.current&&u.current.disconnect(),e&&(u.current=new IntersectionObserver(e=>{e[0].isIntersecting&&s&&i()},{threshold:l,root:r,rootMargin:d}),u.current.observe(e)))},[s,t,i,n,r,d]),m=a.useMemo(()=>a.Children.toArray(o),[o]);return(0,l.jsx)(l.Fragment,{children:m.map((e,t)=>{if(!a.isValidElement(e))return e;let s=c?0===t:t===m.length-1;return a.cloneElement(e,{ref:s?h:null})})})}var F=s(34859),H=s(55936),B=s(97540),U=s(74697),q=e=>{let{skillDomainData:t=[],currSkills:s=[],handleAddSkill:a,handleDeleteSkill:i,handleAddToLobby:n,talent:r,setTmpSkill:d,tmpSkill:c,open:u,setOpen:h,isLoading:m}=e;return(0,l.jsx)(x.Vq,{open:u,onOpenChange:h,children:(0,l.jsxs)(x.cZ,{className:"max-w-md",children:[(0,l.jsx)(x.fK,{children:(0,l.jsx)(x.$N,{children:"Add Skills to Lobby"})}),(0,l.jsxs)("div",{className:" mt-2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsxs)(b.Ph,{value:c||"",onValueChange:e=>d(e),children:[(0,l.jsx)(b.i4,{className:"w-full",children:(0,l.jsx)(b.ki,{placeholder:"Select skill",children:c||null})}),(0,l.jsx)(b.Bw,{children:t.filter(e=>!s.some(t=>t.name===e.label)).map((e,t)=>(0,l.jsx)(b.Ql,{value:e.label,children:e.label},t))})]}),(0,l.jsx)(p.z,{variant:"outline",size:"icon",onClick:()=>{a(),d("")},children:(0,l.jsx)(o.Z,{className:"h-4 w-4"})})]}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2 mt-4",children:s.map((e,t)=>(0,l.jsxs)(S.C,{className:"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1",children:[e.name,(0,l.jsx)("button",{type:"button",onClick:()=>i(e.name),className:"ml-2 text-red-500 hover:text-red-700",children:(0,l.jsx)(U.Z,{className:"h-4 w-4"})})]},t))})]}),(0,l.jsx)(x.cN,{className:"mt-4",children:(0,l.jsx)(p.z,{onClick:()=>{n(r.freelancer_id)},className:"w-full text-sm py-1 px-2 text-black rounded-md",type:"submit",children:m?(0,l.jsx)(L.Z,{className:"animate-spin"}):"Save"})})]})})};let Q=["left"];var G=e=>{let{skillFilter:t,domainFilter:s,skillDomainFormProps:i}=e,[n,d]=(0,a.useState)([]),[o,u]=(0,a.useState)([]),h=(0,a.useRef)(0),[m,x]=(0,a.useState)(!1),[b,j]=(0,a.useState)(!0),N=(0,a.useRef)(!1),[g,y]=(0,a.useState)([]),[k,E]=(0,a.useState)([]),w=(0,c.v9)(e=>e.user),[I,C]=(0,a.useState)([]),[,A]=(0,a.useState)([]),[D]=(0,a.useState)(new Set),[U,G]=(0,a.useState)(),[X,$]=(0,a.useState)([]),[J,W]=(0,a.useState)(""),[K,Y]=(0,a.useState)(!1),[ee,et]=(0,a.useState)(!1),es=()=>{console.log(J),J&&!X.some(e=>e.name===J)&&($([...X,{name:J,level:"",experience:"",interviewStatus:B.sB.PENDING,interviewInfo:"",interviewerRating:0}]),W(""))};(0,a.useEffect)(()=>{(async()=>{try{var e,t;let[s,l]=await Promise.all([f.b.get("/skills"),f.b.get("/domain")]);y((null===(e=s.data)||void 0===e?void 0:e.data)||[]),E((null===(t=l.data)||void 0===t?void 0:t.data)||[])}catch(e){console.error("Error fetching skills and domains:",e),(0,v.Am)({variant:"destructive",title:"Error",description:"Failed to load skills and domains. Please try again."})}})()},[]);let el=(0,a.useCallback)(async()=>{try{var e,t,s,l,a;let n=await f.b.get("/skills");if(null==n?void 0:null===(e=n.data)||void 0===e?void 0:e.data)y(n.data.data);else throw Error("Skills response is null or invalid");let r=await f.b.get("/domain");if(null==r?void 0:null===(t=r.data)||void 0===t?void 0:t.data)E(r.data.data);else throw Error("Domains response is null or invalid");if(null==w?void 0:w.uid){let e=await f.b.get("/business/hire-dehixtalent"),t=(null===(s=e.data)||void 0===s?void 0:s.data)||{},n=t.filter(e=>e.skillName&&e.visible).map(e=>({_id:e.skillId,label:e.skillName})),r=t.filter(e=>e.domainName&&e.visible).map(e=>({_id:e.domainId,label:e.domainName}));null==i||i.skillFilter(n),null==i||i.domainFilter(r);let d=Object.values(t).map(e=>({uid:e._id,label:e.skillName||e.domainName||"N/A",experience:e.experience||"N/A",description:e.description||"N/A",status:e.status,visible:e.visible,talentId:e.skillId||e.domainId}));console.log(d),C(d),A(d.map(e=>e.visible));let c=t.filter(e=>e.skillName).map(e=>({_id:e.skillId,label:e.skillName})),o=t.filter(e=>e.domainName).map(e=>({_id:e.domainId,label:e.domainName})),u=await f.b.get("/skills");if(null==u?void 0:null===(l=u.data)||void 0===l?void 0:l.data){let e=u.data.data.filter(e=>!c.some(t=>t._id===e._id));y(e)}else throw Error("Skills response is null or invalid");let h=await f.b.get("/domain");if(null==h?void 0:null===(a=h.data)||void 0===a?void 0:a.data){let e=h.data.data.filter(e=>!o.some(t=>t._id===e._id));E(e)}else throw Error("Domains response is null or invalid")}}catch(e){console.error("Error fetching user data:",e),e.response&&404===e.response.status||(0,v.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}},[null==w?void 0:w.uid,i]);(0,a.useEffect)(()=>{el()},[el]);let ea=e=>{$(X.filter(t=>t.name!==e))},ei=(0,a.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:h.current,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!N.current)try{var s,l;N.current=!0,x(!0);let a=await f.b.get("freelancer/dehixtalent",{params:{limit:F.Dz.BATCH,skip:e}}),i=(null==a?void 0:null===(s=a.data)||void 0===s?void 0:s.data)||[];if(i.length<F.Dz.BATCH&&j(!1),null==a?void 0:null===(l=a.data)||void 0===l?void 0:l.data)u(e=>t?i:[...e,...i]),h.current=t?F.Dz.BATCH:h.current+F.Dz.BATCH;else throw Error("Fail to fetch data")}catch(e){console.error("Error fetching talent data",e),e.response&&404===e.response.status?j(!1):(0,v.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}finally{x(!1),N.current=!1}},[]),en=(0,a.useCallback)(()=>{u([]),h.current=0,j(!0),ei(0,!0)},[ei]);(0,a.useEffect)(()=>{en()},[en]),(0,a.useEffect)(()=>{d(o.filter(e=>"all"==t&&"all"==s||"all"==t&&s==e.dehixTalent.domainName||t==e.dehixTalent.skillName&&"all"==s||t==e.dehixTalent.skillName||s==e.dehixTalent.domainName))},[t,s,o]);let er=async e=>{let t=[],s=[];if(X.forEach(e=>{let l=I.find(t=>t.label===e.name);(null==l?void 0:l.talentId)&&(null==l?void 0:l.uid)&&(t.push(l.talentId),s.push(l.uid))}),0===t.length||0===s.length){(0,v.Am)({variant:"destructive",title:"No Skills Selected",description:"Please add some skills before adding to lobby."});return}et(!0);try{let l=await f.b.put("business/hire-dehixtalent/add_into_lobby",{freelancerId:e,dehixTalentId:t,hireDehixTalent_id:s});200===l.status&&((0,v.Am)({title:"Success",description:"Freelancer added to lobby"}),$([]))}catch(e){(0,v.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}finally{et(!1)}};return(0,l.jsxs)("div",{className:"flex flex-wrap mt-4 justify-center gap-4",children:[n.map(e=>{let t=e.dehixTalent,s=e.education,a=e.projects,i=t.skillName?"Skill":"Domain",n=t.skillName||t.domainName||"N/A",d=D.has(t._id);return console.log(e.freelancer_id),(0,l.jsxs)(r.Zb,{className:"w-full sm:w-[350px] lg:w-[450px]",children:[(0,l.jsxs)(r.Ol,{className:"flex flex-row items-center gap-4",children:[(0,l.jsxs)(z.Avatar,{className:"h-14 w-14",children:[(0,l.jsx)(z.AvatarImage,{src:e.profilePic||"/default-avatar.png"}),(0,l.jsx)(z.AvatarFallback,{children:"JD"})]}),(0,l.jsxs)("div",{className:"flex flex-col",children:[(0,l.jsx)(r.ll,{children:e.Name||"Unknown"}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:e.userName})]})]}),(0,l.jsx)(r.aY,{children:(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsxs)("div",{className:"flex justify-between mb-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{className:"text-sm font-semibold",children:i}),(0,l.jsx)(S.C,{children:n})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{className:"text-sm font-semibold",children:"Experience"}),(0,l.jsxs)(S.C,{children:[t.experience," years"]})]})]}),(0,l.jsxs)("div",{className:"flex justify-between mb-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{className:"text-sm font-semibold",children:"Monthly Pay"}),(0,l.jsxs)(S.C,{children:["$",t.monthlyPay]})]}),d&&(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{className:"text-sm font-semibold",children:"Status"}),(0,l.jsx)(S.C,{variant:"default",children:"Invited"})]})]}),(0,l.jsx)("div",{className:"py-4",children:Q.map(i=>(0,l.jsxs)(H.yo,{children:[(0,l.jsx)(H.aM,{asChild:!0,children:(0,l.jsx)(p.z,{className:"w-full text-sm  rounded-md",children:"View"})}),(0,l.jsxs)(H.ue,{side:i,className:"overflow-y-auto no-scrollbar max-h-[100vh]",children:[(0,l.jsx)(H.Tu,{children:(0,l.jsxs)(H.bC,{className:"flex items-center justify-between text-lg font-bold py-4",children:[(0,l.jsx)("span",{className:"text-center flex-1",children:"View Talent Details"}),(0,l.jsxs)(O.u,{children:[(0,l.jsx)(O.aJ,{asChild:!0,children:(0,l.jsx)(V.default,{href:"/business/freelancerProfile/".concat(e.freelancer_id),passHref:!0,children:(0,l.jsx)(T,{className:"w-6 h-6 cursor-pointer text-gray-600 "})})}),(0,l.jsx)(O._v,{side:"top",children:"Expand"})]})]})}),(0,l.jsx)("div",{className:"grid gap-4 py-2",children:(0,l.jsx)("div",{className:"w-full text-center",children:(0,l.jsxs)("div",{className:"items-center",children:[(0,l.jsxs)(z.Avatar,{className:"h-20 w-20 mx-auto mb-4 rounded-full border-4 border-white hover:border-white transition-all duration-300",children:[(0,l.jsx)(z.AvatarImage,{src:e.profilePic||"/default-avatar.png"}),(0,l.jsx)(z.AvatarFallback,{children:"Unable to load"})]}),(0,l.jsxs)("div",{className:"text-lg font-bold",children:[" ",e.Name]}),(0,l.jsxs)("div",{className:"flex items-center justify-center gap-4 mt-4",children:[(0,l.jsx)("a",{href:e.Github||"#",target:e.Github?"_blank":"_self",rel:"noopener noreferrer",className:"flex items-center gap-2 transition-all ".concat(e.Github?"text-blue-500 hover:text-blue-700":"text-gray-500 cursor-default"),children:(0,l.jsx)(P.Z,{className:"w-5 h-5 ".concat(e.Github?"text-blue-500":"text-gray-500")})}),(0,l.jsx)("a",{href:e.LinkedIn||"#",target:e.LinkedIn?"_blank":"_self",rel:"noopener noreferrer",className:"flex items-center gap-2 transition-all ".concat(e.LinkedIn?"text-blue-500 hover:text-blue-700":"text-gray-500 cursor-default"),children:(0,l.jsx)(_.Z,{className:"w-5 h-5 ".concat(e.LinkedIn?"text-blue-500":"text-gray-500")})})]})]})})}),(0,l.jsx)("table",{className:"min-w-full table-auto border-collapse ",children:(0,l.jsx)("tbody",{children:(0,l.jsxs)("tr",{children:[(0,l.jsx)("td",{className:"border-b px-4 py-2 font-medium",children:"Username"}),(0,l.jsx)("td",{className:"border-b px-4 py-2",children:e.userName||"N/A"})]})})}),(0,l.jsxs)(R.UQ,{type:"multiple",className:"w-full",children:[(0,l.jsxs)(R.Qd,{value:"education",children:[(0,l.jsx)(R.o4,{className:"w-full flex justify-between px-4 py-2 !no-underline focus:ring-0 focus:outline-none",children:"Education"}),(0,l.jsx)(R.vF,{className:"p-4 transition-all duration-300",children:s&&Object.values(s).length>0?Object.values(s).map(e=>(0,l.jsxs)("div",{className:"mb-2 p-2 border border-gray-300 rounded-lg",children:[(0,l.jsx)("p",{className:"text-sm font-semibold",children:e.degree}),(0,l.jsx)("p",{className:"text-xs text-gray-600",children:e.universityName}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:e.fieldOfStudy}),(0,l.jsxs)("p",{className:"text-xs text-gray-500",children:[new Date(e.startDate).toLocaleDateString()," ","-"," ",new Date(e.endDate).toLocaleDateString()]}),(0,l.jsxs)("p",{className:"text-xs text-gray-700",children:["Grade: ",e.grade]})]},e._id)):"No education details available."})]}),(0,l.jsxs)(R.Qd,{value:"projects",children:[(0,l.jsx)(R.o4,{className:"w-full flex justify-between px-4 py-2 !no-underline focus:ring-0 focus:outline-none",children:"Projects"}),(0,l.jsx)(R.vF,{className:"p-4 transition-all duration-300",children:a&&Object.values(a).length>0?Object.values(a).map(e=>(0,l.jsxs)("div",{className:"mb-2 p-2 border border-gray-300 rounded-lg",children:[(0,l.jsx)("p",{className:"text-sm font-semibold",children:e.projectName}),(0,l.jsxs)("p",{className:"text-xs text-gray-600",children:["Role: ",e.role]}),(0,l.jsxs)("p",{className:"text-xs text-gray-500",children:["Tech Used:"," ",e.techUsed.length>0?e.techUsed.join(", "):"N/A"]}),e.githubLink&&(0,l.jsxs)("a",{href:e.githubLink,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-xs text-blue-500 hover:underline",children:[(0,l.jsx)(P.Z,{className:"w-4 h-4"}),"View on GitHub"]})]},e._id)):(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"No projects available."})})]}),(0,l.jsxs)(R.Qd,{value:"skills",children:[(0,l.jsx)(R.o4,{className:"w-full flex justify-between px-4 py-2 !no-underline focus:ring-0 focus:outline-none",children:"Skills"}),(0,l.jsx)(R.vF,{className:"p-4 transition-all duration-300",children:t.skillName?t.skillName:"N/A"})]}),(0,l.jsxs)(R.Qd,{value:"domain",children:[(0,l.jsx)(R.o4,{className:"w-full flex justify-between px-4 py-2 !no-underline focus:ring-0 focus:outline-none",children:"Domain"}),(0,l.jsx)(R.vF,{className:"p-4 transition-all duration-300",children:t.domainName?t.domainName:"N/A"})]}),(0,l.jsxs)(R.Qd,{value:"experience",children:[(0,l.jsx)(R.o4,{className:"w-full flex justify-between px-4 py-2 !no-underline focus:ring-0 focus:outline-none",children:"Experience"}),(0,l.jsx)(R.vF,{className:"p-4 transition-all duration-300",children:t.experience?"".concat(t.experience," years"):"N/A"})]})]}),(0,l.jsxs)(p.z,{onClick:()=>{Y(!0),G(e)},className:"w-full mt-4 ".concat(d?"bg-blue-600 hover:bg-blue-700":"bg-primary hover:bg-primary/90"),children:[(0,l.jsx)(Z.Z,{className:"mr-2 h-4 w-4"}),"Add to Lobby"]})]})]},i))}),(0,l.jsxs)(p.z,{onClick:()=>{Y(!0),G(e)},className:"w-full ".concat(d?"bg-blue-600 hover:bg-blue-700":"bg-primary hover:bg-primary/90"),children:[(0,l.jsx)(Z.Z,{className:"mr-2 h-4 w-4"}),"Add to Lobby"]})]})}),U&&(0,l.jsx)(q,{skillDomainData:I,currSkills:X,handleAddSkill:es,handleDeleteSkill:ea,handleAddToLobby:er,talent:U,tmpSkill:J,setTmpSkill:W,open:K,setOpen:Y,isLoading:ee})]},t._id)}),(0,l.jsx)(M,{hasMore:b,isLoading:m,next:ei,threshold:1,children:m&&(0,l.jsx)(L.Z,{className:"my-4 h-8 w-8 animate-spin"})})]})},X=s(62688);function $(){let[e,t]=(0,a.useState)("all"),[s,d]=(0,a.useState)("all"),[c]=(0,a.useState)(),[o,u]=(0,a.useState)([]),[h,m]=(0,a.useState)([]);return(0,l.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40 overflow-auto",children:[(0,l.jsx)(i.Z,{menuItemsTop:n.yn,menuItemsBottom:n.$C,active:"Dehix Talent"}),(0,l.jsxs)("div",{className:"flex flex-col sm:gap-8  sm:py-0 sm:pl-14",children:[(0,l.jsx)(X.Z,{menuItemsTop:n.yn,menuItemsBottom:n.$C,activeMenu:"Dehix Talent",breadcrumbItems:[{label:"Business",link:"/dashboard/business"},{label:"HireTalent",link:"#"}]}),(0,l.jsxs)("main",{className:"flex-1 gap-4 p-4 sm:px-6 sm:py-0 md:gap-8 lg:grid lg:grid-cols-3 lg:items-start xl:grid-cols-3",children:[(0,l.jsx)("div",{className:"space-y-6 lg:col-span-2",children:(0,l.jsx)(D,{setFilterSkill:u,setFilterDomain:m})}),(0,l.jsxs)("div",{className:"space-y-6 lg:mt-0 mt-6",children:[(0,l.jsx)(r.ll,{className:"group flex items-center gap-2 text-2xl",children:"Talent"}),(0,l.jsxs)("div",{className:"flex space-x-4",children:[(0,l.jsxs)(b.Ph,{onValueChange:t,value:e,children:[(0,l.jsx)(b.i4,{className:"w-full",children:(0,l.jsx)(b.ki,{placeholder:"Select Skill"})}),(0,l.jsxs)(b.Bw,{children:[(0,l.jsx)(b.Ql,{value:"all",children:"All Skills"}),null==o?void 0:o.map(e=>(0,l.jsx)(b.Ql,{value:e.label,children:e.label},e._id))]})]}),(0,l.jsxs)(b.Ph,{onValueChange:d,value:s,children:[(0,l.jsx)(b.i4,{className:"w-full",children:(0,l.jsx)(b.ki,{placeholder:"Select Domain"})}),(0,l.jsxs)(b.Bw,{children:[(0,l.jsx)(b.Ql,{value:"all",children:"All Domains"}),null==h?void 0:h.map(e=>(0,l.jsx)(b.Ql,{value:e.label,children:e.label},e._id))]})]})]}),(0,l.jsx)("div",{className:"lg:h-[75vh] h-[59vh] rounded-lg  overflow-y-scroll no-scrollbar",children:(0,l.jsx)(G,{skillFilter:e,domainFilter:s,skillDomainFormProps:c})})]})]})]})]})}},86074:function(e,t,s){"use strict";s.d(t,{Z:function(){return o}});var l=s(57437),a=s(2265),i=s(78068),n=s(15922),r=s(89733),d=s(54662),c=s(77209);function o(e){let{loading:t,setLoading:s,onSubmit:o,isValidCheck:u,userId:h,buttonText:m,userType:x,requiredConnects:p,data:b}=e,[f,v]=(0,a.useState)(!1),[j,N]=(0,a.useState)(!1),g=parseInt(localStorage.getItem("DHX_CONNECTS")||"0",10),y=async()=>{try{await n.b.post("/token-request",{userId:h,userType:x,amount:"100",status:"PENDING",dateTime:new Date().toISOString()}),(0,i.Am)({title:"Success!",description:"Request to add connects has been sent.",duration:3e3});let e={userId:h,amount:100,status:"PENDING",dateTime:new Date().toISOString()};window.dispatchEvent(new CustomEvent("newConnectRequest",{detail:e}))}catch(e){console.error("Error requesting more connects:",e.response),(0,i.Am)({variant:"destructive",title:"Error!",description:"Failed to request connects. Try again!",duration:3e3})}},k=async()=>{let e=await u();if(console.log(e),e){if(console.log(p),g<p){N(!0),v(!0);return}N(!1),v(!0)}},E=async()=>{if(console.log(j),!j){s(!0);try{b?await o(b):await o(),v(!1)}catch(e){console.error("Error deducting connects:",e),alert("Failed to deduct connects. Try again!")}finally{s(!1)}}};return(0,l.jsxs)("div",{children:[(0,l.jsx)(r.z,{type:"button",className:"lg:col-span-2 w-full xl:col-span-2 mt-4",disabled:t,onClick:k,children:t?"Loading...":m}),(0,l.jsx)(d.Vq,{open:f,onOpenChange:v,children:(0,l.jsx)(d.cZ,{children:j?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(d.$N,{children:"Insufficient Connects"}),(0,l.jsxs)(d.Be,{children:["You don't have enough connects to create a project.",(0,l.jsx)("br",{}),"Please"," ",(0,l.jsx)("span",{className:"text-blue-600 font-bold cursor-pointer",onClick:y,children:"Request Connects"})," ","to proceed."]}),(0,l.jsx)(d.cN,{children:(0,l.jsx)(r.z,{variant:"outline",onClick:()=>v(!1),children:"Close"})})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(d.$N,{children:"Confirm Deduction"}),(0,l.jsx)(c.I,{type:"text",value:p,disabled:!0}),(0,l.jsxs)(d.Be,{children:["Creating this project will deduct"," ",(0,l.jsxs)("span",{className:"font-extrabold",children:[" ",p," connects"]}),". Do you want to proceed?"]}),(0,l.jsxs)(d.cN,{children:[(0,l.jsx)(r.z,{variant:"outline",onClick:()=>v(!1),children:"Cancel"}),(0,l.jsx)(r.z,{onClick:E,disabled:t,children:t?"Processing...":"Confirm"})]})]})})})]})}},60343:function(e,t,s){"use strict";s.d(t,{r:function(){return r}});var l=s(57437),a=s(2265),i=s(9646),n=s(49354);let r=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,l.jsx)(i.fC,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",s),...a,ref:t,children:(0,l.jsx)(i.bU,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});r.displayName=i.fC.displayName},82230:function(e,t,s){"use strict";s.d(t,{$C:function(){return f},Ne:function(){return v},yn:function(){return b}});var l=s(57437),a=s(11005),i=s(98960),n=s(38133),r=s(20897),d=s(13231),c=s(71935),o=s(47390),u=s(73347),h=s(24258),m=s(5891),x=s(10883),p=s(66648);let b=[{href:"#",icon:(0,l.jsx)(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,l.jsx)(a.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:(0,l.jsx)(i.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:(0,l.jsx)(n.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:(0,l.jsx)(n.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:(0,l.jsx)(r.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:(0,l.jsx)(d.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:(0,l.jsx)(c.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:(0,l.jsx)(o.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,l.jsx)(u.Z,{className:"h-5 w-5"}),label:"Notes"}],f=[{href:"/business/settings/business-info",icon:(0,l.jsx)(h.Z,{className:"h-5 w-5"}),label:"Settings"}],v=[{href:"#",icon:(0,l.jsx)(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,l.jsx)(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:(0,l.jsx)(u.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:(0,l.jsx)(m.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:(0,l.jsx)(x.Z,{className:"h-5 w-5"}),label:"Trash"}]},34859:function(e,t,s){"use strict";var l,a,i,n,r,d,c,o,u,h,m,x,p,b,f,v,j,N,g,y,k,E;s.d(t,{Dy:function(){return h},Dz:function(){return w}});let w={BATCH:3};(x=l||(l={})).PROJECT_HIRING="PROJECT_HIRING",x.SKILL_INTERVIEW="SKILL_INTERVIEW",x.DOMAIN_INTERVIEW="DOMAIN_INTERVIEW",x.TALENT_INTERVIEW="TALENT_INTERVIEW",(p=a||(a={})).ADDED="Added",p.APPROVED="Approved",p.CLOSED="Closed",p.COMPLETED="Completed",(b=i||(i={})).ACTIVE="Active",b.IN_ACTIVE="Inactive",b.NOT_VERIFIED="Not Verified",(f=n||(n={})).BUSINESS="Business",f.FREELANCER="Freelancer",f.BOTH="Both",(v=r||(r={})).ACTIVE="Active",v.IN_ACTIVE="Inactive",(j=d||(d={})).APPLIED="APPLIED",j.NOT_APPLIED="NOT_APPLIED",j.APPROVED="APPROVED",j.FAILED="FAILED",j.STOPPED="STOPPED",j.REAPPLIED="REAPPLIED",(N=c||(c={})).PENDING="Pending",N.ACCEPTED="Accepted",N.REJECTED="Rejected",N.PANEL="Panel",N.INTERVIEW="Interview",(g=o||(o={})).ACTIVE="ACTIVE",g.INACTIVE="INACTIVE",g.ARCHIVED="ARCHIVED",(y=u||(u={})).ACTIVE="Active",y.PENDING="Pending",y.INACTIVE="Inactive",y.CLOSED="Closed",(k=h||(h={})).FREELANCER="FREELANCER",k.ADMIN="ADMIN",k.BUSINESS="BUSINESS",(E=m||(m={})).CREATED="Created",E.CLOSED="Closed",E.ACTIVE="Active"},97540:function(e,t,s){"use strict";var l,a,i,n,r,d;s.d(t,{cd:function(){return l},d8:function(){return c},kJ:function(){return a},sB:function(){return i}}),(n=l||(l={})).Mastery="Mastery",n.Proficient="Proficient",n.Beginner="Beginner",(r=a||(a={})).ACTIVE="Active",r.PENDING="Pending",r.REJECTED="Rejected",r.COMPLETED="Completed",(d=i||(i={})).ACTIVE="ACTIVE",d.PENDING="PENDING",d.REJECTED="REJECTED",d.COMPLETED="COMPLETED";let c={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},9646:function(e,t,s){"use strict";s.d(t,{bU:function(){return k},fC:function(){return y}});var l=s(2265),a=s(78149),i=s(1584),n=s(98324),r=s(91715),d=s(47250),c=s(75238),o=s(18676),u=s(57437),h="Switch",[m,x]=(0,n.b)(h),[p,b]=m(h),f=l.forwardRef((e,t)=>{let{__scopeSwitch:s,name:n,checked:d,defaultChecked:c,required:h,disabled:m,value:x="on",onCheckedChange:b,...f}=e,[v,j]=l.useState(null),y=(0,i.e)(t,e=>j(e)),k=l.useRef(!1),E=!v||!!v.closest("form"),[w=!1,I]=(0,r.T)({prop:d,defaultProp:c,onChange:b});return(0,u.jsxs)(p,{scope:s,checked:w,disabled:m,children:[(0,u.jsx)(o.WV.button,{type:"button",role:"switch","aria-checked":w,"aria-required":h,"data-state":g(w),"data-disabled":m?"":void 0,disabled:m,value:x,...f,ref:y,onClick:(0,a.M)(e.onClick,e=>{I(e=>!e),E&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),E&&(0,u.jsx)(N,{control:v,bubbles:!k.current,name:n,value:x,checked:w,required:h,disabled:m,style:{transform:"translateX(-100%)"}})]})});f.displayName=h;var v="SwitchThumb",j=l.forwardRef((e,t)=>{let{__scopeSwitch:s,...l}=e,a=b(v,s);return(0,u.jsx)(o.WV.span,{"data-state":g(a.checked),"data-disabled":a.disabled?"":void 0,...l,ref:t})});j.displayName=v;var N=e=>{let{control:t,checked:s,bubbles:a=!0,...i}=e,n=l.useRef(null),r=(0,d.D)(s),o=(0,c.t)(t);return l.useEffect(()=>{let e=n.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(r!==s&&t){let l=new Event("click",{bubbles:a});t.call(e,s),e.dispatchEvent(l)}},[r,s,a]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...i,tabIndex:-1,ref:n,style:{...e.style,...o,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function g(e){return e?"checked":"unchecked"}var y=f,k=j}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,1374,2455,9726,2688,2971,7023,1744],function(){return e(e.s=87710)}),_N_E=e.O()}]);