(()=>{var e={};e.id=2331,e.ids=[2331],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},20692:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>l}),s(13221),s(54302),s(12523);var r=s(23191),a=s(88716),i=s(37922),n=s.n(i),o=s(95231),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);s.d(t,c);let l=["",{children:["dashboard",{children:["business",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,13221)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\dashboard\\business\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\dashboard\\business\\page.tsx"],m="/dashboard/business/page",p={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/business/page",pathname:"/dashboard/business",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},8256:(e,t,s)=>{Promise.resolve().then(s.bind(s,33790))},40900:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},12070:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},57248:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("CalendarX2",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M21 13V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h8",key:"3spt84"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"m17 22 5-5",key:"1k6ppv"}],["path",{d:"m17 17 5 5",key:"p7ous7"}]])},66307:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69669:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},48998:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},40617:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},23015:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},60763:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},57671:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},69515:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},98091:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},33790:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(10326),a=s(90434),i=s(30361),n=s(48998),o=s(23015),c=s(57248),l=s(25842),d=s(17577),m=s(92166),p=s(91664),h=s(29752),u=s(47548),x=s(78062),g=s(97265),f=s(21486),b=s(46319);s(6260);var j=s(72787),v=s(39958),y=s(40588);function N(){(0,l.v9)(e=>e.user);let[e,t]=(0,d.useState)([]),s=e.filter(e=>e.status==v.sB.COMPLETED),N=e.filter(e=>e.status!==v.sB.COMPLETED);return(0,r.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[r.jsx(m.Z,{menuItemsTop:b.yn,menuItemsBottom:b.$C,active:"Dashboard"}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[r.jsx(y.Z,{menuItemsTop:b.yn,menuItemsBottom:b.$C,activeMenu:"Dashboard",breadcrumbItems:[{label:"Business",link:"#"}]}),(0,r.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8 lg:grid-cols-3 xl:grid-cols-3",children:[(0,r.jsxs)("div",{className:"grid auto-rows-max items-start gap-4 md:gap-8 lg:col-span-2",children:[(0,r.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-2 xl:grid-cols-4",children:[(0,r.jsxs)(h.Zb,{className:"sm:col-span-2","x-chunk":"dashboard-05-chunk-0",children:[(0,r.jsxs)(h.Ol,{className:"pb-3",children:[r.jsx(h.ll,{children:"Your Projects"}),r.jsx(h.SZ,{className:"max-w-lg text-balance leading-relaxed",children:"Introducing Our Dynamic Projects Dashboard for Seamless Management and Insightful Analysis."})]}),r.jsx(h.eW,{children:r.jsx(a.default,{href:"/business/add-project",passHref:!0,children:(0,r.jsxs)(p.z,{className:"w-full",children:[" ","Create New Project"]})})})]}),r.jsx(g.Z,{title:"Completed Projects",value:s.length,icon:r.jsx(i.Z,{className:"h-6 w-6 text-success"}),additionalInfo:"Project stats will be here"}),r.jsx(g.Z,{title:"Pending Projects",value:N.length,icon:r.jsx(n.Z,{className:"h-6 w-6 text-warning"}),additionalInfo:"Pending project stats will be here"})]}),r.jsx(x.Separator,{className:"my-1"}),(0,r.jsxs)("h2",{className:"scroll-m-20 text-3xl font-semibold tracking-tight transition-colors first:mt-0",children:["Current Projects ",`(${N.length})`]}),r.jsx("div",{className:"flex gap-4 overflow-x-scroll no-scrollbar pb-8 pt-5",children:(0,r.jsxs)(u.lr,{className:"w-full relative",children:[r.jsx(u.KI,{className:"flex mt-3 gap-4",children:N.length>0?N.map((e,t)=>r.jsx(u.d$,{className:"md:basis-1/2 lg:basis-1/2",children:r.jsx(f.t,{cardClassName:"min-w-[45%]",project:e},t)},t)):(0,r.jsxs)("div",{className:"text-center py-10 w-[100%] ",children:[r.jsx(o.Z,{className:"mx-auto text-gray-500",size:"100"}),r.jsx("p",{className:"text-gray-500",children:"No projects available"})]})}),N.length>2&&r.jsx(r.Fragment,{children:(0,r.jsxs)("div",{className:"flex",children:[r.jsx(u.am,{className:"absolute left-0 -top-1 transform -translate-y-1/2 p-2 shadow-md transition-colors",children:"Previous"}),r.jsx(u.Pz,{className:"absolute right-0 -top-1 transform -translate-y-1/2 p-2 shadow-md transition-colors",children:"Next"})]})})]})}),r.jsx(x.Separator,{className:"my-1"}),(0,r.jsxs)("h2",{className:"scroll-m-20 text-3xl font-semibold tracking-tight transition-colors first:mt-0",children:["Completed Projects ",`(${s.length})`]}),r.jsx("div",{className:"flex relative gap-4 overflow-x-scroll no-scrollbar pb-8 pt-5",children:(0,r.jsxs)(u.lr,{className:"w-full relative pt-9",children:[r.jsx(u.KI,{className:"flex gap-4",children:s.length>0?s.map((e,t)=>r.jsx(u.d$,{className:"md:basis-1/2 lg:basis-1/2",children:r.jsx(f.t,{cardClassName:"min-w-full",project:e})},t)):(0,r.jsxs)("div",{className:"text-center py-10 w-full",children:[r.jsx(o.Z,{className:"mx-auto text-gray-500",size:"100"}),r.jsx("p",{className:"text-gray-500",children:"No projects available"})]})}),s.length>2&&r.jsx(r.Fragment,{children:(0,r.jsxs)("div",{className:"flex",children:[r.jsx(u.am,{className:"absolute left-0 top-1 transform -translate-y-1/2 p-2 shadow-md transition-colors",children:"Previous"}),r.jsx(u.Pz,{className:"absolute right-0 top-1 transform -translate-y-1/2 p-2 shadow-md transition-colors",children:"Next"})]})})]})})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx(h.ll,{className:"group flex items-center gap-2 text-2xl",children:"Interviews"}),j?.freelancersampleInterview?(0,r.jsxs)("div",{className:"text-center py-10",children:[r.jsx(c.Z,{className:"mx-auto mb-2 text-gray-500",size:"100"}),r.jsx("p",{className:"text-gray-500",children:"No interviews scheduled"})]}):r.jsx(r.Fragment,{})]})]})]})]})}s(56627)},21486:(e,t,s)=>{"use strict";s.d(t,{t:()=>h});var r=s(10326),a=s(90434),i=s(60763),n=s(51223),o=s(91664),c=s(29752),l=s(38443),d=s(71064),m=s(58285),p=s(39958);function h({cardClassName:e,project:t,type:s=m.Dy.BUSINESS,...h}){let{text:u,className:x}=(0,d.S)(t.status);return(0,r.jsxs)(c.Zb,{className:(0,n.cn)("flex flex-col h-[400px]",e),...h,children:[(0,r.jsxs)(c.Ol,{children:[(0,r.jsxs)(c.ll,{className:"flex",children:[t.projectName,"\xa0",t.verified&&r.jsx(i.Z,{className:"text-success"})]}),(0,r.jsxs)(c.SZ,{className:"text-gray-600",children:[r.jsx("p",{className:"my-auto",children:t.createdAt?new Date(t.createdAt).toLocaleDateString():"N/A"}),r.jsx("br",{}),r.jsx(l.C,{className:x,children:u})]})]}),(0,r.jsxs)(c.aY,{className:"grid gap-4 mb-auto flex-grow",children:[(0,r.jsxs)("div",{className:"mb-4 items-start pb-4 last:mb-0 last:pb-0 w-full",children:[r.jsx("span",{className:"flex h-2 w-2 rounded-full"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:t.description?.length>40?`${t.description.slice(0,40)}...`:t.description||"No description available"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Company:"})," ",t.companyName]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Role:"})," ",t.role]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Experience:"})," ",t.experience]}),r.jsx("div",{className:"flex flex-wrap gap-1 mt-2",children:t?.skillsRequired?.map((e,t)=>r.jsx(l.C,{className:"text-xs text-white bg-muted",children:e},t))})]})]}),r.jsx(c.eW,{children:r.jsx(a.default,{href:`/${s.toLocaleLowerCase()}/project/${t._id}`,className:"w-full",children:r.jsx(o.z,{className:`w-full ${t.status===p.sB.COMPLETED&&"bg-green-900 hover:bg-green-700"}`,children:"View full details"})})})]})}},97265:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var r=s(10326),a=s(29752);function i({title:e,value:t,icon:s,additionalInfo:i}){return(0,r.jsxs)(a.Zb,{"x-chunk":"dashboard-05-chunk-1",children:[(0,r.jsxs)(a.Ol,{className:"pb-2",children:[s,r.jsx(a.SZ,{children:e}),r.jsx(a.ll,{className:"text-4xl",children:t})]}),r.jsx(a.aY,{children:r.jsx("div",{className:"text-xs text-muted-foreground",children:i})})]})}},78062:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Separator:()=>o});var r=s(10326),a=s(17577),i=s(90220),n=s(51223);let o=a.forwardRef(({className:e,orientation:t="horizontal",decorative:s=!0,...a},o)=>r.jsx(i.f,{ref:o,decorative:s,orientation:t,className:(0,n.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...a}));o.displayName=i.f.displayName},46319:(e,t,s)=>{"use strict";s.d(t,{$C:()=>f,Ne:()=>b,yn:()=>g});var r=s(10326),a=s(95920),i=s(57671),n=s(94909),o=s(12070),c=s(66307),l=s(69669),d=s(40617),m=s(69515),p=s(88378),h=s(40900),u=s(98091),x=s(46226);let g=[{href:"#",icon:r.jsx(x.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:r.jsx(a.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:r.jsx(i.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:r.jsx(n.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:r.jsx(n.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:r.jsx(o.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:r.jsx(c.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:r.jsx(l.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:r.jsx(d.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:r.jsx(m.Z,{className:"h-5 w-5"}),label:"Notes"}],f=[{href:"/business/settings/business-info",icon:r.jsx(p.Z,{className:"h-5 w-5"}),label:"Settings"}],b=[{href:"#",icon:r.jsx(x.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:r.jsx(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:r.jsx(m.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:r.jsx(h.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:r.jsx(u.Z,{className:"h-5 w-5"}),label:"Trash"}]},58285:(e,t,s)=>{"use strict";var r,a,i,n,o,c,l,d,m,p,h;s.d(t,{Dy:()=>p,Dz:()=>u});let u={BATCH:3};(function(e){e.PROJECT_HIRING="PROJECT_HIRING",e.SKILL_INTERVIEW="SKILL_INTERVIEW",e.DOMAIN_INTERVIEW="DOMAIN_INTERVIEW",e.TALENT_INTERVIEW="TALENT_INTERVIEW"})(r||(r={})),function(e){e.ADDED="Added",e.APPROVED="Approved",e.CLOSED="Closed",e.COMPLETED="Completed"}(a||(a={})),function(e){e.ACTIVE="Active",e.IN_ACTIVE="Inactive",e.NOT_VERIFIED="Not Verified"}(i||(i={})),function(e){e.BUSINESS="Business",e.FREELANCER="Freelancer",e.BOTH="Both"}(n||(n={})),function(e){e.ACTIVE="Active",e.IN_ACTIVE="Inactive"}(o||(o={})),function(e){e.APPLIED="APPLIED",e.NOT_APPLIED="NOT_APPLIED",e.APPROVED="APPROVED",e.FAILED="FAILED",e.STOPPED="STOPPED",e.REAPPLIED="REAPPLIED"}(c||(c={})),function(e){e.PENDING="Pending",e.ACCEPTED="Accepted",e.REJECTED="Rejected",e.PANEL="Panel",e.INTERVIEW="Interview"}(l||(l={})),function(e){e.ACTIVE="ACTIVE",e.INACTIVE="INACTIVE",e.ARCHIVED="ARCHIVED"}(d||(d={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.INACTIVE="Inactive",e.CLOSED="Closed"}(m||(m={})),function(e){e.FREELANCER="FREELANCER",e.ADMIN="ADMIN",e.BUSINESS="BUSINESS"}(p||(p={})),function(e){e.CREATED="Created",e.CLOSED="Closed",e.ACTIVE="Active"}(h||(h={}))},39958:(e,t,s)=>{"use strict";var r,a,i;s.d(t,{cd:()=>r,d8:()=>n,kJ:()=>a,sB:()=>i}),function(e){e.Mastery="Mastery",e.Proficient="Proficient",e.Beginner="Beginner"}(r||(r={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.REJECTED="Rejected",e.COMPLETED="Completed"}(a||(a={})),function(e){e.ACTIVE="ACTIVE",e.PENDING="PENDING",e.REJECTED="REJECTED",e.COMPLETED="COMPLETED"}(i||(i={}));let n={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},13221:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>o});var r=s(68570);let a=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\dashboard\business\page.tsx`),{__esModule:i,$$typeof:n}=a;a.default;let o=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\dashboard\business\page.tsx#default`)},90220:(e,t,s)=>{"use strict";s.d(t,{f:()=>l});var r=s(17577),a=s(77335),i=s(10326),n="horizontal",o=["horizontal","vertical"],c=r.forwardRef((e,t)=>{let{decorative:s,orientation:r=n,...c}=e,l=o.includes(r)?r:n;return(0,i.jsx)(a.WV.div,{"data-orientation":l,...s?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...c,ref:t})});c.displayName="Separator";var l=c},72787:e=>{"use strict";e.exports=JSON.parse('{"freelancerEarnings":1000,"freelancersampleInterview":{"interviewer":"John Doe","interviewee":"Jane Smith","skill":"React Development","interviewDate":"2023-11-23T10:30:00Z","rating":4.5,"comments":"Great communication skills and technical expertise."},"dashboardactiveProject":{"heading":"Active Project","content":"+11 Current active projects"},"dashboardpendingProject":{"title":"Pending projects","itemCounts":{"total":15,"low":5,"medium":5,"high":5}},"dashboardBusinessCompleteProject":"+10% from last month","dashboardBusinessPendingProject":"+2 new projects this week","dashboardtotalRevenue":{"heading":"Total Revenue","content":"$45,231.89 +20.1% from last month"},"dashboardoracleWork":{"heading":"Oracle Work","content":"+11 projects"},"dashboardorderTable":{"customerName":"Jack smith","customerEmail":"<EMAIL>","customerType":"Sale","customerStatus":"Fulfilled","customerDate":"2023-06-23","customerAmount":"$250.00"},"dashboardorderDate":"Date: November 23, 2023","dashboardorderShippingAddress":{"name":"Liam Johnson","address":"1234 Main St.","state":"Anytown, CA 12345"},"dashboardorderCustomerInfo":{"customer":"Liam Johnson","email":"<EMAIL>","phone":"****** 567 890"},"dashboardorderCard":{"card":"Visa","cardNumber":"**** **** **** 4532"},"dashboardorderUpdateDate":"November 23, 2023","marketfreelancerJob":{"heading":"Arya.ai Data Scientist","content":"Arya is an autonomous AI operating platform for banks, insurers, and financial service providers that simplifies buildout and manages the...","skills":["Generative AI","Python","NLP","PyTorch","Transformers"],"location":"Mumbai","founded":"2013","employees":"10-50 employees"},"dashboardfreelancercurrentInterview":[{"reference":"Jane Smith","skill":"HTML/CSS","interviewDate":"2023-11-23T10:30:00Z","rating":9,"comments":"Great communication skills and technical expertise.","status":"Completed","description":"This interview focused on assessing proficiency in HTML/CSS and evaluating communication skills.","contact":"<EMAIL>"},{"reference":"Jane Smith","domain":"DevOps","interviewDate":"2023-11-23T10:30:00Z","rating":9,"comments":"Great communication skills and technical expertise.","status":"Pending","description":"This interview was scheduled to discuss the candidate\'s experience and skills in DevOps.","contact":"<EMAIL>"}],"dashboardfreelancerhistoryUserEmail":"<EMAIL>","dashboardfreelancerhistoryInterview":[{"reference":"Jane Smith","skill":"HTML/CSS","interviewDate":"2023-11-23T10:30:00Z","rating":9,"comments":"Great communication skills and technical expertise.","status":"Completed","description":"This interview focused on assessing proficiency in HTML/CSS and evaluating communication skills.","contact":"<EMAIL>"},{"reference":"Jane Smith","domain":"DevOps","interviewDate":"2023-11-23T10:30:00Z","rating":9,"comments":"Great communication skills and technical expertise.","status":"Completed","description":"This interview was scheduled to discuss the candidate\'s experience and skills in DevOps.","contact":"<EMAIL>"}],"dashboardFreelancerOracleBusiness":[{"firstName":"John","lastName":"Smith","email":"<EMAIL>","phone":"+*********0","companyName":"Tech Innovators Inc.","companySize":"500-1000 employees","referenceEmail":"<EMAIL>","websiteLink":"https://www.techinnovators.com","linkedInLink":"https://www.linkedin.com/in/johnsmith","githubLink":"https://github.com/johnsmith","comments":"","status":"pending"},{"firstName":"Alice","lastName":"Johnson","email":"<EMAIL>","phone":"+0*********","companyName":"Creative Solutions Ltd.","companySize":"100-500 employees","referenceEmail":"<EMAIL>","websiteLink":"https://www.creativesolutions.com","linkedInLink":"https://www.linkedin.com/in/alicejohnson","githubLink":"https://github.com/alicejohnson","comments":"","status":"pending"},{"firstName":"Robert","lastName":"Brown","email":"<EMAIL>","phone":"+1122334455","companyName":"Global Enterprises","companySize":"1000-5000 employees","referenceEmail":"<EMAIL>","websiteLink":"https://www.globalenterprises.com","linkedInLink":"https://www.linkedin.com/in/robertbrown","githubLink":"https://github.com/robertbrown","comments":"","status":"pending"}],"dashboardFreelancerOracleEducation":[{"type":"Bachelor\'s Degree","instituteName":"University of Example","location":"Example City, Example Country","startFrom":"2018-09-01","endTo":"2022-06-15","grade":"A","referencePersonName":"Dr. John Doe","degreeNumber":"*********","comments":"","status":"pending"},{"type":"Master\'s Degree","instituteName":"University of Example","location":"Example City, Example Country","startFrom":"2022-09-01","endTo":"2024-06-15","grade":"A+","referencePersonName":"Dr. Jane Smith","degreeNumber":"*********","comments":"","status":"pending"},{"type":"Ph.D.","instituteName":"University of Example","location":"Example City, Example Country","startFrom":"2024-09-01","endTo":"2028-06-15","grade":"A+","referencePersonName":"Dr. Emily Johnson","degreeNumber":"456789123","comments":"","status":"pending"}],"dashboardFreelancerOracleProject":[{"projectName":"Task Tracker","description":"A web application for managing and tracking daily tasks and projects.","githubLink":"https://github.com/yourusername/TaskTracker","startFrom":"2023-05-01","endTo":"2023-10-15","reference":"Mr. Alex Johnson, Senior Developer","techUsed":["Vue.js","JavaScript","Firebase","CSS"],"comments":"","status":"pending"},{"projectName":"Inventory Management System","description":"A system for managing inventory in warehouses.","githubLink":"https://github.com/yourusername/InventoryManagementSystem","startFrom":"2022-01-01","endTo":"2022-06-01","reference":"Ms. Maria Gonzalez, Project Manager","techUsed":["React","Node.js","MongoDB","Sass"],"comments":"","status":"pending"},{"projectName":"E-commerce Platform","description":"An online platform for buying and selling products.","githubLink":"https://github.com/yourusername/EcommercePlatform","startFrom":"2021-02-01","endTo":"2021-08-01","reference":"Mr. John Smith, Lead Developer","techUsed":["Angular","TypeScript","Firebase","Bootstrap"],"comments":"","status":"pending"}],"dashboardFreelancerOracleExperience":[{"jobTitle":"Frontend Developer","workDescription":"Responsible for developing user-friendly web applications using React and TypeScript.","startFrom":"2022-01-15","endTo":"2023-07-01","referencePersonName":"Jane Doe","referencePersonEmail":"<EMAIL>","githubRepoLink":"https://github.com/janedoe/project-repo","comments":"","status":"pending"},{"jobTitle":"Backend Developer","workDescription":"Developed and maintained server-side logic using Node.js and Express.","startFrom":"2021-02-01","endTo":"2021-12-31","referencePersonName":"John Smith","referencePersonEmail":"<EMAIL>","githubRepoLink":"https://github.com/johnsmith/backend-project","comments":"","status":"pending"},{"jobTitle":"Full Stack Developer","workDescription":"Worked on both frontend and backend development using MERN stack.","startFrom":"2020-03-01","endTo":"2021-01-31","referencePersonName":"Alice Johnson","referencePersonEmail":"<EMAIL>","githubRepoLink":"https://github.com/alicejohnson/fullstack-project","comments":"","status":"pending"}],"businessProjectDetailCard":{"description":"Welcome to our project! This initiative aims to [briefly describe the main goal or purpose of the project ]. Through thisproject, we intend to [mention key objectives or outcomes]. Our team is dedicated to [highlight any unique approaches ormethodologies]. We believe this project will [state the expected impact or benefits ]. Feel free to replace the placeholders with specific details about your project. \\nIf you need further customization or additional sections, let me know!  \\nWelcome to our project! This initiative aims to [briefly describe the main goal or purpose of the project ]. Through this project, we intend to [mention key objectives or outcomes ]. Our team is dedicated to [highlight any unique approaches or methodologies ]. We believe this project will [state the expected impact or benefits ]. Feel free to replace the placeholders with specific details about your project. If you need further customization or additional sections, let me know! ","email":"<EMAIL>","status":"Current","startDate":"22/22/2222","endDate":"24/22/2222"},"businessprojectCardDomains":["Frontend","Backend","Graphic Designer","3D artist","Fullstack"],"businessprojectCardSkills":["React","Mongo","Golang","Java","Html","Css"],"businessprojectProfileCard":{"heading":"Frontend Developer","description":"Your requirement is of 2 freelancers for this profile, 6 people have appplied via bid and 1 person is selected till now.","email":"<EMAIL>","status":"Current","startDate":"22/22/2222","endDate":"24/22/2222"},"marketFreelancerProject":{"project_name":"AI Development Project","project_id":"#12345","location":"Delhi, India","description":"We\'re looking for an experienced web developer who\'s really good at making interactive forms. The perfect candidate should know a lot about web development and have a bunch of cool forms they\'ve made before. Your main job will be making forms that people can use easily and that look nice.","email":"<EMAIL>","company_name":"Tech Innovators Inc.","start":"2023-01-01T00:00:00.000Z","end":"2023-12-31T00:00:00.000Z","skills_required":["JavaScript","React","Python","Machine Learning"],"experience":"2+ years of experience in AI development.","role":"Lead Developer","project_type":"Full-time"},"marketFreelancerProjectOtherBits":[{"username":"Alex004","bitAmount":100},{"username":"User2","bitAmount":150},{"username":"alen789","bitAmount":200}],"projectRejectedCard":{"companyName":"ABC Corporation","role":"Software Engineer","projectType":"Web Development","description":"This is a sample project description","skillsRequired":["JavaScript","React","Node.js"],"start":"2023-02-15","email":"<EMAIL>","experience":"5+ years"},"projectCurrentCard":{"companyName":"ABC Corporation","role":"Software Engineer","projectType":"Web Development","description":"This is a sample project description for a current ongoing project.","skillsRequired":["JavaScript","React","Node.js"],"start":"2023-02-15","end":"current","email":"<EMAIL>","experience":"5+ years"},"projectCompleteCard":{"companyName":"ABC Corporation","role":"Software Engineer","projectType":"Web Development","description":"This is a sample project description for a current ongoing project.","skillsRequired":["JavaScript","React","Node.js"],"start":"2023-02-15","end":"2023-09-24","email":"<EMAIL>","experience":"5+ years"},"projectUnderVerificatinCard":{"companyName":"ABC Corporation","role":"Software Engineer","projectType":"Web Development","description":"This is a sample project description for a current ongoing project.","skillsRequired":["JavaScript","React","Node.js"],"start":"2023-02-15","email":"<EMAIL>","experience":"5+ years"}}')}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588],()=>s(20692));module.exports=r})();