(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9151],{72319:function(e,t,s){Promise.resolve().then(s.bind(s,73262))},73262:function(e,t,s){"use strict";s.r(t);var n=s(57437),i=s(2265),l=s(11444),r=s(3274),o=s(64797),a=s(82230),c=s(5498),d=s(85121),u=s(15922),m=s(83640),h=s(78068),f=s(22637),v=s(62688),N=s(66227);t.default=()=>{let e=(0,l.v9)(e=>e.user),t=e.uid,{notes:s,isLoading:p,fetchNotes:x,setNotes:b}=(0,f.Z)(t);(0,i.useEffect)(()=>{t&&x()},[x,t]);let j=async s=>{var n;if(!s.title||!s.content||!t){(0,h.Am)({title:"Missing required fields",description:"Title and content are required to create a note.",duration:3e3});return}let i={...s,userId:t,bgColor:s.bgColor||"#FFFFFF",banner:s.banner||"",noteType:m.wK.NOTE,type:m.JG.PERSONAL,entityType:null==e?void 0:null===(n=e.type)||void 0===n?void 0:n.toUpperCase()};b(e=>[i,...e]);try{let e=await u.b.post("/notes",i);(null==e?void 0:e.data)&&((0,h.Am)({title:"Note Created",description:"Your note was successfully created.",duration:3e3}),x())}catch(e){console.error("Failed to create note:",e),(0,h.Am)({title:"Error",description:"Failed to create the note.",duration:3e3}),b(e=>e.filter(e=>e!==i))}};return(0,n.jsxs)("section",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,n.jsx)(o.Z,{menuItemsTop:a.Ne,menuItemsBottom:N.$C,active:"Notes"}),(0,n.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,n.jsx)("div",{children:(0,n.jsx)(v.Z,{menuItemsTop:a.Ne,menuItemsBottom:N.$C,activeMenu:"Notes",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Notes",link:"/notes"}]})}),(0,n.jsxs)("div",{children:[(0,n.jsx)(c.Z,{isTrash:!1,setNotes:b,notes:s,onNoteCreate:j}),(0,n.jsx)("div",{className:"p-6",children:p?(0,n.jsx)("div",{className:"flex justify-center items-center h-[40vh]",children:(0,n.jsx)(r.Z,{className:"my-4 h-8 w-8 animate-spin"})}):(0,n.jsx)("div",{children:(null==s?void 0:s.length)>0?(0,n.jsx)(d.Z,{fetchNotes:x,notes:s,setNotes:b,isArchive:!1}):(0,n.jsx)("div",{className:"flex justify-center items-center h-[40vh] w-full",children:(0,n.jsx)("p",{children:"No notes available. Start adding some!"})})})})]})]})]})}}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2455,9726,2688,6755,2971,7023,1744],function(){return e(e.s=72319)}),_N_E=e.O()}]);