(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4940],{1604:function(e,a,t){Promise.resolve().then(t.bind(t,5281))},5891:function(e,a,t){"use strict";t.d(a,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},76035:function(e,a,t){"use strict";t.d(a,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},43193:function(e,a,t){"use strict";t.d(a,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]])},49100:function(e,a,t){"use strict";t.d(a,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},47390:function(e,a,t){"use strict";t.d(a,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},29406:function(e,a,t){"use strict";t.d(a,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},36141:function(e,a,t){"use strict";t.d(a,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},33907:function(e,a,t){"use strict";t.d(a,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},73347:function(e,a,t){"use strict";t.d(a,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},33149:function(e,a,t){"use strict";t.d(a,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]])},40064:function(e,a,t){"use strict";t.d(a,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("TabletSmartphone",[["rect",{width:"10",height:"14",x:"3",y:"8",rx:"2",key:"1vrsiq"}],["path",{d:"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4",key:"1j4zmg"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},10883:function(e,a,t){"use strict";t.d(a,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},5281:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return Z}});var l=t(57437),s=t(2265),n=t(64797),r=t(66227),i=t(39343),c=t(31014),d=t(59772),h=t(92513),o=t(29406),m=t(78068),x=t(54662),u=t(89733),p=t(77209),j=t(15922),v=t(2128),f=t(48185),y=t(97540);let k="Pending",b=d.z.object({skill:d.z.string().min(1,"Skill is required"),experience:d.z.number().min(0,"Experience must be a non-negative number"),level:d.z.string().min(1,"Level is required")}),g=d.z.object({domain:d.z.string().min(1,"Domain is required"),experience:d.z.number().min(0,"Experience must be a non-negative number"),level:d.z.string().min(1,"Level is required")});var N=()=>{let[e,a]=(0,s.useState)([]),[t,n]=(0,s.useState)([]),[r,d]=(0,s.useState)([]),[N,w]=(0,s.useState)([]),[Z,C]=(0,s.useState)(!1),[E,M]=(0,s.useState)(!1);(0,s.useEffect)(()=>{!async function(){try{var e,t;let l=await j.b.get("/skills");(null==l?void 0:null===(e=l.data)||void 0===e?void 0:e.data)&&Array.isArray(l.data.data)&&a(l.data.data);let s=await j.b.get("/domain");(null==s?void 0:null===(t=s.data)||void 0===t?void 0:t.data)&&Array.isArray(s.data.data)&&n(s.data.data)}catch(a){console.error("Error fetching data:",a);let e="Failed to add project. Please try again later.";a instanceof Error&&(e="Failed to add project. Error: ".concat(a.message)),(0,m.Am)({variant:"destructive",title:"Submission Error",description:e})}}()},[]);let{handleSubmit:S,formState:{errors:A},control:z,reset:I}=(0,i.cI)({resolver:(0,c.F)(b)}),{handleSubmit:P,formState:{errors:D},control:V,reset:L}=(0,i.cI)({resolver:(0,c.F)(g)});return(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsxs)("div",{className:"mb-8 ml-5",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold",children:"Schedule Interview"}),(0,l.jsx)("p",{className:"text-gray-400 mt-2",children:"Add your relevant skills and domains to help us schedule the right interview for you."})]}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 p-2 sm:px-6 sm:py-0 md:gap-8 lg:flex-row xl:flex-row pt-2 pl-4 sm:pt-4 sm:pl-6 md:pt-6 md:pl-8",children:[(0,l.jsxs)("div",{className:"mb-8 w-full sm:w-1/2",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold",children:"Skills"}),(0,l.jsxs)(x.Vq,{open:Z,onOpenChange:C,children:[(0,l.jsx)(x.hg,{asChild:!0,children:(0,l.jsxs)(u.z,{children:[(0,l.jsx)(h.Z,{className:"mr-2 h-4 w-4"})," Add Skill"]})}),(0,l.jsxs)(x.cZ,{children:[(0,l.jsxs)(x.fK,{children:[(0,l.jsx)(x.$N,{children:"Add Skill"}),(0,l.jsx)(x.Be,{children:"Select a skill, level, and enter your experience."})]}),(0,l.jsxs)("form",{onSubmit:S(e=>{d([...r,{skill:e.skill,experience:e.experience,level:e.level,status:k}]),I(),C(!1)}),children:[(0,l.jsx)(i.Qr,{name:"skill",control:z,render:a=>{let{field:t}=a;return(0,l.jsxs)(v.Ph,{...t,onValueChange:e=>t.onChange(e),children:[(0,l.jsx)(v.i4,{children:(0,l.jsx)(v.ki,{placeholder:"Select a skill"})}),(0,l.jsx)(v.Bw,{children:e.map(e=>(0,l.jsx)(v.Ql,{value:e.label,children:e.label},e.label))})]})}}),A.skill&&(0,l.jsx)("p",{className:"text-red-500",children:A.skill.message}),(0,l.jsxs)("div",{className:"mt-2",children:[(0,l.jsx)(i.Qr,{name:"level",control:z,render:e=>{let{field:a}=e;return(0,l.jsxs)(v.Ph,{...a,onValueChange:e=>a.onChange(e),children:[(0,l.jsx)(v.i4,{children:(0,l.jsx)(v.ki,{placeholder:"Select level"})}),(0,l.jsx)(v.Bw,{children:Object.values(y.cd).map(e=>(0,l.jsx)(v.Ql,{value:e,children:e},e))})]})}}),A.level&&(0,l.jsx)("p",{className:"text-red-500",children:A.level.message})]}),(0,l.jsxs)("div",{className:"mt-2",children:[(0,l.jsx)(i.Qr,{name:"experience",control:z,render:e=>{let{field:a}=e;return(0,l.jsxs)("div",{className:"col-span-3 relative",children:[(0,l.jsx)(p.I,{...a,type:"number",placeholder:"Experience (years)",className:"w-full",min:0,max:50,step:"0.1",onChange:e=>a.onChange(parseFloat(e.target.value)||0)}),(0,l.jsx)("span",{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"YEARS"})]})}}),A.experience&&(0,l.jsx)("p",{className:"text-red-500",children:A.experience.message})]}),(0,l.jsxs)(x.cN,{children:[(0,l.jsx)(u.z,{className:"mt-3",variant:"ghost",onClick:()=>C(!1),children:"Cancel"}),(0,l.jsx)(u.z,{className:"mt-3",type:"submit",children:"Add"})]})]})]})]})]}),(0,l.jsx)(f.Zb,{className:"p-4",children:(0,l.jsxs)("div",{className:"text-center py-10 w-[100%] mt-10",children:[(0,l.jsx)(o.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,l.jsx)("p",{className:"text-gray-500",children:"No data available"})]})})]}),(0,l.jsxs)("div",{className:"mb-8 w-full sm:w-1/2",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold",children:"Domains"}),(0,l.jsxs)(x.Vq,{open:E,onOpenChange:M,children:[(0,l.jsx)(x.hg,{asChild:!0,children:(0,l.jsxs)(u.z,{children:[(0,l.jsx)(h.Z,{className:"mr-2 h-4 w-4"})," Add Domain"]})}),(0,l.jsxs)(x.cZ,{children:[(0,l.jsxs)(x.fK,{children:[(0,l.jsx)(x.$N,{children:"Add Domain"}),(0,l.jsx)(x.Be,{children:"Select a domain, level, and enter your experience."})]}),(0,l.jsxs)("form",{onSubmit:P(e=>{console.log("Domain data:",e),w([...N,{domain:e.domain,experience:e.experience,level:e.level,status:k}]),L(),M(!1)}),children:[(0,l.jsx)(i.Qr,{name:"domain",control:V,render:e=>{let{field:a}=e;return(0,l.jsxs)(v.Ph,{...a,onValueChange:e=>a.onChange(e),children:[(0,l.jsx)(v.i4,{children:(0,l.jsx)(v.ki,{placeholder:"Select a domain"})}),(0,l.jsx)(v.Bw,{children:t.map(e=>(0,l.jsx)(v.Ql,{value:e.label,children:e.label},e.label))})]})}}),D.domain&&(0,l.jsx)("p",{className:"text-red-500",children:D.domain.message}),(0,l.jsxs)("div",{className:"mt-2",children:[(0,l.jsx)(i.Qr,{name:"level",control:V,render:e=>{let{field:a}=e;return(0,l.jsxs)(v.Ph,{...a,onValueChange:e=>a.onChange(e),children:[(0,l.jsx)(v.i4,{children:(0,l.jsx)(v.ki,{placeholder:"Select level"})}),(0,l.jsx)(v.Bw,{children:Object.values(y.cd).map(e=>(0,l.jsx)(v.Ql,{value:e,children:e},e))})]})}}),D.level&&(0,l.jsx)("p",{className:"text-red-500",children:D.level.message})]}),(0,l.jsxs)("div",{className:"mt-2",children:[(0,l.jsx)(i.Qr,{name:"experience",control:V,render:e=>{let{field:a}=e;return(0,l.jsxs)("div",{className:"col-span-3 relative",children:[(0,l.jsx)(p.I,{...a,type:"number",placeholder:"Experience (years)",className:"w-full",min:0,max:50,step:.1,onChange:e=>a.onChange(parseFloat(e.target.value)||0)}),(0,l.jsx)("span",{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"YEARS"})]})}}),D.experience&&(0,l.jsx)("p",{className:"text-red-500",children:D.experience.message})]}),(0,l.jsxs)(x.cN,{children:[(0,l.jsx)(u.z,{className:"mt-3",variant:"ghost",onClick:()=>M(!1),children:"Cancel"}),(0,l.jsx)(u.z,{className:"mt-3",type:"submit",children:"Add"})]})]})]})]})]}),(0,l.jsx)(f.Zb,{className:"p-4",children:(0,l.jsxs)("div",{className:"text-center py-10 w-[100%] mt-10",children:[(0,l.jsx)(o.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,l.jsx)("p",{className:"text-gray-500",children:"No data available"})]})})]})]})]})},w=t(62688);function Z(){return(0,l.jsxs)("div",{className:"flex min-h-screen w-full",children:[(0,l.jsx)(n.Z,{menuItemsTop:r.yn,menuItemsBottom:r.$C,active:"ScheduleInterviews"}),(0,l.jsxs)("div",{className:"flex mb-8 flex-col sm:py-0 sm:gap-2 sm:pl-14 w-full",children:[(0,l.jsx)(w.Z,{menuItemsTop:r.yn,menuItemsBottom:r.$C,activeMenu:"ScheduleInterviews",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Schedule-Interview",link:"#"}]}),(0,l.jsx)(N,{})]})]})}},66227:function(e,a,t){"use strict";t.d(a,{$C:function(){return y},yL:function(){return k},yn:function(){return f}});var l=t(57437),s=t(11005),n=t(33149),r=t(76035),i=t(49100),c=t(40064),d=t(43193),h=t(36141),o=t(33907),m=t(47390),x=t(73347),u=t(24258),p=t(5891),j=t(10883),v=t(66648);let f=[{href:"#",icon:(0,l.jsx)(v.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,l.jsx)(s.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:(0,l.jsx)(n.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:(0,l.jsx)(r.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:(0,l.jsx)(i.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:(0,l.jsx)(c.Z,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:(0,l.jsx)(d.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:(0,l.jsx)(h.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:(0,l.jsx)(o.Z,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:(0,l.jsx)(m.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,l.jsx)(x.Z,{className:"h-5 w-5"}),label:"Notes"}],y=[{href:"/freelancer/settings/personal-info",icon:(0,l.jsx)(u.Z,{className:"h-5 w-5"}),label:"Settings"}];v.default,s.Z,x.Z,p.Z,j.Z;let k=[{href:"#",icon:(0,l.jsx)(v.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,l.jsx)(s.Z,{className:"h-5 w-5"}),label:"Home"}]},97540:function(e,a,t){"use strict";var l,s,n,r,i,c;t.d(a,{cd:function(){return l},d8:function(){return d},kJ:function(){return s},sB:function(){return n}}),(r=l||(l={})).Mastery="Mastery",r.Proficient="Proficient",r.Beginner="Beginner",(i=s||(s={})).ACTIVE="Active",i.PENDING="Pending",i.REJECTED="Rejected",i.COMPLETED="Completed",(c=n||(n={})).ACTIVE="ACTIVE",c.PENDING="PENDING",c.REJECTED="REJECTED",c.COMPLETED="COMPLETED";let d={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,1374,2455,9726,2688,2971,7023,1744],function(){return e(e.s=1604)}),_N_E=e.O()}]);