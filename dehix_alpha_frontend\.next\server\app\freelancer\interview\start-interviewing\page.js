(()=>{var e={};e.id=1783,e.ids=[1783],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},16159:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>d,originalPathname:()=>p,pages:()=>u,routeModule:()=>h,tree:()=>c}),t(72881),t(54302),t(12523);var s=t(23191),i=t(88716),n=t(37922),a=t.n(n),o=t(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let c=["",{children:["freelancer",{children:["interview",{children:["start-interviewing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,72881)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\interview\\start-interviewing\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\interview\\start-interviewing\\page.tsx"],p="/freelancer/interview/start-interviewing/page",d={require:t,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/freelancer/interview/start-interviewing/page",pathname:"/freelancer/interview/start-interviewing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},1384:(e,r,t)=>{Promise.resolve().then(t.bind(t,54133))},47546:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},54133:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(10326);t(17577);var i=t(92166),n=t(30325),a=t(40588);function o(){return(0,s.jsxs)("div",{className:"flex min-h-screen w-full  bg-muted/40",children:[s.jsx(i.Z,{menuItemsTop:n.y,menuItemsBottom:n.$,active:"project"}),(0,s.jsxs)("div",{className:"flex mb-8 flex-col sm:pl-14 w-full",children:[s.jsx(a.Z,{menuItemsTop:n.y,menuItemsBottom:n.$,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Interview",link:"#"},{label:"start-interviewing",link:"#"}]}),s.jsx("div",{children:"TODO"})]})]})}},30325:(e,r,t)=>{"use strict";t.d(r,{$:()=>h,y:()=>d});var s=t(10326),i=t(95920),n=t(94909),a=t(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,a.Z)("ListVideo",[["path",{d:"M12 12H3",key:"18klou"}],["path",{d:"M16 6H3",key:"1wxfjs"}],["path",{d:"M12 18H3",key:"11ftsu"}],["path",{d:"m16 12 5 3-5 3v-6Z",key:"zpskkp"}]]);var l=t(47546);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,a.Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var u=t(88378),p=t(46226);let d=[{href:"#",icon:s.jsx(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:s.jsx(i.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/interview/profile",icon:s.jsx(n.Z,{className:"h-5 w-5"}),label:"Profile"},{href:"/freelancer/interview/current",icon:s.jsx(o,{className:"h-5 w-5"}),label:"Current"},{href:"/freelancer/interview/bids",icon:s.jsx(l.Z,{className:"h-5 w-5"}),label:"Bids"},{href:"/freelancer/interview/history",icon:s.jsx(c,{className:"h-5 w-5"}),label:"History"}],h=[{href:"/freelancer/settings/personal-info",icon:s.jsx(u.Z,{className:"h-5 w-5"}),label:"Settings"}]},72881:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>a,__esModule:()=>n,default:()=>o});var s=t(68570);let i=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\interview\start-interviewing\page.tsx`),{__esModule:n,$$typeof:a}=i;i.default;let o=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\interview\start-interviewing\page.tsx#default`)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588],()=>t(16159));module.exports=s})();