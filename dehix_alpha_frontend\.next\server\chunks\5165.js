"use strict";exports.id=5165,exports.ids=[5165],exports.modules={40900:(e,t,r)=>{r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},12070:(e,t,r)=>{r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},66307:(e,t,r)=>{r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69669:(e,t,r)=>{r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},41137:(e,t,r)=>{r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},40617:(e,t,r)=>{r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},88378:(e,t,r)=>{r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},57671:(e,t,r)=>{r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},69515:(e,t,r)=>{r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},98091:(e,t,r)=>{r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94909:(e,t,r)=>{r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("UsersRound",[["path",{d:"M18 21a8 8 0 0 0-16 0",key:"3ypg7q"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["path",{d:"M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3",key:"10s06x"}]])},72301:(e,t,r)=>{r.d(t,{Z:()=>i});var a=r(71159),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(e,t)=>{let r=(0,a.forwardRef)(({color:r="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:d,className:s="",children:c,...u},p)=>(0,a.createElement)("svg",{ref:p,...n,width:i,height:i,stroke:r,strokeWidth:d?24*Number(o)/Number(i):o,className:["lucide",`lucide-${l(e)}`,s].join(" "),...u},[...t.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(c)?c:[c]]));return r.displayName=`${e}`,r}},80086:(e,t,r)=>{r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(72301).Z)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},90071:(e,t,r)=>{r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(72301).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},69013:(e,t,r)=>{r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(72301).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},34478:(e,t,r)=>{r.d(t,{f:()=>o});var a=r(17577),n=r(77335),l=r(10326),i=a.forwardRef((e,t)=>(0,l.jsx)(n.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i},90220:(e,t,r)=>{r.d(t,{f:()=>s});var a=r(17577),n=r(77335),l=r(10326),i="horizontal",o=["horizontal","vertical"],d=a.forwardRef((e,t)=>{let{decorative:r,orientation:a=i,...d}=e,s=o.includes(a)?a:i;return(0,l.jsx)(n.WV.div,{"data-orientation":s,...r?{role:"none"}:{"aria-orientation":"vertical"===s?s:void 0,role:"separator"},...d,ref:t})});d.displayName="Separator";var s=d},41959:(e,t,r)=>{r.d(t,{bU:()=>j,fC:()=>w});var a=r(17577),n=r(82561),l=r(48051),i=r(93095),o=r(52067),d=r(53405),s=r(2566),c=r(77335),u=r(10326),p="Switch",[f,y]=(0,i.b)(p),[h,v]=f(p),k=a.forwardRef((e,t)=>{let{__scopeSwitch:r,name:i,checked:d,defaultChecked:s,required:p,disabled:f,value:y="on",onCheckedChange:v,...k}=e,[m,b]=a.useState(null),w=(0,l.e)(t,e=>b(e)),j=a.useRef(!1),Z=!m||!!m.closest("form"),[M=!1,C]=(0,o.T)({prop:d,defaultProp:s,onChange:v});return(0,u.jsxs)(h,{scope:r,checked:M,disabled:f,children:[(0,u.jsx)(c.WV.button,{type:"button",role:"switch","aria-checked":M,"aria-required":p,"data-state":g(M),"data-disabled":f?"":void 0,disabled:f,value:y,...k,ref:w,onClick:(0,n.M)(e.onClick,e=>{C(e=>!e),Z&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})}),Z&&(0,u.jsx)(x,{control:m,bubbles:!j.current,name:i,value:y,checked:M,required:p,disabled:f,style:{transform:"translateX(-100%)"}})]})});k.displayName=p;var m="SwitchThumb",b=a.forwardRef((e,t)=>{let{__scopeSwitch:r,...a}=e,n=v(m,r);return(0,u.jsx)(c.WV.span,{"data-state":g(n.checked),"data-disabled":n.disabled?"":void 0,...a,ref:t})});b.displayName=m;var x=e=>{let{control:t,checked:r,bubbles:n=!0,...l}=e,i=a.useRef(null),o=(0,d.D)(r),c=(0,s.t)(t);return a.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(o!==r&&t){let a=new Event("click",{bubbles:n});t.call(e,r),e.dispatchEvent(a)}},[o,r,n]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...l,tabIndex:-1,ref:i,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function g(e){return e?"checked":"unchecked"}var w=k,j=b},28407:(e,t,r)=>{r.d(t,{VY:()=>E,aV:()=>R,fC:()=>N,xz:()=>z});var a=r(17577),n=r(82561),l=r(93095),i=r(15594),o=r(9815),d=r(77335),s=r(17124),c=r(52067),u=r(88957),p=r(10326),f="Tabs",[y,h]=(0,l.b)(f,[i.Pc]),v=(0,i.Pc)(),[k,m]=y(f),b=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:n,defaultValue:l,orientation:i="horizontal",dir:o,activationMode:f="automatic",...y}=e,h=(0,s.gm)(o),[v,m]=(0,c.T)({prop:a,onChange:n,defaultProp:l});return(0,p.jsx)(k,{scope:r,baseId:(0,u.M)(),value:v,onValueChange:m,orientation:i,dir:h,activationMode:f,children:(0,p.jsx)(d.WV.div,{dir:h,"data-orientation":i,...y,ref:t})})});b.displayName=f;var x="TabsList",g=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...n}=e,l=m(x,r),o=v(r);return(0,p.jsx)(i.fC,{asChild:!0,...o,orientation:l.orientation,dir:l.dir,loop:a,children:(0,p.jsx)(d.WV.div,{role:"tablist","aria-orientation":l.orientation,...n,ref:t})})});g.displayName=x;var w="TabsTrigger",j=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:l=!1,...o}=e,s=m(w,r),c=v(r),u=C(s.baseId,a),f=V(s.baseId,a),y=a===s.value;return(0,p.jsx)(i.ck,{asChild:!0,...c,focusable:!l,active:y,children:(0,p.jsx)(d.WV.button,{type:"button",role:"tab","aria-selected":y,"aria-controls":f,"data-state":y?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:u,...o,ref:t,onMouseDown:(0,n.M)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(a)}),onKeyDown:(0,n.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(a)}),onFocus:(0,n.M)(e.onFocus,()=>{let e="manual"!==s.activationMode;y||l||!e||s.onValueChange(a)})})})});j.displayName=w;var Z="TabsContent",M=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:l,children:i,...s}=e,c=m(Z,r),u=C(c.baseId,n),f=V(c.baseId,n),y=n===c.value,h=a.useRef(y);return a.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(o.z,{present:l||y,children:({present:r})=>(0,p.jsx)(d.WV.div,{"data-state":y?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:f,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:r&&i})})});function C(e,t){return`${e}-trigger-${t}`}function V(e,t){return`${e}-content-${t}`}M.displayName=Z;var N=b,R=g,z=j,E=M},22813:(e,t,r)=>{r.d(t,{g7:()=>i});var a=r(71159);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var l=r(19510),i=a.forwardRef((e,t)=>{let{children:r,...n}=e,i=a.Children.toArray(r),d=i.find(s);if(d){let e=d.props.children,r=i.map(t=>t!==d?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,l.jsx)(o,{...n,ref:t,children:a.isValidElement(e)?a.cloneElement(e,void 0,r):null})}return(0,l.jsx)(o,{...n,ref:t,children:r})});i.displayName="Slot";var o=a.forwardRef((e,t)=>{let{children:r,...l}=e;if(a.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return a.cloneElement(r,{...function(e,t){let r={...t};for(let a in t){let n=e[a],l=t[a];/^on[A-Z]/.test(a)?n&&l?r[a]=(...e)=>{l(...e),n(...e)}:n&&(r[a]=n):"style"===a?r[a]={...n,...l}:"className"===a&&(r[a]=[n,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props),ref:t?function(...e){return t=>{let r=!1,a=e.map(e=>{let a=n(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():n(e[t],null)}}}}(t,e):e})}return a.Children.count(r)>1?a.Children.only(null):null});o.displayName="SlotClone";var d=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});function s(e){return a.isValidElement(e)&&e.type===d}},60791:(e,t,r)=>{r.d(t,{j:()=>l});let a=e=>"boolean"==typeof e?"".concat(e):0===e?"0":e,n=function(){for(var e,t,r=0,a="";r<arguments.length;)(e=arguments[r++])&&(t=function e(t){var r,a,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t){if(Array.isArray(t))for(r=0;r<t.length;r++)t[r]&&(a=e(t[r]))&&(n&&(n+=" "),n+=a);else for(r in t)t[r]&&(n&&(n+=" "),n+=r)}return n}(e))&&(a&&(a+=" "),a+=t);return a},l=(e,t)=>r=>{var l;if((null==t?void 0:t.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:o}=t,d=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==o?void 0:o[e];if(null===t)return null;let l=a(t)||a(n);return i[e][l]}),s=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return n(e,d,null==t?void 0:null===(l=t.compoundVariants)||void 0===l?void 0:l.reduce((e,t)=>{let{class:r,className:a,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...s}[t]):({...o,...s})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}};