"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2688],{62688:function(e,t,s){s.d(t,{Z:function(){return M}});var a=s(57437),n=s(2265),r=s(11444),c=s(15699),l=s(9753),i=s(52022),d=s(24258),o=s(22023),u=s(5068),x=s(83220),m=s(38133),h=s(51077),f=s(22468),j=s(37505),p=s(6600),w=s(16463),g=s(25224),v=s(80420),N=s(21413),b=s(89733),y=s(55163);let C=()=>{let e=(0,w.useRouter)(),t=(0,r.v9)(e=>e.user),[s,c]=(0,n.useState)([]),l=s.filter(e=>!e.isRead).length;return(0,n.useEffect)(()=>{let e;return(null==t?void 0:t.uid)&&(e=(0,y.e$)(t.uid,e=>{c(e)})),()=>{e&&e()}},[t]),(0,a.jsxs)(N.J2,{children:[(0,a.jsx)(N.xo,{asChild:!0,children:(0,a.jsxs)(b.z,{variant:"ghost",size:"icon",className:"relative rounded-full hover:scale-105 transition-transform",children:[(0,a.jsx)(p.Z,{className:"w-6 h-6 relative rounded-full hover:scale-105 transition-transform"}),l>0&&(0,a.jsx)("span",{className:"absolute top-1 left-9 flex h-4 w-7 items-center justify-center rounded-full bg-red-500 text-white text-xs transform -translate-x-1/2 -translate-y-1/2",children:l})]})}),(0,a.jsxs)(N.yk,{className:"w-[300px] p-4",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"flex justify-between items-center",children:(0,a.jsxs)("p",{className:"ml-auto text-xs text-muted-foreground",children:[l," unread"]})}),0===s.length?(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No notifications available."}):(0,a.jsx)("div",{className:"space-y-2",children:s.map(t=>(0,a.jsxs)("div",{onClick:()=>e.push(t.path),className:"rounded py-4 mb-4 items-start cursor-pointer hover:bg-muted hover:opacity-75 transition",children:[(0,a.jsx)("div",{children:!t.isRead&&(0,a.jsx)("span",{className:"flex h-2 w-2 translate-y-1 rounded-full bg-sky-500"})}),(0,a.jsxs)("div",{className:"space-y-1 px-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)(v.Avatar,{className:"h-6 w-6 text-white flex items-center justify-center p-1 ring-1 ring-white",children:function(e){switch(e){case"Account":return(0,a.jsx)(i.Z,{});case"Settings":return(0,a.jsx)(d.Z,{});case"Document":return(0,a.jsx)(o.Z,{});case"Bid":return(0,a.jsx)(u.Z,{});case"Interview":return(0,a.jsx)(x.Z,{});case"Hire":return(0,a.jsx)(m.Z,{});case"Transaction":return(0,a.jsx)(h.Z,{});case"Verification":return(0,a.jsx)(f.Z,{});case"Ticket":return(0,a.jsx)(j.Z,{});default:return(0,a.jsx)(p.Z,{})}}(t.entity)}),(0,a.jsx)("p",{className:"text-sm font-medium leading-none",children:t.message})]}),(0,a.jsx)("p",{className:"flex justify-end text-xs text-muted-foreground",children:(0,g.Q)(t.timestamp)})]})]},t.id))})]}),s.length>0&&(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)(b.z,{variant:"outline",size:"sm",className:"w-full",onClick:()=>{(0,y.Go)(t.uid)},children:[(0,a.jsx)(f.Z,{className:"mr-2 h-4 w-4"})," Mark all as read"]})})]})]})};var k=s(52922),E=s(8555),S=s(95737),Z=s(72377),I=s(79055),A=s(31590),D=s(80023),L=s(15922),J=s(89859),R=s(47304),T=s(54662),z=e=>{let{connects:t,userId:s}=e,[r,c]=(0,n.useState)("ALL"),[l,i]=(0,n.useState)([]),[d,o]=(0,n.useState)([]),[u,x]=(0,n.useState)(!1),[m,h]=(0,n.useState)(!1),f=(0,n.useCallback)(async()=>{if(!m){h(!0);try{let e=await L.b.get("/token-request/user/".concat(s),{params:{latestConnects:!0}});o(e.data.data),i(e.data.data)}catch(e){console.error("Error fetching data:",e)}finally{h(!1)}}},[s,m]),j=e=>{let t=e.detail;o(e=>{let s=[t,...e.slice(0,2)];return i(s),s})};(0,n.useEffect)(()=>(window.addEventListener("newConnectRequest",j),()=>{window.removeEventListener("newConnectRequest",j)}),[]),(0,n.useEffect)(()=>{u&&f()},[u,f]),(0,n.useEffect)(()=>{i(()=>"ALL"===r?null!=d?d:[]:(null!=d?d:[]).filter(e=>e.status===r))},[d,r]);let p=e=>{c(e)},w=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"2-digit"});return(0,a.jsxs)(T.Vq,{open:u,onOpenChange:x,children:[(0,a.jsx)(T.hg,{asChild:!0,children:(0,a.jsxs)("div",{className:"relative flex items-center justify-end md:justify-center cursor-pointer hover:scale-105 transition-transform",children:[(0,a.jsx)(S.Z,{}),t>=0&&(0,a.jsx)("span",{className:"absolute -top-2 -right-3 bg-red-500 text-white text-[9px] font-bold rounded-full px-2 shadow-md",children:t})]})}),(0,a.jsxs)(T.cZ,{className:"max-w-3xl max-h-[80vh] flex flex-col",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Connects Request"}),(0,a.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,a.jsxs)(A.h_,{children:[(0,a.jsx)(A.$F,{asChild:!0,children:(0,a.jsxs)(b.z,{variant:"outline",size:"sm",className:"h-7 gap-1 w-auto text-sm",children:[(0,a.jsx)(Z.Z,{className:"h-3.5 w-3.5"}),(0,a.jsx)("span",{className:"sr-only sm:not-sr-only",children:"Filter"})]})}),(0,a.jsxs)(A.AW,{align:"center",className:"w-40",children:[(0,a.jsx)(A.Ju,{children:"Status"}),(0,a.jsx)(A.VD,{}),[{key:"ALL",label:"All"},{key:"PENDING",label:"Pending"},{key:"REJECTED",label:"Rejected"},{key:"ACCEPTED",label:"Accepted"}].map(e=>{let{key:t,label:s}=e;return(0,a.jsx)(A.bO,{className:"py-1",checked:r===t,onSelect:()=>p(t),children:s},t)})]})]})}),(0,a.jsxs)("div",{className:"relative overflow-hidden",children:[(0,a.jsx)(R.iA,{className:"min-w-full border rounded-lg shadow-lg",children:(0,a.jsx)(R.xD,{className:"sticky top-0 z-10 bg-muted/40",children:(0,a.jsxs)(R.SC,{className:"text-center",children:[(0,a.jsx)(R.ss,{className:"text-center w-1/3",children:"Connects"}),(0,a.jsx)(R.ss,{className:"text-center w-1/3",children:"Status"}),(0,a.jsx)(R.ss,{className:"text-center w-1/3",children:"Date"})]})})}),(0,a.jsx)(D.x,{className:"max-h-[300px] no-scrollbar pb-10 overflow-y-auto",children:(0,a.jsx)(R.iA,{className:"min-w-full",children:(0,a.jsx)(R.RM,{children:l.length>0?l.map((e,t)=>(0,a.jsxs)(R.SC,{className:"text-center",children:[(0,a.jsx)(R.pj,{className:"font-semibold text-center w-1/3",children:e.amount}),(0,a.jsx)(R.pj,{className:"text-center w-1/3",children:(0,a.jsx)(I.C,{className:(0,J.S)(e.status),children:e.status})}),(0,a.jsx)(R.pj,{className:"text-center w-1/3",children:w(e.dateTime)})]},t)):(0,a.jsx)(R.SC,{children:(0,a.jsx)(R.pj,{colSpan:3,className:"text-center font-semibold text-gray-500",children:"No Transactions Found"})})})})})]})]})]})},M=e=>{let{menuItemsTop:t,menuItemsBottom:s,activeMenu:i,breadcrumbItems:d,conversations:o,activeConversation:u,setActiveConversation:x}=e,m=(0,r.v9)(e=>e.user),[h,f]=(0,n.useState)(0),j=async()=>{try{let e=localStorage.getItem("DHX_CONNECTS"),t=e?parseInt(e):0;isNaN(t)||f(t)}catch(e){console.error("Error fetching connects:",e)}};return(0,n.useEffect)(()=>{(null==m?void 0:m.uid)&&j();let e=()=>j();return window.addEventListener("connectsUpdated",e),()=>{window.removeEventListener("connectsUpdated",e)}},[null==m?void 0:m.uid]),(0,a.jsxs)("header",{className:"sticky top-0 z-30 flex h-14 items-center py-6 gap-4 border-b bg-background px-4 sm:border-0 sm:px-6",children:[(0,a.jsx)(c.Z,{menuItemsTop:t,menuItemsBottom:s,active:i,setActiveConversation:x,conversations:o,activeConversation:u}),(0,a.jsx)(k.Z,{items:d}),(0,a.jsx)("div",{className:"relative ml-auto flex-1 md:grow-0"}),(0,a.jsx)(E.zs,{children:(0,a.jsxs)("div",{className:"relative ml-auto flex-1 md:grow-0",children:[(0,a.jsx)(E.Yi,{asChild:!0,children:(0,a.jsx)(z,{userId:m.uid,connects:h})}),(0,a.jsx)(E.bZ,{className:"w-auto px-4 py-2 text-center font-bold shadow-xl rounded-lg",children:null!==h?"".concat(h?h>=1e6?(h/1e6).toFixed(1).replace(/\.0$/,"")+"M":h>=1e3?(h/1e3).toFixed(1).replace(/\.0$/,"")+"K":h.toString():"0"," rewards Available"):"No rewards yet!"})]})}),(0,a.jsx)(C,{}),(0,a.jsx)(l.Z,{setConnects:f})]})}},55163:function(e,t,s){s.d(t,{Go:function(){return o},Hd:function(){return l},K5:function(){return c},SH:function(){return r},e$:function(){return d},fy:function(){return i}});var a=s(69842),n=s(42361);function r(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"asc",r=(0,a.hJ)(n.db,e),c=(0,a.IO)(r,(0,a.Xo)("timestamp",s));return(0,a.cf)(c,e=>{t(e.docs.map(e=>({id:e.id,...e.data()})))})}function c(e,t,s){let r=(0,a.hJ)(n.db,e),c=(0,a.IO)(r,(0,a.ar)("participants","array-contains",t),(0,a.Xo)("lastMessage.timestamp","desc"));return(0,a.cf)(c,async e=>{s(await Promise.all(e.docs.map(async e=>{let t={id:e.id,...e.data()},s=(null==t?void 0:t.lastMessage)||null;return{...t,lastMessage:s}})))})}async function l(e,t,s,r){try{return await (0,a.i3)(n.db,async c=>{let l=(0,a.JU)(n.db,e,t),i=(0,a.hJ)(n.db,e,t,"messages");c.update(l,{lastMessage:s,timestamp:r});let d=(0,a.JU)(i);c.set(d,{...s,timestamp:r}),console.log("Transaction committed: Message ID - ".concat(d.id))}),"Transaction successful"}catch(e){throw console.error("Transaction failed:",e),e}}async function i(e,t,s){try{let r=(0,a.JU)(n.db,e,t);await (0,a.r7)(r,s),console.log("Document updated with ID:",t)}catch(e){console.error("Error updating document:",e.message)}}let d=(e,t)=>{let s=(0,a.hJ)(n.db,"notifications"),r=(0,a.IO)(s,(0,a.ar)("userId","array-contains",e));return(0,a.cf)(r,e=>{let s=[];e.forEach(e=>{s.push({id:e.id,...e.data()})}),t(s)})},o=async e=>{let t=(0,a.qs)(n.db);try{let s=(0,a.hJ)(n.db,"notifications"),r=(0,a.IO)(s,(0,a.ar)("userId","==",e),(0,a.ar)("isRead","==",!1));(await (0,a.PL)(r)).forEach(e=>{let s=(0,a.JU)(n.db,"notifications",e.id);t.update(s,{isRead:!0})}),await t.commit()}catch(e){throw console.error("Error marking notifications as read:",e),Error("Failed to mark notifications as read")}}},89859:function(e,t,s){s.d(t,{S:function(){return a}});let a=e=>{switch(null==e?void 0:e.toLowerCase()){case"active":case"verified":case"added":return"bg-green-500 text-white";case"pending":return"bg-yellow-500 text-black";case"approved":return"bg-green-500 text-black";case"rejected":return"bg-red-500 text-black";default:return"bg-gray-500 text-white"}}}}]);