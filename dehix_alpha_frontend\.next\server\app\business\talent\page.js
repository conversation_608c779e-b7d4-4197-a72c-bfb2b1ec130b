(()=>{var e={};e.id=1942,e.ids=[1942],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},66118:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>r.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>d}),t(70234),t(54302),t(12523);var a=t(23191),l=t(88716),i=t(37922),r=t.n(i),n=t(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["business",{children:["talent",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,70234)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\talent\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\talent\\page.tsx"],u="/business/talent/page",h={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/business/talent/page",pathname:"/business/talent",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},86596:(e,s,t)=>{Promise.resolve().then(t.bind(t,31698))},40900:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},12070:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},66307:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69669:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},12893:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},32019:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},40617:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},23015:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},69436:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},57671:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},69515:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},98091:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},31698:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>$});var a=t(10326),l=t(17577),i=t(92166),r=t(46319),n=t(29752),c=t(23015),d=t(25842),o=t(83855),u=t(74723),h=t(74064),m=t(27256),x=t(24118),p=t(91664),b=t(29280),f=t(6260),j=t(56627),N=t(51400);let v=m.z.object({label:m.z.string().nonempty("Please select a domain"),skillId:m.z.string().nonempty("Domain ID is required"),experience:m.z.string().nonempty("Please enter your experience").regex(/^\d+$/,"Experience must be a number"),description:m.z.string().nonempty("Please enter description"),visible:m.z.boolean(),status:m.z.string()}),g=({skills:e,onSubmitSkill:s})=>{let t=(0,d.v9)(e=>e.user),[i,r]=(0,l.useState)(!1),[n,c]=(0,l.useState)(!1),{control:m,handleSubmit:g,formState:{errors:y},reset:E,setValue:k,getValues:w,trigger:I}=(0,u.cI)({resolver:(0,h.F)(v),defaultValues:{skillId:"",label:"",experience:"",description:"",visible:!1,status:"ADDED"}}),C=async e=>{c(!0);try{let a=await f.b.post("/business/hire-dehixtalent",{skillId:e.skillId,skillName:e.label,businessId:t.uid,experience:e.experience,description:e.description,status:e.status,visible:e.visible});if(200===a.status){if(a?.data?.data){let t=a.data.data;s({...e,uid:t._id}),E(),r(!1),(0,j.Am)({title:"Talent Added",description:"The Hire Talent has been successfully added."});let l=parseInt(process.env.NEXT_PUBLIC__APP_HIRE_TALENT_COST||"0",10),i=Number(localStorage.getItem("DHX_CONNECTS"))||0;localStorage.setItem("DHX_CONNECTS",Math.max(0,i-l).toString()),window.dispatchEvent(new Event("connectsUpdated"))}else throw Error("Failed to add hire talen")}}catch(e){console.error("Error submitting skill data",e),E(),(0,j.Am)({variant:"destructive",title:"Error",description:"Failed to add hire talent. Please try again."})}finally{c(!1)}};return(0,a.jsxs)(x.Vq,{open:i,onOpenChange:r,children:[a.jsx(x.hg,{asChild:!0,children:(0,a.jsxs)(p.z,{onClick:()=>r(!0),className:"w-full sm:w-auto",children:[a.jsx(o.Z,{className:"mr-2 h-4 w-4"}),"Add Skill"]})}),(0,a.jsxs)(x.cZ,{children:[(0,a.jsxs)(x.fK,{children:[a.jsx(x.$N,{children:"Add Skill"}),a.jsx(x.Be,{children:"Select a skill, enter your experience and monthly pay."})]}),(0,a.jsxs)("form",{onSubmit:g(C),children:[a.jsx("div",{className:"mb-3",children:a.jsx(u.Qr,{control:m,name:"label",render:({field:s})=>(0,a.jsxs)(b.Ph,{value:s.value,onValueChange:t=>{let a=e.find(e=>e.label===t);s.onChange(t),k("skillId",a?._id||"")},children:[a.jsx(b.i4,{children:a.jsx(b.ki,{placeholder:"Select a skill"})}),a.jsx(b.Bw,{children:e.map(e=>a.jsx(b.Ql,{value:e.label,children:e.label},e.label))})]})})}),y.label&&a.jsx("p",{className:"text-red-600",children:y.label.message}),a.jsx("div",{className:"mb-3",children:a.jsx(u.Qr,{control:m,name:"experience",render:({field:e})=>(0,a.jsxs)("div",{className:"col-span-3 relative",children:[a.jsx("input",{type:"number",placeholder:"Experience (years)",min:0,max:50,step:.1,...e,className:"border p-2 rounded mt-0 w-full"}),a.jsx("span",{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"YEARS"})]})})}),y.experience&&a.jsx("p",{className:"text-red-600",children:y.experience.message}),a.jsx(u.Qr,{control:m,name:"description",render:({field:e})=>a.jsx("input",{type:"text",placeholder:"Description",...e,className:"border p-2 rounded mt-2 w-full"})}),y.description&&a.jsx("p",{className:"text-red-600",children:y.description.message}),a.jsx(N.Z,{loading:n,setLoading:c,onSubmit:C,isValidCheck:I,userId:t.uid,buttonText:"Submit",userType:"BUSINESS",requiredConnects:parseInt(process.env.NEXT_PUBLIC__APP_HIRE_TALENT_COST||"0",10),data:w()})]})]})]})},y=m.z.object({label:m.z.string().nonempty("Please select a domain"),domainId:m.z.string().nonempty("Domain ID is required"),experience:m.z.string().nonempty("Please enter your experience").regex(/^\d+$/,"Experience must be a number"),description:m.z.string().nonempty("Please enter description"),visible:m.z.boolean(),status:m.z.string()}),E=({domains:e,onSubmitDomain:s})=>{let t=(0,d.v9)(e=>e.user),[i,r]=(0,l.useState)(!1),[n,c]=(0,l.useState)(!1),{control:m,handleSubmit:v,formState:{errors:g},reset:E,setValue:k,getValues:w,trigger:I}=(0,u.cI)({resolver:(0,h.F)(y),defaultValues:{domainId:"",label:"",experience:"",description:"",visible:!1,status:"ADDED"}}),C=async e=>{c(!0);try{let a=await f.b.post("/business/hire-dehixtalent",{domainId:e.domainId,domainName:e.label,businessId:t.uid,experience:e.experience,description:e.description,status:e.status,visible:e.visible});if(200===a.status){let t=a.data.data;s({...e,uid:t._id}),E(),r(!1),(0,j.Am)({title:"Talent Added",description:"The Talent has been successfully added."});let l=parseInt(process.env.NEXT_PUBLIC__APP_HIRE_TALENT_COST||"0",10),i=Number(localStorage.getItem("DHX_CONNECTS"))||0;localStorage.setItem("DHX_CONNECTS",Math.max(0,i-l).toString()),window.dispatchEvent(new Event("connectsUpdated"))}}catch(e){console.error("Error submitting domain data",e),E(),(0,j.Am)({variant:"destructive",title:"Error",description:"Failed to add talent. Please try again."})}finally{c(!1)}};return(0,a.jsxs)(x.Vq,{open:i,onOpenChange:r,children:[a.jsx(x.hg,{asChild:!0,children:(0,a.jsxs)(p.z,{onClick:()=>r(!0),className:"w-full sm:w-auto",children:[a.jsx(o.Z,{className:"mr-2 h-4 w-4"}),"Add Domain"]})}),(0,a.jsxs)(x.cZ,{children:[(0,a.jsxs)(x.fK,{children:[a.jsx(x.$N,{children:"Add Domain"}),a.jsx(x.Be,{children:"Select a domain, enter your experience and monthly pay."})]}),(0,a.jsxs)("form",{onSubmit:v(C),children:[a.jsx("div",{className:"mb-3",children:a.jsx(u.Qr,{control:m,name:"label",render:({field:s})=>(0,a.jsxs)(b.Ph,{value:s.value,onValueChange:t=>{let a=e.find(e=>e.label===t);s.onChange(t),k("domainId",a?._id||"")},children:[a.jsx(b.i4,{children:a.jsx(b.ki,{placeholder:"Select a domain"})}),a.jsx(b.Bw,{children:e.map(e=>a.jsx(b.Ql,{value:e.label,children:e.label},e._id))})]})})}),g.label&&a.jsx("p",{className:"text-red-600",children:g.label.message}),a.jsx("div",{className:"mb-3",children:a.jsx(u.Qr,{control:m,name:"experience",render:({field:e})=>(0,a.jsxs)("div",{className:"col-span-3 relative",children:[a.jsx("input",{type:"number",placeholder:"Experience (years)",min:0,max:50,step:.1,...e,className:"border p-2 rounded mt-0 w-full"}),a.jsx("span",{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"YEARS"})]})})}),g.experience&&a.jsx("p",{className:"text-red-600",children:g.experience.message}),a.jsx(u.Qr,{control:m,name:"description",render:({field:e})=>a.jsx("input",{type:"text",placeholder:"Description",...e,className:"border p-2 rounded mt-2 w-full"})}),g.description&&a.jsx("p",{className:"text-red-600",children:g.description.message}),a.jsx(N.Z,{loading:n,setLoading:c,onSubmit:C,isValidCheck:I,userId:t.uid,buttonText:"Submit",userType:"BUSINESS",requiredConnects:parseInt(process.env.NEXT_PUBLIC__APP_HIRE_TALENT_COST||"0",10),data:w()})]})]})]})};var k=t(41156),w=t(73326),I=t(38443),C=t(54423);let S=({setFilterSkill:e,setFilterDomain:s})=>{let[t,i]=(0,l.useState)([]),[r,o]=(0,l.useState)([]),[u,h]=(0,l.useState)([]),[m,x]=(0,l.useState)([]),p=(0,d.v9)(e=>e.user);(0,l.useEffect)(()=>{(async()=>{try{let[e,s]=await Promise.all([f.b.get("/skills"),f.b.get("/domain")]);i(e.data?.data||[]),o(s.data?.data||[])}catch(e){console.error("Error fetching skills and domains:",e),(0,j.Am)({variant:"destructive",title:"Error",description:"Failed to load skills and domains. Please try again."})}})()},[]);let b=(0,l.useCallback)(async()=>{try{let t=await f.b.get("/skills");if(t?.data?.data)i(t.data.data);else throw Error("Skills response is null or invalid");let a=await f.b.get("/domain");if(a?.data?.data)o(a.data.data);else throw Error("Domains response is null or invalid");if(p?.uid){let t=await f.b.get("/business/hire-dehixtalent"),a=t.data?.data||{},l=a.filter(e=>e.skillName&&e.visible).map(e=>({_id:e.skillId,label:e.skillName})),r=a.filter(e=>e.domainName&&e.visible).map(e=>({_id:e.domainId,label:e.domainName}));e(l),s(r);let n=Object.values(a).map(e=>({uid:e._id,label:e.skillName||e.domainName||"N/A",experience:e.experience||"N/A",description:e.description||"N/A",status:e.status,visible:e.visible}));h(n),x(n.map(e=>e.visible));let c=a.filter(e=>e.skillName).map(e=>({_id:e.skillId,label:e.skillName})),d=a.filter(e=>e.domainName).map(e=>({_id:e.domainId,label:e.domainName})),u=await f.b.get("/skills");if(u?.data?.data){let e=u.data.data.filter(e=>!c.some(s=>s._id===e._id));i(e)}else throw Error("Skills response is null or invalid");let m=await f.b.get("/domain");if(m?.data?.data){let e=m.data.data.filter(e=>!d.some(s=>s._id===e._id));o(e)}else throw Error("Domains response is null or invalid")}}catch(e){console.error("Error fetching user data:",e),(0,j.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}},[p?.uid,e,s]);(0,l.useEffect)(()=>{b()},[b]);let N=async(e,s,t)=>{try{let a=await f.b.patch(`/business/hire-dehixtalent/${t}`,{visible:s});if(200===a.status){let t=[...m];t[e]=s,x(t),await b()}}catch(e){console.error("Error updating visibility:",e),(0,j.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}};return(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsxs)("div",{className:"mb-8 ",children:[a.jsx("h1",{className:"text-3xl font-bold",children:" Hire Talent "}),a.jsx("p",{className:"text-gray-400 mt-2",children:"Help us understand the skills and domain you are looking for in potential hires.Enter the required experience and a short description to refine your talent search."})]}),a.jsx("div",{className:"",children:(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("div",{className:"flex items-center justify-between mb-4",children:(0,a.jsxs)("div",{className:"flex space-x-4",children:[a.jsx(g,{skills:t,onSubmitSkill:e=>{h([...u,{...e,status:"ADDED",visible:!1}]),x([...m,!1])}}),a.jsx(E,{domains:r,onSubmitDomain:e=>{h([...u,{...e,status:"ADDED",visible:!1}]),x([...m,!1])}})]})}),a.jsx(n.Zb,{className:"h-[65.4vh] overflow-auto no-scrollbar",children:(0,a.jsxs)(k.iA,{className:"w-full",children:[a.jsx(k.xD,{className:"sticky top-0 z-10",children:(0,a.jsxs)(k.SC,{children:[a.jsx(k.ss,{children:"Label"}),a.jsx(k.ss,{children:"Experience"}),a.jsx(k.ss,{children:"Description"}),a.jsx(k.ss,{children:"Status"}),a.jsx(k.ss,{})]})}),a.jsx(k.RM,{children:u.length>0?u.map((e,s)=>(0,a.jsxs)(k.SC,{children:[a.jsx(k.pj,{children:e.label}),(0,a.jsxs)(k.pj,{children:[e.experience," years"]}),a.jsx(k.pj,{children:e.description}),a.jsx(k.pj,{children:a.jsx(I.C,{className:(0,C.S)(e.status),children:e.status.toUpperCase()})}),a.jsx(k.pj,{children:a.jsx(w.r,{checked:m[s],onCheckedChange:t=>e.uid?N(s,t,e.uid):console.error("UID missing for item",e)})})]},s)):a.jsx("tr",{children:(0,a.jsxs)("td",{colSpan:5,className:"text-center py-10",children:[a.jsx(c.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,a.jsxs)("p",{className:"text-gray-500",children:["No data available.",a.jsx("br",{})," This feature will be available soon.",a.jsx("br",{}),"Here you can directly hire freelancer for different roles."]})]})})})]})})]})})]})},D=(0,t(80851).Z)("Expand",[["path",{d:"m21 21-6-6m6 6v-4.8m0 4.8h-4.8",key:"1c15vz"}],["path",{d:"M3 16.2V21m0 0h4.8M3 21l6-6",key:"1fsnz2"}],["path",{d:"M21 7.8V3m0 0h-4.8M21 3l-6 6",key:"hawz9i"}],["path",{d:"M3 7.8V3m0 0h4.8M3 3l6 6",key:"u9ee12"}]]);var A=t(12893),P=t(32019),_=t(69436),T=t(77506),Z=t(90434),V=t(56556),L=t(82631),R=t(3594);function q({isLoading:e,hasMore:s,next:t,threshold:i=1,root:r=null,rootMargin:n="0px",reverse:c,children:d}){let o=l.useRef(),u=l.useCallback(a=>{let l=i;(i<0||i>1)&&(console.warn("threshold should be between 0 and 1. You are exceed the range. will use default value: 1"),l=1),!e&&(o.current&&o.current.disconnect(),a&&(o.current=new IntersectionObserver(e=>{e[0].isIntersecting&&s&&t()},{threshold:l,root:r,rootMargin:n}),o.current.observe(a)))},[s,e,t,i,r,n]),h=l.useMemo(()=>l.Children.toArray(d),[d]);return a.jsx(a.Fragment,{children:h.map((e,s)=>{if(!l.isValidElement(e))return e;let t=c?0===s:s===h.length-1;return l.cloneElement(e,{ref:t?u:null})})})}var O=t(58285),M=t(76066),z=t(39958),F=t(94019);let H=({skillDomainData:e=[],currSkills:s=[],handleAddSkill:t,handleDeleteSkill:l,handleAddToLobby:i,talent:r,setTmpSkill:n,tmpSkill:c,open:d,setOpen:u,isLoading:h})=>a.jsx(x.Vq,{open:d,onOpenChange:u,children:(0,a.jsxs)(x.cZ,{className:"max-w-md",children:[a.jsx(x.fK,{children:a.jsx(x.$N,{children:"Add Skills to Lobby"})}),(0,a.jsxs)("div",{className:" mt-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(b.Ph,{value:c||"",onValueChange:e=>n(e),children:[a.jsx(b.i4,{className:"w-full",children:a.jsx(b.ki,{placeholder:"Select skill",children:c||null})}),a.jsx(b.Bw,{children:e.filter(e=>!s.some(s=>s.name===e.label)).map((e,s)=>a.jsx(b.Ql,{value:e.label,children:e.label},s))})]}),a.jsx(p.z,{variant:"outline",size:"icon",onClick:()=>{t(),n("")},children:a.jsx(o.Z,{className:"h-4 w-4"})})]}),a.jsx("div",{className:"flex flex-wrap gap-2 mt-4",children:s.map((e,s)=>(0,a.jsxs)(I.C,{className:"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1",children:[e.name,a.jsx("button",{type:"button",onClick:()=>l(e.name),className:"ml-2 text-red-500 hover:text-red-700",children:a.jsx(F.Z,{className:"h-4 w-4"})})]},s))})]}),a.jsx(x.cN,{className:"mt-4",children:a.jsx(p.z,{onClick:()=>{i(r.freelancer_id)},className:"w-full text-sm py-1 px-2 text-black rounded-md",type:"submit",children:h?a.jsx(T.Z,{className:"animate-spin"}):"Save"})})]})}),B=["left"],U=({skillFilter:e,domainFilter:s,skillDomainFormProps:t})=>{let[i,r]=(0,l.useState)([]),[c,o]=(0,l.useState)([]),u=(0,l.useRef)(0),[h,m]=(0,l.useState)(!1),[x,b]=(0,l.useState)(!0),N=(0,l.useRef)(!1),[v,g]=(0,l.useState)([]),[y,E]=(0,l.useState)([]),k=(0,d.v9)(e=>e.user),[w,C]=(0,l.useState)([]),[,S]=(0,l.useState)([]),[F]=(0,l.useState)(new Set),[U,G]=(0,l.useState)(),[$,Q]=(0,l.useState)([]),[X,J]=(0,l.useState)(""),[W,K]=(0,l.useState)(!1),[Y,ee]=(0,l.useState)(!1),es=()=>{console.log(X),X&&!$.some(e=>e.name===X)&&(Q([...$,{name:X,level:"",experience:"",interviewStatus:z.sB.PENDING,interviewInfo:"",interviewerRating:0}]),J(""))};(0,l.useEffect)(()=>{(async()=>{try{let[e,s]=await Promise.all([f.b.get("/skills"),f.b.get("/domain")]);g(e.data?.data||[]),E(s.data?.data||[])}catch(e){console.error("Error fetching skills and domains:",e),(0,j.Am)({variant:"destructive",title:"Error",description:"Failed to load skills and domains. Please try again."})}})()},[]);let et=(0,l.useCallback)(async()=>{try{let e=await f.b.get("/skills");if(e?.data?.data)g(e.data.data);else throw Error("Skills response is null or invalid");let s=await f.b.get("/domain");if(s?.data?.data)E(s.data.data);else throw Error("Domains response is null or invalid");if(k?.uid){let e=await f.b.get("/business/hire-dehixtalent"),s=e.data?.data||{},a=s.filter(e=>e.skillName&&e.visible).map(e=>({_id:e.skillId,label:e.skillName})),l=s.filter(e=>e.domainName&&e.visible).map(e=>({_id:e.domainId,label:e.domainName}));t?.skillFilter(a),t?.domainFilter(l);let i=Object.values(s).map(e=>({uid:e._id,label:e.skillName||e.domainName||"N/A",experience:e.experience||"N/A",description:e.description||"N/A",status:e.status,visible:e.visible,talentId:e.skillId||e.domainId}));console.log(i),C(i),S(i.map(e=>e.visible));let r=s.filter(e=>e.skillName).map(e=>({_id:e.skillId,label:e.skillName})),n=s.filter(e=>e.domainName).map(e=>({_id:e.domainId,label:e.domainName})),c=await f.b.get("/skills");if(c?.data?.data){let e=c.data.data.filter(e=>!r.some(s=>s._id===e._id));g(e)}else throw Error("Skills response is null or invalid");let d=await f.b.get("/domain");if(d?.data?.data){let e=d.data.data.filter(e=>!n.some(s=>s._id===e._id));E(e)}else throw Error("Domains response is null or invalid")}}catch(e){console.error("Error fetching user data:",e),e.response&&404===e.response.status||(0,j.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}},[k?.uid,t]);(0,l.useEffect)(()=>{et()},[et]);let ea=e=>{Q($.filter(s=>s.name!==e))},el=(0,l.useCallback)(async(e=u.current,s=!1)=>{if(!N.current)try{N.current=!0,m(!0);let t=await f.b.get("freelancer/dehixtalent",{params:{limit:O.Dz.BATCH,skip:e}}),a=t?.data?.data||[];if(a.length<O.Dz.BATCH&&b(!1),t?.data?.data)o(e=>s?a:[...e,...a]),u.current=s?O.Dz.BATCH:u.current+O.Dz.BATCH;else throw Error("Fail to fetch data")}catch(e){console.error("Error fetching talent data",e),e.response&&404===e.response.status?b(!1):(0,j.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}finally{m(!1),N.current=!1}},[]),ei=(0,l.useCallback)(()=>{o([]),u.current=0,b(!0),el(0,!0)},[el]);(0,l.useEffect)(()=>{ei()},[ei]),(0,l.useEffect)(()=>{r(c.filter(t=>"all"==e&&"all"==s||"all"==e&&s==t.dehixTalent.domainName||e==t.dehixTalent.skillName&&"all"==s||e==t.dehixTalent.skillName||s==t.dehixTalent.domainName))},[e,s,c]);let er=async e=>{let s=[],t=[];if($.forEach(e=>{let a=w.find(s=>s.label===e.name);a?.talentId&&a?.uid&&(s.push(a.talentId),t.push(a.uid))}),0===s.length||0===t.length){(0,j.Am)({variant:"destructive",title:"No Skills Selected",description:"Please add some skills before adding to lobby."});return}ee(!0);try{let a=await f.b.put("business/hire-dehixtalent/add_into_lobby",{freelancerId:e,dehixTalentId:s,hireDehixTalent_id:t});200===a.status&&((0,j.Am)({title:"Success",description:"Freelancer added to lobby"}),Q([]))}catch(e){(0,j.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}finally{ee(!1)}};return(0,a.jsxs)("div",{className:"flex flex-wrap mt-4 justify-center gap-4",children:[i.map(e=>{let s=e.dehixTalent,t=e.education,l=e.projects,i=s.skillName?"Skill":"Domain",r=s.skillName||s.domainName||"N/A",c=F.has(s._id);return console.log(e.freelancer_id),(0,a.jsxs)(n.Zb,{className:"w-full sm:w-[350px] lg:w-[450px]",children:[(0,a.jsxs)(n.Ol,{className:"flex flex-row items-center gap-4",children:[(0,a.jsxs)(R.Avatar,{className:"h-14 w-14",children:[a.jsx(R.AvatarImage,{src:e.profilePic||"/default-avatar.png"}),a.jsx(R.AvatarFallback,{children:"JD"})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[a.jsx(n.ll,{children:e.Name||"Unknown"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:e.userName})]})]}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("span",{className:"text-sm font-semibold",children:i}),a.jsx(I.C,{children:r})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("span",{className:"text-sm font-semibold",children:"Experience"}),(0,a.jsxs)(I.C,{children:[s.experience," years"]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("span",{className:"text-sm font-semibold",children:"Monthly Pay"}),(0,a.jsxs)(I.C,{children:["$",s.monthlyPay]})]}),c&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("span",{className:"text-sm font-semibold",children:"Status"}),a.jsx(I.C,{variant:"default",children:"Invited"})]})]}),a.jsx("div",{className:"py-4",children:B.map(i=>(0,a.jsxs)(M.yo,{children:[a.jsx(M.aM,{asChild:!0,children:a.jsx(p.z,{className:"w-full text-sm  rounded-md",children:"View"})}),(0,a.jsxs)(M.ue,{side:i,className:"overflow-y-auto no-scrollbar max-h-[100vh]",children:[a.jsx(M.Tu,{children:(0,a.jsxs)(M.bC,{className:"flex items-center justify-between text-lg font-bold py-4",children:[a.jsx("span",{className:"text-center flex-1",children:"View Talent Details"}),(0,a.jsxs)(L.u,{children:[a.jsx(L.aJ,{asChild:!0,children:a.jsx(Z.default,{href:`/business/freelancerProfile/${e.freelancer_id}`,passHref:!0,children:a.jsx(D,{className:"w-6 h-6 cursor-pointer text-gray-600 "})})}),a.jsx(L._v,{side:"top",children:"Expand"})]})]})}),a.jsx("div",{className:"grid gap-4 py-2",children:a.jsx("div",{className:"w-full text-center",children:(0,a.jsxs)("div",{className:"items-center",children:[(0,a.jsxs)(R.Avatar,{className:"h-20 w-20 mx-auto mb-4 rounded-full border-4 border-white hover:border-white transition-all duration-300",children:[a.jsx(R.AvatarImage,{src:e.profilePic||"/default-avatar.png"}),a.jsx(R.AvatarFallback,{children:"Unable to load"})]}),(0,a.jsxs)("div",{className:"text-lg font-bold",children:[" ",e.Name]}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-4 mt-4",children:[a.jsx("a",{href:e.Github||"#",target:e.Github?"_blank":"_self",rel:"noopener noreferrer",className:`flex items-center gap-2 transition-all ${e.Github?"text-blue-500 hover:text-blue-700":"text-gray-500 cursor-default"}`,children:a.jsx(A.Z,{className:`w-5 h-5 ${e.Github?"text-blue-500":"text-gray-500"}`})}),a.jsx("a",{href:e.LinkedIn||"#",target:e.LinkedIn?"_blank":"_self",rel:"noopener noreferrer",className:`flex items-center gap-2 transition-all ${e.LinkedIn?"text-blue-500 hover:text-blue-700":"text-gray-500 cursor-default"}`,children:a.jsx(P.Z,{className:`w-5 h-5 ${e.LinkedIn?"text-blue-500":"text-gray-500"}`})})]})]})})}),a.jsx("table",{className:"min-w-full table-auto border-collapse ",children:a.jsx("tbody",{children:(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"border-b px-4 py-2 font-medium",children:"Username"}),a.jsx("td",{className:"border-b px-4 py-2",children:e.userName||"N/A"})]})})}),(0,a.jsxs)(V.UQ,{type:"multiple",className:"w-full",children:[(0,a.jsxs)(V.Qd,{value:"education",children:[a.jsx(V.o4,{className:"w-full flex justify-between px-4 py-2 !no-underline focus:ring-0 focus:outline-none",children:"Education"}),a.jsx(V.vF,{className:"p-4 transition-all duration-300",children:t&&Object.values(t).length>0?Object.values(t).map(e=>(0,a.jsxs)("div",{className:"mb-2 p-2 border border-gray-300 rounded-lg",children:[a.jsx("p",{className:"text-sm font-semibold",children:e.degree}),a.jsx("p",{className:"text-xs text-gray-600",children:e.universityName}),a.jsx("p",{className:"text-xs text-gray-500",children:e.fieldOfStudy}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[new Date(e.startDate).toLocaleDateString()," ","-"," ",new Date(e.endDate).toLocaleDateString()]}),(0,a.jsxs)("p",{className:"text-xs text-gray-700",children:["Grade: ",e.grade]})]},e._id)):"No education details available."})]}),(0,a.jsxs)(V.Qd,{value:"projects",children:[a.jsx(V.o4,{className:"w-full flex justify-between px-4 py-2 !no-underline focus:ring-0 focus:outline-none",children:"Projects"}),a.jsx(V.vF,{className:"p-4 transition-all duration-300",children:l&&Object.values(l).length>0?Object.values(l).map(e=>(0,a.jsxs)("div",{className:"mb-2 p-2 border border-gray-300 rounded-lg",children:[a.jsx("p",{className:"text-sm font-semibold",children:e.projectName}),(0,a.jsxs)("p",{className:"text-xs text-gray-600",children:["Role: ",e.role]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Tech Used:"," ",e.techUsed.length>0?e.techUsed.join(", "):"N/A"]}),e.githubLink&&(0,a.jsxs)("a",{href:e.githubLink,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-xs text-blue-500 hover:underline",children:[a.jsx(A.Z,{className:"w-4 h-4"}),"View on GitHub"]})]},e._id)):a.jsx("p",{className:"text-sm text-gray-500",children:"No projects available."})})]}),(0,a.jsxs)(V.Qd,{value:"skills",children:[a.jsx(V.o4,{className:"w-full flex justify-between px-4 py-2 !no-underline focus:ring-0 focus:outline-none",children:"Skills"}),a.jsx(V.vF,{className:"p-4 transition-all duration-300",children:s.skillName?s.skillName:"N/A"})]}),(0,a.jsxs)(V.Qd,{value:"domain",children:[a.jsx(V.o4,{className:"w-full flex justify-between px-4 py-2 !no-underline focus:ring-0 focus:outline-none",children:"Domain"}),a.jsx(V.vF,{className:"p-4 transition-all duration-300",children:s.domainName?s.domainName:"N/A"})]}),(0,a.jsxs)(V.Qd,{value:"experience",children:[a.jsx(V.o4,{className:"w-full flex justify-between px-4 py-2 !no-underline focus:ring-0 focus:outline-none",children:"Experience"}),a.jsx(V.vF,{className:"p-4 transition-all duration-300",children:s.experience?`${s.experience} years`:"N/A"})]})]}),(0,a.jsxs)(p.z,{onClick:()=>{K(!0),G(e)},className:`w-full mt-4 ${c?"bg-blue-600 hover:bg-blue-700":"bg-primary hover:bg-primary/90"}`,children:[a.jsx(_.Z,{className:"mr-2 h-4 w-4"}),"Add to Lobby"]})]})]},i))}),(0,a.jsxs)(p.z,{onClick:()=>{K(!0),G(e)},className:`w-full ${c?"bg-blue-600 hover:bg-blue-700":"bg-primary hover:bg-primary/90"}`,children:[a.jsx(_.Z,{className:"mr-2 h-4 w-4"}),"Add to Lobby"]})]})}),U&&a.jsx(H,{skillDomainData:w,currSkills:$,handleAddSkill:es,handleDeleteSkill:ea,handleAddToLobby:er,talent:U,tmpSkill:X,setTmpSkill:J,open:W,setOpen:K,isLoading:Y})]},s._id)}),a.jsx(q,{hasMore:x,isLoading:h,next:el,threshold:1,children:h&&a.jsx(T.Z,{className:"my-4 h-8 w-8 animate-spin"})})]})};var G=t(40588);function $(){let[e,s]=(0,l.useState)("all"),[t,c]=(0,l.useState)("all"),[d]=(0,l.useState)(),[o,u]=(0,l.useState)([]),[h,m]=(0,l.useState)([]);return(0,a.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40 overflow-auto",children:[a.jsx(i.Z,{menuItemsTop:r.yn,menuItemsBottom:r.$C,active:"Dehix Talent"}),(0,a.jsxs)("div",{className:"flex flex-col sm:gap-8  sm:py-0 sm:pl-14",children:[a.jsx(G.Z,{menuItemsTop:r.yn,menuItemsBottom:r.$C,activeMenu:"Dehix Talent",breadcrumbItems:[{label:"Business",link:"/dashboard/business"},{label:"HireTalent",link:"#"}]}),(0,a.jsxs)("main",{className:"flex-1 gap-4 p-4 sm:px-6 sm:py-0 md:gap-8 lg:grid lg:grid-cols-3 lg:items-start xl:grid-cols-3",children:[a.jsx("div",{className:"space-y-6 lg:col-span-2",children:a.jsx(S,{setFilterSkill:u,setFilterDomain:m})}),(0,a.jsxs)("div",{className:"space-y-6 lg:mt-0 mt-6",children:[a.jsx(n.ll,{className:"group flex items-center gap-2 text-2xl",children:"Talent"}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)(b.Ph,{onValueChange:s,value:e,children:[a.jsx(b.i4,{className:"w-full",children:a.jsx(b.ki,{placeholder:"Select Skill"})}),(0,a.jsxs)(b.Bw,{children:[a.jsx(b.Ql,{value:"all",children:"All Skills"}),o?.map(e=>a.jsx(b.Ql,{value:e.label,children:e.label},e._id))]})]}),(0,a.jsxs)(b.Ph,{onValueChange:c,value:t,children:[a.jsx(b.i4,{className:"w-full",children:a.jsx(b.ki,{placeholder:"Select Domain"})}),(0,a.jsxs)(b.Bw,{children:[a.jsx(b.Ql,{value:"all",children:"All Domains"}),h?.map(e=>a.jsx(b.Ql,{value:e.label,children:e.label},e._id))]})]})]}),a.jsx("div",{className:"lg:h-[75vh] h-[59vh] rounded-lg  overflow-y-scroll no-scrollbar",children:a.jsx(U,{skillFilter:e,domainFilter:t,skillDomainFormProps:d})})]})]})]})]})}},51400:(e,s,t)=>{"use strict";t.d(s,{Z:()=>o});var a=t(10326),l=t(17577),i=t(56627),r=t(6260),n=t(91664),c=t(24118),d=t(41190);function o({loading:e,setLoading:s,onSubmit:t,isValidCheck:o,userId:u,buttonText:h,userType:m,requiredConnects:x,data:p}){let[b,f]=(0,l.useState)(!1),[j,N]=(0,l.useState)(!1),v=parseInt(localStorage.getItem("DHX_CONNECTS")||"0",10),g=async()=>{try{await r.b.post("/token-request",{userId:u,userType:m,amount:"100",status:"PENDING",dateTime:new Date().toISOString()}),(0,i.Am)({title:"Success!",description:"Request to add connects has been sent.",duration:3e3});let e={userId:u,amount:100,status:"PENDING",dateTime:new Date().toISOString()};window.dispatchEvent(new CustomEvent("newConnectRequest",{detail:e}))}catch(e){console.error("Error requesting more connects:",e.response),(0,i.Am)({variant:"destructive",title:"Error!",description:"Failed to request connects. Try again!",duration:3e3})}},y=async()=>{let e=await o();if(console.log(e),e){if(console.log(x),v<x){N(!0),f(!0);return}N(!1),f(!0)}},E=async()=>{if(console.log(j),!j){s(!0);try{p?await t(p):await t(),f(!1)}catch(e){console.error("Error deducting connects:",e),alert("Failed to deduct connects. Try again!")}finally{s(!1)}}};return(0,a.jsxs)("div",{children:[a.jsx(n.z,{type:"button",className:"lg:col-span-2 w-full xl:col-span-2 mt-4",disabled:e,onClick:y,children:e?"Loading...":h}),a.jsx(c.Vq,{open:b,onOpenChange:f,children:a.jsx(c.cZ,{children:j?(0,a.jsxs)(a.Fragment,{children:[a.jsx(c.$N,{children:"Insufficient Connects"}),(0,a.jsxs)(c.Be,{children:["You don't have enough connects to create a project.",a.jsx("br",{}),"Please"," ",a.jsx("span",{className:"text-blue-600 font-bold cursor-pointer",onClick:g,children:"Request Connects"})," ","to proceed."]}),a.jsx(c.cN,{children:a.jsx(n.z,{variant:"outline",onClick:()=>f(!1),children:"Close"})})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(c.$N,{children:"Confirm Deduction"}),a.jsx(d.I,{type:"text",value:x,disabled:!0}),(0,a.jsxs)(c.Be,{children:["Creating this project will deduct"," ",(0,a.jsxs)("span",{className:"font-extrabold",children:[" ",x," connects"]}),". Do you want to proceed?"]}),(0,a.jsxs)(c.cN,{children:[a.jsx(n.z,{variant:"outline",onClick:()=>f(!1),children:"Cancel"}),a.jsx(n.z,{onClick:E,disabled:e,children:e?"Processing...":"Confirm"})]})]})})})]})}},73326:(e,s,t)=>{"use strict";t.d(s,{r:()=>n});var a=t(10326),l=t(17577),i=t(41959),r=t(51223);let n=l.forwardRef(({className:e,...s},t)=>a.jsx(i.fC,{className:(0,r.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:t,children:a.jsx(i.bU,{className:(0,r.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));n.displayName=i.fC.displayName},46319:(e,s,t)=>{"use strict";t.d(s,{$C:()=>f,Ne:()=>j,yn:()=>b});var a=t(10326),l=t(95920),i=t(57671),r=t(94909),n=t(12070),c=t(66307),d=t(69669),o=t(40617),u=t(69515),h=t(88378),m=t(40900),x=t(98091),p=t(46226);let b=[{href:"#",icon:a.jsx(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:a.jsx(l.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:a.jsx(i.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:a.jsx(r.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:a.jsx(r.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:a.jsx(n.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:a.jsx(c.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:a.jsx(d.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:a.jsx(o.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:a.jsx(u.Z,{className:"h-5 w-5"}),label:"Notes"}],f=[{href:"/business/settings/business-info",icon:a.jsx(h.Z,{className:"h-5 w-5"}),label:"Settings"}],j=[{href:"#",icon:a.jsx(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:a.jsx(l.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:a.jsx(u.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:a.jsx(m.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:a.jsx(x.Z,{className:"h-5 w-5"}),label:"Trash"}]},58285:(e,s,t)=>{"use strict";var a,l,i,r,n,c,d,o,u,h,m;t.d(s,{Dy:()=>h,Dz:()=>x});let x={BATCH:3};(function(e){e.PROJECT_HIRING="PROJECT_HIRING",e.SKILL_INTERVIEW="SKILL_INTERVIEW",e.DOMAIN_INTERVIEW="DOMAIN_INTERVIEW",e.TALENT_INTERVIEW="TALENT_INTERVIEW"})(a||(a={})),function(e){e.ADDED="Added",e.APPROVED="Approved",e.CLOSED="Closed",e.COMPLETED="Completed"}(l||(l={})),function(e){e.ACTIVE="Active",e.IN_ACTIVE="Inactive",e.NOT_VERIFIED="Not Verified"}(i||(i={})),function(e){e.BUSINESS="Business",e.FREELANCER="Freelancer",e.BOTH="Both"}(r||(r={})),function(e){e.ACTIVE="Active",e.IN_ACTIVE="Inactive"}(n||(n={})),function(e){e.APPLIED="APPLIED",e.NOT_APPLIED="NOT_APPLIED",e.APPROVED="APPROVED",e.FAILED="FAILED",e.STOPPED="STOPPED",e.REAPPLIED="REAPPLIED"}(c||(c={})),function(e){e.PENDING="Pending",e.ACCEPTED="Accepted",e.REJECTED="Rejected",e.PANEL="Panel",e.INTERVIEW="Interview"}(d||(d={})),function(e){e.ACTIVE="ACTIVE",e.INACTIVE="INACTIVE",e.ARCHIVED="ARCHIVED"}(o||(o={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.INACTIVE="Inactive",e.CLOSED="Closed"}(u||(u={})),function(e){e.FREELANCER="FREELANCER",e.ADMIN="ADMIN",e.BUSINESS="BUSINESS"}(h||(h={})),function(e){e.CREATED="Created",e.CLOSED="Closed",e.ACTIVE="Active"}(m||(m={}))},39958:(e,s,t)=>{"use strict";var a,l,i;t.d(s,{cd:()=>a,d8:()=>r,kJ:()=>l,sB:()=>i}),function(e){e.Mastery="Mastery",e.Proficient="Proficient",e.Beginner="Beginner"}(a||(a={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.REJECTED="Rejected",e.COMPLETED="Completed"}(l||(l={})),function(e){e.ACTIVE="ACTIVE",e.PENDING="PENDING",e.REJECTED="REJECTED",e.COMPLETED="COMPLETED"}(i||(i={}));let r={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},70234:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>r,__esModule:()=>i,default:()=>n});var a=t(68570);let l=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\business\talent\page.tsx`),{__esModule:i,$$typeof:r}=l;l.default;let n=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\business\talent\page.tsx#default`)},41959:(e,s,t)=>{"use strict";t.d(s,{bU:()=>E,fC:()=>y});var a=t(17577),l=t(82561),i=t(48051),r=t(93095),n=t(52067),c=t(53405),d=t(2566),o=t(77335),u=t(10326),h="Switch",[m,x]=(0,r.b)(h),[p,b]=m(h),f=a.forwardRef((e,s)=>{let{__scopeSwitch:t,name:r,checked:c,defaultChecked:d,required:h,disabled:m,value:x="on",onCheckedChange:b,...f}=e,[j,N]=a.useState(null),y=(0,i.e)(s,e=>N(e)),E=a.useRef(!1),k=!j||!!j.closest("form"),[w=!1,I]=(0,n.T)({prop:c,defaultProp:d,onChange:b});return(0,u.jsxs)(p,{scope:t,checked:w,disabled:m,children:[(0,u.jsx)(o.WV.button,{type:"button",role:"switch","aria-checked":w,"aria-required":h,"data-state":g(w),"data-disabled":m?"":void 0,disabled:m,value:x,...f,ref:y,onClick:(0,l.M)(e.onClick,e=>{I(e=>!e),k&&(E.current=e.isPropagationStopped(),E.current||e.stopPropagation())})}),k&&(0,u.jsx)(v,{control:j,bubbles:!E.current,name:r,value:x,checked:w,required:h,disabled:m,style:{transform:"translateX(-100%)"}})]})});f.displayName=h;var j="SwitchThumb",N=a.forwardRef((e,s)=>{let{__scopeSwitch:t,...a}=e,l=b(j,t);return(0,u.jsx)(o.WV.span,{"data-state":g(l.checked),"data-disabled":l.disabled?"":void 0,...a,ref:s})});N.displayName=j;var v=e=>{let{control:s,checked:t,bubbles:l=!0,...i}=e,r=a.useRef(null),n=(0,c.D)(t),o=(0,d.t)(s);return a.useEffect(()=>{let e=r.current,s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(n!==t&&s){let a=new Event("click",{bubbles:l});s.call(e,t),e.dispatchEvent(a)}},[n,t,l]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...i,tabIndex:-1,ref:r,style:{...e.style,...o,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function g(e){return e?"checked":"unchecked"}var y=f,E=N}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,6686,4736,6499,8066,588],()=>t(66118));module.exports=a})();