(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5916],{39238:function(e,s,l){Promise.resolve().then(l.bind(l,52142))},25912:function(e,s,l){"use strict";l.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,l(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},29406:function(e,s,l){"use strict";l.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,l(33480).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},52142:function(e,s,l){"use strict";l.r(s),l.d(s,{default:function(){return m}});var t=l(57437),a=l(2265),r=l(72377),n=l(29406),i=l(31590),c=l(64797),o=l(59282),h=l(89733),d=l(62688);function m(){let[e,s]=(0,a.useState)("All");return(0,t.jsxs)("div",{className:"flex min-h-screen w-full",children:[(0,t.jsx)(c.Z,{menuItemsTop:o.y,menuItemsBottom:o.$,active:"History"}),(0,t.jsxs)("div",{className:"flex flex-col sm:gap-4 mb-8 sm:py-0 sm:pl-14 w-full",children:[(0,t.jsx)(d.Z,{breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Interview",link:"/freelancer/interview/profile"},{label:"History Interviews",link:"#"}],menuItemsTop:o.y,menuItemsBottom:o.$,activeMenu:"History"}),(0,t.jsxs)("div",{className:"ml-10",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"History Interviews"}),(0,t.jsx)("p",{className:"text-gray-400 mt-2",children:"Review your past interviews and reflect on your progress and experiences."})]}),(0,t.jsxs)("div",{className:"flex flex-1 items-start gap-4 p-2 sm:px-6 sm:py-0 md:gap-8 lg:flex-col xl:flex-col pt-2 pl-4 sm:pt-4 sm:pl-6 md:pt-6 md:pl-8",children:[(0,t.jsxs)(i.h_,{children:[(0,t.jsx)(i.$F,{asChild:!0,children:(0,t.jsxs)(h.z,{variant:"outline",size:"sm",className:"h-7 gap-1 text-sm",children:[(0,t.jsx)(r.Z,{className:"h-3.5 w-3.5"}),(0,t.jsx)("span",{className:"sr-only sm:not-sr-only",children:"Filter"})]})}),(0,t.jsxs)(i.AW,{align:"end",children:[(0,t.jsx)(i.Ju,{children:"Filter by"}),(0,t.jsx)(i.VD,{}),(0,t.jsx)(i.bO,{checked:"All"===e,onSelect:()=>s("All"),children:"All"}),(0,t.jsx)(i.bO,{checked:"Skills"===e,onSelect:()=>s("Skills"),children:"Skills"}),(0,t.jsx)(i.bO,{checked:"Domain"===e,onSelect:()=>s("Domain"),children:"Domain"})]})]}),(0,t.jsxs)("div",{className:"text-center py-10 w-[90vw] mt-10",children:[(0,t.jsx)(n.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,t.jsx)("p",{className:"text-gray-500",children:"No Inverview Scheduled for you."})]})]})]})]})}},59282:function(e,s,l){"use strict";l.d(s,{$:function(){return x},y:function(){return m}});var t=l(57437),a=l(11005),r=l(38133),n=l(33480);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,n.Z)("ListVideo",[["path",{d:"M12 12H3",key:"18klou"}],["path",{d:"M16 6H3",key:"1wxfjs"}],["path",{d:"M12 18H3",key:"11ftsu"}],["path",{d:"m16 12 5 3-5 3v-6Z",key:"zpskkp"}]]);var c=l(25912);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,n.Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var h=l(24258),d=l(66648);let m=[{href:"#",icon:(0,t.jsx)(d.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,t.jsx)(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/interview/profile",icon:(0,t.jsx)(r.Z,{className:"h-5 w-5"}),label:"Profile"},{href:"/freelancer/interview/current",icon:(0,t.jsx)(i,{className:"h-5 w-5"}),label:"Current"},{href:"/freelancer/interview/bids",icon:(0,t.jsx)(c.Z,{className:"h-5 w-5"}),label:"Bids"},{href:"/freelancer/interview/history",icon:(0,t.jsx)(o,{className:"h-5 w-5"}),label:"History"}],x=[{href:"/freelancer/settings/personal-info",icon:(0,t.jsx)(h.Z,{className:"h-5 w-5"}),label:"Settings"}]}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2455,9726,2688,2971,7023,1744],function(){return e(e.s=39238)}),_N_E=e.O()}]);