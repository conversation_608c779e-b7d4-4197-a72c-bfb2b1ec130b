(()=>{var e={};e.id=4575,e.ids=[4575],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},81520:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>o}),t(78669),t(54302),t(12523);var r=t(23191),i=t(88716),a=t(37922),n=t.n(a),l=t(95231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let o=["",{children:["freelancer",{children:["oracleDashboard",{children:["businessVerification",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,78669)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\oracleDashboard\\businessVerification\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\oracleDashboard\\businessVerification\\page.tsx"],x="/freelancer/oracleDashboard/businessVerification/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/freelancer/oracleDashboard/businessVerification/page",pathname:"/freelancer/oracleDashboard/businessVerification",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},78077:(e,s,t)=>{Promise.resolve().then(t.bind(t,39445))},12893:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(80851).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},32019:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(80851).Z)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},5932:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(80851).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},42887:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(80851).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},24061:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(80851).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},39445:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>S});var r=t(10326),i=t(17577),a=t(41137),n=t(23015),l=t(91664),c=t(24118),o=t(2822),d=t(40588),x=t(92166),m=t(34270),u=t(39958),h=t(6260),p=t(56627);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let f=(0,t(80851).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var j=t(32019),y=t(12893),b=t(24061),g=t(5932),v=t(42887),N=t(40617),_=t(74064),k=t(74723),q=t(27256),w=t(29752),C=t(38443),V=t(9969),D=t(82631),Z=t(82015);let P=q.z.object({type:q.z.enum(["verified","rejected"],{required_error:"You need to select a type."}),comment:q.z.string().optional()}),E=({firstName:e,lastName:s,email:t,phone:a,companyName:n,companySize:c,referenceEmail:d,websiteLink:x,linkedInLink:m,githubLink:u,comments:h,status:p,onStatusUpdate:q,onCommentUpdate:E})=>{let[S,z]=(0,i.useState)(p),M=(0,k.cI)({resolver:(0,_.F)(P)}),I=M.watch("type");async function G(e){z(e.type),q(e.type),E(e.comment||"")}return(0,i.useEffect)(()=>{z(p)},[p]),(0,r.jsxs)(w.Zb,{className:"max-w-full md:max-w-2xl",children:[(0,r.jsxs)(w.Ol,{children:[(0,r.jsxs)(w.ll,{className:"flex justify-between",children:[(0,r.jsxs)("span",{children:[e," ",s]}),(0,r.jsxs)("div",{className:"flex flex-row space-x-3",children:[x&&r.jsx("a",{href:x,target:"_blank",rel:"noopener noreferrer",className:"text-sm underline flex items-center",children:r.jsx(f,{className:"mt-auto"})}),m&&r.jsx("a",{href:m,target:"_blank",rel:"noopener noreferrer",className:"text-sm  underline flex items-center",children:r.jsx(j.Z,{})}),u&&r.jsx("a",{href:u,target:"_blank",rel:"noopener noreferrer",className:"text-sm  underline flex items-center",children:r.jsx(y.Z,{})})]})]}),(0,r.jsxs)(w.SZ,{className:"mt-1 text-justify text-gray-600",children:[n,r.jsx("br",{}),"pending"===S?r.jsx(C.C,{className:"bg-warning-foreground text-white mt-2",children:"PENDING"}):"verified"===S?r.jsx(C.C,{className:"bg-success text-white mt-2",children:"VERIFIED"}):r.jsx(C.C,{className:"bg-red-500 text-white mt-2",children:"REJECTED"})]})]}),r.jsx(w.aY,{children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(D.u,{children:[r.jsx(D.aJ,{asChild:!0,children:(0,r.jsxs)("p",{className:"text-sm text-gray-600 flex items-center",children:[r.jsx(b.Z,{className:"mr-2"}),c]})}),r.jsx(D._v,{side:"bottom",children:"Company Size"})]}),(0,r.jsxs)(D.u,{children:[r.jsx(D.aJ,{asChild:!0,children:(0,r.jsxs)("p",{className:"text-sm text-gray-600 flex items-center",children:[r.jsx(g.Z,{className:"mr-2"}),t]})}),r.jsx(D._v,{side:"bottom",children:"Email"})]}),(0,r.jsxs)(D.u,{children:[r.jsx(D.aJ,{asChild:!0,children:(0,r.jsxs)("p",{className:"text-sm text-gray-600 flex items-center",children:[r.jsx(v.Z,{className:"mr-2"}),a]})}),r.jsx(D._v,{side:"bottom",children:"Phone"})]}),(0,r.jsxs)(D.u,{children:[r.jsx(D.aJ,{asChild:!0,children:(0,r.jsxs)("p",{className:"text-sm text-gray-600 flex items-center",children:[r.jsx(g.Z,{className:"mr-2"}),d]})}),r.jsx(D._v,{side:"bottom",children:"Reference Email"})]}),h&&(0,r.jsxs)("p",{className:"mt-2 flex items-center text-gray-500 border p-3 rounded",children:[r.jsx(N.Z,{className:"mr-2"}),h]})]})}),r.jsx(w.eW,{className:"flex flex-col items-center",children:"pending"===S&&r.jsx(V.l0,{...M,children:(0,r.jsxs)("form",{onSubmit:M.handleSubmit(G),className:"w-full space-y-6 mt-6",children:[r.jsx(V.Wi,{control:M.control,name:"type",render:({field:e})=>(0,r.jsxs)(V.xJ,{className:"space-y-3",children:[r.jsx(V.lX,{children:"Choose Verification Status:"}),r.jsx(V.NI,{children:(0,r.jsxs)(o.E,{onValueChange:e.onChange,defaultValue:e.value,className:"flex flex-col space-y-1",children:[(0,r.jsxs)(V.xJ,{className:"flex items-center space-x-3",children:[r.jsx(V.NI,{children:r.jsx(o.m,{value:"verified"})}),r.jsx(V.lX,{className:"font-normal",children:"Verified"})]}),(0,r.jsxs)(V.xJ,{className:"flex items-center space-x-3",children:[r.jsx(V.NI,{children:r.jsx(o.m,{value:"rejected"})}),r.jsx(V.lX,{className:"font-normal",children:"Rejected"})]})]})}),r.jsx(V.zG,{})]})}),r.jsx(V.Wi,{control:M.control,name:"comment",render:({field:e})=>(0,r.jsxs)(V.xJ,{children:[r.jsx(V.lX,{children:"Comments:"}),r.jsx(V.NI,{children:r.jsx(Z.g,{placeholder:"Enter comments:",...e})}),r.jsx(V.zG,{})]})}),r.jsx(l.z,{type:"submit",className:"w-full",disabled:!I||M.formState.isSubmitting,children:"Submit"})]})})})]})};function S(){let[e,s]=(0,i.useState)([]),[t,f]=(0,i.useState)("all"),[j,y]=(0,i.useState)(!1),b=e=>{f(e),y(!1)},g=e.filter(e=>"all"===t||("current"===t?e.verificationStatus===u.sB.PENDING:e.verificationStatus===t));(0,i.useCallback)(async()=>{try{let e=(await h.b.get("/verification/oracle?doc_type=business")).data.data.flatMap(e=>e.result?.projects?Object.values(e.result.projects).map(s=>({...s,verifier_id:e.verifier_id,verifier_username:e.verifier_username})):[]);s(e)}catch(e){console.error("Error in getting verification data:",e),(0,p.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}},[]);let v=(t,r)=>{let i=[...e];i[t].status=r,s(i)},N=(t,r)=>{let i=[...e];i[t].comments=r,s(i)};return(0,r.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[r.jsx(x.Z,{menuItemsTop:m.y,menuItemsBottom:m.$,active:"Business Verification"}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[r.jsx(d.Z,{menuItemsTop:m.y,menuItemsBottom:m.$,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Oracle",link:"#"},{label:"Business Verification",link:"#"}]}),(0,r.jsxs)("div",{className:"mb-8 ml-4 flex justify-between mt-8 md:mt-4 items-center",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold",children:"Business Verification"}),r.jsx("p",{className:"text-gray-400 mt-2",children:"Monitor the status of your Business verifications."})]}),r.jsx(l.z,{variant:"outline",size:"icon",className:"mr-8 mb-12",onClick:()=>y(!0),children:r.jsx(a.Z,{className:"h-4 w-4"})})]}),r.jsx(c.Vq,{open:j,onOpenChange:y,children:(0,r.jsxs)(c.cZ,{children:[r.jsx(c.fK,{children:r.jsx(c.$N,{children:"Filter Business Verification"})}),(0,r.jsxs)(o.E,{value:t,onValueChange:e=>b(e),className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(o.m,{value:"all",id:"filter-all"}),r.jsx("label",{htmlFor:"filter-all",children:"All"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(o.m,{value:"current",id:"filter-current"}),r.jsx("label",{htmlFor:"filter-current",children:"Pending"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(o.m,{value:"verified",id:"filter-verified"}),r.jsx("label",{htmlFor:"filter-verified",children:"Verified"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(o.m,{value:"rejected",id:"filter-rejected"}),r.jsx("label",{htmlFor:"filter-rejected",children:"Rejected"})]})]}),r.jsx(c.cN,{children:r.jsx(l.z,{type:"button",onClick:()=>y(!1),children:"Close"})})]})}),r.jsx("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8    grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3",children:g.length>0?g.map((e,s)=>r.jsx(E,{_id:e._id,firstName:e.firstName,lastName:e.lastName,email:e.email,phone:e.phone,companyName:e.companyName,companySize:e.companySize,referenceEmail:e.referenceEmail,websiteLink:e.websiteLink,linkedInLink:e.linkedInLink,githubLink:e.githubLink,comments:e.comments,status:e.status,onStatusUpdate:e=>v(s,e),onCommentUpdate:e=>N(s,e)},s)):(0,r.jsxs)("div",{className:"text-center w-full col-span-full mt-20 py-10",children:[r.jsx(n.Z,{className:"mx-auto text-gray-500",size:"100"}),r.jsx("p",{className:"text-gray-500",children:"No Business verification records found."})]})})]})]})}},78669:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>a,default:()=>l});var r=t(68570);let i=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\oracleDashboard\businessVerification\page.tsx`),{__esModule:a,$$typeof:n}=i;i.default;let l=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\oracleDashboard\businessVerification\page.tsx#default`)}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,6686,4736,6499,8066,588,3379],()=>t(81520));module.exports=r})();