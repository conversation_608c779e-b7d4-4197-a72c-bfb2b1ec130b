(()=>{var e={};e.id=9151,e.ids=[9151],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},50473:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,originalPathname:()=>p,pages:()=>l,routeModule:()=>x,tree:()=>c}),s(90524),s(54302),s(12523);var r=s(23191),i=s(88716),n=s(37922),o=s.n(n),a=s(95231),u={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);s.d(t,u);let c=["",{children:["notes",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,90524)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\notes\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\notes\\page.tsx"],p="/notes/page",d={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/notes/page",pathname:"/notes",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},74545:(e,t,s)=>{Promise.resolve().then(s.bind(s,24472))},24472:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>q});var r=s(10326),i=s(17577),n=s(25842),o=s(77506),a=s(92166),u=s(46319),c=s(74935),l=s(12389),p=s(6260),d=s(2069),x=s(56627),h=s(17978),m=s(40588),f=s(48586);let q=()=>{let e=(0,n.v9)(e=>e.user),t=e.uid,{notes:s,isLoading:q,fetchNotes:g,setNotes:v}=(0,h.Z)(t);(0,i.useEffect)(()=>{t&&g()},[g,t]);let _=async s=>{if(!s.title||!s.content||!t){(0,x.Am)({title:"Missing required fields",description:"Title and content are required to create a note.",duration:3e3});return}let r={...s,userId:t,bgColor:s.bgColor||"#FFFFFF",banner:s.banner||"",noteType:d.wK.NOTE,type:d.JG.PERSONAL,entityType:e?.type?.toUpperCase()};v(e=>[r,...e]);try{let e=await p.b.post("/notes",r);e?.data&&((0,x.Am)({title:"Note Created",description:"Your note was successfully created.",duration:3e3}),g())}catch(e){console.error("Failed to create note:",e),(0,x.Am)({title:"Error",description:"Failed to create the note.",duration:3e3}),v(e=>e.filter(e=>e!==r))}};return(0,r.jsxs)("section",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[r.jsx(a.Z,{menuItemsTop:u.Ne,menuItemsBottom:f.$C,active:"Notes"}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[r.jsx("div",{children:r.jsx(m.Z,{menuItemsTop:u.Ne,menuItemsBottom:f.$C,activeMenu:"Notes",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Notes",link:"/notes"}]})}),(0,r.jsxs)("div",{children:[r.jsx(c.Z,{isTrash:!1,setNotes:v,notes:s,onNoteCreate:_}),r.jsx("div",{className:"p-6",children:q?r.jsx("div",{className:"flex justify-center items-center h-[40vh]",children:r.jsx(o.Z,{className:"my-4 h-8 w-8 animate-spin"})}):r.jsx("div",{children:s?.length>0?r.jsx(l.Z,{fetchNotes:g,notes:s,setNotes:v,isArchive:!1}):r.jsx("div",{className:"flex justify-center items-center h-[40vh] w-full",children:r.jsx("p",{children:"No notes available. Start adding some!"})})})})]})]})]})}},90524:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>n,default:()=>a});var r=s(68570);let i=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\notes\page.tsx`),{__esModule:n,$$typeof:o}=i;i.default;let a=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\notes\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588,5442],()=>s(50473));module.exports=r})();