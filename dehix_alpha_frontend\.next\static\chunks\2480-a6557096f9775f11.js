"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2480],{6540:function(e,r,t){t.d(r,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(33480).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},25912:function(e,r,t){t.d(r,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},404:function(e,r,t){t.d(r,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(33480).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},47390:function(e,r,t){t.d(r,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},29406:function(e,r,t){t.d(r,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(33480).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},67524:function(e,r,t){t.d(r,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(33480).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},93363:function(e,r,t){t.d(r,{NI:function(){return v},Wi:function(){return u},l0:function(){return s},lX:function(){return m},pf:function(){return x},xJ:function(){return h},zG:function(){return b}});var a=t(57437),n=t(2265),i=t(63355),o=t(39343),l=t(49354),c=t(70402);let s=o.RV,d=n.createContext({}),u=e=>{let{...r}=e;return(0,a.jsx)(d.Provider,{value:{name:r.name},children:(0,a.jsx)(o.Qr,{...r})})},f=()=>{let e=n.useContext(d),r=n.useContext(p),{getFieldState:t,formState:a}=(0,o.Gc)(),i=t(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=r;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...i}},p=n.createContext({}),h=n.forwardRef((e,r)=>{let{className:t,...i}=e,o=n.useId();return(0,a.jsx)(p.Provider,{value:{id:o},children:(0,a.jsx)("div",{ref:r,className:(0,l.cn)("space-y-2",t),...i})})});h.displayName="FormItem";let m=n.forwardRef((e,r)=>{let{className:t,...n}=e,{error:i,formItemId:o}=f();return(0,a.jsx)(c.Label,{ref:r,className:(0,l.cn)(i&&"text-destructive",t),htmlFor:o,...n})});m.displayName="FormLabel";let v=n.forwardRef((e,r)=>{let{...t}=e,{error:n,formItemId:o,formDescriptionId:l,formMessageId:c}=f();return(0,a.jsx)(i.g7,{ref:r,id:o,"aria-describedby":n?"".concat(l," ").concat(c):"".concat(l),"aria-invalid":!!n,...t})});v.displayName="FormControl";let x=n.forwardRef((e,r)=>{let{className:t,...n}=e,{formDescriptionId:i}=f();return(0,a.jsx)("p",{ref:r,id:i,className:(0,l.cn)("text-sm text-muted-foreground",t),...n})});x.displayName="FormDescription";let b=n.forwardRef((e,r)=>{let{className:t,children:n,...i}=e,{error:o,formMessageId:c}=f(),s=o?String(null==o?void 0:o.message):n;return s?(0,a.jsx)("p",{ref:r,id:c,className:(0,l.cn)("text-sm font-medium text-destructive",t),...i,children:s}):null});b.displayName="FormMessage"},70402:function(e,r,t){t.r(r),t.d(r,{Label:function(){return s}});var a=t(57437),n=t(2265),i=t(38364),o=t(12218),l=t(49354);let c=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),s=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)(i.f,{ref:r,className:(0,l.cn)(c(),t),...n})});s.displayName=i.f.displayName},97694:function(e,r,t){t.d(r,{E:function(){return O},m:function(){return z}});var a=t(57437),n=t(2265),i=t(78149),o=t(1584),l=t(98324),c=t(18676),s=t(53398),d=t(91715),u=t(87513),f=t(75238),p=t(47250),h=t(31383),m="Radio",[v,x]=(0,l.b)(m),[b,y]=v(m),w=n.forwardRef((e,r)=>{let{__scopeRadio:t,name:l,checked:s=!1,required:d,disabled:u,value:f="on",onCheck:p,...h}=e,[m,v]=n.useState(null),x=(0,o.e)(r,e=>v(e)),y=n.useRef(!1),w=!m||!!m.closest("form");return(0,a.jsxs)(b,{scope:t,checked:s,disabled:u,children:[(0,a.jsx)(c.WV.button,{type:"button",role:"radio","aria-checked":s,"data-state":N(s),"data-disabled":u?"":void 0,disabled:u,value:f,...h,ref:x,onClick:(0,i.M)(e.onClick,e=>{s||null==p||p(),w&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})}),w&&(0,a.jsx)(E,{control:m,bubbles:!y.current,name:l,value:f,checked:s,required:d,disabled:u,style:{transform:"translateX(-100%)"}})]})});w.displayName=m;var k="RadioIndicator",g=n.forwardRef((e,r)=>{let{__scopeRadio:t,forceMount:n,...i}=e,o=y(k,t);return(0,a.jsx)(h.z,{present:n||o.checked,children:(0,a.jsx)(c.WV.span,{"data-state":N(o.checked),"data-disabled":o.disabled?"":void 0,...i,ref:r})})});g.displayName=k;var E=e=>{let{control:r,checked:t,bubbles:i=!0,...o}=e,l=n.useRef(null),c=(0,p.D)(t),s=(0,f.t)(r);return n.useEffect(()=>{let e=l.current,r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(c!==t&&r){let a=new Event("click",{bubbles:i});r.call(e,t),e.dispatchEvent(a)}},[c,t,i]),(0,a.jsx)("input",{type:"radio","aria-hidden":!0,defaultChecked:t,...o,tabIndex:-1,ref:l,style:{...e.style,...s,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function N(e){return e?"checked":"unchecked"}var j=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],R="RadioGroup",[D,C]=(0,l.b)(R,[s.Pc,x]),P=(0,s.Pc)(),M=x(),[I,V]=D(R),L=n.forwardRef((e,r)=>{let{__scopeRadioGroup:t,name:n,defaultValue:i,value:o,required:l=!1,disabled:f=!1,orientation:p,dir:h,loop:m=!0,onValueChange:v,...x}=e,b=P(t),y=(0,u.gm)(h),[w,k]=(0,d.T)({prop:o,defaultProp:i,onChange:v});return(0,a.jsx)(I,{scope:t,name:n,required:l,disabled:f,value:w,onValueChange:k,children:(0,a.jsx)(s.fC,{asChild:!0,...b,orientation:p,dir:y,loop:m,children:(0,a.jsx)(c.WV.div,{role:"radiogroup","aria-required":l,"aria-orientation":p,"data-disabled":f?"":void 0,dir:y,...x,ref:r})})})});L.displayName=R;var Z="RadioGroupItem",F=n.forwardRef((e,r)=>{let{__scopeRadioGroup:t,disabled:l,...c}=e,d=V(Z,t),u=d.disabled||l,f=P(t),p=M(t),h=n.useRef(null),m=(0,o.e)(r,h),v=d.value===c.value,x=n.useRef(!1);return n.useEffect(()=>{let e=e=>{j.includes(e.key)&&(x.current=!0)},r=()=>x.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",r),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",r)}},[]),(0,a.jsx)(s.ck,{asChild:!0,...f,focusable:!u,active:v,children:(0,a.jsx)(w,{disabled:u,required:d.required,checked:v,...p,...c,name:d.name,ref:m,onCheck:()=>d.onValueChange(c.value),onKeyDown:(0,i.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,i.M)(c.onFocus,()=>{var e;x.current&&(null===(e=h.current)||void 0===e||e.click())})})})});F.displayName=Z;var A=n.forwardRef((e,r)=>{let{__scopeRadioGroup:t,...n}=e,i=M(t);return(0,a.jsx)(g,{...i,...n,ref:r})});A.displayName="RadioGroupIndicator";var T=t(28165),G=t(49354);let O=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)(L,{className:(0,G.cn)("grid gap-2",t),...n,ref:r})});O.displayName=L.displayName;let z=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)(F,{ref:r,className:(0,G.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),...n,children:(0,a.jsx)(A,{className:"flex items-center justify-center",children:(0,a.jsx)(T.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})})});z.displayName=F.displayName},746:function(e,r,t){t.d(r,{$:function(){return f},y:function(){return u}});var a=t(57437),n=t(11005),i=t(25912),o=t(52022),l=t(67524),c=t(6540),s=t(24258),d=t(66648);let u=[{href:"#",icon:(0,a.jsx)(d.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,a.jsx)(n.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/oracleDashboard/businessVerification",icon:(0,a.jsx)(i.Z,{className:"h-5 w-5"}),label:"Business Verification"},{href:"/freelancer/oracleDashboard/workExpVerification",icon:(0,a.jsx)(o.Z,{className:"h-5 w-5"}),label:"Experience Verification"},{href:"/freelancer/oracleDashboard/projectVerification",icon:(0,a.jsx)(l.Z,{className:"h-5 w-5"}),label:"Project Verification"},{href:"/freelancer/oracleDashboard/educationVerification",icon:(0,a.jsx)(c.Z,{className:"h-5 w-5"}),label:"Education Verification"}],f=[{href:"/freelancer/settings/personal-info",icon:(0,a.jsx)(s.Z,{className:"h-5 w-5"}),label:"Settings"}]},97540:function(e,r,t){var a,n,i,o,l,c;t.d(r,{cd:function(){return a},d8:function(){return s},kJ:function(){return n},sB:function(){return i}}),(o=a||(a={})).Mastery="Mastery",o.Proficient="Proficient",o.Beginner="Beginner",(l=n||(n={})).ACTIVE="Active",l.PENDING="Pending",l.REJECTED="Rejected",l.COMPLETED="Completed",(c=i||(i={})).ACTIVE="ACTIVE",c.PENDING="PENDING",c.REJECTED="REJECTED",c.COMPLETED="COMPLETED";let s={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},38364:function(e,r,t){t.d(r,{f:function(){return l}});var a=t(2265),n=t(18676),i=t(57437),o=a.forwardRef((e,r)=>(0,i.jsx)(n.WV.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null===(t=e.onMouseDown)||void 0===t||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));o.displayName="Label";var l=o}}]);