(()=>{var e={};e.id=6893,e.ids=[6893],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},32929:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>d}),t(60610),t(54302),t(12523);var a=t(23191),r=t(88716),l=t(37922),i=t.n(l),n=t(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["business",{children:["project",{children:["[project_id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,60610)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\project\\[project_id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\project\\[project_id]\\page.tsx"],m="/business/project/[project_id]/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/business/project/[project_id]/page",pathname:"/business/project/[project_id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},70524:(e,s,t)=>{Promise.resolve().then(t.bind(t,74818))},40900:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},12070:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},57248:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("CalendarX2",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M21 13V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h8",key:"3spt84"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"m17 22 5-5",key:"1k6ppv"}],["path",{d:"m17 17 5 5",key:"p7ous7"}]])},66307:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69669:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},12714:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5932:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},40617:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},57671:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},69515:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},40765:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},98091:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},24061:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},49758:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},74818:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eo});var a,r,l=t(10326),i=t(57248),n=t(35047),c=t(17577),d=t.n(c),o=t(29752),m=t(47548),x=t(94880),u=t(43593),p=t(92166),h=t(46319),f=t(6260),j=t(83855),N=t(5932),g=t(78062),b=t(1370),v=t(51682),y=t(38443);let w=function({domainName:e,description:s,email:t,status:a,startDate:r,endDate:i,domains:n=[],skills:c=[],isLastCard:d,onAddProfile:m}){return d?l.jsx(o.Zb,{className:"flex w-full items-center justify-center h-[430px] border border-dashed border-gray-400 rounded-lg cursor-pointer hover:border-gray-300 transition-colors",onClick:m,children:l.jsx(j.Z,{className:"w-12 h-12 text-gray-400"})}):(0,l.jsxs)("div",{className:"w-full min-h-[400px] bg-card relative border border-gray-700 rounded-lg shadow-md p-6 flex flex-col h-full",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsxs)(v.zs,{children:[l.jsx(v.Yi,{children:l.jsx("h2",{className:"text-lg cursor-pointer font-semibold",children:e&&e.length>10?`${e.substring(0,10)}...`:e})}),l.jsx(v.bZ,{className:"py-2 w-auto",children:e})]}),l.jsx(y.C,{className:"bg-yellow-400 capitalize text-black text-xs px-2 py-1 rounded-md",children:a?.toLocaleLowerCase()||"Pending"})]}),(0,l.jsxs)("div",{className:"flex-grow",children:[l.jsx(g.Separator,{}),l.jsx("div",{className:"flex mt-6 mb-6 items-center text-sm",children:l.jsx(b.Z,{startDate:r,endDate:i})}),l.jsx("p",{className:"text-sm mb-4",children:s||"No description available."}),n.length>0&&(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("h3",{className:"text-sm font-semibold mb-2",children:"Project Domain:"}),l.jsx("div",{className:"flex flex-wrap gap-2",children:n.map((e,s)=>l.jsx(y.C,{className:"px-3 bg-gray-200 text-gray-900 py-1 text-xs rounded-full",children:e},s))})]}),c.length>0&&(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("h3",{className:"text-sm font-semibold mb-2",children:"Skills:"}),l.jsx("div",{className:"flex flex-wrap gap-2",children:c.map((e,s)=>l.jsx(y.C,{className:"px-3 bg-gray-200 text-gray-900 py-1 text-xs rounded-full",children:e},s))})]}),(0,l.jsxs)("div",{className:"flex mt-[28px] items-center text-sm",children:[l.jsx(N.Z,{className:"w-4 h-4 mr-2"}),l.jsx("span",{children:t||"No email provided."})]})]}),l.jsx("div",{className:"pt-3",children:l.jsx("button",{className:"w-auto bg-blue-600 text-white px-5 py-1 rounded-md hover:bg-blue-700",children:"Mark as completed"})})]})};var C=t(50384),k=t(30361),E=t(69669),P=t(24061),D=t(49758),I=t(12714),S=t(23015),A=t(56556),Z=t(91664),F=t(24118),T=t(56627),M=t(51223),R=t(38227),V=t(41156);(function(e){e.TEXT="Text",e.CURRENCY="Currency",e.ACTION="Action",e.STATUS="Status",e.CUSTOM="Custom"})(a||(a={})),function(e){e.INR="INR",e.USD="USD"}(r||(r={}));var _=t(90434),q=t(76812),$=t(10143);let z=(0,c.memo)(({value:e})=>l.jsx("span",{children:e||"-"}));z.displayName="TextField";let L=(0,c.memo)(({fieldData:e,value:s})=>{let t=e.currency||r.USD,a=Number(s)||0,i=new Intl.NumberFormat("en-US",{style:"currency",currency:t,maximumFractionDigits:0}).format(a);return l.jsx("span",{className:"font-medium text-green-600",children:i})});L.displayName="CurrencyField";let U=(0,c.memo)(({value:e,fieldData:s})=>{if(!e)return l.jsx("span",{className:"text-gray-400",children:"-"});let t=s.statusFormats?.find(s=>s.value.toLowerCase()===e.toLowerCase());return t?l.jsx("span",{style:{backgroundColor:t.bgColor,color:t.textColor},className:`px-2 py-1 rounded-sm text-xs font-medium text-center ${t.isUppercase?"uppercase":""}`,children:t.textValue}):l.jsx("span",{className:"px-2 py-1 bg-gray-100 text-gray-800 rounded-sm text-xs font-medium",children:e})});U.displayName="StatusField";let B={[a.ACTION]:({id:e,fieldData:s,refetch:t,value:a})=>{if(!s.actions?.options?.length){let e=a?.bid_status||a?.status;return"ACCEPTED"===e?l.jsx("span",{className:"text-sm font-medium text-green-600",children:"Bid has been accepted"}):"REJECTED"===e?l.jsx("span",{className:"text-sm font-medium text-red-600",children:"Bid has been rejected"}):l.jsx("span",{className:"text-sm font-medium text-gray-500",children:"No actions available"})}return(0,l.jsxs)($.h_,{children:[l.jsx($.$F,{className:"text-sm text-white hover:bg-gray-700 p-1 rounded transition-colors",children:s.actions?.icon||l.jsx(q.nWS,{})}),l.jsx($.AW,{className:"bg-black border-gray-600",children:s.actions?.options.map(({actionName:s,type:a,handler:r,href:i,className:n},c)=>l.jsx($.Xi,{className:"px-0 py-0",children:"Button"===a?l.jsx("button",{onClick:()=>r?.({id:e,refetch:t}),className:M.cn("w-full py-2 px-3 text-left text-sm font-medium text-white hover:bg-gray-800",n),children:s}):l.jsx(_.default,{href:i||"#",className:M.cn("block w-full py-2 px-3 text-sm font-medium text-white hover:bg-gray-800",n),children:s})},c))})]})},[a.CURRENCY]:L,[a.STATUS]:U,[a.CUSTOM]:({fieldData:e,id:s,value:t,refetch:a})=>e.CustomComponent?l.jsx(e.CustomComponent,{id:s,data:t,refetch:a}):l.jsx("span",{className:"text-gray-400",children:"-"}),[a.TEXT]:z},O=({value:e,fieldData:s,id:t,refetch:a})=>{let r=B[s.type]||z;return l.jsx(r,{fieldData:s,value:e,id:t,refetch:a})},G=({fields:e,data:s,uniqueId:t})=>{let[r,i]=(0,c.useState)([]),[n,d]=(0,c.useState)(!0),m=()=>{};return(0,c.useEffect)(()=>{s&&(i(s),d(!1))},[s]),l.jsx("div",{className:"w-full",children:l.jsx("div",{className:"mb-8 mt-4",children:l.jsx(o.Zb,{children:l.jsx("div",{className:"lg:overflow-x-auto",children:(0,l.jsxs)(V.iA,{children:[l.jsx(V.xD,{children:l.jsx(V.SC,{children:e.map((e,s)=>l.jsx(V.ss,{className:"text-center",children:e.textValue},e.fieldName||s))})}),l.jsx(V.RM,{children:n?Array.from({length:3},(s,t)=>l.jsx(V.SC,{children:e.map((e,s)=>l.jsx(V.pj,{children:l.jsx(R.O,{className:"h-5 w-20"})},e.fieldName||s))},t)):r?.length>0?r.map((s,r)=>l.jsx(V.SC,{children:e.map((e,r)=>l.jsx(V.pj,{className:(0,M.cn)("text-gray-900 dark:text-gray-300 text-center",e.className),children:l.jsx(O,{fieldData:e,value:e.fieldName?s[e.fieldName]:e.type===a.CUSTOM?s:void 0,id:s[t],refetch:m})},e.fieldName||r))},String(s[t]??r))):l.jsx(V.SC,{children:l.jsx(V.pj,{colSpan:e.length,className:"text-center py-10",children:(0,l.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[l.jsx(S.Z,{className:"text-gray-400",size:48}),l.jsx("p",{className:"text-gray-500 text-sm",children:"No data available"})]})})})})]})})})})})},W=["PENDING","ACCEPTED","REJECTED","PANEL","INTERVIEW"],J=[{value:"PENDING",textValue:"Pending",bgColor:"#D97706",textColor:"#FFFFFF"},{value:"ACCEPTED",textValue:"Accepted",bgColor:"#059669",textColor:"#FFFFFF"},{value:"REJECTED",textValue:"Rejected",bgColor:"#DC2626",textColor:"#FFFFFF"},{value:"PANEL",textValue:"Panel",bgColor:"#7C3AED",textColor:"#FFFFFF"},{value:"INTERVIEW",textValue:"Interview",bgColor:"#2563EB",textColor:"#FFFFFF"}],H=d().memo(({profilePic:e,userName:s})=>(0,l.jsxs)(l.Fragment,{children:[e?l.jsx("img",{src:e,alt:s,className:"w-10 h-10 rounded-full object-cover",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling?.classList.remove("hidden")}}):null,l.jsx("div",{className:`w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center ${e?"hidden":""}`,children:l.jsx("span",{className:"text-white font-semibold text-sm",children:s.charAt(0).toUpperCase()})})]})),X=d().memo(({freelancer:e,bidData:s,isOpen:t,onClose:a})=>{if(!e)return null;let r=e.firstName&&e.lastName?`${e.firstName} ${e.lastName}`.trim():e.userName;return l.jsx(F.Vq,{open:t,onOpenChange:a,children:(0,l.jsxs)(F.cZ,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[(0,l.jsxs)(F.fK,{children:[(0,l.jsxs)(F.$N,{className:"flex items-center gap-3",children:[l.jsx(H,{profilePic:e.profilePic,userName:e.userName}),(0,l.jsxs)("div",{children:[l.jsx("h2",{className:"text-xl font-bold",children:r}),(0,l.jsxs)("p",{className:"text-sm text-gray-500",children:["@",e.userName]})]})]}),l.jsx(F.Be,{children:"Freelancer Application Details"})]}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{children:[l.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Basic Information"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[l.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Email"}),l.jsx("p",{className:"text-sm",children:e.email||"N/A"})]}),(0,l.jsxs)("div",{children:[l.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Role"}),l.jsx("p",{className:"text-sm",children:e.role||"N/A"})]}),(0,l.jsxs)("div",{children:[l.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Work Experience"}),l.jsx("p",{className:"text-sm",children:e.workExperience?`${e.workExperience} years`:"N/A"})]}),(0,l.jsxs)("div",{children:[l.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Hourly Rate"}),l.jsx("p",{className:"text-sm",children:e.perHourPrice?`$${e.perHourPrice}/hour`:"N/A"})]})]})]}),e.description&&(0,l.jsxs)("div",{children:[l.jsx("h3",{className:"text-lg font-semibold mb-3",children:"About"}),l.jsx("p",{className:"text-sm text-gray-700 leading-relaxed",children:e.description})]}),s?.description&&(0,l.jsxs)("div",{children:[l.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Cover Letter"}),l.jsx("div",{className:"bg-gray-50 dark:bg-gray-800 p-4 rounded-lg",children:l.jsx("p",{className:"text-sm text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap",children:s.description})})]}),e.skills&&e.skills.length>0&&(0,l.jsxs)("div",{children:[l.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Skills"}),l.jsx("div",{className:"flex flex-wrap gap-2",children:e.skills.map((e,s)=>(0,l.jsxs)(y.C,{variant:"secondary",className:"text-xs",children:[e.name,e.level&&` (${e.level})`]},s))})]}),e.domain&&e.domain.length>0&&(0,l.jsxs)("div",{children:[l.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Domain Expertise"}),l.jsx("div",{className:"flex flex-wrap gap-2",children:e.domain.map((e,s)=>(0,l.jsxs)(y.C,{variant:"outline",className:"text-xs",children:[e.name,e.level&&` (${e.level})`]},s))})]})]})]})})}),Q=({id:e})=>{let[s,t]=(0,c.useState)(null),[r,i]=(0,c.useState)(!0),[n,d]=(0,c.useState)(null),[o,m]=(0,c.useState)(),[x,u]=(0,c.useState)([]),[p,h]=(0,c.useState)({}),[j,N]=(0,c.useState)(!1),[g,b]=(0,c.useState)(null),[v,y]=(0,c.useState)(null),[w,F]=(0,c.useState)(!1),M=(0,c.useMemo)(()=>W.reduce((e,s)=>(e[s]=x.filter(e=>e.bid_status===s).length,e),{}),[x]);(0,c.useEffect)(()=>{(async()=>{if(e)try{let s=await f.b.get(`/project/${e}`);t(s.data),d(null)}catch(s){let e=s.response?.data?.message||"Failed to fetch project data";d(e),(0,T.Am)({variant:"destructive",title:"Error",description:e})}finally{i(!1)}})()},[e]);let R=(0,c.useCallback)((e,s,t)=>({_id:e?._id||s,userName:e?.userName||t||"",firstName:e?.firstName||"",lastName:e?.lastName||"",email:e?.email||"",profilePic:e?.profilePic,description:e?.description||`Freelancer: ${t||"Unknown"}`,skills:e?.skills||[],domain:e?.domain||[],workExperience:e?.workExperience||0,perHourPrice:e?.perHourPrice||0,role:e?.role||""}),[]),V=(0,c.useCallback)(async s=>{try{N(!0),d(null);let t=await f.b.get(`/bid/project/${e}/profile/${s}/bid`),a=t.data?.data||[];if(0===a.length){u([]);return}let r=Array.from(new Set(a.map(e=>e.bidder_id).filter(e=>e&&e.trim()))),l=new Map;if(r.length>0){let e=r.map(async e=>{try{let s=await f.b.get(`/public/freelancer/${e}`);return{bidderId:e,data:s.data?.data||s.data}}catch(s){return console.warn(`Failed to fetch freelancer ${e}:`,s.message),{bidderId:e,data:null}}});(await Promise.all(e)).forEach(({bidderId:e,data:s})=>{l.set(e,s)})}let i=a.map(e=>({...e,freelancer:R(l.get(e.bidder_id),e.bidder_id,e.userName)}));u(i)}catch(s){let e=s.response?.data?.message||"Failed to fetch bid details";d(e),(0,T.Am)({variant:"destructive",title:"Error",description:e})}finally{N(!1)}},[e,R]);(0,c.useEffect)(()=>{o&&V(o)},[o,V]);let _=(0,c.useCallback)((e,s)=>{b(e),y(s),F(!0)},[]),q=(0,c.useCallback)(()=>{F(!1),b(null),y(null)},[]),$=(0,c.useCallback)(async(e,s)=>{try{h(s=>({...s,[e]:!0})),await f.b.put(`/bid/${e}/status`,{bid_status:s}),u(t=>t.map(t=>t._id===e?{...t,bid_status:s}:t)),(0,T.Am)({title:"Success",description:`Bid status updated to ${s.toLowerCase()}.`})}catch(s){let e=s.response?.data?.message||"Failed to update bid status";d(e),(0,T.Am)({variant:"destructive",title:"Error",description:e})}finally{h(s=>({...s,[e]:!1}))}},[]),z=(0,c.useCallback)(e=>{let s={accept:{actionName:"Accept",actionIcon:l.jsx(k.Z,{className:"w-4 h-4 text-green-600"}),type:"Button",handler:({id:e})=>$(e,"ACCEPTED")},reject:{actionName:"Reject",actionIcon:l.jsx(E.Z,{className:"w-4 h-4 text-red-600"}),type:"Button",handler:({id:e})=>$(e,"REJECTED")},panel:{actionName:"Send to Panel",actionIcon:l.jsx(P.Z,{className:"w-4 h-4 text-yellow-600"}),type:"Button",handler:({id:e})=>$(e,"PANEL")},interview:{actionName:"Schedule Interview",actionIcon:l.jsx(D.Z,{className:"w-4 h-4 text-blue-600"}),type:"Button",handler:({id:e})=>$(e,"INTERVIEW")}};return({PENDING:[s.accept,s.reject,s.panel,s.interview],PANEL:[s.accept,s.reject,s.interview],INTERVIEW:[s.accept,s.reject],ACCEPTED:[],REJECTED:[]})[e]||[]},[$]),L=(0,c.useCallback)(e=>({uniqueId:"_id",data:x.filter(s=>s.bid_status===e),searchColumn:["userName","current_price","description"],searchPlaceholder:"Search by username, bid amount etc...",fields:[{textValue:"Freelancer",type:a.CUSTOM,CustomComponent:({data:e})=>{let s=e?.freelancer,t=e?.userName||s?.userName||"Unknown",a=s?.firstName&&s?.lastName?`${s.firstName} ${s.lastName}`.trim():t;return(0,l.jsxs)("div",{className:"flex items-center gap-3 justify-center",children:[l.jsx(H,{profilePic:s?.profilePic,userName:t}),(0,l.jsxs)("div",{children:[l.jsx("p",{className:"font-medium text-white",children:a}),(0,l.jsxs)("p",{className:"text-sm text-gray-400",children:["@",t]})]})]})}},{textValue:"Bid Amount",type:a.CUSTOM,CustomComponent:({data:e})=>(0,l.jsxs)("span",{className:"font-medium text-green-400",children:["$",e?.current_price||"N/A"]})},{fieldName:"bid_status",textValue:"Status",type:a.STATUS,statusFormats:J},{textValue:"Application",type:a.CUSTOM,CustomComponent:({data:e})=>{let s=e?.freelancer;return l.jsx("div",{className:"flex justify-center",children:(0,l.jsxs)(Z.z,{variant:"outline",size:"sm",onClick:()=>s&&_(s,e),className:"flex items-center gap-2",children:[l.jsx(I.Z,{className:"w-4 h-4"}),"View"]})})}},{textValue:"Actions",type:a.ACTION,actions:{options:z(e)}}]}),[x,z,_]);return r?l.jsx("div",{className:"max-w-5xl mx-auto p-4",children:l.jsx("div",{className:"text-center py-10",children:l.jsx("p",{children:"Loading..."})})}):n?l.jsx("div",{className:"max-w-5xl mx-auto p-4",children:l.jsx("div",{className:"text-center py-10",children:l.jsx("p",{className:"text-red-500",children:n})})}):s?.data?.profiles?.length?(0,l.jsxs)(l.Fragment,{children:[l.jsx("div",{className:"max-w-5xl mx-auto p-4",children:l.jsx("div",{className:"mb-8 mt-4",children:l.jsx(A.UQ,{type:"single",collapsible:!0,children:s.data.profiles.map(e=>(0,l.jsxs)(A.Qd,{value:e._id||"",onClick:()=>m(e._id),children:[l.jsx(A.o4,{children:(0,l.jsxs)("div",{className:"flex justify-between items-center w-full",children:[l.jsx("h3",{className:"text-lg font-semibold",children:e.domain??"N/A"}),(0,l.jsxs)("span",{className:"text-gray-500",children:["Rate: ",e.rate??"N/A"]})]})}),(0,l.jsxs)(A.vF,{className:"p-0",children:[(0,l.jsxs)("div",{className:"px-6 py-4 flex flex-col gap-2",children:[l.jsx("div",{className:"flex gap-2 items-center",children:(0,l.jsxs)("p",{children:["Experience: ",e.experience??"N/A"]})}),l.jsx("div",{className:"flex gap-2 items-center",children:(0,l.jsxs)("p",{children:["Min Connect: ",e.minConnect??"N/A"]})}),l.jsx("div",{className:"flex gap-2 items-center",children:(0,l.jsxs)("p",{children:["Total Bids: ",e.totalBid?.length||0]})})]}),(0,l.jsxs)(C.mQ,{defaultValue:"PENDING",className:"w-full",children:[l.jsx(C.dr,{className:"grid w-full grid-cols-5 mb-4",children:W.map(e=>l.jsx(C.SP,{value:e,children:`${e.charAt(0)+e.slice(1).toLowerCase()} (${M[e]||0})`},e))}),W.map(e=>l.jsx(C.nU,{value:e,className:"mt-4",children:j?(0,l.jsxs)("div",{className:"flex justify-center items-center py-8",children:[l.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),l.jsx("span",{className:"ml-2 text-gray-600",children:"Loading freelancer details..."})]}):l.jsx(G,{...L(e)})},e))]})]})]},e._id))})})}),l.jsx(X,{freelancer:g,bidData:v,isOpen:w,onClose:q})]}):l.jsx("div",{className:"max-w-5xl mx-auto p-4",children:(0,l.jsxs)("div",{className:"text-center py-10 w-full mt-10",children:[l.jsx(S.Z,{className:"mx-auto text-gray-500",size:"100"}),l.jsx("p",{className:"text-gray-500 text-lg",children:"No bid profiles found"})]})})};var Y=t(39958),K=t(40588),ee=t(74064),es=t(74723),et=t(27256),ea=t(94019),er=t(9969),el=t(41190),ei=t(82015),en=t(29280);let ec=et.z.object({domain:et.z.string().min(1,"Domain is required"),freelancersRequired:et.z.string().min(1,"Number of freelancers is required"),skills:et.z.array(et.z.string()).min(1,"At least one skill is required"),experience:et.z.string().min(1,"Experience is required"),minConnect:et.z.string().min(1,"Minimum connects is required"),rate:et.z.string().min(1,"Rate is required"),description:et.z.string().min(1,"Description is required"),domain_id:et.z.string().min(1,"Domain ID is required")}),ed=({projectId:e,onProfileAdded:s,trigger:t,open:a,onOpenChange:r})=>{let[i,n]=(0,c.useState)(!1),d=void 0!==a?a:i,o=r||n,[m,x]=(0,c.useState)(!1),[u,p]=(0,c.useState)(!1),[h,N]=(0,c.useState)([]),[g,b]=(0,c.useState)([]),[v,w]=(0,c.useState)([]),[C,k]=(0,c.useState)(""),E=(0,es.cI)({resolver:(0,ee.F)(ec),defaultValues:{domain:"",freelancersRequired:"",skills:[],experience:"",minConnect:"",rate:"",description:"",domain_id:""},mode:"onChange"});(0,c.useEffect)(()=>{let e=async()=>{p(!0);try{let[e,s]=await Promise.all([f.b.get("/domain"),f.b.get("/skills")]),t=e.data.data||[],a=s.data.data||[];N(t),b(a)}catch(s){console.error("Error fetching data:",s);let e=s.response?.data?.message||s.message||"Failed to load domains and skills.";(0,T.Am)({variant:"destructive",title:"Error",description:e})}finally{p(!1)}};d&&e()},[d]);let P=e=>{let s=v.filter(s=>s!==e);w(s),E.setValue("skills",s)},D=e=>{let s=h.find(s=>s.label===e);s&&(E.setValue("domain",s.label),E.setValue("domain_id",s._id))},I=async t=>{x(!0);try{let a=await f.b.get(`/project/${e}`),r=a?.data?.data?.data||a?.data?.data;if(!r)throw Error("Project not found");let l={_id:crypto.randomUUID(),domain:t.domain,freelancersRequired:t.freelancersRequired,skills:v,experience:parseInt(t.experience),minConnect:parseInt(t.minConnect),rate:parseInt(t.rate),description:t.description,domain_id:t.domain_id,selectedFreelancer:[],freelancers:[],totalBid:[]},i=[...r.profiles||[],l],n={projectName:r.projectName,description:r.description,email:r.email,companyName:r.companyName,skillsRequired:r.skillsRequired,role:r.role||"",projectType:r.projectType,profiles:i};await f.b.put(`/project/${e}/update`,n),(0,T.Am)({title:"Profile Added",description:"The profile has been successfully added to the project."}),E.reset(),w([]),k(""),o(!1),s()}catch(s){console.error("API Error:",s);let e=s.response?.data?.message||s.message||"Failed to add profile. Please try again.";(0,T.Am)({variant:"destructive",title:"Error",description:e})}finally{x(!1)}};return(0,l.jsxs)(F.Vq,{open:d,onOpenChange:o,children:[t&&l.jsx(F.hg,{asChild:!0,children:t}),(0,l.jsxs)(F.cZ,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[(0,l.jsxs)(F.fK,{children:[l.jsx(F.$N,{children:"Add New Profile"}),l.jsx(F.Be,{children:"Add a new profile to this project with specific requirements."})]}),l.jsx(er.l0,{...E,children:(0,l.jsxs)("form",{onSubmit:E.handleSubmit(I),className:"space-y-4",children:[l.jsx(er.Wi,{control:E.control,name:"domain",render:({field:e})=>(0,l.jsxs)(er.xJ,{children:[l.jsx(er.lX,{children:"Profile Domain"}),l.jsx(er.NI,{children:(0,l.jsxs)(en.Ph,{onValueChange:D,value:e.value,disabled:u,children:[l.jsx(en.i4,{children:l.jsx(en.ki,{placeholder:u?"Loading domains...":"Select a domain"})}),l.jsx(en.Bw,{children:h.map(e=>l.jsx(en.Ql,{value:e.label,children:e.label},e._id))})]})}),l.jsx(er.zG,{})]})}),l.jsx(er.Wi,{control:E.control,name:"freelancersRequired",render:({field:e})=>(0,l.jsxs)(er.xJ,{children:[l.jsx(er.lX,{children:"Number of Freelancers Required"}),l.jsx(er.NI,{children:l.jsx(el.I,{type:"number",placeholder:"Enter number of freelancers",...e})}),l.jsx(er.zG,{})]})}),(0,l.jsxs)("div",{className:"space-y-2",children:[l.jsx(er.lX,{children:"Skills Required"}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsxs)(en.Ph,{onValueChange:k,value:C,disabled:u,children:[l.jsx(en.i4,{className:"flex-1",children:l.jsx(en.ki,{placeholder:u?"Loading skills...":"Select a skill"})}),l.jsx(en.Bw,{children:g.map(e=>l.jsx(en.Ql,{value:e.talentName||e.label,children:e.talentName||e.label},e._id))})]}),l.jsx(Z.z,{type:"button",onClick:()=>{if(C.trim()&&!v.includes(C.trim())){let e=[...v,C.trim()];w(e),E.setValue("skills",e),k("")}},disabled:!C,children:l.jsx(j.Z,{className:"w-4 h-4"})})]}),l.jsx("div",{className:"flex flex-wrap gap-2",children:v.map(e=>(0,l.jsxs)(y.C,{variant:"secondary",className:"flex items-center gap-1",children:[e,l.jsx(ea.Z,{className:"w-3 h-3 cursor-pointer",onClick:()=>P(e)})]},e))}),E.formState.isSubmitted&&0===v.length&&l.jsx("p",{className:"text-sm text-red-500",children:"At least one skill is required"})]}),l.jsx(er.Wi,{control:E.control,name:"experience",render:({field:e})=>(0,l.jsxs)(er.xJ,{children:[l.jsx(er.lX,{children:"Experience Required (years)"}),l.jsx(er.NI,{children:l.jsx(el.I,{type:"number",placeholder:"Enter years of experience",...e})}),l.jsx(er.zG,{})]})}),l.jsx(er.Wi,{control:E.control,name:"minConnect",render:({field:e})=>(0,l.jsxs)(er.xJ,{children:[l.jsx(er.lX,{children:"Minimum Connects Required"}),l.jsx(er.NI,{children:l.jsx(el.I,{type:"number",placeholder:"Enter minimum connects",...e})}),l.jsx(er.zG,{})]})}),l.jsx(er.Wi,{control:E.control,name:"rate",render:({field:e})=>(0,l.jsxs)(er.xJ,{children:[l.jsx(er.lX,{children:"Rate (per hour/project)"}),l.jsx(er.NI,{children:l.jsx(el.I,{type:"number",placeholder:"Enter rate",...e})}),l.jsx(er.zG,{})]})}),l.jsx(er.Wi,{control:E.control,name:"description",render:({field:e})=>(0,l.jsxs)(er.xJ,{children:[l.jsx(er.lX,{children:"Profile Description"}),l.jsx(er.NI,{children:l.jsx(ei.g,{placeholder:"Describe the requirements for this profile...",...e})}),l.jsx(er.zG,{})]})}),(0,l.jsxs)("div",{className:"flex justify-end gap-2 pt-4",children:[l.jsx(Z.z,{type:"button",variant:"outline",onClick:()=>o(!1),children:"Cancel"}),l.jsx(Z.z,{type:"submit",disabled:m||0===v.length,children:m?"Adding...":"Add Profile"})]})]})})]})]})};function eo(){let{project_id:e}=(0,n.useParams)(),[s,t]=(0,c.useState)(null),[a,r]=(0,c.useState)(!1),d=async()=>{try{let s=await f.b.get(`/project/${e}`),a=s?.data?.data?.data||s?.data?.data;a&&t(a)}catch(e){console.error("Error refetching project:",e),(0,T.Am)({variant:"destructive",title:"Error",description:"Failed to refresh project data."})}};return s?(0,l.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[l.jsx(p.Z,{menuItemsTop:h.yn,menuItemsBottom:h.$C,active:""}),(0,l.jsxs)("div",{className:"flex flex-col sm:gap-4 sm:py-4 md:py-0 sm:pl-14 mb-8",children:[l.jsx(K.Z,{menuItemsTop:h.yn,menuItemsBottom:h.$C,activeMenu:"",breadcrumbItems:[{label:"Dashboard",link:"/dashboard/business"},{label:"Project",link:"/dashboard/business"},{label:s.projectName,link:"#"}]}),(0,l.jsxs)("main",{className:"flex flex-col lg:grid lg:grid-cols-4 xl:grid-cols-4 flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8",children:[l.jsx("div",{className:"w-full lg:col-span-3 space-y-4 md:space-y-8",children:(0,l.jsxs)(C.mQ,{defaultValue:"Project-Info",children:[(0,l.jsxs)(C.dr,{className:"grid w-full grid-cols-2",children:[l.jsx(C.SP,{value:"Project-Info",children:"Project-Info"}),l.jsx(C.SP,{value:"Profiles",children:"Profile Bids"})]}),l.jsx(C.nU,{value:"Project-Info",children:(0,l.jsxs)("div",{className:"space-y-4 md:space-y-8",children:[l.jsx("div",{children:l.jsx(u.Z,{projectName:s.projectName,description:s.description,email:s.email,status:s.status,startDate:s.createdAt,endDate:s.end,projectDomain:s.projectDomain,skills:s.skillsRequired,projectId:s._id,handleCompleteProject:()=>{if(!e){(0,T.Am)({title:"Error",description:"Project ID is missing.",variant:"destructive"});return}f.b.put(`/project/${e}`,{status:Y.sB.COMPLETED}).then(e=>{200===e.status?(t(e=>e?{...e,status:Y.sB.COMPLETED}:e),(0,T.Am)({title:"Success",description:"Project marked as completed!"})):(console.error("Unexpected response:",e),(0,T.Am)({title:"Failed",description:"Failed to mark project as completed.",variant:"destructive"}))}).catch(e=>{console.error("Error updating project status:",e),(0,T.Am)({title:"Error",description:"An error occurred while updating the project status.",variant:"destructive"})})},userRole:"Business"})}),(0,l.jsxs)("div",{children:[l.jsx(o.Ol,{className:"pl-0 ",children:l.jsx(o.ll,{className:"pb-4",children:"Profiles"})}),(0,l.jsxs)(m.lr,{className:"w-full relative pt-3",children:[(0,l.jsxs)(m.KI,{className:"flex mt-3 -ml-2",children:[s.profiles?.map((e,t)=>l.jsx(m.d$,{className:"basis-full md:basis-1/2 lg:basis-1/2 xl:basis-1/3 pl-2",children:l.jsx(w,{domainName:e.domain,description:e.description,email:s.email,status:s.status,startDate:s.createdAt,endDate:s.end,domains:[],skills:e.skills})},t)),s.status!==Y.sB.COMPLETED&&s.status!==Y.sB.REJECTED&&l.jsx(m.d$,{className:"basis-full md:basis-1/2 lg:basis-1/2 xl:basis-1/3 pl-2",children:l.jsx(w,{isLastCard:!0,onAddProfile:()=>{r(!0)}})})]}),s.profiles&&s.profiles.length>0&&l.jsx(l.Fragment,{children:(0,l.jsxs)("div",{className:"flex",children:[l.jsx(m.am,{className:"absolute  left-0 top-1 transform -translate-y-1/2 p-2 shadow-md transition-colors",children:"Previous"}),l.jsx(m.Pz,{className:"absolute right-0 top-1 transform -translate-y-1/2 p-2 shadow-md transition-colors",children:"Next"})]})})]})]})]})}),l.jsx(C.nU,{value:"Profiles",children:l.jsx(Q,{id:e||""})})]})}),(0,l.jsxs)("div",{className:"w-full lg:col-span-1 lg:w-auto mt-8 lg:mt-0 space-y-6 min-w-0",children:[l.jsx(o.ll,{className:"group flex items-center gap-2 text-xl",children:"Interviews"}),(0,l.jsxs)("div",{className:"text-center py-6",children:[l.jsx(i.Z,{className:"mx-auto mb-2 text-gray-500",size:"80"}),l.jsx("p",{className:"text-gray-500 text-sm",children:"No interviews scheduled"})]})]})]}),l.jsx(ed,{projectId:e||"",onProfileAdded:d,open:a,onOpenChange:r})]})]}):l.jsx("div",{className:"flex items-center justify-center min-h-screen",children:l.jsx(x.E,{className:"w-1/2",value:50})})}},1370:(e,s,t)=>{"use strict";t.d(s,{Z:()=>l});var a=t(10326);t(17577);var r=t(37358);let l=({startDate:e,endDate:s})=>{let t=e?new Date(e).toLocaleDateString():"Start Date N/A",l="current"!==s&&s?new Date(s).toLocaleDateString():"Still Going On!";return(0,a.jsxs)("div",{className:"flex relative whitespace-nowrap items-start sm:items-center gap-1 rounded-md ",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2 ",children:[a.jsx(r.Z,{className:"w-4 h-4 sm:w-5 sm:h-5 "}),a.jsx("span",{className:"text-xs sm:text-sm font-medium",children:`Start  ${t}`})]}),a.jsx("p",{children:"-"}),a.jsx("div",{className:"flex items-center ",children:a.jsx("span",{className:"text-xs sm:text-sm font-medium",children:` ${l}`})})]})}},43593:(e,s,t)=>{"use strict";t.d(s,{Z:()=>j});var a=t(10326);t(17577);var r=t(40765),l=t(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,l.Z)("CodeXml",[["path",{d:"m18 16 4-4-4-4",key:"1inbqp"}],["path",{d:"m6 8-4 4 4 4",key:"15zrgr"}],["path",{d:"m14.5 4-5 16",key:"e7oirm"}]]);var n=t(5932);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,l.Z)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]);var d=t(30361),o=t(90434),m=t(29752),x=t(38443),u=t(71064),p=t(78062),h=t(1370),f=t(91664);let j=function({projectName:e,description:s,email:t,status:l,startDate:j,endDate:N,projectDomain:g,skills:b,userRole:v="Business",projectId:y,handleCompleteProject:w}){let{text:C,className:k}=(0,u.S)(l),E=`/${v.toLowerCase()}/project/${y}/milestone`;return(0,a.jsxs)(m.Zb,{className:"shadow-lg border border-gray-800 rounded-lg",children:[(0,a.jsxs)(m.Ol,{children:[(0,a.jsxs)("div",{className:"flex flex-wrap justify-between items-center mb-0.5",children:[a.jsx(m.ll,{className:"text-xl md:text-2xl font-semibold",children:e}),a.jsx(x.C,{className:`${k} px-1 py-0.5 text-xs md:text-sm rounded-md`,children:C})]}),a.jsx(p.Separator,{className:"my-4"})]}),(0,a.jsxs)(m.aY,{className:"space-y-6",children:[a.jsx(h.Z,{startDate:j,endDate:N}),a.jsx("p",{className:"text-sm md:text-base leading-relaxed",children:s}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 md:gap-6",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2 px-3 py-1 text-xs md:text-sm rounded-md shadow-inner w-full md:w-1/2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(r.Z,{className:"w-4 h-4 block md:hidden"}),a.jsx("p",{className:"font-medium",children:"Project Domain:"})]}),a.jsx("div",{className:"flex flex-wrap gap-1",children:g.map((e,s)=>a.jsx(x.C,{className:"bg-gray-200 text-gray-900 text-xs md:text-sm px-2 py-1 rounded-full",children:e},s))})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2 px-3 py-1 text-xs md:text-sm rounded-md shadow-inner w-full md:w-1/2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(i,{className:"w-4 h-4 block md:hidden"}),a.jsx("p",{className:"font-medium",children:"Skills:"})]}),a.jsx("div",{className:"flex flex-wrap gap-1",children:b.map((e,s)=>a.jsx(x.C,{className:"bg-gray-200 text-gray-900 text-xs md:text-sm px-2 py-1 rounded-full",children:e},s))})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4 px-3 py-1 text-xs md:text-sm rounded-md shadow-inner",children:[a.jsx(n.Z,{className:"w-4 h-4"}),a.jsx("span",{className:"text-sm",children:t})]}),(0,a.jsxs)("div",{className:"flex justify-between mt-4",children:[a.jsx(o.default,{href:E,children:(0,a.jsxs)(f.z,{className:"flex items-center px-4 py-2 text-xs md:text-sm font-medium text-white rounded-md bg-blue-600 hover:bg-blue-500",size:"sm",children:[a.jsx(c,{className:"w-4 h-4 mr-1"}),"Milestone"]})}),(0,a.jsxs)(f.z,{className:`flex items-center px-4 py-2 text-xs md:text-sm font-medium text-white rounded-md ${"COMPLETED"===C?"bg-green-600 hover:bg-green-500":"bg-blue-600 hover:bg-blue-500"}`,size:"sm",onClick:w,disabled:!w,children:[a.jsx(d.Z,{className:"w-4 h-4 mr-1"}),"COMPLETED"===C?"Completed":"Mark complete"]})]})]})]})}},9969:(e,s,t)=>{"use strict";t.d(s,{NI:()=>f,Wi:()=>m,l0:()=>d,lX:()=>h,pf:()=>j,xJ:()=>p,zG:()=>N});var a=t(10326),r=t(17577),l=t(99469),i=t(74723),n=t(51223),c=t(44794);let d=i.RV,o=r.createContext({}),m=({...e})=>a.jsx(o.Provider,{value:{name:e.name},children:a.jsx(i.Qr,{...e})}),x=()=>{let e=r.useContext(o),s=r.useContext(u),{getFieldState:t,formState:a}=(0,i.Gc)(),l=t(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=s;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...l}},u=r.createContext({}),p=r.forwardRef(({className:e,...s},t)=>{let l=r.useId();return a.jsx(u.Provider,{value:{id:l},children:a.jsx("div",{ref:t,className:(0,n.cn)("space-y-2",e),...s})})});p.displayName="FormItem";let h=r.forwardRef(({className:e,...s},t)=>{let{error:r,formItemId:l}=x();return a.jsx(c.Label,{ref:t,className:(0,n.cn)(r&&"text-destructive",e),htmlFor:l,...s})});h.displayName="FormLabel";let f=r.forwardRef(({...e},s)=>{let{error:t,formItemId:r,formDescriptionId:i,formMessageId:n}=x();return a.jsx(l.g7,{ref:s,id:r,"aria-describedby":t?`${i} ${n}`:`${i}`,"aria-invalid":!!t,...e})});f.displayName="FormControl";let j=r.forwardRef(({className:e,...s},t)=>{let{formDescriptionId:r}=x();return a.jsx("p",{ref:t,id:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...s})});j.displayName="FormDescription";let N=r.forwardRef(({className:e,children:s,...t},r)=>{let{error:l,formMessageId:i}=x(),c=l?String(l?.message):s;return c?a.jsx("p",{ref:r,id:i,className:(0,n.cn)("text-sm font-medium text-destructive",e),...t,children:c}):null});N.displayName="FormMessage"},44794:(e,s,t)=>{"use strict";t.r(s),t.d(s,{Label:()=>d});var a=t(10326),r=t(17577),l=t(34478),i=t(28671),n=t(51223);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...s},t)=>a.jsx(l.f,{ref:t,className:(0,n.cn)(c(),e),...s}));d.displayName=l.f.displayName},94880:(e,s,t)=>{"use strict";t.d(s,{E:()=>v});var a=t(10326),r=t(17577),l=t(93095),i=t(77335),n="Progress",[c,d]=(0,l.b)(n),[o,m]=c(n),x=r.forwardRef((e,s)=>{var t,r;let{__scopeProgress:l,value:n=null,max:c,getValueLabel:d=h,...m}=e;(c||0===c)&&!N(c)&&console.error((t=`${c}`,`Invalid prop \`max\` of value \`${t}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let x=N(c)?c:100;null===n||g(n,x)||console.error((r=`${n}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let u=g(n,x)?n:null,p=j(u)?d(u,x):void 0;return(0,a.jsx)(o,{scope:l,value:u,max:x,children:(0,a.jsx)(i.WV.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":j(u)?u:void 0,"aria-valuetext":p,role:"progressbar","data-state":f(u,x),"data-value":u??void 0,"data-max":x,...m,ref:s})})});x.displayName=n;var u="ProgressIndicator",p=r.forwardRef((e,s)=>{let{__scopeProgress:t,...r}=e,l=m(u,t);return(0,a.jsx)(i.WV.div,{"data-state":f(l.value,l.max),"data-value":l.value??void 0,"data-max":l.max,...r,ref:s})});function h(e,s){return`${Math.round(e/s*100)}%`}function f(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function j(e){return"number"==typeof e}function N(e){return j(e)&&!isNaN(e)&&e>0}function g(e,s){return j(e)&&!isNaN(e)&&e<=s&&e>=0}p.displayName=u;var b=t(51223);let v=r.forwardRef(({className:e,value:s,...t},r)=>a.jsx(x,{ref:r,className:(0,b.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...t,children:a.jsx(p,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})}));v.displayName=x.displayName},78062:(e,s,t)=>{"use strict";t.r(s),t.d(s,{Separator:()=>n});var a=t(10326),r=t(17577),l=t(90220),i=t(51223);let n=r.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...r},n)=>a.jsx(l.f,{ref:n,decorative:t,orientation:s,className:(0,i.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));n.displayName=l.f.displayName},38227:(e,s,t)=>{"use strict";t.d(s,{O:()=>l});var a=t(10326),r=t(51223);function l({className:e,...s}){return a.jsx("div",{className:(0,r.cn)("animate-pulse rounded-md bg-primary/10",e),...s})}},50384:(e,s,t)=>{"use strict";t.d(s,{SP:()=>d,dr:()=>c,mQ:()=>n,nU:()=>o});var a=t(10326),r=t(17577),l=t(28407),i=t(51223);let n=l.fC,c=r.forwardRef(({className:e,...s},t)=>a.jsx(l.aV,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));c.displayName=l.aV.displayName;let d=r.forwardRef(({className:e,...s},t)=>a.jsx(l.xz,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));d.displayName=l.xz.displayName;let o=r.forwardRef(({className:e,...s},t)=>a.jsx(l.VY,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));o.displayName=l.VY.displayName},46319:(e,s,t)=>{"use strict";t.d(s,{$C:()=>j,Ne:()=>N,yn:()=>f});var a=t(10326),r=t(95920),l=t(57671),i=t(94909),n=t(12070),c=t(66307),d=t(69669),o=t(40617),m=t(69515),x=t(88378),u=t(40900),p=t(98091),h=t(46226);let f=[{href:"#",icon:a.jsx(h.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:a.jsx(r.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:a.jsx(l.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:a.jsx(i.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:a.jsx(i.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:a.jsx(n.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:a.jsx(c.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:a.jsx(d.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:a.jsx(o.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:a.jsx(m.Z,{className:"h-5 w-5"}),label:"Notes"}],j=[{href:"/business/settings/business-info",icon:a.jsx(x.Z,{className:"h-5 w-5"}),label:"Settings"}],N=[{href:"#",icon:a.jsx(h.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:a.jsx(r.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:a.jsx(m.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:a.jsx(u.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:a.jsx(p.Z,{className:"h-5 w-5"}),label:"Trash"}]},39958:(e,s,t)=>{"use strict";var a,r,l;t.d(s,{cd:()=>a,d8:()=>i,kJ:()=>r,sB:()=>l}),function(e){e.Mastery="Mastery",e.Proficient="Proficient",e.Beginner="Beginner"}(a||(a={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.REJECTED="Rejected",e.COMPLETED="Completed"}(r||(r={})),function(e){e.ACTIVE="ACTIVE",e.PENDING="PENDING",e.REJECTED="REJECTED",e.COMPLETED="COMPLETED"}(l||(l={}));let i={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},60610:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var a=t(68570);let r=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\business\project\[project_id]\page.tsx`),{__esModule:l,$$typeof:i}=r;r.default;let n=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\business\project\[project_id]\page.tsx#default`)},34478:(e,s,t)=>{"use strict";t.d(s,{f:()=>n});var a=t(17577),r=t(77335),l=t(10326),i=a.forwardRef((e,s)=>(0,l.jsx)(r.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var n=i},90220:(e,s,t)=>{"use strict";t.d(s,{f:()=>d});var a=t(17577),r=t(77335),l=t(10326),i="horizontal",n=["horizontal","vertical"],c=a.forwardRef((e,s)=>{let{decorative:t,orientation:a=i,...c}=e,d=n.includes(a)?a:i;return(0,l.jsx)(r.WV.div,{"data-orientation":d,...t?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:s})});c.displayName="Separator";var d=c},28407:(e,s,t)=>{"use strict";t.d(s,{VY:()=>A,aV:()=>I,fC:()=>D,xz:()=>S});var a=t(17577),r=t(82561),l=t(93095),i=t(15594),n=t(9815),c=t(77335),d=t(17124),o=t(52067),m=t(88957),x=t(10326),u="Tabs",[p,h]=(0,l.b)(u,[i.Pc]),f=(0,i.Pc)(),[j,N]=p(u),g=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,onValueChange:r,defaultValue:l,orientation:i="horizontal",dir:n,activationMode:u="automatic",...p}=e,h=(0,d.gm)(n),[f,N]=(0,o.T)({prop:a,onChange:r,defaultProp:l});return(0,x.jsx)(j,{scope:t,baseId:(0,m.M)(),value:f,onValueChange:N,orientation:i,dir:h,activationMode:u,children:(0,x.jsx)(c.WV.div,{dir:h,"data-orientation":i,...p,ref:s})})});g.displayName=u;var b="TabsList",v=a.forwardRef((e,s)=>{let{__scopeTabs:t,loop:a=!0,...r}=e,l=N(b,t),n=f(t);return(0,x.jsx)(i.fC,{asChild:!0,...n,orientation:l.orientation,dir:l.dir,loop:a,children:(0,x.jsx)(c.WV.div,{role:"tablist","aria-orientation":l.orientation,...r,ref:s})})});v.displayName=b;var y="TabsTrigger",w=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,disabled:l=!1,...n}=e,d=N(y,t),o=f(t),m=E(d.baseId,a),u=P(d.baseId,a),p=a===d.value;return(0,x.jsx)(i.ck,{asChild:!0,...o,focusable:!l,active:p,children:(0,x.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":u,"data-state":p?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:m,...n,ref:s,onMouseDown:(0,r.M)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(a)}),onKeyDown:(0,r.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(a)}),onFocus:(0,r.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;p||l||!e||d.onValueChange(a)})})})});w.displayName=y;var C="TabsContent",k=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,forceMount:l,children:i,...d}=e,o=N(C,t),m=E(o.baseId,r),u=P(o.baseId,r),p=r===o.value,h=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,x.jsx)(n.z,{present:l||p,children:({present:t})=>(0,x.jsx)(c.WV.div,{"data-state":p?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":m,hidden:!t,id:u,tabIndex:0,...d,ref:s,style:{...e.style,animationDuration:h.current?"0s":void 0},children:t&&i})})});function E(e,s){return`${e}-trigger-${s}`}function P(e,s){return`${e}-content-${s}`}k.displayName=C;var D=g,I=v,S=w,A=k}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,6686,9561,4736,6499,8066,588],()=>t(32929));module.exports=a})();