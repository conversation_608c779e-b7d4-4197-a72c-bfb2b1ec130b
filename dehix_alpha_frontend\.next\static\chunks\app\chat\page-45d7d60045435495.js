(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1929],{98290:function(e,a,t){Promise.resolve().then(t.bind(t,77317))},77317:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return J}});var s=t(57437),r=t(2265),n=t(3274),l=t(47390),i=t(11444),c=t(62688),o=t(64797),d=t(22638),h=t(59791),x=t(74697),m=t(706),u=t(19322),g=t(21216),f=t(85637),p=t(58184),b=t(68954),y=t(60994),v=t(88904),j=t(92991),w=t(6460),N=t(66291),k=t(53238),Z=t(25224),C=t(66648),D=t(12092),I=t(71123),S=t(89733),E=t(21413);let T=e=>{let{onSelect:a}=e,[t,n]=(0,r.useState)(!1);return(0,s.jsxs)(E.J2,{open:t,onOpenChange:n,children:[(0,s.jsx)(E.xo,{asChild:!0,children:(0,s.jsx)(S.z,{className:"my-auto ",variant:"link",size:"sm",children:(0,s.jsx)(D.Z,{className:"h-4 w-4 "})})}),(0,s.jsx)(E.yk,{align:"start",className:"p-0 w-[320px] shadow-md rounded-lg",children:(0,s.jsx)(I.Z,{onEmojiSelect:e=>{a(e.native),n(!1)}})})]})};var z=t(89736),A=t(41448),M=t.n(A),O=t(79055);let R=e=>{let{messageId:a,reactions:t,toggleReaction:r}=e,n=(0,i.v9)(e=>e.user),l=e=>{r(a,e)};return(0,s.jsx)("div",{className:"flex items-center gap-2 mt-2",children:Object.entries(t).map(e=>{let[a,t]=e;return(0,s.jsx)(O.C,{onClick:()=>l(a),className:"cursor-pointer ".concat(Array.isArray(t)&&t.includes(n.uid)?"bg-gray-400":"bg-green"),children:(0,s.jsxs)("span",{className:"flex items-center",children:[a," ",t.length>0&&(0,s.jsx)("span",{className:"ml-2",children:t.length})," "]})},a)})})};R.propTypes={messageId:M().string.isRequired,reactions:M().any.isRequired,toggleReaction:M().func.isRequired};var _=t(37164);function L(e){let{fileName:a,fileUrl:t,fileType:r}=e,n=a.length>15?a.substring(14,28)+"...":a;return(0,s.jsxs)("div",{className:"flex items-center space-x-3 p-2 bg-gray-500 rounded-md w-full max-w-md",children:[(0,s.jsx)("div",{className:"text-2xl",children:(e=>{switch(e){case"pdf":return"\uD83D\uDCC4";case"ppt":case"pptx":return"\uD83D\uDCCA";case"doc":case"docx":return"\uD83D\uDCDD";default:return"\uD83D\uDCC1"}})(r)}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium truncate",children:n}),(0,s.jsx)("p",{className:"text-xs  uppercase",children:r})]}),(0,s.jsx)(S.z,{variant:"ghost",size:"icon",onClick:()=>{let e=document.createElement("a");e.href=t,e.download=a,document.body.appendChild(e),e.click(),document.body.removeChild(e)},title:"Download",children:(0,s.jsx)(_.Z,{className:"h-4 w-4"})})]})}var F=t(49354),P=t(80420),U=t(48185),$=t(55163),W=t(15922),B=t(86763);function H(e){var a;let{conversation:t}=e,[l,c]=(0,r.useState)({userName:"",email:"",profilePic:""}),[o,D]=(0,r.useState)([]),[I,E]=(0,r.useState)(""),[A,M]=(0,r.useState)(!0),[O,_]=(0,r.useState)(!1),H=(0,i.v9)(e=>e.user),K=(0,r.useRef)(null),[q,J]=(0,r.useState)(""),[V,Y]=(0,r.useState)(null),Q=(0,r.useRef)(null),[X,G]=(0,r.useState)(!1),ee=(0,r.useRef)(o.length),[ea,et]=(0,r.useState)(!1);async function es(e,a,t){try{_(!0);let s=new Date().toISOString();await (0,$.Hd)("conversations",null==e?void 0:e.id,{...a,timestamp:s,replyTo:q||null},s)?(t(""),_(!1)):console.error("Failed to send message")}catch(e){console.error("Error sending message:",e)}finally{_(!1)}}if((0,r.useEffect)(()=>{let e=()=>{window.innerWidth>=768&&et(!1)};return window.addEventListener("resize",e),e(),()=>window.removeEventListener("resize",e)},[]),(0,r.useEffect)(()=>{let e;let a=async()=>{let e=t.participants.find(e=>e!==H.uid);if(e)try{let a=await W.b.get("/freelancer/".concat(e));c(a.data.data)}catch(e){console.error("Error fetching primary user:",e),(0,B.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}},s=async()=>{M(!0),e=(0,$.SH)("conversations/".concat(t.id,"/messages"),e=>{D(e),M(!1)},"desc")};return t&&(a(),s()),()=>{e&&e()}},[t,H.uid]),(0,r.useEffect)(()=>{if(o.length>ee.current){var e;null===(e=K.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})}ee.current=o.length},[o.length]),!t)return null;async function er(){let e=document.createElement("input");e.type="file",e.onchange=async()=>{var a;let s=null===(a=e.files)||void 0===a?void 0:a[0];if(s)try{let e=new FormData;e.append("file",s);let a=(await W.b.post("/register/upload-image",e,{headers:{"Content-Type":"multipart/form-data"}})).data.data.Location,r={senderId:H.uid,content:a,timestamp:new Date().toISOString()};es(t,r,E)}catch(e){console.error("Error uploading file:",e)}},e.click()}async function en(){try{let e=(await W.b.post("/meeting",{participants:t.participants})).data.meetLink,a={senderId:H.uid,content:"\uD83D\uDD17 Join the Meet: [Click here](".concat(e,")"),timestamp:new Date().toISOString()};es(t,a,E)}catch(e){console.error("Error creating meet:",e)}}function el(){if(Q.current){let e=Q.current,{selectionStart:a,selectionEnd:t,value:s}=e;if(a===t)return;let r=s.slice(a,t);E("".concat(s.slice(0,a),"**").concat(r,"**").concat(s.slice(t))),e.setSelectionRange(a+2,t+2)}}let ei=()=>{if(Q.current){let e=Q.current,{selectionStart:a,selectionEnd:t,value:s}=e;if(a===t)return;let r=s.slice(a,t);E("".concat(s.slice(0,a),"__").concat(r,"__").concat(s.slice(t))),e.setSelectionRange(a+2,t+2)}};function ec(){if(Q.current){let e=Q.current,{selectionStart:a,selectionEnd:t,value:s}=e;if(a===t)return;let r=s.slice(a,t);E("".concat(s.slice(0,a),"*").concat(r,"*").concat(s.slice(t))),e.setSelectionRange(a+1,t+1)}}async function eo(e,a){let s=o.find(a=>a.id===e),r={...null==s?void 0:s.reactions},n=Object.keys(r).find(e=>{var a;return null===(a=r[e])||void 0===a?void 0:a.includes(H.uid)});n===a?(r[a]=r[a].filter(e=>e!==H.uid),0===r[a].length&&delete r[a]):(n&&(r[n]=r[n].filter(e=>e!==H.uid),0===r[n].length&&delete r[n]),r[a]||(r[a]=[]),r[a].push(H.uid)),await (0,$.fy)("conversations/".concat(t.id,"/messages/"),e,{reactions:r})}return(0,s.jsx)(s.Fragment,{children:A?(0,s.jsx)("div",{className:"flex justify-center items-center p-5 col-span-3",children:(0,s.jsx)(n.Z,{className:"h-6 w-6 text-white animate-spin"})}):(0,s.jsxs)(U.Zb,{className:"col-span-3 w-[92vw] mt-0 min-h-[70vh] border-gray-400  dark:border-white border-2 shadow-none",children:[(0,s.jsx)(U.Ol,{className:"flex flex-row items-center  bg-[#ececec] dark:bg-[#333333] text-gray-800 dark:text-white p-2 rounded-t-lg",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)(P.Avatar,{children:[(0,s.jsx)(P.AvatarImage,{src:l.profilePic,alt:"Image"}),(0,s.jsx)(P.AvatarFallback,{children:l.userName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none text-gray-800 dark:text-white",children:l.userName}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:l.email})]})]})}),(0,s.jsx)(U.aY,{className:"flex-1 px-2 pb-2 pt-2 bg-[#ffffff] dark:bg-[#181818]",children:(0,s.jsxs)("div",{className:"flex flex-col-reverse space-y-4 space-y-reverse overflow-y-auto h-[65vh] md:h-[58vh]",children:[(0,s.jsx)("div",{ref:K}),o.map((e,a)=>{var t;let r=function(e){let a=new Date(e);return(0,j.z)(a)?(0,w.WU)(a,"hh:mm a"):(0,N.g)(a)?"Yesterday, ".concat((0,w.WU)(a,"hh:mm a")):(0,k.X)(a)?(0,w.WU)(a,"MMM dd, hh:mm a"):(0,w.WU)(a,"yyyy MMM dd, hh:mm a")}(e.timestamp),n=(0,Z.Q)(new Date(e.timestamp))+" ago";return(0,s.jsxs)("div",{id:e.id,className:"flex flex-row relative",onMouseEnter:()=>Y(e.id),onMouseLeave:()=>Y(null),children:[e.senderId!==H.uid&&(0,s.jsxs)(P.Avatar,{className:"w-8 h-8 mr-1 my-auto",children:[(0,s.jsx)(P.AvatarImage,{src:l.profilePic,alt:e.senderId}),(0,s.jsx)(P.AvatarFallback,{children:e.senderId.charAt(0).toUpperCase()})]},a),(0,s.jsxs)("div",{className:(0,F.cn)("flex w-max max-w-[65%] flex-col gap-1 rounded-lg px-3 py-2 text-sm",e.senderId===H.uid?"ml-auto bg-[#9155bc] dark:bg-[#580d8f] text-white  rounded-tr-none":"bg-[#d9d9d9] dark:bg-[#333333] text-white  rounded-tl-none"),onClick:()=>{if(e.replyTo){let a=o.find(a=>a.id===e.replyTo);if(a){let e=document.getElementById(a.id);e&&(e.classList.add("bg-gray-200","dark:bg-gray-600","border-2","border-gray-300","dark:border-gray-500","bg-opacity-50","dark:bg-opacity-50"),e.scrollIntoView({behavior:"smooth"}),setTimeout(()=>{e.classList.remove("bg-gray-200","dark:bg-gray-600","border-2","border-gray-300","dark:border-gray-500","bg-opacity-50","dark:bg-opacity-50")},2e3))}}},children:[(0,s.jsx)(z.TooltipProvider,{children:(0,s.jsxs)(z.u,{children:[(0,s.jsx)(z.aJ,{asChild:!0,children:(0,s.jsxs)("div",{className:"break-words rounded-lg w-full",children:[e.replyTo&&(0,s.jsx)("div",{className:"flex items-center justify-between p-2 bg-gray-200 dark:bg-gray-600 rounded-lg border-l-4 border-gray-400 dark:border-gray-500 shadow-sm opacity-100 transition-opacity duration-300 max-w-2xl mb-1",children:(0,s.jsx)("div",{className:"text-sm italic text-gray-600 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 overflow-hidden whitespace-pre-wrap text-ellipsis max-h-[3em] line-clamp-2 max-w-2xl",children:(0,s.jsx)("span",{className:"font-semibold",children:(null===(t=o.find(a=>a.id===e.replyTo))||void 0===t?void 0:t.content)||"Message not found"})})}),e.content.match(/\.(jpeg|jpg|gif|png)$/)?(0,s.jsx)(C.default,{src:e.content||"/placeholder.svg",alt:"Message Image",width:300,height:300,className:"rounded-lg"}):e.content.match(/\.(pdf|doc|docx|ppt|pptx)$/)?(0,s.jsx)(L,{fileName:e.content.split("/").pop()||"File",fileUrl:e.content,fileType:e.content.split(".").pop()||"file"}):(0,s.jsx)(v.U,{className:" ".concat(e.senderId===H.uid?"text-white":"text-black"," dark:text-gray-100"),children:e.content})]})}),(0,s.jsx)(z._v,{side:"bottom",sideOffset:10,children:(0,s.jsx)("p",{className:"  p-1 rounded",children:n})})]})}),(0,s.jsx)(R,{messageId:e.id,reactions:e.reactions||{},toggleReaction:eo}),(0,s.jsxs)("div",{className:(0,F.cn)("text-[10px] mt-1 text-right",e.senderId===H.uid?"text-gray-100 dark:text-gray-300 flex items-center gap-0.5":"text-gray-500 dark:text-gray-400"),children:[r,e.senderId===H.uid&&(0,s.jsx)("span",{className:"ml-1",children:(0,s.jsx)(d.Z,{className:"w-4"})})]})]}),(0,s.jsx)("div",{className:"relative ".concat(e.senderId===H.uid?"text-right":"text-left"),children:V===e.id&&(0,s.jsx)(h.Z,{className:"h-4 w-4 absolute cursor-pointer top-0 z-10 pointer-events-auto \n        ".concat(e.senderId===H.uid?"right-2 text-white ":"-left-5 text-black"),onClick:()=>J(e.id)})}),e.senderId!==H.uid&&(0,s.jsx)(T,{onSelect:a=>eo(e.id,a)})]},a)})]})}),(0,s.jsx)(U.eW,{className:"bg-[#ffffff] dark:bg-[#181818] rounded-b-lg p-2",children:(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),0!==I.trim().length&&(es(t,{senderId:H.uid,content:I,timestamp:new Date().toISOString(),replyTo:q||null},E),J(""))},className:"flex flex-col w-full mb-2",children:[q&&(0,s.jsxs)("div",{className:"flex items-center justify-between p-2 rounded-lg shadow-sm opacity-90 bg-white dark:bg-[#2D2D2D] mb-2 border-l-4 border-gray-400 dark:border-gray-500 ",children:[(0,s.jsx)("div",{className:"text-sm italic text-gray-600 dark:text-gray-300 overflow-hidden whitespace-nowrap text-ellipsis max-w-full",children:(0,s.jsx)("span",{className:"font-semibold",children:(null===(a=o.find(e=>e.id===q))||void 0===a?void 0:a.content.replace(/\*/g,""))||"Message not found"})}),(0,s.jsx)(S.z,{onClick:()=>J(""),className:"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 bg-transparent hover:bg-gray-200 dark:hover:bg-gray-600 h-6 rounded-full",title:"Cancel Reply",variant:"ghost",children:(0,s.jsx)(x.Z,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"relative bg-[#ececec] dark:bg-[#333333] rounded-full border border-gray-300 dark:border-gray-600 p-1 flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"sm:hidden",children:(0,s.jsx)("button",{onClick:()=>et(!ea),className:"p-2 text-gray-500 dark:text-gray-400",children:(0,s.jsx)(m.Z,{className:"h-5 w-5 text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200"})})}),(0,s.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 bg-white dark:bg-gray-800 p-3 rounded-lg shadow-lg transition-transform duration-300 ".concat(ea?"translate-y-0 opacity-100":"translate-y-5 opacity-0 pointer-events-none"),children:(0,s.jsxs)("div",{className:"flex justify-around space-x-3",children:[(0,s.jsx)("button",{onClick:el,className:"p-2",children:(0,s.jsx)(u.Z,{className:"h-5 w-5"})}),(0,s.jsx)("button",{onClick:ec,className:"p-2",children:(0,s.jsx)(g.Z,{className:"h-5 w-5"})}),(0,s.jsx)("button",{onClick:ei,className:"p-2",children:(0,s.jsx)(f.Z,{className:"h-5 w-5"})}),(0,s.jsx)("button",{onClick:er,className:"p-2",children:(0,s.jsx)(p.Z,{className:"h-5 w-5"})}),(0,s.jsx)("button",{onClick:en,className:"p-2",children:(0,s.jsx)(b.Z,{className:"h-5 w-5"})})]})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(S.z,{size:"icon",variant:"ghost",title:"Text Formatting",className:"group text-gray-500 hidden md:flex dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-600 rounded-full",onClick:()=>{G(e=>!e)},children:(0,s.jsx)(m.Z,{className:"h-4 w-4"})}),X&&(0,s.jsxs)("div",{className:"formatting-options",children:[(0,s.jsx)(S.z,{size:"icon",type:"button",onClick:el,title:"Bold",className:"group text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-600 rounded-full",children:(0,s.jsx)(u.Z,{className:"h-5 w-5 text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200"})}),(0,s.jsx)(S.z,{type:"button",size:"icon",onClick:ec,title:"Italics",className:"group text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-600 rounded-full",children:(0,s.jsx)(g.Z,{className:"h-5 w-5 text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200"})}),(0,s.jsx)(S.z,{type:"button",size:"icon",onClick:ei,title:"Underline",className:"group text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-600 rounded-full",children:(0,s.jsx)(f.Z,{className:"h-5 w-5 text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200"})})]})]}),(0,s.jsx)("textarea",{ref:Q,className:"w-full flex-1 h-10 max-h-32 resize-none border-none p-2 bg-transparent placeholder-gray-500 dark:placeholder-gray-400 text-gray-800 dark:text-gray-100 focus:outline-none",placeholder:"Type message",value:I,rows:1,onChange:e=>E(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||e.ctrlKey||(e.preventDefault(),I.trim().length>0&&(_(!0),setTimeout(()=>{E(""),_(!1)},1e3)))}}),(0,s.jsx)("button",{disabled:!I.trim().length||O,className:"p-2 flex md:hidden disabled:text-gray-600",children:O?(0,s.jsx)(n.Z,{className:"h-5 w-5 animate-spin "}):(0,s.jsx)(y.Z,{className:"h-5 w-5"})}),(0,s.jsxs)("div",{className:"hidden sm:flex items-center space-x-2 pr-2",children:[(0,s.jsx)("button",{onClick:er,className:"p-2",children:(0,s.jsx)(p.Z,{className:"h-5 w-5"})}),(0,s.jsx)("button",{onClick:en,className:"p-2",children:(0,s.jsx)(b.Z,{className:"h-5 w-5"})}),(0,s.jsx)("button",{disabled:!I.trim().length||O,className:"p-2 disabled:text-gray-600",children:O?(0,s.jsx)(n.Z,{className:"h-5 w-5 animate-spin"}):(0,s.jsx)(y.Z,{className:"h-5 w-5"})})]})]})]})})]})})}var K=t(82230),q=t(66227),J=()=>{let e=(0,i.v9)(e=>e.user),[a,t]=(0,r.useState)([]),[d,h]=(0,r.useState)(a[0]),[x,m]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{let a;return(async()=>{m(!0),a=await (0,$.K5)("conversations",e.uid,e=>{t(e),m(!1)})})(),()=>{a&&a()}},[e.uid]),(0,r.useEffect)(()=>{!d&&a.length>0&&h(a[0])},[a,d]),(0,s.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,s.jsx)(o.Z,{menuItemsTop:"business"===e.type?K.yn:q.yL,menuItemsBottom:"business"===e.type?K.$C:q.$C,active:"Chats",conversations:a,setActiveConversation:h,activeConversation:d}),(0,s.jsxs)("div",{className:"flex flex-col mb-8 sm:gap-8 sm:py-0 sm:pl-14",children:[(0,s.jsx)(c.Z,{menuItemsTop:"business"===e.type?K.yn:q.yL,menuItemsBottom:"business"===e.type?K.$C:q.$C,activeMenu:"Chats",conversations:a,setActiveConversation:h,activeConversation:d,breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Chats",link:"/dashboard/chats"}],searchPlaceholder:"Search..."}),(0,s.jsx)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-4 lg:grid-cols-3 xl:grid-cols-3",children:x?(0,s.jsx)("div",{className:"col-span-3 flex justify-center items-center p-5",children:(0,s.jsx)(n.Z,{className:"h-6 w-6 text-primary animate-spin"})}):a.length>0?(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(H,{conversation:d,conversations:a,setActiveConversation:h})}):(0,s.jsxs)("div",{className:"col-span-3 flex flex-col items-center justify-center h-full px-4 py-16 text-center text-muted-foreground",children:[(0,s.jsx)(l.Z,{className:"w-10 h-10 mb-2"}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"No conversations found"}),(0,s.jsx)("p",{className:"text-sm",children:"Start a new chat or wait for others to connect!"})]})})]})]})}},82230:function(e,a,t){"use strict";t.d(a,{$C:function(){return p},Ne:function(){return b},yn:function(){return f}});var s=t(57437),r=t(11005),n=t(98960),l=t(38133),i=t(20897),c=t(13231),o=t(71935),d=t(47390),h=t(73347),x=t(24258),m=t(5891),u=t(10883),g=t(66648);let f=[{href:"#",icon:(0,s.jsx)(g.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,s.jsx)(r.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:(0,s.jsx)(n.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:(0,s.jsx)(l.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:(0,s.jsx)(l.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:(0,s.jsx)(i.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:(0,s.jsx)(c.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:(0,s.jsx)(o.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:(0,s.jsx)(d.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,s.jsx)(h.Z,{className:"h-5 w-5"}),label:"Notes"}],p=[{href:"/business/settings/business-info",icon:(0,s.jsx)(x.Z,{className:"h-5 w-5"}),label:"Settings"}],b=[{href:"#",icon:(0,s.jsx)(g.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,s.jsx)(r.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:(0,s.jsx)(h.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:(0,s.jsx)(m.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:(0,s.jsx)(u.Z,{className:"h-5 w-5"}),label:"Trash"}]},66227:function(e,a,t){"use strict";t.d(a,{$C:function(){return y},yL:function(){return v},yn:function(){return b}});var s=t(57437),r=t(11005),n=t(33149),l=t(76035),i=t(49100),c=t(40064),o=t(43193),d=t(36141),h=t(33907),x=t(47390),m=t(73347),u=t(24258),g=t(5891),f=t(10883),p=t(66648);let b=[{href:"#",icon:(0,s.jsx)(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,s.jsx)(r.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:(0,s.jsx)(n.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:(0,s.jsx)(l.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:(0,s.jsx)(i.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:(0,s.jsx)(c.Z,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:(0,s.jsx)(o.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:(0,s.jsx)(d.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:(0,s.jsx)(h.Z,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:(0,s.jsx)(x.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,s.jsx)(m.Z,{className:"h-5 w-5"}),label:"Notes"}],y=[{href:"/freelancer/settings/personal-info",icon:(0,s.jsx)(u.Z,{className:"h-5 w-5"}),label:"Settings"}];p.default,r.Z,m.Z,g.Z,f.Z;let v=[{href:"#",icon:(0,s.jsx)(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,s.jsx)(r.Z,{className:"h-5 w-5"}),label:"Home"}]}},function(e){e.O(0,[4358,7481,9208,2010,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,9834,2455,9726,2688,2971,7023,1744],function(){return e(e.s=98290)}),_N_E=e.O()}]);