(()=>{var e={};e.id=5916,e.ids=[5916],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},30704:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>d,pages:()=>p,routeModule:()=>h,tree:()=>c}),t(34658),t(54302),t(12523);var s=t(23191),i=t(88716),a=t(37922),n=t.n(a),l=t(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let c=["",{children:["freelancer",{children:["interview",{children:["history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,34658)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\interview\\history\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\interview\\history\\page.tsx"],d="/freelancer/interview/history/page",u={require:t,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/freelancer/interview/history/page",pathname:"/freelancer/interview/history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},95734:(e,r,t)=>{Promise.resolve().then(t.bind(t,67749))},47546:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},23015:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},67749:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(10326),i=t(17577),a=t(97685),n=t(23015),l=t(10143),o=t(92166),c=t(30325),p=t(91664),d=t(40588);function u(){let[e,r]=(0,i.useState)("All");return(0,s.jsxs)("div",{className:"flex min-h-screen w-full",children:[s.jsx(o.Z,{menuItemsTop:c.y,menuItemsBottom:c.$,active:"History"}),(0,s.jsxs)("div",{className:"flex flex-col sm:gap-4 mb-8 sm:py-0 sm:pl-14 w-full",children:[s.jsx(d.Z,{breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Interview",link:"/freelancer/interview/profile"},{label:"History Interviews",link:"#"}],menuItemsTop:c.y,menuItemsBottom:c.$,activeMenu:"History"}),(0,s.jsxs)("div",{className:"ml-10",children:[s.jsx("h1",{className:"text-3xl font-bold",children:"History Interviews"}),s.jsx("p",{className:"text-gray-400 mt-2",children:"Review your past interviews and reflect on your progress and experiences."})]}),(0,s.jsxs)("div",{className:"flex flex-1 items-start gap-4 p-2 sm:px-6 sm:py-0 md:gap-8 lg:flex-col xl:flex-col pt-2 pl-4 sm:pt-4 sm:pl-6 md:pt-6 md:pl-8",children:[(0,s.jsxs)(l.h_,{children:[s.jsx(l.$F,{asChild:!0,children:(0,s.jsxs)(p.z,{variant:"outline",size:"sm",className:"h-7 gap-1 text-sm",children:[s.jsx(a.Z,{className:"h-3.5 w-3.5"}),s.jsx("span",{className:"sr-only sm:not-sr-only",children:"Filter"})]})}),(0,s.jsxs)(l.AW,{align:"end",children:[s.jsx(l.Ju,{children:"Filter by"}),s.jsx(l.VD,{}),s.jsx(l.bO,{checked:"All"===e,onSelect:()=>r("All"),children:"All"}),s.jsx(l.bO,{checked:"Skills"===e,onSelect:()=>r("Skills"),children:"Skills"}),s.jsx(l.bO,{checked:"Domain"===e,onSelect:()=>r("Domain"),children:"Domain"})]})]}),(0,s.jsxs)("div",{className:"text-center py-10 w-[90vw] mt-10",children:[s.jsx(n.Z,{className:"mx-auto text-gray-500",size:"100"}),s.jsx("p",{className:"text-gray-500",children:"No Inverview Scheduled for you."})]})]})]})]})}},30325:(e,r,t)=>{"use strict";t.d(r,{$:()=>h,y:()=>u});var s=t(10326),i=t(95920),a=t(94909),n=t(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,n.Z)("ListVideo",[["path",{d:"M12 12H3",key:"18klou"}],["path",{d:"M16 6H3",key:"1wxfjs"}],["path",{d:"M12 18H3",key:"11ftsu"}],["path",{d:"m16 12 5 3-5 3v-6Z",key:"zpskkp"}]]);var o=t(47546);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,n.Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var p=t(88378),d=t(46226);let u=[{href:"#",icon:s.jsx(d.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:s.jsx(i.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/interview/profile",icon:s.jsx(a.Z,{className:"h-5 w-5"}),label:"Profile"},{href:"/freelancer/interview/current",icon:s.jsx(l,{className:"h-5 w-5"}),label:"Current"},{href:"/freelancer/interview/bids",icon:s.jsx(o.Z,{className:"h-5 w-5"}),label:"Bids"},{href:"/freelancer/interview/history",icon:s.jsx(c,{className:"h-5 w-5"}),label:"History"}],h=[{href:"/freelancer/settings/personal-info",icon:s.jsx(p.Z,{className:"h-5 w-5"}),label:"Settings"}]},34658:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>n,__esModule:()=>a,default:()=>l});var s=t(68570);let i=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\interview\history\page.tsx`),{__esModule:a,$$typeof:n}=i;i.default;let l=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\interview\history\page.tsx#default`)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588],()=>t(30704));module.exports=s})();