"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2308],{24241:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},70518:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},32309:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Dot",[["circle",{cx:"12.1",cy:"12.1",r:"1",key:"18d7e5"}]])},87140:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},42873:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},24258:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},38364:function(e,t,n){n.d(t,{f:function(){return i}});var r=n(2265),o=n(18676),l=n(57437),a=r.forwardRef((e,t)=>(0,l.jsx)(o.WV.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var i=a},7568:function(e,t,n){n.d(t,{VY:function(){return q},h_:function(){return G},fC:function(){return H},xz:function(){return $}});var r=n(2265),o=n(78149),l=n(1584),a=n(98324),i=n(53938),u=n(20589),s=n(80467),c=n(53201),d=n(25510),p=n(56935),f=n(31383),v=n(18676),h=n(57437),g=r.forwardRef((e,t)=>{let{children:n,...o}=e,l=r.Children.toArray(n),a=l.find(b);if(a){let e=a.props.children,n=l.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,h.jsx)(m,{...o,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,h.jsx)(m,{...o,ref:t,children:n})});g.displayName="Slot";var m=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e,a;let i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref;return r.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...e)=>{l(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props),ref:t?(0,l.F)(t,i):i})}return r.Children.count(n)>1?r.Children.only(null):null});m.displayName="SlotClone";var y=({children:e})=>(0,h.jsx)(h.Fragment,{children:e});function b(e){return r.isValidElement(e)&&e.type===y}var x=n(91715),w=n(78369),C=n(9219),E="Popover",[P,k]=(0,a.b)(E,[d.D7]),S=(0,d.D7)(),[R,M]=P(E),j=e=>{let{__scopePopover:t,children:n,open:o,defaultOpen:l,onOpenChange:a,modal:i=!1}=e,u=S(t),s=r.useRef(null),[p,f]=r.useState(!1),[v=!1,g]=(0,x.T)({prop:o,defaultProp:l,onChange:a});return(0,h.jsx)(d.fC,{...u,children:(0,h.jsx)(R,{scope:t,contentId:(0,c.M)(),triggerRef:s,open:v,onOpenChange:g,onOpenToggle:r.useCallback(()=>g(e=>!e),[g]),hasCustomAnchor:p,onCustomAnchorAdd:r.useCallback(()=>f(!0),[]),onCustomAnchorRemove:r.useCallback(()=>f(!1),[]),modal:i,children:n})})};j.displayName=E;var D="PopoverAnchor";r.forwardRef((e,t)=>{let{__scopePopover:n,...o}=e,l=M(D,n),a=S(n),{onCustomAnchorAdd:i,onCustomAnchorRemove:u}=l;return r.useEffect(()=>(i(),()=>u()),[i,u]),(0,h.jsx)(d.ee,{...a,...o,ref:t})}).displayName=D;var O="PopoverTrigger",A=r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,a=M(O,n),i=S(n),u=(0,l.e)(t,a.triggerRef),s=(0,h.jsx)(v.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":L(a.open),...r,ref:u,onClick:(0,o.M)(e.onClick,a.onOpenToggle)});return a.hasCustomAnchor?s:(0,h.jsx)(d.ee,{asChild:!0,...i,children:s})});A.displayName=O;var F="PopoverPortal",[W,_]=P(F,{forceMount:void 0}),T=e=>{let{__scopePopover:t,forceMount:n,children:r,container:o}=e,l=M(F,t);return(0,h.jsx)(W,{scope:t,forceMount:n,children:(0,h.jsx)(f.z,{present:n||l.open,children:(0,h.jsx)(p.h,{asChild:!0,container:o,children:r})})})};T.displayName=F;var N="PopoverContent",B=r.forwardRef((e,t)=>{let n=_(N,e.__scopePopover),{forceMount:r=n.forceMount,...o}=e,l=M(N,e.__scopePopover);return(0,h.jsx)(f.z,{present:r||l.open,children:l.modal?(0,h.jsx)(I,{...o,ref:t}):(0,h.jsx)(V,{...o,ref:t})})});B.displayName=N;var I=r.forwardRef((e,t)=>{let n=M(N,e.__scopePopover),a=r.useRef(null),i=(0,l.e)(t,a),u=r.useRef(!1);return r.useEffect(()=>{let e=a.current;if(e)return(0,w.Ry)(e)},[]),(0,h.jsx)(C.Z,{as:g,allowPinchZoom:!0,children:(0,h.jsx)(Z,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),u.current||null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;u.current=r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),V=r.forwardRef((e,t)=>{let n=M(N,e.__scopePopover),o=r.useRef(!1),l=r.useRef(!1);return(0,h.jsx)(Z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,a;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current||null===(a=n.triggerRef.current)||void 0===a||a.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{var r,a;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(l.current=!0));let i=t.target;(null===(a=n.triggerRef.current)||void 0===a?void 0:a.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),Z=r.forwardRef((e,t)=>{let{__scopePopover:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:l,disableOutsidePointerEvents:a,onEscapeKeyDown:c,onPointerDownOutside:p,onFocusOutside:f,onInteractOutside:v,...g}=e,m=M(N,n),y=S(n);return(0,u.EW)(),(0,h.jsx)(s.M,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:l,children:(0,h.jsx)(i.XB,{asChild:!0,disableOutsidePointerEvents:a,onInteractOutside:v,onEscapeKeyDown:c,onPointerDownOutside:p,onFocusOutside:f,onDismiss:()=>m.onOpenChange(!1),children:(0,h.jsx)(d.VY,{"data-state":L(m.open),role:"dialog",id:m.contentId,...y,...g,ref:t,style:{...g.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),z="PopoverClose";function L(e){return e?"open":"closed"}r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,l=M(z,n);return(0,h.jsx)(v.WV.button,{type:"button",...r,ref:t,onClick:(0,o.M)(e.onClick,()=>l.onOpenChange(!1))})}).displayName=z,r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=S(n);return(0,h.jsx)(d.Eh,{...o,...r,ref:t})}).displayName="PopoverArrow";var H=j,$=A,G=T,q=B},48484:function(e,t,n){n.d(t,{f:function(){return s}});var r=n(2265),o=n(18676),l=n(57437),a="horizontal",i=["horizontal","vertical"],u=r.forwardRef((e,t)=>{let{decorative:n,orientation:r=a,...u}=e,s=i.includes(r)?r:a;return(0,l.jsx)(o.WV.div,{"data-orientation":s,...n?{role:"none"}:{"aria-orientation":"vertical"===s?s:void 0,role:"separator"},...u,ref:t})});u.displayName="Separator";var s=u},66431:function(e,t,n){n.d(t,{VM:function(){return v},uZ:function(){return h}});var r=n(2265),o=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,c=(e,t,n)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,d=(e,t)=>{for(var n in t||(t={}))u.call(t,n)&&c(e,n,t[n]);if(i)for(var n of i(t))s.call(t,n)&&c(e,n,t[n]);return e},p=(e,t)=>l(e,a(t)),f=(e,t)=>{var n={};for(var r in e)u.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&i)for(var r of i(e))0>t.indexOf(r)&&s.call(e,r)&&(n[r]=e[r]);return n},v=r.createContext({}),h=r.forwardRef((e,t)=>{let n;var o,l,a,i,u,{value:s,onChange:c,maxLength:h,textAlign:y="left",pattern:b="^\\d+$",inputMode:x="numeric",onComplete:w,pushPasswordManagerStrategy:C="increase-width",containerClassName:E,noScriptCSSFallback:P=m,render:k,children:S}=e,R=f(e,["value","onChange","maxLength","textAlign","pattern","inputMode","onComplete","pushPasswordManagerStrategy","containerClassName","noScriptCSSFallback","render","children"]);let[M,j]=r.useState("string"==typeof R.defaultValue?R.defaultValue:""),D=null!=s?s:M,O=(n=r.useRef(),r.useEffect(()=>{n.current=D}),n.current),A=r.useCallback(e=>{null==c||c(e),j(e)},[c]),F=r.useMemo(()=>b?"string"==typeof b?new RegExp(b):b:null,[b]),W=r.useRef(null),_=r.useRef(null),T=r.useRef({value:D,onChange:A,isIOS:"undefined"!=typeof window&&(null==(l=null==(o=null==window?void 0:window.CSS)?void 0:o.supports)?void 0:l.call(o,"-webkit-touch-callout","none"))}),N=r.useRef({prev:[null==(a=W.current)?void 0:a.selectionStart,null==(i=W.current)?void 0:i.selectionEnd,null==(u=W.current)?void 0:u.selectionDirection]});r.useImperativeHandle(t,()=>W.current,[]),r.useEffect(()=>{let e=W.current,t=_.current;if(!e||!t)return;function n(){if(document.activeElement!==e){L(null),$(null);return}let t=e.selectionStart,n=e.selectionEnd,r=e.selectionDirection,o=e.maxLength,l=e.value,a=N.current.prev,i=-1,u=-1,s;if(0!==l.length&&null!==t&&null!==n){let e=t===n,r=t===l.length&&l.length<o;if(e&&!r){if(0===t)i=0,u=1,s="forward";else if(t===o)i=t-1,u=t,s="backward";else if(o>1&&l.length>1){let e=0;if(null!==a[0]&&null!==a[1]){s=t<a[1]?"backward":"forward";let n=a[0]===a[1]&&a[0]<o;"backward"!==s||n||(e=-1)}i=e+t,u=e+t+1}}-1!==i&&-1!==u&&i!==u&&W.current.setSelectionRange(i,u,s)}let c=-1!==i?i:t,d=-1!==u?u:n,p=null!=s?s:r;L(c),$(d),N.current.prev=[c,d,p]}if(T.current.value!==e.value&&T.current.onChange(e.value),N.current.prev=[e.selectionStart,e.selectionEnd,e.selectionDirection],document.addEventListener("selectionchange",n,{capture:!0}),n(),document.activeElement===e&&Z(!0),!document.getElementById("input-otp-style")){let e=document.createElement("style");if(e.id="input-otp-style",document.head.appendChild(e),e.sheet){let t="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";g(e.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),g(e.sheet,`[data-input-otp]:autofill { ${t} }`),g(e.sheet,`[data-input-otp]:-webkit-autofill { ${t} }`),g(e.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),g(e.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let r=()=>{t&&t.style.setProperty("--root-height",`${e.clientHeight}px`)};r();let o=new ResizeObserver(r);return o.observe(e),()=>{document.removeEventListener("selectionchange",n,{capture:!0}),o.disconnect()}},[]);let[B,I]=r.useState(!1),[V,Z]=r.useState(!1),[z,L]=r.useState(null),[H,$]=r.useState(null);r.useEffect(()=>{var e;setTimeout(e=()=>{var e,t,n,r;null==(e=W.current)||e.dispatchEvent(new Event("input"));let o=null==(t=W.current)?void 0:t.selectionStart,l=null==(n=W.current)?void 0:n.selectionEnd,a=null==(r=W.current)?void 0:r.selectionDirection;null!==o&&null!==l&&(L(o),$(l),N.current.prev=[o,l,a])},0),setTimeout(e,10),setTimeout(e,50)},[D,V]),r.useEffect(()=>{void 0!==O&&D!==O&&O.length<h&&D.length===h&&(null==w||w(D))},[h,w,O,D]);let G=function({containerRef:e,inputRef:t,pushPasswordManagerStrategy:n,isFocused:o}){let l=r.useRef({done:!1,refocused:!1}),[a,i]=r.useState(!1),[u,s]=r.useState(!1),[c,d]=r.useState(!1),p=r.useMemo(()=>"none"!==n&&("increase-width"===n||"experimental-no-flickering"===n)&&a&&u,[a,u,n]),f=r.useCallback(()=>{let r=e.current,o=t.current;if(!r||!o||c||"none"===n)return;let a=r.getBoundingClientRect().left+r.offsetWidth,u=r.getBoundingClientRect().top+r.offsetHeight/2;if(!(0===document.querySelectorAll('[data-lastpass-icon-root],com-1password-button,[data-dashlanecreated],[style$="2147483647 !important;"]').length&&document.elementFromPoint(a-18,u)===r)&&(i(!0),d(!0),!l.current.refocused&&document.activeElement===o)){let e=[o.selectionStart,o.selectionEnd];o.blur(),o.focus(),o.setSelectionRange(e[0],e[1]),l.current.refocused=!0}},[e,t,c,n]);return r.useEffect(()=>{let t=e.current;if(!t||"none"===n)return;function r(){s(window.innerWidth-t.getBoundingClientRect().right>=40)}r();let o=setInterval(r,1e3);return()=>{clearInterval(o)}},[e,n]),r.useEffect(()=>{let e=o||document.activeElement===t.current;if("none"===n||!e)return;let r=setTimeout(f,0),l=setTimeout(f,2e3),a=setTimeout(f,5e3),i=setTimeout(()=>{d(!0)},6e3);return()=>{clearTimeout(r),clearTimeout(l),clearTimeout(a),clearTimeout(i)}},[t,o,n,f]),{hasPWMBadge:a,willPushPWMBadge:p,PWM_BADGE_SPACE_WIDTH:"40px"}}({containerRef:_,inputRef:W,pushPasswordManagerStrategy:C,isFocused:V}),q=r.useCallback(e=>{let t=e.currentTarget.value.slice(0,h);if(t.length>0&&F&&!F.test(t)){e.preventDefault();return}"string"==typeof O&&t.length<O.length&&document.dispatchEvent(new Event("selectionchange")),A(t)},[h,A,O,F]),U=r.useCallback(()=>{var e;if(W.current){let t=Math.min(W.current.value.length,h-1),n=W.current.value.length;null==(e=W.current)||e.setSelectionRange(t,n),L(t),$(n)}Z(!0)},[h]),Y=r.useCallback(e=>{var t,n;let r=W.current;if(!T.current.isIOS||!e.clipboardData||!r)return;let o=e.clipboardData.getData("text/plain");e.preventDefault();let l=null==(t=W.current)?void 0:t.selectionStart,a=null==(n=W.current)?void 0:n.selectionEnd,i=(l!==a?D.slice(0,l)+o+D.slice(a):D.slice(0,l)+o+D.slice(l)).slice(0,h);if(i.length>0&&F&&!F.test(i))return;r.value=i,A(i);let u=Math.min(i.length,h-1),s=i.length;r.setSelectionRange(u,s),L(u),$(s)},[h,A,F,D]),K=r.useMemo(()=>({position:"relative",cursor:R.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[R.disabled]),X=r.useMemo(()=>({position:"absolute",inset:0,width:G.willPushPWMBadge?`calc(100% + ${G.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:G.willPushPWMBadge?`inset(0 ${G.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:y,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[G.PWM_BADGE_SPACE_WIDTH,G.willPushPWMBadge,y]),J=r.useMemo(()=>r.createElement("input",p(d({autoComplete:R.autoComplete||"one-time-code"},R),{"data-input-otp":!0,"data-input-otp-mss":z,"data-input-otp-mse":H,inputMode:x,pattern:null==F?void 0:F.source,style:X,maxLength:h,value:D,ref:W,onPaste:e=>{var t;Y(e),null==(t=R.onPaste)||t.call(R,e)},onChange:q,onMouseOver:e=>{var t;I(!0),null==(t=R.onMouseOver)||t.call(R,e)},onMouseLeave:e=>{var t;I(!1),null==(t=R.onMouseLeave)||t.call(R,e)},onFocus:e=>{var t;U(),null==(t=R.onFocus)||t.call(R,e)},onBlur:e=>{var t;Z(!1),null==(t=R.onBlur)||t.call(R,e)}})),[q,U,Y,x,X,h,H,z,R,null==F?void 0:F.source,D]),Q=r.useMemo(()=>({slots:Array.from({length:h}).map((e,t)=>{let n=V&&null!==z&&null!==H&&(z===H&&t===z||t>=z&&t<H),r=void 0!==D[t]?D[t]:null;return{char:r,isActive:n,hasFakeCaret:n&&null===r}}),isFocused:V,isHovering:!R.disabled&&B}),[V,B,h,H,z,R.disabled,D]),ee=r.useMemo(()=>k?k(Q):r.createElement(v.Provider,{value:Q},S),[S,Q,k]);return r.createElement(r.Fragment,null,null!==P&&r.createElement("noscript",null,r.createElement("style",null,P)),r.createElement("div",{ref:_,"data-input-otp-container":!0,style:K,className:E},ee,r.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},J)))});function g(e,t){try{e.insertRule(t)}catch(e){console.error("input-otp could not insert CSS rule:",t)}}h.displayName="Input";var m=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`}}]);