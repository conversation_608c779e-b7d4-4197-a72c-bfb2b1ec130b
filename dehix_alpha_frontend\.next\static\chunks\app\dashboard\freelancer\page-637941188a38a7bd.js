(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4306],{42050:function(e,s,t){Promise.resolve().then(t.bind(t,15305))},15305:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return Y}});var a=t(57437),r=t(87592),l=t(92940),i=t(40933),n=t(43224),c=t(11444),d=t(2265),o=t(48185),m=t(86864),u=t(41969),x=t(15922),h=t(64797),j=t(66227),f=t(29406),p=t(87138),g=t(44475),N=t(47304),v=t(79055),b=t(89733),y=t(2183),w=t(54662),E=e=>{let{projects:s,loading:t,type:r}=e,[l,i]=(0,d.useState)(null),[n,c]=(0,d.useState)(!1),[m,u]=(0,d.useState)(!1),x=()=>{c(!1),i(null)},h=e=>{i(e),c(!0)};return(0,a.jsxs)(o.Zb,{children:[(0,a.jsxs)(o.Ol,{className:"px-7",children:[(0,a.jsx)(o.ll,{children:"Projects"}),(0,a.jsx)(o.SZ,{children:"Recent projects from your account."})]}),(0,a.jsx)(o.aY,{children:(0,a.jsxs)(N.iA,{children:[(0,a.jsx)(N.xD,{children:(0,a.jsxs)(N.SC,{children:[(0,a.jsx)(N.ss,{className:"text-start",children:"Project Name"}),(0,a.jsx)(N.ss,{className:"text-center",children:"Verification"}),(0,a.jsx)(N.ss,{className:"text-center",children:"Start Date"}),(0,a.jsx)(N.ss,{className:"text-center",children:"Status"}),(0,a.jsx)(N.ss,{className:"text-center",children:"Actions"})]})}),(0,a.jsx)(N.RM,{children:t?[void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)(N.SC,{children:[(0,a.jsx)(N.pj,{children:(0,a.jsx)(y.O,{className:"h-4 w-32"})}),(0,a.jsx)(N.pj,{className:"text-center",children:(0,a.jsx)(y.O,{className:"h-4 w-20"})}),(0,a.jsx)(N.pj,{className:"text-center",children:(0,a.jsx)(y.O,{className:"h-4 w-16"})}),(0,a.jsx)(N.pj,{className:"text-center",children:(0,a.jsx)(y.O,{className:"h-4 w-20"})}),(0,a.jsx)(N.pj,{className:"text-center",children:(0,a.jsx)(y.O,{className:"h-8 w-24"})}),(0,a.jsx)(N.pj,{className:"text-center",children:(0,a.jsx)(y.O,{className:"h-4 w-20"})})]},s)):s.length>0?s.map(e=>{var s,t,i,d;return(0,a.jsxs)(N.SC,{children:[(0,a.jsx)(N.pj,{children:(0,a.jsx)("div",{className:"font-medium",children:e.projectName})}),(0,a.jsx)(N.pj,{className:"text-center",children:(0,a.jsx)(v.C,{className:"text-xs",variant:e.verified?"secondary":"outline",children:e.verified?"Verified":"Not Verified"})}),(0,a.jsx)(N.pj,{className:"text-center",children:e.start?new Date(e.start).toLocaleDateString():"N/A"}),(0,a.jsx)(N.pj,{className:"text-center",children:e.status?(0,a.jsx)(v.C,{className:(0,g.S)(e.status).className,children:(0,g.S)(e.status).text}):"N/A"}),(0,a.jsx)(N.pj,{className:"text-center",children:"rejected"===r||"pending"===r?(0,a.jsxs)(w.Vq,{open:n,onOpenChange:e=>c(e),children:[(0,a.jsx)(w.hg,{asChild:!0,children:(0,a.jsx)(b.z,{size:"sm",variant:"outline",onClick:()=>h(e),className:"border border-gray-300 rounded-lg px-4 py-2 transition-all duration-300 shadow-sm hover:shadow-md",children:(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)("i",{className:"fas fa-info-circle"}),(0,a.jsx)("span",{children:"View Status"})]})})}),(0,a.jsxs)(w.cZ,{className:"rounded-lg shadow-md p-6 w-96 mx-auto border border-gray-200",children:[(0,a.jsx)(w.fK,{className:"mb-4 text-center",children:(0,a.jsxs)(w.$N,{className:"text-lg font-semibold leading-tight flex items-center gap-2 justify-center",children:[(0,a.jsx)("i",{className:"fas fa-project-diagram"}),(0,a.jsx)("span",{children:"Project Details"})]})}),(0,a.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("strong",{children:"Project Name :"}),(0,a.jsx)("span",{children:null==l?void 0:l.projectName})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("strong",{children:"Company :"}),(0,a.jsx)("span",{children:null==l?void 0:l.companyName})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center gap-2",children:[(0,a.jsx)("strong",{className:"w-1/3",children:"Description :"}),(0,a.jsx)("p",{className:"w-2/3",children:(null==l?void 0:l.description)&&l.description.length>55&&!m?(0,a.jsxs)("span",{children:[null==l?void 0:l.description.substring(0,55),"...",(0,a.jsx)("button",{onClick:()=>u(!m),className:"ml-2 text-blue-500 cursor-pointer",children:"See More"})]}):(0,a.jsxs)("span",{children:[null==l?void 0:l.description,(null==l?void 0:l.description)&&l.description.length>55&&(0,a.jsx)("button",{onClick:()=>u(!m),className:"ml-2 text-blue-500 cursor-pointer",children:m?"See Less":"See More"})]})})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("strong",{children:"Skills Required :"}),(0,a.jsx)("span",{children:null!==(i=null==l?void 0:null===(s=l.skillsRequired)||void 0===s?void 0:s.length)&&void 0!==i&&i&&null!==(d=null==l?void 0:null===(t=l.skillsRequired)||void 0===t?void 0:t.join(", "))&&void 0!==d?d:"Not specified"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("strong",{children:"Status :"}),(0,a.jsx)("span",{className:"font-medium",children:null==l?void 0:l.status})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("strong",{children:"Created :"}),(0,a.jsx)("span",{children:(null==l?void 0:l.createdAt)?new Date(l.createdAt).toLocaleDateString():"N/A"})]})]}),(0,a.jsx)("div",{className:"mt-6 flex justify-end",children:(0,a.jsx)(b.z,{className:"border border-gray-400 rounded-lg px-4 py-2 transition-transform transform hover:scale-105 shadow-sm",onClick:x,children:"Close"})})]})]}):(0,a.jsx)(p.default,{href:"/freelancer/project/".concat(e._id),children:(0,a.jsx)(b.z,{size:"sm",variant:"outline",children:"View Details"})})})]},e._id)}):(0,a.jsx)("tr",{children:(0,a.jsxs)("td",{colSpan:6,className:"text-center py-10",children:[(0,a.jsx)(f.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No projects available"})]})})})]})})]})},C=t(77209),P=t(70402),S=t(62737),D=t.n(S),k=t(92513),I=t(16463),Z=t(86763),A=function(e){let{isOpen:s,onClose:t}=e,r=(0,I.useRouter)(),l=(0,I.useSearchParams)(),[i,n]=(0,d.useState)(""),[c,o]=(0,d.useState)(""),[m,u]=(0,d.useState)(D()().add(1,"day").format("YYYY-MM-DD")),[h,j]=(0,d.useState)(D()().add(1,"day").add(1,"hour").format("YYYY-MM-DD")),[f,p]=(0,d.useState)([""]),g=e=>{let s=Object.fromEntries(l.entries());s.code?N(e,s.code):v()},N=(e,s)=>{x.b.post("/meeting",e,{params:{code:s}})},v=async()=>{try{let e=window.location.origin+window.location.pathname,s=(await x.b.get("/meeting/auth-url",{params:{redirectUri:e}})).data.url;s&&r.push(s)}catch(e){console.error("Error fetching Google Auth URL:",e),(0,Z.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}},y=()=>{p([...f,""])},E=(e,s)=>{let t=[...f];t[e]=s,p(t)};return(0,a.jsx)(w.Vq,{open:s,onOpenChange:t,children:(0,a.jsxs)(w.cZ,{className:"sm:max-w-[425px]",children:[(0,a.jsxs)(w.fK,{children:[(0,a.jsx)(w.$N,{children:"Create a Meeting"}),(0,a.jsx)(w.Be,{children:"Fill in the details below to schedule a new meeting."})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),g({summary:i,description:c,start:{dateTime:D()(m).toISOString(),timeZone:"Asia/Kolkata"},end:{dateTime:D()(h).toISOString(),timeZone:"Asia/Kolkata"},attendees:f})},className:"grid gap-4 py-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(P.Label,{htmlFor:"summary",className:"text-right",children:"Summary"}),(0,a.jsx)(C.I,{id:"summary",value:i,onChange:e=>n(e.target.value),className:"col-span-3",placeholder:"Meeting Summary",required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(P.Label,{htmlFor:"description",className:"text-right",children:"Description"}),(0,a.jsx)(C.I,{id:"description",value:c,onChange:e=>o(e.target.value),className:"col-span-3",placeholder:"Meeting Description",required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(P.Label,{htmlFor:"start-date",className:"text-right",children:"Start Date"}),(0,a.jsx)(C.I,{type:"date",value:m,onChange:e=>u(e.target.value),className:"col-span-3",required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(P.Label,{htmlFor:"end-date",className:"text-right",children:"End Date"}),(0,a.jsx)(C.I,{type:"date",value:h,onChange:e=>j(e.target.value),className:"col-span-3",required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(P.Label,{htmlFor:"start-time",className:"text-right",children:"Start Time"}),(0,a.jsx)(C.I,{type:"time",className:"col-span-3",value:D()(m).format("HH:mm"),onChange:e=>{let[s,t]=e.target.value.split(":").map(Number);u(D()(m).set("hour",s).set("minute",t).format("YYYY-MM-DDTHH:mm"))},required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(P.Label,{htmlFor:"start-time",className:"text-right",children:"End Time"}),(0,a.jsx)(C.I,{type:"time",className:"col-span-3",value:D()(m).format("HH:mm"),onChange:e=>{let[s,t]=e.target.value.split(":").map(Number);u(D()(m).set("hour",s).set("minute",t).format("YYYY-MM-DDTHH:mm"))},required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-start gap-3",children:[(0,a.jsx)(P.Label,{htmlFor:"attendees",className:"text-right",children:"Attendees"}),(0,a.jsx)("div",{className:"col-span-3 space-y-2",children:f.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(C.I,{value:e,onChange:e=>E(s,e.target.value),placeholder:"Enter attendee email",className:"flex-grow",required:!0}),s===f.length-1&&(0,a.jsx)(b.z,{type:"button",onClick:y,className:"ml-2 flex-shrink-0",children:(0,a.jsx)(k.Z,{className:"h-4 w-4"})})]},s))})]}),(0,a.jsx)(w.cN,{className:"flex justify-center",children:(0,a.jsx)(b.z,{type:"submit",children:"Create Meeting"})})]})]})})},O=t(97540),T=t(62688),M=t(78068),V=e=>{let{userId:s}=e,[t,r]=(0,d.useState)(null),[l,i]=(0,d.useState)(0),[n,c]=(0,d.useState)({}),m=(0,I.useRouter)();(0,d.useEffect)(()=>{let e=async()=>{try{let e=(await x.b.get("/freelancer/".concat(s))).data.data;r(e);let{percentage:t,fields:a}=u(e);i(t),c(a)}catch(e){console.error("Error fetching user profile:",e),(0,M.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}};s&&e()},[s]);let u=e=>{var s,t,a,r,l,i,n;let c={firstName:!!(null===(s=e.firstName)||void 0===s?void 0:s.trim()),lastName:!!(null===(t=e.lastName)||void 0===t?void 0:t.trim()),userName:!!(null===(a=e.userName)||void 0===a?void 0:a.trim()),email:!!(null===(r=e.email)||void 0===r?void 0:r.trim()),phone:!!(null===(l=e.phone)||void 0===l?void 0:l.trim()),profilePic:!!(null===(i=e.profilePic)||void 0===i?void 0:i.trim()),description:!!(null===(n=e.description)||void 0===n?void 0:n.trim()),skills:Array.isArray(e.skills)&&e.skills.length>0,domain:Array.isArray(e.domain)&&e.domain.length>0,projectDomain:Array.isArray(e.projectDomain)&&e.projectDomain.length>0,kycApplied:!!(e.kyc&&"NOT_APPLIED"!==e.kyc.status),kycVerified:!!(e.kyc&&"VERIFIED"===e.kyc.status)},d=Object.keys(c).length;return{percentage:Object.values(c).filter(Boolean).length/d*100,fields:c}};if(!t)return(0,a.jsxs)(o.Zb,{className:"w-full",children:[(0,a.jsx)(o.Ol,{className:"pb-2",children:(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(o.ll,{className:"text-2xl",children:"Profile Completion"}),(0,a.jsx)(o.SZ,{children:"Complete your profile to increase visibility"})]})})}),(0,a.jsx)(o.aY,{children:(0,a.jsx)(y.O,{className:"w-[100px] h-[20px] rounded-full"})})]});let h=n?Object.entries(n).filter(e=>{let[,s]=e;return!s}).map(e=>{let[s]=e,t=s.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase());return"kycApplied"===s?"KYC Application":"kycVerified"===s?"KYC Verification":t}):[];return(0,a.jsxs)(o.Zb,{className:"w-full border-2",children:[(0,a.jsx)(o.Ol,{className:"pb-2",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(o.ll,{className:"text-2xl",children:"Profile Completion"}),(0,a.jsx)(o.SZ,{children:"Complete your profile to increase visibility"})]}),(0,a.jsx)(b.z,{className:"w-32",onClick:()=>m.push("/freelancer/settings/personal-info"),variant:"default",children:"Complete Profile"})]})}),(0,a.jsx)(o.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"w-full mr-3",children:(0,a.jsx)("div",{className:"h-3 w-full overflow-hidden rounded-full bg-muted",children:(0,a.jsx)("div",{className:"h-full bg-primary transition-all duration-300 ease-in-out",style:{width:"".concat(l,"%")}})})}),(0,a.jsxs)("span",{className:"text-xl font-bold whitespace-nowrap",children:[l.toFixed(0),"%"]})]})}),h.length>0&&l<100&&(0,a.jsxs)("div",{className:"mt-2 text-sm text-muted-foreground",children:[(0,a.jsx)("p",{children:"Missing information:"}),(0,a.jsxs)("ul",{className:"list-disc pl-5 mt-1",children:[h.slice(0,3).map((e,s)=>(0,a.jsx)("li",{children:e},s)),h.length>3&&(0,a.jsxs)("li",{children:["And ",h.length-3," more..."]})]})]})]})})]})};function Y(){let e=(0,c.v9)(e=>e.user),[s,t]=((0,c.I0)(),(0,d.useState)([])),[f,p]=(0,d.useState)([]),[g,N]=(0,d.useState)([]),[v,w]=(0,d.useState)(!1),[C,P]=(0,d.useState)("ACTIVE"),[S,D]=(0,d.useState)(!0),[k,I]=(0,d.useState)(!0),Z=async e=>{D(!0);try{var s;let a=await x.b.get("/freelancer/project?status=".concat(e));200==a.status&&(null==a?void 0:null===(s=a.data)||void 0===s?void 0:s.data)&&t(a.data.data)}catch(e){(0,M.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.error("API Error:",e)}finally{D(!1)}};(0,d.useEffect)(()=>{},[]);let Y=async()=>{I(!0);try{var e,s;let t=await x.b.get("/freelancer/project?status=ACTIVE"),a=await x.b.get("/freelancer/project?status=PENDING");200==t.status&&(null==t?void 0:null===(e=t.data)||void 0===e?void 0:e.data)&&p(t.data.data),200==a.status&&(null==a?void 0:null===(s=a.data)||void 0===s?void 0:s.data)&&N(a.data.data)}catch(e){console.error("API Error for project stats:",e),(0,M.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}finally{I(!1)}};(0,d.useEffect)(()=>{Z(C)},[e.uid,C]),(0,d.useEffect)(()=>{Y()},[e.uid]);let R=e=>{P(e),Z(e)};return(0,a.jsxs)("div",{className:"flex min-h-screen  w-full flex-col bg-muted/40",children:[(0,a.jsx)(h.Z,{menuItemsTop:j.yn,menuItemsBottom:j.$C,active:"Dashboard"}),(0,a.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,a.jsx)(T.Z,{menuItemsTop:j.yn,menuItemsBottom:j.$C,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"}]}),(0,a.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8 lg:grid-cols-3 xl:grid-cols-3",children:[(0,a.jsxs)("div",{className:"grid auto-rows-max items-start gap-4 md:gap-8 lg:col-span-2",children:[(0,a.jsx)(V,{userId:e.uid}),(0,a.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-2 xl:grid-cols-4",children:[(0,a.jsxs)(o.Zb,{className:"sm:col-span-2 flex flex-col h-full",children:[(0,a.jsx)(o.Ol,{className:"pb-3",children:(0,a.jsx)(o.ll,{className:"text-4xl mb-3",children:S?(0,a.jsx)(y.O,{className:"h-10 w-20"}):"0"})}),(0,a.jsxs)(o.eW,{className:"grid gap-4 grid-cols-4",children:[(0,a.jsxs)("div",{className:"col-span-3",children:[(0,a.jsx)(o.ll,{children:"Total Earnings"}),(0,a.jsx)(o.SZ,{className:"max-w-lg text-balance leading-relaxed",children:S?(0,a.jsx)(y.O,{className:"h-5 w-40"}):"Your total earnings from projects."})]}),(0,a.jsx)("div",{className:"flex items-end justify-end",children:(0,a.jsx)(r.Z,{className:"h-12 w-12 text-muted-foreground"})})]})]}),(0,a.jsx)(u.Z,{title:"Active Projects",value:k?"...":f.length,icon:(0,a.jsx)(l.Z,{className:"h-6 w-6 text-success"}),additionalInfo:"Earning stats will be here"}),(0,a.jsx)(u.Z,{title:"Pending Projects",value:k?"...":g.length,icon:(0,a.jsx)(i.Z,{className:"h-6 w-6 text-warning"}),additionalInfo:k?"Loading...":"Project stats will be here"})]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)(m.mQ,{value:C,onValueChange:e=>R(e),children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(m.dr,{children:[(0,a.jsx)(m.SP,{value:O.sB.ACTIVE,children:"Active"}),(0,a.jsx)(m.SP,{value:O.sB.PENDING,children:"Pending"}),(0,a.jsx)(m.SP,{value:O.sB.COMPLETED,children:"Completed"}),(0,a.jsx)(m.SP,{value:O.sB.REJECTED,children:"Rejected"})]})}),(0,a.jsx)(m.nU,{value:O.sB.ACTIVE,children:(0,a.jsx)(E,{type:"active",projects:s,loading:S})}),(0,a.jsx)(m.nU,{value:O.sB.PENDING,children:(0,a.jsx)(E,{type:"pending",projects:s,loading:S})}),(0,a.jsx)(m.nU,{value:O.sB.COMPLETED,children:(0,a.jsx)(E,{type:"completed",projects:s,loading:S})}),(0,a.jsx)(m.nU,{value:O.sB.REJECTED,children:(0,a.jsx)(E,{type:"rejected",projects:s,loading:S})})]})})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(o.ll,{className:"group flex items-center gap-2 text-2xl",children:"Interviews"}),(0,a.jsxs)("div",{className:"text-center py-10",children:[(0,a.jsx)(n.Z,{className:"mx-auto mb-2 text-gray-500",size:"100"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No interviews scheduled"}),(0,a.jsx)(b.z,{className:"mt-3",onClick:()=>{w(!0)},disabled:!0,children:"Create Meet"})]})]})]})]}),(0,a.jsx)(A,{isOpen:v,onClose:()=>w(!1)})]})}t(62143)},41969:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});var a=t(57437),r=t(48185);function l(e){let{title:s,value:t,icon:l,additionalInfo:i}=e;return(0,a.jsxs)(r.Zb,{"x-chunk":"dashboard-05-chunk-1",children:[(0,a.jsxs)(r.Ol,{className:"pb-2",children:[l,(0,a.jsx)(r.SZ,{children:s}),(0,a.jsx)(r.ll,{className:"text-4xl",children:t})]}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:i})})]})}},70402:function(e,s,t){"use strict";t.r(s),t.d(s,{Label:function(){return d}});var a=t(57437),r=t(2265),l=t(38364),i=t(12218),n=t(49354);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.f,{ref:s,className:(0,n.cn)(c(),t),...r})});d.displayName=l.f.displayName},2183:function(e,s,t){"use strict";t.d(s,{O:function(){return l}});var a=t(57437),r=t(49354);function l(e){let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-primary/10",s),...t})}},86864:function(e,s,t){"use strict";t.d(s,{SP:function(){return d},dr:function(){return c},mQ:function(){return n},nU:function(){return o}});var a=t(57437),r=t(2265),l=t(62447),i=t(49354);let n=l.fC,c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.aV,{ref:s,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...r})});c.displayName=l.aV.displayName;let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.xz,{ref:s,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...r})});d.displayName=l.xz.displayName;let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.VY,{ref:s,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...r})});o.displayName=l.VY.displayName},66227:function(e,s,t){"use strict";t.d(s,{$C:function(){return N},yL:function(){return v},yn:function(){return g}});var a=t(57437),r=t(11005),l=t(33149),i=t(76035),n=t(49100),c=t(40064),d=t(43193),o=t(36141),m=t(33907),u=t(47390),x=t(73347),h=t(24258),j=t(5891),f=t(10883),p=t(66648);let g=[{href:"#",icon:(0,a.jsx)(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,a.jsx)(r.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:(0,a.jsx)(l.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:(0,a.jsx)(i.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:(0,a.jsx)(n.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:(0,a.jsx)(c.Z,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:(0,a.jsx)(d.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:(0,a.jsx)(o.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:(0,a.jsx)(m.Z,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:(0,a.jsx)(u.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,a.jsx)(x.Z,{className:"h-5 w-5"}),label:"Notes"}],N=[{href:"/freelancer/settings/personal-info",icon:(0,a.jsx)(h.Z,{className:"h-5 w-5"}),label:"Settings"}];p.default,r.Z,x.Z,j.Z,f.Z;let v=[{href:"#",icon:(0,a.jsx)(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,a.jsx)(r.Z,{className:"h-5 w-5"}),label:"Home"}]},62143:function(e,s,t){"use strict";t.d(s,{Dj:function(){return n},MR:function(){return l},kd:function(){return r}});let a=(0,t(69753).oM)({name:"projectDraft",initialState:{draftedProjects:[]},reducers:{addDraftedProject:(e,s)=>{e.draftedProjects.includes(s.payload)||e.draftedProjects.push(s.payload)},removeDraftedProject:(e,s)=>{e.draftedProjects=e.draftedProjects.filter(e=>e!==s.payload)},clearDraftedProjects:e=>{e.draftedProjects=[]},setDraftedProjects:(e,s)=>{e.draftedProjects=s.payload}}}),{addDraftedProject:r,removeDraftedProject:l,clearDraftedProjects:i,setDraftedProjects:n}=a.actions;s.ZP=a.reducer},97540:function(e,s,t){"use strict";var a,r,l,i,n,c;t.d(s,{cd:function(){return a},d8:function(){return d},kJ:function(){return r},sB:function(){return l}}),(i=a||(a={})).Mastery="Mastery",i.Proficient="Proficient",i.Beginner="Beginner",(n=r||(r={})).ACTIVE="Active",n.PENDING="Pending",n.REJECTED="Rejected",n.COMPLETED="Completed",(c=l||(l={})).ACTIVE="ACTIVE",c.PENDING="PENDING",c.REJECTED="REJECTED",c.COMPLETED="COMPLETED";let d={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2167,2455,9726,2688,2971,7023,1744],function(){return e(e.s=42050)}),_N_E=e.O()}]);