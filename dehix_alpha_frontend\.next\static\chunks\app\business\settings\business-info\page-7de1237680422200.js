(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8475],{40840:function(e,s,r){Promise.resolve().then(r.bind(r,23785))},23785:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return A}});var t=r(57437),a=r(11444),i=r(64797),n=r(43595),l=r(2265),o=r(31014),c=r(39343),d=r(59772),m=r(74531),u=r(70402),p=r(48185),f=r(78068),x=r(15922),h=r(89733),N=r(93363),j=r(77209),v=r(34859),I=r(29973),E=r(3483);let b=d.z.object({firstName:d.z.string().min(2,{message:"First Name must be at least 2 characters."}),lastName:d.z.string().min(2,{message:"Last Name must be at least 2 characters."}),email:d.z.string().email({message:"Email must be a valid email address."}),phone:d.z.string().min(10,{message:"Phone number must be at least 10 digits."}),companyName:d.z.string().optional(),companySize:d.z.string().optional(),position:d.z.string().optional(),linkedin:d.z.string().url({message:"Must be a valid URL."}).refine(e=>e.includes("linkedin.com/in/")||e.includes("linkedin.com/company/"),{message:"LinkedIn URL must start with: https://www.linkedin.com/in/"}).optional(),website:d.z.string().url({message:"Must be a valid URL."}).optional()});function y(e){let{user_id:s}=e,[r,i]=(0,l.useState)({}),[n,d]=(0,l.useState)(!1),y=(0,a.I0)(),g=(0,c.cI)({resolver:(0,o.F)(b),defaultValues:{firstName:"",lastName:"",email:"",phone:"",companyName:"",companySize:"",position:"",linkedin:"",website:""},mode:"all"});async function A(e){d(!0);try{let s=await x.b.put("/business",{...e});if(i({...r,firstName:e.firstName,lastName:e.lastName,email:e.email,phone:e.phone,companyName:e.companyName,companySize:e.companySize,position:e.position,linkedin:e.linkedin,personalWebsite:e.website}),200===s.status){let s={...r,...e};y((0,E.av)(s)),i(s),(0,f.Am)({title:"Profile Updated",description:"Your profile has been successfully updated."})}else console.error("Unexpected status code:",s.status),(0,f.Am)({variant:"destructive",title:"Error",description:"Failed to update profile. Unexpected server response."})}catch(e){console.error("API Error:",e),(0,f.Am)({variant:"destructive",title:"Error",description:"Failed to update profile. Please try again later."})}finally{d(!1)}}return(0,l.useEffect)(()=>{(async()=>{try{let e=await x.b.get("/business/".concat(s));i(e.data)}catch(e){console.error("API Error:",e),(0,f.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}})()},[s]),(0,l.useEffect)(()=>{g.reset({firstName:(null==r?void 0:r.firstName)||"",lastName:(null==r?void 0:r.lastName)||"",email:(null==r?void 0:r.email)||"",phone:(null==r?void 0:r.phone)||"",companyName:(null==r?void 0:r.companyName)||"",companySize:(null==r?void 0:r.companySize)||"",position:(null==r?void 0:r.position)||"",linkedin:(null==r?void 0:r.linkedin)||"",website:(null==r?void 0:r.personalWebsite)||""})},[r,g]),(0,t.jsx)(p.Zb,{className:"p-10",children:(0,t.jsxs)(N.l0,{...g,children:[(0,t.jsx)(m.Z,{profile:r.profilePic,entityType:v.Dy.BUSINESS}),(0,t.jsxs)("form",{onSubmit:g.handleSubmit(A),className:"space-y-6 mt-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.Label,{children:"First Name"}),(0,t.jsx)(N.Wi,{control:g.control,name:"firstName",render:e=>{let{field:s}=e;return(0,t.jsxs)(N.xJ,{children:[(0,t.jsx)(N.NI,{children:(0,t.jsx)(j.I,{placeholder:"Enter your first name",...s})}),(0,t.jsx)(N.zG,{})]})}})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.Label,{children:"Last Name"}),(0,t.jsx)(N.Wi,{control:g.control,name:"lastName",render:e=>{let{field:s}=e;return(0,t.jsxs)(N.xJ,{children:[(0,t.jsx)(N.NI,{children:(0,t.jsx)(j.I,{placeholder:"Enter your last name",...s})}),(0,t.jsx)(N.zG,{})]})}})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.Label,{children:"Email"}),(0,t.jsx)(N.Wi,{control:g.control,name:"email",render:e=>{let{field:s}=e;return(0,t.jsxs)(N.xJ,{children:[(0,t.jsx)(N.NI,{children:(0,t.jsx)(j.I,{disabled:!0,placeholder:"Enter your email",type:"email",...s,readOnly:!0})}),(0,t.jsx)(N.zG,{}),(0,t.jsx)(N.pf,{children:"Non editable field"})]})}})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.Label,{children:"Phone"}),(0,t.jsx)(N.Wi,{control:g.control,name:"phone",render:e=>{let{field:s}=e;return(0,t.jsxs)(N.xJ,{children:[(0,t.jsx)(N.NI,{children:(0,t.jsx)(j.I,{disabled:!0,placeholder:"Enter your phone number",type:"tel",...s,readOnly:!0})}),(0,t.jsx)(N.zG,{}),(0,t.jsx)(N.pf,{children:"Non editable field"})]})}})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.Label,{children:"Company Name"}),(0,t.jsx)(N.Wi,{control:g.control,name:"companyName",render:e=>{let{field:s}=e;return(0,t.jsxs)(N.xJ,{children:[(0,t.jsx)(N.NI,{children:(0,t.jsx)(j.I,{placeholder:"Enter your company name",...s})}),(0,t.jsx)(N.zG,{})]})}})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.Label,{children:"Company Size"}),(0,t.jsx)(N.Wi,{control:g.control,name:"companySize",render:e=>{let{field:s}=e;return(0,t.jsxs)(N.xJ,{children:[(0,t.jsx)(N.NI,{children:(0,t.jsx)(j.I,{placeholder:"Enter your company size",...s})}),(0,t.jsx)(N.zG,{})]})}})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.Label,{children:"Position"}),(0,t.jsx)(N.Wi,{control:g.control,name:"position",render:e=>{let{field:s}=e;return(0,t.jsxs)(N.xJ,{children:[(0,t.jsx)(N.NI,{children:(0,t.jsx)(j.I,{placeholder:"Enter your position",...s})}),(0,t.jsx)(N.zG,{})]})}})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.Label,{children:"LinkedIn URL"}),(0,t.jsx)(N.Wi,{control:g.control,name:"linkedin",render:e=>{let{field:s}=e;return(0,t.jsxs)(N.xJ,{children:[(0,t.jsx)(N.NI,{children:(0,t.jsx)(j.I,{placeholder:"Enter your LinkedIn URL",type:"url",...s})}),(0,t.jsx)(N.zG,{})]})}})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.Label,{children:"Website URL"}),(0,t.jsx)(N.Wi,{control:g.control,name:"website",render:e=>{let{field:s}=e;return(0,t.jsxs)(N.xJ,{children:[(0,t.jsx)(N.NI,{children:(0,t.jsx)(j.I,{placeholder:"Enter your website URL",type:"url",...s})}),(0,t.jsx)(N.zG,{})]})}})]})]}),(0,t.jsx)(I.Separator,{className:"col-span-2"}),(0,t.jsx)(h.z,{className:"w-full",type:"submit",disabled:n,children:n?"Loading...":"Save changes"})]})]})})}var g=r(62688);function A(){let e=(0,a.v9)(e=>e.user);return(0,t.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,t.jsx)(i.Z,{menuItemsTop:n.y,menuItemsBottom:n.$,active:"Business Info",isKycCheck:!0}),(0,t.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 mb-8 sm:pl-14",children:[(0,t.jsx)(g.Z,{menuItemsTop:n.y,menuItemsBottom:n.$,activeMenu:"Personal Info",breadcrumbItems:[{label:"Business",link:"/dashboard/business"},{label:"Settings",link:"#"},{label:"Business Info",link:"#"}]}),(0,t.jsx)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8",children:(0,t.jsx)(y,{user_id:e.uid})})]})]})}},74531:function(e,s,r){"use strict";r.d(s,{Z:function(){return h}});var t=r(57437),a=r(2265);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(33480).Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var n=r(92513),l=r(3274),o=r(66648),c=r(11444),d=r(78068),m=r(89733),u=r(15922),p=r(3483),f=r(34859);let x=["image/png","image/jpeg","image/jpg","image/gif","image/svg+xml"];var h=e=>{let{profile:s,entityType:r}=e,h=(0,c.v9)(e=>e.user),N=(0,c.I0)(),[j,v]=(0,a.useState)(null),[I,E]=(0,a.useState)(s),[b,y]=(0,a.useState)(!1),g=(0,a.useRef)(null),A=async e=>{if(e.preventDefault(),!j){(0,d.Am)({variant:"destructive",title:"No Image Selected",description:"Please select an image before submitting."});return}y(!0);let s=new FormData;s.append("profilePicture",j);try{let{Location:e}=(await u.b.post("/register/upload-image",s,{headers:{"Content-Type":"multipart/form-data"}})).data.data;N((0,p.av)({...h,photoURL:e}));let t=r===f.Dy.FREELANCER?"/freelancer":"/business",a=await u.b.put(t,{profilePic:e});if(200===a.status)(0,d.Am)({title:"Success",description:"Profile picture uploaded successfully!"});else throw Error("Failed to update profile picture")}catch(e){console.error("Error during upload:",e),(0,d.Am)({variant:"destructive",title:"Upload failed",description:"Image upload failed. Please try again."})}finally{y(!1)}};return(0,t.jsx)("div",{className:"upload-form max-w-md mx-auto rounded shadow-md",children:(0,t.jsxs)("form",{onSubmit:A,className:"space-y-6",children:[(0,t.jsx)("input",{type:"file",accept:x.join(","),onChange:e=>{var s;let r=null===(s=e.target.files)||void 0===s?void 0:s[0];r&&x.includes(r.type)?r.size<=1048576?(v(r),E(URL.createObjectURL(r))):(0,d.Am)({variant:"destructive",title:"File too large",description:"Image size should not exceed 1MB."}):(0,d.Am)({variant:"destructive",title:"Invalid file type",description:"Please upload a valid image file. Allowed formats: ".concat(x.join(", "))})},className:"hidden",ref:g}),(0,t.jsx)("div",{className:"relative flex flex-col items-center",children:(0,t.jsxs)("label",{htmlFor:"file-input",className:"cursor-pointer relative",children:[I?(0,t.jsx)(o.default,{width:28,height:28,src:I,alt:"Avatar Preview",className:"w-28 h-28 rounded-full object-cover border-2 border-black-300"}):(0,t.jsx)("div",{className:"w-28 h-28 rounded-full bg-gray-700 flex items-center justify-center",children:(0,t.jsx)(o.default,{width:112,height:112,src:s,alt:"Avatar Preview",className:"w-28 h-28 rounded-full object-cover border-2 border-black-300"})}),(0,t.jsx)(m.z,{variant:"outline",type:"button",size:"icon",className:"absolute bottom-0 right-0 w-10 h-10 rounded-full bg-black border border-gray-300 flex items-center justify-center shadow-md",onClick:()=>{if(I)E(null);else{var e;null===(e=g.current)||void 0===e||e.click()}},children:I?(0,t.jsx)(i,{className:"h-4 w-4 text-gray-400"}):(0,t.jsx)(n.Z,{className:"h-4 w-4 text-gray-400"})})]})}),I&&(0,t.jsx)(m.z,{type:"submit",className:"w-full",disabled:!j||b,children:b?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Please wait"]}):"Upload Profile Picture"})]})})}},93363:function(e,s,r){"use strict";r.d(s,{NI:function(){return h},Wi:function(){return m},l0:function(){return c},lX:function(){return x},pf:function(){return N},xJ:function(){return f},zG:function(){return j}});var t=r(57437),a=r(2265),i=r(63355),n=r(39343),l=r(49354),o=r(70402);let c=n.RV,d=a.createContext({}),m=e=>{let{...s}=e;return(0,t.jsx)(d.Provider,{value:{name:s.name},children:(0,t.jsx)(n.Qr,{...s})})},u=()=>{let e=a.useContext(d),s=a.useContext(p),{getFieldState:r,formState:t}=(0,n.Gc)(),i=r(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=s;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...i}},p=a.createContext({}),f=a.forwardRef((e,s)=>{let{className:r,...i}=e,n=a.useId();return(0,t.jsx)(p.Provider,{value:{id:n},children:(0,t.jsx)("div",{ref:s,className:(0,l.cn)("space-y-2",r),...i})})});f.displayName="FormItem";let x=a.forwardRef((e,s)=>{let{className:r,...a}=e,{error:i,formItemId:n}=u();return(0,t.jsx)(o.Label,{ref:s,className:(0,l.cn)(i&&"text-destructive",r),htmlFor:n,...a})});x.displayName="FormLabel";let h=a.forwardRef((e,s)=>{let{...r}=e,{error:a,formItemId:n,formDescriptionId:l,formMessageId:o}=u();return(0,t.jsx)(i.g7,{ref:s,id:n,"aria-describedby":a?"".concat(l," ").concat(o):"".concat(l),"aria-invalid":!!a,...r})});h.displayName="FormControl";let N=a.forwardRef((e,s)=>{let{className:r,...a}=e,{formDescriptionId:i}=u();return(0,t.jsx)("p",{ref:s,id:i,className:(0,l.cn)("text-sm text-muted-foreground",r),...a})});N.displayName="FormDescription";let j=a.forwardRef((e,s)=>{let{className:r,children:a,...i}=e,{error:n,formMessageId:o}=u(),c=n?String(null==n?void 0:n.message):a;return c?(0,t.jsx)("p",{ref:s,id:o,className:(0,l.cn)("text-sm font-medium text-destructive",r),...i,children:c}):null});j.displayName="FormMessage"},70402:function(e,s,r){"use strict";r.r(s),r.d(s,{Label:function(){return c}});var t=r(57437),a=r(2265),i=r(38364),n=r(12218),l=r(49354);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)(i.f,{ref:s,className:(0,l.cn)(o(),r),...a})});c.displayName=i.f.displayName},29973:function(e,s,r){"use strict";r.r(s),r.d(s,{Separator:function(){return l}});var t=r(57437),a=r(2265),i=r(48484),n=r(49354);let l=a.forwardRef((e,s)=>{let{className:r,orientation:a="horizontal",decorative:l=!0,...o}=e;return(0,t.jsx)(i.f,{ref:s,decorative:l,orientation:a,className:(0,n.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",r),...o})});l.displayName=i.f.displayName},43595:function(e,s,r){"use strict";r.d(s,{$:function(){return o},y:function(){return l}});var t=r(57437),a=r(11005),i=r(52022),n=r(66648);let l=[{href:"#",icon:(0,t.jsx)(n.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,t.jsx)(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/business/settings/business-info",icon:(0,t.jsx)(i.Z,{className:"h-5 w-5"}),label:"Business Info"}],o=[]},34859:function(e,s,r){"use strict";var t,a,i,n,l,o,c,d,m,u,p,f,x,h,N,j,v,I,E,b,y,g;r.d(s,{Dy:function(){return u},Dz:function(){return A}});let A={BATCH:3};(f=t||(t={})).PROJECT_HIRING="PROJECT_HIRING",f.SKILL_INTERVIEW="SKILL_INTERVIEW",f.DOMAIN_INTERVIEW="DOMAIN_INTERVIEW",f.TALENT_INTERVIEW="TALENT_INTERVIEW",(x=a||(a={})).ADDED="Added",x.APPROVED="Approved",x.CLOSED="Closed",x.COMPLETED="Completed",(h=i||(i={})).ACTIVE="Active",h.IN_ACTIVE="Inactive",h.NOT_VERIFIED="Not Verified",(N=n||(n={})).BUSINESS="Business",N.FREELANCER="Freelancer",N.BOTH="Both",(j=l||(l={})).ACTIVE="Active",j.IN_ACTIVE="Inactive",(v=o||(o={})).APPLIED="APPLIED",v.NOT_APPLIED="NOT_APPLIED",v.APPROVED="APPROVED",v.FAILED="FAILED",v.STOPPED="STOPPED",v.REAPPLIED="REAPPLIED",(I=c||(c={})).PENDING="Pending",I.ACCEPTED="Accepted",I.REJECTED="Rejected",I.PANEL="Panel",I.INTERVIEW="Interview",(E=d||(d={})).ACTIVE="ACTIVE",E.INACTIVE="INACTIVE",E.ARCHIVED="ARCHIVED",(b=m||(m={})).ACTIVE="Active",b.PENDING="Pending",b.INACTIVE="Inactive",b.CLOSED="Closed",(y=u||(u={})).FREELANCER="FREELANCER",y.ADMIN="ADMIN",y.BUSINESS="BUSINESS",(g=p||(p={})).CREATED="Created",g.CLOSED="Closed",g.ACTIVE="Active"},38364:function(e,s,r){"use strict";r.d(s,{f:function(){return l}});var t=r(2265),a=r(18676),i=r(57437),n=t.forwardRef((e,s)=>(0,i.jsx)(a.WV.label,{...e,ref:s,onMouseDown:s=>{var r;s.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var l=n},48484:function(e,s,r){"use strict";r.d(s,{f:function(){return c}});var t=r(2265),a=r(18676),i=r(57437),n="horizontal",l=["horizontal","vertical"],o=t.forwardRef((e,s)=>{let{decorative:r,orientation:t=n,...o}=e,c=l.includes(t)?t:n;return(0,i.jsx)(a.WV.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:s})});o.displayName="Separator";var c=o}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,1374,2455,9726,2688,2971,7023,1744],function(){return e(e.s=40840)}),_N_E=e.O()}]);