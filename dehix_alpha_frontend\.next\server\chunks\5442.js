"use strict";exports.id=5442,exports.ids=[5442],exports.modules={40900:(e,t,a)=>{a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(80851).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},12070:(e,t,a)=>{a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(80851).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},66307:(e,t,a)=>{a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(80851).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69669:(e,t,a)=>{a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(80851).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},43727:(e,t,a)=>{a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(80851).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},40617:(e,t,a)=>{a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},60763:(e,t,a)=>{a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(80851).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},57671:(e,t,a)=>{a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(80851).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},69515:(e,t,a)=>{a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(80851).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},40765:(e,t,a)=>{a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(80851).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},98091:(e,t,a)=>{a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},74935:(e,t,a)=>{a.d(t,{Z:()=>v});var s=a(10326),l=a(17577),r=a(91664),n=a(10143),i=a(83855),o=a(46226),c=a(51223);let d=["#ffffff","#f28b82","#fbbc04","#fff475","#ccff90","#a7ffeb","#cbf0f8","#aecbfa","#d7aefb","#fdcfe8"];function h({selectedColor:e,onColorSelect:t}){return s.jsx("div",{className:"flex gap-2 flex-wrap",children:d.map(a=>s.jsx("button",{onClick:()=>t(a),className:(0,c.cn)("w-6 h-6 rounded-full border transition-all",e===a&&"ring-2 ring-offset-2 ring-black"),style:{backgroundColor:a},"aria-label":`Select color ${a}`},a))})}var m=a(24118),x=a(41190),u=a(82015),p=a(2069);let b=["/banner1.svg","/banner2.svg","/banner3.svg","/banner4.svg","/banner5.svg","/banner6.svg","/banner7.svg"];function f({onNoteCreate:e}){let[t,a]=(0,l.useState)(""),[n,c]=(0,l.useState)(""),[d,f]=(0,l.useState)("#ffffff"),[v,g]=(0,l.useState)(null),[j,y]=(0,l.useState)(!1),[N,k]=(0,l.useState)(!1);return(0,s.jsxs)(m.Vq,{open:j,onOpenChange:y,children:[s.jsx(m.hg,{asChild:!0,children:(0,s.jsxs)(r.z,{className:"gap-2",children:[s.jsx(i.Z,{className:"h-4 w-4"}),"Add"]})}),(0,s.jsxs)(m.cZ,{className:"sm:max-w-[425px]",children:[s.jsx(m.$N,{children:"Create New Note"}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[s.jsx(x.I,{placeholder:"Title",value:t,onChange:e=>a(e.target.value)}),s.jsx(u.g,{placeholder:"Take a note...",value:n,onChange:e=>c(e.target.value),className:"min-h-[100px] resize-none"}),(0,s.jsxs)("label",{className:"flex items-center gap-2",children:[s.jsx("input",{type:"checkbox",checked:N,onChange:e=>k(e.target.checked),className:"cursor-pointer size-3 text-xs"}),s.jsx("span",{className:"text-xs",children:"Render content as HTML"})]}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm font-medium mb-2",children:"Select a color or banner:"}),s.jsx(h,{selectedColor:d,onColorSelect:e=>{f(e),g(null)}}),s.jsx("div",{className:"grid grid-cols-8 gap-2 mt-4",children:b.map((e,t)=>s.jsx("div",{onClick:()=>{g(e),f("#ffffff")},className:`cursor-pointer border rounded-full p-1 flex items-center justify-center ${v===e?"border-blue-500":"border-gray-300"}`,children:s.jsx(o.default,{src:e,alt:`Banner ${t+1}`,width:32,height:32,className:"object-cover rounded-full"})},t))})]}),s.jsx(r.z,{onClick:()=>{if(!t.trim()&&!n.trim())return;let s={title:t.trim(),content:n.trim(),bgColor:v?void 0:d,banner:v||void 0,createdAt:new Date,noteType:p.wK.NOTE,isHTML:N};e&&e(s),a(""),c(""),f("#ffffff"),g(null),y(!1)},className:"w-full",children:"Save Note"})]})]})]})}let v=({onNoteCreate:e,notes:t,setNotes:a,isTrash:i})=>{let[o,c]=(0,l.useState)(""),d=()=>{a([...t].sort((e,t)=>(e.bgColor||"").localeCompare(t.bgColor||"")))},h=()=>{a([...t].sort((e,t)=>{let a=e.createdAt?new Date(e.createdAt).getTime():0;return(t.createdAt?new Date(t.createdAt).getTime():0)-a}))},m=()=>{a([...t].sort((e,t)=>(e.createdAt?new Date(e.createdAt).getTime():0)-(t.createdAt?new Date(t.createdAt).getTime():0)))};return s.jsx("div",{children:(0,s.jsxs)("div",{className:"mb-8 ml-6 mt-8 flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-3xl font-bold",children:"Notes"}),s.jsx("p",{className:"text-gray-400 mt-2 hidden md:block ",children:"Organize your thoughts and ideas. Add, view, and manage your personal notes with ease."})]}),!i&&s.jsx("div",{className:"mt-4 mr-5",children:(0,s.jsxs)("div",{className:"flex justify-center gap-5 items-center flex-wrap mt-4 sm:mt-0",children:[(0,s.jsxs)(n.h_,{children:[s.jsx(n.$F,{asChild:!0,children:s.jsx(r.z,{variant:"outline",className:"sm:text-sm md:text-base",children:"Sort"})}),(0,s.jsxs)(n.AW,{className:"w-56  shadow-md rounded-md border",children:[s.jsx(n.Ju,{className:"",children:"Sort Notes"}),s.jsx(n.VD,{}),s.jsx(n.bO,{checked:"color"===o,onCheckedChange:()=>{c("color"),d()},children:"Sort by color"}),s.jsx(n.bO,{checked:"latest"===o,onCheckedChange:()=>{c("latest"),h()},children:"Sort by latest"}),s.jsx(n.bO,{checked:"oldest"===o,onCheckedChange:()=>{c("oldest"),m()},children:"Sort by oldest"})]})]}),!i&&s.jsx(f,{onNoteCreate:e})]})})]})})}},12389:(e,t,a)=>{a.d(t,{Z:()=>L});var s=a(10326),l=a(17577),r=a(69508),n=a(98091),i=a(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,i.Z)("Recycle",[["path",{d:"M7 19H4.815a1.83 1.83 0 0 1-1.57-.881 1.785 1.785 0 0 1-.004-1.784L7.196 9.5",key:"x6z5xu"}],["path",{d:"M11 19h8.203a1.83 1.83 0 0 0 1.556-.89 1.784 1.784 0 0 0 0-1.775l-1.226-2.12",key:"1x4zh5"}],["path",{d:"m14 16-3 3 3 3",key:"f6jyew"}],["path",{d:"M8.293 13.596 7.196 9.5 3.1 10.598",key:"wf1obh"}],["path",{d:"m9.344 5.811 1.093-1.892A1.83 1.83 0 0 1 11.985 3a1.784 1.784 0 0 1 1.546.888l3.943 6.843",key:"9tzpgr"}],["path",{d:"m13.378 9.633 4.096 1.098 1.097-4.096",key:"1oe83g"}]]);var c=a(40765);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,i.Z)("RotateCw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]]),h=(0,i.Z)("ArchiveRestore",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h2",key:"tvwodi"}],["path",{d:"M20 8v11a2 2 0 0 1-2 2h-2",key:"1gkqxj"}],["path",{d:"m9 15 3-3 3 3",key:"1pd0qc"}],["path",{d:"M12 12v9",key:"192myk"}]]);var m=a(38443);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let x=(0,i.Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);var u=a(46226),p=a(51682);let b=({handleChangeBanner:e})=>(0,s.jsxs)(p.zs,{children:[s.jsx(p.Yi,{asChild:!0,children:s.jsx("button",{children:s.jsx(x,{size:15,className:"text-black transition-all duration-200"})})}),s.jsx(p.bZ,{className:"shadow-md rounded-md p-3",children:s.jsx("div",{className:"flex flex-nowrap justify-center gap-1",children:["/banner1.svg","/banner2.svg","/banner3.svg","/banner4.svg","/banner5.svg","/banner6.svg","/banner7.svg"].map((t,a)=>s.jsx("div",{onClick:()=>e(t),className:"cursor-pointer p-1 rounded",children:s.jsx(u.default,{src:t,alt:`Banner ${a+1}`,width:28,height:28,className:"rounded-md hover:scale-110 transition-transform"})},a))})})]});var f=a(39447);let v=({items:e})=>{let[t,a]=(0,l.useState)(!1),r=(0,l.useRef)(null),[n,i]=(0,l.useState)("bottom");return(0,l.useEffect)(()=>{let e=e=>{r.current&&!r.current.contains(e.target)&&a(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,l.useEffect)(()=>{t&&setTimeout(()=>{if(r.current){let e=r.current.getBoundingClientRect(),t=window.innerWidth,a=window.innerHeight;e.bottom+150>a?i("top"):e.right+150>t?i("left"):e.left-150<0?i("right"):i("bottom")}},10)},[t]),(0,s.jsxs)("div",{className:"relative",ref:r,children:[s.jsx("button",{onClick:e=>{e.stopPropagation(),a(!t)},className:" rounded-md text-black",children:s.jsx(f.Z,{size:16})}),t&&s.jsx("div",{className:`absolute w-24 rounded-md shadow-md bg-white dark:bg-black border border-gray-300 dark:border-gray-700 transition-all duration-200 ease-in-out overflow-visible ${"top"===n?"bottom-full mb-2":"left"===n?"right-full mr-2":"right"===n?"left-full ml-2":"top-full mt-1"} z-[9999]`,style:{transform:"translateZ(0)"},children:e.map((e,t)=>(0,s.jsxs)("button",{onClick:t=>{t.stopPropagation(),e.onClick(),a(!1)},className:"flex items-center w-full px-1 py-1 text-sm text-black dark:text-white hover:bg-gray-200 dark:hover:bg-gray-800",children:[e.icon&&s.jsx("span",{className:"mr-2",children:e.icon}),e.label]},t))})]})},g=({navItems:e})=>s.jsx("div",{className:"relative overflow-visible",children:s.jsx(v,{items:e})});var j=a(29752),y=a(2069);function N(e,t){let a=e?.split(/\s+/);return a?.length>t?`${a.slice(0,t).join(" ")}...`:e}let k=({note:e,notes:t,setNotes:a,onDragStart:l,onDragOver:r,onDrop:i,isTrash:o,isArchive:c,onEditNote:x,onUpdateNoteType:u,onDeleteClick:p,onChangeBanner:f,navItems:v})=>s.jsx("div",{draggable:!0,onDragStart:l,onDragOver:r,onDrop:i,className:"relative group",children:(0,s.jsxs)(j.Zb,{className:"break-inside-avoid cursor-pointer bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 group w-[80vw] mb-3 md:w-[200px] relative",style:e.banner?{backgroundImage:`url(${e.banner})`,backgroundSize:"cover",backgroundPosition:"center"}:{backgroundColor:e.bgColor||"#ffffff"},children:[(0,s.jsxs)("div",{onClick:()=>x(e),children:[(0,s.jsxs)(j.Ol,{children:[e.type&&s.jsx("div",{className:"absolute top-1 left-1",children:s.jsx(m.C,{className:`text-xs py-0.5 ${y.EH[e.type]||" "}`,children:e.type.toLowerCase()})}),e.title&&s.jsx(j.ll,{className:"font-semibold text-lg text-black mt-6",children:e.title})]}),s.jsx(j.aY,{className:"max-h-[320px] overflow-hidden",children:s.jsx(j.SZ,{className:"text-sm whitespace-pre-wrap truncate break-words",children:e.isHTML?s.jsx("div",{className:"text-sm whitespace-pre-wrap break-words",dangerouslySetInnerHTML:{__html:function(e,t){try{let t=document.createElement("div");if(t.innerHTML=e,t.textContent===e)return N(e,30);let a=t.textContent||t.innerText||"";return N(a,30)}catch(t){return console.error("Error processing input as HTML:",t),N(e,30)}}(e.content,30)}}):s.jsx(j.SZ,{className:"text-sm font-bold truncate bg-opacity-100 whitespace-pre-wrap break-words text-black",children:N(e.content,30)})})})]}),s.jsx("div",{className:"relative group",children:(0,s.jsxs)("div",{className:"absolute bottom-2 right-2 hidden group-hover:flex items-center gap-4 justify-center",children:[o?(0,s.jsxs)(s.Fragment,{children:[s.jsx(d,{size:15,className:"text-black cursor-pointer",onClick:()=>u(e._id,y.wK.NOTE)}),s.jsx(n.Z,{size:15,className:"text-black cursor-pointer",onClick:()=>p(e._id)})]}):c?s.jsx(h,{size:15,className:"text-black cursor-pointer",onClick:()=>u(e._id,y.wK.NOTE)}):s.jsx(h,{size:15,className:"text-black cursor-pointer",onClick:()=>u(e._id,y.wK.ARCHIVE)}),s.jsx(b,{handleChangeBanner:t=>f(e._id,t)}),!o&&s.jsx(g,{navItems:v.map(s=>({...s,icon:s.icon,onClick:()=>s.onClick(e._id,t,a)}))})]})})]})});var w=a(24118),C=a(91664);let S=({note:e,onClose:t,onDelete:a})=>s.jsx(w.Vq,{open:!0,onOpenChange:t,children:(0,s.jsxs)(w.cZ,{className:"sm:max-w-[425px] p-6 rounded-lg shadow-lg",children:[(0,s.jsxs)(w.fK,{className:"border-b pb-4",children:[s.jsx(w.$N,{className:"text-2xl font-semibold",children:"Confirm Deletion"}),(0,s.jsxs)(w.Be,{className:"mt-2 text-sm text-gray-600",children:["Are you sure you want to delete the note titled"," ",s.jsx("strong",{children:e?.title}),"? This action cannot be undone."]})]}),(0,s.jsxs)(w.cN,{className:"mt-6",children:[s.jsx(C.z,{variant:"outline",onClick:t,className:"mr-2",children:"Cancel"}),s.jsx(C.z,{variant:"destructive",onClick:()=>a(e?._id||null),children:"Delete"})]})]})});var Z=a(41190),E=a(82015);let T=({note:e,onClose:t,onSave:a})=>{let[r,n]=(0,l.useState)(e.title||""),[i,o]=(0,l.useState)(e.content||""),[c]=(0,l.useState)(e.entityID||""),[d,h]=(0,l.useState)(""),[m,x]=(0,l.useState)(!1);return s.jsx(w.Vq,{open:!0,onOpenChange:t,children:(0,s.jsxs)(w.cZ,{className:"sm:max-w-[425px] p-6 rounded-lg shadow-lg text-black",style:{backgroundColor:`${e.bgColor}B3`||"#ffffff"},children:[e.banner&&s.jsx("div",{className:"absolute inset-0 z-0",children:s.jsx(u.default,{src:e.banner,alt:"Note Banner",layout:"fill",objectFit:"cover",className:"rounded-lg"})}),s.jsx(w.fK,{className:"border-b pb-4 relative z-10",children:s.jsx(w.$N,{className:"text-2xl font-semibold",children:m?"Edit Note":"Note Details"})}),d&&s.jsx("div",{className:"text-red-500 text-sm mb-4",children:d}),m?(0,s.jsxs)("div",{className:"mt-6 relative z-10",children:[(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{htmlFor:"title",className:"block text-sm font-semibold",children:"Title"}),s.jsx(Z.I,{id:"title",type:"text",required:!0,value:r,onChange:e=>n(e.target.value),placeholder:"Enter note title",className:"mt-2 p-2 border bg-opacity-50 bg-white rounded-md w-full text-sm text-opacity-100"})]}),(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{htmlFor:"content",className:"block text-sm font-semibold",children:"Content"}),s.jsx(E.g,{id:"content",value:i,onChange:e=>o(e.target.value),placeholder:"Enter note content",className:"mt-2 p-2 bg-opacity-50 bg-white border  no-scrollbar rounded-md w-full text-sm text-opacity-100  resize-none",rows:5})]}),(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{htmlFor:"entityID",className:"block text-sm font-semibold",children:"Entity ID"}),s.jsx(Z.I,{id:"entityID",type:"text",disabled:!0,value:c,placeholder:"Entity ID",className:"mt-2 p-2 border bg-opacity-50 bg-white rounded-md w-full text-sm text-opacity-100"})]})]}):(0,s.jsxs)("div",{className:"mt-6 relative z-10 w-full max-w-2xl",children:[(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("p",{className:"text-sm font-bold",children:"Title:"}),s.jsx("p",{className:"text-black-300 mt-1",children:e.title})]}),(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("p",{className:"text-sm font-bold",children:"Content:"}),s.jsx("div",{className:`text-black-300 mt-1 no-scrollbar p-2 ${e.content.length>300?"max-h-52 overflow-y-auto":""}`,children:e.content})]}),(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("p",{className:"text-sm font-bold",children:"Entity ID:"}),s.jsx("p",{className:"text-black-300 mt-1",children:c})]})]}),s.jsx(w.cN,{className:"mt-6 relative z-10 text-white",children:m?(0,s.jsxs)(s.Fragment,{children:[s.jsx(C.z,{variant:"outline",onClick:()=>x(!1),className:"mr-2",children:"Cancel"}),s.jsx(C.z,{onClick:()=>{if(!r.trim()||!i.trim()){h("Title and content cannot be empty.");return}h(""),a({...e,title:r,content:i}),x(!1)},children:"Save"})]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(C.z,{variant:"outline",onClick:t,className:"mr-2 text-black dark:text-white",children:"Close"}),s.jsx(C.z,{onClick:()=>x(!0),children:"Edit"})]})})]})})};var A=a(29280);let M=({note:e,onClose:t,onUpdate:a})=>{let[r,n]=(0,l.useState)(e.type||"");return s.jsx(w.Vq,{open:!!e,onOpenChange:t,children:(0,s.jsxs)(w.cZ,{children:[s.jsx(w.fK,{children:s.jsx(w.$N,{children:"Update Note Type"})}),(0,s.jsxs)(A.Ph,{onValueChange:e=>n(e),value:r,children:[s.jsx(A.i4,{children:s.jsx(A.ki,{placeholder:"Select type"})}),(0,s.jsxs)(A.Bw,{children:[s.jsx(A.Ql,{value:"PERSONAL",children:"Personal"}),s.jsx(A.Ql,{value:"WORK",children:"Work"}),s.jsx(A.Ql,{value:"REMINDER",children:"reminder"}),s.jsx(A.Ql,{value:"TASK",children:"task"})]})]}),(0,s.jsxs)(w.cN,{children:[s.jsx(C.z,{variant:"secondary",onClick:t,children:"Cancel"}),s.jsx(C.z,{onClick:()=>{r&&a(e._id,r.toUpperCase())},children:"Update"})]})]})})};var z=a(6260),D=a(56627);let I=(e,t)=>{let[a,s]=(0,l.useState)(null),[r,n]=(0,l.useState)(null),[i,o]=(0,l.useState)(null),[c,d]=(0,l.useState)(!1),h=e=>{(0,D.Am)({title:"Error",description:e,variant:"destructive",duration:5e3})},m=e=>{(0,D.Am)({description:e,duration:5e3})},x=async t=>{if(!t._id){h("Missing required fields for updating the note.");return}console.log(t);try{let e=await z.b.put(`/notes/${t._id}`,{title:t.title,content:t.content,bgColor:t.bgColor||"#FFFFFF",banner:t.banner||"",isHTML:t.isHTML||!1,entityID:t.entityID||"",entityType:t.entityType||"",noteType:t?.noteType||y.wK.NOTE,type:t?.type||y.JG.PERSONAL});e?.status===200&&m("Note updated successfully.")}catch(e){h("Failed to update the note.")}finally{await e(),s(null)}},u=async t=>{if(!t){h("Invalid note ID.");return}try{await z.b.delete(`/notes/${t}`),m("Note deleted permanently."),e()}catch(e){h("Failed to delete the note."),(0,D.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}d(!1)};return{selectedNote:a,setSelectedNote:s,selectedDeleteNote:r,setSelectedDeleteNote:n,selectedTypeNote:i,setSelectedTypeNote:o,isDeleting:c,setIsDeleting:d,handleSaveEditNote:x,handleDialogClose:()=>{s(null),d(!1)},handleDeletePermanently:u,handleChangeBanner:async(a,s)=>{let l=t.find(e=>e._id===a);if(!l){h("Note not found.");return}try{let t=await z.b.put(`/notes/${l._id}`,{...l,banner:s});t?.status==200&&m("Note Banner updated"),await e()}catch(e){h("Failed to update the note banner."),(0,D.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.log(e)}},handleUpdateNoteType:async(a,s)=>{let l=t.find(e=>e._id===a);if(!l){h("Note not found.");return}try{let t=await z.b.put(`/notes/${l._id}`,{...l,noteType:s});t?.status==200&&m(`Note moved to ${s.toLowerCase()}`),await e()}catch(e){h("Failed to update the note label."),console.log(e)}},handleUpdateNoteLabel:async(a,s)=>{let l=t.find(e=>e._id===a);if(!l){h("Note not found.");return}try{let t=await z.b.put(`/notes/${l._id}`,{...l,type:s});t?.status==200&&m("Note Label updated"),await e()}catch(e){h("Failed to update the note label."),console.log(e)}}}},R=(e,t)=>{let[a,s]=(0,l.useState)(null),[r,n]=(0,l.useState)(null),i=async()=>{if(null!==a&&null!==r&&a!==r){let s=[...e],l=s[a],n=s[r];s[a]=n,s[r]=l,t(s);let i=s.map(e=>e._id),o=s[0]?.userId;if(o)try{let e=await z.b.patch("/notes/update-noteorder",{userId:o,noteOrder:i});200===e.status?console.log("Notes order updated successfully:",e.data):(console.error("Failed to update note order:",e.statusText),(0,D.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}))}catch(e){console.error("Error updating note order:",e.message),console.log(e),(0,D.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}else console.error("User ID is missing. Cannot update note order."),(0,D.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}s(null),n(null)};return{draggingIndex:a,draggingOverIndex:r,handleDragStart:e=>{s(e)},handleDragOver:e=>{a!==e&&n(e)},handleDrop:i}},O=({notes:e,setNotes:t,isArchive:a,isTrash:l,fetchNotes:i})=>{let{selectedNote:d,setSelectedNote:h,selectedDeleteNote:m,setSelectedDeleteNote:x,selectedTypeNote:u,setSelectedTypeNote:p,isDeleting:b,setIsDeleting:f,handleSaveEditNote:v,handleDialogClose:g,handleDeletePermanently:j,handleChangeBanner:N,handleUpdateNoteType:w,handleUpdateNoteLabel:C}=I(i,e),{handleDragStart:Z,handleDragOver:E,handleDrop:A}=R(e,t),z=[{label:"Edit",icon:s.jsx(r.Z,{size:15,className:"text-white-500"}),onClick:(e,t)=>{h(t.find(t=>t._id===e)||null)}},{label:"Delete",icon:s.jsx(n.Z,{size:15,className:"text-white-500"}),onClick:(e,t)=>{f(!0),x(t.find(t=>t._id===e)||null)}},{label:"Recycle",icon:s.jsx(o,{size:15,className:"text-white-500"}),onClick:e=>{w(e,y.wK.TRASH)}},{label:"Label",icon:s.jsx(c.Z,{size:15,className:"text-white-500"}),onClick:(e,t)=>{p(t.find(t=>t._id===e)||null)}}];return(0,s.jsxs)("div",{className:"flex justify-center items-center",children:[s.jsx("div",{className:"columns-1 mt-3 sm:columns-2 md:columns-3 lg:columns-5 gap-6",children:e.map((r,n)=>s.jsx(k,{note:r,onDragStart:()=>Z(n),onDragOver:e=>{e.preventDefault(),E(n)},onDrop:A,notes:e,setNotes:t,isTrash:!!l,isArchive:a,onEditNote:h,onUpdateNoteType:w,onDeleteClick:t=>{f(!0),x(e.find(e=>e._id===t)||null)},onChangeBanner:N,navItems:z},r._id))}),b&&s.jsx(S,{onClose:g,note:m,onDelete:j}),d&&s.jsx(T,{onSave:v,note:d,onClose:g}),u&&s.jsx(M,{note:u,onClose:()=>p(null),onUpdate:C})]})},L=({notes:e,setNotes:t,isArchive:a,isTrash:l,fetchNotes:r})=>s.jsx(O,{notes:e,setNotes:t,isArchive:a,isTrash:l,fetchNotes:r})},46319:(e,t,a)=>{a.d(t,{$C:()=>f,Ne:()=>v,yn:()=>b});var s=a(10326),l=a(95920),r=a(57671),n=a(94909),i=a(12070),o=a(66307),c=a(69669),d=a(40617),h=a(69515),m=a(88378),x=a(40900),u=a(98091),p=a(46226);let b=[{href:"#",icon:s.jsx(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:s.jsx(l.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:s.jsx(r.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:s.jsx(n.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:s.jsx(n.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:s.jsx(i.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:s.jsx(o.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:s.jsx(c.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:s.jsx(d.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:s.jsx(h.Z,{className:"h-5 w-5"}),label:"Notes"}],f=[{href:"/business/settings/business-info",icon:s.jsx(m.Z,{className:"h-5 w-5"}),label:"Settings"}],v=[{href:"#",icon:s.jsx(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:s.jsx(l.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:s.jsx(h.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:s.jsx(x.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:s.jsx(u.Z,{className:"h-5 w-5"}),label:"Trash"}]},48586:(e,t,a)=>{a.d(t,{yL:()=>y,$C:()=>j,yn:()=>g});var s=a(10326),l=a(95920),r=a(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]]),i=(0,r.Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);var o=a(43727);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,r.Z)("TabletSmartphone",[["rect",{width:"10",height:"14",x:"3",y:"8",rx:"2",key:"1vrsiq"}],["path",{d:"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4",key:"1j4zmg"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),d=(0,r.Z)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]]);var h=a(60763);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,r.Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);var x=a(40617),u=a(69515),p=a(88378),b=a(40900),f=a(98091),v=a(46226);let g=[{href:"#",icon:s.jsx(v.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:s.jsx(l.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:s.jsx(n,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:s.jsx(i,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:s.jsx(o.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:s.jsx(c,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:s.jsx(d,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:s.jsx(h.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:s.jsx(m,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:s.jsx(x.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:s.jsx(u.Z,{className:"h-5 w-5"}),label:"Notes"}],j=[{href:"/freelancer/settings/personal-info",icon:s.jsx(p.Z,{className:"h-5 w-5"}),label:"Settings"}];v.default,l.Z,u.Z,b.Z,f.Z;let y=[{href:"#",icon:s.jsx(v.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:s.jsx(l.Z,{className:"h-5 w-5"}),label:"Home"}]},17978:(e,t,a)=>{a.d(t,{Z:()=>n});var s=a(17577),l=a(6260),r=a(56627);let n=e=>{let[t,a]=(0,s.useState)([]),[n,i]=(0,s.useState)([]),[o,c]=(0,s.useState)([]),[d,h]=(0,s.useState)(!1);return{notes:t,archive:n,isLoading:d,fetchNotes:(0,s.useCallback)(async()=>{if(e){h(!0);try{let t=await l.b.get("/notes",{params:{userId:e}});t?.data?.notes&&(a(t.data.notes.notes),i(t.data.notes.archive||[]),c(t.data.notes.trash||[]))}catch(e){console.error("Failed to fetch notes:",e),(0,r.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}finally{h(!1)}}},[e]),setNotes:a,setArchive:i,trash:o,setTrash:c}}},2069:(e,t,a)=>{var s,l,r;a.d(t,{EH:()=>n,JG:()=>l,wK:()=>r}),function(e){e.BUSINESS="BUSINESS",e.FREELANCER="FREELANCER",e.TRANSACTION="TRANSACTION",e.PROJECT="PROJECT",e.BID="BID",e.INTERVIEW="INTERVIEW",e.DEHIX_TALENT="DEHIX_TALENT"}(s||(s={})),function(e){e.PERSONAL="PERSONAL",e.WORK="WORK",e.REMINDER="REMINDER",e.TASK="TASK"}(l||(l={})),function(e){e.NOTE="NOTE",e.TRASH="TRASH",e.ARCHIVE="ARCHIVE"}(r||(r={}));let n={PERSONAL:"bg-blue-500 text-white hover:text-black",WORK:"bg-green-500 text-white hover:text-black",REMINDER:"bg-yellow-500 text-black hover:text-black",TASK:"bg-red-500 text-white hover:text-black"}}};