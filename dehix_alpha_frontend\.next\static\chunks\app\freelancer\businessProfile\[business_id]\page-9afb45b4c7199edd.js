(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1341],{36649:function(e,s,l){Promise.resolve().then(l.bind(l,14420))},5891:function(e,s,l){"use strict";l.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,l(33480).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},20897:function(e,s,l){"use strict";l.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,l(33480).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},13231:function(e,s,l){"use strict";l.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,l(33480).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},71935:function(e,s,l){"use strict";l.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,l(33480).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},47390:function(e,s,l){"use strict";l.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,l(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},98960:function(e,s,l){"use strict";l.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,l(33480).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},73347:function(e,s,l){"use strict";l.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,l(33480).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},10883:function(e,s,l){"use strict";l.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,l(33480).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},14420:function(e,s,l){"use strict";l.r(s),l.d(s,{default:function(){return x}});var t=l(57437),a=l(2265),n=l(16463),r=l(82230),i=l(64797),c=l(48185),o=l(78068),h=l(80420),u=l(15922),d=l(2183),m=l(62688),f=l(89733);function x(){var e,s,l;let{business_id:x}=(0,n.useParams)(),[b,v]=(0,a.useState)(null),[p,j]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{x&&(async()=>{try{let e=await u.b.get("/public/business");200===e.status&&v(e.data)}catch(e){console.error("Error fetching business details",e),(0,o.Am)({variant:"destructive",title:"Error",description:"Failed to fetch business details."})}finally{j(!1)}})()},[x]),(0,t.jsxs)("div",{className:"flex min-h-screen bg-muted/40",children:[(0,t.jsx)(i.Z,{menuItemsTop:r.yn,menuItemsBottom:r.$C,active:"Business Profile"}),(0,t.jsxs)("div",{className:"flex mb-8 flex-1 flex-col sm:pl-14",children:[(0,t.jsx)(m.Z,{menuItemsTop:r.yn,menuItemsBottom:r.$C,activeMenu:"Business Profile",breadcrumbItems:[{label:"Dashboard",link:"/dashboard"},{label:"Business Profile",link:"/dashboard/business-profile"},{label:"#".concat(x),link:"#"}]}),(0,t.jsxs)("main",{className:"flex flex-col items-center p-4 sm:px-6 gap-6 mt-7",children:[(0,t.jsx)("h1",{children:"Business Profile Overview"}),(0,t.jsxs)(c.Zb,{className:"w-full max-w-4xl bg-black text-white p-4 shadow-md",children:[(0,t.jsx)(c.Zb,{className:"p-14 flex items-center rounded-lg",children:p?(0,t.jsx)(d.O,{className:"w-24 h-24 rounded-full mr-6"}):(0,t.jsxs)(h.Avatar,{className:"w-24 h-24 rounded-full mr-6 relative overflow-hidden border-4 border-primary shadow-lg hover:scale-110 transition-transform duration-300 ease-in-out",children:[(0,t.jsx)(h.AvatarImage,{src:"/default-avatar.png",alt:"".concat(null==b?void 0:b.firstName," ").concat(null==b?void 0:b.lastName," Profile Picture"),className:"object-cover w-full h-full"}),(0,t.jsx)(h.AvatarFallback,{children:"".concat((null==b?void 0:null===(e=b.firstName)||void 0===e?void 0:e[0])||"J").concat((null==b?void 0:null===(s=b.lastName)||void 0===s?void 0:s[0])||"D")})]})}),(0,t.jsxs)(c.Zb,{className:"w-full max-w-4xl shadow-lg",children:[(0,t.jsx)(c.Ol,{children:(0,t.jsx)(c.ll,{children:"Projects"})}),(0,t.jsx)(c.aY,{children:p?(0,t.jsx)(d.O,{className:"h-6 w-48"}):(null==b?void 0:null===(l=b.ProjectList)||void 0===l?void 0:l.length)?(0,t.jsx)("ul",{className:"list-disc pl-5",children:b.ProjectList.map((e,s)=>(0,t.jsx)("li",{children:e},s))}):(0,t.jsx)("p",{children:"No projects listed."})})]}),(0,t.jsxs)(c.Zb,{className:"w-full max-w-4xl shadow-lg",children:[(0,t.jsx)(c.Ol,{children:(0,t.jsx)(c.ll,{children:"Contact Information"})}),(0,t.jsx)(c.aY,{children:p?(0,t.jsx)(d.O,{className:"h-6 w-48"}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("p",{children:["Email: ",null==b?void 0:b.email]}),(null==b?void 0:b.linkedin)&&(0,t.jsxs)("p",{children:["LinkedIn:"," ",(0,t.jsx)("a",{href:b.linkedin,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:b.linkedin})]}),(null==b?void 0:b.personalWebsite)&&(0,t.jsxs)("p",{children:["Website:"," ",(0,t.jsx)("a",{href:b.personalWebsite,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:b.personalWebsite})]})]})})]})]}),(0,t.jsx)(f.z,{onClick:()=>{let e={title:"Business Profile",text:"Check out the profile of ".concat(null==b?void 0:b.firstName," ").concat(null==b?void 0:b.lastName," at ").concat(null==b?void 0:b.companyName,"."),url:window.location.href};navigator.share?navigator.share(e).catch(e=>console.error("Error sharing:",e)):(0,o.Am)({variant:"destructive",title:"Error",description:"Sharing is not supported on this browser."})},className:"mt-4",children:"Share Profile"})]})]})]})}},2183:function(e,s,l){"use strict";l.d(s,{O:function(){return n}});var t=l(57437),a=l(49354);function n(e){let{className:s,...l}=e;return(0,t.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-primary/10",s),...l})}},82230:function(e,s,l){"use strict";l.d(s,{$C:function(){return v},Ne:function(){return p},yn:function(){return b}});var t=l(57437),a=l(11005),n=l(98960),r=l(38133),i=l(20897),c=l(13231),o=l(71935),h=l(47390),u=l(73347),d=l(24258),m=l(5891),f=l(10883),x=l(66648);let b=[{href:"#",icon:(0,t.jsx)(x.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,t.jsx)(a.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:(0,t.jsx)(n.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:(0,t.jsx)(r.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:(0,t.jsx)(r.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:(0,t.jsx)(i.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:(0,t.jsx)(c.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:(0,t.jsx)(o.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:(0,t.jsx)(h.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,t.jsx)(u.Z,{className:"h-5 w-5"}),label:"Notes"}],v=[{href:"/business/settings/business-info",icon:(0,t.jsx)(d.Z,{className:"h-5 w-5"}),label:"Settings"}],p=[{href:"#",icon:(0,t.jsx)(x.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,t.jsx)(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:(0,t.jsx)(u.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:(0,t.jsx)(m.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:(0,t.jsx)(f.Z,{className:"h-5 w-5"}),label:"Trash"}]}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2455,9726,2688,2971,7023,1744],function(){return e(e.s=36649)}),_N_E=e.O()}]);