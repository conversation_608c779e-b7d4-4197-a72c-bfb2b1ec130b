(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5032],{75759:function(e,t,n){Promise.resolve().then(n.bind(n,70470))},25912:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,n(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},42421:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,n(33480).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},13231:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,n(33480).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},32309:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,n(33480).Z)("Dot",[["circle",{cx:"12.1",cy:"12.1",r:"1",key:"18d7e5"}]])},47019:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,n(33480).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},75733:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,n(33480).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},22757:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,n(33480).Z)("Rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]])},20500:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,n(33480).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},52022:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,n(33480).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},70470:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return L}});var s=n(57437),r=n(87138),a=n(90564),i=n(31014),l=n(44504),o=n(52022),c=n(25912),d=n(20500),u=n(13231),m=n(75733),p=n(47019),h=n(3274),f=n(71976),x=n(95137),g=n(22757),v=n(16463),b=n(2265),j=n(39343),y=n(59772),w=n(67319),N=n(8185),k=n(65438),C=n(60253),S=n(89733),P=n(93363),E=n(77209),z=n(70402),Z=n(2128),M=n(78068),A=n(15922),T=n(49354),I=n(54662);function R(e){let{open:t,setOpen:n,setIsChecked:r}=e;return(0,s.jsx)(I.Vq,{open:t,onOpenChange:n,children:(0,s.jsxs)(I.cZ,{className:"max-w-5xl sm:mx-4 max-h-screen overflow-y-auto rounded-2xl p-6 shadow-lg",children:[(0,s.jsx)(I.fK,{children:(0,s.jsx)(I.$N,{className:"text-2xl font-bold mb-4 text-center",children:"Terms & Conditions for Businesses/Clients"})}),(0,s.jsxs)("div",{className:"space-y-6 text-sm leading-relaxed px-2 sm:px-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-base mb-1",children:"1. Registration and Account Management"}),(0,s.jsx)("p",{children:"By registering as a business or client on the platform, users agree to abide by these terms and conditions. Businesses must provide accurate, complete, and up-to-date company information during registration. Failure to provide correct details may result in account rejection or termination. The platform reserves the right to approve, reject, or terminate business accounts at its sole discretion."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-base mb-1",children:"2. Responsibilities of Businesses"}),(0,s.jsx)("p",{children:"Businesses are responsible for carefully reviewing freelancer profiles, portfolios, and qualifications before making hiring decisions. The platform does not vet or guarantee the performance of freelancers. Businesses acknowledge that freelancers are independent contractors and not employees of the platform. Therefore, the platform holds no liability for disputes regarding employment status, wages, or project outcomes. Businesses must comply with local labor laws when engaging freelancers through the platform."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-base mb-1",children:"3. Platform Limitations"}),(0,s.jsx)("p",{children:"The platform acts solely as a connection facilitator between freelancers and businesses. It does not provide job guarantees or ensure the quality of work delivered. The platform is not responsible for delays, missed deadlines, incomplete work, or disputes between businesses and freelancers."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-base mb-1",children:"4. Payment Policies"}),(0,s.jsx)("p",{children:"Direct payments to freelancers outside the platform are strictly prohibited. All payments must be processed through the platform to ensure security, accountability, and dispute resolution. Upon successful payment, businesses retain full rights to freelancer-created content or work unless otherwise agreed in writing."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-base mb-1",children:"5. Job Posting Guidelines"}),(0,s.jsx)("p",{children:"Businesses are prohibited from posting scam jobs, misleading job descriptions, or any job listings that promote illegal or unethical activities. The platform reserves the right to review, modify, or remove job postings that violate these guidelines."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-base mb-1",children:"6. Communication and Conduct"}),(0,s.jsx)("p",{children:"Businesses must maintain professional, respectful, and non-offensive communication when interacting with freelancers or platform representatives. Spam, harassment, or abusive behavior will result in account suspension or termination."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-base mb-1",children:"7. Liability and Disputes"}),(0,s.jsx)("p",{children:"The platform is not liable for freelancer performance or quality of work, missed deadlines or incomplete deliverables, or financial losses incurred by businesses due to freelancer engagements."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-base mb-1",children:"8. Account Termination"}),(0,s.jsx)("p",{children:"Accounts may be terminated if businesses provide false or misleading information, attempt to bypass platform payment processes, post scam or illegal job offers, or violate communication guidelines or engage in unethical conduct."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-base mb-1",children:"9. Use of User Information"}),(0,s.jsx)("p",{children:"User information may be utilized to improve platform functionality and matchmaking, assist in effective communication between clients and freelancers, and support marketing campaigns, promotions, or platform updates (with the option to manage notification preferences)."})]})]}),(0,s.jsx)("div",{className:"mt-8 flex justify-end",children:(0,s.jsx)(S.z,{onClick:()=>{r(!0),n(!1)},className:"px-6 py-2 text-sm font-medium rounded-md bg-primary hover:bg-primary/90 transition",children:"I Accept"})})]})})}let B=e=>{let{currentStep:t=0}=e,n=[{id:0,title:"Personal Info",icon:o.Z},{id:1,title:"Company Info",icon:c.Z},{id:2,title:"Verification",icon:d.Z}];return(0,s.jsxs)("div",{className:"w-full max-w-5xl mx-auto py-4  sm:py-6 mb-10 sm:mb-8",children:[(0,s.jsxs)("div",{className:"text-center space-y-2 sm:space-y-4",children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold",children:["Create Your Business ",(0,s.jsx)("span",{className:"block",children:"Account"})]}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Join our community and find the best talent in web3 space"})]}),(0,s.jsxs)("div",{className:"my-4 text-center text-xs sm:text-sm",children:["Are you a Freelancer?"," ",(0,s.jsx)(S.z,{variant:"outline",size:"sm",className:"ml-2",asChild:!0,children:(0,s.jsx)(r.default,{href:"/auth/sign-up/freelancer",children:"Register Freelancer"})})]}),(0,s.jsx)("div",{className:"flex items-center justify-center sm:mt-8 px-2 sm:px-0",children:n.map((e,r)=>(0,s.jsxs)(b.Fragment,{children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-8 h-8 sm:w-12 sm:h-12 flex items-center justify-center rounded-full border-2 transition-all duration-300\n                ".concat(t>e.id?"bg-primary border-primary":t===e.id?"border-primary bg-background text-primary":"border-muted bg-background text-muted"),children:t>e.id?(0,s.jsx)(u.Z,{className:"w-4 h-4 sm:w-6 sm:h-6 text-background"}):(0,s.jsx)(e.icon,{className:"w-4 h-4 sm:w-6 sm:h-6"})}),(0,s.jsx)("span",{className:"absolute -bottom-6 left-1/2 -translate-x-1/2 text-xs sm:text-sm whitespace-nowrap font-medium\n                ".concat(t>=e.id?"text-primary":"text-muted-foreground"),children:e.title})]}),r<n.length-1&&(0,s.jsx)("div",{className:"w-20 sm:w-40 mx-2 sm:mx-4 h-[2px] bg-muted",children:(0,s.jsx)("div",{className:"h-full bg-primary transition-all duration-500",style:{width:t>e.id?"100%":"0%"}})})]},e.id))})]})},D=y.z.object({firstName:y.z.string().min(1,"First name is required"),lastName:y.z.string().min(1,"Last name is required"),userName:y.z.string().min(1,"Username is required"),companyName:y.z.string().min(1,"Company name is required"),companySize:y.z.string().min(1,"Company size is required"),position:y.z.string().min(1,"Position is required"),email:y.z.string().email("Invalid email address"),phone:y.z.string().min(1,"Phone number is required"),referralCode:y.z.string().optional(),linkedin:y.z.string().url("Invalid URL").optional().refine(e=>!e||/^https:\/\/www\.linkedin\.com\/in\/[a-zA-Z0-9_-]+\/?$/.test(e),{message:'LinkedIn URL must start with "https://www.linkedin.com/in/" and have a valid username'}),personalWebsite:y.z.string().optional().refine(e=>!e||/^(https?:\/\/|www\.)[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}.*[a-zA-Z0-9].*$/.test(e),{message:'Invalid website URL. Must start with "www." or "https://" and contain letters'}),password:y.z.string().min(8,"Password must be at least 8 characters long"),confirmPassword:y.z.string().min(8,"Confirm Password must be at least 8 characters long")}).refine(e=>e.password===e.confirmPassword,{path:["confirmPassword"],message:"Passwords do not match"});function O(){let[e,t]=(0,b.useState)(0);return(0,s.jsx)("div",{className:"flex w-full items-center justify-center",children:(0,s.jsxs)("div",{className:"w-full max-w-5xl px-4 sm:px-6 lg:px-4",children:[(0,s.jsx)(B,{currentStep:e}),(0,s.jsx)("div",{className:"flex justify-center w-full ",children:(0,s.jsx)("div",{className:"w-full max-w-4xl",children:(0,s.jsx)(W,{currentStep:e,setCurrentStep:t})})})]})})}function W(e){let{currentStep:t,setCurrentStep:n}=e,[r,a]=(0,b.useState)(!1),[o,c]=(0,b.useState)(!1),[d,u]=(0,b.useState)("IN"),[y,I]=(0,b.useState)(""),[B,O]=(0,b.useState)(!1),[W,L]=(0,b.useState)(!1),[F,_]=(0,b.useState)(!1),[V,q]=(0,b.useState)(!1),U=(0,v.useSearchParams)(),[H,$]=(0,b.useState)(null),G=()=>{c(e=>!e)},Q=(0,j.cI)({resolver:(0,i.F)(D),defaultValues:{firstName:"",lastName:"",userName:"",companyName:"",companySize:"",position:"",email:"",phone:"",linkedin:"",personalWebsite:"",password:"",referralCode:""},mode:"all"}),J=async()=>{n(t-1)},Y=async()=>{if(0===t){if(await Q.trigger(["firstName","lastName","userName","email","password","confirmPassword"])){let{userName:e}=Q.getValues();try{if(_(!0),e===H){n(t+1);return}let s=await A.b.get("/public/username/check-duplicate?username=".concat(e,"&is_business=true"));!1===s.data.duplicate?n(t+1):((0,M.Am)({variant:"destructive",title:"User Already Exists",description:"This username is already taken. Please choose another one."}),$(e))}catch(e){(0,M.Am)({variant:"destructive",title:"API Error",description:"There was an error while checking the username."})}finally{_(!1)}}else(0,M.Am)({variant:"destructive",title:"Validation Error",description:"Please fill in all required fields before proceeding."})}else 1===t&&(await Q.trigger(["companyName","companySize","position","linkedin","personalWebsite"])?n(t+1):(0,M.Am)({variant:"destructive",title:"Validation Error",description:"Please fill in all required fields before proceeding."}))},K=async e=>{var t,n,r;a(!0);let i=U.get("referral"),o=e.referralCode,c=i||o||null;I("".concat(null===(t=w.find(e=>e.code===d))||void 0===t?void 0:t.dialCode).concat(e.phone));let u={...e,phone:"".concat(null===(n=w.find(e=>e.code===d))||void 0===n?void 0:n.dialCode).concat(e.phone),phoneVerify:!1,isBusiness:!0,connects:0,otp:"123456",otpverified:"No",ProjectList:[],Appliedcandidates:[],hirefreelancer:[],refer:"",verified:"",isVerified:!1};try{await A.b.post(c?"/register/business?referralCode=".concat(c):"/register/business",u),(0,M.Am)({title:"Account created successfully!",description:"Your business account has been created."}),L(!0)}catch(e){console.error("API Error:",e),(0,M.Am)({variant:"destructive",title:"Uh oh! Something went wrong.",description:"Error: ".concat((null===(r=e.response)||void 0===r?void 0:r.data.message)||"Something went wrong!"),action:(0,s.jsx)(l.gD,{altText:"Try again",children:"Try again"})})}finally{a(!1)}};return(0,s.jsx)(P.l0,{...Q,children:(0,s.jsxs)("form",{onSubmit:Q.handleSubmit(K),className:"w-full max-w-3xl mx-auto",children:[(0,s.jsx)("div",{className:"w-full p-4 sm:p-6 rounded-lg shadow-sm border",children:(0,s.jsxs)("div",{className:"grid gap-4 sm:gap-6 w-full",children:[(0,s.jsxs)("div",{className:(0,T.cn)("grid gap-4",0===t?"":"hidden"),children:[(0,s.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,s.jsx)(k.Z,{control:Q.control,name:"firstName",label:"First name",placeholder:"First Name"}),(0,s.jsx)(k.Z,{control:Q.control,name:"lastName",label:"Last name",placeholder:"Last Name"})]}),(0,s.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,s.jsx)(k.Z,{control:Q.control,name:"userName",label:"Username",placeholder:"Username"}),(0,s.jsx)(k.Z,{control:Q.control,name:"email",label:"Email",placeholder:"Enter your email",type:"email"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(z.Label,{children:"Password"}),(0,s.jsx)(P.Wi,{control:Q.control,name:"password",render:e=>{let{field:t}=e;return(0,s.jsxs)(P.xJ,{children:[(0,s.jsx)(P.NI,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(E.I,{placeholder:"Enter your password",type:o?"text":"password",className:"pr-10",...t}),(0,s.jsx)("button",{type:"button",onClick:G,className:"absolute inset-y-0 right-0 px-3 flex items-center",children:o?(0,s.jsx)(m.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"}):(0,s.jsx)(p.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"})})]})}),(0,s.jsx)(P.zG,{})]})}})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(z.Label,{children:"Confirm Password"}),(0,s.jsx)(P.Wi,{control:Q.control,name:"confirmPassword",render:e=>{let{field:t}=e;return(0,s.jsxs)(P.xJ,{children:[(0,s.jsx)(P.NI,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(E.I,{placeholder:"Confirm your password",type:o?"text":"password",className:"pr-10",...t}),(0,s.jsx)("button",{type:"button",onClick:G,className:"absolute inset-y-0 right-0 px-3 flex items-center",children:o?(0,s.jsx)(m.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"}):(0,s.jsx)(p.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"})})]})}),(0,s.jsx)(P.zG,{})]})}})]}),(0,s.jsx)("div",{className:"flex gap-2 justify-end mt-4",children:(0,s.jsx)(S.z,{type:"button",onClick:Y,className:"w-full sm:w-auto flex items-center justify-center",disabled:F,children:F?(0,s.jsx)(h.Z,{size:20,className:"animate-spin"}):(0,s.jsxs)(s.Fragment,{children:["Next",(0,s.jsx)(f.Z,{className:"w-4 h-4 ml-2"})]})})})]}),(0,s.jsxs)("div",{className:(0,T.cn)("grid gap-4",1===t?"":"hidden"),children:[(0,s.jsx)(k.Z,{control:Q.control,name:"companyName",label:"Company Name",placeholder:"Company Name"}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(z.Label,{htmlFor:"company-size",children:"Company Size"}),(0,s.jsx)(j.Qr,{control:Q.control,name:"companySize",render:e=>{let{field:t}=e;return(0,s.jsxs)(Z.Ph,{onValueChange:t.onChange,value:t.value,children:[(0,s.jsx)(Z.i4,{id:"company-size",children:(0,s.jsx)(Z.ki,{placeholder:"Select Size"})}),(0,s.jsx)(Z.Bw,{children:(0,s.jsxs)(Z.DI,{children:[(0,s.jsx)(Z.Ql,{value:"0-20",children:"0-20"}),(0,s.jsx)(Z.Ql,{value:"20-50",children:"20-50"}),(0,s.jsx)(Z.Ql,{value:"50-100",children:"50-100"}),(0,s.jsx)(Z.Ql,{value:"100-500",children:"100-500"}),(0,s.jsx)(Z.Ql,{value:"500+",children:"500 +"})]})})]})}})]}),(0,s.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,s.jsx)(k.Z,{control:Q.control,name:"position",label:"Position",placeholder:"CTO"}),(0,s.jsx)(k.Z,{control:Q.control,name:"referralCode",label:"Referral",type:"string",placeholder:"JOHN123",className:"w-full"})]}),(0,s.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,s.jsx)(k.Z,{control:Q.control,name:"linkedin",label:"LinkedIn",placeholder:"https://www.linkedin.com/in/username",type:"url"}),(0,s.jsx)(k.Z,{control:Q.control,name:"personalWebsite",label:"Portfolio Url",type:"url",placeholder:"https://www.yourwebsite.com"})]}),(0,s.jsxs)("div",{className:"flex gap-2 justify-between mt-4",children:[(0,s.jsxs)(S.z,{type:"button",onClick:J,className:"w-full sm:w-auto",children:[(0,s.jsx)(x.Z,{className:"w-4 h-4 mr-2"}),"Previous"]}),(0,s.jsxs)(S.z,{type:"button",onClick:Y,className:"w-full sm:w-auto",children:["Next",(0,s.jsx)(f.Z,{className:"w-4 h-4 ml-2"})]})]})]}),(0,s.jsxs)("div",{className:(0,T.cn)("grid gap-4",2===t?"":"hidden"),children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(z.Label,{htmlFor:"phone",children:"Phone Number"}),(0,s.jsx)(N.Z,{control:Q.control,setCode:u,code:d})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-4",children:[(0,s.jsx)("input",{type:"checkbox",id:"terms",checked:B,onChange:()=>{V||O(!B)},className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,s.jsxs)("label",{htmlFor:"terms",className:"text-sm text-gray-600",children:["I agree to the"," ",(0,s.jsx)("span",{onClick:()=>q(!0),className:"text-primary hover:underline",children:"Terms and Conditions"})]}),(0,s.jsx)(R,{open:V,setOpen:q,setIsChecked:O})]}),(0,s.jsxs)("div",{className:"flex gap-2 flex-col sm:flex-row justify-between mt-4",children:[(0,s.jsxs)(S.z,{type:"button",onClick:J,className:"w-full sm:w-auto",children:[(0,s.jsx)(x.Z,{className:"w-4 h-4 mr-2"}),"Previous"]}),(0,s.jsxs)(S.z,{type:"submit",className:"w-full sm:w-auto",disabled:r||!B,children:[r?(0,s.jsx)(h.Z,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(g.Z,{className:"mr-2 h-4 w-4"}),"Create account"]})]})]})]})}),(0,s.jsx)(C.Z,{phoneNumber:y,isModalOpen:W,setIsModalOpen:L})]})})}function L(){return(0,s.jsxs)("div",{className:"relative min-h-screen w-full",children:[(0,s.jsx)("div",{className:"absolute left-4 top-4 sm:left-10 sm:top-10",children:(0,s.jsx)(a.T,{})}),(0,s.jsx)("div",{className:"flex items-center justify-center py-20 sm:py-12",children:(0,s.jsx)("div",{className:"mx-auto w-full  px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"grid gap-6",children:[(0,s.jsx)(O,{}),(0,s.jsxs)("div",{className:"mt-4 text-center text-xs sm:text-sm",children:["Already have an account?"," ",(0,s.jsx)(S.z,{variant:"outline",size:"sm",className:"ml-2",asChild:!0,children:(0,s.jsx)(r.default,{href:"/auth/login",children:"Sign in"})})]}),(0,s.jsxs)("p",{className:"px-2 text-center text-xs text-muted-foreground sm:px-8 sm:text-sm",children:["By clicking continue, you agree to our"," ",(0,s.jsx)(S.z,{variant:"link",className:"p-0",asChild:!0,children:(0,s.jsx)(r.default,{href:"/terms",children:"Terms of Service"})})," ","and"," ",(0,s.jsx)(S.z,{variant:"link",className:"p-0",asChild:!0,children:(0,s.jsx)(r.default,{href:"/privacy",children:"Privacy Policy."})})]})]})})})]})}},62361:function(e,t,n){"use strict";function s(e,[t,n]){return Math.min(n,Math.max(t,e))}n.d(t,{u:function(){return s}})},38364:function(e,t,n){"use strict";n.d(t,{f:function(){return l}});var s=n(2265),r=n(18676),a=n(57437),i=s.forwardRef((e,t)=>(0,a.jsx)(r.WV.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i},47250:function(e,t,n){"use strict";n.d(t,{D:function(){return r}});var s=n(2265);function r(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},31725:function(e,t,n){"use strict";n.d(t,{T:function(){return i},f:function(){return l}});var s=n(2265),r=n(18676),a=n(57437),i=s.forwardRef((e,t)=>(0,a.jsx)(r.WV.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));i.displayName="VisuallyHidden";var l=i},66431:function(e,t,n){"use strict";n.d(t,{VM:function(){return h},uZ:function(){return f}});var s=n(2265),r=Object.defineProperty,a=Object.defineProperties,i=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,d=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,u=(e,t)=>{for(var n in t||(t={}))o.call(t,n)&&d(e,n,t[n]);if(l)for(var n of l(t))c.call(t,n)&&d(e,n,t[n]);return e},m=(e,t)=>a(e,i(t)),p=(e,t)=>{var n={};for(var s in e)o.call(e,s)&&0>t.indexOf(s)&&(n[s]=e[s]);if(null!=e&&l)for(var s of l(e))0>t.indexOf(s)&&c.call(e,s)&&(n[s]=e[s]);return n},h=s.createContext({}),f=s.forwardRef((e,t)=>{let n;var r,a,i,l,o,{value:c,onChange:d,maxLength:f,textAlign:v="left",pattern:b="^\\d+$",inputMode:j="numeric",onComplete:y,pushPasswordManagerStrategy:w="increase-width",containerClassName:N,noScriptCSSFallback:k=g,render:C,children:S}=e,P=p(e,["value","onChange","maxLength","textAlign","pattern","inputMode","onComplete","pushPasswordManagerStrategy","containerClassName","noScriptCSSFallback","render","children"]);let[E,z]=s.useState("string"==typeof P.defaultValue?P.defaultValue:""),Z=null!=c?c:E,M=(n=s.useRef(),s.useEffect(()=>{n.current=Z}),n.current),A=s.useCallback(e=>{null==d||d(e),z(e)},[d]),T=s.useMemo(()=>b?"string"==typeof b?new RegExp(b):b:null,[b]),I=s.useRef(null),R=s.useRef(null),B=s.useRef({value:Z,onChange:A,isIOS:"undefined"!=typeof window&&(null==(a=null==(r=null==window?void 0:window.CSS)?void 0:r.supports)?void 0:a.call(r,"-webkit-touch-callout","none"))}),D=s.useRef({prev:[null==(i=I.current)?void 0:i.selectionStart,null==(l=I.current)?void 0:l.selectionEnd,null==(o=I.current)?void 0:o.selectionDirection]});s.useImperativeHandle(t,()=>I.current,[]),s.useEffect(()=>{let e=I.current,t=R.current;if(!e||!t)return;function n(){if(document.activeElement!==e){V(null),U(null);return}let t=e.selectionStart,n=e.selectionEnd,s=e.selectionDirection,r=e.maxLength,a=e.value,i=D.current.prev,l=-1,o=-1,c;if(0!==a.length&&null!==t&&null!==n){let e=t===n,s=t===a.length&&a.length<r;if(e&&!s){if(0===t)l=0,o=1,c="forward";else if(t===r)l=t-1,o=t,c="backward";else if(r>1&&a.length>1){let e=0;if(null!==i[0]&&null!==i[1]){c=t<i[1]?"backward":"forward";let n=i[0]===i[1]&&i[0]<r;"backward"!==c||n||(e=-1)}l=e+t,o=e+t+1}}-1!==l&&-1!==o&&l!==o&&I.current.setSelectionRange(l,o,c)}let d=-1!==l?l:t,u=-1!==o?o:n,m=null!=c?c:s;V(d),U(u),D.current.prev=[d,u,m]}if(B.current.value!==e.value&&B.current.onChange(e.value),D.current.prev=[e.selectionStart,e.selectionEnd,e.selectionDirection],document.addEventListener("selectionchange",n,{capture:!0}),n(),document.activeElement===e&&F(!0),!document.getElementById("input-otp-style")){let e=document.createElement("style");if(e.id="input-otp-style",document.head.appendChild(e),e.sheet){let t="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";x(e.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),x(e.sheet,`[data-input-otp]:autofill { ${t} }`),x(e.sheet,`[data-input-otp]:-webkit-autofill { ${t} }`),x(e.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),x(e.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let s=()=>{t&&t.style.setProperty("--root-height",`${e.clientHeight}px`)};s();let r=new ResizeObserver(s);return r.observe(e),()=>{document.removeEventListener("selectionchange",n,{capture:!0}),r.disconnect()}},[]);let[O,W]=s.useState(!1),[L,F]=s.useState(!1),[_,V]=s.useState(null),[q,U]=s.useState(null);s.useEffect(()=>{var e;setTimeout(e=()=>{var e,t,n,s;null==(e=I.current)||e.dispatchEvent(new Event("input"));let r=null==(t=I.current)?void 0:t.selectionStart,a=null==(n=I.current)?void 0:n.selectionEnd,i=null==(s=I.current)?void 0:s.selectionDirection;null!==r&&null!==a&&(V(r),U(a),D.current.prev=[r,a,i])},0),setTimeout(e,10),setTimeout(e,50)},[Z,L]),s.useEffect(()=>{void 0!==M&&Z!==M&&M.length<f&&Z.length===f&&(null==y||y(Z))},[f,y,M,Z]);let H=function({containerRef:e,inputRef:t,pushPasswordManagerStrategy:n,isFocused:r}){let a=s.useRef({done:!1,refocused:!1}),[i,l]=s.useState(!1),[o,c]=s.useState(!1),[d,u]=s.useState(!1),m=s.useMemo(()=>"none"!==n&&("increase-width"===n||"experimental-no-flickering"===n)&&i&&o,[i,o,n]),p=s.useCallback(()=>{let s=e.current,r=t.current;if(!s||!r||d||"none"===n)return;let i=s.getBoundingClientRect().left+s.offsetWidth,o=s.getBoundingClientRect().top+s.offsetHeight/2;if(!(0===document.querySelectorAll('[data-lastpass-icon-root],com-1password-button,[data-dashlanecreated],[style$="2147483647 !important;"]').length&&document.elementFromPoint(i-18,o)===s)&&(l(!0),u(!0),!a.current.refocused&&document.activeElement===r)){let e=[r.selectionStart,r.selectionEnd];r.blur(),r.focus(),r.setSelectionRange(e[0],e[1]),a.current.refocused=!0}},[e,t,d,n]);return s.useEffect(()=>{let t=e.current;if(!t||"none"===n)return;function s(){c(window.innerWidth-t.getBoundingClientRect().right>=40)}s();let r=setInterval(s,1e3);return()=>{clearInterval(r)}},[e,n]),s.useEffect(()=>{let e=r||document.activeElement===t.current;if("none"===n||!e)return;let s=setTimeout(p,0),a=setTimeout(p,2e3),i=setTimeout(p,5e3),l=setTimeout(()=>{u(!0)},6e3);return()=>{clearTimeout(s),clearTimeout(a),clearTimeout(i),clearTimeout(l)}},[t,r,n,p]),{hasPWMBadge:i,willPushPWMBadge:m,PWM_BADGE_SPACE_WIDTH:"40px"}}({containerRef:R,inputRef:I,pushPasswordManagerStrategy:w,isFocused:L}),$=s.useCallback(e=>{let t=e.currentTarget.value.slice(0,f);if(t.length>0&&T&&!T.test(t)){e.preventDefault();return}"string"==typeof M&&t.length<M.length&&document.dispatchEvent(new Event("selectionchange")),A(t)},[f,A,M,T]),G=s.useCallback(()=>{var e;if(I.current){let t=Math.min(I.current.value.length,f-1),n=I.current.value.length;null==(e=I.current)||e.setSelectionRange(t,n),V(t),U(n)}F(!0)},[f]),Q=s.useCallback(e=>{var t,n;let s=I.current;if(!B.current.isIOS||!e.clipboardData||!s)return;let r=e.clipboardData.getData("text/plain");e.preventDefault();let a=null==(t=I.current)?void 0:t.selectionStart,i=null==(n=I.current)?void 0:n.selectionEnd,l=(a!==i?Z.slice(0,a)+r+Z.slice(i):Z.slice(0,a)+r+Z.slice(a)).slice(0,f);if(l.length>0&&T&&!T.test(l))return;s.value=l,A(l);let o=Math.min(l.length,f-1),c=l.length;s.setSelectionRange(o,c),V(o),U(c)},[f,A,T,Z]),J=s.useMemo(()=>({position:"relative",cursor:P.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[P.disabled]),Y=s.useMemo(()=>({position:"absolute",inset:0,width:H.willPushPWMBadge?`calc(100% + ${H.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:H.willPushPWMBadge?`inset(0 ${H.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:v,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[H.PWM_BADGE_SPACE_WIDTH,H.willPushPWMBadge,v]),K=s.useMemo(()=>s.createElement("input",m(u({autoComplete:P.autoComplete||"one-time-code"},P),{"data-input-otp":!0,"data-input-otp-mss":_,"data-input-otp-mse":q,inputMode:j,pattern:null==T?void 0:T.source,style:Y,maxLength:f,value:Z,ref:I,onPaste:e=>{var t;Q(e),null==(t=P.onPaste)||t.call(P,e)},onChange:$,onMouseOver:e=>{var t;W(!0),null==(t=P.onMouseOver)||t.call(P,e)},onMouseLeave:e=>{var t;W(!1),null==(t=P.onMouseLeave)||t.call(P,e)},onFocus:e=>{var t;G(),null==(t=P.onFocus)||t.call(P,e)},onBlur:e=>{var t;F(!1),null==(t=P.onBlur)||t.call(P,e)}})),[$,G,Q,j,Y,f,q,_,P,null==T?void 0:T.source,Z]),X=s.useMemo(()=>({slots:Array.from({length:f}).map((e,t)=>{let n=L&&null!==_&&null!==q&&(_===q&&t===_||t>=_&&t<q),s=void 0!==Z[t]?Z[t]:null;return{char:s,isActive:n,hasFakeCaret:n&&null===s}}),isFocused:L,isHovering:!P.disabled&&O}),[L,O,f,q,_,P.disabled,Z]),ee=s.useMemo(()=>C?C(X):s.createElement(h.Provider,{value:X},S),[S,X,C]);return s.createElement(s.Fragment,null,null!==k&&s.createElement("noscript",null,s.createElement("style",null,k)),s.createElement("div",{ref:R,"data-input-otp-container":!0,style:J,className:N},ee,s.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},K)))});function x(e,t){try{e.insertRule(t)}catch(e){console.error("input-otp could not insert CSS rule:",t)}}f.displayName="Input";var g=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,9812,4022,1374,4504,2455,253,2908,2971,7023,1744],function(){return e(e.s=75759)}),_N_E=e.O()}]);