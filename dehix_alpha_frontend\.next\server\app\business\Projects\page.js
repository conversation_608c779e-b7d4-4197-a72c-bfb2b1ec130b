(()=>{var e={};e.id=684,e.ids=[684],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},99608:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>m,routeModule:()=>h,tree:()=>c}),s(22866),s(54302),s(12523);var r=s(23191),a=s(88716),i=s(37922),n=s.n(i),o=s(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["business",{children:["Projects",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,22866)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\Projects\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],m=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\Projects\\page.tsx"],d="/business/Projects/page",p={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/business/Projects/page",pathname:"/business/Projects",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},51214:(e,t,s)=>{Promise.resolve().then(s.bind(s,22990))},71475:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Boxes",[["path",{d:"M2.97 12.92A2 2 0 0 0 2 14.63v3.24a2 2 0 0 0 .97 1.71l3 1.8a2 2 0 0 0 2.06 0L12 19v-5.5l-5-3-4.03 2.42Z",key:"lc1i9w"}],["path",{d:"m7 16.5-4.74-2.85",key:"1o9zyk"}],["path",{d:"m7 16.5 5-3",key:"va8pkn"}],["path",{d:"M7 16.5v5.17",key:"jnp8gn"}],["path",{d:"M12 13.5V19l3.97 2.38a2 2 0 0 0 2.06 0l3-1.8a2 2 0 0 0 .97-1.71v-3.24a2 2 0 0 0-.97-1.71L17 10.5l-5 3Z",key:"8zsnat"}],["path",{d:"m17 16.5-5-3",key:"8arw3v"}],["path",{d:"m17 16.5 4.74-2.85",key:"8rfmw"}],["path",{d:"M17 16.5v5.17",key:"k6z78m"}],["path",{d:"M7.97 4.42A2 2 0 0 0 7 6.13v4.37l5 3 5-3V6.13a2 2 0 0 0-.97-1.71l-3-1.8a2 2 0 0 0-2.06 0l-3 1.8Z",key:"1xygjf"}],["path",{d:"M12 8 7.26 5.15",key:"1vbdud"}],["path",{d:"m12 8 4.74-2.85",key:"3rx089"}],["path",{d:"M12 13.5V8",key:"1io7kd"}]])},43727:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},48705:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},99899:(e,t,s)=>{"use strict";var r=s(56715);function a(){}function i(){}i.resetWarningCache=a,e.exports=function(){function e(e,t,s,a,i,n){if(n!==r){var o=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var s={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:a};return s.PropTypes=s,s}},78439:(e,t,s)=>{e.exports=s(99899)()},56715:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},22990:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>D});var r=s(10326),a=s(71475),i=s(95920),n=s(48705),o=s(94909),l=s(43727),c=s(88378),m=s(35047),d=s(29752),p=s(78062);s(17577);var h=s(43810),u=s(78439),x=s.n(u),f=s(91664);let g=({interviewer:e,interviewee:t,skill:s,interviewDate:a,rating:i,comments:n})=>(0,r.jsxs)(d.Zb,{className:"overflow-hidden","x-chunk":"dashboard-05-chunk-4",children:[r.jsx(d.Ol,{className:"flex flex-row items-start bg-muted/50",children:r.jsx("div",{className:"grid gap-0.5",children:(0,r.jsxs)(d.ll,{className:"group flex items-center gap-2 text-lg",children:[`Interview with ${t}`,(0,r.jsxs)(f.z,{size:"icon",variant:"outline",className:"h-6 w-6 opacity-0 transition-opacity group-hover:opacity-100",children:[r.jsx(h.Z,{className:"h-3 w-3"}),r.jsx("span",{className:"sr-only",children:"Copy Interview ID"})]})]})})}),r.jsx(d.aY,{className:"p-6 text-sm",children:(0,r.jsxs)("div",{className:"grid gap-3",children:[r.jsx("div",{className:"font-semibold",children:"Interview Details"}),(0,r.jsxs)("ul",{className:"grid gap-3",children:[(0,r.jsxs)("li",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-muted-foreground",children:"Interviewer"}),r.jsx("span",{children:e})]}),(0,r.jsxs)("li",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-muted-foreground",children:"Skill"}),r.jsx("span",{children:s})]}),(0,r.jsxs)("li",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-muted-foreground",children:"Rating"}),r.jsx("span",{children:i})]}),n&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("li",{className:"font-semibold mt-4",children:"Comments"}),r.jsx("li",{className:"flex items-center justify-between",children:r.jsx("span",{className:"text-muted-foreground",children:n})})]})]})]})}),r.jsx(d.eW,{className:"flex flex-row items-center border-t bg-muted/50 px-6 py-3",children:(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Updated"," ",r.jsx("time",{dateTime:a.toISOString(),children:new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})})]})})]});g.propTypes={interviewer:x().string.isRequired,interviewee:x().string.isRequired,skill:x().string.isRequired,interviewDate:x().instanceOf(Date).isRequired,rating:x().oneOfType([x().string,x().number]),comments:x().string};var j=s(72787),b=s(38443);let v=function(){return r.jsx("div",{children:r.jsx(d.Zb,{className:"",children:(0,r.jsxs)(d.Ol,{className:"pb-3",children:[r.jsx(d.ll,{children:"Project Name"}),r.jsx(d.SZ,{className:"",children:(0,r.jsxs)("div",{className:"mt-4 grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8 lg:grid-cols-4 xl:grid-cols-4",children:[(0,r.jsxs)("div",{className:"grid auto-rows-max items-start gap-4 md:gap-8 lg:col-span-3 border-r-2 pr-6 border-gray-300",children:[j.businessProjectDetailCard.description,r.jsx("div",{className:" ml-4",children:(0,r.jsxs)("ul",{className:"flex flex-wrap gap-4",children:[(0,r.jsxs)("li",{className:"min-w-[45%] ",children:[(0,r.jsxs)("span",{className:"text-gray-700 font-semibold ",children:["Email-"," "]})," ",j.businessProjectDetailCard.email]}),(0,r.jsxs)("li",{className:"min-w-[45%] ",children:[(0,r.jsxs)("span",{className:"text-gray-700 font-semibold ",children:["Staus-"," "]})," ",j.businessProjectDetailCard.status]}),(0,r.jsxs)("li",{className:"min-w-[45%] ",children:[(0,r.jsxs)("span",{className:"text-gray-700 font-semibold ",children:["Start Date-"," "]})," ",j.businessProjectDetailCard.startDate]}),(0,r.jsxs)("li",{className:"min-w-[45%] ",children:[(0,r.jsxs)("span",{className:"text-gray-700 font-semibold ",children:["End date-"," "]})," ",j.businessProjectDetailCard.endDate]})]})})]}),(0,r.jsxs)("div",{className:" ",children:[(0,r.jsxs)("div",{className:"my-4",children:[r.jsx("h4",{className:"text-center scroll-m-20 text-xl font-semibold tracking-tight transition-colors first:mt-0",children:"Project Domains"}),r.jsx("div",{className:"flex flex-wrap gap-x-4 gap-y-2 my-2",children:j.businessprojectCardDomains.map((e,t)=>r.jsx(b.C,{children:e},t))})]}),(0,r.jsxs)("div",{className:"pt-4",children:[r.jsx("h4",{className:"text-center scroll-m-20 text-xl font-semibold tracking-tight transition-colors first:mt-0",children:"Skills"}),r.jsx("div",{className:"flex flex-wrap gap-x-4 gap-y-2 my-2",children:j.businessprojectCardSkills.map((e,t)=>r.jsx(b.C,{children:e},t))})]})]})]})})]})})})};var y=s(51223);function w({className:e,...t}){return(0,r.jsxs)(d.Zb,{className:(0,y.cn)("w-[350px]",e),...t,children:[(0,r.jsxs)(d.Ol,{children:[(0,r.jsxs)(d.ll,{children:[j.businessprojectProfileCard.heading,"(2)"]}),r.jsx(d.SZ,{className:"text-gray-600",children:j.businessprojectProfileCard.description})]}),r.jsx(d.aY,{className:"grid gap-4",children:r.jsx("div",{children:(0,r.jsxs)("ul",{className:"flex flex-wrap gap-2",children:[(0,r.jsxs)("li",{className:"min-w-[45%] ",children:[r.jsx("span",{className:"text-gray-700 font-semibold ",children:"Email- "})," ",j.businessprojectProfileCard.email]}),(0,r.jsxs)("li",{className:"min-w-[45%] ",children:[r.jsx("span",{className:"text-gray-700 font-semibold ",children:"Staus- "})," ",j.businessprojectProfileCard.status]}),(0,r.jsxs)("li",{className:"min-w-[45%] ",children:[r.jsx("span",{className:"text-gray-700 font-semibold ",children:"Start Date- "})," ",j.businessprojectProfileCard.startDate]}),(0,r.jsxs)("li",{className:"min-w-[45%] ",children:[r.jsx("span",{className:"text-gray-700 font-semibold ",children:"End date- "})," ",j.businessprojectProfileCard.endDate]})]})})}),r.jsx(d.eW,{children:r.jsx(f.z,{className:"w-full ",children:"View Bids"})})]})}var k=s(92166),N=s(40588);function D(){let{project_id:e}=(0,m.useParams)(),t=[{href:"#",icon:r.jsx(a.Z,{className:"h-4 w-4 transition-all group-hover:scale-110"}),label:"Dehix"},{href:"#",icon:r.jsx(i.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"#",icon:r.jsx(n.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:r.jsx(o.Z,{className:"h-5 w-5"}),label:"Customers"},{href:"#",icon:r.jsx(l.Z,{className:"h-5 w-5"}),label:"Analytics"}],s=[{href:"/settings/personal-info",icon:r.jsx(c.Z,{className:"h-5 w-5"}),label:"Settings"}];return(0,r.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[r.jsx(k.Z,{menuItemsTop:t,menuItemsBottom:s,active:"Projects"}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-4 sm:py-4 sm:pl-14 mb-8",children:[r.jsx(N.Z,{menuItemsTop:t,menuItemsBottom:s,activeMenu:"Projects",breadcrumbItems:[{label:"Business",link:"/dashboard/business"},{label:"Project",link:"/dashboard/business"},{label:e,link:"#"}]}),(0,r.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8 lg:grid-cols-3 xl:grid-cols-3",children:[(0,r.jsxs)("div",{className:"grid auto-rows-max items-start gap-4 md:gap-8 lg:col-span-2",children:[r.jsx("div",{className:"",children:r.jsx(v,{})}),r.jsx(p.Separator,{className:"my-1"}),r.jsx("h2",{className:"scroll-m-20 text-3xl font-semibold tracking-tight transition-colors first:mt-0",children:"Profiles"}),(0,r.jsxs)("div",{className:"flex gap-4 overflow-x-scroll no-scrollbar pb-8",children:[r.jsx(w,{className:"min-w-[55%] "}),r.jsx(w,{className:"min-w-[55%] "})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx(d.ll,{className:"group flex items-center gap-2 text-2xl",children:"Interviews"}),r.jsx(g,{interviewer:j.freelancersampleInterview.interviewer,interviewee:j.freelancersampleInterview.interviewee,skill:j.freelancersampleInterview.skill,interviewDate:new Date(j.freelancersampleInterview.interviewDate),rating:j.freelancersampleInterview.rating,comments:j.freelancersampleInterview.comments}),r.jsx(g,{interviewer:j.freelancersampleInterview.interviewer,interviewee:j.freelancersampleInterview.interviewee,skill:j.freelancersampleInterview.skill,interviewDate:new Date(j.freelancersampleInterview.interviewDate),rating:j.freelancersampleInterview.rating,comments:j.freelancersampleInterview.comments})]})]})]})]})}},78062:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Separator:()=>o});var r=s(10326),a=s(17577),i=s(90220),n=s(51223);let o=a.forwardRef(({className:e,orientation:t="horizontal",decorative:s=!0,...a},o)=>r.jsx(i.f,{ref:o,decorative:s,orientation:t,className:(0,n.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...a}));o.displayName=i.f.displayName},22866:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>o});var r=s(68570);let a=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\business\Projects\page.tsx`),{__esModule:i,$$typeof:n}=a;a.default;let o=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\business\Projects\page.tsx#default`)},90220:(e,t,s)=>{"use strict";s.d(t,{f:()=>c});var r=s(17577),a=s(77335),i=s(10326),n="horizontal",o=["horizontal","vertical"],l=r.forwardRef((e,t)=>{let{decorative:s,orientation:r=n,...l}=e,c=o.includes(r)?r:n;return(0,i.jsx)(a.WV.div,{"data-orientation":c,...s?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...l,ref:t})});l.displayName="Separator";var c=l},72787:e=>{"use strict";e.exports=JSON.parse('{"freelancerEarnings":1000,"freelancersampleInterview":{"interviewer":"John Doe","interviewee":"Jane Smith","skill":"React Development","interviewDate":"2023-11-23T10:30:00Z","rating":4.5,"comments":"Great communication skills and technical expertise."},"dashboardactiveProject":{"heading":"Active Project","content":"+11 Current active projects"},"dashboardpendingProject":{"title":"Pending projects","itemCounts":{"total":15,"low":5,"medium":5,"high":5}},"dashboardBusinessCompleteProject":"+10% from last month","dashboardBusinessPendingProject":"+2 new projects this week","dashboardtotalRevenue":{"heading":"Total Revenue","content":"$45,231.89 +20.1% from last month"},"dashboardoracleWork":{"heading":"Oracle Work","content":"+11 projects"},"dashboardorderTable":{"customerName":"Jack smith","customerEmail":"<EMAIL>","customerType":"Sale","customerStatus":"Fulfilled","customerDate":"2023-06-23","customerAmount":"$250.00"},"dashboardorderDate":"Date: November 23, 2023","dashboardorderShippingAddress":{"name":"Liam Johnson","address":"1234 Main St.","state":"Anytown, CA 12345"},"dashboardorderCustomerInfo":{"customer":"Liam Johnson","email":"<EMAIL>","phone":"****** 567 890"},"dashboardorderCard":{"card":"Visa","cardNumber":"**** **** **** 4532"},"dashboardorderUpdateDate":"November 23, 2023","marketfreelancerJob":{"heading":"Arya.ai Data Scientist","content":"Arya is an autonomous AI operating platform for banks, insurers, and financial service providers that simplifies buildout and manages the...","skills":["Generative AI","Python","NLP","PyTorch","Transformers"],"location":"Mumbai","founded":"2013","employees":"10-50 employees"},"dashboardfreelancercurrentInterview":[{"reference":"Jane Smith","skill":"HTML/CSS","interviewDate":"2023-11-23T10:30:00Z","rating":9,"comments":"Great communication skills and technical expertise.","status":"Completed","description":"This interview focused on assessing proficiency in HTML/CSS and evaluating communication skills.","contact":"<EMAIL>"},{"reference":"Jane Smith","domain":"DevOps","interviewDate":"2023-11-23T10:30:00Z","rating":9,"comments":"Great communication skills and technical expertise.","status":"Pending","description":"This interview was scheduled to discuss the candidate\'s experience and skills in DevOps.","contact":"<EMAIL>"}],"dashboardfreelancerhistoryUserEmail":"<EMAIL>","dashboardfreelancerhistoryInterview":[{"reference":"Jane Smith","skill":"HTML/CSS","interviewDate":"2023-11-23T10:30:00Z","rating":9,"comments":"Great communication skills and technical expertise.","status":"Completed","description":"This interview focused on assessing proficiency in HTML/CSS and evaluating communication skills.","contact":"<EMAIL>"},{"reference":"Jane Smith","domain":"DevOps","interviewDate":"2023-11-23T10:30:00Z","rating":9,"comments":"Great communication skills and technical expertise.","status":"Completed","description":"This interview was scheduled to discuss the candidate\'s experience and skills in DevOps.","contact":"<EMAIL>"}],"dashboardFreelancerOracleBusiness":[{"firstName":"John","lastName":"Smith","email":"<EMAIL>","phone":"+*********0","companyName":"Tech Innovators Inc.","companySize":"500-1000 employees","referenceEmail":"<EMAIL>","websiteLink":"https://www.techinnovators.com","linkedInLink":"https://www.linkedin.com/in/johnsmith","githubLink":"https://github.com/johnsmith","comments":"","status":"pending"},{"firstName":"Alice","lastName":"Johnson","email":"<EMAIL>","phone":"+0*********","companyName":"Creative Solutions Ltd.","companySize":"100-500 employees","referenceEmail":"<EMAIL>","websiteLink":"https://www.creativesolutions.com","linkedInLink":"https://www.linkedin.com/in/alicejohnson","githubLink":"https://github.com/alicejohnson","comments":"","status":"pending"},{"firstName":"Robert","lastName":"Brown","email":"<EMAIL>","phone":"+1122334455","companyName":"Global Enterprises","companySize":"1000-5000 employees","referenceEmail":"<EMAIL>","websiteLink":"https://www.globalenterprises.com","linkedInLink":"https://www.linkedin.com/in/robertbrown","githubLink":"https://github.com/robertbrown","comments":"","status":"pending"}],"dashboardFreelancerOracleEducation":[{"type":"Bachelor\'s Degree","instituteName":"University of Example","location":"Example City, Example Country","startFrom":"2018-09-01","endTo":"2022-06-15","grade":"A","referencePersonName":"Dr. John Doe","degreeNumber":"*********","comments":"","status":"pending"},{"type":"Master\'s Degree","instituteName":"University of Example","location":"Example City, Example Country","startFrom":"2022-09-01","endTo":"2024-06-15","grade":"A+","referencePersonName":"Dr. Jane Smith","degreeNumber":"*********","comments":"","status":"pending"},{"type":"Ph.D.","instituteName":"University of Example","location":"Example City, Example Country","startFrom":"2024-09-01","endTo":"2028-06-15","grade":"A+","referencePersonName":"Dr. Emily Johnson","degreeNumber":"456789123","comments":"","status":"pending"}],"dashboardFreelancerOracleProject":[{"projectName":"Task Tracker","description":"A web application for managing and tracking daily tasks and projects.","githubLink":"https://github.com/yourusername/TaskTracker","startFrom":"2023-05-01","endTo":"2023-10-15","reference":"Mr. Alex Johnson, Senior Developer","techUsed":["Vue.js","JavaScript","Firebase","CSS"],"comments":"","status":"pending"},{"projectName":"Inventory Management System","description":"A system for managing inventory in warehouses.","githubLink":"https://github.com/yourusername/InventoryManagementSystem","startFrom":"2022-01-01","endTo":"2022-06-01","reference":"Ms. Maria Gonzalez, Project Manager","techUsed":["React","Node.js","MongoDB","Sass"],"comments":"","status":"pending"},{"projectName":"E-commerce Platform","description":"An online platform for buying and selling products.","githubLink":"https://github.com/yourusername/EcommercePlatform","startFrom":"2021-02-01","endTo":"2021-08-01","reference":"Mr. John Smith, Lead Developer","techUsed":["Angular","TypeScript","Firebase","Bootstrap"],"comments":"","status":"pending"}],"dashboardFreelancerOracleExperience":[{"jobTitle":"Frontend Developer","workDescription":"Responsible for developing user-friendly web applications using React and TypeScript.","startFrom":"2022-01-15","endTo":"2023-07-01","referencePersonName":"Jane Doe","referencePersonEmail":"<EMAIL>","githubRepoLink":"https://github.com/janedoe/project-repo","comments":"","status":"pending"},{"jobTitle":"Backend Developer","workDescription":"Developed and maintained server-side logic using Node.js and Express.","startFrom":"2021-02-01","endTo":"2021-12-31","referencePersonName":"John Smith","referencePersonEmail":"<EMAIL>","githubRepoLink":"https://github.com/johnsmith/backend-project","comments":"","status":"pending"},{"jobTitle":"Full Stack Developer","workDescription":"Worked on both frontend and backend development using MERN stack.","startFrom":"2020-03-01","endTo":"2021-01-31","referencePersonName":"Alice Johnson","referencePersonEmail":"<EMAIL>","githubRepoLink":"https://github.com/alicejohnson/fullstack-project","comments":"","status":"pending"}],"businessProjectDetailCard":{"description":"Welcome to our project! This initiative aims to [briefly describe the main goal or purpose of the project ]. Through thisproject, we intend to [mention key objectives or outcomes]. Our team is dedicated to [highlight any unique approaches ormethodologies]. We believe this project will [state the expected impact or benefits ]. Feel free to replace the placeholders with specific details about your project. \\nIf you need further customization or additional sections, let me know!  \\nWelcome to our project! This initiative aims to [briefly describe the main goal or purpose of the project ]. Through this project, we intend to [mention key objectives or outcomes ]. Our team is dedicated to [highlight any unique approaches or methodologies ]. We believe this project will [state the expected impact or benefits ]. Feel free to replace the placeholders with specific details about your project. If you need further customization or additional sections, let me know! ","email":"<EMAIL>","status":"Current","startDate":"22/22/2222","endDate":"24/22/2222"},"businessprojectCardDomains":["Frontend","Backend","Graphic Designer","3D artist","Fullstack"],"businessprojectCardSkills":["React","Mongo","Golang","Java","Html","Css"],"businessprojectProfileCard":{"heading":"Frontend Developer","description":"Your requirement is of 2 freelancers for this profile, 6 people have appplied via bid and 1 person is selected till now.","email":"<EMAIL>","status":"Current","startDate":"22/22/2222","endDate":"24/22/2222"},"marketFreelancerProject":{"project_name":"AI Development Project","project_id":"#12345","location":"Delhi, India","description":"We\'re looking for an experienced web developer who\'s really good at making interactive forms. The perfect candidate should know a lot about web development and have a bunch of cool forms they\'ve made before. Your main job will be making forms that people can use easily and that look nice.","email":"<EMAIL>","company_name":"Tech Innovators Inc.","start":"2023-01-01T00:00:00.000Z","end":"2023-12-31T00:00:00.000Z","skills_required":["JavaScript","React","Python","Machine Learning"],"experience":"2+ years of experience in AI development.","role":"Lead Developer","project_type":"Full-time"},"marketFreelancerProjectOtherBits":[{"username":"Alex004","bitAmount":100},{"username":"User2","bitAmount":150},{"username":"alen789","bitAmount":200}],"projectRejectedCard":{"companyName":"ABC Corporation","role":"Software Engineer","projectType":"Web Development","description":"This is a sample project description","skillsRequired":["JavaScript","React","Node.js"],"start":"2023-02-15","email":"<EMAIL>","experience":"5+ years"},"projectCurrentCard":{"companyName":"ABC Corporation","role":"Software Engineer","projectType":"Web Development","description":"This is a sample project description for a current ongoing project.","skillsRequired":["JavaScript","React","Node.js"],"start":"2023-02-15","end":"current","email":"<EMAIL>","experience":"5+ years"},"projectCompleteCard":{"companyName":"ABC Corporation","role":"Software Engineer","projectType":"Web Development","description":"This is a sample project description for a current ongoing project.","skillsRequired":["JavaScript","React","Node.js"],"start":"2023-02-15","end":"2023-09-24","email":"<EMAIL>","experience":"5+ years"},"projectUnderVerificatinCard":{"companyName":"ABC Corporation","role":"Software Engineer","projectType":"Web Development","description":"This is a sample project description for a current ongoing project.","skillsRequired":["JavaScript","React","Node.js"],"start":"2023-02-15","email":"<EMAIL>","experience":"5+ years"}}')}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588],()=>s(99608));module.exports=r})();