(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8221],{97955:function(e,t,s){Promise.resolve().then(s.bind(s,48823))},94499:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(33480).Z)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},87055:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(33480).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},89832:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(33480).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},48823:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return z}});var r=s(57437),l=s(404),a=s(29406),i=s(2265),n=s(89733),c=s(54662),d=s(97694),m=s(64797),o=s(62688),x=s(746),h=s(15922),u=s(87055),f=s(94499);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let j=(0,s(33480).Z)("UserRound",[["circle",{cx:"12",cy:"8",r:"5",key:"1hypcn"}],["path",{d:"M20 21a8 8 0 0 0-16 0",key:"rfgkzh"}]]);var p=s(89832),v=s(47390),y=s(31014),N=s(39343),g=s(59772),b=s(48185),k=s(79055),w=s(93363),C=s(89736),S=s(4919),_=s(78068);let Z=g.z.object({type:g.z.enum(["Approved","Denied"],{required_error:"You need to select a type."}),comment:g.z.string().optional()});var E=e=>{let{_id:t,jobTitle:s,workDescription:l,startFrom:a,company:c,endTo:m,referencePersonName:o,referencePersonContact:x,githubRepoLink:g,comments:E,status:M,onStatusUpdate:z,onCommentUpdate:P}=e,[D,A]=(0,i.useState)(M),I=(0,N.cI)({resolver:(0,y.F)(Z)}),V=I.watch("type");async function F(e){try{await h.b.put("/verification/".concat(t,"/oracle?doc_type=experience"),{comments:e.comment,verification_status:e.type})}catch(e){(0,_.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}A(e.type),z(e.type),P(e.comment||"")}return(0,i.useEffect)(()=>{A(M)},[M]),(0,r.jsxs)(b.Zb,{className:"max-w-full mx-auto md:min-w-[30vw]",children:[(0,r.jsxs)(b.Ol,{children:[(0,r.jsxs)(b.ll,{className:"flex justify-between",children:[(0,r.jsx)("span",{children:s}),g&&(0,r.jsx)("a",{href:g,target:"_blank",rel:"noopener noreferrer",className:"text-sm text-white underline ml-auto",children:(0,r.jsx)(u.Z,{})})]}),(0,r.jsxs)(b.SZ,{className:"text-justify text-gray-600",children:["Pending"===D?(0,r.jsx)(k.C,{className:"bg-warning-foreground text-white my-2",children:"Pending"}):"Approved"===D?(0,r.jsx)(k.C,{className:"bg-success text-white my-2",children:"Approved"}):(0,r.jsx)(k.C,{className:"bg-red-500 text-white my-2",children:"Denied"}),(0,r.jsx)("br",{}),l]})]}),(0,r.jsx)(b.aY,{children:(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("p",{className:"mt-4 mb-3 text-m text-gray-600 flex items-center",children:(0,r.jsxs)("span",{className:"flex",children:[(0,r.jsx)(f.Z,{className:"mr-2"}),c]})}),(0,r.jsxs)(C.u,{children:[(0,r.jsx)(C.aJ,{asChild:!0,children:(0,r.jsxs)("p",{className:"text-sm text-gray-600 flex items-center",children:[(0,r.jsx)(j,{className:"mr-2"}),o]})}),(0,r.jsx)(C._v,{side:"bottom",children:o})]}),(0,r.jsxs)(C.u,{children:[(0,r.jsx)(C.aJ,{asChild:!0,children:(0,r.jsxs)("p",{className:"text-sm text-gray-600 flex items-center mt-2",children:[(0,r.jsx)(p.Z,{className:"mr-2"}),x]})}),(0,r.jsx)(C._v,{side:"bottom",children:x})]})]}),E&&(0,r.jsxs)("p",{className:"mt-2 flex items-center text-gray-500 border p-3 rounded",children:[(0,r.jsx)(v.Z,{className:"mr-2"}),E]})]})}),(0,r.jsxs)(b.eW,{className:"flex flex-col items-center",children:[(0,r.jsxs)("div",{className:"flex gap-4 text-gray-500",children:[new Date(a).toLocaleDateString()," -"," ","current"!==m?new Date(m).toLocaleDateString():"Current"]}),"Pending"===D&&(0,r.jsx)(w.l0,{...I,children:(0,r.jsxs)("form",{onSubmit:I.handleSubmit(F),className:"w-full space-y-6 mt-6",children:[(0,r.jsx)(w.Wi,{control:I.control,name:"type",render:e=>{let{field:t}=e;return(0,r.jsxs)(w.xJ,{className:"space-y-3",children:[(0,r.jsx)(w.lX,{children:"Choose Verification Status:"}),(0,r.jsx)(w.NI,{children:(0,r.jsxs)(d.E,{onValueChange:t.onChange,defaultValue:t.value,className:"flex flex-col space-y-1",children:[(0,r.jsxs)(w.xJ,{className:"flex items-center space-x-3",children:[(0,r.jsx)(w.NI,{children:(0,r.jsx)(d.m,{value:"Approved"})}),(0,r.jsx)(w.lX,{className:"font-normal",children:"Approved"})]}),(0,r.jsxs)(w.xJ,{className:"flex items-center space-x-3",children:[(0,r.jsx)(w.NI,{children:(0,r.jsx)(d.m,{value:"Denied"})}),(0,r.jsx)(w.lX,{className:"font-normal",children:"Denied"})]})]})}),(0,r.jsx)(w.zG,{})]})}}),(0,r.jsx)(w.Wi,{control:I.control,name:"comment",render:e=>{let{field:t}=e;return(0,r.jsxs)(w.xJ,{children:[(0,r.jsx)(w.lX,{children:"Comments:"}),(0,r.jsx)(w.NI,{children:(0,r.jsx)(S.g,{placeholder:"Enter comments:",...t})}),(0,r.jsx)(w.zG,{})]})}}),(0,r.jsx)(n.z,{type:"submit",className:"w-full",disabled:!V||I.formState.isSubmitting,children:"Submit"})]})})]})]})},M=s(97540);function z(){let[e,t]=(0,i.useState)([]),[s,u]=(0,i.useState)("all"),[f,j]=(0,i.useState)(!1),p=e=>{u(e),j(!1)},v=e.filter(e=>"all"===s||e.verificationStatus===s||"current"===s&&e.verificationStatus===M.sB.PENDING),y=(0,i.useCallback)(async()=>{try{let e=(await h.b.get("/verification/oracle?doc_type=experience")).data.data.flatMap(e=>{var t;return(null===(t=e.result)||void 0===t?void 0:t.projects)?Object.values(e.result.projects).map(t=>({...t,verifier_id:e.verifier_id,verifier_username:e.verifier_username})):[]});t(e)}catch(e){(0,_.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.log(e,"error in getting verification data")}},[]);(0,i.useEffect)(()=>{y()},[y]);let N=(s,r)=>{let l=[...e];l[s].verificationStatus=r,t(l)},g=(s,r)=>{let l=[...e];l[s].comments=r,t(l)};return(0,r.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,r.jsx)(m.Z,{menuItemsTop:x.y,menuItemsBottom:x.$,active:"Experience Verification"}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,r.jsx)(o.Z,{menuItemsTop:x.y,menuItemsBottom:x.$,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Oracle",link:"#"},{label:"Experience Verification",link:"#"}]}),(0,r.jsx)("div",{className:"mb-8 ml-4 flex justify-between mt-8 md:mt-4 items-center",children:(0,r.jsxs)("div",{className:"mb-8 ",children:[(0,r.jsxs)("div",{className:"mb-8 ml-4 flex justify-between mt-8 md:mt-4 items-center",children:[(0,r.jsxs)("div",{className:"mb-8 ",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Experience Verification"}),(0,r.jsx)("p",{className:"text-gray-400 mt-2",children:"Stay updated on your work experience verification status. Check back regularly for any new updates or requirements."})]}),(0,r.jsx)(n.z,{variant:"outline",size:"icon",className:"mr-8 mb-12",onClick:()=>j(!0),children:(0,r.jsx)(l.Z,{className:"h-4 w-4"})})]}),(0,r.jsx)(c.Vq,{open:f,onOpenChange:j,children:(0,r.jsxs)(c.cZ,{children:[(0,r.jsx)(c.fK,{children:(0,r.jsx)(c.$N,{children:"Filter Experience Status"})}),(0,r.jsxs)(d.E,{defaultValue:"all",value:s,onValueChange:e=>p(e),className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(d.m,{value:"all",id:"filter-all"}),(0,r.jsx)("label",{htmlFor:"filter-all",children:"All"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(d.m,{value:"current",id:"filter-current"}),(0,r.jsx)("label",{htmlFor:"filter-current",children:"Pending"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(d.m,{value:"verified",id:"filter-verified"}),(0,r.jsx)("label",{htmlFor:"filter-verified",children:"Verified"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(d.m,{value:"rejected",id:"filter-rejected"}),(0,r.jsx)("label",{htmlFor:"filter-rejected",children:"Rejected"})]})]}),(0,r.jsx)(c.cN,{children:(0,r.jsx)(n.z,{type:"button",onClick:()=>j(!1),children:"Close"})})]})}),(0,r.jsxs)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8    grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3",children:[v.map((e,t)=>(0,r.jsx)(E,{_id:e._id,jobTitle:e.jobTitle,workDescription:e.workDescription,company:e.company,startFrom:e.workFrom,endTo:e.workTo,referencePersonName:e.referencePersonName,referencePersonContact:e.referencePersonContact,githubRepoLink:e.githubRepoLink,comments:e.comments,status:e.verificationStatus,onStatusUpdate:e=>N(t,e),onCommentUpdate:e=>g(t,e)},t)),0==e.length?(0,r.jsxs)("div",{className:"text-center w-[90vw] px-auto mt-20 py-10",children:[(0,r.jsx)(a.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No Work Experience verification for you now."})]}):null]})]})})]})]})}}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,1374,2455,9726,2688,2480,2971,7023,1744],function(){return e(e.s=97955)}),_N_E=e.O()}]);