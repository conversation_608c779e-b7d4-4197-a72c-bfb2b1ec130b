(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8168],{55184:function(e,a,t){Promise.resolve().then(t.bind(t,81466))},5891:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(33480).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},76035:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(33480).Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},43193:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(33480).Z)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]])},49100:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(33480).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},27218:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(33480).Z)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},4086:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(33480).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},47390:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},89832:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(33480).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},24258:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(33480).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},36141:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(33480).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},33907:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(33480).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},73347:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(33480).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},33149:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(33480).Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]])},40064:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(33480).Z)("TabletSmartphone",[["rect",{width:"10",height:"14",x:"3",y:"8",rx:"2",key:"1vrsiq"}],["path",{d:"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4",key:"1j4zmg"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},10883:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(33480).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},81466:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return Z}});var s=t(57437),l=t(27720),r=t(64797),n=t(52922),c=t(15699),i=t(9753),h=t(66227),o=t(2265),d=t(16463),x=t(4086),u=t(89832),f=t(27218);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,t(33480).Z)("Earth",[["path",{d:"M21.54 15H17a2 2 0 0 0-2 2v4.54",key:"1djwo0"}],["path",{d:"M7 3.34V5a3 3 0 0 0 3 3v0a2 2 0 0 1 2 2v0c0 1.1.9 2 2 2v0a2 2 0 0 0 2-2v0c0-1.1.9-2 2-2h3.17",key:"1fi5u6"}],["path",{d:"M11 21.95V18a2 2 0 0 0-2-2v0a2 2 0 0 1-2-2v-1a2 2 0 0 0-2-2H2.05",key:"xsiumc"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var p=t(48185),j=t(80420),v=t(29973),y=t(15922),k=t(89736),N=t(78068),b=()=>{let[e,a]=(0,o.useState)(null),{business_id:t}=(0,d.useParams)();return((0,o.useEffect)(()=>{t&&(async()=>{try{let e=await y.b.get("/business/".concat(t));a(e.data)}catch(e){console.error("Error fetching profile data:",e),(0,N.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}})()},[t]),e)?(0,s.jsxs)(p.Zb,{className:"w-full h-full max-w-4xl mx-auto p-6",children:[(0,s.jsxs)(p.Ol,{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{children:(0,s.jsxs)(j.Avatar,{children:[(0,s.jsx)(j.AvatarImage,{src:e.profileImage||"/default-image.png",alt:e.companyName}),(0,s.jsxs)(j.AvatarFallback,{children:[e.firstName[0],e.lastName[0]]})]})}),(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-1",children:[(0,s.jsx)(p.ll,{className:"text-xl font-bold",children:e.companyName}),(0,s.jsx)("p",{className:"text-sm font-bold",children:e.position}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"".concat(e.firstName," ").concat(e.lastName)})]})]}),(0,s.jsxs)(p.aY,{className:"flex justify-around items-center pb-4 mb-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-xl font-semibold",children:e.connects}),(0,s.jsx)("p",{className:"text-xs",children:"Connects"})]}),(0,s.jsx)(v.Separator,{orientation:"vertical",className:"h-6 bg-gray-400"}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-xl font-semibold",children:e.ProjectList.length}),(0,s.jsx)("p",{className:"text-xs",children:"Projects"})]}),(0,s.jsx)(v.Separator,{orientation:"vertical",className:"h-6 bg-gray-400"}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-xl font-semibold",children:e.companySize}),(0,s.jsx)("p",{className:"text-xs",children:"Company Size"})]})]}),(0,s.jsxs)(p.aY,{className:"flex flex-col items-center space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-center",children:"Contact Information"}),(0,s.jsxs)("div",{className:"flex justify-between w-full",children:[(0,s.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,s.jsxs)(k.u,{children:[(0,s.jsx)(k.aJ,{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(x.Z,{className:"mr-1 h-5"}),(0,s.jsx)("a",{href:"mailto:".concat(e.email),className:"text-blue-600 ml-1",children:e.email})]})}),(0,s.jsx)(k._v,{side:"left",children:"Email address for contact"})]}),(0,s.jsxs)(k.u,{children:[(0,s.jsx)(k.aJ,{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(u.Z,{className:"mr-1 h-5"}),e.phone]})}),(0,s.jsx)(k._v,{side:"left",children:"Contact via phone"})]})]}),(0,s.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,s.jsxs)(k.u,{children:[(0,s.jsx)(k.aJ,{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(f.Z,{className:"mr-1 h-5"}),(0,s.jsx)("a",{href:e.linkedin,className:"text-blue-600 ml-1",children:e.linkedin})]})}),(0,s.jsx)(k._v,{side:"left",children:"View LinkedIn profile"})]}),(0,s.jsxs)(k.u,{children:[(0,s.jsx)(k.aJ,{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(m,{className:"mr-1 h-5"}),(0,s.jsx)("a",{href:e.personalWebsite,className:"text-blue-600 ml-1",children:e.personalWebsite})]})}),(0,s.jsx)(k._v,{side:"left",children:"Visit website"})]})]})]})]})]}):(0,s.jsx)("p",{children:"Loading..."})};function Z(){return(0,s.jsxs)("div",{className:"flex min-h-screen w-full flex-col ",children:[(0,s.jsx)(r.Z,{menuItemsTop:h.yn,menuItemsBottom:h.$C,active:"Market"}),(0,s.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,s.jsxs)("header",{className:"sticky top-0 z-30 flex h-14 items-center gap-4 border-b bg-background px-4  sm:border-0  sm:px-6",children:[(0,s.jsx)(c.Z,{menuItemsTop:h.yn,menuItemsBottom:h.$C,active:"Market"}),(0,s.jsx)(n.Z,{items:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Freelancer Market",link:"#"}]}),(0,s.jsx)("div",{className:"relative ml-auto flex-1 md:grow-0",children:(0,s.jsx)(l.o,{className:"w-full md:w-[200px] lg:w-[336px]"})}),(0,s.jsx)(i.Z,{})]}),(0,s.jsx)("main",{className:"",children:(0,s.jsx)(b,{})})]})]})}},27720:function(e,a,t){"use strict";t.d(a,{o:function(){return n}});var s=t(57437),l=t(54817),r=t(77209);function n(e){let{placeholder:a="Search...",className:t=""}=e;return(0,s.jsxs)("div",{className:"relative ".concat(t),children:[(0,s.jsx)(l.Z,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(r.I,{type:"search",placeholder:a,className:"w-full rounded-lg bg-background pl-8 md:w-[200px] lg:w-[336px]"})]})}},29973:function(e,a,t){"use strict";t.r(a),t.d(a,{Separator:function(){return c}});var s=t(57437),l=t(2265),r=t(48484),n=t(49354);let c=l.forwardRef((e,a)=>{let{className:t,orientation:l="horizontal",decorative:c=!0,...i}=e;return(0,s.jsx)(r.f,{ref:a,decorative:c,orientation:l,className:(0,n.cn)("shrink-0 bg-border","horizontal"===l?"h-[1px] w-full":"h-full w-[1px]",t),...i})});c.displayName=r.f.displayName},66227:function(e,a,t){"use strict";t.d(a,{$C:function(){return y},yL:function(){return k},yn:function(){return v}});var s=t(57437),l=t(11005),r=t(33149),n=t(76035),c=t(49100),i=t(40064),h=t(43193),o=t(36141),d=t(33907),x=t(47390),u=t(73347),f=t(24258),m=t(5891),p=t(10883),j=t(66648);let v=[{href:"#",icon:(0,s.jsx)(j.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,s.jsx)(l.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:(0,s.jsx)(r.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:(0,s.jsx)(n.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:(0,s.jsx)(c.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:(0,s.jsx)(i.Z,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:(0,s.jsx)(h.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:(0,s.jsx)(o.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:(0,s.jsx)(d.Z,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:(0,s.jsx)(x.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,s.jsx)(u.Z,{className:"h-5 w-5"}),label:"Notes"}],y=[{href:"/freelancer/settings/personal-info",icon:(0,s.jsx)(f.Z,{className:"h-5 w-5"}),label:"Settings"}];j.default,l.Z,u.Z,m.Z,p.Z;let k=[{href:"#",icon:(0,s.jsx)(j.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,s.jsx)(l.Z,{className:"h-5 w-5"}),label:"Home"}]},48484:function(e,a,t){"use strict";t.d(a,{f:function(){return h}});var s=t(2265),l=t(18676),r=t(57437),n="horizontal",c=["horizontal","vertical"],i=s.forwardRef((e,a)=>{let{decorative:t,orientation:s=n,...i}=e,h=c.includes(s)?s:n;return(0,r.jsx)(l.WV.div,{"data-orientation":h,...t?{role:"none"}:{"aria-orientation":"vertical"===h?h:void 0,role:"separator"},...i,ref:a})});i.displayName="Separator";var h=i}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,2455,9726,2971,7023,1744],function(){return e(e.s=55184)}),_N_E=e.O()}]);