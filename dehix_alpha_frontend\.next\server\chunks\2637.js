"use strict";exports.id=2637,exports.ids=[2637],exports.modules={6507:(t,e,a)=>{a.d(e,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(80851).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},71821:(t,e,a)=>{a.d(e,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(80851).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},36283:(t,e,a)=>{a.d(e,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(80851).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},40167:(t,e,a)=>{a.d(e,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(80851).Z)("Gavel",[["path",{d:"m14.5 12.5-8 8a2.119 2.119 0 1 1-3-3l8-8",key:"15492f"}],["path",{d:"m16 16 6-6",key:"vzrcl6"}],["path",{d:"m8 8 6-6",key:"18bi4p"}],["path",{d:"m9 7 8 8",key:"5jnvq1"}],["path",{d:"m21 11-8-8",key:"z4y7zo"}]])},15721:(t,e,a)=>{a.d(e,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(80851).Z)("LaptopMinimal",[["rect",{width:"18",height:"12",x:"3",y:"4",rx:"2",ry:"2",key:"1qhy41"}],["line",{x1:"2",x2:"22",y1:"20",y2:"20",key:"ni3hll"}]])},97685:(t,e,a)=>{a.d(e,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(80851).Z)("ListFilter",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M7 12h10",key:"b7w52i"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},88378:(t,e,a)=>{a.d(e,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(80851).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},21985:(t,e,a)=>{a.d(e,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(80851).Z)("Ticket",[["path",{d:"M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z",key:"qn84l0"}],["path",{d:"M13 5v2",key:"dyzc3o"}],["path",{d:"M13 17v2",key:"1ont0d"}],["path",{d:"M13 11v2",key:"1wjjxi"}]])},94909:(t,e,a)=>{a.d(e,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(80851).Z)("UsersRound",[["path",{d:"M18 21a8 8 0 0 0-16 0",key:"3ypg7q"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["path",{d:"M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3",key:"10s06x"}]])},16671:(t,e,a)=>{a.d(e,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(80851).Z)("Wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]])},19547:(t,e,a)=>{a.d(e,{y:()=>n});var r=a(99276);function n(t){return(0,r.L)(t,Date.now())}},67480:(t,e,a)=>{a.d(e,{i:()=>n});var r=a(71271);function n(t){let e=(0,r.Q)(t);return e.setHours(23,59,59,999),e}},88118:(t,e,a)=>{a.d(e,{Q:()=>c});var r=a(19547),n=a(71271);function i(t,e){let a=(0,n.Q)(t),r=(0,n.Q)(e),i=a.getTime()-r.getTime();return i<0?-1:i>0?1:i}var l=a(79740),s=a(33612),o=a(67480),h=a(53704),d=a(57885),u=a(61981),f=a(78349);function c(t,e){return function(t,e,a){var r,c,y,M;let m,p,k;let v=(0,u.j)(),D=a?.locale??v.locale??d._,x=i(t,e);if(isNaN(x))throw RangeError("Invalid time value");let Z=Object.assign({},a,{addSuffix:a?.addSuffix,comparison:x});x>0?(m=(0,n.Q)(e),p=(0,n.Q)(t)):(m=(0,n.Q)(t),p=(0,n.Q)(e));let H=(r=p,c=m,(M=void 0,t=>{let e=(M?Math[M]:Math.trunc)(t);return 0===e?0:e})((+(0,n.Q)(r)-+(0,n.Q)(c))/1e3)),Q=Math.round((H-((0,f.D)(p)-(0,f.D)(m))/1e3)/60);if(Q<2){if(a?.includeSeconds){if(H<5)return D.formatDistance("lessThanXSeconds",5,Z);if(H<10)return D.formatDistance("lessThanXSeconds",10,Z);if(H<20)return D.formatDistance("lessThanXSeconds",20,Z);if(H<40)return D.formatDistance("halfAMinute",0,Z);else if(H<60)return D.formatDistance("lessThanXMinutes",1,Z);else return D.formatDistance("xMinutes",1,Z)}return 0===Q?D.formatDistance("lessThanXMinutes",1,Z):D.formatDistance("xMinutes",Q,Z)}if(Q<45)return D.formatDistance("xMinutes",Q,Z);if(Q<90)return D.formatDistance("aboutXHours",1,Z);if(Q<l.H_)return D.formatDistance("aboutXHours",Math.round(Q/60),Z);if(Q<2520)return D.formatDistance("xDays",1,Z);if(Q<l.fH){let t=Math.round(Q/l.H_);return D.formatDistance("xDays",t,Z)}if(Q<2*l.fH)return k=Math.round(Q/l.fH),D.formatDistance("aboutXMonths",k,Z);if((k=function(t,e){let a;let r=(0,n.Q)(t),l=(0,n.Q)(e),d=i(r,l),u=Math.abs((0,s.T)(r,l));if(u<1)a=0;else{1===r.getMonth()&&r.getDate()>27&&r.setDate(30),r.setMonth(r.getMonth()-d*u);let e=i(r,l)===-d;(function(t){let e=(0,n.Q)(t);return+(0,o.i)(e)==+(0,h.V)(e)})((0,n.Q)(t))&&1===u&&1===i(t,l)&&(e=!1),a=d*(u-Number(e))}return 0===a?0:a}(p,m))<12){let t=Math.round(Q/l.fH);return D.formatDistance("xMonths",t,Z)}{let t=k%12,e=Math.trunc(k/12);return t<3?D.formatDistance("aboutXYears",e,Z):t<9?D.formatDistance("overXYears",e,Z):D.formatDistance("almostXYears",e+1,Z)}}(t,(0,r.y)(t),e)}}};