"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9726],{15699:function(e,t,s){var a=s(57437),r=s(2265),l=s(87138),n=s(60143),i=s(90564),o=s(80420),d=s(55936),c=s(89733);t.Z=e=>{let{menuItemsTop:t,menuItemsBottom:s,active:m,setActive:u=()=>null,conversations:x,setActiveConversation:f,activeConversation:h}=e,p=e=>{let{conversation:t}=e;if(!t||"object"!=typeof t||Array.isArray(t)||!f)return null;let s=t.name||"Unknown",r=(null==h?void 0:h.id)===t.id;return(0,a.jsxs)(o.<PERSON><PERSON>,{className:"w-full h-10 flex justify-start gap-2 items-center rounded-full \n    transition-all cursor-pointer \n    ".concat(r?"border-2 border-blue-500 bg-gray-200 dark:bg-gray-700":"border-transparent"),onClick:()=>f(t),children:[(0,a.jsxs)("div",{className:"w-10 h-10",children:[(0,a.jsx)(o.AvatarImage,{src:"",alt:s}),(0,a.jsx)(o.AvatarFallback,{className:"bg-gray-300 dark:bg-gray-600 text-black dark:text-white font-bold",children:s.split(" ").map(e=>e.charAt(0).toUpperCase()).join("").slice(0,2)})]}),(0,a.jsx)("span",{className:"text-black dark:text-white",children:s})]})};return(0,a.jsxs)(d.yo,{children:[(0,a.jsx)(d.aM,{asChild:!0,children:(0,a.jsxs)(c.z,{size:"icon",variant:"outline",className:"sm:hidden",children:[(0,a.jsx)(n.Z,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle Menu"})]})}),(0,a.jsxs)(d.ue,{side:"left",className:"flex flex-col justify-between sm:max-w-xs",children:[(0,a.jsxs)("nav",{className:"grid gap-6 text-lg font-medium",children:[t.map((e,t)=>(0,a.jsxs)(l.default,{href:e.href,onClick:()=>u(e.label),className:"flex items-center gap-4 px-2.5 ".concat("Dehix"===e.label?"group flex h-10 w-10 shrink-0 items-center justify-center gap-2 rounded-full bg-primary text-lg font-semibold text-primary-foreground md:text-base":e.label===m?"text-foreground":"text-muted-foreground hover:text-foreground"),children:[e.icon,"Dehix"!==e.label&&e.label]},t)),"Chats"===m&&f&&x&&x.map(e=>(0,a.jsx)(p,{conversation:e},e.id))]}),(0,a.jsx)("nav",{className:"grid gap-6 text-lg font-medium",children:s.map((e,t)=>(0,a.jsxs)(r.Fragment,{children:["Settings"===e.label&&(0,a.jsx)("div",{className:"flex items-center px-2.5 text-muted-foreground hover:text-foreground mb-1",children:(0,a.jsx)(i.T,{})}),(0,a.jsxs)(l.default,{href:e.href,onClick:()=>u(e.label),className:"flex items-center gap-4 px-2.5 ".concat("Dehix"===e.label?"group flex h-10 w-10 shrink-0 items-center justify-center gap-2 rounded-full bg-primary text-lg font-semibold text-primary-foreground md:text-base":e.label===m?"text-foreground":"text-muted-foreground hover:text-foreground"),children:[e.icon,"Dehix"!==e.label&&e.label]})]},t))})]})]})}},64797:function(e,t,s){var a=s(57437),r=s(87138),l=s(16463),n=s(11444),i=s(53541),o=s(90564),d=s(80420),c=s(89736),m=s(21413);t.Z=e=>{let{menuItemsTop:t,menuItemsBottom:s,active:u,setActive:x=()=>null,isKycCheck:f,conversations:h,setActiveConversation:p,activeConversation:v}=e,j=(0,l.usePathname)(),N=(0,n.v9)(e=>e.user),g=e=>j===e,b=e=>{var t;return!!g(e.href)||(null===(t=e.subItems)||void 0===t?void 0:t.some(e=>g(e.href)))},y=[...t];if(f&&(null==N?void 0:N.kycStatus)!=="ACTIVE"&&(null==N?void 0:N.kycStatus)!==void 0){var w;y.splice(3,0,{href:"/".concat(null==N?void 0:null===(w=N.type)||void 0===w?void 0:w.toLowerCase(),"/settings/kyc"),icon:(0,a.jsx)(i.Z,{className:"h-5 w-5"}),label:"kyc"})}let k=e=>{let{conversation:t}=e;if(!t||"object"!=typeof t||Array.isArray(t)||!p)return null;let s=t.name||"Unknown",r=(null==v?void 0:v.id)===t.id;return(0,a.jsxs)(d.Avatar,{className:"w-10 h-10 cursor-pointer transition-all ".concat(r?"border-2 border-blue-500":"border-transparent"),onClick:()=>p(t),children:[(0,a.jsx)(d.AvatarImage,{src:"",alt:s}),(0,a.jsx)(d.AvatarFallback,{children:s.split(" ").map(e=>e.charAt(0).toUpperCase()).join("").slice(0,2)})]})},S=e=>{let{item:t}=e;return t.subItems?(0,a.jsxs)(m.J2,{children:[(0,a.jsx)(m.xo,{className:"flex h-9 w-9 items-center justify-center rounded-lg ".concat(b(t)?"bg-accent text-accent-foreground":"text-muted-foreground hover:text-foreground"," transition-colors md:h-8 md:w-8"),children:t.icon}),(0,a.jsx)(m.yk,{side:"right",align:"start",className:"w-48 p-2",sideOffset:8,children:(0,a.jsx)("div",{className:"flex flex-col gap-1",children:t.subItems.map((e,t)=>(0,a.jsxs)(r.default,{href:e.href,onClick:()=>x(e.label),className:"flex items-center gap-2 rounded-md px-2 py-1.5 text-sm ".concat(g(e.href)?"bg-accent text-accent-foreground":"text-muted-foreground hover:bg-accent hover:text-accent-foreground"),children:[e.icon,e.label]},t))})})]}):(0,a.jsx)(c.TooltipProvider,{children:(0,a.jsxs)(c.u,{children:[(0,a.jsx)(c.aJ,{asChild:!0,children:(0,a.jsxs)(r.default,{href:t.href,onClick:()=>x(t.label),className:"flex h-9 w-9 items-center justify-center rounded-lg ".concat(t.label===u||"Dehix"===t.label?"Dehix"===t.label?"group flex h-9 w-9 shrink-0 items-center justify-center gap-2 rounded-full bg-primary text-lg font-semibold text-primary-foreground md:h-8 md:w-8 md:text-base":"flex h-9 w-9 items-center justify-center rounded-lg bg-accent text-accent-foreground transition-colors hover:text-foreground md:h-8 md:w-8":"flex h-9 w-9 items-center justify-center rounded-lg text-muted-foreground transition-colors hover:text-foreground md:h-8 md:w-8"),children:[t.icon,(0,a.jsx)("span",{className:"sr-only",children:t.label})]})}),(0,a.jsx)(c._v,{side:"right",children:t.label})]})})};return(0,a.jsxs)("aside",{className:"fixed inset-y-0 left-0 z-10 hidden w-14 flex-col border-r bg-background sm:flex",children:[(0,a.jsxs)("nav",{className:"flex flex-col items-center gap-4 px-2 sm:py-5",children:[y.map((e,t)=>(0,a.jsx)(S,{item:e},t)),"Chats"===u&&p&&h&&h.map(e=>(0,a.jsx)(k,{conversation:e},e.id))]}),(0,a.jsx)("div",{className:"mt-auto mx-auto",children:(0,a.jsx)(o.T,{})}),(0,a.jsx)("nav",{className:"flex flex-col items-center gap-4 px-2 sm:py-5",children:s.map((e,t)=>(0,a.jsx)(S,{item:e},t))})]})}},9753:function(e,t,s){s.d(t,{Z:function(){return w}});var a=s(57437),r=s(2265),l=s(52022),n=s(89896),i=s(71510),o=s(22468),d=s(6884),c=s(11444),m=s(16463),u=s(87138),x=s(44785),f=s(56508),h=s(31590),p=s(89733),v=s(80420),j=s(3483),N=s(54662),g=s(15922),b=s(86763);let y=()=>async(e,t,s)=>{if(navigator.share)try{await navigator.share({title:e,text:t,url:s})}catch(e){console.error("Error sharing content:",e)}else console.warn("Share API is not supported on this browser.")};function w(e){let{setConnects:t}=e,s=(0,c.v9)(e=>e.user),w=(0,c.I0)(),k=(0,m.useRouter)(),[S,C]=(0,r.useState)(null),[T,R]=(0,r.useState)(""),[z,A]=(0,r.useState)(!1),[D,F]=(0,r.useState)(!0),[P,O]=(0,r.useState)(null),E=y();(0,r.useEffect)(()=>{(null==s?void 0:s.type)?C(s.type):C(x.Z.get("userType")||null)},[s]),(0,r.useEffect)(()=>{let e=async()=>{F(!0);try{var e,a,r,l,n,i,o;let d=await g.b.get("/".concat(s.type,"/").concat(null==s?void 0:s.uid)),c=(null===(a=d.data.data)||void 0===a?void 0:null===(e=a.referral)||void 0===e?void 0:e.referralCode)||"",m=null!==(o=null!==(i=null===(l=d.data)||void 0===l?void 0:null===(r=l.data)||void 0===r?void 0:r.connects)&&void 0!==i?i:null===(n=d.data)||void 0===n?void 0:n.connects)&&void 0!==o?o:0;localStorage.setItem("DHX_CONNECTS",m.toString()),t&&t(m),R(c)}catch(e){console.error("API Error:",e),(0,b.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}finally{F(!1)}};(null==s?void 0:s.uid)?e():(console.warn("User ID is not available. Skipping API call."),F(!1))},[null==s?void 0:s.uid,s.type,t]);let I=e=>{E("Referral Link","Check out this referral link!",e)},_=T?"".concat("http://127.0.0.1:8080/","auth/sign-up/freelancer?referral=").concat(T):"",Z=e=>{navigator.clipboard.writeText(e).then(()=>{O(e),setTimeout(()=>{O(null)},2e3)},e=>{console.error("Failed to copy text: ",e)})};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(h.h_,{children:[(0,a.jsx)(h.$F,{asChild:!0,children:(0,a.jsx)(p.z,{variant:"outline",size:"icon",className:"overflow-hidden rounded-full hover:scale-105 transition-transform",children:(0,a.jsxs)(v.Avatar,{className:"h-8 w-8",children:[(0,a.jsx)(v.AvatarImage,{src:s.photoURL,alt:"@shadcn"}),(0,a.jsx)(v.AvatarFallback,{children:(0,a.jsx)(l.Z,{className:"w-5 h-5 hover:scale-105 transition-transform"})})]})})}),(0,a.jsxs)(h.AW,{align:"end",children:[(0,a.jsx)(h.Ju,{children:s.email}),(0,a.jsx)(h.VD,{}),(0,a.jsx)(u.default,{href:"/dashboard/freelancer",children:(0,a.jsx)(h.Xi,{children:"Home"})}),(0,a.jsx)("div",{children:"freelancer"===S?(0,a.jsx)(u.default,{href:"/freelancer/settings/personal-info",children:(0,a.jsx)(h.Xi,{children:"Settings"})}):"business"===S?(0,a.jsx)(u.default,{href:"/business/settings/business-info",children:(0,a.jsx)(h.Xi,{children:"Settings"})}):(0,a.jsx)("p",{children:"Loading..."})}),(0,a.jsx)(u.default,{href:"/settings/support",children:(0,a.jsx)(h.Xi,{children:"Support"})}),(0,a.jsx)(h.Xi,{onClick:()=>{A(!0)},children:"Referral"}),(0,a.jsx)(h.VD,{}),(0,a.jsxs)(h.Xi,{onClick:()=>{w((0,j.pn)()),x.Z.remove("userType"),x.Z.remove("token"),k.replace("/auth/login")},children:[(0,a.jsx)(n.Z,{size:18,className:"mr-2"}),"Logout"]})]})]}),(0,a.jsx)(N.Vq,{open:z,onOpenChange:A,children:(0,a.jsxs)(N.cZ,{className:"max-w-2xl w-full px-4 sm:px-6 md:px-8 py-6 rounded-lg",children:[(0,a.jsxs)(N.fK,{children:[(0,a.jsx)(N.$N,{className:"text-lg sm:text-xl font-bold",children:"Your Referral Information"}),(0,a.jsx)(N.Be,{className:"text-sm sm:text-base text-gray-500",children:"Share this link and code with your friends to invite them:"})]}),D?(0,a.jsx)("p",{className:"text-center text-gray-600 text-sm sm:text-base",children:"Loading referral information..."}):T?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("p",{className:"text-sm sm:text-base font-medium text-gray-300",children:"Referral Link:"}),(0,a.jsxs)("div",{className:"mt-2 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2",children:[(0,a.jsx)("a",{href:_,target:"_blank",rel:"noopener noreferrer",className:"flex-1 max-w-full break-words sm:truncate",title:_,children:(0,f.N)(_,60)}),(0,a.jsx)(p.z,{variant:"ghost",size:"icon",onClick:()=>I(_),className:"ml-2 sm:ml-4",children:(0,a.jsx)(i.Z,{size:16})})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("p",{className:"text-sm sm:text-base font-medium text-gray-300",children:"Referral Code:"}),(0,a.jsxs)("div",{className:"mt-2 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2",children:[(0,a.jsx)("span",{className:"font-medium flex-1 truncate",children:T}),(0,a.jsx)(p.z,{variant:"ghost",size:"icon",onClick:()=>Z(T),className:"ml-2 sm:ml-4",children:P===T?(0,a.jsx)(o.Z,{size:16,className:"text-green-500"}):(0,a.jsx)(d.Z,{size:16})})]})]})]}):(0,a.jsx)("p",{className:"text-center text-gray-600 text-sm sm:text-base",children:"No referral code is available for this user."})]})})]})}},56508:function(e,t,s){s.d(t,{Z:function(){return el},N:function(){return er}});var a=s(57437),r=s(2265),l=s(13793),n=e=>{let{date:t,title:s,summary:r,position:l,isMobile:n,isSelected:i}=e,o=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return e.length>t?e.slice(0,t)+"...":e};return(0,a.jsx)("div",{className:"flex flex-col group-hover:bg-[#40b3ff] rounded-md   items-center gap-2 relative ".concat(n?"w-64":"w-48 h-20"," ").concat("top"===l?"mt-[132px]":"bottom"===l?"-mt-[106px]":""," \n          ").concat(i?"bg-[#11a0ff]":"dynamic-card","\n        "),style:{width:"200px",maxWidth:"100%",visibility:"dummy"===s?"hidden":"visible"},children:(0,a.jsxs)("div",{className:"text-center  border-line-bg w-full h-full rounded-md  p-4 ".concat(n?"text-base":"text-sm","  border"),children:[(0,a.jsx)("p",{className:"text-xs",children:new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}),(0,a.jsx)("h3",{className:"font-bold  ".concat(n?"text-lg":"text-sm"),children:o(s,20)}),r&&(0,a.jsx)("p",{className:" mt-0.5 ".concat(n?"text-sm":"text-xs"),children:o(r,20)})]})})},i=s(92513),o=s(89733),d=s(48185),c=s(79055),m=s(44475),u=e=>{let{milestone:t}=e,{text:s,className:l}=(0,m.S)(t.status),[n,i]=(0,r.useState)(!1),o=t.description.split(" "),u=o.length>30?o.slice(0,30).join(" ")+"...":t.description;return(0,a.jsxs)(d.Ol,{children:[(0,a.jsxs)(d.ll,{className:"text-lg md:text-xl font-bold flex justify-between items-center",children:[(0,a.jsxs)("p",{children:["Milestone: ",t.title]}),(0,a.jsx)("p",{children:(0,a.jsx)(c.C,{className:"".concat(l," px-3 py-1 hidden md:flex text-xs md:text-sm rounded-full"),children:s})})]}),(0,a.jsxs)(d.SZ,{className:"mt-1 text-sm md:text-base",children:[(0,a.jsx)("p",{children:n?t.description:u}),o.length>30&&(0,a.jsx)("button",{onClick:()=>{i(!n)},className:"text-blue-500 text-xs mt-2",children:n?"Show Less":"Show More"})]})]})},x=s(1282),f=s(6884),h=s(22468),p=s(74697),v=s(15642),j=s(8555),N=s(78068),g=s(54662),b=s(47304),y=s(11444),w=s(45188),k=s(6649),S=s(92940),C=s(68279),T=s(31590),R=s(3274),z=s(66648),A=s(15922),D=s(86763),F=e=>{let{freelancerId:t,onClose:s}=e,[l,n]=(0,r.useState)(null),[i,d]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{(async()=>{if(t){d(!0);try{let e=await A.b.get("/freelancer/".concat(t));n(e.data)}catch(e){console.error("Error fetching freelancer details:",e),(0,D.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}finally{d(!1)}}})()},[t]),(0,a.jsx)(g.Vq,{open:!0,onOpenChange:s,children:(0,a.jsxs)(g.cZ,{className:"sm:max-w-[400px] p-4",children:[i?(0,a.jsx)("div",{className:"flex justify-center py-10",children:(0,a.jsx)(R.Z,{className:"w-10 h-10 text-blue-500 animate-spin"})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(g.fK,{children:[(0,a.jsx)(g.$N,{className:"text-base",children:null==l?void 0:l.userName}),(0,a.jsx)(g.Be,{className:"text-sm",children:"Details of the freelancer."})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(z.default,{src:(null==l?void 0:l.profilePic)||"https://img.freepik.com/premium-photo/flat-icon-design_1258715-221692.jpg?semt=ais_hybrid",alt:"Profile of freelancer",width:80,height:80,className:"rounded-full border-2 border-gray-300 mb-3 object-cover",unoptimized:!0}),(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"".concat(null==l?void 0:l.firstName," ").concat(null==l?void 0:l.lastName)}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:null==l?void 0:l.role}),(0,a.jsxs)("div",{className:"mt-3 space-y-1 text-center",children:[(0,a.jsxs)("p",{className:"text-sm",children:["Email: ",null==l?void 0:l.email]}),(0,a.jsxs)("p",{className:"text-sm",children:["Phone: ",null==l?void 0:l.phone]}),(0,a.jsxs)("p",{className:"text-sm",children:["Hourly Rate: $",null==l?void 0:l.perHourPrice]})]}),(0,a.jsxs)("div",{className:"flex space-x-3 mt-4",children:[(null==l?void 0:l.githubLink)&&(0,a.jsx)("a",{href:null==l?void 0:l.githubLink,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)(o.z,{variant:"outline",size:"sm",children:"GitHub"})}),(null==l?void 0:l.linkedin)&&(0,a.jsx)("a",{href:null==l?void 0:l.linkedin,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)(o.z,{variant:"outline",size:"sm",children:"LinkedIn"})}),(null==l?void 0:l.personalWebsite)&&(0,a.jsx)("a",{href:null==l?void 0:l.personalWebsite,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)(o.z,{variant:"outline",size:"sm",children:"Website"})})]})]})]}),(0,a.jsx)(g.cN,{className:"mt-4"})]})})},P=s(77209),O=s(4919),E=s(2128),I=e=>{var t,s,l,n;let{task:i,milestoneId:d,storyId:c,taskId:m,userType:u,showPermissionDialog:x,setShowPermissionDialog:f,handleConfirmPermissionRequest:h,fetchMilestones:p}=e,[v,j]=(0,r.useState)({title:(null==i?void 0:i.title)||"",summary:(null==i?void 0:i.summary)||"",taskStatus:(null==i?void 0:i.taskStatus)||"NOT_STARTED"}),b={title:(null==i?void 0:i.title)||"",summary:(null==i?void 0:i.summary)||"",taskStatus:(null==i?void 0:i.taskStatus)||"NOT_STARTED"},y=null==i?void 0:null===(t=i.freelancers[0])||void 0===t?void 0:t.updatePermissionFreelancer,w=null==i?void 0:null===(s=i.freelancers[0])||void 0===s?void 0:s.updatePermissionBusiness,k=null==i?void 0:null===(l=i.freelancers[0])||void 0===l?void 0:l.acceptanceBusiness,S=null==i?void 0:null===(n=i.freelancers[0])||void 0===n?void 0:n.acceptanceFreelancer,C="business"===u?w&&y&&k:y&&w&&S,T="business"===u&&w&&!y||"freelancer"===u&&y&&!w,R=(e,t)=>{j(s=>({...s,[e]:t}))},z=async()=>{if(JSON.stringify(v)===JSON.stringify(b)){(0,N.Am)({description:"No changes detected. Task update not required.",duration:3e3});return}let e="/milestones/update/milestone/".concat(d,"/story/").concat(c,"/task/").concat(i._id);try{await A.b.patch(e,{milestoneId:d,storyId:c,taskId:m,userType:u,title:v.title,summary:v.summary,taskStatus:v.taskStatus}),(0,N.Am)({description:"Task updated",duration:3e3}),f(!1),p()}catch(e){(0,N.Am)({description:"Task not update , please try again .",duration:3e3})}};return(0,a.jsxs)(g.Vq,{open:x,onOpenChange:f,children:[(0,a.jsx)(g.hg,{className:"hidden",children:"Trigger"}),(0,a.jsxs)(g.cZ,{className:" sm:w-[86vw] md:w-[450px]  p-6 border rounded-md shadow-md",children:[(0,a.jsxs)(g.fK,{children:[(0,a.jsx)(g.$N,{children:C?"Update Task Details":"Request Permission"}),(0,a.jsx)(g.Be,{children:C?"You have permission to update the task details.":T?"Your request has been sent. Please wait until permission is accepted.":"You don't have permission to update. Please request permission."})]}),C?(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"taskTitle",className:"text-sm font-medium mb-2",children:"Task Title:"}),(0,a.jsx)(P.I,{id:"taskTitle",type:"text",value:v.title,onChange:e=>R("title",e.target.value),className:"mb-2 mt-1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"taskSummary",className:"text-sm font-medium mb-2",children:"Summary:"}),(0,a.jsx)(O.g,{id:"taskSummary",value:v.summary,onChange:e=>R("summary",e.target.value),rows:4,className:"mb-2 mt-1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"taskStatus",className:"text-sm font-medium mb-1",children:"Task Status:"}),(0,a.jsxs)(E.Ph,{value:v.taskStatus,onValueChange:e=>R("taskStatus",e),children:[(0,a.jsx)(E.i4,{className:"p-2 mt-1 border rounded-md",children:(0,a.jsx)("span",{children:v.taskStatus})}),(0,a.jsxs)(E.Bw,{children:[(0,a.jsx)(E.Ql,{value:"NOT_STARTED",children:"NOT_STARTED"}),(0,a.jsx)(E.Ql,{value:"ONGOING",children:"ONGOING"}),(0,a.jsx)(E.Ql,{value:"COMPLETED",children:"COMPLETED"})]})]})]}),(0,a.jsx)(g.cN,{className:"flex mt-2 justify-end gap-3",children:(0,a.jsx)(o.z,{onClick:z,variant:"default",children:"Save"})})]}):(0,a.jsxs)(g.cN,{className:"flex mt-2 justify-end gap-4",children:[T?(0,a.jsx)(o.z,{variant:"outline",className:"bg-gray-400 text-white px-4 py-2 rounded-md",disabled:!0,children:"Request Sent"}):(0,a.jsx)(o.z,{onClick:()=>{h("business"===u,"freelancer"===u,"business"===u,"freelancer"===u)},variant:"outline",className:"bg-blue-600 text-white px-4 py-2 rounded-md",children:"Send Permission Request"}),(0,a.jsx)(o.z,{onClick:()=>f(!1),variant:"outline",children:"Cancel"})]})]})]})},_=e=>{var t,s,l,n,i,o,d,c,m,u,x,f,h,p,v,j,g;let{task:b,milestoneId:R,storyId:z,fetchMilestones:D}=e,{type:P}=(0,y.v9)(e=>e.user),[O,E]=(0,r.useState)(!1),[_,Z]=(0,r.useState)(null),[U,L]=(0,r.useState)(!1),[Y,B]=(0,r.useState)(!1),V=()=>{B(!0)},q=async(e,t,s,a)=>{let r="/milestones/".concat(R,"/story/").concat(z,"/task/").concat(b._id);try{await A.b.patch(r,{updatePermissionBusiness:e,updatePermissionFreelancer:t,acceptanceBusiness:s,acceptanceFreelancer:a}),B(!1),(0,N.Am)({title:"Success",description:"Permissions updated successfully.",duration:3e3}),D()}catch(e){console.error("Error during permission request:",e),(0,N.Am)({title:"Error",description:"Failed to update permissions. Please try again.",variant:"destructive",duration:3e3})}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(T.h_,{open:U,onOpenChange:L,children:[(0,a.jsxs)(T.$F,{className:"px-2 py-1 absolute bottom-1 right-0 rounded-md",children:[(0,a.jsx)(w.Z,{className:"text-3xl w-5 h-5"}),("freelancer"==P?(null==b?void 0:null===(t=b.freelancers[0])||void 0===t?void 0:t.updatePermissionBusiness)&&(null==b?void 0:null===(s=b.freelancers[0])||void 0===s?void 0:s.updatePermissionFreelancer)&&!(null==b?void 0:null===(l=b.freelancers[0])||void 0===l?void 0:l.acceptanceBusiness):(null==b?void 0:null===(n=b.freelancers[0])||void 0===n?void 0:n.updatePermissionBusiness)&&(null==b?void 0:null===(i=b.freelancers[0])||void 0===i?void 0:i.updatePermissionFreelancer)&&!(null===(o=b.freelancers[0])||void 0===o?void 0:o.acceptanceFreelancer))&&(0,a.jsx)("span",{className:"absolute top-0 right-2 w-2 h-2 bg-red-500 rounded-full"}),("freelancer"==P?(null==b?void 0:null===(d=b.freelancers[0])||void 0===d?void 0:d.updatePermissionBusiness)&&!(null==b?void 0:null===(c=b.freelancers[0])||void 0===c?void 0:c.updatePermissionFreelancer)&&(null==b?void 0:null===(m=b.freelancers[0])||void 0===m?void 0:m.acceptanceBusiness):!(null==b?void 0:null===(u=b.freelancers[0])||void 0===u?void 0:u.updatePermissionBusiness)&&(null==b?void 0:null===(x=b.freelancers[0])||void 0===x?void 0:x.updatePermissionFreelancer)&&(null===(f=b.freelancers[0])||void 0===f?void 0:f.acceptanceFreelancer))&&(0,a.jsx)("span",{className:"absolute top-0 right-2 w-2 h-2 bg-yellow-500 rounded-full"})]}),(0,a.jsx)(T.AW,{align:"end",className:"w-56 p-2 border rounded-md shadow-md",children:"freelancer"===P?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(T.Xi,{className:"flex items-center gap-2",onClick:V,children:[(0,a.jsx)(k.Z,{className:"w-4 h-4 text-blue-500"}),"Update Task Details"]}),!(null==b?void 0:null===(h=b.freelancers[0])||void 0===h?void 0:h.updatePermissionFreelancer)&&(null==b?void 0:null===(p=b.freelancers[0])||void 0===p?void 0:p.updatePermissionBusiness)&&!(null==b?void 0:null===(v=b.freelancers[0])||void 0===v?void 0:v.acceptanceFreelancer)&&(0,a.jsxs)(T.Xi,{className:"flex whitespace-nowrap text-xs  items-center gap-2",onClick:()=>q(!0,!0,!0,!1),children:[(0,a.jsx)(S.Z,{className:"w-4 h-4 text-yellow-500"}),"Approve Updates permission"]})]}):"business"===P?(0,a.jsxs)(a.Fragment,{children:[!(null==b?void 0:null===(j=b.freelancers[0])||void 0===j?void 0:j.updatePermissionBusiness)&&(null==b?void 0:b.freelancers[0].updatePermissionFreelancer)&&!(null==b?void 0:null===(g=b.freelancers[0])||void 0===g?void 0:g.acceptanceBusiness)&&(0,a.jsxs)(T.Xi,{className:"flex whitespace-nowrap text-xs  items-center gap-2",onClick:()=>q(!0,!0,!1,!0),children:[(0,a.jsx)(S.Z,{className:"w-4 h-4 text-yellow-500"}),"Approve Updates permission"]}),b.freelancers.length>0&&(0,a.jsxs)(T.Xi,{className:"flex items-center gap-2",onClick:()=>{var e;Z(null==b?void 0:null===(e=b.freelancers[0])||void 0===e?void 0:e.freelancerId),E(!0)},children:[(0,a.jsx)(C.Z,{className:"w-4 h-4 text-gray-500"}),"View Freelancer"]}),(0,a.jsxs)(T.Xi,{className:"flex items-center gap-2",onClick:V,children:[(0,a.jsx)(k.Z,{className:"w-4 h-4 text-blue-500"}),"Update Task Details"]})]}):(0,a.jsx)(T.Xi,{disabled:!0,className:"text-gray-400",children:"No actions available for this user type."})})]}),O&&_&&(0,a.jsx)(F,{freelancerId:_,onClose:()=>{Z(null),E(!1)}}),(0,a.jsx)(I,{fetchMilestones:D,userType:P,task:b,milestoneId:R,storyId:z,taskId:b._id,showPermissionDialog:Y,setShowPermissionDialog:B,handleConfirmPermissionRequest:q})]})},Z=s(35265),U=s(21413),L=s(35353),Y=s(54817),B=s(49354);let V=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(L.mY,{ref:t,className:(0,B.cn)("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",s),...r})});V.displayName=L.mY.displayName;let q=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsxs)("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[(0,a.jsx)(Y.Z,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),(0,a.jsx)(L.mY.Input,{ref:t,className:(0,B.cn)("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",s),...r})]})});q.displayName=L.mY.Input.displayName;let G=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(L.mY.List,{ref:t,className:(0,B.cn)("max-h-[300px] overflow-y-auto overflow-x-hidden",s),...r})});G.displayName=L.mY.List.displayName;let M=r.forwardRef((e,t)=>(0,a.jsx)(L.mY.Empty,{ref:t,className:"py-6 text-center text-sm",...e}));M.displayName=L.mY.Empty.displayName;let X=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(L.mY.Group,{ref:t,className:(0,B.cn)("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",s),...r})});X.displayName=L.mY.Group.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(L.mY.Separator,{ref:t,className:(0,B.cn)("-mx-1 h-px bg-border",s),...r})}).displayName=L.mY.Separator.displayName;let $=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(L.mY.Item,{ref:t,className:(0,B.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50",s),...r})});$.displayName=L.mY.Item.displayName;var W=e=>{var t,s,l,n,u,h,p,g,b,y,w,k,S,C,T,R,z,A,D,F,P,O,E,I,L,Y,B,W,K,H,Q,ee,et,es,ea,er,el,en,ei,eo,ed,ec,em,eu,ex,ef,eh,ep,ev,ej,eN,eg;let{milestoneId:eb,story:ey,idx:ew,milestoneStoriesLength:ek,setIsTaskDialogOpen:eS,isFreelancer:eC=!1,fetchMilestones:eT}=e,{text:eR,className:ez}=(0,m.S)(ey.storyStatus),[eA,eD]=(0,r.useState)(!1),[eF,eP]=(0,r.useState)(""),[eO,eE]=(0,r.useState)(null),eI=e=>{navigator.clipboard.writeText(e),(0,N.Am)({description:"URL copied!!",duration:900}),eD(!1),eP("")},e_=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return(null==e?void 0:e.length)>t?e.slice(0,t)+"...":e};return(0,a.jsxs)(Z.Qd,{value:null!==(eg=ey._id)&&void 0!==eg?eg:"",className:"py-2  ".concat(ew===ek-1?"border-b-0 ":"border-b-2"," "),children:[(0,a.jsx)(Z.o4,{className:"flex hover:no-underline items-center under px-4 w-full ",children:(0,a.jsxs)("div",{className:"flex justify-between items-center w-full px-4 py-1 rounded-lg duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg md:text-xl flex items-center font-semibold",children:[ey.title,(0,a.jsxs)("span",{className:"text-sm",children:[(null==ey?void 0:null===(l=ey.tasks)||void 0===l?void 0:null===(s=l[0])||void 0===s?void 0:null===(t=s.freelancers)||void 0===t?void 0:t[0])&&eC?!(null==ey?void 0:null===(u=ey.tasks[0])||void 0===u?void 0:null===(n=u.freelancers[0])||void 0===n?void 0:n.acceptanceFreelancer)&&(null==ey?void 0:null===(p=ey.tasks[0])||void 0===p?void 0:null===(h=p.freelancers[0])||void 0===h?void 0:h.updatePermissionBusiness)&&!(null==ey?void 0:null===(b=ey.tasks[0])||void 0===b?void 0:null===(g=b.freelancers[0])||void 0===g?void 0:g.updatePermissionFreelancer)&&(null==ey?void 0:null===(w=ey.tasks[0])||void 0===w?void 0:null===(y=w.freelancers[0])||void 0===y?void 0:y.acceptanceBusiness)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-yellow-500 ml-5 hidden md:flex",children:"Update Req"}),(0,a.jsx)("span",{className:"text-yellow-500 ml-5 rounded-full flex md:hidden w-2 h-2 bg-yellow-500"})]}):!(null==ey?void 0:null===(T=ey.tasks)||void 0===T?void 0:null===(C=T[0])||void 0===C?void 0:null===(S=C.freelancers)||void 0===S?void 0:null===(k=S[0])||void 0===k?void 0:k.acceptanceBusiness)&&(null==ey?void 0:null===(D=ey.tasks)||void 0===D?void 0:null===(A=D[0])||void 0===A?void 0:null===(z=A.freelancers)||void 0===z?void 0:null===(R=z[0])||void 0===R?void 0:R.updatePermissionFreelancer)&&!(null==ey?void 0:null===(E=ey.tasks)||void 0===E?void 0:null===(O=E[0])||void 0===O?void 0:null===(P=O.freelancers)||void 0===P?void 0:null===(F=P[0])||void 0===F?void 0:F.updatePermissionBusiness)&&(null==ey?void 0:null===(B=ey.tasks)||void 0===B?void 0:null===(Y=B[0])||void 0===Y?void 0:null===(L=Y.freelancers)||void 0===L?void 0:null===(I=L[0])||void 0===I?void 0:I.acceptanceFreelancer)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-yellow-500 ml-5 hidden md:flex",children:"Update Req"}),(0,a.jsx)("span",{className:"text-yellow-500 ml-5 rounded-full flex md:hidden w-2 h-2 bg-yellow-500"})]}),(null==ey?void 0:null===(H=ey.tasks)||void 0===H?void 0:null===(K=H[0])||void 0===K?void 0:null===(W=K.freelancers)||void 0===W?void 0:W[0])&&eC?(null==ey?void 0:null===(ee=ey.tasks[0])||void 0===ee?void 0:null===(Q=ee.freelancers[0])||void 0===Q?void 0:Q.updatePermissionBusiness)&&(null==ey?void 0:null===(es=ey.tasks[0])||void 0===es?void 0:null===(et=es.freelancers[0])||void 0===et?void 0:et.updatePermissionFreelancer)&&!(null==ey?void 0:null===(er=ey.tasks[0])||void 0===er?void 0:null===(ea=er.freelancers[0])||void 0===ea?void 0:ea.acceptanceBusiness)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-green-500 ml-5 hidden md:flex",children:"Req Approve"}),(0,a.jsx)("span",{className:"text-green-500 ml-5 rounded-full flex md:hidden w-2 h-2 bg-green-500"})]}):(null==ey?void 0:null===(eo=ey.tasks)||void 0===eo?void 0:null===(ei=eo[0])||void 0===ei?void 0:null===(en=ei.freelancers)||void 0===en?void 0:null===(el=en[0])||void 0===el?void 0:el.updatePermissionFreelancer)&&(null==ey?void 0:null===(eu=ey.tasks)||void 0===eu?void 0:null===(em=eu[0])||void 0===em?void 0:null===(ec=em.freelancers)||void 0===ec?void 0:null===(ed=ec[0])||void 0===ed?void 0:ed.updatePermissionBusiness)&&!(null==ey?void 0:null===(ep=ey.tasks)||void 0===ep?void 0:null===(eh=ep[0])||void 0===eh?void 0:null===(ef=eh.freelancers)||void 0===ef?void 0:null===(ex=ef[0])||void 0===ex?void 0:ex.acceptanceFreelancer)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-green-500 ml-5 hidden md:flex",children:"Req Approve"}),(0,a.jsx)("span",{className:"text-green-500 ml-5 rounded-full flex md:hidden w-2 h-2 bg-green-500"})]})]})]}),(0,a.jsx)(c.C,{className:"".concat(ez," px-3 py-1 hidden md:flex text-xs md:text-sm rounded-full"),children:eR})]})}),(0,a.jsxs)(Z.vF,{className:"w-full px-4 py-4 sm:px-6 sm:py-4 md:px-8 md:py-6 lg:px-10 lg:py-8",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(c.C,{className:"".concat(ez," px-2 py-1 block md:hidden text-xs md:text-sm rounded-full"),style:{width:"fit-content"},children:eR}),(0,a.jsx)("p",{className:"leading-relaxed px-2 py-1  text-sm md:text-base",children:ey.summary}),(0,a.jsxs)("div",{className:"space-y-4 hidden md:flex justify-start items-center gap-4",children:[(0,a.jsx)("h4",{className:"text-lg md:text-xl font-semibold mt-2",children:"Important URLs:"}),(0,a.jsxs)(U.J2,{open:eA,onOpenChange:eD,children:[(0,a.jsx)(U.xo,{asChild:!0,children:(0,a.jsxs)(o.z,{variant:"outline",role:"combobox","aria-expanded":eA,className:"w-[300px] justify-between",children:[e_(eF,23)||"Select or search URL...",(0,a.jsx)(x.Z,{className:"opacity-50"})]})}),(0,a.jsx)(U.yk,{className:"w-[300px] p-0",children:(0,a.jsxs)(V,{children:[(0,a.jsx)(q,{placeholder:"Search URL...",className:"h-9"}),(0,a.jsxs)(G,{children:[(0,a.jsx)(M,{children:"No URL found."}),(0,a.jsx)(X,{children:ey.importantUrls.map(e=>(0,a.jsx)("div",{children:(0,a.jsxs)(j.zs,{children:[(0,a.jsx)(j.Yi,{asChild:!0,children:(0,a.jsxs)($,{value:e.urlName,className:"cursor-pointer",onSelect:()=>{eP(e.urlName),eD(!1)},children:[e_(e.urlName,20),(0,a.jsx)(o.z,{variant:"ghost",size:"sm",className:"ml-auto cursor-pointer",onClick:t=>{t.stopPropagation(),eI(e.url)},children:(0,a.jsx)(f.Z,{className:"w-4 h-4"})})]})}),(0,a.jsx)(j.bZ,{className:"w-auto py-1",children:e.url})]})},e.urlName))})]})]})})]})]})]}),(0,a.jsx)(d.Zb,{className:"px-2  mt-5 py-3",children:(null==ey?void 0:null===(ev=ey.tasks)||void 0===ev?void 0:ev.length)>0?(0,a.jsxs)("div",{className:"bg-transparent",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center px-3 mt-4",children:[(0,a.jsx)("h4",{className:"text-lg md:text-xl font-semibold",children:"Tasks:"}),!eC&&(0,a.jsxs)(o.z,{className:"md:px-3 px-2 py-0 md:py-1 text-sm sm:text-base",onClick:()=>eS(!0),children:[(0,a.jsx)(i.Z,{size:15})," Add Task"]})]}),(0,a.jsxs)(v.lr,{className:"w-[85vw] md:w-full relative mt-4",children:[(0,a.jsx)(v.KI,{className:"flex flex-nowrap gap-2 md:gap-0",children:ey.tasks.map(e=>{let{className:t}=(0,m.S)(e.taskStatus);return(0,a.jsx)(v.d$,{className:"min-w-0 mt-2 w-full md:basis-1/2 sm:w-full md:w-1/2 lg:w-1/3",children:(0,a.jsx)("div",{className:" p-0 md:p-2 mt-5",children:(0,a.jsxs)(d.Zb,{className:"w-full cursor-pointer  border relative rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300",onClick:()=>eE(e),children:[(0,a.jsx)(d.Ol,{className:"p-2 md:p-4 ",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)(d.ll,{className:"text-sm md:text-lg font-medium",children:e_(e.title,20)}),(0,a.jsx)(c.C,{className:"".concat(t," px-2 py-0.5 text-xs md:text-sm rounded-md"),children:e.taskStatus})]})}),(0,a.jsx)(d.aY,{className:"p-4",children:(0,a.jsx)("p",{className:"text-sm leading-relaxed",children:e_(e.summary,30)})}),(0,a.jsx)("div",{onClick:e=>e.stopPropagation(),children:(0,a.jsx)(_,{fetchMilestones:eT,milestoneId:eb,storyId:ey._id,task:e})})]})})},e._id)})}),(0,a.jsx)("div",{className:"".concat((null==ey?void 0:null===(ej=ey.tasks)||void 0===ej?void 0:ej.length)>(window.innerWidth>768?2:1)?"block":"hidden"),children:(null==ey?void 0:null===(eN=ey.tasks)||void 0===eN?void 0:eN.length)>(window.innerWidth>=768?2:1)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.am,{className:"absolute top-1 md:top-2 left-2 transform -translate-y-1/2 shadow rounded-full p-2"}),(0,a.jsx)(v.Pz,{className:"absolute top-1 md:top-2  right-2 transform -translate-y-1/2 shadow rounded-full p-2"})]})})]})]}):(0,a.jsxs)("div",{className:"text-center mt-12 p-4 rounded-md",children:[eC?(0,a.jsxs)("p",{children:["This ",ey.title," currently has no tasks. Wait until the business assigns you to any task."]}):(0,a.jsxs)("p",{children:["This ",ey.title," currently has no tasks. Add tasks to ensure smooth progress and better tracking."]}),!eC&&(0,a.jsxs)(o.z,{className:"mt-2 px-3 py-1 text-sm sm:text-base",onClick:()=>eS(!0),children:[(0,a.jsx)(i.Z,{size:15})," Add Task"]})]})})]}),(0,a.jsx)(J,{task:eO,open:!!eO,onClose:()=>eE(null)})]},ey._id)};let J=e=>{var t,s;let{task:r,open:l,onClose:n}=e;return r?(0,a.jsx)(g.Vq,{open:l,onOpenChange:n,children:(0,a.jsxs)(g.cZ,{className:"max-w-lg rounded-lg mx-auto w-[90vw] md:w-auto shadow-lg ",children:[(0,a.jsx)(g.fK,{children:(0,a.jsx)(g.$N,{className:"text-xl font-bold",children:r.title})}),(0,a.jsxs)(g.Be,{className:"text-sm mt-4 leading-relaxed",children:[(0,a.jsxs)("p",{className:"mt-2 text-sm",children:["Task status:"," ",(0,a.jsx)("span",{className:"font-medium ",children:null==r?void 0:r.taskStatus})]}),(0,a.jsxs)("p",{className:"mt-2 text-sm",children:["Freelancer Name:"," ",(0,a.jsx)("span",{className:"font-medium ",children:null==r?void 0:null===(t=r.freelancers[0])||void 0===t?void 0:t.freelancerName})]}),(0,a.jsxs)("p",{className:"mt-2 text-sm",children:["Payment Status:"," ",(0,a.jsx)("span",{className:"font-medium ",children:null==r?void 0:null===(s=r.freelancers[0])||void 0===s?void 0:s.paymentStatus})]}),(0,a.jsxs)(b.iA,{className:"mt-4 border border-gray-300",children:[(0,a.jsx)(b.xD,{children:(0,a.jsxs)(b.SC,{className:"",children:[(0,a.jsx)(b.ss,{className:"font-semibold text-left",children:"User"}),(0,a.jsxs)(b.ss,{className:"font-semibold text-left",children:["Freelancer"," "]}),(0,a.jsxs)(b.ss,{className:"font-semibold text-left",children:["Business"," "]})]})}),(0,a.jsx)(b.RM,{children:r.freelancers.map((e,t)=>(0,a.jsxs)(b.SC,{className:"border-b border-gray-200",children:[(0,a.jsx)(b.pj,{className:"py-2 px-4 font-medium",children:"Update Permission"}),(0,a.jsx)(b.pj,{className:"py-2 px-4 text-center",children:(0,a.jsx)("div",{className:"flex justify-center items-center",children:e.updatePermissionFreelancer&&e.updatePermissionFreelancer&&e.acceptanceFreelancer?(0,a.jsx)(h.Z,{className:"text-green-600 w-5 h-5"}):(0,a.jsx)(p.Z,{className:"text-red-600 w-5 h-5"})})}),(0,a.jsx)(b.pj,{className:"py-2 px-4 text-center",children:(0,a.jsx)("div",{className:"flex justify-center items-center",children:e.updatePermissionBusiness&&e.updatePermissionFreelancer&&e.acceptanceBusiness?(0,a.jsx)(h.Z,{className:"text-green-600 w-5 h-5"}):(0,a.jsx)(p.Z,{className:"text-red-600 w-5 h-5"})})})]},t))})]}),(0,a.jsx)("p",{className:"mt-3",children:r.summary})]}),(0,a.jsx)("div",{className:"flex justify-end mt-4",children:(0,a.jsx)(g.GG,{asChild:!0,children:(0,a.jsx)(o.z,{variant:"outline",children:"Close"})})})]})}):null};var K=s(16463),H=s(80420),Q=e=>{let{isDialogOpen:t,setIsDialogOpen:s,formData:l,handleInputChange:n,handelSubmit:i,handleFreelancerSelect:d}=e,{project_id:m}=(0,K.useParams)(),[u,x]=(0,r.useState)({title:!1,summary:!1,taskStatus:!1,freelancer:!1}),[f,h]=(0,r.useState)([]),[p,v]=(0,r.useState)([]),[j,N]=(0,r.useState)(null),[b,y]=(0,r.useState)(!1),w=()=>{let e={title:!l.title.trim(),summary:!l.summary.trim(),taskStatus:!l.taskStatus,freelancer:!l.freelancers};return x(e),!Object.values(e).some(e=>e)};(0,r.useEffect)(()=>{let e=async()=>{y(!0);try{let e=await A.b.get("/project/get-freelancer/".concat(m));h(e.data.freelancers.freelancerData),v(e.data.freelancers.freelancerData)}catch(e){console.error("Failed to fetch freelancers:",e),(0,D.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}finally{y(!1)}};t&&e()},[t,m]);let k=e=>{let t=e.toLowerCase();v(f.filter(e=>e.userName.toLowerCase().includes(t)||e.email.toLowerCase().includes(t)))};return(0,a.jsx)(g.Vq,{open:t,onOpenChange:s,children:(0,a.jsxs)(g.cZ,{className:"w-full max-w-lg",children:[(0,a.jsx)(g.fK,{children:(0,a.jsx)(g.$N,{children:"Add New Task"})}),b?(0,a.jsx)("div",{className:"flex justify-center py-10",children:(0,a.jsx)(R.Z,{className:"w-10 h-10 text-blue-500 animate-spin"})}):(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),w()&&i(e)},children:[(0,a.jsxs)("div",{className:"space-y-4 p-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"title",className:"block text-sm font-medium mb-1",children:"Task Title"}),(0,a.jsx)(P.I,{id:"title",name:"title",placeholder:"Task Title",value:l.title,onChange:n,required:!0,className:"".concat(u.title?"border-red-500":"")}),u.title&&(0,a.jsx)("p",{className:"text-red-500 text-xs mt-1",children:"Task title is required."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"summary",className:"block text-sm font-medium mb-1",children:"Task Summary"}),(0,a.jsx)(O.g,{id:"summary",name:"summary",placeholder:"Task Summary",value:l.summary,onChange:n,required:!0,className:"".concat(u.summary?"border-red-500":"")}),u.summary&&(0,a.jsx)("p",{className:"text-red-500 text-xs mt-1",children:"Task summary is required."})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("label",{htmlFor:"taskStatus",className:"w-1/3 text-sm font-medium mb-1",children:"Task Status"}),(0,a.jsxs)(T.h_,{children:[(0,a.jsx)(T.$F,{asChild:!0,children:(0,a.jsx)(o.z,{variant:"ghost",className:"w-full border rounded-md text-left ".concat(u.taskStatus?"border-red-500":""),children:l.taskStatus?({NOT_STARTED:"Not Started",ONGOING:"On Going",COMPLETED:"Completed"})[l.taskStatus]:"Select Status"})}),(0,a.jsxs)(T.AW,{children:[(0,a.jsx)(T.Xi,{onSelect:()=>n("NOT_STARTED"),children:"Not Started"}),(0,a.jsx)(T.Xi,{onSelect:()=>n("ONGOING"),children:"On Going"}),(0,a.jsx)(T.Xi,{onSelect:()=>n("COMPLETED"),children:"Completed"})]})]})]}),u.taskStatus&&(0,a.jsx)("p",{className:"text-red-500 text-xs mt-1",children:"Task status is required."}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("label",{htmlFor:"freelancer",className:"w-1/3 text-sm font-medium mb-1",children:"Freelancer"}),(0,a.jsxs)(U.J2,{children:[(0,a.jsx)(U.xo,{className:"overscroll-y-none",asChild:!0,children:(0,a.jsx)(o.z,{variant:"ghost",className:"w-full border rounded-md text-left ".concat(u.freelancer?"border-red-500":""),children:j||"Select Freelancer"})}),(0,a.jsx)(U.yk,{className:"w-full p-0",children:(0,a.jsxs)(V,{children:[(0,a.jsx)(q,{placeholder:"Search freelancer by username or email...",onValueChange:e=>k(e)}),(0,a.jsx)(G,{className:" overflow-x-visible max-h-[300px]",children:0===p.length?(0,a.jsx)(M,{children:"No freelancers found."}):(0,a.jsx)(X,{className:" block overflow-auto",children:(0,a.jsx)("div",{className:"space-y-2 px-4 py-2",children:p.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between gap-1 p-3 rounded-lg border shadow-sm hover:shadow-md cursor-pointer",onClick:()=>{N(e.userName),d(e)},children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(H.Avatar,{className:"h-9 w-9",children:(0,a.jsx)(H.AvatarFallback,{children:e.userName.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:e.userName}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:er(e.email,20)})]})]}),(0,a.jsx)(c.C,{children:"".concat(e.role||"N/A")})]},e._id))})})})]})})]})]}),u.freelancer&&(0,a.jsx)("p",{className:"text-red-500 text-xs mt-1",children:"Freelancer selection is required."})]}),(0,a.jsxs)(g.cN,{children:[(0,a.jsx)(o.z,{type:"submit",children:"Add Task"}),(0,a.jsx)(o.z,{type:"button",variant:"secondary",className:"mb-3",onClick:()=>s(!1),children:"Cancel"})]})]})]})})},ee=e=>{let{isDialogOpen:t,setIsDialogOpen:s,handleInputChange:l,handleCloseDialog:n,handleStorySubmit:i,storyData:d,handleRemoveUrl:c,handleAddUrl:m,milestones:u,resetFields:x}=e,[f,h]=(0,r.useState)({}),v=()=>{let e={};return d.title.trim()||(e.title="Story title is required."),d.summary.trim()||(e.summary="Summary is required."),d.importantUrls.some(e=>!e.urlName.trim()||!e.url.trim())&&(e.importantUrls="All URL fields are required."),d.storyStatus||(e.storyStatus="Story status is required."),h(e),0===Object.keys(e).length};return(0,a.jsx)(g.Vq,{open:t,onOpenChange:s,children:(0,a.jsxs)(g.cZ,{children:[(0,a.jsx)(g.fK,{children:(0,a.jsx)(g.$N,{children:"Add New Story"})}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),v()&&(i(e,d,u),x(),n())},children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"storyTitle",className:"block text-sm font-medium mb-1",children:"Story Title"}),(0,a.jsx)(P.I,{placeholder:"Enter story title",value:d.title,onChange:e=>l("title",e.target.value),required:!0}),f.title&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.title})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"storySummary",className:"block text-sm font-medium mb-1",children:"Summary"}),(0,a.jsx)(O.g,{placeholder:"Enter summary",value:d.summary,onChange:e=>l("summary",e.target.value),required:!0}),f.summary&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.summary})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"storyUrls",className:"block text-sm font-medium mb-2",children:"Important URLs"}),d.importantUrls.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsxs)("div",{className:"flex w-full items-center gap-2",children:[(0,a.jsx)(P.I,{type:"text",placeholder:"URL Name",value:e.urlName,className:"w-1/2",onChange:s=>l("importantUrls",{...e,urlName:s.target.value},t),required:!0}),(0,a.jsx)(P.I,{type:"text",placeholder:"URL",value:e.url,className:"w-1/2",onChange:s=>l("importantUrls",{...e,url:s.target.value},t),required:!0})]}),(0,a.jsx)(o.z,{type:"button",variant:"ghost",onClick:()=>c(t),className:"text-red-500 px-1",children:(0,a.jsx)(p.Z,{})})]},t)),f.importantUrls&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.importantUrls}),(0,a.jsx)(o.z,{type:"button",variant:"secondary",onClick:m,className:"mt-2",children:"Add URL"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("label",{htmlFor:"storyStatus",className:"text-sm flex justify-center items-center font-medium mb-2 w-1/4",children:"Story Status"}),(0,a.jsxs)(T.h_,{children:[(0,a.jsx)(T.$F,{asChild:!0,children:(0,a.jsx)(o.z,{className:"w-3/4",variant:"outline",children:d.storyStatus?({NOT_STARTED:"Not Started",ONGOING:"On Going",COMPLETED:"Completed"})[d.storyStatus]:"Select Status"})}),(0,a.jsxs)(T.AW,{className:"w-56",children:[(0,a.jsx)(T.Xi,{onClick:()=>l("storyStatus","NOT_STARTED"),children:"Not Started"}),(0,a.jsx)(T.Xi,{onClick:()=>l("storyStatus","ONGOING"),children:"On Going"}),(0,a.jsx)(T.Xi,{onClick:()=>l("storyStatus","COMPLETED"),children:"Completed"})]})]}),f.storyStatus&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.storyStatus})]})]}),(0,a.jsxs)(g.cN,{className:"mt-4",children:[(0,a.jsx)(o.z,{type:"button",variant:"ghost",onClick:n,children:"Cancel"}),(0,a.jsx)(o.z,{type:"submit",children:"Submit"})]})]})]})})};let et=function(){let{setStoryData:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{setStoryData:()=>{}},[t,s]=(0,r.useState)(!1);return{isDialogOpen:t,handleOpenDialog:()=>{s(!0)},handleCloseDialog:()=>{s(!1)},handleStoryInputChange:(t,s,a)=>{e(e=>{if("importantUrls"===t&&"number"==typeof a){let t=[...e.importantUrls];return t[a]=s,{...e,importantUrls:t}}return{...e,[t]:s}})},handleAddUrl:()=>{e(e=>({...e,importantUrls:[...e.importantUrls,{urlName:"",url:""}]}))},handleRemoveUrl:t=>{e(e=>({...e,importantUrls:e.importantUrls.filter((e,s)=>s!==t)}))},setIsDialogOpen:s}};var es=e=>{var t,s,l;let{milestone:n,fetchMilestones:c,handleStorySubmit:m,isFreelancer:x=!1}=e,[f,h]=(0,r.useState)(void 0),[p,v]=(0,r.useState)(!1),[j,N]=(0,r.useState)(!1),[g,b]=(0,r.useState)({summary:"",title:"",taskStatus:"NOT_STARTED",freelancers:[{freelancerId:"",freelancerName:"",cost:0}]}),[y,w]=(0,r.useState)({title:"",summary:"",storyStatus:"",tasks:[],importantUrls:[{urlName:"",url:""}]}),{handleRemoveUrl:k,handleAddUrl:S,handleStoryInputChange:C}=et({setStoryData:w});return(0,a.jsxs)("div",{className:"w-full px-0 md:px-0 lg:px-0 py-3 md:py-6 max-w-5xl mx-auto rounded-lg",children:[(0,a.jsxs)(d.Zb,{className:"pb-5 mx-3",children:[(0,a.jsx)(u,{milestone:n}),(0,a.jsxs)(d.Zb,{className:" px-0 md:px-10  ".concat(f?"mx-0":"mx-4"," md:mx-3 my-2"),children:[(null!==(t=n.stories)&&void 0!==t?t:[]).length>0&&(0,a.jsxs)("div",{className:"flex p-4 justify-between items-center mt-4",children:[(0,a.jsx)("h3",{className:"text-lg md:text-xl font-semibold",children:"Stories:"}),!x&&(0,a.jsxs)(o.z,{className:"px-3 py-1 text-sm sm:text-base",onClick:()=>N(!0),children:[(0,a.jsx)(i.Z,{size:15})," Add Story"]})]}),(0,a.jsx)(Z.UQ,{type:"single",collapsible:!0,value:f,onValueChange:e=>h(e),className:"pt-4 px-0",children:(null!==(s=n.stories)&&void 0!==s?s:[]).length>0?(null!==(l=n.stories)&&void 0!==l?l:[]).map((e,t)=>{var s;return(0,a.jsx)(W,{fetchMilestones:c,isFreelancer:x,milestoneId:n._id,story:e,idx:t,milestoneStoriesLength:(null!==(s=n.stories)&&void 0!==s?s:[]).length,setIsTaskDialogOpen:v},t)}):(0,a.jsxs)("div",{className:"text-center mt-4 p-4 rounded-md",children:[x?(0,a.jsxs)("p",{children:["This ",n.title," currently has no associated stories. Please wait until the business adds a story."]}):(0,a.jsxs)("p",{children:["This ",n.title," currently has no associated stories. Please add a story to ensure smooth progress and effective tracking."]}),!x&&(0,a.jsxs)(o.z,{className:"mt-2 px-3 py-1 text-sm sm:text-base",onClick:()=>N(!0),children:[(0,a.jsx)(i.Z,{size:15})," Add Story"]})]})})]})]}),p&&(0,a.jsx)(Q,{isDialogOpen:p,setIsDialogOpen:v,formData:g,handleInputChange:e=>{let{name:t,value:s}="string"==typeof e?{name:"taskStatus",value:e}:e.target;b(e=>({...e,[t]:s}))},handelSubmit:e=>{if(e.preventDefault(),n.stories){let t=n.stories.find(e=>e._id===f);t&&m(e,t,n,!0,{formData:g,storyId:f})}b({summary:"",title:"",taskStatus:"NOT_STARTED",freelancers:[{freelancerId:"",freelancerName:"",cost:0}]}),v(!1)},handleFreelancerSelect:e=>{b(t=>({...t,freelancers:[{freelancerId:e._id,freelancerName:e.userName,cost:Number(e.perHourPrice)}]}))}}),j&&(0,a.jsx)(ee,{isDialogOpen:j,setIsDialogOpen:N,handleInputChange:C,handleCloseDialog:()=>N(!1),storyData:y,resetFields:()=>{w({title:"",summary:"",storyStatus:"",tasks:[],importantUrls:[{urlName:"",url:""}]})},handleRemoveUrl:k,handleAddUrl:S,milestones:n,handleStorySubmit:m})]})},ea=s(80023);let er=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return e.length>t?e.slice(0,t)+"...":e};var el=e=>{let{milestones:t,fetchMilestones:s,handleStorySubmit:i,isFreelancer:o=!1}=e,d=(0,r.useRef)(null),[c,m]=(0,r.useState)(0);(0,r.useEffect)(()=>{let e=d.current,t=t=>{if(e){let s=e.scrollWidth-e.clientWidth;Math.abs(t.deltaY)>Math.abs(t.deltaX)&&(t.preventDefault(),e.scrollLeft+t.deltaY>s?e.scrollLeft=s:e.scrollLeft+t.deltaY<0?e.scrollLeft=0:e.scrollLeft+=t.deltaY)}};return e&&e.addEventListener("wheel",t,{passive:!1}),()=>{e&&e.removeEventListener("wheel",t)}},[d]);let u=(e,s)=>{s<0||s>=t.length||m(s)},x=1===t.length?[...t,{_id:"dummy",title:"dummy",description:"",stories:[],storyStatus:"",createdAt:""}]:t;return(0,a.jsxs)("div",{className:"h-auto ",children:[(0,a.jsx)("div",{className:"w-[100vw] max-w-6xl mx-auto px-4 py-12 relative",children:(0,a.jsxs)(ea.x,{className:"w-full whitespace-nowrap rounded-md border",children:[t&&(0,a.jsxs)("div",{ref:d,className:"hidden md:block ",children:[(0,a.jsx)("div",{className:"absolute overflow-hidden left-0 right-0 top-1/2 h-1 line-bg bg-gray-500 transform -translate-y-1/2"}),(0,a.jsx)("div",{className:"relative cursor-pointer flex items-center whitespace-nowrap overflow-x-auto overflow-y-scroll px-4 py-6  no-scrollbar",children:x.map((e,s)=>(0,a.jsxs)("div",{className:"relative group px-16 inline-block ".concat(1===x.length?"mx-auto ":""),onClick:()=>u(e,s),children:[(0,a.jsx)("div",{className:"absolute ".concat(1===t.length&&"dummy"===e.title?"hidden":""," ").concat(s===c?"bg-[var(--dot-hover-bg-color)] border-[var(--dot-hover-border-color)]":"border-[var(--dot-border-color)]"," top-1/2 transform -translate-y-1/2 w-5 h-5 bg-[var(--dot-bg-color)] rounded-full border-4 group group-hover:bg-[var(--dot-hover-bg-color)] group-hover:border-[var(--dot-hover-border-color)] "),style:{left:"50%",transform:"translate(-50%, -50%)"},children:(0,a.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2 ".concat(s%2==0?"-top-6":"top-3"," ").concat(s===c?"bg-[#11a0ff] text-[#11a0ff]":"","  group-hover:text-[#11a0ff] overflow-hidden "),children:"|"})}),(0,a.jsx)(n,{date:e.createdAt||"",title:e.title,summary:e.description,position:s%2==0?"bottom":"top",isSelected:s===c}),"dummy"===e._id&&(0,a.jsx)("div",{style:{display:"none"}})]},s))})]}),(0,a.jsx)(ea.B,{className:"cursor-pointer",orientation:"horizontal"})]})}),(0,a.jsx)("div",{className:"flex pb-8 justify-center  items-center md:hidden",children:(0,a.jsx)(v.lr,{children:(0,a.jsx)(v.KI,{className:"flex min-h-[200px] items-center w-[100vw] gap-4",children:t.map((e,s)=>(0,a.jsx)(v.d$,{className:"flex relative justify-center top-0  h-auto items-center",onClick:()=>u(e,s+1),children:"dummy"!==e._id&&(0,a.jsxs)("div",{className:"border p-6 border-line-bg  rounded-lg shadow-lg  w-full max-w-[80vw]",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-xs",children:e.createdAt&&new Date(e.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}),(0,a.jsx)("h3",{className:"font-medium text-lg mt-2",children:er(e.title,16)}),e.description&&(0,a.jsx)("p",{className:"text-sm mt-1",children:er(e.description)})]}),(0,a.jsx)(v.am,{className:"absolute top-[117%] left-12 transform -translate-y-1/2",children:(0,a.jsx)("button",{onClick:()=>u(e,s-1),className:"bg-white/20 rounded-full p-3 text-white hover:bg-white/30",disabled:0===s,children:(0,a.jsx)(l.hf,{})})}),(0,a.jsx)(v.Pz,{className:"absolute top-[117%] right-8 transform -translate-y-1/2",children:(0,a.jsx)("button",{onClick:()=>u(e,s+1),className:"bg-white/20 rounded-full p-3 text-white ".concat(s===t.length-1?"cursor-not-allowed":"hover:bg-white/30"),disabled:s===t.length-1,children:(0,a.jsx)(l.Jp,{})})})]})},s))})})}),null!==c&&(0,a.jsx)("div",{className:"mt-10",children:(0,a.jsx)(es,{milestone:t[c],fetchMilestones:s,handleStorySubmit:i,isFreelancer:o})})]})}},52922:function(e,t,s){s.d(t,{Z:function(){return f}});var a=s(57437),r=s(2265),l=s(87138),n=s(63355),i=s(87592),o=(s(63550),s(49354));let d=r.forwardRef((e,t)=>{let{...s}=e;return(0,a.jsx)("nav",{ref:t,"aria-label":"breadcrumb",...s})});d.displayName="Breadcrumb";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("ol",{ref:t,className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",s),...r})});c.displayName="BreadcrumbList";let m=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("li",{ref:t,className:(0,o.cn)("inline-flex items-center gap-1.5",s),...r})});m.displayName="BreadcrumbItem";let u=r.forwardRef((e,t)=>{let{asChild:s,className:r,...l}=e,i=s?n.g7:"a";return(0,a.jsx)(i,{ref:t,className:(0,o.cn)("transition-colors hover:text-foreground",r),...l})});u.displayName="BreadcrumbLink",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("span",{ref:t,role:"link","aria-disabled":"true","aria-current":"page",className:(0,o.cn)("font-normal text-foreground",s),...r})}).displayName="BreadcrumbPage";let x=e=>{let{children:t,className:s,...r}=e;return(0,a.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,o.cn)("[&>svg]:size-3.5",s),...r,children:null!=t?t:(0,a.jsx)(i.Z,{})})};x.displayName="BreadcrumbSeparator";var f=e=>{let{items:t}=e;return(0,a.jsx)(d,{className:"hidden md:flex",children:(0,a.jsx)(c,{children:t.map((e,s)=>(0,a.jsxs)(r.Fragment,{children:[(0,a.jsx)(m,{children:(0,a.jsx)(u,{asChild:!0,children:(0,a.jsx)(l.default,{href:e.link,children:e.label})})}),s!==t.length-1&&(0,a.jsx)(x,{})]},s))})})}},35265:function(e,t,s){s.d(t,{Qd:function(){return d},UQ:function(){return o},o4:function(){return c},vF:function(){return m}});var a=s(57437),r=s(2265),l=s(64756),n=s(42421),i=s(49354);let o=l.fC,d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.ck,{ref:t,className:(0,i.cn)("border-b",s),...r})});d.displayName="AccordionItem";let c=r.forwardRef((e,t)=>{let{className:s,children:r,...o}=e;return(0,a.jsx)(l.h4,{className:"flex",children:(0,a.jsxs)(l.xz,{ref:t,className:(0,i.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",s),...o,children:[r,(0,a.jsx)(n.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})});c.displayName=l.xz.displayName;let m=r.forwardRef((e,t)=>{let{className:s,children:r,...n}=e;return(0,a.jsx)(l.VY,{ref:t,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...n,children:(0,a.jsx)("div",{className:(0,i.cn)("pb-4 pt-0",s),children:r})})});m.displayName=l.VY.displayName},80420:function(e,t,s){s.d(t,{Avatar:function(){return i},AvatarFallback:function(){return d},AvatarImage:function(){return o}});var a=s(57437),r=s(2265),l=s(44458),n=s(49354);let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.fC,{ref:t,className:(0,n.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...r})});i.displayName=l.fC.displayName;let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.Ee,{ref:t,className:(0,n.cn)("aspect-square h-full w-full",s),...r})});o.displayName=l.Ee.displayName;let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.NY,{ref:t,className:(0,n.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...r})});d.displayName=l.NY.displayName},79055:function(e,t,s){s.d(t,{C:function(){return i}});var a=s(57437);s(2265);var r=s(12218),l=s(49354);let n=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:s}),t),...r})}},48185:function(e,t,s){s.d(t,{Ol:function(){return i},SZ:function(){return d},Zb:function(){return n},aY:function(){return c},eW:function(){return m},ll:function(){return o}});var a=s(57437),r=s(2265),l=s(49354);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});n.displayName="Card";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...r})});i.displayName="CardHeader";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});o.displayName="CardTitle";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...r})});d.displayName="CardDescription";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...r})});c.displayName="CardContent";let m=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",s),...r})});m.displayName="CardFooter"},15642:function(e,t,s){s.d(t,{KI:function(){return x},Pz:function(){return p},am:function(){return h},d$:function(){return f},lr:function(){return u}});var a=s(57437),r=s(2265),l=s(74300),n=s(95137),i=s(71976),o=s(49354),d=s(89733);let c=r.createContext(null);function m(){let e=r.useContext(c);if(!e)throw Error("useCarousel must be used within a <Carousel />");return e}let u=r.forwardRef((e,t)=>{let{orientation:s="horizontal",opts:n,setApi:i,plugins:d,className:m,children:u,...x}=e,[f,h]=(0,l.Z)({...n,axis:"horizontal"===s?"x":"y"},d),[p,v]=r.useState(!1),[j,N]=r.useState(!1),g=r.useCallback(e=>{e&&(v(e.canScrollPrev()),N(e.canScrollNext()))},[]),b=r.useCallback(()=>{null==h||h.scrollPrev()},[h]),y=r.useCallback(()=>{null==h||h.scrollNext()},[h]),w=r.useCallback(e=>{"ArrowLeft"===e.key?(e.preventDefault(),b()):"ArrowRight"===e.key&&(e.preventDefault(),y())},[b,y]);return r.useEffect(()=>{h&&i&&i(h)},[h,i]),r.useEffect(()=>{if(h)return g(h),h.on("reInit",g),h.on("select",g),()=>{null==h||h.off("select",g)}},[h,g]),(0,a.jsx)(c.Provider,{value:{carouselRef:f,api:h,opts:n,orientation:s||((null==n?void 0:n.axis)==="y"?"vertical":"horizontal"),scrollPrev:b,scrollNext:y,canScrollPrev:p,canScrollNext:j},children:(0,a.jsx)("div",{ref:t,onKeyDownCapture:w,className:(0,o.cn)("relative",m),role:"region","aria-roledescription":"carousel",...x,children:u})})});u.displayName="Carousel";let x=r.forwardRef((e,t)=>{let{className:s,...r}=e,{carouselRef:l,orientation:n}=m();return(0,a.jsx)("div",{ref:l,className:"overflow-hidden",children:(0,a.jsx)("div",{ref:t,className:(0,o.cn)("flex","horizontal"===n?"-ml-4":"-mt-4 flex-col",s),...r})})});x.displayName="CarouselContent";let f=r.forwardRef((e,t)=>{let{className:s,...r}=e,{orientation:l}=m();return(0,a.jsx)("div",{ref:t,role:"group","aria-roledescription":"slide",className:(0,o.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===l?"pl-4":"pt-4",s),...r})});f.displayName="CarouselItem";let h=r.forwardRef((e,t)=>{let{className:s,variant:r="outline",size:l="icon",...i}=e,{orientation:c,scrollPrev:u,canScrollPrev:x}=m();return(0,a.jsx)(d.z,{ref:t,variant:r,size:l,className:(0,o.cn)("absolute  h-8 w-8 rounded-full","horizontal"===c?"-left-12 top-1/2 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",s),onClick:u,disabled:!x,...i,children:(0,a.jsx)(n.Z,{className:"h-4 w-4"})})});h.displayName="CarouselPrevious";let p=r.forwardRef((e,t)=>{let{className:s,variant:r="outline",size:l="icon",...n}=e,{orientation:c,scrollNext:u,canScrollNext:x}=m();return(0,a.jsxs)(d.z,{ref:t,variant:r,size:l,className:(0,o.cn)("absolute h-8 w-8 rounded-full","horizontal"===c?"-right-12 top-1/2 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",s),disabled:!x,onClick:u,...n,children:[(0,a.jsx)(i.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Next slide"})]})});p.displayName="CarouselNext"},54662:function(e,t,s){s.d(t,{$N:function(){return p},Be:function(){return v},GG:function(){return m},Vq:function(){return o},cN:function(){return h},cZ:function(){return x},fK:function(){return f},hg:function(){return d}});var a=s(57437),r=s(2265),l=s(32226),n=s(74697),i=s(49354);let o=l.fC,d=l.xz,c=l.h_,m=l.x8,u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.aV,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r})});u.displayName=l.aV.displayName;let x=r.forwardRef((e,t)=>{let{className:s,children:r,...o}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(u,{}),(0,a.jsxs)(l.VY,{ref:t,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...o,children:[r,(0,a.jsxs)(l.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground text-red-500",children:[(0,a.jsx)(n.Z,{className:"h-4 w-4",strokeWidth:4}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});x.displayName=l.VY.displayName;let f=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};f.displayName="DialogHeader";let h=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...s})};h.displayName="DialogFooter";let p=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.Dx,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",s),...r})});p.displayName=l.Dx.displayName;let v=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.dk,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",s),...r})});v.displayName=l.dk.displayName},8555:function(e,t,s){s.d(t,{Yi:function(){return o},bZ:function(){return d},zs:function(){return i}});var a=s(57437),r=s(2265),l=s(68602),n=s(49354);let i=l.fC,o=l.xz,d=r.forwardRef((e,t)=>{let{className:s,align:r="center",sideOffset:i=4,...o}=e;return(0,a.jsx)(l.VY,{ref:t,align:r,sideOffset:i,className:(0,n.cn)("z-50 w-64 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...o})});d.displayName=l.VY.displayName},21413:function(e,t,s){s.d(t,{J2:function(){return i},xo:function(){return o},yk:function(){return d}});var a=s(57437),r=s(2265),l=s(7568),n=s(49354);let i=l.fC,o=l.xz,d=r.forwardRef((e,t)=>{let{className:s,align:r="center",sideOffset:i=4,...o}=e;return(0,a.jsx)(l.h_,{children:(0,a.jsx)(l.VY,{ref:t,align:r,sideOffset:i,className:(0,n.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...o})})});d.displayName=l.VY.displayName},80023:function(e,t,s){s.d(t,{B:function(){return o},x:function(){return i}});var a=s(57437),r=s(2265),l=s(37193),n=s(49354);let i=r.forwardRef((e,t)=>{let{className:s,children:r,...i}=e;return(0,a.jsxs)(l.fC,{ref:t,className:(0,n.cn)("relative overflow-hidden",s),...i,children:[(0,a.jsx)(l.l_,{className:"h-full w-full rounded-[inherit]",children:r}),(0,a.jsx)(o,{}),(0,a.jsx)(l.Ns,{})]})});i.displayName=l.fC.displayName;let o=r.forwardRef((e,t)=>{let{className:s,orientation:r="vertical",...i}=e;return(0,a.jsx)(l.gb,{ref:t,orientation:r,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===r&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",s),...i,children:(0,a.jsx)(l.q4,{className:"relative flex-1 rounded-full bg-border"})})});o.displayName=l.gb.displayName},2128:function(e,t,s){s.d(t,{Bw:function(){return p},DI:function(){return m},Ph:function(){return c},Ql:function(){return v},i4:function(){return x},ki:function(){return u}});var a=s(57437),r=s(2265),l=s(48362),n=s(42421),i=s(14392),o=s(22468),d=s(49354);let c=l.fC,m=l.ZA,u=l.B4,x=r.forwardRef((e,t)=>{let{className:s,children:r,...i}=e;return(0,a.jsxs)(l.xz,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...i,children:[r,(0,a.jsx)(l.JO,{asChild:!0,children:(0,a.jsx)(n.Z,{className:"h-4 w-4 opacity-50"})})]})});x.displayName=l.xz.displayName;let f=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.u_,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})});f.displayName=l.u_.displayName;let h=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.$G,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(n.Z,{className:"h-4 w-4"})})});h.displayName=l.$G.displayName;let p=r.forwardRef((e,t)=>{let{className:s,children:r,position:n="popper",...i}=e;return(0,a.jsx)(l.h_,{children:(0,a.jsxs)(l.VY,{ref:t,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...i,children:[(0,a.jsx)(f,{}),(0,a.jsx)(l.l_,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(h,{})]})})});p.displayName=l.VY.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.__,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...r})}).displayName=l.__.displayName;let v=r.forwardRef((e,t)=>{let{className:s,children:r,...n}=e;return(0,a.jsxs)(l.ck,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.wU,{children:(0,a.jsx)(o.Z,{className:"h-4 w-4"})})}),(0,a.jsx)(l.eT,{children:r})]})});v.displayName=l.ck.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.Z0,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",s),...r})}).displayName=l.Z0.displayName},55936:function(e,t,s){s.d(t,{Ei:function(){return N},FF:function(){return v},Tu:function(){return p},aM:function(){return c},bC:function(){return j},sw:function(){return m},ue:function(){return h},yo:function(){return d}});var a=s(57437),r=s(2265),l=s(32226),n=s(12218),i=s(74697),o=s(49354);let d=l.fC,c=l.xz,m=l.x8,u=l.h_,x=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.aV,{className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r,ref:t})});x.displayName=l.aV.displayName;let f=(0,n.j)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),h=r.forwardRef((e,t)=>{let{side:s="right",className:r,children:n,...d}=e;return(0,a.jsxs)(u,{children:[(0,a.jsx)(x,{}),(0,a.jsxs)(l.VY,{ref:t,className:(0,o.cn)(f({side:s}),r),...d,children:[n,(0,a.jsxs)(l.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,a.jsx)(i.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});h.displayName=l.VY.displayName;let p=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-2 text-center sm:text-left",t),...s})};p.displayName="SheetHeader";let v=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...s})};v.displayName="SheetFooter";let j=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.Dx,{ref:t,className:(0,o.cn)("text-lg font-semibold text-foreground",s),...r})});j.displayName=l.Dx.displayName;let N=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.dk,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",s),...r})});N.displayName=l.dk.displayName},47304:function(e,t,s){s.d(t,{RM:function(){return o},SC:function(){return d},iA:function(){return n},pj:function(){return m},ss:function(){return c},xD:function(){return i}});var a=s(57437),r=s(2265),l=s(49354);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:t,className:(0,l.cn)("w-full caption-bottom text-sm",s),...r})})});n.displayName="Table";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("thead",{ref:t,className:(0,l.cn)("[&_tr]:border-b",s),...r})});i.displayName="TableHeader";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("tbody",{ref:t,className:(0,l.cn)("[&_tr:last-child]:border-0",s),...r})});o.displayName="TableBody",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("tfoot",{ref:t,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...r})}).displayName="TableFooter";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("tr",{ref:t,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...r})});d.displayName="TableRow";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("th",{ref:t,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),...r})});c.displayName="TableHead";let m=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("td",{ref:t,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),...r})});m.displayName="TableCell",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("caption",{ref:t,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",s),...r})}).displayName="TableCaption"},4919:function(e,t,s){s.d(t,{g:function(){return n}});var a=s(57437),r=s(2265),l=s(49354);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...r})});n.displayName="Textarea"},89736:function(e,t,s){s.d(t,{TooltipProvider:function(){return i},_v:function(){return c},aJ:function(){return d},u:function(){return o}});var a=s(57437),r=s(2265),l=s(60364),n=s(49354);let i=l.zt,o=l.fC,d=l.xz,c=r.forwardRef((e,t)=>{let{className:s,sideOffset:r=4,...i}=e;return(0,a.jsx)(l.VY,{ref:t,sideOffset:r,className:(0,n.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...i})});c.displayName=l.VY.displayName},86763:function(e,t,s){s.d(t,{Am:function(){return c}}),s(2265);let a=0,r=new Map,l=e=>{if(r.has(e))return;let t=setTimeout(()=>{r.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);r.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?l(s):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},i=[],o={toasts:[]};function d(e){o=n(o,e),i.forEach(e=>{e(o)})}function c(e){let{...t}=e,s=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>d({type:"DISMISS_TOAST",toastId:s});return d({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||r()}}}),{id:s,dismiss:r,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:s}})}}},3483:function(e,t,s){s.d(t,{av:function(){return r},pn:function(){return l}});let a=(0,s(69753).oM)({name:"user",initialState:{},reducers:{setUser:(e,t)=>({...e,...t.payload}),clearUser:()=>({})}}),{setUser:r,clearUser:l}=a.actions;t.ZP=a.reducer},44475:function(e,t,s){s.d(t,{S:function(){return a}});let a=e=>{switch(null==e?void 0:e.toLowerCase()){case"active":return{text:"ACTIVE",className:"bg-blue-500 hover:bg-blue-600"};case"pending":return{text:"PENDING",className:"bg-warning hover:bg-warning"};case"completed":return{text:"COMPLETED",className:"bg-success hover:bg-success"};case"rejected":return{text:"REJECTED",className:"bg-red-500 hover:bg-red-600"};case"ongoing":return{text:"ON-GOING",className:"bg-warning hover:bg-warning"};case"not_started":return{text:"NOT-STARTED",className:"bg-blue-500 hover:bg-blue-600"};default:return{text:"UNKNOWN",className:"bg-gray-500 hover:bg-gray-600"}}}}}]);