"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6755],{5891:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},20897:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},76035:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},43193:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]])},13231:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},71935:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},49100:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},47390:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},36141:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},98960:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},33907:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},73347:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},33149:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]])},40064:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("TabletSmartphone",[["rect",{width:"10",height:"14",x:"3",y:"8",rx:"2",key:"1vrsiq"}],["path",{d:"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4",key:"1j4zmg"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},16717:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},10883:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},5498:function(e,t,a){a.d(t,{Z:function(){return v}});var n=a(57437),l=a(2265),s=a(89733),r=a(31590),i=a(92513),c=a(66648),o=a(49354);let d=["#ffffff","#f28b82","#fbbc04","#fff475","#ccff90","#a7ffeb","#cbf0f8","#aecbfa","#d7aefb","#fdcfe8"];function h(e){let{selectedColor:t,onColorSelect:a}=e;return(0,n.jsx)("div",{className:"flex gap-2 flex-wrap",children:d.map(e=>(0,n.jsx)("button",{onClick:()=>a(e),className:(0,o.cn)("w-6 h-6 rounded-full border transition-all",t===e&&"ring-2 ring-offset-2 ring-black"),style:{backgroundColor:e},"aria-label":"Select color ".concat(e)},e))})}var u=a(54662),m=a(77209),x=a(4919),f=a(83640);let p=["/banner1.svg","/banner2.svg","/banner3.svg","/banner4.svg","/banner5.svg","/banner6.svg","/banner7.svg"];function b(e){let{onNoteCreate:t}=e,[a,r]=(0,l.useState)(""),[o,d]=(0,l.useState)(""),[b,v]=(0,l.useState)("#ffffff"),[g,j]=(0,l.useState)(null),[y,N]=(0,l.useState)(!1),[k,w]=(0,l.useState)(!1);return(0,n.jsxs)(u.Vq,{open:y,onOpenChange:N,children:[(0,n.jsx)(u.hg,{asChild:!0,children:(0,n.jsxs)(s.z,{className:"gap-2",children:[(0,n.jsx)(i.Z,{className:"h-4 w-4"}),"Add"]})}),(0,n.jsxs)(u.cZ,{className:"sm:max-w-[425px]",children:[(0,n.jsx)(u.$N,{children:"Create New Note"}),(0,n.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,n.jsx)(m.I,{placeholder:"Title",value:a,onChange:e=>r(e.target.value)}),(0,n.jsx)(x.g,{placeholder:"Take a note...",value:o,onChange:e=>d(e.target.value),className:"min-h-[100px] resize-none"}),(0,n.jsxs)("label",{className:"flex items-center gap-2",children:[(0,n.jsx)("input",{type:"checkbox",checked:k,onChange:e=>w(e.target.checked),className:"cursor-pointer size-3 text-xs"}),(0,n.jsx)("span",{className:"text-xs",children:"Render content as HTML"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium mb-2",children:"Select a color or banner:"}),(0,n.jsx)(h,{selectedColor:b,onColorSelect:e=>{v(e),j(null)}}),(0,n.jsx)("div",{className:"grid grid-cols-8 gap-2 mt-4",children:p.map((e,t)=>(0,n.jsx)("div",{onClick:()=>{j(e),v("#ffffff")},className:"cursor-pointer border rounded-full p-1 flex items-center justify-center ".concat(g===e?"border-blue-500":"border-gray-300"),children:(0,n.jsx)(c.default,{src:e,alt:"Banner ".concat(t+1),width:32,height:32,className:"object-cover rounded-full"})},t))})]}),(0,n.jsx)(s.z,{onClick:()=>{if(!a.trim()&&!o.trim())return;let e={title:a.trim(),content:o.trim(),bgColor:g?void 0:b,banner:g||void 0,createdAt:new Date,noteType:f.wK.NOTE,isHTML:k};t&&t(e),r(""),d(""),v("#ffffff"),j(null),N(!1)},className:"w-full",children:"Save Note"})]})]})]})}var v=e=>{let{onNoteCreate:t,notes:a,setNotes:i,isTrash:c}=e,[o,d]=(0,l.useState)(""),h=()=>{i([...a].sort((e,t)=>(e.bgColor||"").localeCompare(t.bgColor||"")))},u=()=>{i([...a].sort((e,t)=>{let a=e.createdAt?new Date(e.createdAt).getTime():0;return(t.createdAt?new Date(t.createdAt).getTime():0)-a}))},m=()=>{i([...a].sort((e,t)=>(e.createdAt?new Date(e.createdAt).getTime():0)-(t.createdAt?new Date(t.createdAt).getTime():0)))};return(0,n.jsx)("div",{children:(0,n.jsxs)("div",{className:"mb-8 ml-6 mt-8 flex justify-between items-center",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold",children:"Notes"}),(0,n.jsx)("p",{className:"text-gray-400 mt-2 hidden md:block ",children:"Organize your thoughts and ideas. Add, view, and manage your personal notes with ease."})]}),!c&&(0,n.jsx)("div",{className:"mt-4 mr-5",children:(0,n.jsxs)("div",{className:"flex justify-center gap-5 items-center flex-wrap mt-4 sm:mt-0",children:[(0,n.jsxs)(r.h_,{children:[(0,n.jsx)(r.$F,{asChild:!0,children:(0,n.jsx)(s.z,{variant:"outline",className:"sm:text-sm md:text-base",children:"Sort"})}),(0,n.jsxs)(r.AW,{className:"w-56  shadow-md rounded-md border",children:[(0,n.jsx)(r.Ju,{className:"",children:"Sort Notes"}),(0,n.jsx)(r.VD,{}),(0,n.jsx)(r.bO,{checked:"color"===o,onCheckedChange:()=>{d("color"),h()},children:"Sort by color"}),(0,n.jsx)(r.bO,{checked:"latest"===o,onCheckedChange:()=>{d("latest"),u()},children:"Sort by latest"}),(0,n.jsx)(r.bO,{checked:"oldest"===o,onCheckedChange:()=>{d("oldest"),m()},children:"Sort by oldest"})]})]}),!c&&(0,n.jsx)(b,{onNoteCreate:t})]})})]})})}},85121:function(e,t,a){a.d(t,{Z:function(){return L}});var n=a(57437),l=a(2265),s=a(6649),r=a(10883),i=a(33480);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,i.Z)("Recycle",[["path",{d:"M7 19H4.815a1.83 1.83 0 0 1-1.57-.881 1.785 1.785 0 0 1-.004-1.784L7.196 9.5",key:"x6z5xu"}],["path",{d:"M11 19h8.203a1.83 1.83 0 0 0 1.556-.89 1.784 1.784 0 0 0 0-1.775l-1.226-2.12",key:"1x4zh5"}],["path",{d:"m14 16-3 3 3 3",key:"f6jyew"}],["path",{d:"M8.293 13.596 7.196 9.5 3.1 10.598",key:"wf1obh"}],["path",{d:"m9.344 5.811 1.093-1.892A1.83 1.83 0 0 1 11.985 3a1.784 1.784 0 0 1 1.546.888l3.943 6.843",key:"9tzpgr"}],["path",{d:"m13.378 9.633 4.096 1.098 1.097-4.096",key:"1oe83g"}]]);var o=a(16717);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,i.Z)("RotateCw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]]),h=(0,i.Z)("ArchiveRestore",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h2",key:"tvwodi"}],["path",{d:"M20 8v11a2 2 0 0 1-2 2h-2",key:"1gkqxj"}],["path",{d:"m9 15 3-3 3 3",key:"1pd0qc"}],["path",{d:"M12 12v9",key:"192myk"}]]);var u=a(79055);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,i.Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);var x=a(66648),f=a(8555),p=e=>{let{handleChangeBanner:t}=e;return(0,n.jsxs)(f.zs,{children:[(0,n.jsx)(f.Yi,{asChild:!0,children:(0,n.jsx)("button",{children:(0,n.jsx)(m,{size:15,className:"text-black transition-all duration-200"})})}),(0,n.jsx)(f.bZ,{className:"shadow-md rounded-md p-3",children:(0,n.jsx)("div",{className:"flex flex-nowrap justify-center gap-1",children:["/banner1.svg","/banner2.svg","/banner3.svg","/banner4.svg","/banner5.svg","/banner6.svg","/banner7.svg"].map((e,a)=>(0,n.jsx)("div",{onClick:()=>t(e),className:"cursor-pointer p-1 rounded",children:(0,n.jsx)(x.default,{src:e,alt:"Banner ".concat(a+1),width:28,height:28,className:"rounded-md hover:scale-110 transition-transform"})},a))})})]})},b=a(45188),v=e=>{let{items:t}=e,[a,s]=(0,l.useState)(!1),r=(0,l.useRef)(null),[i,c]=(0,l.useState)("bottom");return(0,l.useEffect)(()=>{let e=e=>{r.current&&!r.current.contains(e.target)&&s(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,l.useEffect)(()=>{a&&setTimeout(()=>{if(r.current){let e=r.current.getBoundingClientRect(),t=window.innerWidth,a=window.innerHeight;e.bottom+150>a?c("top"):e.right+150>t?c("left"):e.left-150<0?c("right"):c("bottom")}},10)},[a]),(0,n.jsxs)("div",{className:"relative",ref:r,children:[(0,n.jsx)("button",{onClick:e=>{e.stopPropagation(),s(!a)},className:" rounded-md text-black",children:(0,n.jsx)(b.Z,{size:16})}),a&&(0,n.jsx)("div",{className:"absolute w-24 rounded-md shadow-md bg-white dark:bg-black border border-gray-300 dark:border-gray-700 transition-all duration-200 ease-in-out overflow-visible ".concat("top"===i?"bottom-full mb-2":"left"===i?"right-full mr-2":"right"===i?"left-full ml-2":"top-full mt-1"," z-[9999]"),style:{transform:"translateZ(0)"},children:t.map((e,t)=>(0,n.jsxs)("button",{onClick:t=>{t.stopPropagation(),e.onClick(),s(!1)},className:"flex items-center w-full px-1 py-1 text-sm text-black dark:text-white hover:bg-gray-200 dark:hover:bg-gray-800",children:[e.icon&&(0,n.jsx)("span",{className:"mr-2",children:e.icon}),e.label]},t))})]})},g=e=>{let{navItems:t}=e;return(0,n.jsx)("div",{className:"relative overflow-visible",children:(0,n.jsx)(v,{items:t})})},j=a(48185),y=a(83640);function N(e,t){let a=null==e?void 0:e.split(/\s+/);return(null==a?void 0:a.length)>t?"".concat(a.slice(0,t).join(" "),"..."):e}var k=e=>{let{note:t,notes:a,setNotes:l,onDragStart:s,onDragOver:i,onDrop:c,isTrash:o,isArchive:m,onEditNote:x,onUpdateNoteType:f,onDeleteClick:b,onChangeBanner:v,navItems:k}=e;return(0,n.jsx)("div",{draggable:!0,onDragStart:s,onDragOver:i,onDrop:c,className:"relative group",children:(0,n.jsxs)(j.Zb,{className:"break-inside-avoid cursor-pointer bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 group w-[80vw] mb-3 md:w-[200px] relative",style:t.banner?{backgroundImage:"url(".concat(t.banner,")"),backgroundSize:"cover",backgroundPosition:"center"}:{backgroundColor:t.bgColor||"#ffffff"},children:[(0,n.jsxs)("div",{onClick:()=>x(t),children:[(0,n.jsxs)(j.Ol,{children:[t.type&&(0,n.jsx)("div",{className:"absolute top-1 left-1",children:(0,n.jsx)(u.C,{className:"text-xs py-0.5 ".concat(y.EH[t.type]||" "),children:t.type.toLowerCase()})}),t.title&&(0,n.jsx)(j.ll,{className:"font-semibold text-lg text-black mt-6",children:t.title})]}),(0,n.jsx)(j.aY,{className:"max-h-[320px] overflow-hidden",children:(0,n.jsx)(j.SZ,{className:"text-sm whitespace-pre-wrap truncate break-words",children:t.isHTML?(0,n.jsx)("div",{className:"text-sm whitespace-pre-wrap break-words",dangerouslySetInnerHTML:{__html:function(e,t){try{let t=document.createElement("div");if(t.innerHTML=e,t.textContent===e)return N(e,30);let a=t.textContent||t.innerText||"";return N(a,30)}catch(t){return console.error("Error processing input as HTML:",t),N(e,30)}}(t.content,30)}}):(0,n.jsx)(j.SZ,{className:"text-sm font-bold truncate bg-opacity-100 whitespace-pre-wrap break-words text-black",children:N(t.content,30)})})})]}),(0,n.jsx)("div",{className:"relative group",children:(0,n.jsxs)("div",{className:"absolute bottom-2 right-2 hidden group-hover:flex items-center gap-4 justify-center",children:[o?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d,{size:15,className:"text-black cursor-pointer",onClick:()=>f(t._id,y.wK.NOTE)}),(0,n.jsx)(r.Z,{size:15,className:"text-black cursor-pointer",onClick:()=>b(t._id)})]}):m?(0,n.jsx)(h,{size:15,className:"text-black cursor-pointer",onClick:()=>f(t._id,y.wK.NOTE)}):(0,n.jsx)(h,{size:15,className:"text-black cursor-pointer",onClick:()=>f(t._id,y.wK.ARCHIVE)}),(0,n.jsx)(p,{handleChangeBanner:e=>v(t._id,e)}),!o&&(0,n.jsx)(g,{navItems:k.map(e=>({...e,icon:e.icon,onClick:()=>e.onClick(t._id,a,l)}))})]})})]})})},w=a(54662),C=a(89733),Z=e=>{let{note:t,onClose:a,onDelete:l}=e;return(0,n.jsx)(w.Vq,{open:!0,onOpenChange:a,children:(0,n.jsxs)(w.cZ,{className:"sm:max-w-[425px] p-6 rounded-lg shadow-lg",children:[(0,n.jsxs)(w.fK,{className:"border-b pb-4",children:[(0,n.jsx)(w.$N,{className:"text-2xl font-semibold",children:"Confirm Deletion"}),(0,n.jsxs)(w.Be,{className:"mt-2 text-sm text-gray-600",children:["Are you sure you want to delete the note titled"," ",(0,n.jsx)("strong",{children:null==t?void 0:t.title}),"? This action cannot be undone."]})]}),(0,n.jsxs)(w.cN,{className:"mt-6",children:[(0,n.jsx)(C.z,{variant:"outline",onClick:a,className:"mr-2",children:"Cancel"}),(0,n.jsx)(C.z,{variant:"destructive",onClick:()=>l((null==t?void 0:t._id)||null),children:"Delete"})]})]})})},S=a(77209),E=a(4919),T=e=>{let{note:t,onClose:a,onSave:s}=e,[r,i]=(0,l.useState)(t.title||""),[c,o]=(0,l.useState)(t.content||""),[d]=(0,l.useState)(t.entityID||""),[h,u]=(0,l.useState)(""),[m,f]=(0,l.useState)(!1);return(0,n.jsx)(w.Vq,{open:!0,onOpenChange:a,children:(0,n.jsxs)(w.cZ,{className:"sm:max-w-[425px] p-6 rounded-lg shadow-lg text-black",style:{backgroundColor:"".concat(t.bgColor,"B3")||0},children:[t.banner&&(0,n.jsx)("div",{className:"absolute inset-0 z-0",children:(0,n.jsx)(x.default,{src:t.banner,alt:"Note Banner",layout:"fill",objectFit:"cover",className:"rounded-lg"})}),(0,n.jsx)(w.fK,{className:"border-b pb-4 relative z-10",children:(0,n.jsx)(w.$N,{className:"text-2xl font-semibold",children:m?"Edit Note":"Note Details"})}),h&&(0,n.jsx)("div",{className:"text-red-500 text-sm mb-4",children:h}),m?(0,n.jsxs)("div",{className:"mt-6 relative z-10",children:[(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("label",{htmlFor:"title",className:"block text-sm font-semibold",children:"Title"}),(0,n.jsx)(S.I,{id:"title",type:"text",required:!0,value:r,onChange:e=>i(e.target.value),placeholder:"Enter note title",className:"mt-2 p-2 border bg-opacity-50 bg-white rounded-md w-full text-sm text-opacity-100"})]}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("label",{htmlFor:"content",className:"block text-sm font-semibold",children:"Content"}),(0,n.jsx)(E.g,{id:"content",value:c,onChange:e=>o(e.target.value),placeholder:"Enter note content",className:"mt-2 p-2 bg-opacity-50 bg-white border  no-scrollbar rounded-md w-full text-sm text-opacity-100  resize-none",rows:5})]}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("label",{htmlFor:"entityID",className:"block text-sm font-semibold",children:"Entity ID"}),(0,n.jsx)(S.I,{id:"entityID",type:"text",disabled:!0,value:d,placeholder:"Entity ID",className:"mt-2 p-2 border bg-opacity-50 bg-white rounded-md w-full text-sm text-opacity-100"})]})]}):(0,n.jsxs)("div",{className:"mt-6 relative z-10 w-full max-w-2xl",children:[(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("p",{className:"text-sm font-bold",children:"Title:"}),(0,n.jsx)("p",{className:"text-black-300 mt-1",children:t.title})]}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("p",{className:"text-sm font-bold",children:"Content:"}),(0,n.jsx)("div",{className:"text-black-300 mt-1 no-scrollbar p-2 ".concat(t.content.length>300?"max-h-52 overflow-y-auto":""),children:t.content})]}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("p",{className:"text-sm font-bold",children:"Entity ID:"}),(0,n.jsx)("p",{className:"text-black-300 mt-1",children:d})]})]}),(0,n.jsx)(w.cN,{className:"mt-6 relative z-10 text-white",children:m?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(C.z,{variant:"outline",onClick:()=>f(!1),className:"mr-2",children:"Cancel"}),(0,n.jsx)(C.z,{onClick:()=>{if(!r.trim()||!c.trim()){u("Title and content cannot be empty.");return}u(""),s({...t,title:r,content:c}),f(!1)},children:"Save"})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(C.z,{variant:"outline",onClick:a,className:"mr-2 text-black dark:text-white",children:"Close"}),(0,n.jsx)(C.z,{onClick:()=>f(!0),children:"Edit"})]})})]})})},A=a(2128),M=e=>{let{note:t,onClose:a,onUpdate:s}=e,[r,i]=(0,l.useState)(t.type||"");return(0,n.jsx)(w.Vq,{open:!!t,onOpenChange:a,children:(0,n.jsxs)(w.cZ,{children:[(0,n.jsx)(w.fK,{children:(0,n.jsx)(w.$N,{children:"Update Note Type"})}),(0,n.jsxs)(A.Ph,{onValueChange:e=>i(e),value:r,children:[(0,n.jsx)(A.i4,{children:(0,n.jsx)(A.ki,{placeholder:"Select type"})}),(0,n.jsxs)(A.Bw,{children:[(0,n.jsx)(A.Ql,{value:"PERSONAL",children:"Personal"}),(0,n.jsx)(A.Ql,{value:"WORK",children:"Work"}),(0,n.jsx)(A.Ql,{value:"REMINDER",children:"reminder"}),(0,n.jsx)(A.Ql,{value:"TASK",children:"task"})]})]}),(0,n.jsxs)(w.cN,{children:[(0,n.jsx)(C.z,{variant:"secondary",onClick:a,children:"Cancel"}),(0,n.jsx)(C.z,{onClick:()=>{r&&s(t._id,r.toUpperCase())},children:"Update"})]})]})})},z=a(15922),D=a(78068),I=(e,t)=>{let[a,n]=(0,l.useState)(null),[s,r]=(0,l.useState)(null),[i,c]=(0,l.useState)(null),[o,d]=(0,l.useState)(!1),h=e=>{(0,D.Am)({title:"Error",description:e,variant:"destructive",duration:5e3})},u=e=>{(0,D.Am)({description:e,duration:5e3})},m=async t=>{if(!t._id){h("Missing required fields for updating the note.");return}console.log(t);try{let e=await z.b.put("/notes/".concat(t._id),{title:t.title,content:t.content,bgColor:t.bgColor||"#FFFFFF",banner:t.banner||"",isHTML:t.isHTML||!1,entityID:t.entityID||"",entityType:t.entityType||"",noteType:(null==t?void 0:t.noteType)||y.wK.NOTE,type:(null==t?void 0:t.type)||y.JG.PERSONAL});(null==e?void 0:e.status)===200&&u("Note updated successfully.")}catch(e){h("Failed to update the note.")}finally{await e(),n(null)}},x=async t=>{if(!t){h("Invalid note ID.");return}try{await z.b.delete("/notes/".concat(t)),u("Note deleted permanently."),e()}catch(e){h("Failed to delete the note."),(0,D.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}d(!1)};return{selectedNote:a,setSelectedNote:n,selectedDeleteNote:s,setSelectedDeleteNote:r,selectedTypeNote:i,setSelectedTypeNote:c,isDeleting:o,setIsDeleting:d,handleSaveEditNote:m,handleDialogClose:()=>{n(null),d(!1)},handleDeletePermanently:x,handleChangeBanner:async(a,n)=>{let l=t.find(e=>e._id===a);if(!l){h("Note not found.");return}try{let t=await z.b.put("/notes/".concat(l._id),{...l,banner:n});(null==t?void 0:t.status)==200&&u("Note Banner updated"),await e()}catch(e){h("Failed to update the note banner."),(0,D.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.log(e)}},handleUpdateNoteType:async(a,n)=>{let l=t.find(e=>e._id===a);if(!l){h("Note not found.");return}try{let t=await z.b.put("/notes/".concat(l._id),{...l,noteType:n});(null==t?void 0:t.status)==200&&u("Note moved to ".concat(n.toLowerCase())),await e()}catch(e){h("Failed to update the note label."),console.log(e)}},handleUpdateNoteLabel:async(a,n)=>{let l=t.find(e=>e._id===a);if(!l){h("Note not found.");return}try{let t=await z.b.put("/notes/".concat(l._id),{...l,type:n});(null==t?void 0:t.status)==200&&u("Note Label updated"),await e()}catch(e){h("Failed to update the note label."),console.log(e)}}}},R=(e,t)=>{let[a,n]=(0,l.useState)(null),[s,r]=(0,l.useState)(null),i=async()=>{if(null!==a&&null!==s&&a!==s){var l;let n=[...e],r=n[a],i=n[s];n[a]=i,n[s]=r,t(n);let c=n.map(e=>e._id),o=null===(l=n[0])||void 0===l?void 0:l.userId;if(o)try{let e=await z.b.patch("/notes/update-noteorder",{userId:o,noteOrder:c});200===e.status?console.log("Notes order updated successfully:",e.data):(console.error("Failed to update note order:",e.statusText),(0,D.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}))}catch(e){console.error("Error updating note order:",e.message),console.log(e),(0,D.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}else console.error("User ID is missing. Cannot update note order."),(0,D.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}n(null),r(null)};return{draggingIndex:a,draggingOverIndex:s,handleDragStart:e=>{n(e)},handleDragOver:e=>{a!==e&&r(e)},handleDrop:i}},O=e=>{let{notes:t,setNotes:a,isArchive:l,isTrash:i,fetchNotes:d}=e,{selectedNote:h,setSelectedNote:u,selectedDeleteNote:m,setSelectedDeleteNote:x,selectedTypeNote:f,setSelectedTypeNote:p,isDeleting:b,setIsDeleting:v,handleSaveEditNote:g,handleDialogClose:j,handleDeletePermanently:N,handleChangeBanner:w,handleUpdateNoteType:C,handleUpdateNoteLabel:S}=I(d,t),{handleDragStart:E,handleDragOver:A,handleDrop:z}=R(t,a),D=[{label:"Edit",icon:(0,n.jsx)(s.Z,{size:15,className:"text-white-500"}),onClick:(e,t)=>{u(t.find(t=>t._id===e)||null)}},{label:"Delete",icon:(0,n.jsx)(r.Z,{size:15,className:"text-white-500"}),onClick:(e,t)=>{v(!0),x(t.find(t=>t._id===e)||null)}},{label:"Recycle",icon:(0,n.jsx)(c,{size:15,className:"text-white-500"}),onClick:e=>{C(e,y.wK.TRASH)}},{label:"Label",icon:(0,n.jsx)(o.Z,{size:15,className:"text-white-500"}),onClick:(e,t)=>{p(t.find(t=>t._id===e)||null)}}];return(0,n.jsxs)("div",{className:"flex justify-center items-center",children:[(0,n.jsx)("div",{className:"columns-1 mt-3 sm:columns-2 md:columns-3 lg:columns-5 gap-6",children:t.map((e,s)=>(0,n.jsx)(k,{note:e,onDragStart:()=>E(s),onDragOver:e=>{e.preventDefault(),A(s)},onDrop:z,notes:t,setNotes:a,isTrash:!!i,isArchive:l,onEditNote:u,onUpdateNoteType:C,onDeleteClick:e=>{v(!0),x(t.find(t=>t._id===e)||null)},onChangeBanner:w,navItems:D},e._id))}),b&&(0,n.jsx)(Z,{onClose:j,note:m,onDelete:N}),h&&(0,n.jsx)(T,{onSave:g,note:h,onClose:j}),f&&(0,n.jsx)(M,{note:f,onClose:()=>p(null),onUpdate:S})]})},L=e=>{let{notes:t,setNotes:a,isArchive:l,isTrash:s,fetchNotes:r}=e;return(0,n.jsx)(O,{notes:t,setNotes:a,isArchive:l,isTrash:s,fetchNotes:r})}},82230:function(e,t,a){a.d(t,{$C:function(){return b},Ne:function(){return v},yn:function(){return p}});var n=a(57437),l=a(11005),s=a(98960),r=a(38133),i=a(20897),c=a(13231),o=a(71935),d=a(47390),h=a(73347),u=a(24258),m=a(5891),x=a(10883),f=a(66648);let p=[{href:"#",icon:(0,n.jsx)(f.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,n.jsx)(l.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:(0,n.jsx)(s.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:(0,n.jsx)(r.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:(0,n.jsx)(r.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:(0,n.jsx)(i.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:(0,n.jsx)(c.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:(0,n.jsx)(o.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:(0,n.jsx)(d.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,n.jsx)(h.Z,{className:"h-5 w-5"}),label:"Notes"}],b=[{href:"/business/settings/business-info",icon:(0,n.jsx)(u.Z,{className:"h-5 w-5"}),label:"Settings"}],v=[{href:"#",icon:(0,n.jsx)(f.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,n.jsx)(l.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:(0,n.jsx)(h.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:(0,n.jsx)(m.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:(0,n.jsx)(x.Z,{className:"h-5 w-5"}),label:"Trash"}]},66227:function(e,t,a){a.d(t,{$C:function(){return g},yL:function(){return j},yn:function(){return v}});var n=a(57437),l=a(11005),s=a(33149),r=a(76035),i=a(49100),c=a(40064),o=a(43193),d=a(36141),h=a(33907),u=a(47390),m=a(73347),x=a(24258),f=a(5891),p=a(10883),b=a(66648);let v=[{href:"#",icon:(0,n.jsx)(b.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,n.jsx)(l.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:(0,n.jsx)(s.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:(0,n.jsx)(r.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:(0,n.jsx)(i.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:(0,n.jsx)(c.Z,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:(0,n.jsx)(o.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:(0,n.jsx)(d.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:(0,n.jsx)(h.Z,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:(0,n.jsx)(u.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,n.jsx)(m.Z,{className:"h-5 w-5"}),label:"Notes"}],g=[{href:"/freelancer/settings/personal-info",icon:(0,n.jsx)(x.Z,{className:"h-5 w-5"}),label:"Settings"}];b.default,l.Z,m.Z,f.Z,p.Z;let j=[{href:"#",icon:(0,n.jsx)(b.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,n.jsx)(l.Z,{className:"h-5 w-5"}),label:"Home"}]},22637:function(e,t,a){var n=a(2265),l=a(15922),s=a(78068);t.Z=e=>{let[t,a]=(0,n.useState)([]),[r,i]=(0,n.useState)([]),[c,o]=(0,n.useState)([]),[d,h]=(0,n.useState)(!1);return{notes:t,archive:r,isLoading:d,fetchNotes:(0,n.useCallback)(async()=>{if(e){h(!0);try{var t;let n=await l.b.get("/notes",{params:{userId:e}});(null==n?void 0:null===(t=n.data)||void 0===t?void 0:t.notes)&&(a(n.data.notes.notes),i(n.data.notes.archive||[]),o(n.data.notes.trash||[]))}catch(e){console.error("Failed to fetch notes:",e),(0,s.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}finally{h(!1)}}},[e]),setNotes:a,setArchive:i,trash:c,setTrash:o}}},83640:function(e,t,a){var n,l,s,r,i,c;a.d(t,{EH:function(){return o},JG:function(){return l},wK:function(){return s}}),(r=n||(n={})).BUSINESS="BUSINESS",r.FREELANCER="FREELANCER",r.TRANSACTION="TRANSACTION",r.PROJECT="PROJECT",r.BID="BID",r.INTERVIEW="INTERVIEW",r.DEHIX_TALENT="DEHIX_TALENT",(i=l||(l={})).PERSONAL="PERSONAL",i.WORK="WORK",i.REMINDER="REMINDER",i.TASK="TASK",(c=s||(s={})).NOTE="NOTE",c.TRASH="TRASH",c.ARCHIVE="ARCHIVE";let o={PERSONAL:"bg-blue-500 text-white hover:text-black",WORK:"bg-green-500 text-white hover:text-black",REMINDER:"bg-yellow-500 text-black hover:text-black",TASK:"bg-red-500 text-white hover:text-black"}}}]);