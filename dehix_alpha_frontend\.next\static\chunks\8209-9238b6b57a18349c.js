"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8209],{5891:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},76035:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},43193:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]])},24241:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},70518:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},49100:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},47390:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},29406:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},36141:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},33907:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},73347:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},33149:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]])},40064:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("TabletSmartphone",[["rect",{width:"10",height:"14",x:"3",y:"8",rx:"2",key:"1vrsiq"}],["path",{d:"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4",key:"1j4zmg"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},10883:function(e,t,a){a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(33480).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},90938:function(e,t,a){a.d(t,{Z:function(){return _}});var n=a(57437),r=a(2265),s=a(29406),l=a(11444),i=a(92513),d=a(39343),c=a(31014),o=a(59772),h=a(87138),u=a(54662),m=a(89733),p=a(2128),x=a(77209),y=a(15922),f=a(78068),v=a(97540);let b=o.z.object({skillId:o.z.string(),label:o.z.string().nonempty("Please select a skill"),experience:o.z.string().nonempty("Please enter your experience").regex(/^\d+$/,"Experience must be a number"),monthlyPay:o.z.string().nonempty("Please enter your monthly pay").regex(/^\d+$/,"Monthly pay must be a number"),activeStatus:o.z.boolean(),status:o.z.string()});var j=e=>{let{skills:t,onSubmitSkill:a,setSkills:s}=e,[l,o]=(0,r.useState)(!1),[j,g]=(0,r.useState)(!1),{control:N,handleSubmit:w,formState:{errors:k},reset:S,setValue:I}=(0,d.cI)({resolver:(0,c.F)(b),defaultValues:{skillId:"",label:"",experience:"",monthlyPay:"",activeStatus:!1,status:v.sB.PENDING}}),C=async e=>{if(g(!0),!a({...e,uid:"",type:"SKILL"})){g(!1);return}try{let t=await y.b.post("/freelancer/dehix-talent",{talentId:e.skillId,talentName:e.label,experience:e.experience,monthlyPay:e.monthlyPay,activeStatus:e.activeStatus,status:e.status,type:"SKILL"});200===t.status&&(t.data.data,S(),o(!1),(0,f.Am)({title:"Talent Added",description:"The Talent has been successfully added."}))}catch(e){console.error("Error submitting skill data",e),S(),(0,f.Am)({variant:"destructive",title:"Error",description:"Failed to add talent. Please try again."})}finally{g(!1)}};return(0,n.jsxs)(u.Vq,{open:l,onOpenChange:o,children:[(0,n.jsx)(u.hg,{asChild:!0,children:(0,n.jsxs)(m.z,{onClick:()=>o(!0),children:[(0,n.jsx)(i.Z,{className:"mr-2 h-4 w-4"}),"Add Skill"]})}),(0,n.jsxs)(u.cZ,{children:[(0,n.jsxs)(u.fK,{children:[(0,n.jsx)(u.$N,{children:"Add Skill"}),(0,n.jsx)(u.Be,{children:"Select a skill, enter your experience and monthly pay."})]}),(0,n.jsxs)("form",{onSubmit:w(C),children:[(0,n.jsx)("div",{className:"mb-3",children:(0,n.jsx)(d.Qr,{control:N,name:"label",render:e=>{let{field:a}=e;return(0,n.jsxs)(p.Ph,{value:a.value,onValueChange:e=>{let n=t.find(t=>t.name===e);a.onChange(e),I("skillId",(null==n?void 0:n._id)||"")},children:[(0,n.jsx)(p.i4,{children:(0,n.jsx)(p.ki,{placeholder:"Select a skill"})}),(0,n.jsx)(p.Bw,{children:t.length>0?t.map(e=>(0,n.jsx)(p.Ql,{value:e.name,children:e.name},e._id)):(0,n.jsxs)("div",{className:"p-4 flex justify-center items-center",children:["No skills to add -"," ",(0,n.jsx)(h.default,{href:"/freelancer/settings/personal-info",className:"text-blue-500 ml-2",children:"Add some"})]})})]})}})}),k.label&&(0,n.jsx)("p",{className:"text-red-600",children:k.label.message}),(0,n.jsx)("div",{className:"mb-3",children:(0,n.jsx)(d.Qr,{control:N,name:"experience",render:e=>{let{field:t}=e;return(0,n.jsxs)("div",{className:"col-span-3 relative",children:[(0,n.jsx)(x.I,{type:"number",placeholder:"Experience (years)",min:0,max:50,step:.1,...t,className:"mt-2 w-full"}),(0,n.jsx)("span",{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"YEARS"})]})}})}),k.experience&&(0,n.jsx)("p",{className:"text-red-600",children:k.experience.message}),(0,n.jsx)(d.Qr,{control:N,name:"monthlyPay",render:e=>{let{field:t}=e;return(0,n.jsxs)("div",{className:"col-span-3 relative",children:[(0,n.jsx)(x.I,{type:"number",placeholder:"$ Monthly Pay",min:0,...t,className:"mt-2 w-full"}),(0,n.jsx)("span",{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"$"})]})}}),k.monthlyPay&&(0,n.jsx)("p",{className:"text-red-600",children:k.monthlyPay.message}),(0,n.jsx)(u.cN,{className:"mt-3",children:(0,n.jsx)(m.z,{type:"submit",disabled:j,children:j?"Loading...":"Submit"})})]})]})]})};let g=o.z.object({domainId:o.z.string(),label:o.z.string().nonempty("Please select a domain"),experience:o.z.string().nonempty("Please enter your experience").regex(/^\d+$/,"Experience must be a number"),monthlyPay:o.z.string().nonempty("Please enter your monthly pay").regex(/^\d+$/,"Monthly pay must be a number"),activeStatus:o.z.boolean(),status:o.z.string()});var N=e=>{let{domains:t,onSubmitDomain:a,setDomains:s}=e,[l,o]=(0,r.useState)(!1),[b,j]=(0,r.useState)(!1),{control:N,handleSubmit:w,formState:{errors:k},reset:S,setValue:I}=(0,d.cI)({resolver:(0,c.F)(g),defaultValues:{domainId:"",label:"",experience:"",monthlyPay:"",activeStatus:!1,status:v.sB.PENDING}}),C=async e=>{if(j(!0),!a({...e,uid:"",type:"DOMAIN"})){j(!1);return}try{let t=await y.b.post("/freelancer/dehix-talent",{talentId:e.domainId,talentName:e.label,experience:e.experience,monthlyPay:e.monthlyPay,activeStatus:e.activeStatus,status:e.status,type:"DOMAIN"});200===t.status&&(t.data.data,S(),o(!1),(0,f.Am)({title:"Talent Added",description:"The Talent has been successfully added."}))}catch(e){console.error("Error submitting domain data",e),S(),(0,f.Am)({variant:"destructive",title:"Error",description:"Failed to add talent. Please try again."})}finally{j(!1)}};return(0,n.jsxs)(u.Vq,{open:l,onOpenChange:o,children:[(0,n.jsx)(u.hg,{asChild:!0,children:(0,n.jsxs)(m.z,{onClick:()=>o(!0),children:[(0,n.jsx)(i.Z,{className:"mr-2 h-4 w-4"})," Add Domain"]})}),(0,n.jsxs)(u.cZ,{children:[(0,n.jsxs)(u.fK,{children:[(0,n.jsx)(u.$N,{children:"Add Domain"}),(0,n.jsx)(u.Be,{children:"Select a domain, enter your experience and monthly pay."})]}),(0,n.jsxs)("form",{onSubmit:w(C),children:[(0,n.jsx)("div",{className:"mb-3",children:(0,n.jsx)(d.Qr,{control:N,name:"label",render:e=>{let{field:a}=e;return(0,n.jsxs)(p.Ph,{value:a.value,onValueChange:e=>{let n=t.find(t=>t.name===e);a.onChange(e),I("domainId",(null==n?void 0:n._id)||"")},children:[(0,n.jsx)(p.i4,{children:(0,n.jsx)(p.ki,{placeholder:"Select a domain"})}),(0,n.jsx)(p.Bw,{children:t.length>0?t.map(e=>(0,n.jsx)(p.Ql,{value:e.name,children:e.name},e._id)):(0,n.jsx)(h.default,{href:"/freelancer/settings/personal-info",children:(0,n.jsxs)("p",{className:"p-4 flex justify-center items-center",children:["No domains to add -"," ",(0,n.jsx)("span",{className:"text-blue-500 ml-2",children:"Add some"})," "]})})})]})}})}),k.label&&(0,n.jsx)("p",{className:"text-red-600",children:k.label.message}),(0,n.jsx)("div",{className:"mb-3",children:(0,n.jsx)(d.Qr,{control:N,name:"experience",render:e=>{let{field:t}=e;return(0,n.jsxs)("div",{className:"col-span-3 relative",children:[(0,n.jsx)(x.I,{type:"number",placeholder:"Experience (years)",min:0,max:50,step:.1,...t,className:"w-full mt-2"}),(0,n.jsx)("span",{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"YEARS"})]})}})}),k.experience&&(0,n.jsx)("p",{className:"text-red-600",children:k.experience.message}),(0,n.jsx)("div",{className:"mb-3",children:(0,n.jsx)(d.Qr,{control:N,name:"monthlyPay",render:e=>{let{field:t}=e;return(0,n.jsxs)("div",{className:"col-span-3 relative",children:[(0,n.jsx)(x.I,{type:"number",placeholder:"$ Monthly Pay",min:0,...t,className:"w-full mt-2"}),(0,n.jsx)("span",{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"$"})]})}})}),k.monthlyPay&&(0,n.jsx)("p",{className:"text-red-600",children:k.monthlyPay.message}),(0,n.jsx)(u.cN,{className:"mt-3",children:(0,n.jsx)(m.z,{type:"submit",disabled:b,children:b?"Loading...":"Save"})})]})]})]})},w=a(24241),k=a(62230),S=a(6460),I=a(4919),C=a(69081),P=a(21413),E=a(49354),Z=e=>{let{talentType:t,talentId:a,userId:s}=e,[l,i]=r.useState(),[d,c]=r.useState(!1),[o,h]=r.useState(!1),{toast:p}=(0,f.pm)(),v=async e=>{if(e.preventDefault(),h(!0),!l){p({variant:"destructive",title:"Error",description:"Please select an interview date"}),h(!1);return}let n=(0,k.i)(l),r={intervieweeId:s,interviewType:"INTERVIEWER",talentType:t.toUpperCase(),talentId:a,interviewDate:n.toISOString(),description:e.target.description.value};console.log(r);try{let e=await y.b.post("/interview/".concat(s),r);(200===e.status||201===e.status)&&(p({title:"Success",description:"Interview scheduled successfully"}),c(!1))}catch(e){var i,d;p({variant:"destructive",title:"Error",description:(null===(d=e.response)||void 0===d?void 0:null===(i=d.data)||void 0===i?void 0:i.message)||"Failed to schedule interview"})}finally{h(!1)}};return(0,n.jsxs)(u.Vq,{open:d,onOpenChange:c,children:[(0,n.jsx)(u.hg,{asChild:!0,children:(0,n.jsx)(m.z,{variant:"outline",className:"bg-primary text-primary-foreground hover:bg-primary/90 hover:text-black",children:"Verify"})}),(0,n.jsxs)(u.cZ,{className:"sm:max-w-[425px]",children:[(0,n.jsx)(u.fK,{children:(0,n.jsxs)(u.$N,{className:"text-lg font-semibold",children:["Verify ",t]})}),(0,n.jsxs)("form",{onSubmit:v,className:"space-y-4 mt-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"talentType",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Type"}),(0,n.jsx)(x.I,{id:"talentType",value:t,disabled:!0,className:"bg-muted"})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"interviewDate",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Interview Date"}),(0,n.jsxs)(P.J2,{children:[(0,n.jsx)(P.xo,{asChild:!0,children:(0,n.jsxs)(m.z,{id:"interviewDate",variant:"outline",className:(0,E.cn)("w-full justify-start text-left font-normal",!l&&"text-muted-foreground"),children:[(0,n.jsx)(w.Z,{className:"mr-2 h-4 w-4"}),l?(0,S.WU)(l,"PPP"):"Pick a date"]})}),(0,n.jsx)(P.yk,{className:"w-auto p-0",align:"start",children:(0,n.jsx)(C.f,{mode:"single",selected:l,onSelect:i,initialFocus:!0,disabled:e=>e<new Date})})]})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"description",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Description"}),(0,n.jsx)(I.g,{id:"description",name:"description",placeholder:"Enter interview description",className:"min-h-[100px] resize-none",required:!0})]}),(0,n.jsx)(m.z,{type:"submit",className:"w-full",disabled:o,children:o?(0,n.jsxs)("span",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"}),"Scheduling..."]}):"Schedule Interview"})]})]})]})},M=a(48185),A=a(47304),D=a(60343),L=a(79055),T=a(2183),z=a(89859),_=()=>{let[e,t]=(0,r.useState)([]),[a,i]=(0,r.useState)([]),[d,c]=(0,r.useState)([]),[o,h]=(0,r.useState)([]),[u,m]=(0,r.useState)(!0),[p,x]=(0,r.useState)({}),[b,g]=(0,r.useState)({}),w=e=>{let t=new Set;return e.filter(e=>{let a="".concat(e.label.trim().toLowerCase(),"-").concat(e.type);return!t.has(a)&&(t.add(a),!0)})},k=(0,l.v9)(e=>e.user);(0,r.useEffect)(()=>{!async function(){m(!0);try{var e,a,n,r,s,l,d,o,u;let m=await y.b.get("/freelancer/".concat(k.uid,"/skill")),p=(null===(n=m.data)||void 0===n?void 0:null===(a=n.data)||void 0===a?void 0:null===(e=a[0])||void 0===e?void 0:e.skills)||[],f=await y.b.get("/freelancer/".concat(k.uid,"/domain")),v=(null===(l=f.data)||void 0===l?void 0:null===(s=l.data)||void 0===s?void 0:null===(r=s[0])||void 0===r?void 0:r.domain)||[],b={data:{data:{}}};(null==k?void 0:k.uid)&&(b=await y.b.get("/freelancer/".concat(k.uid,"/dehix-talent")));let j=Array.isArray(null===(d=b.data)||void 0===d?void 0:d.data)?null===(o=b.data)||void 0===o?void 0:o.data:Object.values((null===(u=b.data)||void 0===u?void 0:u.data)||{});j.map(e=>e.talentId),j.map(e=>e.talentId);let N=j.flat().map(e=>({uid:e._id,label:e.talentName||"N/A",experience:e.experience||"N/A",monthlyPay:e.monthlyPay||"N/A",status:e.status,activeStatus:e.activeStatus,type:e.type,originalTalentId:e.talentId})),S=new Set(N.filter(e=>"SKILL"===e.type).map(e=>{var t;return null===(t=e.label)||void 0===t?void 0:t.toLowerCase().trim().replace(/\s+/g," ")}).filter(Boolean)),I=new Set(N.filter(e=>"DOMAIN"===e.type).map(e=>{var t;return null===(t=e.label)||void 0===t?void 0:t.toLowerCase().trim().replace(/\s+/g," ")}).filter(Boolean)),C=new Set(N.map(e=>e.originalTalentId).filter(Boolean)),P=Array.isArray(p)?p.filter(e=>{var t;let a=null===(t=e.name)||void 0===t?void 0:t.toLowerCase().trim().replace(/\s+/g," "),n=S.has(a),r=C.has(e._id);return!n&&!r}):[],E=Array.isArray(v)?v.filter(e=>{var t;let a=null===(t=e.name)||void 0===t?void 0:t.toLowerCase().trim().replace(/\s+/g," "),n=I.has(a),r=C.has(e._id);return!n&&!r}):[],Z=w(N);c(Z),h(Z.map(e=>e.activeStatus)),t(P),i(E);let M={},A={};Z.forEach(e=>{let t=e.label.trim().toLowerCase();"SKILL"===e.type&&(M[t]=(M[t]||0)+1),"DOMAIN"===e.type&&(A[t]=(A[t]||0)+1)}),x(M),g(A)}catch(e){console.error("Error fetching data:",e),(0,f.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}finally{m(!1)}}()},[null==k?void 0:k.uid]);let S=e=>{let a=e.label.toLowerCase().trim().replace(/\s+/g," ");return d.some(e=>e.label.toLowerCase().trim().replace(/\s+/g," ")===a&&"SKILL"===e.type)?((0,f.Am)({variant:"destructive",title:"Duplicate Skill",description:"This skill has already been added."}),!1):(c([...d,{...e,status:v.sB.PENDING,activeStatus:!1,type:"SKILL"}]),h([...o,!1]),t(e=>e.filter(e=>e.name.toLowerCase().trim().replace(/\s+/g," ")!==a)),!0)},I=e=>{let t=e.label.toLowerCase().trim().replace(/\s+/g," ");return d.some(e=>e.label.toLowerCase().trim().replace(/\s+/g," ")===t&&"DOMAIN"===e.type)?((0,f.Am)({variant:"destructive",title:"Duplicate Domain",description:"This domain has already been added."}),!1):(c([...d,{...e,status:v.sB.PENDING,activeStatus:!1,type:"DOMAIN"}]),h([...o,!1]),i(e=>e.filter(e=>e.name.toLowerCase().trim().replace(/\s+/g," ")!==t)),!0)},C=async(e,t,a)=>{try{let n=await y.b.put("/freelancer/dehix-talent/".concat(a),{activeStatus:t});if(200===n.status){let a=[...o];a[e]=t,h(a)}}catch(e){console.error("Error updating visibility:",e),(0,f.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}};return(0,n.jsxs)("div",{className:"p-6 mt-2",children:[(0,n.jsxs)("div",{className:"mb-8 mt-1 ml-2",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold",children:"Dehix Talent"}),(0,n.jsx)("p",{className:"text-gray-400 mt-2",children:"Here you can add relevant skills and domains to get directly hired from dehix talent."})]}),(0,n.jsx)("div",{className:"px-4",children:(0,n.jsxs)("div",{className:"mb-8 mt-4",children:[(0,n.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,n.jsxs)("div",{className:"flex space-x-4",children:[(0,n.jsx)(j,{setSkills:t,skills:e,onSubmitSkill:e=>{let t=S(e);return t&&(0,f.Am)({variant:"default",title:"Talent Added",description:"The skill has been successfully added."}),t}}),(0,n.jsx)(N,{setDomains:i,domains:a,onSubmitDomain:e=>{let t=I(e);return t&&(0,f.Am)({variant:"default",title:"Talent Added",description:"The domain has been successfully added."}),t}})]})}),(0,n.jsx)(M.Zb,{children:(0,n.jsxs)(A.iA,{children:[(0,n.jsx)(A.xD,{children:(0,n.jsxs)(A.SC,{children:[(0,n.jsx)(A.ss,{children:"Label"}),(0,n.jsx)(A.ss,{children:"Experience"}),(0,n.jsx)(A.ss,{className:"text-center",children:"Monthly Pay"}),(0,n.jsx)(A.ss,{children:"Status"}),(0,n.jsx)(A.ss,{})]})}),(0,n.jsx)(A.RM,{children:u?Array.from({length:9}).map((e,t)=>(0,n.jsxs)(A.SC,{children:[(0,n.jsx)(A.pj,{children:(0,n.jsx)(T.O,{className:"h-6 w-24"})}),(0,n.jsx)(A.pj,{children:(0,n.jsx)(T.O,{className:"h-6 w-16"})}),(0,n.jsx)(A.pj,{className:"text-center",children:(0,n.jsx)(T.O,{className:"mx-auto h-6 w-12"})}),(0,n.jsx)(A.pj,{children:(0,n.jsx)(T.O,{className:"h-6 w-20"})}),(0,n.jsx)(A.pj,{children:(0,n.jsx)(T.O,{className:"h-6 w-12 rounded-xl"})})]},t)):d.length>0?d.map((e,t)=>{var a;return(0,n.jsxs)(A.SC,{children:[(0,n.jsx)(A.pj,{children:e.label}),(0,n.jsxs)(A.pj,{children:[e.experience," Years"]}),(0,n.jsxs)(A.pj,{className:"text-center",children:["$",e.monthlyPay]}),(0,n.jsx)(A.pj,{children:e.status.toUpperCase()===v.sB.PENDING?(0,n.jsx)(Z,{talentType:e.type,talentId:e.uid,userId:k.uid}):(0,n.jsx)(L.C,{className:(0,z.S)(e.status),children:null==e?void 0:null===(a=e.status)||void 0===a?void 0:a.toUpperCase()})}),(0,n.jsx)(A.pj,{children:(0,n.jsx)(D.r,{checked:o[t],onCheckedChange:a=>e.uid?C(t,a,e.uid):console.error("UID missing for item",e)})})]},t)}):(0,n.jsx)(A.SC,{children:(0,n.jsx)(A.pj,{colSpan:5,className:"text-center",children:(0,n.jsxs)("div",{className:"text-center py-10 w-[90vw] h-[30vw] mt-10",children:[(0,n.jsx)(s.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,n.jsxs)("p",{className:"text-gray-500",children:["No data available.",(0,n.jsx)("br",{})," This feature will be available soon.",(0,n.jsx)("br",{}),"Here you can get directly hired for different roles."]})]})})})})]})})]})})]})}},69081:function(e,t,a){a.d(t,{f:function(){return c}});var n=a(57437);a(2265);var r=a(70518),s=a(87592),l=a(13793),i=a(49354),d=a(89733);function c(e){let{className:t,classNames:a,showOutsideDays:c=!0,...o}=e;return(0,n.jsx)(l._W,{showOutsideDays:c,className:(0,i.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,i.cn)((0,d.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,i.cn)((0,d.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...a},components:{IconLeft:e=>{let{...t}=e;return(0,n.jsx)(r.Z,{className:"h-4 w-4"})},IconRight:e=>{let{...t}=e;return(0,n.jsx)(s.Z,{className:"h-4 w-4"})}},...o})}c.displayName="Calendar"},2183:function(e,t,a){a.d(t,{O:function(){return s}});var n=a(57437),r=a(49354);function s(e){let{className:t,...a}=e;return(0,n.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-primary/10",t),...a})}},60343:function(e,t,a){a.d(t,{r:function(){return i}});var n=a(57437),r=a(2265),s=a(9646),l=a(49354);let i=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(s.fC,{className:(0,l.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...r,ref:t,children:(0,n.jsx)(s.bU,{className:(0,l.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});i.displayName=s.fC.displayName},66227:function(e,t,a){a.d(t,{$C:function(){return b},yL:function(){return j},yn:function(){return v}});var n=a(57437),r=a(11005),s=a(33149),l=a(76035),i=a(49100),d=a(40064),c=a(43193),o=a(36141),h=a(33907),u=a(47390),m=a(73347),p=a(24258),x=a(5891),y=a(10883),f=a(66648);let v=[{href:"#",icon:(0,n.jsx)(f.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,n.jsx)(r.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:(0,n.jsx)(s.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:(0,n.jsx)(l.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:(0,n.jsx)(i.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:(0,n.jsx)(d.Z,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:(0,n.jsx)(c.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:(0,n.jsx)(o.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:(0,n.jsx)(h.Z,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:(0,n.jsx)(u.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,n.jsx)(m.Z,{className:"h-5 w-5"}),label:"Notes"}],b=[{href:"/freelancer/settings/personal-info",icon:(0,n.jsx)(p.Z,{className:"h-5 w-5"}),label:"Settings"}];f.default,r.Z,m.Z,x.Z,y.Z;let j=[{href:"#",icon:(0,n.jsx)(f.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,n.jsx)(r.Z,{className:"h-5 w-5"}),label:"Home"}]},97540:function(e,t,a){var n,r,s,l,i,d;a.d(t,{cd:function(){return n},d8:function(){return c},kJ:function(){return r},sB:function(){return s}}),(l=n||(n={})).Mastery="Mastery",l.Proficient="Proficient",l.Beginner="Beginner",(i=r||(r={})).ACTIVE="Active",i.PENDING="Pending",i.REJECTED="Rejected",i.COMPLETED="Completed",(d=s||(s={})).ACTIVE="ACTIVE",d.PENDING="PENDING",d.REJECTED="REJECTED",d.COMPLETED="COMPLETED";let c={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},9646:function(e,t,a){a.d(t,{bU:function(){return w},fC:function(){return N}});var n=a(2265),r=a(78149),s=a(1584),l=a(98324),i=a(91715),d=a(47250),c=a(75238),o=a(18676),h=a(57437),u="Switch",[m,p]=(0,l.b)(u),[x,y]=m(u),f=n.forwardRef((e,t)=>{let{__scopeSwitch:a,name:l,checked:d,defaultChecked:c,required:u,disabled:m,value:p="on",onCheckedChange:y,...f}=e,[v,b]=n.useState(null),N=(0,s.e)(t,e=>b(e)),w=n.useRef(!1),k=!v||!!v.closest("form"),[S=!1,I]=(0,i.T)({prop:d,defaultProp:c,onChange:y});return(0,h.jsxs)(x,{scope:a,checked:S,disabled:m,children:[(0,h.jsx)(o.WV.button,{type:"button",role:"switch","aria-checked":S,"aria-required":u,"data-state":g(S),"data-disabled":m?"":void 0,disabled:m,value:p,...f,ref:N,onClick:(0,r.M)(e.onClick,e=>{I(e=>!e),k&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})}),k&&(0,h.jsx)(j,{control:v,bubbles:!w.current,name:l,value:p,checked:S,required:u,disabled:m,style:{transform:"translateX(-100%)"}})]})});f.displayName=u;var v="SwitchThumb",b=n.forwardRef((e,t)=>{let{__scopeSwitch:a,...n}=e,r=y(v,a);return(0,h.jsx)(o.WV.span,{"data-state":g(r.checked),"data-disabled":r.disabled?"":void 0,...n,ref:t})});b.displayName=v;var j=e=>{let{control:t,checked:a,bubbles:r=!0,...s}=e,l=n.useRef(null),i=(0,d.D)(a),o=(0,c.t)(t);return n.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(i!==a&&t){let n=new Event("click",{bubbles:r});t.call(e,a),e.dispatchEvent(n)}},[i,a,r]),(0,h.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...s,tabIndex:-1,ref:l,style:{...e.style,...o,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function g(e){return e?"checked":"unchecked"}var N=f,w=b}}]);