"use strict";exports.id=2146,exports.ids=[2146],exports.modules={24230:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(80851).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},96633:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(80851).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},77506:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(80851).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},37956:(e,t,r)=>{r.d(t,{x8:()=>ei,VY:()=>el,dk:()=>ea,Vq:()=>E,cZ:()=>A,t9:()=>W,aV:()=>en,h_:()=>er,fC:()=>ee,Dx:()=>eo,xz:()=>et});var n=r(17577),l=r(82561),o=r(48051),a=r(93095),i=r(88957),s=r(52067),d=r(825),u=r(10441),c=r(83078),p=r(9815),f=r(77335),h=r(80699),v=r(17397),g=r(35664),m=r(10326),x=n.forwardRef((e,t)=>{let{children:r,...l}=e,o=n.Children.toArray(r),a=o.find(b);if(a){let e=a.props.children,r=o.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,m.jsx)(w,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,m.jsx)(w,{...l,ref:t,children:r})});x.displayName="Slot";var w=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return n.cloneElement(r,{...function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props),ref:t?(0,o.F)(t,e):e})}return n.Children.count(r)>1?n.Children.only(null):null});w.displayName="SlotClone";var y=({children:e})=>(0,m.jsx)(m.Fragment,{children:e});function b(e){return n.isValidElement(e)&&e.type===y}var C="Dialog",[j,S]=(0,a.b)(C),[R,D]=j(C),E=e=>{let{__scopeDialog:t,children:r,open:l,defaultOpen:o,onOpenChange:a,modal:d=!0}=e,u=n.useRef(null),c=n.useRef(null),[p=!1,f]=(0,s.T)({prop:l,defaultProp:o,onChange:a});return(0,m.jsx)(R,{scope:t,triggerRef:u,contentRef:c,contentId:(0,i.M)(),titleId:(0,i.M)(),descriptionId:(0,i.M)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};E.displayName=C;var M="DialogTrigger",I=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=D(M,r),i=(0,o.e)(t,a.triggerRef);return(0,m.jsx)(f.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":$(a.open),...n,ref:i,onClick:(0,l.M)(e.onClick,a.onOpenToggle)})});I.displayName=M;var N="DialogPortal",[T,k]=j(N,{forceMount:void 0}),P=e=>{let{__scopeDialog:t,forceMount:r,children:l,container:o}=e,a=D(N,t);return(0,m.jsx)(T,{scope:t,forceMount:r,children:n.Children.map(l,e=>(0,m.jsx)(p.z,{present:r||a.open,children:(0,m.jsx)(c.h,{asChild:!0,container:o,children:e})}))})};P.displayName=N;var V="DialogOverlay",W=n.forwardRef((e,t)=>{let r=k(V,e.__scopeDialog),{forceMount:n=r.forceMount,...l}=e,o=D(V,e.__scopeDialog);return o.modal?(0,m.jsx)(p.z,{present:n||o.open,children:(0,m.jsx)(_,{...l,ref:t})}):null});W.displayName=V;var _=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=D(V,r);return(0,m.jsx)(v.Z,{as:x,allowPinchZoom:!0,shards:[l.contentRef],children:(0,m.jsx)(f.WV.div,{"data-state":$(l.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),O="DialogContent",A=n.forwardRef((e,t)=>{let r=k(O,e.__scopeDialog),{forceMount:n=r.forceMount,...l}=e,o=D(O,e.__scopeDialog);return(0,m.jsx)(p.z,{present:n||o.open,children:o.modal?(0,m.jsx)(F,{...l,ref:t}):(0,m.jsx)(L,{...l,ref:t})})});A.displayName=O;var F=n.forwardRef((e,t)=>{let r=D(O,e.__scopeDialog),a=n.useRef(null),i=(0,o.e)(t,r.contentRef,a);return n.useEffect(()=>{let e=a.current;if(e)return(0,g.Ry)(e)},[]),(0,m.jsx)(B,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,l.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,l.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,l.M)(e.onFocusOutside,e=>e.preventDefault())})}),L=n.forwardRef((e,t)=>{let r=D(O,e.__scopeDialog),l=n.useRef(!1),o=n.useRef(!1);return(0,m.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(l.current||r.triggerRef.current?.focus(),t.preventDefault()),l.current=!1,o.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(l.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),B=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:l,onOpenAutoFocus:a,onCloseAutoFocus:i,...s}=e,c=D(O,r),p=n.useRef(null),f=(0,o.e)(t,p);return(0,h.EW)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(u.M,{asChild:!0,loop:!0,trapped:l,onMountAutoFocus:a,onUnmountAutoFocus:i,children:(0,m.jsx)(d.XB,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":$(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(J,{titleId:c.titleId}),(0,m.jsx)(Q,{contentRef:p,descriptionId:c.descriptionId})]})]})}),H="DialogTitle",Z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=D(H,r);return(0,m.jsx)(f.WV.h2,{id:l.titleId,...n,ref:t})});Z.displayName=H;var z="DialogDescription",K=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=D(z,r);return(0,m.jsx)(f.WV.p,{id:l.descriptionId,...n,ref:t})});K.displayName=z;var U="DialogClose",q=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(U,r);return(0,m.jsx)(f.WV.button,{type:"button",...n,ref:t,onClick:(0,l.M)(e.onClick,()=>o.onOpenChange(!1))})});function $(e){return e?"open":"closed"}q.displayName=U;var Y="DialogTitleWarning",[X,G]=(0,a.k)(Y,{contentName:O,titleName:H,docsSlug:"dialog"}),J=({titleId:e})=>{let t=G(Y),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},Q=({contentRef:e,descriptionId:t})=>{let r=G("DialogDescriptionWarning"),l=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(l)},[l,e,t]),null},ee=E,et=I,er=P,en=W,el=A,eo=Z,ea=K,ei=q},75334:(e,t,r)=>{r.d(t,{VY:()=>eO,ZA:()=>eF,JO:()=>eW,ck:()=>eB,wU:()=>eZ,eT:()=>eH,__:()=>eL,h_:()=>e_,fC:()=>ek,$G:()=>eK,u_:()=>ez,Z0:()=>eU,xz:()=>eP,B4:()=>eV,l_:()=>eA});var n=r(17577),l=r(60962),o=r(37125),a=r(82561),i=r(73866),s=r(48051),d=r(93095),u=r(17124),c=r(825),p=r(80699),f=r(10441),h=r(88957),v=r(17103),g=r(83078),m=r(77335),x=r(10326),w=n.forwardRef((e,t)=>{let{children:r,...l}=e,o=n.Children.toArray(r),a=o.find(C);if(a){let e=a.props.children,r=o.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,x.jsx)(y,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,x.jsx)(y,{...l,ref:t,children:r})});w.displayName="Slot";var y=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return n.cloneElement(r,{...function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props),ref:t?(0,s.F)(t,e):e})}return n.Children.count(r)>1?n.Children.only(null):null});y.displayName="SlotClone";var b=({children:e})=>(0,x.jsx)(x.Fragment,{children:e});function C(e){return n.isValidElement(e)&&e.type===b}var j=r(55049),S=r(52067),R=r(65819),D=r(53405),E=r(6009),M=r(35664),I=r(17397),N=[" ","Enter","ArrowUp","ArrowDown"],T=[" ","Enter"],k="Select",[P,V,W]=(0,i.B)(k),[_,O]=(0,d.b)(k,[W,v.D7]),A=(0,v.D7)(),[F,L]=_(k),[B,H]=_(k),Z=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:f,disabled:g,required:m}=e,w=A(t),[y,b]=n.useState(null),[C,j]=n.useState(null),[R,D]=n.useState(!1),E=(0,u.gm)(c),[M=!1,I]=(0,S.T)({prop:l,defaultProp:o,onChange:a}),[N,T]=(0,S.T)({prop:i,defaultProp:s,onChange:d}),k=n.useRef(null),V=!y||!!y.closest("form"),[W,_]=n.useState(new Set),O=Array.from(W).map(e=>e.props.value).join(";");return(0,x.jsx)(v.fC,{...w,children:(0,x.jsxs)(F,{required:m,scope:t,trigger:y,onTriggerChange:b,valueNode:C,onValueNodeChange:j,valueNodeHasChildren:R,onValueNodeHasChildrenChange:D,contentId:(0,h.M)(),value:N,onValueChange:T,open:M,onOpenChange:I,dir:E,triggerPointerDownPosRef:k,disabled:g,children:[(0,x.jsx)(P.Provider,{scope:t,children:(0,x.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{_(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{_(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),V?(0,x.jsxs)(eI,{"aria-hidden":!0,required:m,tabIndex:-1,name:p,autoComplete:f,value:N,onChange:e=>T(e.target.value),disabled:g,children:[void 0===N?(0,x.jsx)("option",{value:""}):null,Array.from(W)]},O):null]})})};Z.displayName=k;var z="SelectTrigger",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:n=!1,...l}=e,o=A(r),i=L(z,r),d=i.disabled||n,u=(0,s.e)(t,i.onTriggerChange),c=V(r),[p,f,h]=eN(e=>{let t=c().filter(e=>!e.disabled),r=t.find(e=>e.value===i.value),n=eT(t,e,r);void 0!==n&&i.onValueChange(n.value)}),g=()=>{d||(i.onOpenChange(!0),h())};return(0,x.jsx)(v.ee,{asChild:!0,...o,children:(0,x.jsx)(m.WV.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":eM(i.value)?"":void 0,...l,ref:u,onClick:(0,a.M)(l.onClick,e=>{e.currentTarget.focus()}),onPointerDown:(0,a.M)(l.onPointerDown,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&(g(),i.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)},e.preventDefault())}),onKeyDown:(0,a.M)(l.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||f(e.key),(!t||" "!==e.key)&&N.includes(e.key)&&(g(),e.preventDefault())})})})});K.displayName=z;var U="SelectValue",q=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,d=L(U,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==o,p=(0,s.e)(t,d.onValueNodeChange);return(0,R.b)(()=>{u(c)},[u,c]),(0,x.jsx)(m.WV.span,{...i,ref:p,style:{pointerEvents:"none"},children:eM(d.value)?(0,x.jsx)(x.Fragment,{children:a}):o})});q.displayName=U;var $=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,x.jsx)(m.WV.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});$.displayName="SelectIcon";var Y=e=>(0,x.jsx)(g.h,{asChild:!0,...e});Y.displayName="SelectPortal";var X="SelectContent",G=n.forwardRef((e,t)=>{let r=L(X,e.__scopeSelect),[o,a]=n.useState();return((0,R.b)(()=>{a(new DocumentFragment)},[]),r.open)?(0,x.jsx)(ee,{...e,ref:t}):o?l.createPortal((0,x.jsx)(J,{scope:e.__scopeSelect,children:(0,x.jsx)(P.Slot,{scope:e.__scopeSelect,children:(0,x.jsx)("div",{children:e.children})})}),o):null});G.displayName=X;var[J,Q]=_(X),ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:h,align:v,alignOffset:g,arrowPadding:m,collisionBoundary:y,collisionPadding:b,sticky:C,hideWhenDetached:j,avoidCollisions:S,...R}=e,D=L(X,r),[E,N]=n.useState(null),[T,k]=n.useState(null),P=(0,s.e)(t,e=>N(e)),[W,_]=n.useState(null),[O,A]=n.useState(null),F=V(r),[B,H]=n.useState(!1),Z=n.useRef(!1);n.useEffect(()=>{if(E)return(0,M.Ry)(E)},[E]),(0,p.EW)();let z=n.useCallback(e=>{let[t,...r]=F().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(r?.scrollIntoView({block:"nearest"}),r===t&&T&&(T.scrollTop=0),r===n&&T&&(T.scrollTop=T.scrollHeight),r?.focus(),document.activeElement!==l))return},[F,T]),K=n.useCallback(()=>z([W,E]),[z,W,E]);n.useEffect(()=>{B&&K()},[B,K]);let{onOpenChange:U,triggerPointerDownPosRef:q}=D;n.useEffect(()=>{if(E){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(q.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(q.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():E.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[E,U,q]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[$,Y]=eN(e=>{let t=F().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eT(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),G=n.useCallback((e,t,r)=>{let n=!Z.current&&!r;(void 0!==D.value&&D.value===t||n)&&(_(e),n&&(Z.current=!0))},[D.value]),Q=n.useCallback(()=>E?.focus(),[E]),ee=n.useCallback((e,t,r)=>{let n=!Z.current&&!r;(void 0!==D.value&&D.value===t||n)&&A(e)},[D.value]),en="popper"===l?er:et,el=en===er?{side:u,sideOffset:h,align:v,alignOffset:g,arrowPadding:m,collisionBoundary:y,collisionPadding:b,sticky:C,hideWhenDetached:j,avoidCollisions:S}:{};return(0,x.jsx)(J,{scope:r,content:E,viewport:T,onViewportChange:k,itemRefCallback:G,selectedItem:W,onItemLeave:Q,itemTextRefCallback:ee,focusSelectedItem:K,selectedItemText:O,position:l,isPositioned:B,searchRef:$,children:(0,x.jsx)(I.Z,{as:w,allowPinchZoom:!0,children:(0,x.jsx)(f.M,{asChild:!0,trapped:D.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.M)(o,e=>{D.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,x.jsx)(c.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>D.onOpenChange(!1),children:(0,x.jsx)(en,{role:"listbox",id:D.contentId,"data-state":D.open?"open":"closed",dir:D.dir,onContextMenu:e=>e.preventDefault(),...R,...el,onPlaced:()=>H(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...R.style},onKeyDown:(0,a.M)(R.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=F().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>z(t)),e.preventDefault()}})})})})})})});ee.displayName="SelectContentImpl";var et=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=L(X,r),d=Q(X,r),[u,c]=n.useState(null),[p,f]=n.useState(null),h=(0,s.e)(t,e=>f(e)),v=V(r),g=n.useRef(!1),w=n.useRef(!0),{viewport:y,selectedItem:b,selectedItemText:C,focusSelectedItem:j}=d,S=n.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&y&&b&&C){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=C.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,d=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.u)(a,[10,c-d]);u.style.minWidth=s+"px",u.style.left=p+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,d=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.u)(a,[10,c-d]);u.style.minWidth=s+"px",u.style.right=p+"px"}let a=v(),s=window.innerHeight-20,d=y.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),h=parseInt(c.paddingTop,10),m=parseInt(c.borderBottomWidth,10),x=f+h+d+parseInt(c.paddingBottom,10)+m,w=Math.min(5*b.offsetHeight,x),j=window.getComputedStyle(y),S=parseInt(j.paddingTop,10),R=parseInt(j.paddingBottom,10),D=e.top+e.height/2-10,E=b.offsetHeight/2,M=f+h+(b.offsetTop+E);if(M<=D){let e=b===a[a.length-1].ref.current;u.style.bottom="0px";let t=p.clientHeight-y.offsetTop-y.offsetHeight;u.style.height=M+Math.max(s-D,E+(e?R:0)+t+m)+"px"}else{let e=b===a[0].ref.current;u.style.top="0px";let t=Math.max(D,f+y.offsetTop+(e?S:0)+E);u.style.height=t+(x-M)+"px",y.scrollTop=M-D+y.offsetTop}u.style.margin="10px 0",u.style.minHeight=w+"px",u.style.maxHeight=s+"px",l?.(),requestAnimationFrame(()=>g.current=!0)}},[v,i.trigger,i.valueNode,u,p,y,b,C,i.dir,l]);(0,R.b)(()=>S(),[S]);let[D,E]=n.useState();(0,R.b)(()=>{p&&E(window.getComputedStyle(p).zIndex)},[p]);let M=n.useCallback(e=>{e&&!0===w.current&&(S(),j?.(),w.current=!1)},[S,j]);return(0,x.jsx)(en,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:g,onScrollButtonChange:M,children:(0,x.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:D},children:(0,x.jsx)(m.WV.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});et.displayName="SelectItemAlignedPosition";var er=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=A(r);return(0,x.jsx)(v.VY,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});er.displayName="SelectPopperPosition";var[en,el]=_(X,{}),eo="SelectViewport",ea=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=Q(eo,r),d=el(eo,r),u=(0,s.e)(t,i.onViewportChange),c=n.useRef(0);return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,x.jsx)(P.Slot,{scope:r,children:(0,x.jsx)(m.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"auto",...o.style},onScroll:(0,a.M)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if(n?.current&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});ea.displayName=eo;var ei="SelectGroup",[es,ed]=_(ei),eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,h.M)();return(0,x.jsx)(es,{scope:r,id:l,children:(0,x.jsx)(m.WV.div,{role:"group","aria-labelledby":l,...n,ref:t})})});eu.displayName=ei;var ec="SelectLabel",ep=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ed(ec,r);return(0,x.jsx)(m.WV.div,{id:l.id,...n,ref:t})});ep.displayName=ec;var ef="SelectItem",[eh,ev]=_(ef),eg=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...d}=e,u=L(ef,r),c=Q(ef,r),p=u.value===l,[f,v]=n.useState(i??""),[g,w]=n.useState(!1),y=(0,s.e)(t,e=>c.itemRefCallback?.(e,l,o)),b=(0,h.M)(),C=()=>{o||(u.onValueChange(l),u.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,x.jsx)(eh,{scope:r,value:l,disabled:o,textId:b,isSelected:p,onItemTextChange:n.useCallback(e=>{v(t=>t||(e?.textContent??"").trim())},[]),children:(0,x.jsx)(P.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,x.jsx)(m.WV.div,{role:"option","aria-labelledby":b,"data-highlighted":g?"":void 0,"aria-selected":p&&g,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...d,ref:y,onFocus:(0,a.M)(d.onFocus,()=>w(!0)),onBlur:(0,a.M)(d.onBlur,()=>w(!1)),onPointerUp:(0,a.M)(d.onPointerUp,C),onPointerMove:(0,a.M)(d.onPointerMove,e=>{o?c.onItemLeave?.():e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.M)(d.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:(0,a.M)(d.onKeyDown,e=>{c.searchRef?.current!==""&&" "===e.key||(T.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});eg.displayName=ef;var em="SelectItemText",ex=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,d=L(em,r),u=Q(em,r),c=ev(em,r),p=H(em,r),[f,h]=n.useState(null),v=(0,s.e)(t,e=>h(e),c.onItemTextChange,e=>u.itemTextRefCallback?.(e,c.value,c.disabled)),g=f?.textContent,w=n.useMemo(()=>(0,x.jsx)("option",{value:c.value,disabled:c.disabled,children:g},c.value),[c.disabled,c.value,g]),{onNativeOptionAdd:y,onNativeOptionRemove:b}=p;return(0,R.b)(()=>(y(w),()=>b(w)),[y,b,w]),(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(m.WV.span,{id:c.textId,...i,ref:v}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?l.createPortal(i.children,d.valueNode):null]})});ex.displayName=em;var ew="SelectItemIndicator",ey=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ev(ew,r).isSelected?(0,x.jsx)(m.WV.span,{"aria-hidden":!0,...n,ref:t}):null});ey.displayName=ew;var eb="SelectScrollUpButton",eC=n.forwardRef((e,t)=>{let r=Q(eb,e.__scopeSelect),l=el(eb,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.e)(t,l.onScrollButtonChange);return(0,R.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,x.jsx)(eR,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eC.displayName=eb;var ej="SelectScrollDownButton",eS=n.forwardRef((e,t)=>{let r=Q(ej,e.__scopeSelect),l=el(ej,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.e)(t,l.onScrollButtonChange);return(0,R.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,x.jsx)(eR,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eS.displayName=ej;var eR=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=Q("SelectScrollButton",r),s=n.useRef(null),d=V(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,R.b)(()=>{let e=d().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[d]),(0,x.jsx)(m.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.M)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.M)(o.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.M)(o.onPointerLeave,()=>{u()})})}),eD=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,x.jsx)(m.WV.div,{"aria-hidden":!0,...n,ref:t})});eD.displayName="SelectSeparator";var eE="SelectArrow";function eM(e){return""===e||void 0===e}n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=A(r),o=L(eE,r),a=Q(eE,r);return o.open&&"popper"===a.position?(0,x.jsx)(v.Eh,{...l,...n,ref:t}):null}).displayName=eE;var eI=n.forwardRef((e,t)=>{let{value:r,...l}=e,o=n.useRef(null),a=(0,s.e)(t,o),i=(0,D.D)(r);return n.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[i,r]),(0,x.jsx)(E.T,{asChild:!0,children:(0,x.jsx)("select",{...l,ref:a,defaultValue:r})})});function eN(e){let t=(0,j.W)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eT(e,t,r){var n;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===l.length&&(o=o.filter(e=>e!==r));let a=o.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return a!==r?a:void 0}eI.displayName="BubbleSelect";var ek=Z,eP=K,eV=q,eW=$,e_=Y,eO=G,eA=ea,eF=eu,eL=ep,eB=eg,eH=ex,eZ=ey,ez=eC,eK=eS,eU=eD}};