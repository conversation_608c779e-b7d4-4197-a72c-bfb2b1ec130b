"use strict";exports.id=8066,exports.ids=[8066],exports.modules={54406:(e,s,t)=>{t.d(s,{Z:()=>m});var a=t(10326),r=t(17577),l=t(90434),n=t(9664),i=t(40603),d=t(3594),o=t(76066),c=t(91664);let m=({menuItemsTop:e,menuItemsBottom:s,active:t,setActive:m=()=>null,conversations:x,setActiveConversation:u,activeConversation:h})=>{let f=({conversation:e})=>{if(!e||"object"!=typeof e||Array.isArray(e)||!u)return null;let s=e.name||"Unknown",t=h?.id===e.id;return(0,a.jsxs)(d.<PERSON>tar,{className:`w-full h-10 flex justify-start gap-2 items-center rounded-full 
    transition-all cursor-pointer 
    ${t?"border-2 border-blue-500 bg-gray-200 dark:bg-gray-700":"border-transparent"}`,onClick:()=>u(e),children:[(0,a.jsxs)("div",{className:"w-10 h-10",children:[a.jsx(d.AvatarImage,{src:"",alt:s}),a.jsx(d.AvatarFallback,{className:"bg-gray-300 dark:bg-gray-600 text-black dark:text-white font-bold",children:s.split(" ").map(e=>e.charAt(0).toUpperCase()).join("").slice(0,2)})]}),a.jsx("span",{className:"text-black dark:text-white",children:s})]})};return(0,a.jsxs)(o.yo,{children:[a.jsx(o.aM,{asChild:!0,children:(0,a.jsxs)(c.z,{size:"icon",variant:"outline",className:"sm:hidden",children:[a.jsx(n.Z,{className:"h-5 w-5"}),a.jsx("span",{className:"sr-only",children:"Toggle Menu"})]})}),(0,a.jsxs)(o.ue,{side:"left",className:"flex flex-col justify-between sm:max-w-xs",children:[(0,a.jsxs)("nav",{className:"grid gap-6 text-lg font-medium",children:[e.map((e,s)=>(0,a.jsxs)(l.default,{href:e.href,onClick:()=>m(e.label),className:`flex items-center gap-4 px-2.5 ${"Dehix"===e.label?"group flex h-10 w-10 shrink-0 items-center justify-center gap-2 rounded-full bg-primary text-lg font-semibold text-primary-foreground md:text-base":e.label===t?"text-foreground":"text-muted-foreground hover:text-foreground"}`,children:[e.icon,"Dehix"!==e.label&&e.label]},s)),"Chats"===t&&u&&x&&x.map(e=>a.jsx(f,{conversation:e},e.id))]}),a.jsx("nav",{className:"grid gap-6 text-lg font-medium",children:s.map((e,s)=>(0,a.jsxs)(r.Fragment,{children:["Settings"===e.label&&a.jsx("div",{className:"flex items-center px-2.5 text-muted-foreground hover:text-foreground mb-1",children:a.jsx(i.T,{})}),(0,a.jsxs)(l.default,{href:e.href,onClick:()=>m(e.label),className:`flex items-center gap-4 px-2.5 ${"Dehix"===e.label?"group flex h-10 w-10 shrink-0 items-center justify-center gap-2 rounded-full bg-primary text-lg font-semibold text-primary-foreground md:text-base":e.label===t?"text-foreground":"text-muted-foreground hover:text-foreground"}`,children:[e.icon,"Dehix"!==e.label&&e.label]})]},s))})]})]})}},87922:(e,s,t)=>{t.d(s,{Z:()=>y});var a=t(10326),r=t(17577),l=t(79635),n=t(71810),i=t(76993),d=t(32933),o=t(43810),c=t(25842),m=t(35047),x=t(90434),u=t(66562),h=t(48474),f=t(10143),p=t(91664),j=t(3594),N=t(4594),g=t(24118);t(6260),t(84097);let b=()=>async(e,s,t)=>{if(navigator.share)try{await navigator.share({title:e,text:s,url:t})}catch(e){console.error("Error sharing content:",e)}else console.warn("Share API is not supported on this browser.")};function y({setConnects:e}){let s=(0,c.v9)(e=>e.user),t=(0,c.I0)(),y=(0,m.useRouter)(),[v,w]=(0,r.useState)(null),[k,S]=(0,r.useState)(""),[C,T]=(0,r.useState)(!1),[R,z]=(0,r.useState)(!0),[A,F]=(0,r.useState)(null),D=b(),O=e=>{D("Referral Link","Check out this referral link!",e)},P=k?`http://127.0.0.1:8080/auth/sign-up/freelancer?referral=${k}`:"",_=e=>{navigator.clipboard.writeText(e).then(()=>{F(e),setTimeout(()=>{F(null)},2e3)},e=>{console.error("Failed to copy text: ",e)})};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(f.h_,{children:[a.jsx(f.$F,{asChild:!0,children:a.jsx(p.z,{variant:"outline",size:"icon",className:"overflow-hidden rounded-full hover:scale-105 transition-transform",children:(0,a.jsxs)(j.Avatar,{className:"h-8 w-8",children:[a.jsx(j.AvatarImage,{src:s.photoURL,alt:"@shadcn"}),a.jsx(j.AvatarFallback,{children:a.jsx(l.Z,{className:"w-5 h-5 hover:scale-105 transition-transform"})})]})})}),(0,a.jsxs)(f.AW,{align:"end",children:[a.jsx(f.Ju,{children:s.email}),a.jsx(f.VD,{}),a.jsx(x.default,{href:"/dashboard/freelancer",children:a.jsx(f.Xi,{children:"Home"})}),a.jsx("div",{children:"freelancer"===v?a.jsx(x.default,{href:"/freelancer/settings/personal-info",children:a.jsx(f.Xi,{children:"Settings"})}):"business"===v?a.jsx(x.default,{href:"/business/settings/business-info",children:a.jsx(f.Xi,{children:"Settings"})}):a.jsx("p",{children:"Loading..."})}),a.jsx(x.default,{href:"/settings/support",children:a.jsx(f.Xi,{children:"Support"})}),a.jsx(f.Xi,{onClick:()=>{T(!0)},children:"Referral"}),a.jsx(f.VD,{}),(0,a.jsxs)(f.Xi,{onClick:()=>{t((0,N.pn)()),u.Z.remove("userType"),u.Z.remove("token"),y.replace("/auth/login")},children:[a.jsx(n.Z,{size:18,className:"mr-2"}),"Logout"]})]})]}),a.jsx(g.Vq,{open:C,onOpenChange:T,children:(0,a.jsxs)(g.cZ,{className:"max-w-2xl w-full px-4 sm:px-6 md:px-8 py-6 rounded-lg",children:[(0,a.jsxs)(g.fK,{children:[a.jsx(g.$N,{className:"text-lg sm:text-xl font-bold",children:"Your Referral Information"}),a.jsx(g.Be,{className:"text-sm sm:text-base text-gray-500",children:"Share this link and code with your friends to invite them:"})]}),R?a.jsx("p",{className:"text-center text-gray-600 text-sm sm:text-base",children:"Loading referral information..."}):k?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("p",{className:"text-sm sm:text-base font-medium text-gray-300",children:"Referral Link:"}),(0,a.jsxs)("div",{className:"mt-2 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2",children:[a.jsx("a",{href:P,target:"_blank",rel:"noopener noreferrer",className:"flex-1 max-w-full break-words sm:truncate",title:P,children:(0,h.N)(P,60)}),a.jsx(p.z,{variant:"ghost",size:"icon",onClick:()=>O(P),className:"ml-2 sm:ml-4",children:a.jsx(i.Z,{size:16})})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("p",{className:"text-sm sm:text-base font-medium text-gray-300",children:"Referral Code:"}),(0,a.jsxs)("div",{className:"mt-2 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2",children:[a.jsx("span",{className:"font-medium flex-1 truncate",children:k}),a.jsx(p.z,{variant:"ghost",size:"icon",onClick:()=>_(k),className:"ml-2 sm:ml-4",children:A===k?a.jsx(d.Z,{size:16,className:"text-green-500"}):a.jsx(o.Z,{size:16})})]})]})]}):a.jsx("p",{className:"text-center text-gray-600 text-sm sm:text-base",children:"No referral code is available for this user."})]})})]})}},48474:(e,s,t)=>{t.d(s,{Z:()=>el,N:()=>er});var a=t(10326),r=t(17577),l=t(25579);let n=({date:e,title:s,summary:t,position:r,isMobile:l,isSelected:n})=>{let i=(e,s=50)=>e.length>s?e.slice(0,s)+"...":e;return a.jsx("div",{className:`flex flex-col group-hover:bg-[#40b3ff] rounded-md   items-center gap-2 relative ${l?"w-64":"w-48 h-20"} ${"top"===r?"mt-[132px]":"bottom"===r?"-mt-[106px]":""} 
          ${n?"bg-[#11a0ff]":"dynamic-card"}
        `,style:{width:"200px",maxWidth:"100%",visibility:"dummy"===s?"hidden":"visible"},children:(0,a.jsxs)("div",{className:`text-center  border-line-bg w-full h-full rounded-md  p-4 ${l?"text-base":"text-sm"}  border`,children:[a.jsx("p",{className:"text-xs",children:new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}),a.jsx("h3",{className:`font-bold  ${l?"text-lg":"text-sm"}`,children:i(s,20)}),t&&a.jsx("p",{className:` mt-0.5 ${l?"text-sm":"text-xs"}`,children:i(t,20)})]})})};var i=t(83855),d=t(91664),o=t(29752),c=t(38443),m=t(71064);let x=({milestone:e})=>{let{text:s,className:t}=(0,m.S)(e.status),[l,n]=(0,r.useState)(!1),i=e.description.split(" "),d=i.length>30?i.slice(0,30).join(" ")+"...":e.description;return(0,a.jsxs)(o.Ol,{children:[(0,a.jsxs)(o.ll,{className:"text-lg md:text-xl font-bold flex justify-between items-center",children:[(0,a.jsxs)("p",{children:["Milestone: ",e.title]}),a.jsx("p",{children:a.jsx(c.C,{className:`${t} px-3 py-1 hidden md:flex text-xs md:text-sm rounded-full`,children:s})})]}),(0,a.jsxs)(o.SZ,{className:"mt-1 text-sm md:text-base",children:[a.jsx("p",{children:l?e.description:d}),i.length>30&&a.jsx("button",{onClick:()=>{n(!l)},className:"text-blue-500 text-xs mt-2",children:l?"Show Less":"Show More"})]})]})};var u=t(47206),h=t(43810),f=t(32933),p=t(94019),j=t(47548),N=t(51682),g=t(56627),b=t(24118),y=t(41156),v=t(25842),w=t(39447),k=t(69508),S=t(30361),C=t(59819),T=t(10143),R=t(77506),z=t(46226),A=t(6260),F=t(84097);let D=({freelancerId:e,onClose:s})=>{let[t,l]=(0,r.useState)(null),[n,i]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{(async()=>{if(e){i(!0);try{let s=await A.b.get(`/freelancer/${e}`);l(s.data)}catch(e){console.error("Error fetching freelancer details:",e),(0,F.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}finally{i(!1)}}})()},[e]),a.jsx(b.Vq,{open:!0,onOpenChange:s,children:(0,a.jsxs)(b.cZ,{className:"sm:max-w-[400px] p-4",children:[n?a.jsx("div",{className:"flex justify-center py-10",children:a.jsx(R.Z,{className:"w-10 h-10 text-blue-500 animate-spin"})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(b.fK,{children:[a.jsx(b.$N,{className:"text-base",children:t?.userName}),a.jsx(b.Be,{className:"text-sm",children:"Details of the freelancer."})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[a.jsx(z.default,{src:t?.profilePic||"https://img.freepik.com/premium-photo/flat-icon-design_1258715-221692.jpg?semt=ais_hybrid",alt:"Profile of freelancer",width:80,height:80,className:"rounded-full border-2 border-gray-300 mb-3 object-cover",unoptimized:!0}),a.jsx("h2",{className:"text-lg font-semibold",children:`${t?.firstName} ${t?.lastName}`}),a.jsx("p",{className:"text-sm text-gray-500",children:t?.role}),(0,a.jsxs)("div",{className:"mt-3 space-y-1 text-center",children:[(0,a.jsxs)("p",{className:"text-sm",children:["Email: ",t?.email]}),(0,a.jsxs)("p",{className:"text-sm",children:["Phone: ",t?.phone]}),(0,a.jsxs)("p",{className:"text-sm",children:["Hourly Rate: $",t?.perHourPrice]})]}),(0,a.jsxs)("div",{className:"flex space-x-3 mt-4",children:[t?.githubLink&&a.jsx("a",{href:t?.githubLink,target:"_blank",rel:"noopener noreferrer",children:a.jsx(d.z,{variant:"outline",size:"sm",children:"GitHub"})}),t?.linkedin&&a.jsx("a",{href:t?.linkedin,target:"_blank",rel:"noopener noreferrer",children:a.jsx(d.z,{variant:"outline",size:"sm",children:"LinkedIn"})}),t?.personalWebsite&&a.jsx("a",{href:t?.personalWebsite,target:"_blank",rel:"noopener noreferrer",children:a.jsx(d.z,{variant:"outline",size:"sm",children:"Website"})})]})]})]}),a.jsx(b.cN,{className:"mt-4"})]})})};var O=t(41190),P=t(82015),_=t(29280);let E=({task:e,milestoneId:s,storyId:t,taskId:l,userType:n,showPermissionDialog:i,setShowPermissionDialog:o,handleConfirmPermissionRequest:c,fetchMilestones:m})=>{let[x,u]=(0,r.useState)({title:e?.title||"",summary:e?.summary||"",taskStatus:e?.taskStatus||"NOT_STARTED"}),h={title:e?.title||"",summary:e?.summary||"",taskStatus:e?.taskStatus||"NOT_STARTED"},f=e?.freelancers[0]?.updatePermissionFreelancer,p=e?.freelancers[0]?.updatePermissionBusiness,j=e?.freelancers[0]?.acceptanceBusiness,N=e?.freelancers[0]?.acceptanceFreelancer,y="business"===n?p&&f&&j:f&&p&&N,v="business"===n&&p&&!f||"freelancer"===n&&f&&!p,w=(e,s)=>{u(t=>({...t,[e]:s}))},k=async()=>{if(JSON.stringify(x)===JSON.stringify(h)){(0,g.Am)({description:"No changes detected. Task update not required.",duration:3e3});return}let a=`/milestones/update/milestone/${s}/story/${t}/task/${e._id}`;try{await A.b.patch(a,{milestoneId:s,storyId:t,taskId:l,userType:n,title:x.title,summary:x.summary,taskStatus:x.taskStatus}),(0,g.Am)({description:"Task updated",duration:3e3}),o(!1),m()}catch(e){(0,g.Am)({description:"Task not update , please try again .",duration:3e3})}};return(0,a.jsxs)(b.Vq,{open:i,onOpenChange:o,children:[a.jsx(b.hg,{className:"hidden",children:"Trigger"}),(0,a.jsxs)(b.cZ,{className:" sm:w-[86vw] md:w-[450px]  p-6 border rounded-md shadow-md",children:[(0,a.jsxs)(b.fK,{children:[a.jsx(b.$N,{children:y?"Update Task Details":"Request Permission"}),a.jsx(b.Be,{children:y?"You have permission to update the task details.":v?"Your request has been sent. Please wait until permission is accepted.":"You don't have permission to update. Please request permission."})]}),y?(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"taskTitle",className:"text-sm font-medium mb-2",children:"Task Title:"}),a.jsx(O.I,{id:"taskTitle",type:"text",value:x.title,onChange:e=>w("title",e.target.value),className:"mb-2 mt-1"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"taskSummary",className:"text-sm font-medium mb-2",children:"Summary:"}),a.jsx(P.g,{id:"taskSummary",value:x.summary,onChange:e=>w("summary",e.target.value),rows:4,className:"mb-2 mt-1"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"taskStatus",className:"text-sm font-medium mb-1",children:"Task Status:"}),(0,a.jsxs)(_.Ph,{value:x.taskStatus,onValueChange:e=>w("taskStatus",e),children:[a.jsx(_.i4,{className:"p-2 mt-1 border rounded-md",children:a.jsx("span",{children:x.taskStatus})}),(0,a.jsxs)(_.Bw,{children:[a.jsx(_.Ql,{value:"NOT_STARTED",children:"NOT_STARTED"}),a.jsx(_.Ql,{value:"ONGOING",children:"ONGOING"}),a.jsx(_.Ql,{value:"COMPLETED",children:"COMPLETED"})]})]})]}),a.jsx(b.cN,{className:"flex mt-2 justify-end gap-3",children:a.jsx(d.z,{onClick:k,variant:"default",children:"Save"})})]}):(0,a.jsxs)(b.cN,{className:"flex mt-2 justify-end gap-4",children:[v?a.jsx(d.z,{variant:"outline",className:"bg-gray-400 text-white px-4 py-2 rounded-md",disabled:!0,children:"Request Sent"}):a.jsx(d.z,{onClick:()=>{c("business"===n,"freelancer"===n,"business"===n,"freelancer"===n)},variant:"outline",className:"bg-blue-600 text-white px-4 py-2 rounded-md",children:"Send Permission Request"}),a.jsx(d.z,{onClick:()=>o(!1),variant:"outline",children:"Cancel"})]})]})]})},I=({task:e,milestoneId:s,storyId:t,fetchMilestones:l})=>{let{type:n}=(0,v.v9)(e=>e.user),[i,d]=(0,r.useState)(!1),[o,c]=(0,r.useState)(null),[m,x]=(0,r.useState)(!1),[u,h]=(0,r.useState)(!1),f=()=>{h(!0)},p=async(a,r,n,i)=>{let d=`/milestones/${s}/story/${t}/task/${e._id}`;try{await A.b.patch(d,{updatePermissionBusiness:a,updatePermissionFreelancer:r,acceptanceBusiness:n,acceptanceFreelancer:i}),h(!1),(0,g.Am)({title:"Success",description:"Permissions updated successfully.",duration:3e3}),l()}catch(e){console.error("Error during permission request:",e),(0,g.Am)({title:"Error",description:"Failed to update permissions. Please try again.",variant:"destructive",duration:3e3})}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(T.h_,{open:m,onOpenChange:x,children:[(0,a.jsxs)(T.$F,{className:"px-2 py-1 absolute bottom-1 right-0 rounded-md",children:[a.jsx(w.Z,{className:"text-3xl w-5 h-5"}),("freelancer"==n?e?.freelancers[0]?.updatePermissionBusiness&&e?.freelancers[0]?.updatePermissionFreelancer&&!e?.freelancers[0]?.acceptanceBusiness:e?.freelancers[0]?.updatePermissionBusiness&&e?.freelancers[0]?.updatePermissionFreelancer&&!e.freelancers[0]?.acceptanceFreelancer)&&a.jsx("span",{className:"absolute top-0 right-2 w-2 h-2 bg-red-500 rounded-full"}),("freelancer"==n?e?.freelancers[0]?.updatePermissionBusiness&&!e?.freelancers[0]?.updatePermissionFreelancer&&e?.freelancers[0]?.acceptanceBusiness:!e?.freelancers[0]?.updatePermissionBusiness&&e?.freelancers[0]?.updatePermissionFreelancer&&e.freelancers[0]?.acceptanceFreelancer)&&a.jsx("span",{className:"absolute top-0 right-2 w-2 h-2 bg-yellow-500 rounded-full"})]}),a.jsx(T.AW,{align:"end",className:"w-56 p-2 border rounded-md shadow-md",children:"freelancer"===n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(T.Xi,{className:"flex items-center gap-2",onClick:f,children:[a.jsx(k.Z,{className:"w-4 h-4 text-blue-500"}),"Update Task Details"]}),!e?.freelancers[0]?.updatePermissionFreelancer&&e?.freelancers[0]?.updatePermissionBusiness&&!e?.freelancers[0]?.acceptanceFreelancer&&(0,a.jsxs)(T.Xi,{className:"flex whitespace-nowrap text-xs  items-center gap-2",onClick:()=>p(!0,!0,!0,!1),children:[a.jsx(S.Z,{className:"w-4 h-4 text-yellow-500"}),"Approve Updates permission"]})]}):"business"===n?(0,a.jsxs)(a.Fragment,{children:[!e?.freelancers[0]?.updatePermissionBusiness&&e?.freelancers[0].updatePermissionFreelancer&&!e?.freelancers[0]?.acceptanceBusiness&&(0,a.jsxs)(T.Xi,{className:"flex whitespace-nowrap text-xs  items-center gap-2",onClick:()=>p(!0,!0,!1,!0),children:[a.jsx(S.Z,{className:"w-4 h-4 text-yellow-500"}),"Approve Updates permission"]}),e.freelancers.length>0&&(0,a.jsxs)(T.Xi,{className:"flex items-center gap-2",onClick:()=>{c(e?.freelancers[0]?.freelancerId),d(!0)},children:[a.jsx(C.Z,{className:"w-4 h-4 text-gray-500"}),"View Freelancer"]}),(0,a.jsxs)(T.Xi,{className:"flex items-center gap-2",onClick:f,children:[a.jsx(k.Z,{className:"w-4 h-4 text-blue-500"}),"Update Task Details"]})]}):a.jsx(T.Xi,{disabled:!0,className:"text-gray-400",children:"No actions available for this user type."})})]}),i&&o&&a.jsx(D,{freelancerId:o,onClose:()=>{c(null),d(!1)}}),a.jsx(E,{fetchMilestones:l,userType:n,task:e,milestoneId:s,storyId:t,taskId:e._id,showPermissionDialog:u,setShowPermissionDialog:h,handleConfirmPermissionRequest:p})]})};var Z=t(56556),$=t(51027),U=t(28260),L=t(88307),B=t(51223);let Y=r.forwardRef(({className:e,...s},t)=>a.jsx(U.mY,{ref:t,className:(0,B.cn)("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",e),...s}));Y.displayName=U.mY.displayName;let q=r.forwardRef(({className:e,...s},t)=>(0,a.jsxs)("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[a.jsx(L.Z,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),a.jsx(U.mY.Input,{ref:t,className:(0,B.cn)("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",e),...s})]}));q.displayName=U.mY.Input.displayName;let V=r.forwardRef(({className:e,...s},t)=>a.jsx(U.mY.List,{ref:t,className:(0,B.cn)("max-h-[300px] overflow-y-auto overflow-x-hidden",e),...s}));V.displayName=U.mY.List.displayName;let G=r.forwardRef((e,s)=>a.jsx(U.mY.Empty,{ref:s,className:"py-6 text-center text-sm",...e}));G.displayName=U.mY.Empty.displayName;let M=r.forwardRef(({className:e,...s},t)=>a.jsx(U.mY.Group,{ref:t,className:(0,B.cn)("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",e),...s}));M.displayName=U.mY.Group.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(U.mY.Separator,{ref:t,className:(0,B.cn)("-mx-1 h-px bg-border",e),...s})).displayName=U.mY.Separator.displayName;let X=r.forwardRef(({className:e,...s},t)=>a.jsx(U.mY.Item,{ref:t,className:(0,B.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50",e),...s}));X.displayName=U.mY.Item.displayName;let W=({milestoneId:e,story:s,idx:t,milestoneStoriesLength:l,setIsTaskDialogOpen:n,isFreelancer:x=!1,fetchMilestones:f})=>{let{text:p,className:b}=(0,m.S)(s.storyStatus),[y,v]=(0,r.useState)(!1),[w,k]=(0,r.useState)(""),[S,C]=(0,r.useState)(null),T=e=>{navigator.clipboard.writeText(e),(0,g.Am)({description:"URL copied!!",duration:900}),v(!1),k("")},R=(e,s=50)=>e?.length>s?e.slice(0,s)+"...":e;return(0,a.jsxs)(Z.Qd,{value:s._id??"",className:`py-2  ${t===l-1?"border-b-0 ":"border-b-2"} `,children:[a.jsx(Z.o4,{className:"flex hover:no-underline items-center under px-4 w-full ",children:(0,a.jsxs)("div",{className:"flex justify-between items-center w-full px-4 py-1 rounded-lg duration-300",children:[(0,a.jsxs)("h3",{className:"text-lg md:text-xl flex items-center font-semibold",children:[s.title,(0,a.jsxs)("span",{className:"text-sm",children:[s?.tasks?.[0]?.freelancers?.[0]&&x?!s?.tasks[0]?.freelancers[0]?.acceptanceFreelancer&&s?.tasks[0]?.freelancers[0]?.updatePermissionBusiness&&!s?.tasks[0]?.freelancers[0]?.updatePermissionFreelancer&&s?.tasks[0]?.freelancers[0]?.acceptanceBusiness&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("span",{className:"text-yellow-500 ml-5 hidden md:flex",children:"Update Req"}),a.jsx("span",{className:"text-yellow-500 ml-5 rounded-full flex md:hidden w-2 h-2 bg-yellow-500"})]}):!s?.tasks?.[0]?.freelancers?.[0]?.acceptanceBusiness&&s?.tasks?.[0]?.freelancers?.[0]?.updatePermissionFreelancer&&!s?.tasks?.[0]?.freelancers?.[0]?.updatePermissionBusiness&&s?.tasks?.[0]?.freelancers?.[0]?.acceptanceFreelancer&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("span",{className:"text-yellow-500 ml-5 hidden md:flex",children:"Update Req"}),a.jsx("span",{className:"text-yellow-500 ml-5 rounded-full flex md:hidden w-2 h-2 bg-yellow-500"})]}),s?.tasks?.[0]?.freelancers?.[0]&&x?s?.tasks[0]?.freelancers[0]?.updatePermissionBusiness&&s?.tasks[0]?.freelancers[0]?.updatePermissionFreelancer&&!s?.tasks[0]?.freelancers[0]?.acceptanceBusiness&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("span",{className:"text-green-500 ml-5 hidden md:flex",children:"Req Approve"}),a.jsx("span",{className:"text-green-500 ml-5 rounded-full flex md:hidden w-2 h-2 bg-green-500"})]}):s?.tasks?.[0]?.freelancers?.[0]?.updatePermissionFreelancer&&s?.tasks?.[0]?.freelancers?.[0]?.updatePermissionBusiness&&!s?.tasks?.[0]?.freelancers?.[0]?.acceptanceFreelancer&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("span",{className:"text-green-500 ml-5 hidden md:flex",children:"Req Approve"}),a.jsx("span",{className:"text-green-500 ml-5 rounded-full flex md:hidden w-2 h-2 bg-green-500"})]})]})]}),a.jsx(c.C,{className:`${b} px-3 py-1 hidden md:flex text-xs md:text-sm rounded-full`,children:p})]})}),(0,a.jsxs)(Z.vF,{className:"w-full px-4 py-4 sm:px-6 sm:py-4 md:px-8 md:py-6 lg:px-10 lg:py-8",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[a.jsx(c.C,{className:`${b} px-2 py-1 block md:hidden text-xs md:text-sm rounded-full`,style:{width:"fit-content"},children:p}),a.jsx("p",{className:"leading-relaxed px-2 py-1  text-sm md:text-base",children:s.summary}),(0,a.jsxs)("div",{className:"space-y-4 hidden md:flex justify-start items-center gap-4",children:[a.jsx("h4",{className:"text-lg md:text-xl font-semibold mt-2",children:"Important URLs:"}),(0,a.jsxs)($.J2,{open:y,onOpenChange:v,children:[a.jsx($.xo,{asChild:!0,children:(0,a.jsxs)(d.z,{variant:"outline",role:"combobox","aria-expanded":y,className:"w-[300px] justify-between",children:[R(w,23)||"Select or search URL...",a.jsx(u.Z,{className:"opacity-50"})]})}),a.jsx($.yk,{className:"w-[300px] p-0",children:(0,a.jsxs)(Y,{children:[a.jsx(q,{placeholder:"Search URL...",className:"h-9"}),(0,a.jsxs)(V,{children:[a.jsx(G,{children:"No URL found."}),a.jsx(M,{children:s.importantUrls.map(e=>a.jsx("div",{children:(0,a.jsxs)(N.zs,{children:[a.jsx(N.Yi,{asChild:!0,children:(0,a.jsxs)(X,{value:e.urlName,className:"cursor-pointer",onSelect:()=>{k(e.urlName),v(!1)},children:[R(e.urlName,20),a.jsx(d.z,{variant:"ghost",size:"sm",className:"ml-auto cursor-pointer",onClick:s=>{s.stopPropagation(),T(e.url)},children:a.jsx(h.Z,{className:"w-4 h-4"})})]})}),a.jsx(N.bZ,{className:"w-auto py-1",children:e.url})]})},e.urlName))})]})]})})]})]})]}),a.jsx(o.Zb,{className:"px-2  mt-5 py-3",children:s?.tasks?.length>0?(0,a.jsxs)("div",{className:"bg-transparent",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center px-3 mt-4",children:[a.jsx("h4",{className:"text-lg md:text-xl font-semibold",children:"Tasks:"}),!x&&(0,a.jsxs)(d.z,{className:"md:px-3 px-2 py-0 md:py-1 text-sm sm:text-base",onClick:()=>n(!0),children:[a.jsx(i.Z,{size:15})," Add Task"]})]}),(0,a.jsxs)(j.lr,{className:"w-[85vw] md:w-full relative mt-4",children:[a.jsx(j.KI,{className:"flex flex-nowrap gap-2 md:gap-0",children:s.tasks.map(t=>{let{className:r}=(0,m.S)(t.taskStatus);return a.jsx(j.d$,{className:"min-w-0 mt-2 w-full md:basis-1/2 sm:w-full md:w-1/2 lg:w-1/3",children:a.jsx("div",{className:" p-0 md:p-2 mt-5",children:(0,a.jsxs)(o.Zb,{className:"w-full cursor-pointer  border relative rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300",onClick:()=>C(t),children:[a.jsx(o.Ol,{className:"p-2 md:p-4 ",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx(o.ll,{className:"text-sm md:text-lg font-medium",children:R(t.title,20)}),a.jsx(c.C,{className:`${r} px-2 py-0.5 text-xs md:text-sm rounded-md`,children:t.taskStatus})]})}),a.jsx(o.aY,{className:"p-4",children:a.jsx("p",{className:"text-sm leading-relaxed",children:R(t.summary,30)})}),a.jsx("div",{onClick:e=>e.stopPropagation(),children:a.jsx(I,{fetchMilestones:f,milestoneId:e,storyId:s._id,task:t})})]})})},t._id)})}),a.jsx("div",{className:`${s?.tasks?.length>(window.innerWidth>768?2:1)?"block":"hidden"}`,children:s?.tasks?.length>(window.innerWidth>=768?2:1)&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(j.am,{className:"absolute top-1 md:top-2 left-2 transform -translate-y-1/2 shadow rounded-full p-2"}),a.jsx(j.Pz,{className:"absolute top-1 md:top-2  right-2 transform -translate-y-1/2 shadow rounded-full p-2"})]})})]})]}):(0,a.jsxs)("div",{className:"text-center mt-12 p-4 rounded-md",children:[x?(0,a.jsxs)("p",{children:["This ",s.title," currently has no tasks. Wait until the business assigns you to any task."]}):(0,a.jsxs)("p",{children:["This ",s.title," currently has no tasks. Add tasks to ensure smooth progress and better tracking."]}),!x&&(0,a.jsxs)(d.z,{className:"mt-2 px-3 py-1 text-sm sm:text-base",onClick:()=>n(!0),children:[a.jsx(i.Z,{size:15})," Add Task"]})]})})]}),a.jsx(K,{task:S,open:!!S,onClose:()=>C(null)})]},s._id)},K=({task:e,open:s,onClose:t})=>e?a.jsx(b.Vq,{open:s,onOpenChange:t,children:(0,a.jsxs)(b.cZ,{className:"max-w-lg rounded-lg mx-auto w-[90vw] md:w-auto shadow-lg ",children:[a.jsx(b.fK,{children:a.jsx(b.$N,{className:"text-xl font-bold",children:e.title})}),(0,a.jsxs)(b.Be,{className:"text-sm mt-4 leading-relaxed",children:[(0,a.jsxs)("p",{className:"mt-2 text-sm",children:["Task status:"," ",a.jsx("span",{className:"font-medium ",children:e?.taskStatus})]}),(0,a.jsxs)("p",{className:"mt-2 text-sm",children:["Freelancer Name:"," ",a.jsx("span",{className:"font-medium ",children:e?.freelancers[0]?.freelancerName})]}),(0,a.jsxs)("p",{className:"mt-2 text-sm",children:["Payment Status:"," ",a.jsx("span",{className:"font-medium ",children:e?.freelancers[0]?.paymentStatus})]}),(0,a.jsxs)(y.iA,{className:"mt-4 border border-gray-300",children:[a.jsx(y.xD,{children:(0,a.jsxs)(y.SC,{className:"",children:[a.jsx(y.ss,{className:"font-semibold text-left",children:"User"}),(0,a.jsxs)(y.ss,{className:"font-semibold text-left",children:["Freelancer"," "]}),(0,a.jsxs)(y.ss,{className:"font-semibold text-left",children:["Business"," "]})]})}),a.jsx(y.RM,{children:e.freelancers.map((e,s)=>(0,a.jsxs)(y.SC,{className:"border-b border-gray-200",children:[a.jsx(y.pj,{className:"py-2 px-4 font-medium",children:"Update Permission"}),a.jsx(y.pj,{className:"py-2 px-4 text-center",children:a.jsx("div",{className:"flex justify-center items-center",children:e.updatePermissionFreelancer&&e.updatePermissionFreelancer&&e.acceptanceFreelancer?a.jsx(f.Z,{className:"text-green-600 w-5 h-5"}):a.jsx(p.Z,{className:"text-red-600 w-5 h-5"})})}),a.jsx(y.pj,{className:"py-2 px-4 text-center",children:a.jsx("div",{className:"flex justify-center items-center",children:e.updatePermissionBusiness&&e.updatePermissionFreelancer&&e.acceptanceBusiness?a.jsx(f.Z,{className:"text-green-600 w-5 h-5"}):a.jsx(p.Z,{className:"text-red-600 w-5 h-5"})})})]},s))})]}),a.jsx("p",{className:"mt-3",children:e.summary})]}),a.jsx("div",{className:"flex justify-end mt-4",children:a.jsx(b.GG,{asChild:!0,children:a.jsx(d.z,{variant:"outline",children:"Close"})})})]})}):null;var H=t(35047),J=t(3594);let Q=({isDialogOpen:e,setIsDialogOpen:s,formData:t,handleInputChange:l,handelSubmit:n,handleFreelancerSelect:i})=>{let{project_id:o}=(0,H.useParams)(),[m,x]=(0,r.useState)({title:!1,summary:!1,taskStatus:!1,freelancer:!1}),[u,h]=(0,r.useState)([]),[f,p]=(0,r.useState)([]),[j,N]=(0,r.useState)(null),[g,y]=(0,r.useState)(!1),v=()=>{let e={title:!t.title.trim(),summary:!t.summary.trim(),taskStatus:!t.taskStatus,freelancer:!t.freelancers};return x(e),!Object.values(e).some(e=>e)};(0,r.useEffect)(()=>{let s=async()=>{y(!0);try{let e=await A.b.get(`/project/get-freelancer/${o}`);h(e.data.freelancers.freelancerData),p(e.data.freelancers.freelancerData)}catch(e){console.error("Failed to fetch freelancers:",e),(0,F.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}finally{y(!1)}};e&&s()},[e,o]);let w=e=>{let s=e.toLowerCase();p(u.filter(e=>e.userName.toLowerCase().includes(s)||e.email.toLowerCase().includes(s)))};return a.jsx(b.Vq,{open:e,onOpenChange:s,children:(0,a.jsxs)(b.cZ,{className:"w-full max-w-lg",children:[a.jsx(b.fK,{children:a.jsx(b.$N,{children:"Add New Task"})}),g?a.jsx("div",{className:"flex justify-center py-10",children:a.jsx(R.Z,{className:"w-10 h-10 text-blue-500 animate-spin"})}):(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),v()&&n(e)},children:[(0,a.jsxs)("div",{className:"space-y-4 p-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"title",className:"block text-sm font-medium mb-1",children:"Task Title"}),a.jsx(O.I,{id:"title",name:"title",placeholder:"Task Title",value:t.title,onChange:l,required:!0,className:`${m.title?"border-red-500":""}`}),m.title&&a.jsx("p",{className:"text-red-500 text-xs mt-1",children:"Task title is required."})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"summary",className:"block text-sm font-medium mb-1",children:"Task Summary"}),a.jsx(P.g,{id:"summary",name:"summary",placeholder:"Task Summary",value:t.summary,onChange:l,required:!0,className:`${m.summary?"border-red-500":""}`}),m.summary&&a.jsx("p",{className:"text-red-500 text-xs mt-1",children:"Task summary is required."})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("label",{htmlFor:"taskStatus",className:"w-1/3 text-sm font-medium mb-1",children:"Task Status"}),(0,a.jsxs)(T.h_,{children:[a.jsx(T.$F,{asChild:!0,children:a.jsx(d.z,{variant:"ghost",className:`w-full border rounded-md text-left ${m.taskStatus?"border-red-500":""}`,children:t.taskStatus?({NOT_STARTED:"Not Started",ONGOING:"On Going",COMPLETED:"Completed"})[t.taskStatus]:"Select Status"})}),(0,a.jsxs)(T.AW,{children:[a.jsx(T.Xi,{onSelect:()=>l("NOT_STARTED"),children:"Not Started"}),a.jsx(T.Xi,{onSelect:()=>l("ONGOING"),children:"On Going"}),a.jsx(T.Xi,{onSelect:()=>l("COMPLETED"),children:"Completed"})]})]})]}),m.taskStatus&&a.jsx("p",{className:"text-red-500 text-xs mt-1",children:"Task status is required."}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("label",{htmlFor:"freelancer",className:"w-1/3 text-sm font-medium mb-1",children:"Freelancer"}),(0,a.jsxs)($.J2,{children:[a.jsx($.xo,{className:"overscroll-y-none",asChild:!0,children:a.jsx(d.z,{variant:"ghost",className:`w-full border rounded-md text-left ${m.freelancer?"border-red-500":""}`,children:j||"Select Freelancer"})}),a.jsx($.yk,{className:"w-full p-0",children:(0,a.jsxs)(Y,{children:[a.jsx(q,{placeholder:"Search freelancer by username or email...",onValueChange:e=>w(e)}),a.jsx(V,{className:" overflow-x-visible max-h-[300px]",children:0===f.length?a.jsx(G,{children:"No freelancers found."}):a.jsx(M,{className:" block overflow-auto",children:a.jsx("div",{className:"space-y-2 px-4 py-2",children:f.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between gap-1 p-3 rounded-lg border shadow-sm hover:shadow-md cursor-pointer",onClick:()=>{N(e.userName),i(e)},children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(J.Avatar,{className:"h-9 w-9",children:a.jsx(J.AvatarFallback,{children:e.userName.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium",children:e.userName}),a.jsx("div",{className:"text-xs text-gray-500",children:er(e.email,20)})]})]}),a.jsx(c.C,{children:`${e.role||"N/A"}`})]},e._id))})})})]})})]})]}),m.freelancer&&a.jsx("p",{className:"text-red-500 text-xs mt-1",children:"Freelancer selection is required."})]}),(0,a.jsxs)(b.cN,{children:[a.jsx(d.z,{type:"submit",children:"Add Task"}),a.jsx(d.z,{type:"button",variant:"secondary",className:"mb-3",onClick:()=>s(!1),children:"Cancel"})]})]})]})})},ee=({isDialogOpen:e,setIsDialogOpen:s,handleInputChange:t,handleCloseDialog:l,handleStorySubmit:n,storyData:i,handleRemoveUrl:o,handleAddUrl:c,milestones:m,resetFields:x})=>{let[u,h]=(0,r.useState)({}),f=()=>{let e={};return i.title.trim()||(e.title="Story title is required."),i.summary.trim()||(e.summary="Summary is required."),i.importantUrls.some(e=>!e.urlName.trim()||!e.url.trim())&&(e.importantUrls="All URL fields are required."),i.storyStatus||(e.storyStatus="Story status is required."),h(e),0===Object.keys(e).length};return a.jsx(b.Vq,{open:e,onOpenChange:s,children:(0,a.jsxs)(b.cZ,{children:[a.jsx(b.fK,{children:a.jsx(b.$N,{children:"Add New Story"})}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),f()&&(n(e,i,m),x(),l())},children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"storyTitle",className:"block text-sm font-medium mb-1",children:"Story Title"}),a.jsx(O.I,{placeholder:"Enter story title",value:i.title,onChange:e=>t("title",e.target.value),required:!0}),u.title&&a.jsx("p",{className:"text-red-500 text-sm mt-1",children:u.title})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"storySummary",className:"block text-sm font-medium mb-1",children:"Summary"}),a.jsx(P.g,{placeholder:"Enter summary",value:i.summary,onChange:e=>t("summary",e.target.value),required:!0}),u.summary&&a.jsx("p",{className:"text-red-500 text-sm mt-1",children:u.summary})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"storyUrls",className:"block text-sm font-medium mb-2",children:"Important URLs"}),i.importantUrls.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsxs)("div",{className:"flex w-full items-center gap-2",children:[a.jsx(O.I,{type:"text",placeholder:"URL Name",value:e.urlName,className:"w-1/2",onChange:a=>t("importantUrls",{...e,urlName:a.target.value},s),required:!0}),a.jsx(O.I,{type:"text",placeholder:"URL",value:e.url,className:"w-1/2",onChange:a=>t("importantUrls",{...e,url:a.target.value},s),required:!0})]}),a.jsx(d.z,{type:"button",variant:"ghost",onClick:()=>o(s),className:"text-red-500 px-1",children:a.jsx(p.Z,{})})]},s)),u.importantUrls&&a.jsx("p",{className:"text-red-500 text-sm mt-1",children:u.importantUrls}),a.jsx(d.z,{type:"button",variant:"secondary",onClick:c,className:"mt-2",children:"Add URL"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("label",{htmlFor:"storyStatus",className:"text-sm flex justify-center items-center font-medium mb-2 w-1/4",children:"Story Status"}),(0,a.jsxs)(T.h_,{children:[a.jsx(T.$F,{asChild:!0,children:a.jsx(d.z,{className:"w-3/4",variant:"outline",children:i.storyStatus?({NOT_STARTED:"Not Started",ONGOING:"On Going",COMPLETED:"Completed"})[i.storyStatus]:"Select Status"})}),(0,a.jsxs)(T.AW,{className:"w-56",children:[a.jsx(T.Xi,{onClick:()=>t("storyStatus","NOT_STARTED"),children:"Not Started"}),a.jsx(T.Xi,{onClick:()=>t("storyStatus","ONGOING"),children:"On Going"}),a.jsx(T.Xi,{onClick:()=>t("storyStatus","COMPLETED"),children:"Completed"})]})]}),u.storyStatus&&a.jsx("p",{className:"text-red-500 text-sm mt-1",children:u.storyStatus})]})]}),(0,a.jsxs)(b.cN,{className:"mt-4",children:[a.jsx(d.z,{type:"button",variant:"ghost",onClick:l,children:"Cancel"}),a.jsx(d.z,{type:"submit",children:"Submit"})]})]})]})})},es=({setStoryData:e}={setStoryData:()=>{}})=>{let[s,t]=(0,r.useState)(!1);return{isDialogOpen:s,handleOpenDialog:()=>{t(!0)},handleCloseDialog:()=>{t(!1)},handleStoryInputChange:(s,t,a)=>{e(e=>{if("importantUrls"===s&&"number"==typeof a){let s=[...e.importantUrls];return s[a]=t,{...e,importantUrls:s}}return{...e,[s]:t}})},handleAddUrl:()=>{e(e=>({...e,importantUrls:[...e.importantUrls,{urlName:"",url:""}]}))},handleRemoveUrl:s=>{e(e=>({...e,importantUrls:e.importantUrls.filter((e,t)=>t!==s)}))},setIsDialogOpen:t}},et=({milestone:e,fetchMilestones:s,handleStorySubmit:t,isFreelancer:l=!1})=>{let[n,c]=(0,r.useState)(void 0),[m,u]=(0,r.useState)(!1),[h,f]=(0,r.useState)(!1),[p,j]=(0,r.useState)({summary:"",title:"",taskStatus:"NOT_STARTED",freelancers:[{freelancerId:"",freelancerName:"",cost:0}]}),[N,g]=(0,r.useState)({title:"",summary:"",storyStatus:"",tasks:[],importantUrls:[{urlName:"",url:""}]}),{handleRemoveUrl:b,handleAddUrl:y,handleStoryInputChange:v}=es({setStoryData:g});return(0,a.jsxs)("div",{className:"w-full px-0 md:px-0 lg:px-0 py-3 md:py-6 max-w-5xl mx-auto rounded-lg",children:[(0,a.jsxs)(o.Zb,{className:"pb-5 mx-3",children:[a.jsx(x,{milestone:e}),(0,a.jsxs)(o.Zb,{className:` px-0 md:px-10  ${n?"mx-0":"mx-4"} md:mx-3 my-2`,children:[(e.stories??[]).length>0&&(0,a.jsxs)("div",{className:"flex p-4 justify-between items-center mt-4",children:[a.jsx("h3",{className:"text-lg md:text-xl font-semibold",children:"Stories:"}),!l&&(0,a.jsxs)(d.z,{className:"px-3 py-1 text-sm sm:text-base",onClick:()=>f(!0),children:[a.jsx(i.Z,{size:15})," Add Story"]})]}),a.jsx(Z.UQ,{type:"single",collapsible:!0,value:n,onValueChange:e=>c(e),className:"pt-4 px-0",children:(e.stories??[]).length>0?(e.stories??[]).map((t,r)=>a.jsx(W,{fetchMilestones:s,isFreelancer:l,milestoneId:e._id,story:t,idx:r,milestoneStoriesLength:(e.stories??[]).length,setIsTaskDialogOpen:u},r)):(0,a.jsxs)("div",{className:"text-center mt-4 p-4 rounded-md",children:[l?(0,a.jsxs)("p",{children:["This ",e.title," currently has no associated stories. Please wait until the business adds a story."]}):(0,a.jsxs)("p",{children:["This ",e.title," currently has no associated stories. Please add a story to ensure smooth progress and effective tracking."]}),!l&&(0,a.jsxs)(d.z,{className:"mt-2 px-3 py-1 text-sm sm:text-base",onClick:()=>f(!0),children:[a.jsx(i.Z,{size:15})," Add Story"]})]})})]})]}),m&&a.jsx(Q,{isDialogOpen:m,setIsDialogOpen:u,formData:p,handleInputChange:e=>{let{name:s,value:t}="string"==typeof e?{name:"taskStatus",value:e}:e.target;j(e=>({...e,[s]:t}))},handelSubmit:s=>{if(s.preventDefault(),e.stories){let a=e.stories.find(e=>e._id===n);a&&t(s,a,e,!0,{formData:p,storyId:n})}j({summary:"",title:"",taskStatus:"NOT_STARTED",freelancers:[{freelancerId:"",freelancerName:"",cost:0}]}),u(!1)},handleFreelancerSelect:e=>{j(s=>({...s,freelancers:[{freelancerId:e._id,freelancerName:e.userName,cost:Number(e.perHourPrice)}]}))}}),h&&a.jsx(ee,{isDialogOpen:h,setIsDialogOpen:f,handleInputChange:v,handleCloseDialog:()=>f(!1),storyData:N,resetFields:()=>{g({title:"",summary:"",storyStatus:"",tasks:[],importantUrls:[{urlName:"",url:""}]})},handleRemoveUrl:b,handleAddUrl:y,milestones:e,handleStorySubmit:t})]})};var ea=t(3236);let er=(e,s=50)=>e.length>s?e.slice(0,s)+"...":e,el=({milestones:e,fetchMilestones:s,handleStorySubmit:t,isFreelancer:i=!1})=>{let d=(0,r.useRef)(null),[o,c]=(0,r.useState)(0);(0,r.useEffect)(()=>{let e=d.current,s=s=>{if(e){let t=e.scrollWidth-e.clientWidth;Math.abs(s.deltaY)>Math.abs(s.deltaX)&&(s.preventDefault(),e.scrollLeft+s.deltaY>t?e.scrollLeft=t:e.scrollLeft+s.deltaY<0?e.scrollLeft=0:e.scrollLeft+=s.deltaY)}};return e&&e.addEventListener("wheel",s,{passive:!1}),()=>{e&&e.removeEventListener("wheel",s)}},[d]);let m=(s,t)=>{t<0||t>=e.length||c(t)},x=1===e.length?[...e,{_id:"dummy",title:"dummy",description:"",stories:[],storyStatus:"",createdAt:""}]:e;return(0,a.jsxs)("div",{className:"h-auto ",children:[a.jsx("div",{className:"w-[100vw] max-w-6xl mx-auto px-4 py-12 relative",children:(0,a.jsxs)(ea.x,{className:"w-full whitespace-nowrap rounded-md border",children:[e&&(0,a.jsxs)("div",{ref:d,className:"hidden md:block ",children:[a.jsx("div",{className:"absolute overflow-hidden left-0 right-0 top-1/2 h-1 line-bg bg-gray-500 transform -translate-y-1/2"}),a.jsx("div",{className:"relative cursor-pointer flex items-center whitespace-nowrap overflow-x-auto overflow-y-scroll px-4 py-6  no-scrollbar",children:x.map((s,t)=>(0,a.jsxs)("div",{className:`relative group px-16 inline-block ${1===x.length?"mx-auto ":""}`,onClick:()=>m(s,t),children:[a.jsx("div",{className:`absolute ${1===e.length&&"dummy"===s.title?"hidden":""} ${t===o?"bg-[var(--dot-hover-bg-color)] border-[var(--dot-hover-border-color)]":"border-[var(--dot-border-color)]"} top-1/2 transform -translate-y-1/2 w-5 h-5 bg-[var(--dot-bg-color)] rounded-full border-4 group group-hover:bg-[var(--dot-hover-bg-color)] group-hover:border-[var(--dot-hover-border-color)] `,style:{left:"50%",transform:"translate(-50%, -50%)"},children:a.jsx("div",{className:`absolute left-1/2 transform -translate-x-1/2 ${t%2==0?"-top-6":"top-3"} ${t===o?"bg-[#11a0ff] text-[#11a0ff]":""}  group-hover:text-[#11a0ff] overflow-hidden `,children:"|"})}),a.jsx(n,{date:s.createdAt||"",title:s.title,summary:s.description,position:t%2==0?"bottom":"top",isSelected:t===o}),"dummy"===s._id&&a.jsx("div",{style:{display:"none"}})]},t))})]}),a.jsx(ea.B,{className:"cursor-pointer",orientation:"horizontal"})]})}),a.jsx("div",{className:"flex pb-8 justify-center  items-center md:hidden",children:a.jsx(j.lr,{children:a.jsx(j.KI,{className:"flex min-h-[200px] items-center w-[100vw] gap-4",children:e.map((s,t)=>a.jsx(j.d$,{className:"flex relative justify-center top-0  h-auto items-center",onClick:()=>m(s,t+1),children:"dummy"!==s._id&&(0,a.jsxs)("div",{className:"border p-6 border-line-bg  rounded-lg shadow-lg  w-full max-w-[80vw]",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-xs",children:s.createdAt&&new Date(s.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}),a.jsx("h3",{className:"font-medium text-lg mt-2",children:er(s.title,16)}),s.description&&a.jsx("p",{className:"text-sm mt-1",children:er(s.description)})]}),a.jsx(j.am,{className:"absolute top-[117%] left-12 transform -translate-y-1/2",children:a.jsx("button",{onClick:()=>m(s,t-1),className:"bg-white/20 rounded-full p-3 text-white hover:bg-white/30",disabled:0===t,children:a.jsx(l.hf,{})})}),a.jsx(j.Pz,{className:"absolute top-[117%] right-8 transform -translate-y-1/2",children:a.jsx("button",{onClick:()=>m(s,t+1),className:`bg-white/20 rounded-full p-3 text-white ${t===e.length-1?"cursor-not-allowed":"hover:bg-white/30"}`,disabled:t===e.length-1,children:a.jsx(l.Jp,{})})})]})},t))})})}),null!==o&&a.jsx("div",{className:"mt-10",children:a.jsx(et,{milestone:e[o],fetchMilestones:s,handleStorySubmit:t,isFreelancer:i})})]})}},86144:(e,s,t)=>{t.d(s,{Z:()=>f});var a=t(10326),r=t(17577),l=t.n(r),n=t(90434),i=t(99469),d=t(39183),o=(t(15919),t(51223));let c=r.forwardRef(({...e},s)=>a.jsx("nav",{ref:s,"aria-label":"breadcrumb",...e}));c.displayName="Breadcrumb";let m=r.forwardRef(({className:e,...s},t)=>a.jsx("ol",{ref:t,className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",e),...s}));m.displayName="BreadcrumbList";let x=r.forwardRef(({className:e,...s},t)=>a.jsx("li",{ref:t,className:(0,o.cn)("inline-flex items-center gap-1.5",e),...s}));x.displayName="BreadcrumbItem";let u=r.forwardRef(({asChild:e,className:s,...t},r)=>{let l=e?i.g7:"a";return a.jsx(l,{ref:r,className:(0,o.cn)("transition-colors hover:text-foreground",s),...t})});u.displayName="BreadcrumbLink",r.forwardRef(({className:e,...s},t)=>a.jsx("span",{ref:t,role:"link","aria-disabled":"true","aria-current":"page",className:(0,o.cn)("font-normal text-foreground",e),...s})).displayName="BreadcrumbPage";let h=({children:e,className:s,...t})=>a.jsx("li",{role:"presentation","aria-hidden":"true",className:(0,o.cn)("[&>svg]:size-3.5",s),...t,children:e??a.jsx(d.Z,{})});h.displayName="BreadcrumbSeparator";let f=({items:e})=>a.jsx(c,{className:"hidden md:flex",children:a.jsx(m,{children:e.map((s,t)=>(0,a.jsxs)(l().Fragment,{children:[a.jsx(x,{children:a.jsx(u,{asChild:!0,children:a.jsx(n.default,{href:s.link,children:s.label})})}),t!==e.length-1&&a.jsx(h,{})]},t))})})},56556:(e,s,t)=>{t.d(s,{Qd:()=>o,UQ:()=>d,o4:()=>c,vF:()=>m});var a=t(10326),r=t(17577),l=t(57793),n=t(941),i=t(51223);let d=l.fC,o=r.forwardRef(({className:e,...s},t)=>a.jsx(l.ck,{ref:t,className:(0,i.cn)("border-b",e),...s}));o.displayName="AccordionItem";let c=r.forwardRef(({className:e,children:s,...t},r)=>a.jsx(l.h4,{className:"flex",children:(0,a.jsxs)(l.xz,{ref:r,className:(0,i.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",e),...t,children:[s,a.jsx(n.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})}));c.displayName=l.xz.displayName;let m=r.forwardRef(({className:e,children:s,...t},r)=>a.jsx(l.VY,{ref:r,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...t,children:a.jsx("div",{className:(0,i.cn)("pb-4 pt-0",e),children:s})}));m.displayName=l.VY.displayName},38443:(e,s,t)=>{t.d(s,{C:()=>i});var a=t(10326);t(17577);var r=t(28671),l=t(51223);let n=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:s,...t}){return a.jsx("div",{className:(0,l.cn)(n({variant:s}),e),...t})}},47548:(e,s,t)=>{t.d(s,{KI:()=>u,Pz:()=>p,am:()=>f,d$:()=>h,lr:()=>x});var a=t(10326),r=t(17577),l=t(481),n=t(86333),i=t(24230),d=t(51223),o=t(91664);let c=r.createContext(null);function m(){let e=r.useContext(c);if(!e)throw Error("useCarousel must be used within a <Carousel />");return e}let x=r.forwardRef(({orientation:e="horizontal",opts:s,setApi:t,plugins:n,className:i,children:o,...m},x)=>{let[u,h]=(0,l.Z)({...s,axis:"horizontal"===e?"x":"y"},n),[f,p]=r.useState(!1),[j,N]=r.useState(!1),g=r.useCallback(e=>{e&&(p(e.canScrollPrev()),N(e.canScrollNext()))},[]),b=r.useCallback(()=>{h?.scrollPrev()},[h]),y=r.useCallback(()=>{h?.scrollNext()},[h]),v=r.useCallback(e=>{"ArrowLeft"===e.key?(e.preventDefault(),b()):"ArrowRight"===e.key&&(e.preventDefault(),y())},[b,y]);return r.useEffect(()=>{h&&t&&t(h)},[h,t]),r.useEffect(()=>{if(h)return g(h),h.on("reInit",g),h.on("select",g),()=>{h?.off("select",g)}},[h,g]),a.jsx(c.Provider,{value:{carouselRef:u,api:h,opts:s,orientation:e||(s?.axis==="y"?"vertical":"horizontal"),scrollPrev:b,scrollNext:y,canScrollPrev:f,canScrollNext:j},children:a.jsx("div",{ref:x,onKeyDownCapture:v,className:(0,d.cn)("relative",i),role:"region","aria-roledescription":"carousel",...m,children:o})})});x.displayName="Carousel";let u=r.forwardRef(({className:e,...s},t)=>{let{carouselRef:r,orientation:l}=m();return a.jsx("div",{ref:r,className:"overflow-hidden",children:a.jsx("div",{ref:t,className:(0,d.cn)("flex","horizontal"===l?"-ml-4":"-mt-4 flex-col",e),...s})})});u.displayName="CarouselContent";let h=r.forwardRef(({className:e,...s},t)=>{let{orientation:r}=m();return a.jsx("div",{ref:t,role:"group","aria-roledescription":"slide",className:(0,d.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===r?"pl-4":"pt-4",e),...s})});h.displayName="CarouselItem";let f=r.forwardRef(({className:e,variant:s="outline",size:t="icon",...r},l)=>{let{orientation:i,scrollPrev:c,canScrollPrev:x}=m();return a.jsx(o.z,{ref:l,variant:s,size:t,className:(0,d.cn)("absolute  h-8 w-8 rounded-full","horizontal"===i?"-left-12 top-1/2 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",e),onClick:c,disabled:!x,...r,children:a.jsx(n.Z,{className:"h-4 w-4"})})});f.displayName="CarouselPrevious";let p=r.forwardRef(({className:e,variant:s="outline",size:t="icon",...r},l)=>{let{orientation:n,scrollNext:c,canScrollNext:x}=m();return(0,a.jsxs)(o.z,{ref:l,variant:s,size:t,className:(0,d.cn)("absolute h-8 w-8 rounded-full","horizontal"===n?"-right-12 top-1/2 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",e),disabled:!x,onClick:c,...r,children:[a.jsx(i.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Next slide"})]})});p.displayName="CarouselNext"},24118:(e,s,t)=>{t.d(s,{$N:()=>p,Be:()=>j,GG:()=>m,Vq:()=>d,cN:()=>f,cZ:()=>u,fK:()=>h,hg:()=>o});var a=t(10326),r=t(17577),l=t(37956),n=t(94019),i=t(51223);let d=l.fC,o=l.xz,c=l.h_,m=l.x8,x=r.forwardRef(({className:e,...s},t)=>a.jsx(l.aV,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s}));x.displayName=l.aV.displayName;let u=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(c,{children:[a.jsx(x,{}),(0,a.jsxs)(l.VY,{ref:r,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[s,(0,a.jsxs)(l.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground text-red-500",children:[a.jsx(n.Z,{className:"h-4 w-4",strokeWidth:4}),a.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));u.displayName=l.VY.displayName;let h=({className:e,...s})=>a.jsx("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...s});h.displayName="DialogHeader";let f=({className:e,...s})=>a.jsx("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});f.displayName="DialogFooter";let p=r.forwardRef(({className:e,...s},t)=>a.jsx(l.Dx,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...s}));p.displayName=l.Dx.displayName;let j=r.forwardRef(({className:e,...s},t)=>a.jsx(l.dk,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));j.displayName=l.dk.displayName},51682:(e,s,t)=>{t.d(s,{Yi:()=>d,bZ:()=>o,zs:()=>i});var a=t(10326),r=t(17577),l=t(97185),n=t(51223);let i=l.fC,d=l.xz,o=r.forwardRef(({className:e,align:s="center",sideOffset:t=4,...r},i)=>a.jsx(l.VY,{ref:i,align:s,sideOffset:t,className:(0,n.cn)("z-50 w-64 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r}));o.displayName=l.VY.displayName},3236:(e,s,t)=>{t.d(s,{B:()=>d,x:()=>i});var a=t(10326),r=t(17577),l=t(55370),n=t(51223);let i=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(l.fC,{ref:r,className:(0,n.cn)("relative overflow-hidden",e),...t,children:[a.jsx(l.l_,{className:"h-full w-full rounded-[inherit]",children:s}),a.jsx(d,{}),a.jsx(l.Ns,{})]}));i.displayName=l.fC.displayName;let d=r.forwardRef(({className:e,orientation:s="vertical",...t},r)=>a.jsx(l.gb,{ref:r,orientation:s,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===s&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===s&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...t,children:a.jsx(l.q4,{className:"relative flex-1 rounded-full bg-border"})}));d.displayName=l.gb.displayName},29280:(e,s,t)=>{t.d(s,{Bw:()=>p,DI:()=>m,Ph:()=>c,Ql:()=>j,i4:()=>u,ki:()=>x});var a=t(10326),r=t(17577),l=t(75334),n=t(941),i=t(96633),d=t(32933),o=t(51223);let c=l.fC,m=l.ZA,x=l.B4,u=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(l.xz,{ref:r,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,a.jsx(l.JO,{asChild:!0,children:a.jsx(n.Z,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=l.xz.displayName;let h=r.forwardRef(({className:e,...s},t)=>a.jsx(l.u_,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(i.Z,{className:"h-4 w-4"})}));h.displayName=l.u_.displayName;let f=r.forwardRef(({className:e,...s},t)=>a.jsx(l.$G,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(n.Z,{className:"h-4 w-4"})}));f.displayName=l.$G.displayName;let p=r.forwardRef(({className:e,children:s,position:t="popper",...r},n)=>a.jsx(l.h_,{children:(0,a.jsxs)(l.VY,{ref:n,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[a.jsx(h,{}),a.jsx(l.l_,{className:(0,o.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),a.jsx(f,{})]})}));p.displayName=l.VY.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(l.__,{ref:t,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=l.__.displayName;let j=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(l.ck,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(l.wU,{children:a.jsx(d.Z,{className:"h-4 w-4"})})}),a.jsx(l.eT,{children:s})]}));j.displayName=l.ck.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(l.Z0,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=l.Z0.displayName},76066:(e,s,t)=>{t.d(s,{Ei:()=>g,FF:()=>j,Tu:()=>p,aM:()=>c,bC:()=>N,sw:()=>m,ue:()=>f,yo:()=>o});var a=t(10326),r=t(17577),l=t(37956),n=t(28671),i=t(94019),d=t(51223);let o=l.fC,c=l.xz,m=l.x8,x=l.h_,u=r.forwardRef(({className:e,...s},t)=>a.jsx(l.aV,{className:(0,d.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s,ref:t}));u.displayName=l.aV.displayName;let h=(0,n.j)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),f=r.forwardRef(({side:e="right",className:s,children:t,...r},n)=>(0,a.jsxs)(x,{children:[a.jsx(u,{}),(0,a.jsxs)(l.VY,{ref:n,className:(0,d.cn)(h({side:e}),s),...r,children:[t,(0,a.jsxs)(l.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[a.jsx(i.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));f.displayName=l.VY.displayName;let p=({className:e,...s})=>a.jsx("div",{className:(0,d.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...s});p.displayName="SheetHeader";let j=({className:e,...s})=>a.jsx("div",{className:(0,d.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});j.displayName="SheetFooter";let N=r.forwardRef(({className:e,...s},t)=>a.jsx(l.Dx,{ref:t,className:(0,d.cn)("text-lg font-semibold text-foreground",e),...s}));N.displayName=l.Dx.displayName;let g=r.forwardRef(({className:e,...s},t)=>a.jsx(l.dk,{ref:t,className:(0,d.cn)("text-sm text-muted-foreground",e),...s}));g.displayName=l.dk.displayName},41156:(e,s,t)=>{t.d(s,{RM:()=>d,SC:()=>o,iA:()=>n,pj:()=>m,ss:()=>c,xD:()=>i});var a=t(10326),r=t(17577),l=t(51223);let n=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:t,className:(0,l.cn)("w-full caption-bottom text-sm",e),...s})}));n.displayName="Table";let i=r.forwardRef(({className:e,...s},t)=>a.jsx("thead",{ref:t,className:(0,l.cn)("[&_tr]:border-b",e),...s}));i.displayName="TableHeader";let d=r.forwardRef(({className:e,...s},t)=>a.jsx("tbody",{ref:t,className:(0,l.cn)("[&_tr:last-child]:border-0",e),...s}));d.displayName="TableBody",r.forwardRef(({className:e,...s},t)=>a.jsx("tfoot",{ref:t,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let o=r.forwardRef(({className:e,...s},t)=>a.jsx("tr",{ref:t,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));o.displayName="TableRow";let c=r.forwardRef(({className:e,...s},t)=>a.jsx("th",{ref:t,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));c.displayName="TableHead";let m=r.forwardRef(({className:e,...s},t)=>a.jsx("td",{ref:t,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));m.displayName="TableCell",r.forwardRef(({className:e,...s},t)=>a.jsx("caption",{ref:t,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},82015:(e,s,t)=>{t.d(s,{g:()=>n});var a=t(10326),r=t(17577),l=t(51223);let n=r.forwardRef(({className:e,...s},t)=>a.jsx("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s}));n.displayName="Textarea"},84097:(e,s,t)=>{t.d(s,{Am:()=>c}),t(17577);let a=0,r=new Map,l=e=>{if(r.has(e))return;let s=setTimeout(()=>{r.delete(e),o({type:"REMOVE_TOAST",toastId:e})},1e6);r.set(e,s)},n=(e,s)=>{switch(s.type){case"ADD_TOAST":return{...e,toasts:[s.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)};case"DISMISS_TOAST":{let{toastId:t}=s;return t?l(t):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===s.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)}}},i=[],d={toasts:[]};function o(e){d=n(d,e),i.forEach(e=>{e(d)})}function c({...e}){let s=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),t=()=>o({type:"DISMISS_TOAST",toastId:s});return o({type:"ADD_TOAST",toast:{...e,id:s,open:!0,onOpenChange:e=>{e||t()}}}),{id:s,dismiss:t,update:e=>o({type:"UPDATE_TOAST",toast:{...e,id:s}})}}},71064:(e,s,t)=>{t.d(s,{S:()=>a});let a=e=>{switch(e?.toLowerCase()){case"active":return{text:"ACTIVE",className:"bg-blue-500 hover:bg-blue-600"};case"pending":return{text:"PENDING",className:"bg-warning hover:bg-warning"};case"completed":return{text:"COMPLETED",className:"bg-success hover:bg-success"};case"rejected":return{text:"REJECTED",className:"bg-red-500 hover:bg-red-600"};case"ongoing":return{text:"ON-GOING",className:"bg-warning hover:bg-warning"};case"not_started":return{text:"NOT-STARTED",className:"bg-blue-500 hover:bg-blue-600"};default:return{text:"UNKNOWN",className:"bg-gray-500 hover:bg-gray-600"}}}}};