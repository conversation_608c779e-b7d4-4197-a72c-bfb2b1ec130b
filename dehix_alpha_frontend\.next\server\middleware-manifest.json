{"version": 3, "middleware": {"/": {"files": ["prerender-manifest.js", "server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/protected(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/protected/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/business(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/business/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/freelancer(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/freelancer/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/auth\\/login(.json)?[\\/#\\?]?$", "originalSource": "/auth/login"}], "wasm": [], "assets": [], "environments": {"previewModeId": "e9beafd0f914f7afe45f21543dd12621", "previewModeSigningKey": "62a8501ab39896471ab736794803523b55e863e2bf20e1100db905a248c26f7f", "previewModeEncryptionKey": "ca236a4a4a8d48a6c03b7ef4e8fb1b088f88dd5db24f3f4b158094ee478a4295"}}}, "functions": {}, "sortedMiddleware": ["/"]}