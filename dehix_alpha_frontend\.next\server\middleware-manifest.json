{"version": 3, "middleware": {"/": {"files": ["prerender-manifest.js", "server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/protected(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/protected/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/business(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/business/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/freelancer(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/freelancer/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/auth\\/login(.json)?[\\/#\\?]?$", "originalSource": "/auth/login"}], "wasm": [], "assets": [], "environments": {"previewModeId": "03e54995699f00cfef1a9c121b99c1c6", "previewModeSigningKey": "0f31884c87d6445aa04597a97759a0b1da78c8bee9e74b8a7b854419798e4502", "previewModeEncryptionKey": "4de51a2996747089db838718c2d535a0b6ef9897a73d0a2e19fe6d2db92b0dd4"}}}, "functions": {}, "sortedMiddleware": ["/"]}