"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7308],{19155:function(e,t,n){n.d(t,{HT:function(){return Z},iZ:function(){return Q}});var r={},a=Uint8Array,i=Uint16Array,o=Int32Array,s=new a([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),l=new a([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),u=new a([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),c=function(e,t){for(var n=new i(31),r=0;r<31;++r)n[r]=t+=1<<e[r-1];for(var a=new o(n[30]),r=1;r<30;++r)for(var s=n[r];s<n[r+1];++s)a[s]=s-n[r]<<5|r;return{b:n,r:a}},h=c(s,2),d=h.b,g=h.r;d[28]=258,g[258]=28;for(var p=c(l,0),m=p.b,f=p.r,b=new i(32768),v=0;v<32768;++v){var y=(43690&v)>>1|(21845&v)<<1;y=(61680&(y=(52428&y)>>2|(13107&y)<<2))>>4|(3855&y)<<4,b[v]=((65280&y)>>8|(255&y)<<8)>>1}for(var w=function(e,t,n){for(var r,a=e.length,o=0,s=new i(t);o<a;++o)e[o]&&++s[e[o]-1];var l=new i(t);for(o=1;o<t;++o)l[o]=l[o-1]+s[o-1]<<1;if(n){r=new i(1<<t);var u=15-t;for(o=0;o<a;++o)if(e[o])for(var c=o<<4|e[o],h=t-e[o],d=l[e[o]-1]++<<h,g=d|(1<<h)-1;d<=g;++d)r[b[d]>>u]=c}else for(o=0,r=new i(a);o<a;++o)e[o]&&(r[o]=b[l[e[o]-1]++]>>15-e[o]);return r},k=new a(288),v=0;v<144;++v)k[v]=8;for(var v=144;v<256;++v)k[v]=9;for(var v=256;v<280;++v)k[v]=7;for(var v=280;v<288;++v)k[v]=8;for(var P=new a(32),v=0;v<32;++v)P[v]=5;var A=w(k,9,0),x=w(k,9,1),C=w(P,5,0),j=w(P,5,1),N=function(e){for(var t=e[0],n=1;n<e.length;++n)e[n]>t&&(t=e[n]);return t},I=function(e,t,n){var r=t/8|0;return(e[r]|e[r+1]<<8)>>(7&t)&n},D=function(e,t){var n=t/8|0;return(e[n]|e[n+1]<<8|e[n+2]<<16)>>(7&t)},T=function(e){return(e+7)/8|0},O=function(e,t,n){return(null==t||t<0)&&(t=0),(null==n||n>e.length)&&(n=e.length),new a(e.subarray(t,n))},H=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],E=function(e,t,n){var r=Error(t||H[e]);if(r.code=e,Error.captureStackTrace&&Error.captureStackTrace(r,E),!n)throw r;return r},F=function(e,t,n,r){var i=e.length,o=r?r.length:0;if(!i||t.f&&!t.l)return n||new a(0);var c=!n,h=c||2!=t.i,g=t.i;c&&(n=new a(3*i));var p=function(e){var t=n.length;if(e>t){var r=new a(Math.max(2*t,e));r.set(n),n=r}},f=t.f||0,b=t.p||0,v=t.b||0,y=t.l,k=t.d,P=t.m,A=t.n,C=8*i;do{if(!y){f=I(e,b,1);var H=I(e,b+1,3);if(b+=3,H){if(1==H)y=x,k=j,P=9,A=5;else if(2==H){var F=I(e,b,31)+257,G=I(e,b+10,15)+4,V=F+I(e,b+5,31)+1;b+=14;for(var z=new a(V),M=new a(19),B=0;B<G;++B)M[u[B]]=I(e,b+3*B,7);b+=3*G;for(var S=N(M),$=(1<<S)-1,L=w(M,S,1),B=0;B<V;){var K=L[I(e,b,$)];b+=15&K;var J=K>>4;if(J<16)z[B++]=J;else{var W=0,q=0;for(16==J?(q=3+I(e,b,3),b+=2,W=z[B-1]):17==J?(q=3+I(e,b,7),b+=3):18==J&&(q=11+I(e,b,127),b+=7);q--;)z[B++]=W}}var R=z.subarray(0,F),U=z.subarray(F);P=N(R),A=N(U),y=w(R,P,1),k=w(U,A,1)}else E(1)}else{var J=T(b)+4,_=e[J-4]|e[J-3]<<8,Q=J+_;if(Q>i){g&&E(0);break}h&&p(v+_),n.set(e.subarray(J,Q),v),t.b=v+=_,t.p=b=8*Q,t.f=f;continue}if(b>C){g&&E(0);break}}h&&p(v+131072);for(var Z=(1<<P)-1,Y=(1<<A)-1,X=b;;X=b){var W=y[D(e,b)&Z],ee=W>>4;if((b+=15&W)>C){g&&E(0);break}if(W||E(2),ee<256)n[v++]=ee;else if(256==ee){X=b,y=null;break}else{var et=ee-254;if(ee>264){var B=ee-257,en=s[B];et=I(e,b,(1<<en)-1)+d[B],b+=en}var er=k[D(e,b)&Y],ea=er>>4;er||E(3),b+=15&er;var U=m[ea];if(ea>3){var en=l[ea];U+=D(e,b)&(1<<en)-1,b+=en}if(b>C){g&&E(0);break}h&&p(v+131072);var ei=v+et;if(v<U){var eo=o-U,es=Math.min(U,ei);for(eo+v<0&&E(3);v<es;++v)n[v]=r[eo+v]}for(;v<ei;++v)n[v]=n[v-U]}}t.l=y,t.p=X,t.b=v,t.f=f,y&&(f=1,t.m=P,t.d=k,t.n=A)}while(!f);return v!=n.length&&c?O(n,0,v):n.subarray(0,v)},G=function(e,t,n){n<<=7&t;var r=t/8|0;e[r]|=n,e[r+1]|=n>>8},V=function(e,t,n){n<<=7&t;var r=t/8|0;e[r]|=n,e[r+1]|=n>>8,e[r+2]|=n>>16},z=function(e,t){for(var n=[],r=0;r<e.length;++r)e[r]&&n.push({s:r,f:e[r]});var o=n.length,s=n.slice();if(!o)return{t:J,l:0};if(1==o){var l=new a(n[0].s+1);return l[n[0].s]=1,{t:l,l:1}}n.sort(function(e,t){return e.f-t.f}),n.push({s:-1,f:25001});var u=n[0],c=n[1],h=0,d=1,g=2;for(n[0]={s:-1,f:u.f+c.f,l:u,r:c};d!=o-1;)u=n[n[h].f<n[g].f?h++:g++],c=n[h!=d&&n[h].f<n[g].f?h++:g++],n[d++]={s:-1,f:u.f+c.f,l:u,r:c};for(var p=s[0].s,r=1;r<o;++r)s[r].s>p&&(p=s[r].s);var m=new i(p+1),f=M(n[d-1],m,0);if(f>t){var r=0,b=0,v=f-t,y=1<<v;for(s.sort(function(e,t){return m[t.s]-m[e.s]||e.f-t.f});r<o;++r){var w=s[r].s;if(m[w]>t)b+=y-(1<<f-m[w]),m[w]=t;else break}for(b>>=v;b>0;){var k=s[r].s;m[k]<t?b-=1<<t-m[k]++-1:++r}for(;r>=0&&b;--r){var P=s[r].s;m[P]==t&&(--m[P],++b)}f=t}return{t:new a(m),l:f}},M=function(e,t,n){return -1==e.s?Math.max(M(e.l,t,n+1),M(e.r,t,n+1)):t[e.s]=n},B=function(e){for(var t=e.length;t&&!e[--t];);for(var n=new i(++t),r=0,a=e[0],o=1,s=function(e){n[r++]=e},l=1;l<=t;++l)if(e[l]==a&&l!=t)++o;else{if(!a&&o>2){for(;o>138;o-=138)s(32754);o>2&&(s(o>10?o-11<<5|28690:o-3<<5|12305),o=0)}else if(o>3){for(s(a),--o;o>6;o-=6)s(8304);o>2&&(s(o-3<<5|8208),o=0)}for(;o--;)s(a);o=1,a=e[l]}return{c:n.subarray(0,r),n:t}},S=function(e,t){for(var n=0,r=0;r<t.length;++r)n+=e[r]*t[r];return n},$=function(e,t,n){var r=n.length,a=T(t+2);e[a]=255&r,e[a+1]=r>>8,e[a+2]=255^e[a],e[a+3]=255^e[a+1];for(var i=0;i<r;++i)e[a+i+4]=n[i];return(a+4+r)*8},L=function(e,t,n,r,a,o,c,h,d,g,p){G(t,p++,n),++a[256];for(var m,f,b,v,y=z(a,15),x=y.t,j=y.l,N=z(o,15),I=N.t,D=N.l,T=B(x),O=T.c,H=T.n,E=B(I),F=E.c,M=E.n,L=new i(19),K=0;K<O.length;++K)++L[31&O[K]];for(var K=0;K<F.length;++K)++L[31&F[K]];for(var J=z(L,7),W=J.t,q=J.l,R=19;R>4&&!W[u[R-1]];--R);var U=g+5<<3,_=S(a,k)+S(o,P)+c,Q=S(a,x)+S(o,I)+c+14+3*R+S(L,W)+2*L[16]+3*L[17]+7*L[18];if(d>=0&&U<=_&&U<=Q)return $(t,p,e.subarray(d,d+g));if(G(t,p,1+(Q<_)),p+=2,Q<_){m=w(x,j,0),f=x,b=w(I,D,0),v=I;var Z=w(W,q,0);G(t,p,H-257),G(t,p+5,M-1),G(t,p+10,R-4),p+=14;for(var K=0;K<R;++K)G(t,p+3*K,W[u[K]]);p+=3*R;for(var Y=[O,F],X=0;X<2;++X)for(var ee=Y[X],K=0;K<ee.length;++K){var et=31&ee[K];G(t,p,Z[et]),p+=W[et],et>15&&(G(t,p,ee[K]>>5&127),p+=ee[K]>>12)}}else m=A,f=k,b=C,v=P;for(var K=0;K<h;++K){var en=r[K];if(en>255){var et=en>>18&31;V(t,p,m[et+257]),p+=f[et+257],et>7&&(G(t,p,en>>23&31),p+=s[et]);var er=31&en;V(t,p,b[er]),p+=v[er],er>3&&(V(t,p,en>>5&8191),p+=l[er])}else V(t,p,m[en]),p+=f[en]}return V(t,p,m[256]),p+f[256]},K=new o([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),J=new a(0),W=function(e,t,n,r,u,c){var h=c.z||e.length,d=new a(r+h+5*(1+Math.ceil(h/7e3))+u),p=d.subarray(r,d.length-u),m=c.l,b=7&(c.r||0);if(t){b&&(p[0]=c.r>>3);for(var v=K[t-1],y=v>>13,w=8191&v,k=(1<<n)-1,P=c.p||new i(32768),A=c.h||new i(k+1),x=Math.ceil(n/3),C=2*x,j=function(t){return(e[t]^e[t+1]<<x^e[t+2]<<C)&k},N=new o(25e3),I=new i(288),D=new i(32),H=0,E=0,F=c.i||0,G=0,V=c.w||0,z=0;F+2<h;++F){var M=j(F),B=32767&F,S=A[M];if(P[B]=S,A[M]=B,V<=F){var J=h-F;if((H>7e3||G>24576)&&(J>423||!m)){b=L(e,p,0,N,I,D,E,G,z,F-z,b),G=H=E=0,z=F;for(var W=0;W<286;++W)I[W]=0;for(var W=0;W<30;++W)D[W]=0}var q=2,R=0,U=w,_=B-S&32767;if(J>2&&M==j(F-_))for(var Q=Math.min(y,J)-1,Z=Math.min(32767,F),Y=Math.min(258,J);_<=Z&&--U&&B!=S;){if(e[F+q]==e[F+q-_]){for(var X=0;X<Y&&e[F+X]==e[F+X-_];++X);if(X>q){if(q=X,R=_,X>Q)break;for(var ee=Math.min(_,X-2),et=0,W=0;W<ee;++W){var en=F-_+W&32767,er=P[en],ea=en-er&32767;ea>et&&(et=ea,S=en)}}}S=P[B=S],_+=B-S&32767}if(R){N[G++]=268435456|g[q]<<18|f[R];var ei=31&g[q],eo=31&f[R];E+=s[ei]+l[eo],++I[257+ei],++D[eo],V=F+q,++H}else N[G++]=e[F],++I[e[F]]}}for(F=Math.max(F,V);F<h;++F)N[G++]=e[F],++I[e[F]];b=L(e,p,m,N,I,D,E,G,z,F-z,b),m||(c.r=7&b|p[b/8|0]<<3,b-=7,c.h=A,c.p=P,c.i=F,c.w=V)}else{for(var F=c.w||0;F<h+m;F+=65535){var es=F+65535;es>=h&&(p[b/8|0]=m,es=h),b=$(p,b+1,e.subarray(F,es))}c.i=h}return O(d,0,r+T(b)+u)},q=function(){var e=1,t=0;return{p:function(n){for(var r=e,a=t,i=0|n.length,o=0;o!=i;){for(var s=Math.min(o+2655,i);o<s;++o)a+=r+=n[o];r=(65535&r)+15*(r>>16),a=(65535&a)+15*(a>>16)}e=r,t=a},d:function(){return e%=65521,t%=65521,(255&e)<<24|(65280&e)<<8|(255&t)<<8|t>>8}}},R=function(e,t,n,r,i){if(!i&&(i={l:1},t.dictionary)){var o=t.dictionary.subarray(-32768),s=new a(o.length+e.length);s.set(o),s.set(e,o.length),e=s,i.w=o.length}return W(e,null==t.level?6:t.level,null==t.mem?i.l?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(e.length)))):20:12+t.mem,n,r,i)},U=function(e,t,n){for(;n;++t)e[t]=n,n>>>=8},_=function(e,t){var n=t.level;if(e[0]=120,e[1]=(0==n?0:n<6?1:9==n?3:2)<<6|(t.dictionary&&32),e[1]|=31-(e[0]<<8|e[1])%31,t.dictionary){var r=q();r.p(t.dictionary),U(e,2,r.d())}};function Q(e,t){t||(t={});var n=q();n.p(e);var r=R(e,t,t.dictionary?6:2,4);return _(r,t),U(r,r.length-4,n.d()),r}function Z(e,t){var n;return F(e.subarray((n=t&&t.dictionary,((15&e[0])!=8||e[0]>>4>7||(e[0]<<8|e[1])%31)&&E(6,"invalid zlib data"),(e[1]>>5&1)==+!n&&E(6,"invalid zlib data: "+(32&e[1]?"need":"unexpected")+" dictionary"),(e[1]>>3&4)+2),-4),{i:2},t&&t.out,t&&t.dictionary)}var Y="undefined"!=typeof TextDecoder&&new TextDecoder;try{Y.decode(J,{stream:!0})}catch(e){}"function"==typeof queueMicrotask?queueMicrotask:"function"==typeof setTimeout&&setTimeout},6540:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},25912:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},70518:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},96273:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},40036:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("ImagePlus",[["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7",key:"31hg93"}],["line",{x1:"16",x2:"22",y1:"5",y2:"5",key:"ez7e4s"}],["line",{x1:"19",x2:"19",y1:"2",y2:"8",key:"1gkr8c"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},67524:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},8620:function(e,t,n){n.d(t,{Z:function(){return r}});function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}},38364:function(e,t,n){n.d(t,{f:function(){return s}});var r=n(2265),a=n(18676),i=n(57437),o=r.forwardRef((e,t)=>(0,i.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var s=o},48484:function(e,t,n){n.d(t,{f:function(){return u}});var r=n(2265),a=n(18676),i=n(57437),o="horizontal",s=["horizontal","vertical"],l=r.forwardRef((e,t)=>{let{decorative:n,orientation:r=o,...l}=e,u=s.includes(r)?r:o;return(0,i.jsx)(a.WV.div,{"data-orientation":u,...n?{role:"none"}:{"aria-orientation":"vertical"===u?u:void 0,role:"separator"},...l,ref:t})});l.displayName="Separator";var u=l},41957:function(e,t,n){let r,a;/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function i(e){return e+.5|0}n.d(t,{$:function(){return td},A:function(){return eG},B:function(){return eF},C:function(){return tc},D:function(){return eC},E:function(){return tA},F:function(){return W},G:function(){return tX},H:function(){return eh},I:function(){return tW},J:function(){return t2},K:function(){return t1},L:function(){return eL},M:function(){return tJ},N:function(){return ev},O:function(){return S},P:function(){return eo},Q:function(){return J},R:function(){return tj},S:function(){return eT},T:function(){return es},U:function(){return eA},V:function(){return ta},W:function(){return eO},X:function(){return to},Y:function(){return th},Z:function(){return tm},_:function(){return eS},a:function(){return tC},a0:function(){return tx},a1:function(){return eJ},a2:function(){return eW},a3:function(){return e7},a4:function(){return Q},a5:function(){return et},a6:function(){return e8},a7:function(){return er},a8:function(){return function e(t,n,r,a){return new Proxy({_cacheable:!1,_proxy:t,_context:n,_subProxy:r,_stack:new Set,_descriptors:tD(t,a),setContext:n=>e(t,n,r,a),override:i=>e(t.override(i),n,r,a)},{deleteProperty:(e,n)=>(delete e[n],delete t[n],!0),get:(t,n,r)=>tH(t,n,()=>(function(t,n,r){let{_proxy:a,_context:i,_subProxy:o,_descriptors:s}=t,l=a[n];return er(l)&&s.isScriptable(n)&&(l=function(e,t,n,r){let{_proxy:a,_context:i,_subProxy:o,_stack:s}=n;if(s.has(e))throw Error("Recursion detected: "+Array.from(s).join("->")+"->"+e);s.add(e);let l=t(i,o||r);return s.delete(e),tO(e,l)&&(l=tF(a._scopes,a,e,l)),l}(n,l,t,r)),z(l)&&l.length&&(l=function(t,n,r,a){let{_proxy:i,_context:o,_subProxy:s,_descriptors:l}=r;if(void 0!==o.index&&a(t))return n[o.index%n.length];if(M(n[0])){let r=n,a=i._scopes.filter(e=>e!==r);for(let u of(n=[],r)){let r=tF(a,i,t,u);n.push(e(r,o,s&&s[t],l))}}return n}(n,l,t,s.isIndexable)),tO(n,l)&&(l=e(l,i,o&&o[n],s)),l})(t,n,r)),getOwnPropertyDescriptor:(e,n)=>e._descriptors.allKeys?Reflect.has(t,n)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,n),getPrototypeOf:()=>Reflect.getPrototypeOf(t),has:(e,n)=>Reflect.has(t,n),ownKeys:()=>Reflect.ownKeys(t),set:(e,n,r)=>(t[n]=r,delete e[n],!0)})}},a9:function(){return tI},aA:function(){return t9},aB:function(){return eq},aC:function(){return t7},aD:function(){return tu},aE:function(){return ej},aF:function(){return F},aG:function(){return ek},aH:function(){return eb},aI:function(){return ew},aJ:function(){return ef},aK:function(){return ex},aL:function(){return e9},aM:function(){return ep},aN:function(){return ti},aO:function(){return eV},aP:function(){return eE},aa:function(){return tD},ab:function(){return Z},ac:function(){return G},ad:function(){return eK},ae:function(){return t0},af:function(){return ts},ag:function(){return ea},ah:function(){return q},ai:function(){return ei},aj:function(){return eH},ak:function(){return tw},al:function(){return tK},am:function(){return nr},an:function(){return nn},ao:function(){return t5},ap:function(){return t4},aq:function(){return t3},ar:function(){return tg},as:function(){return tp},at:function(){return tl},au:function(){return tf},av:function(){return tk},aw:function(){return tP},ax:function(){return nt},ay:function(){return eI},az:function(){return t6},b:function(){return z},c:function(){return e0},d:function(){return tr},e:function(){return eY},f:function(){return ee},g:function(){return B},h:function(){return en},i:function(){return M},j:function(){return tN},k:function(){return V},l:function(){return eM},m:function(){return L},n:function(){return K},o:function(){return e4},p:function(){return eD},q:function(){return eR},r:function(){return e$},s:function(){return em},t:function(){return eP},u:function(){return eB},v:function(){return $},w:function(){return eU},x:function(){return ey},y:function(){return tM},z:function(){return tZ}});let o=(e,t,n)=>Math.max(Math.min(e,n),t);function s(e){return o(i(2.55*e),0,255)}function l(e){return o(i(255*e),0,255)}function u(e){return o(i(e/2.55)/100,0,1)}function c(e){return o(i(100*e),0,100)}let h={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},d=[..."0123456789ABCDEF"],g=e=>d[15&e],p=e=>d[(240&e)>>4]+d[15&e],m=e=>(240&e)>>4==(15&e),f=e=>m(e.r)&&m(e.g)&&m(e.b)&&m(e.a),b=(e,t)=>e<255?t(e):"",v=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function y(e,t,n){let r=t*Math.min(n,1-n),a=(t,a=(t+e/30)%12)=>n-r*Math.max(Math.min(a-3,9-a,1),-1);return[a(0),a(8),a(4)]}function w(e,t,n){let r=(r,a=(r+e/60)%6)=>n-n*t*Math.max(Math.min(a,4-a,1),0);return[r(5),r(3),r(1)]}function k(e,t,n){let r;let a=y(e,1,.5);for(t+n>1&&(r=1/(t+n),t*=r,n*=r),r=0;r<3;r++)a[r]*=1-t-n,a[r]+=t;return a}function P(e){let t,n,r;let a=e.r/255,i=e.g/255,o=e.b/255,s=Math.max(a,i,o),l=Math.min(a,i,o),u=(s+l)/2;return s!==l&&(r=s-l,n=u>.5?r/(2-s-l):r/(s+l),t=60*(t=a===s?(i-o)/r+(i<o?6:0):i===s?(o-a)/r+2:(a-i)/r+4)+.5),[0|t,n||0,u]}function A(e,t,n,r){return(Array.isArray(t)?e(t[0],t[1],t[2]):e(t,n,r)).map(l)}function x(e){return(e%360+360)%360}let C={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},j={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"},N=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,I=e=>e<=.0031308?12.92*e:1.055*Math.pow(e,1/2.4)-.055,D=e=>e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4);function T(e,t,n){if(e){let r=P(e);r[t]=Math.max(0,Math.min(r[t]+r[t]*n,0===t?360:1)),r=A(y,r,void 0,void 0),e.r=r[0],e.g=r[1],e.b=r[2]}}function O(e,t){return e?Object.assign(t||{},e):e}function H(e){var t={r:0,g:0,b:0,a:255};return Array.isArray(e)?e.length>=3&&(t={r:e[0],g:e[1],b:e[2],a:255},e.length>3&&(t.a=l(e[3]))):(t=O(e,{r:0,g:0,b:0,a:1})).a=l(t.a),t}class E{constructor(e){let t;if(e instanceof E)return e;let n=typeof e;if("object"===n)t=H(e);else if("string"===n){var a,i;i=e.length,"#"===e[0]&&(4===i||5===i?a={r:255&17*h[e[1]],g:255&17*h[e[2]],b:255&17*h[e[3]],a:5===i?17*h[e[4]]:255}:(7===i||9===i)&&(a={r:h[e[1]]<<4|h[e[2]],g:h[e[3]]<<4|h[e[4]],b:h[e[5]]<<4|h[e[6]],a:9===i?h[e[7]]<<4|h[e[8]]:255})),t=a||function(e){r||((r=function(){let e,t,n,r,a;let i={},o=Object.keys(j),s=Object.keys(C);for(e=0;e<o.length;e++){for(t=0,r=a=o[e];t<s.length;t++)n=s[t],a=a.replace(n,C[n]);n=parseInt(j[r],16),i[a]=[n>>16&255,n>>8&255,255&n]}return i}()).transparent=[0,0,0,0]);let t=r[e.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:4===t.length?t[3]:255}}(e)||("r"===e.charAt(0)?function(e){let t,n,r;let a=N.exec(e),i=255;if(a){if(a[7]!==t){let e=+a[7];i=a[8]?s(e):o(255*e,0,255)}return t=+a[1],n=+a[3],r=+a[5],{r:t=255&(a[2]?s(t):o(t,0,255)),g:n=255&(a[4]?s(n):o(n,0,255)),b:r=255&(a[6]?s(r):o(r,0,255)),a:i}}}(e):function(e){let t;let n=v.exec(e),r=255;if(!n)return;n[5]!==t&&(r=n[6]?s(+n[5]):l(+n[5]));let a=x(+n[2]),i=+n[3]/100,o=+n[4]/100;return{r:(t="hwb"===n[1]?A(k,a,i,o):"hsv"===n[1]?A(w,a,i,o):A(y,a,i,o))[0],g:t[1],b:t[2],a:r}}(e))}this._rgb=t,this._valid=!!t}get valid(){return this._valid}get rgb(){var e=O(this._rgb);return e&&(e.a=u(e.a)),e}set rgb(e){this._rgb=H(e)}rgbString(){var e;return this._valid?(e=this._rgb)&&(e.a<255?`rgba(${e.r}, ${e.g}, ${e.b}, ${u(e.a)})`:`rgb(${e.r}, ${e.g}, ${e.b})`):void 0}hexString(){var e,t;return this._valid?(t=f(e=this._rgb)?g:p,e?"#"+t(e.r)+t(e.g)+t(e.b)+b(e.a,t):void 0):void 0}hslString(){return this._valid?function(e){if(!e)return;let t=P(e),n=t[0],r=c(t[1]),a=c(t[2]);return e.a<255?`hsla(${n}, ${r}%, ${a}%, ${u(e.a)})`:`hsl(${n}, ${r}%, ${a}%)`}(this._rgb):void 0}mix(e,t){if(e){let n;let r=this.rgb,a=e.rgb,i=t===n?.5:t,o=2*i-1,s=r.a-a.a,l=((o*s==-1?o:(o+s)/(1+o*s))+1)/2;n=1-l,r.r=255&l*r.r+n*a.r+.5,r.g=255&l*r.g+n*a.g+.5,r.b=255&l*r.b+n*a.b+.5,r.a=i*r.a+(1-i)*a.a,this.rgb=r}return this}interpolate(e,t){return e&&(this._rgb=function(e,t,n){let r=D(u(e.r)),a=D(u(e.g)),i=D(u(e.b));return{r:l(I(r+n*(D(u(t.r))-r))),g:l(I(a+n*(D(u(t.g))-a))),b:l(I(i+n*(D(u(t.b))-i))),a:e.a+n*(t.a-e.a)}}(this._rgb,e._rgb,t)),this}clone(){return new E(this.rgb)}alpha(e){return this._rgb.a=l(e),this}clearer(e){let t=this._rgb;return t.a*=1-e,this}greyscale(){let e=this._rgb,t=i(.3*e.r+.59*e.g+.11*e.b);return e.r=e.g=e.b=t,this}opaquer(e){let t=this._rgb;return t.a*=1+e,this}negate(){let e=this._rgb;return e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,this}lighten(e){return T(this._rgb,2,e),this}darken(e){return T(this._rgb,2,-e),this}saturate(e){return T(this._rgb,1,e),this}desaturate(e){return T(this._rgb,1,-e),this}rotate(e){var t,n;return(n=P(t=this._rgb))[0]=x(n[0]+e),n=A(y,n,void 0,void 0),t.r=n[0],t.g=n[1],t.b=n[2],this}}/*!
 * Chart.js v4.4.7
 * https://www.chartjs.org
 * (c) 2024 Chart.js Contributors
 * Released under the MIT License
 */function F(){}let G=(a=0,()=>a++);function V(e){return null==e}function z(e){if(Array.isArray&&Array.isArray(e))return!0;let t=Object.prototype.toString.call(e);return"[object"===t.slice(0,7)&&"Array]"===t.slice(-6)}function M(e){return null!==e&&"[object Object]"===Object.prototype.toString.call(e)}function B(e){return("number"==typeof e||e instanceof Number)&&isFinite(+e)}function S(e,t){return B(e)?e:t}function $(e,t){return void 0===e?t:e}let L=(e,t)=>"string"==typeof e&&e.endsWith("%")?parseFloat(e)/100:+e/t,K=(e,t)=>"string"==typeof e&&e.endsWith("%")?parseFloat(e)/100*t:+e;function J(e,t,n){if(e&&"function"==typeof e.call)return e.apply(n,t)}function W(e,t,n,r){let a,i,o;if(z(e)){if(i=e.length,r)for(a=i-1;a>=0;a--)t.call(n,e[a],a);else for(a=0;a<i;a++)t.call(n,e[a],a)}else if(M(e))for(a=0,i=(o=Object.keys(e)).length;a<i;a++)t.call(n,e[o[a]],o[a])}function q(e,t){let n,r,a,i;if(!e||!t||e.length!==t.length)return!1;for(n=0,r=e.length;n<r;++n)if(a=e[n],i=t[n],a.datasetIndex!==i.datasetIndex||a.index!==i.index)return!1;return!0}function R(e){if(z(e))return e.map(R);if(M(e)){let t=Object.create(null),n=Object.keys(e),r=n.length,a=0;for(;a<r;++a)t[n[a]]=R(e[n[a]]);return t}return e}function U(e){return -1===["__proto__","prototype","constructor"].indexOf(e)}function _(e,t,n,r){if(!U(e))return;let a=t[e],i=n[e];M(a)&&M(i)?Q(a,i,r):t[e]=R(i)}function Q(e,t,n){let r;let a=z(t)?t:[t],i=a.length;if(!M(e))return e;let o=(n=n||{}).merger||_;for(let t=0;t<i;++t){if(!M(r=a[t]))continue;let i=Object.keys(r);for(let t=0,a=i.length;t<a;++t)o(i[t],e,r,n)}return e}function Z(e,t){return Q(e,t,{merger:Y})}function Y(e,t,n){if(!U(e))return;let r=t[e],a=n[e];M(r)&&M(a)?Z(r,a):Object.prototype.hasOwnProperty.call(t,e)||(t[e]=R(a))}let X={"":e=>e,x:e=>e.x,y:e=>e.y};function ee(e,t){return(X[t]||(X[t]=function(e){let t=function(e){let t=e.split("."),n=[],r="";for(let e of t)(r+=e).endsWith("\\")?r=r.slice(0,-1)+".":(n.push(r),r="");return n}(e);return e=>{for(let n of t){if(""===n)break;e=e&&e[n]}return e}}(t)))(e)}function et(e){return e.charAt(0).toUpperCase()+e.slice(1)}let en=e=>void 0!==e,er=e=>"function"==typeof e,ea=(e,t)=>{if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0};function ei(e){return"mouseup"===e.type||"click"===e.type||"contextmenu"===e.type}let eo=Math.PI,es=2*eo,el=es+eo,eu=Number.POSITIVE_INFINITY,ec=eo/180,eh=eo/2,ed=eo/4,eg=2*eo/3,ep=Math.log10,em=Math.sign;function ef(e,t,n){return Math.abs(e-t)<n}function eb(e){let t=Math.round(e),n=Math.pow(10,Math.floor(ep(e=ef(e,t,e/1e3)?t:e))),r=e/n;return(r<=1?1:r<=2?2:r<=5?5:10)*n}function ev(e){let t;let n=[],r=Math.sqrt(e);for(t=1;t<r;t++)e%t==0&&(n.push(t),n.push(e/t));return r===(0|r)&&n.push(r),n.sort((e,t)=>e-t).pop(),n}function ey(e){return!isNaN(parseFloat(e))&&isFinite(e)}function ew(e,t){let n=Math.round(e);return n-t<=e&&n+t>=e}function ek(e,t,n){let r,a,i;for(r=0,a=e.length;r<a;r++)isNaN(i=e[r][n])||(t.min=Math.min(t.min,i),t.max=Math.max(t.max,i))}function eP(e){return eo/180*e}function eA(e){return 180/eo*e}function ex(e){if(!B(e))return;let t=1,n=0;for(;Math.round(e*t)/t!==e;)t*=10,n++;return n}function eC(e,t){let n=t.x-e.x,r=t.y-e.y,a=Math.atan2(r,n);return a<-.5*eo&&(a+=es),{angle:a,distance:Math.sqrt(n*n+r*r)}}function ej(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}function eN(e,t){return(e-t+el)%es-eo}function eI(e){return(e%es+es)%es}function eD(e,t,n,r){let a=eI(e),i=eI(t),o=eI(n),s=eI(i-a),l=eI(o-a),u=eI(a-i),c=eI(a-o);return a===i||a===o||r&&i===o||s>l&&u<c}function eT(e,t,n){return Math.max(t,Math.min(n,e))}function eO(e){return eT(e,-32768,32767)}function eH(e,t,n,r=1e-6){return e>=Math.min(t,n)-r&&e<=Math.max(t,n)+r}function eE(e,t,n){let r;n=n||(n=>e[n]<t);let a=e.length-1,i=0;for(;a-i>1;)n(r=i+a>>1)?i=r:a=r;return{lo:i,hi:a}}let eF=(e,t,n,r)=>eE(e,n,r?r=>{let a=e[r][t];return a<n||a===n&&e[r+1][t]===n}:r=>e[r][t]<n),eG=(e,t,n)=>eE(e,n,r=>e[r][t]>=n);function eV(e,t,n){let r=0,a=e.length;for(;r<a&&e[r]<t;)r++;for(;a>r&&e[a-1]>n;)a--;return r>0||a<e.length?e.slice(r,a):e}let ez=["push","pop","shift","splice","unshift"];function eM(e,t){if(e._chartjs){e._chartjs.listeners.push(t);return}Object.defineProperty(e,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),ez.forEach(t=>{let n="_onData"+et(t),r=e[t];Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value(...t){let a=r.apply(this,t);return e._chartjs.listeners.forEach(e=>{"function"==typeof e[n]&&e[n](...t)}),a}})})}function eB(e,t){let n=e._chartjs;if(!n)return;let r=n.listeners,a=r.indexOf(t);-1!==a&&r.splice(a,1),r.length>0||(ez.forEach(t=>{delete e[t]}),delete e._chartjs)}function eS(e){let t=new Set(e);return t.size===e.length?e:Array.from(t)}let e$="undefined"==typeof window?function(e){return e()}:window.requestAnimationFrame;function eL(e,t){let n=[],r=!1;return function(...a){n=a,r||(r=!0,e$.call(window,()=>{r=!1,e.apply(t,n)}))}}function eK(e,t){let n;return function(...r){return t?(clearTimeout(n),n=setTimeout(e,t,r)):e.apply(this,r),t}}let eJ=e=>"start"===e?"left":"end"===e?"right":"center",eW=(e,t,n)=>"start"===e?t:"end"===e?n:(t+n)/2,eq=(e,t,n,r)=>e===(r?"left":"right")?n:"center"===e?(t+n)/2:t;function eR(e,t,n){let r=t.length,a=0,i=r;if(e._sorted){let{iScale:o,_parsed:s}=e,l=o.axis,{min:u,max:c,minDefined:h,maxDefined:d}=o.getUserBounds();h&&(a=eT(Math.min(eF(s,l,u).lo,n?r:eF(t,l,o.getPixelForValue(u)).lo),0,r-1)),i=d?eT(Math.max(eF(s,o.axis,c,!0).hi+1,n?0:eF(t,l,o.getPixelForValue(c),!0).hi+1),a,r)-a:r-a}return{start:a,count:i}}function eU(e){let{xScale:t,yScale:n,_scaleRanges:r}=e,a={xmin:t.min,xmax:t.max,ymin:n.min,ymax:n.max};if(!r)return e._scaleRanges=a,!0;let i=r.xmin!==t.min||r.xmax!==t.max||r.ymin!==n.min||r.ymax!==n.max;return Object.assign(r,a),i}let e_=e=>0===e||1===e,eQ=(e,t,n)=>-(Math.pow(2,10*(e-=1))*Math.sin((e-t)*es/n)),eZ=(e,t,n)=>Math.pow(2,-10*e)*Math.sin((e-t)*es/n)+1,eY={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>-e*(e-2),easeInOutQuad:e=>(e/=.5)<1?.5*e*e:-.5*(--e*(e-2)-1),easeInCubic:e=>e*e*e,easeOutCubic:e=>(e-=1)*e*e+1,easeInOutCubic:e=>(e/=.5)<1?.5*e*e*e:.5*((e-=2)*e*e+2),easeInQuart:e=>e*e*e*e,easeOutQuart:e=>-((e-=1)*e*e*e-1),easeInOutQuart:e=>(e/=.5)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2),easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>(e-=1)*e*e*e*e+1,easeInOutQuint:e=>(e/=.5)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2),easeInSine:e=>-Math.cos(e*eh)+1,easeOutSine:e=>Math.sin(e*eh),easeInOutSine:e=>-.5*(Math.cos(eo*e)-1),easeInExpo:e=>0===e?0:Math.pow(2,10*(e-1)),easeOutExpo:e=>1===e?1:-Math.pow(2,-10*e)+1,easeInOutExpo:e=>e_(e)?e:e<.5?.5*Math.pow(2,10*(2*e-1)):.5*(-Math.pow(2,-10*(2*e-1))+2),easeInCirc:e=>e>=1?e:-(Math.sqrt(1-e*e)-1),easeOutCirc:e=>Math.sqrt(1-(e-=1)*e),easeInOutCirc:e=>(e/=.5)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1),easeInElastic:e=>e_(e)?e:eQ(e,.075,.3),easeOutElastic:e=>e_(e)?e:eZ(e,.075,.3),easeInOutElastic:e=>e_(e)?e:e<.5?.5*eQ(2*e,.1125,.45):.5+.5*eZ(2*e-1,.1125,.45),easeInBack:e=>e*e*(2.70158*e-1.70158),easeOutBack:e=>(e-=1)*e*(2.70158*e********)+1,easeInOutBack(e){let t=1.70158;return(e/=.5)<1?e*e*(((t*=1.525)+1)*e-t)*.5:.5*((e-=2)*e*(((t*=1.525)+1)*e+t)+2)},easeInBounce:e=>1-eY.easeOutBounce(1-e),easeOutBounce:e=>e<.36363636363636365?7.5625*e*e:e<.7272727272727273?7.5625*(e-=.5454545454545454)*e+.75:e<.9090909090909091?7.5625*(e-=.8181818181818182)*e+.9375:7.5625*(e-=.9545454545454546)*e+.984375,easeInOutBounce:e=>e<.5?.5*eY.easeInBounce(2*e):.5*eY.easeOutBounce(2*e-1)+.5};function eX(e){if(e&&"object"==typeof e){let t=e.toString();return"[object CanvasPattern]"===t||"[object CanvasGradient]"===t}return!1}function e0(e){return eX(e)?e:new E(e)}function e1(e){return eX(e)?e:new E(e).saturate(.5).darken(.1).hexString()}let e2=["x","y","borderWidth","radius","tension"],e3=["color","borderColor","backgroundColor"],e5=new Map;function e4(e,t,n){return(function(e,t){let n=e+JSON.stringify(t=t||{}),r=e5.get(n);return r||(r=new Intl.NumberFormat(e,t),e5.set(n,r)),r})(t,n).format(e)}let e6={values:e=>z(e)?e:""+e,numeric(e,t,n){let r;if(0===e)return"0";let a=this.chart.options.locale,i=e;if(n.length>1){let t;let a=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(a<1e-4||a>1e15)&&(r="scientific"),Math.abs(t=n.length>3?n[2].value-n[1].value:n[1].value-n[0].value)>=1&&e!==Math.floor(e)&&(t=e-Math.floor(e)),i=t}let o=ep(Math.abs(i)),s=isNaN(o)?1:Math.max(Math.min(-1*Math.floor(o),20),0),l={notation:r,minimumFractionDigits:s,maximumFractionDigits:s};return Object.assign(l,this.options.ticks.format),e4(e,a,l)},logarithmic(e,t,n){return 0===e?"0":[1,2,3,5,10,15].includes(n[t].significand||e/Math.pow(10,Math.floor(ep(e))))||t>.8*n.length?e6.numeric.call(this,e,t,n):""}};var e9={formatters:e6};let e7=Object.create(null),e8=Object.create(null);function te(e,t){if(!t)return e;let n=t.split(".");for(let t=0,r=n.length;t<r;++t){let r=n[t];e=e[r]||(e[r]=Object.create(null))}return e}function tt(e,t,n){return"string"==typeof t?Q(te(e,t),n):Q(te(e,""),t)}class tn{constructor(e,t){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=e=>e.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(e,t)=>e1(t.backgroundColor),this.hoverBorderColor=(e,t)=>e1(t.borderColor),this.hoverColor=(e,t)=>e1(t.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(e),this.apply(t)}set(e,t){return tt(this,e,t)}get(e){return te(this,e)}describe(e,t){return tt(e8,e,t)}override(e,t){return tt(e7,e,t)}route(e,t,n,r){let a=te(this,e),i=te(this,n),o="_"+t;Object.defineProperties(a,{[o]:{value:a[t],writable:!0},[t]:{enumerable:!0,get(){let e=this[o],t=i[r];return M(e)?Object.assign({},t,e):$(e,t)},set(e){this[o]=e}}})}apply(e){e.forEach(e=>e(this))}}var tr=new tn({_scriptable:e=>!e.startsWith("on"),_indexable:e=>"events"!==e,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(e){e.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),e.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:e=>"onProgress"!==e&&"onComplete"!==e&&"fn"!==e}),e.set("animations",{colors:{type:"color",properties:e3},numbers:{type:"number",properties:e2}}),e.describe("animations",{_fallback:"animation"}),e.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:e=>0|e}}}})},function(e){e.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(e){e.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(e,t)=>t.lineWidth,tickColor:(e,t)=>t.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:e9.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),e.route("scale.ticks","color","","color"),e.route("scale.grid","color","","borderColor"),e.route("scale.border","color","","borderColor"),e.route("scale.title","color","","color"),e.describe("scale",{_fallback:!1,_scriptable:e=>!e.startsWith("before")&&!e.startsWith("after")&&"callback"!==e&&"parser"!==e,_indexable:e=>"borderDash"!==e&&"tickBorderDash"!==e&&"dash"!==e}),e.describe("scales",{_fallback:"scale"}),e.describe("scale.ticks",{_scriptable:e=>"backdropPadding"!==e&&"callback"!==e,_indexable:e=>"backdropPadding"!==e})}]);function ta(e,t,n,r,a){let i=t[a];return i||(i=t[a]=e.measureText(a).width,n.push(a)),i>r&&(r=i),r}function ti(e,t,n,r){let a,i,o,s,l;let u=(r=r||{}).data=r.data||{},c=r.garbageCollect=r.garbageCollect||[];r.font!==t&&(u=r.data={},c=r.garbageCollect=[],r.font=t),e.save(),e.font=t;let h=0,d=n.length;for(a=0;a<d;a++)if(null==(s=n[a])||z(s)){if(z(s))for(i=0,o=s.length;i<o;i++)null==(l=s[i])||z(l)||(h=ta(e,u,c,h,l))}else h=ta(e,u,c,h,s);e.restore();let g=c.length/2;if(g>n.length){for(a=0;a<g;a++)delete u[c[a]];c.splice(0,g)}return h}function to(e,t,n){let r=e.currentDevicePixelRatio,a=0!==n?Math.max(n/2,.5):0;return Math.round((t-a)*r)/r+a}function ts(e,t){(t||e)&&((t=t||e.getContext("2d")).save(),t.resetTransform(),t.clearRect(0,0,e.width,e.height),t.restore())}function tl(e,t,n,r){tu(e,t,n,r,null)}function tu(e,t,n,r,a){let i,o,s,l,u,c,h,d;let g=t.pointStyle,p=t.rotation,m=t.radius,f=(p||0)*ec;if(g&&"object"==typeof g&&("[object HTMLImageElement]"===(i=g.toString())||"[object HTMLCanvasElement]"===i)){e.save(),e.translate(n,r),e.rotate(f),e.drawImage(g,-g.width/2,-g.height/2,g.width,g.height),e.restore();return}if(!isNaN(m)&&!(m<=0)){switch(e.beginPath(),g){default:a?e.ellipse(n,r,a/2,m,0,0,es):e.arc(n,r,m,0,es),e.closePath();break;case"triangle":c=a?a/2:m,e.moveTo(n+Math.sin(f)*c,r-Math.cos(f)*m),f+=eg,e.lineTo(n+Math.sin(f)*c,r-Math.cos(f)*m),f+=eg,e.lineTo(n+Math.sin(f)*c,r-Math.cos(f)*m),e.closePath();break;case"rectRounded":u=.516*m,o=Math.cos(f+ed)*(l=m-u),h=Math.cos(f+ed)*(a?a/2-u:l),s=Math.sin(f+ed)*l,d=Math.sin(f+ed)*(a?a/2-u:l),e.arc(n-h,r-s,u,f-eo,f-eh),e.arc(n+d,r-o,u,f-eh,f),e.arc(n+h,r+s,u,f,f+eh),e.arc(n-d,r+o,u,f+eh,f+eo),e.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*m,c=a?a/2:l,e.rect(n-c,r-l,2*c,2*l);break}f+=ed;case"rectRot":h=Math.cos(f)*(a?a/2:m),o=Math.cos(f)*m,s=Math.sin(f)*m,d=Math.sin(f)*(a?a/2:m),e.moveTo(n-h,r-s),e.lineTo(n+d,r-o),e.lineTo(n+h,r+s),e.lineTo(n-d,r+o),e.closePath();break;case"crossRot":f+=ed;case"cross":h=Math.cos(f)*(a?a/2:m),o=Math.cos(f)*m,s=Math.sin(f)*m,d=Math.sin(f)*(a?a/2:m),e.moveTo(n-h,r-s),e.lineTo(n+h,r+s),e.moveTo(n+d,r-o),e.lineTo(n-d,r+o);break;case"star":h=Math.cos(f)*(a?a/2:m),o=Math.cos(f)*m,s=Math.sin(f)*m,d=Math.sin(f)*(a?a/2:m),e.moveTo(n-h,r-s),e.lineTo(n+h,r+s),e.moveTo(n+d,r-o),e.lineTo(n-d,r+o),f+=ed,h=Math.cos(f)*(a?a/2:m),o=Math.cos(f)*m,s=Math.sin(f)*m,d=Math.sin(f)*(a?a/2:m),e.moveTo(n-h,r-s),e.lineTo(n+h,r+s),e.moveTo(n+d,r-o),e.lineTo(n-d,r+o);break;case"line":o=a?a/2:Math.cos(f)*m,s=Math.sin(f)*m,e.moveTo(n-o,r-s),e.lineTo(n+o,r+s);break;case"dash":e.moveTo(n,r),e.lineTo(n+Math.cos(f)*(a?a/2:m),r+Math.sin(f)*m);break;case!1:e.closePath()}e.fill(),t.borderWidth>0&&e.stroke()}}function tc(e,t,n){return n=n||.5,!t||e&&e.x>t.left-n&&e.x<t.right+n&&e.y>t.top-n&&e.y<t.bottom+n}function th(e,t){e.save(),e.beginPath(),e.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),e.clip()}function td(e){e.restore()}function tg(e,t,n,r,a){if(!t)return e.lineTo(n.x,n.y);if("middle"===a){let r=(t.x+n.x)/2;e.lineTo(r,t.y),e.lineTo(r,n.y)}else"after"===a!=!!r?e.lineTo(t.x,n.y):e.lineTo(n.x,t.y);e.lineTo(n.x,n.y)}function tp(e,t,n,r){if(!t)return e.lineTo(n.x,n.y);e.bezierCurveTo(r?t.cp1x:t.cp2x,r?t.cp1y:t.cp2y,r?n.cp2x:n.cp1x,r?n.cp2y:n.cp1y,n.x,n.y)}function tm(e,t,n,r,a,i={}){let o,s;let l=z(t)?t:[t],u=i.strokeWidth>0&&""!==i.strokeColor;for(e.save(),e.font=a.string,i.translation&&e.translate(i.translation[0],i.translation[1]),V(i.rotation)||e.rotate(i.rotation),i.color&&(e.fillStyle=i.color),i.textAlign&&(e.textAlign=i.textAlign),i.textBaseline&&(e.textBaseline=i.textBaseline),o=0;o<l.length;++o)s=l[o],i.backdrop&&function(e,t){let n=e.fillStyle;e.fillStyle=t.color,e.fillRect(t.left,t.top,t.width,t.height),e.fillStyle=n}(e,i.backdrop),u&&(i.strokeColor&&(e.strokeStyle=i.strokeColor),V(i.strokeWidth)||(e.lineWidth=i.strokeWidth),e.strokeText(s,n,r,i.maxWidth)),e.fillText(s,n,r,i.maxWidth),function(e,t,n,r,a){if(a.strikethrough||a.underline){let i=e.measureText(r),o=t-i.actualBoundingBoxLeft,s=t+i.actualBoundingBoxRight,l=n-i.actualBoundingBoxAscent,u=n+i.actualBoundingBoxDescent,c=a.strikethrough?(l+u)/2:u;e.strokeStyle=e.fillStyle,e.beginPath(),e.lineWidth=a.decorationWidth||2,e.moveTo(o,c),e.lineTo(s,c),e.stroke()}}(e,n,r,s,i),r+=Number(a.lineHeight);e.restore()}function tf(e,t){let{x:n,y:r,w:a,h:i,radius:o}=t;e.arc(n+o.topLeft,r+o.topLeft,o.topLeft,1.5*eo,eo,!0),e.lineTo(n,r+i-o.bottomLeft),e.arc(n+o.bottomLeft,r+i-o.bottomLeft,o.bottomLeft,eo,eh,!0),e.lineTo(n+a-o.bottomRight,r+i),e.arc(n+a-o.bottomRight,r+i-o.bottomRight,o.bottomRight,eh,0,!0),e.lineTo(n+a,r+o.topRight),e.arc(n+a-o.topRight,r+o.topRight,o.topRight,0,-eh,!0),e.lineTo(n+o.topLeft,r)}let tb=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,tv=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/,ty=e=>+e||0;function tw(e,t){let n={},r=M(t),a=r?Object.keys(t):t,i=M(e)?r?n=>$(e[n],e[t[n]]):t=>e[t]:()=>e;for(let e of a)n[e]=ty(i(e));return n}function tk(e){return tw(e,{top:"y",right:"x",bottom:"y",left:"x"})}function tP(e){return tw(e,["topLeft","topRight","bottomLeft","bottomRight"])}function tA(e){let t=tk(e);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function tx(e,t){e=e||{},t=t||tr.font;let n=$(e.size,t.size);"string"==typeof n&&(n=parseInt(n,10));let r=$(e.style,t.style);r&&!(""+r).match(tv)&&(console.warn('Invalid font style specified: "'+r+'"'),r=void 0);let a={family:$(e.family,t.family),lineHeight:function(e,t){let n=(""+e).match(tb);if(!n||"normal"===n[1])return 1.2*t;switch(e=+n[2],n[3]){case"px":return e;case"%":e/=100}return t*e}($(e.lineHeight,t.lineHeight),n),size:n,style:r,weight:$(e.weight,t.weight),string:""};return a.string=!a||V(a.size)||V(a.family)?null:(a.style?a.style+" ":"")+(a.weight?a.weight+" ":"")+a.size+"px "+a.family,a}function tC(e,t,n,r){let a,i,o,s=!0;for(a=0,i=e.length;a<i;++a)if(void 0!==(o=e[a])&&(void 0!==t&&"function"==typeof o&&(o=o(t),s=!1),void 0!==n&&z(o)&&(o=o[n%o.length],s=!1),void 0!==o))return r&&!s&&(r.cacheable=!1),o}function tj(e,t,n){let{min:r,max:a}=e,i=K(t,(a-r)/2),o=(e,t)=>n&&0===e?0:e+t;return{min:o(r,-Math.abs(i)),max:o(a,i)}}function tN(e,t){return Object.assign(Object.create(e),t)}function tI(e,t=[""],n,r,a=()=>e[0]){let i=n||e;return void 0===r&&(r=tV("_fallback",e)),new Proxy({[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:e,_rootScopes:i,_fallback:r,_getTarget:a,override:n=>tI([n,...e],t,i,r)},{deleteProperty:(t,n)=>(delete t[n],delete t._keys,delete e[0][n],!0),get:(n,r)=>tH(n,r,()=>(function(e,t,n,r){let a;for(let i of t)if(void 0!==(a=tV(tT(i,e),n)))return tO(e,a)?tF(n,r,e,a):a})(r,t,e,n)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e._scopes[0],t),getPrototypeOf:()=>Reflect.getPrototypeOf(e[0]),has:(e,t)=>tz(e).includes(t),ownKeys:e=>tz(e),set(e,t,n){let r=e._storage||(e._storage=a());return e[t]=r[t]=n,delete e._keys,!0}})}function tD(e,t={scriptable:!0,indexable:!0}){let{_scriptable:n=t.scriptable,_indexable:r=t.indexable,_allKeys:a=t.allKeys}=e;return{allKeys:a,scriptable:n,indexable:r,isScriptable:er(n)?n:()=>n,isIndexable:er(r)?r:()=>r}}let tT=(e,t)=>e?e+et(t):t,tO=(e,t)=>M(t)&&"adapters"!==e&&(null===Object.getPrototypeOf(t)||t.constructor===Object);function tH(e,t,n){if(Object.prototype.hasOwnProperty.call(e,t)||"constructor"===t)return e[t];let r=n();return e[t]=r,r}let tE=(e,t)=>!0===e?t:"string"==typeof e?ee(t,e):void 0;function tF(e,t,n,r){var a;let i=t._rootScopes,o=er(a=t._fallback)?a(n,r):a,s=[...e,...i],l=new Set;l.add(r);let u=tG(l,s,n,o||n,r);return null!==u&&(void 0===o||o===n||null!==(u=tG(l,s,o,u,r)))&&tI(Array.from(l),[""],i,o,()=>(function(e,t,n){let r=e._getTarget();t in r||(r[t]={});let a=r[t];return z(a)&&M(n)?n:a||{}})(t,n,r))}function tG(e,t,n,r,a){for(;n;)n=function(e,t,n,r,a){for(let o of t){let t=tE(n,o);if(t){var i;e.add(t);let o=er(i=t._fallback)?i(n,a):i;if(void 0!==o&&o!==n&&o!==r)return o}else if(!1===t&&void 0!==r&&n!==r)return null}return!1}(e,t,n,r,a);return n}function tV(e,t){for(let n of t){if(!n)continue;let t=n[e];if(void 0!==t)return t}}function tz(e){let t=e._keys;return t||(t=e._keys=function(e){let t=new Set;for(let n of e)for(let e of Object.keys(n).filter(e=>!e.startsWith("_")))t.add(e);return Array.from(t)}(e._scopes)),t}function tM(e,t,n,r){let a,i,o;let{iScale:s}=e,{key:l="r"}=this._parsing,u=Array(r);for(a=0;a<r;++a)o=t[i=a+n],u[a]={r:s.parse(ee(o,l),i)};return u}let tB=Number.EPSILON||1e-14,tS=(e,t)=>t<e.length&&!e[t].skip&&e[t],t$=e=>"x"===e?"y":"x";function tL(e,t,n){return Math.max(Math.min(e,n),t)}function tK(e,t,n,r,a){let i,o,s,l;if(t.spanGaps&&(e=e.filter(e=>!e.skip)),"monotone"===t.cubicInterpolationMode)!function(e,t="x"){let n,r,a;let i=t$(t),o=e.length,s=Array(o).fill(0),l=Array(o),u=tS(e,0);for(n=0;n<o;++n)if(r=a,a=u,u=tS(e,n+1),a){if(u){let e=u[t]-a[t];s[n]=0!==e?(u[i]-a[i])/e:0}l[n]=r?u?em(s[n-1])!==em(s[n])?0:(s[n-1]+s[n])/2:s[n-1]:s[n]}!function(e,t,n){let r,a,i,o,s;let l=e.length,u=tS(e,0);for(let c=0;c<l-1;++c)if(s=u,u=tS(e,c+1),s&&u){if(ef(t[c],0,tB)){n[c]=n[c+1]=0;continue}(o=Math.pow(r=n[c]/t[c],2)+Math.pow(a=n[c+1]/t[c],2))<=9||(i=3/Math.sqrt(o),n[c]=r*i*t[c],n[c+1]=a*i*t[c])}}(e,s,l),function(e,t,n="x"){let r,a,i;let o=t$(n),s=e.length,l=tS(e,0);for(let u=0;u<s;++u){if(a=i,i=l,l=tS(e,u+1),!i)continue;let s=i[n],c=i[o];a&&(r=(s-a[n])/3,i[`cp1${n}`]=s-r,i[`cp1${o}`]=c-r*t[u]),l&&(r=(l[n]-s)/3,i[`cp2${n}`]=s+r,i[`cp2${o}`]=c+r*t[u])}}(e,l,t)}(e,a);else{let n=r?e[e.length-1]:e[0];for(i=0,o=e.length;i<o;++i)l=function(e,t,n,r){let a=e.skip?t:e,i=n.skip?t:n,o=ej(t,a),s=ej(i,t),l=o/(o+s),u=s/(o+s);l=isNaN(l)?0:l,u=isNaN(u)?0:u;let c=r*l,h=r*u;return{previous:{x:t.x-c*(i.x-a.x),y:t.y-c*(i.y-a.y)},next:{x:t.x+h*(i.x-a.x),y:t.y+h*(i.y-a.y)}}}(n,s=e[i],e[Math.min(i+1,o-(r?0:1))%o],t.tension),s.cp1x=l.previous.x,s.cp1y=l.previous.y,s.cp2x=l.next.x,s.cp2y=l.next.y,n=s}t.capBezierPoints&&function(e,t){let n,r,a,i,o;let s=tc(e[0],t);for(n=0,r=e.length;n<r;++n)o=i,i=s,s=n<r-1&&tc(e[n+1],t),i&&(a=e[n],o&&(a.cp1x=tL(a.cp1x,t.left,t.right),a.cp1y=tL(a.cp1y,t.top,t.bottom)),s&&(a.cp2x=tL(a.cp2x,t.left,t.right),a.cp2y=tL(a.cp2y,t.top,t.bottom)))}(e,n)}function tJ(){return"undefined"!=typeof window&&"undefined"!=typeof document}function tW(e){let t=e.parentNode;return t&&"[object ShadowRoot]"===t.toString()&&(t=t.host),t}function tq(e,t,n){let r;return"string"==typeof e?(r=parseInt(e,10),-1!==e.indexOf("%")&&(r=r/100*t.parentNode[n])):r=e,r}let tR=e=>e.ownerDocument.defaultView.getComputedStyle(e,null),tU=["top","right","bottom","left"];function t_(e,t,n){let r={};n=n?"-"+n:"";for(let a=0;a<4;a++){let i=tU[a];r[i]=parseFloat(e[t+"-"+i+n])||0}return r.width=r.left+r.right,r.height=r.top+r.bottom,r}let tQ=(e,t,n)=>(e>0||t>0)&&(!n||!n.shadowRoot);function tZ(e,t){if("native"in e)return e;let{canvas:n,currentDevicePixelRatio:r}=t,a=tR(n),i="border-box"===a.boxSizing,o=t_(a,"padding"),s=t_(a,"border","width"),{x:l,y:u,box:c}=function(e,t){let n,r;let a=e.touches,i=a&&a.length?a[0]:e,{offsetX:o,offsetY:s}=i,l=!1;if(tQ(o,s,e.target))n=o,r=s;else{let e=t.getBoundingClientRect();n=i.clientX-e.left,r=i.clientY-e.top,l=!0}return{x:n,y:r,box:l}}(e,n),h=o.left+(c&&s.left),d=o.top+(c&&s.top),{width:g,height:p}=t;return i&&(g-=o.width+s.width,p-=o.height+s.height),{x:Math.round((l-h)/g*n.width/r),y:Math.round((u-d)/p*n.height/r)}}let tY=e=>Math.round(10*e)/10;function tX(e,t,n,r){let a=tR(e),i=t_(a,"margin"),o=tq(a.maxWidth,e,"clientWidth")||eu,s=tq(a.maxHeight,e,"clientHeight")||eu,l=function(e,t,n){let r,a;if(void 0===t||void 0===n){let i=e&&tW(e);if(i){let e=i.getBoundingClientRect(),o=tR(i),s=t_(o,"border","width"),l=t_(o,"padding");t=e.width-l.width-s.width,n=e.height-l.height-s.height,r=tq(o.maxWidth,i,"clientWidth"),a=tq(o.maxHeight,i,"clientHeight")}else t=e.clientWidth,n=e.clientHeight}return{width:t,height:n,maxWidth:r||eu,maxHeight:a||eu}}(e,t,n),{width:u,height:c}=l;if("content-box"===a.boxSizing){let e=t_(a,"border","width"),t=t_(a,"padding");u-=t.width+e.width,c-=t.height+e.height}return u=Math.max(0,u-i.width),c=Math.max(0,r?u/r:c-i.height),u=tY(Math.min(u,o,l.maxWidth)),c=tY(Math.min(c,s,l.maxHeight)),u&&!c&&(c=tY(u/2)),(void 0!==t||void 0!==n)&&r&&l.height&&c>l.height&&(u=tY(Math.floor((c=l.height)*r))),{width:u,height:c}}function t0(e,t,n){let r=t||1,a=Math.floor(e.height*r),i=Math.floor(e.width*r);e.height=Math.floor(e.height),e.width=Math.floor(e.width);let o=e.canvas;return o.style&&(n||!o.style.height&&!o.style.width)&&(o.style.height=`${e.height}px`,o.style.width=`${e.width}px`),(e.currentDevicePixelRatio!==r||o.height!==a||o.width!==i)&&(e.currentDevicePixelRatio=r,o.height=a,o.width=i,e.ctx.setTransform(r,0,0,r,0,0),!0)}let t1=function(){let e=!1;try{let t={get passive(){return e=!0,!1}};tJ()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch(e){}return e}();function t2(e,t){let n=tR(e).getPropertyValue(t),r=n&&n.match(/^(\d+)(\.\d+)?px$/);return r?+r[1]:void 0}function t3(e,t,n,r){return{x:e.x+n*(t.x-e.x),y:e.y+n*(t.y-e.y)}}function t5(e,t,n,r){return{x:e.x+n*(t.x-e.x),y:"middle"===r?n<.5?e.y:t.y:"after"===r?n<1?e.y:t.y:n>0?t.y:e.y}}function t4(e,t,n,r){let a={x:e.cp2x,y:e.cp2y},i={x:t.cp1x,y:t.cp1y},o=t3(e,a,n),s=t3(a,i,n),l=t3(i,t,n),u=t3(o,s,n),c=t3(s,l,n);return t3(u,c,n)}function t6(e,t,n){var r;return e?(r=n,{x:e=>t+t+r-e,setWidth(e){r=e},textAlign:e=>"center"===e?e:"right"===e?"left":"right",xPlus:(e,t)=>e-t,leftForLtr:(e,t)=>e-t}):{x:e=>e,setWidth(e){},textAlign:e=>e,xPlus:(e,t)=>e+t,leftForLtr:(e,t)=>e}}function t9(e,t){let n,r;("ltr"===t||"rtl"===t)&&(r=[(n=e.canvas.style).getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",t,"important"),e.prevTextDirection=r)}function t7(e,t){void 0!==t&&(delete e.prevTextDirection,e.canvas.style.setProperty("direction",t[0],t[1]))}function t8(e){return"angle"===e?{between:eD,compare:eN,normalize:eI}:{between:eH,compare:(e,t)=>e-t,normalize:e=>e}}function ne({start:e,end:t,count:n,loop:r,style:a}){return{start:e%n,end:t%n,loop:r&&(t-e+1)%n==0,style:a}}function nt(e,t,n){let r,a,i;if(!n)return[e];let{property:o,start:s,end:l}=n,u=t.length,{compare:c,between:h,normalize:d}=t8(o),{start:g,end:p,loop:m,style:f}=function(e,t,n){let r;let{property:a,start:i,end:o}=n,{between:s,normalize:l}=t8(a),u=t.length,{start:c,end:h,loop:d}=e;if(d){for(c+=u,h+=u,r=0;r<u&&s(l(t[c%u][a]),i,o);++r)c--,h--;c%=u,h%=u}return h<c&&(h+=u),{start:c,end:h,loop:d,style:e.style}}(e,t,n),b=[],v=!1,y=null,w=()=>h(s,i,r)&&0!==c(s,i),k=()=>0===c(l,r)||h(l,i,r),P=()=>v||w(),A=()=>!v||k();for(let e=g,n=g;e<=p;++e)(a=t[e%u]).skip||(r=d(a[o]))===i||(v=h(r,s,l),null===y&&P()&&(y=0===c(r,s)?e:n),null!==y&&A()&&(b.push(ne({start:y,end:e,loop:m,count:u,style:f})),y=null),n=e,i=r);return null!==y&&b.push(ne({start:y,end:p,loop:m,count:u,style:f})),b}function nn(e,t){let n=[],r=e.segments;for(let a=0;a<r.length;a++){let i=nt(r[a],e.points,t);i.length&&n.push(...i)}return n}function nr(e,t){let n=e.points,r=e.options.spanGaps,a=n.length;if(!a)return[];let i=!!e._loop,{start:o,end:s}=function(e,t,n,r){let a=0,i=t-1;if(n&&!r)for(;a<t&&!e[a].skip;)a++;for(;a<t&&e[a].skip;)a++;for(a%=t,n&&(i+=a);i>a&&e[i%t].skip;)i--;return{start:a,end:i%=t}}(n,a,i,r);if(!0===r)return na(e,[{start:o,end:s,loop:i}],n,t);let l=s<o?s+a:s,u=!!e._fullLoop&&0===o&&s===a-1;return na(e,function(e,t,n,r){let a;let i=e.length,o=[],s=t,l=e[t];for(a=t+1;a<=n;++a){let n=e[a%i];n.skip||n.stop?l.skip||(r=!1,o.push({start:t%i,end:(a-1)%i,loop:r}),t=s=n.stop?a:null):(s=a,l.skip&&(t=a)),l=n}return null!==s&&o.push({start:t%i,end:s%i,loop:r}),o}(n,o,l,u),n,t)}function na(e,t,n,r){return r&&r.setContext&&n?function(e,t,n,r){let a=e._chart.getContext(),i=ni(e.options),{_datasetIndex:o,options:{spanGaps:s}}=e,l=n.length,u=[],c=i,h=t[0].start,d=h;function g(e,t,r,a){let i=s?-1:1;if(e!==t){for(e+=l;n[e%l].skip;)e-=i;for(;n[t%l].skip;)t+=i;e%l!=t%l&&(u.push({start:e%l,end:t%l,loop:r,style:a}),c=a,h=t%l)}}for(let e of t){let t;let i=n[(h=s?h:e.start)%l];for(d=h+1;d<=e.end;d++){let s=n[d%l];(function(e,t){if(!t)return!1;let n=[],r=function(e,t){return eX(t)?(n.includes(t)||n.push(t),n.indexOf(t)):t};return JSON.stringify(e,r)!==JSON.stringify(t,r)})(t=ni(r.setContext(tN(a,{type:"segment",p0:i,p1:s,p0DataIndex:(d-1)%l,p1DataIndex:d%l,datasetIndex:o}))),c)&&g(h,d-1,e.loop,c),i=s,c=t}h<d-1&&g(h,d-1,e.loop,c)}return u}(e,t,n,r):t}function ni(e){return{backgroundColor:e.backgroundColor,borderCapStyle:e.borderCapStyle,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderJoinStyle:e.borderJoinStyle,borderWidth:e.borderWidth,borderColor:e.borderColor}}},9266:function(e,t,n){n.d(t,{Z:function(){return hx}});var r,a,i,o={methods:{one:{},two:{},three:{},four:{}},model:{one:{},two:{},three:{}},compute:{},hooks:[]};let s=e=>"[object Array]"===Object.prototype.toString.call(e),l={termList:function(){return this.methods.one.termList(this.docs)},terms:function(e){let t=this.match(".");return"number"==typeof e?t.eq(e):t},groups:function(e){if(e||0===e)return this.update(this._groups[e]||[]);let t={};return Object.keys(this._groups).forEach(e=>{t[e]=this.update(this._groups[e])}),t},eq:function(e){let t=this.pointer;return(t||(t=this.docs.map((e,t)=>[t])),t[e])?this.update([t[e]]):this.none()},first:function(){return this.eq(0)},last:function(){let e=this.fullPointer.length-1;return this.eq(e)},firstTerms:function(){return this.match("^.")},lastTerms:function(){return this.match(".$")},slice:function(e,t){let n=this.pointer||this.docs.map((e,t)=>[t]);return n=n.slice(e,t),this.update(n)},all:function(){return this.update().toView()},fullSentences:function(){let e=this.fullPointer.map(e=>[e[0]]);return this.update(e).toView()},none:function(){return this.update([])},isDoc:function(e){if(!e||!e.isView)return!1;let t=this.fullPointer,n=e.fullPointer;return!t.length!==n.length&&t.every((e,t)=>!!n[t]&&e[0]===n[t][0]&&e[1]===n[t][1]&&e[2]===n[t][2])},wordCount:function(){return this.docs.reduce((e,t)=>e+=t.filter(e=>""!==e.text).length,0)},isFull:function(){let e=this.pointer;if(!e)return!0;if(0===e.length||0!==e[0][0])return!1;let t=0,n=0;return this.document.forEach(e=>t+=e.length),this.docs.forEach(e=>n+=e.length),t===n},getNth:function(e){return"number"==typeof e?this.eq(e):"string"==typeof e?this.if(e):this}};l.group=l.groups,l.fullSentence=l.fullSentences,l.sentence=l.fullSentences,l.lastTerm=l.lastTerms,l.firstTerm=l.firstTerms;let u=Object.assign({},l,{compute:function(e){let{world:t}=this,n=t.compute;return"string"==typeof e&&n.hasOwnProperty(e)?n[e](this):s(e)?e.forEach(r=>{t.compute.hasOwnProperty(r)?n[r](this):console.warn("no compute:",e)}):"function"==typeof e?e(this):console.warn("no compute:",e),this}},{forEach:function(e){return this.fullPointer.forEach((t,n)=>{e(this.update([t]),n)}),this},map:function(e,t){let n=this.fullPointer.map((t,n)=>{let r=e(this.update([t]),n);return void 0===r?this.none():r});if(0===n.length)return t||this.update([]);if(void 0!==n[0]&&("string"==typeof n[0]||"object"==typeof n[0]&&(null===n[0]||!n[0].isView)))return n;let r=[];return n.forEach(e=>{r=r.concat(e.fullPointer)}),this.toView(r)},filter:function(e){let t=this.fullPointer;return t=t.filter((t,n)=>e(this.update([t]),n)),this.update(t)},find:function(e){let t=this.fullPointer.find((t,n)=>e(this.update([t]),n));return this.update([t])},some:function(e){return this.fullPointer.some((t,n)=>e(this.update([t]),n))},random:function(e=1){let t=this.fullPointer,n=Math.floor(Math.random()*t.length);return n+e>this.length&&(n=(n=this.length-e)<0?0:n),t=t.slice(n,n+e),this.update(t)}});u.get=u.eq;class c{constructor(e,t,n={}){[["document",e],["world",o],["_groups",n],["_cache",null],["viewType","View"]].forEach(e=>{Object.defineProperty(this,e[0],{value:e[1],writable:!0})}),this.ptrs=t}get docs(){let e=this.document;return this.ptrs&&(e=o.methods.one.getDoc(this.ptrs,this.document)),e}get pointer(){return this.ptrs}get methods(){return this.world.methods}get model(){return this.world.model}get hooks(){return this.world.hooks}get isView(){return!0}get found(){return this.docs.length>0}get length(){return this.docs.length}get fullPointer(){let{docs:e,ptrs:t,document:n}=this;return(t||e.map((e,t)=>[t])).map(e=>{let[t,r,a,i,o]=e;return r=r||0,a=a||(n[t]||[]).length,n[t]&&n[t][r]&&(i=i||n[t][r].id,n[t][a-1]&&(o=o||n[t][a-1].id)),[t,r,a,i,o]})}update(e){let t=new c(this.document,e);if(this._cache&&e&&e.length>0){let n=[];e.forEach((e,t)=>{let[r,a,i]=e;1===e.length?n[t]=this._cache[r]:0===a&&this.document[r].length===i&&(n[t]=this._cache[r])}),n.length>0&&(t._cache=n)}return t.world=this.world,t}toView(e){return new c(this.document,e||this.pointer)}fromText(e){let{methods:t}=this,n=new c(t.one.tokenize.fromString(e,this.world));return n.world=this.world,n.compute(["normal","freeze","lexicon"]),this.world.compute.preTagger&&n.compute("preTagger"),n.compute("unfreeze"),n}clone(){let e=this.document.slice(0);e=e.map(e=>e.map(e=>((e=Object.assign({},e)).tags=new Set(e.tags),e)));let t=this.update(this.pointer);return t.document=e,t._cache=this._cache,t}}Object.assign(c.prototype,u);let h=function(e){return e&&"object"==typeof e&&!Array.isArray(e)},d=function(e,t){let n=e.two.models||{};Object.keys(t).forEach(e=>{t[e].pastTense&&(n.toPast&&(n.toPast.ex[e]=t[e].pastTense),n.fromPast&&(n.fromPast.ex[t[e].pastTense]=e)),t[e].presentTense&&(n.toPresent&&(n.toPresent.ex[e]=t[e].presentTense),n.fromPresent&&(n.fromPresent.ex[t[e].presentTense]=e)),t[e].gerund&&(n.toGerund&&(n.toGerund.ex[e]=t[e].gerund),n.fromGerund&&(n.fromGerund.ex[t[e].gerund]=e)),t[e].comparative&&(n.toComparative&&(n.toComparative.ex[e]=t[e].comparative),n.fromComparative&&(n.fromComparative.ex[t[e].comparative]=e)),t[e].superlative&&(n.toSuperlative&&(n.toSuperlative.ex[e]=t[e].superlative),n.fromSuperlative&&(n.fromSuperlative.ex[t[e].superlative]=e))})};var g=function(e,t,n,r){let{methods:a,model:i,compute:o,hooks:s}=t;e.methods&&function(e,t){for(let n in t)e[n]=e[n]||{},Object.assign(e[n],t[n])}(a,e.methods),e.model&&function e(t,n){if(h(n))for(let r in n)h(n[r])?(t[r]||Object.assign(t,{[r]:{}}),e(t[r],n[r])):Object.assign(t,{[r]:n[r]});return t}(i,e.model),e.irregulars&&d(i,e.irregulars),e.compute&&Object.assign(o,e.compute),s&&(t.hooks=s.concat(e.hooks||[])),e.api&&e.api(n),e.lib&&Object.keys(e.lib).forEach(t=>r[t]=e.lib[t]),e.tags&&r.addTags(e.tags),e.words&&r.addWords(e.words),e.frozen&&r.addWords(e.frozen,!0),e.mutate&&e.mutate(t,r)},p=n(20357);let m=e=>"[object Object]"===Object.prototype.toString.call(e),f=function(e){return"[object Array]"===Object.prototype.toString.call(e)};var b=function(e,t,n){let{methods:r}=n,a=new t([]);return(a.world=n,"number"==typeof e&&(e=String(e)),e)?"string"==typeof e?new t(r.one.tokenize.fromString(e,n)):m(e)&&e.isView?new t(e.document,e.ptrs):f(e)?new t(f(e[0])?e.map(e=>e.map(e=>({text:e,normal:e,pre:"",post:" ",tags:new Set}))):e.map(e=>e.terms.map(e=>(f(e.tags)&&(e.tags=new Set(e.tags)),e)))):a:a};let v=Object.assign({},o),y=function(e,t){t&&y.addWords(t);let n=b(e,c,v);return e&&n.compute(v.hooks),n};Object.defineProperty(y,"_world",{value:v,writable:!0}),y.tokenize=function(e,t){let{compute:n}=this._world;t&&y.addWords(t);let r=b(e,c,v);return n.contractions&&r.compute(["alias","normal","machine","contractions"]),r},y.plugin=function(e){return g(e,this._world,c,this),this},y.extend=y.plugin,y.world=function(){return this._world},y.model=function(){return this._world.model},y.methods=function(){return this._world.methods},y.hooks=function(){return this._world.hooks},y.verbose=function(e){let t=void 0!==p&&p.env?p.env:self.env||{};return t.DEBUG_TAGS="tagger"===e||!0===e||"",t.DEBUG_MATCH="match"===e||!0===e||"",t.DEBUG_CHUNKS="chunker"===e||!0===e||"",this},y.version="14.14.4";let w={cache:function(){return this._cache=this.methods.one.cacheDoc(this.document),this},uncache:function(){return this._cache=null,this}},k=e=>/^\p{Lu}[\p{Ll}'’]/u.test(e)||/^\p{Lu}$/u.test(e),P=e=>e.replace(/^\p{Ll}/u,e=>e.toUpperCase()),A=e=>e.replace(/^\p{Lu}/u,e=>e.toLowerCase()),x=(e,t,n)=>{if(n.forEach(e=>e.dirty=!0),e){let r=[t,0].concat(n);Array.prototype.splice.apply(e,r)}return e},C=function(e){let t=e[e.length-1];!t||/ $/.test(t.post)||/[-–—]/.test(t.post)||(t.post+=" ")},j=(e,t,n)=>{let r=/[-.?!,;:)–—'"]/g,a=e[t-1];if(!a)return;let i=a.post;if(r.test(i)){let e=i.match(r).join(""),t=n[n.length-1];t.post=e+t.post,a.post=a.post.replace(r,"")}},N=function(e,t,n){let r=e[t];if(0!==t||!k(r.text))return;n[0].text=P(n[0].text);let a=e[t];!(a.tags.has("ProperNoun")||a.tags.has("Acronym"))&&k(a.text)&&a.text.length>1&&(a.text=A(a.text))},I=function(e,t,n,r){let[a,i,o]=t;0===i?C(n):o===r[a].length?C(n):(C(n),C([e[t[1]]])),N(e,i,n),x(e,i,n)},D=function(e,t,n,r){let[a,,i]=t,o=(r[a]||[]).length;i<o?(j(e,i,n),C(n)):o===i&&(C(e),j(e,i,n),r[a+1]&&(n[n.length-1].post+=" ")),x(e,t[2],n),t[4]=n[n.length-1].id},T=0,O=e=>(e=e.length<3?"0"+e:e).length<3?"0"+e:e;var H=function(e){let[t,n]=e.index||[0,0];T+=1,t=t>46655?0:t,n=n>1294?0:n;let r=O((T=T>46655?0:T).toString(36));r+=O(t.toString(36));let a=n.toString(36);return r+=(a=a.length<2?"0"+a:a)+parseInt(36*Math.random(),10).toString(36),e.normal+"|"+r.toUpperCase()};let E=function(e){e.has("@hasContraction")&&"function"==typeof e.contractions&&e.grow("@hasContraction").contractions().expand()},F=e=>"[object Array]"===Object.prototype.toString.call(e),G=function(e,t){let{methods:n}=t;return"string"==typeof e?n.one.tokenize.fromString(e,t)[0]:"object"==typeof e&&e.isView?e.clone().docs[0]||[]:F(e)?F(e[0])?e[0]:e:[]},V=function(e,t,n){let{document:r,world:a}=t;t.uncache();let i=t.fullPointer,o=t.fullPointer;t.forEach((s,l)=>{let u=s.fullPointer[0],[c]=u,h=r[c],d=G(e,a);0!==d.length&&(d=d.map(e=>(e.id=H(e),e)),n?(E(t.update([u]).firstTerm()),I(h,u,d,r)):(E(t.update([u]).lastTerm()),D(h,u,d,r)),r[c]&&r[c][u[1]]&&(u[3]=r[c][u[1]].id),o[l]=u,u[2]+=d.length,i[l]=u)});let s=t.toView(i);return t.ptrs=o,s.compute(["id","index","freeze","lexicon"]),s.world.compute.preTagger&&s.compute("preTagger"),s.compute("unfreeze"),s},z={insertAfter:function(e){return V(e,this,!1)},insertBefore:function(e){return V(e,this,!0)}};z.append=z.insertAfter,z.prepend=z.insertBefore,z.insert=z.insertAfter;let M=/\$[0-9a-z]+/g,B={},S=e=>/^\p{Lu}[\p{Ll}'’]/u.test(e)||/^\p{Lu}$/u.test(e),$=e=>e.replace(/^\p{Ll}/u,e=>e.toUpperCase()),L=e=>e.replace(/^\p{Lu}/u,e=>e.toLowerCase()),K=function(e,t){if("string"!=typeof e)return e;let n=t.groups();return e=e.replace(M,e=>{let t=e.replace(/\$/,"");return n.hasOwnProperty(t)?n[t].text():e})};B.replaceWith=function(e,t={}){let n=this.fullPointer;if(this.uncache(),"function"==typeof e){var r;return r=e,this.forEach(e=>{let n=r(e);e.replaceWith(n,t)}),this}let a=this.docs[0];if(!a)return this;let i=t.possessives&&a[a.length-1].tags.has("Possessive"),o=t.case&&S(a[0].text);e=K(e,this);let s=this.update(n);n=n.map(e=>e.slice(0,3));let l=(s.docs[0]||[]).map(e=>Array.from(e.tags)),u=s.docs[0][0].pre,c=s.docs[0][s.docs[0].length-1].post;if("string"==typeof e&&(e=this.fromText(e).compute("id")),this.insertAfter(e),s.has("@hasContraction")&&this.contractions&&this.grow("@hasContraction+").contractions().expand(),this.delete(s),i){let e=this.docs[0],t=e[e.length-1];t.tags.has("Possessive")||(t.text+="'s",t.normal+="'s",t.tags.add("Possessive"))}if(u&&this.docs[0]&&(this.docs[0][0].pre=u),c&&this.docs[0]){let e=this.docs[0][this.docs[0].length-1];e.post.trim()||(e.post=c)}let h=this.toView(n).compute(["index","freeze","lexicon"]);if(h.world.compute.preTagger&&h.compute("preTagger"),h.compute("unfreeze"),t.tags&&h.terms().forEach((e,t)=>{e.tagSafe(l[t])}),!h.docs[0]||!h.docs[0][0])return h;if(t.case){let e=o?$:L;h.docs[0][0].text=e(h.docs[0][0].text)}return h},B.replace=function(e,t,n){if(e&&!t)return this.replaceWith(e,n);let r=this.match(e);return r.found?(this.soften(),r.replaceWith(t,n)):this};let J=function(e,t){let n=e.length-1,r=e[n],a=e[n-t];a&&r&&(a.post+=r.post,a.post=a.post.replace(/ +([.?!,;:])/,"$1"),a.post=a.post.replace(/[,;:]+([.?!])/,"$1"))};var W=function(e,t){t.forEach(t=>{let[n,r,a]=t,i=a-r;e[n]&&(a===e[n].length&&a>1&&J(e[n],i),e[n].splice(r,i))});for(let t=e.length-1;t>=0;t-=1)if(0===e[t].length&&(e.splice(t,1),t===e.length&&e[t-1])){let n=e[t-1],r=n[n.length-1];r&&(r.post=r.post.trimEnd())}return e};let q={remove:function(e){var t;let{indexN:n}=this.methods.one.pointer;this.uncache();let r=this.all(),a=this;e&&(r=this,a=this.match(e));let i=!r.ptrs;a.has("@hasContraction")&&a.contractions&&a.grow("@hasContraction").contractions().expand();let o=r.fullPointer,s=a.fullPointer.reverse(),l=W(this.document,s),u=n(s);return((t=(t=o).map(e=>{let[t]=e;return u[t]&&u[t].forEach(t=>{let n=t[2]-t[1];e[1]<=t[1]&&e[2]>=t[2]&&(e[2]-=n)}),e})).forEach((e,n)=>{if(0===e[1]&&0==e[2])for(let e=n+1;e<t.length;e+=1)t[e][0]-=1,t[e][0]<0&&(t[e][0]=0)}),o=t=(t=t.filter(e=>e[2]-e[1]>0)).map(e=>(e[3]=null,e[4]=null,e)),r.ptrs=o,r.document=l,r.compute("index"),i&&(r.ptrs=void 0),e)?r.toView(o):(this.ptrs=[],r.none())}};q.delete=q.remove;let R={pre:function(e,t){return void 0===e&&this.found?this.docs[0][0].pre:(this.docs.forEach(n=>{let r=n[0];!0===t?r.pre+=e:r.pre=e}),this)},post:function(e,t){if(void 0===e){let e=this.docs[this.docs.length-1];return e[e.length-1].post}return this.docs.forEach(n=>{let r=n[n.length-1];!0===t?r.post+=e:r.post=e}),this},trim:function(){if(!this.found)return this;let e=this.docs,t=e[0][0];t.pre=t.pre.trimStart();let n=e[e.length-1],r=n[n.length-1];return r.post=r.post.trimEnd(),this},hyphenate:function(){return this.docs.forEach(e=>{e.forEach((t,n)=>{0!==n&&(t.pre=""),e[n+1]&&(t.post="-")})}),this},dehyphenate:function(){let e=/[-–—]/;return this.docs.forEach(t=>{t.forEach(t=>{e.test(t.post)&&(t.post=" ")})}),this},toQuotations:function(e,t){return e=e||'"',t=t||'"',this.docs.forEach(n=>{n[0].pre=e+n[0].pre;let r=n[n.length-1];r.post=t+r.post}),this},toParentheses:function(e,t){return e=e||"(",t=t||")",this.docs.forEach(n=>{n[0].pre=e+n[0].pre;let r=n[n.length-1];r.post=t+r.post}),this}};R.deHyphenate=R.dehyphenate,R.toQuotation=R.toQuotations;var U={alpha:(e,t)=>e.normal<t.normal?-1:e.normal>t.normal?1:0,length:(e,t)=>{let n=e.normal.trim().length,r=t.normal.trim().length;return n<r?1:n>r?-1:0},wordCount:(e,t)=>e.words<t.words?1:e.words>t.words?-1:0,sequential:(e,t)=>e[0]<t[0]?1:e[0]>t[0]?-1:e[1]>t[1]?1:-1,byFreq:function(e){let t={};return e.forEach(e=>{t[e.normal]=t[e.normal]||0,t[e.normal]+=1}),e.sort((e,n)=>{let r=t[e.normal],a=t[n.normal];return r<a?1:r>a?-1:0}),e}};let _=new Set(["index","sequence","seq","sequential","chron","chronological"]),Q=new Set(["freq","frequency","topk","repeats"]),Z=new Set(["alpha","alphabetical"]),Y=function(e,t){let n=e.fullPointer;return n=n.sort((n,r)=>t(n=e.update([n]),r=e.update([r]))),e.ptrs=n,e},X=e=>"[object Array]"===Object.prototype.toString.call(e),ee=function(e,t){if(e.length>0){let t=e[e.length-1],n=t[t.length-1];!1===/ /.test(n.post)&&(n.post+=" ")}return e=e.concat(t)},et=function(e,t){if(e.document===t.document){let n=e.fullPointer.concat(t.fullPointer);return e.toView(n).compute("index")}return t.fullPointer.forEach(t=>{t[0]+=e.document.length}),e.document=ee(e.document,t.docs),e.all()},en=Object.assign({},{toLowerCase:function(){return this.termList().forEach(e=>{e.text=e.text.toLowerCase()}),this},toUpperCase:function(){return this.termList().forEach(e=>{e.text=e.text.toUpperCase()}),this},toTitleCase:function(){return this.termList().forEach(e=>{e.text=e.text.replace(/^ *[a-z\u00C0-\u00FF]/,e=>e.toUpperCase())}),this},toCamelCase:function(){return this.docs.forEach(e=>{e.forEach((t,n)=>{0!==n&&(t.text=t.text.replace(/^ *[a-z\u00C0-\u00FF]/,e=>e.toUpperCase())),n!==e.length-1&&(t.post="")})}),this}},z,B,q,R,{unique:function(){let e=new Set;return this.filter(t=>{let n=t.text("machine");return!e.has(n)&&(e.add(n),!0)})},reverse:function(){let e=this.pointer||this.docs.map((e,t)=>[t]);return e=(e=[].concat(e)).reverse(),this._cache&&(this._cache=this._cache.reverse()),this.update(e)},sort:function(e){let{docs:t,pointer:n}=this;if(this.uncache(),"function"==typeof e)return Y(this,e);e=e||"alpha";let r=n||t.map((e,t)=>[t]),a=t.map((e,t)=>({index:t,words:e.length,normal:e.map(e=>e.machine||e.normal||"").join(" "),pointer:r[t]}));return(_.has(e)&&(e="sequential"),Z.has(e)&&(e="alpha"),Q.has(e))?(a=U.byFreq(a),this.update(a.map(e=>e.pointer))):"function"==typeof U[e]?(a=a.sort(U[e]),this.update(a.map(e=>e.pointer))):this}},{concat:function(e){if("string"==typeof e){let t=this.fromText(e);if(this.found&&this.ptrs){let e=this.fullPointer,n=e[e.length-1][0];this.document.splice(n,0,...t.document)}else this.document=this.document.concat(t.document);return this.all().compute("index")}if("object"==typeof e&&e.isView)return et(this,e);if(X(e)){let t=ee(this.document,e);return this.document=t,this.all()}return this}},{harden:function(){return this.ptrs=this.fullPointer,this},soften:function(){let e=this.ptrs;return!e||e.length<1||(e=e.map(e=>e.slice(0,3)),this.ptrs=e),this}});var er=function(e,t,n){let[r,a]=t;n&&0!==n.length&&((n=n.map((e,t)=>(e.implicit=e.text,e.machine=e.text,e.pre="",e.post="",e.text="",e.normal="",e.index=[r,a+t],e)))[0]&&(n[0].pre=e[r][a].pre,n[n.length-1].post=e[r][a].post,n[0].text=e[r][a].text,n[0].normal=e[r][a].normal),e[r].splice(a,1,...n))};let ea=/'/,ei=new Set(["what","how","when","where","why"]),eo=new Set(["be","go","start","think","need"]),es=new Set(["been","gone"]);var el=function(e,t){let n=e[t].normal.split(ea)[0];if(ei.has(n))return[n,"did"];if(e[t+1]){if(es.has(e[t+1].normal))return[n,"had"];if(eo.has(e[t+1].normal))return[n,"would"]}return null};let eu=/'/,ec=/(e|é|aison|sion|tion)$/,eh=/(age|isme|acle|ege|oire)$/;var ed=(e,t)=>["je",e[t].normal.split(eu)[1]],eg=(e,t)=>{let n=e[t].normal.split(eu)[1];return n&&n.endsWith("e")?["la",n]:["le",n]},ep=(e,t)=>{let n=e[t].normal.split(eu)[1];return n&&ec.test(n)&&!eh.test(n)?["du",n]:n&&n.endsWith("s")?["des",n]:["de",n]};let em=/^([0-9.]{1,4}[a-z]{0,2}) ?[-–—] ?([0-9]{1,4}[a-z]{0,2})$/i,ef=/^([0-9]{1,2}(:[0-9][0-9])?(am|pm)?) ?[-–—] ?([0-9]{1,2}(:[0-9][0-9])?(am|pm)?)$/i,eb=/^[0-9]{3}-[0-9]{4}$/;var ev=function(e,t){let n=e[t],r=n.text.match(em);return null!==r?!0===n.tags.has("PhoneNumber")||eb.test(n.text)?null:[r[1],"to",r[2]]:null!==(r=n.text.match(ef))?[r[1],"to",r[4]]:null};let ey=/^([+-]?[0-9][.,0-9]*)([a-z°²³µ/]+)$/;var ew=function(e,t,n){let r=n.model.one.numberSuffixes||{},a=e[t].text.match(ey);if(null!==a){let e=a[2].toLowerCase().trim();return r.hasOwnProperty(e)?null:[a[1],e]}return null};let ek=/'/,eP=/^[0-9][^-–—]*[-–—].*?[0-9]/,eA=function(e,t,n,r){let a=t.update();a.document=[e];let i=n+r;n>0&&(n-=1),e[i]&&(i+=1),a.ptrs=[[0,n,i]]},ex={t:(e,t)=>"ain't"===e[t].normal||"aint"===e[t].normal?null:[e[t].normal.replace(/n't/,""),"not"],d:(e,t)=>el(e,t)},eC={j:(e,t)=>ed(e,t),l:(e,t)=>eg(e,t),d:(e,t)=>ep(e,t)},ej=function(e,t,n,r){for(let a=0;a<e.length;a+=1){let i=e[a];if(i.word===t.normal)return i.out;if(null!==r&&r===i.after)return[n].concat(i.out);if(null!==n&&n===i.before&&r&&r.length>2)return i.out.concat(r)}return null},eN=function(e,t){let n=t.fromText(e.join(" "));return n.compute(["id","alias"]),n.docs[0]},eI=function(e,t){for(let n=t+1;n<5&&e[n];n+=1)if("been"===e[n].normal)return["there","has"];return["there","is"]},eD=function(e){let t=e.world,{model:n,methods:r}=e.world,a=r.one.setTag,{frozenLex:i}=n.one,o=n.one._multiCache||{};e.docs.forEach(e=>{for(let n=0;n<e.length;n+=1){let r=e[n],s=r.machine||r.normal;if(void 0!==o[s]&&e[n+1]){let r=n+o[s]-1;for(let o=r;o>n;o-=1){let r=e.slice(n,o+1),s=r.map(e=>e.machine||e.normal).join(" ");if(!0===i.hasOwnProperty(s)){a(r,i[s],t,!1,"1-frozen-multi-lexicon"),r.forEach(e=>e.frozen=!0);continue}}}if(void 0!==i[s]&&i.hasOwnProperty(s)){a([r],i[s],t,!1,"1-freeze-lexicon"),r.frozen=!0;continue}}})},eT=e=>"\x1b[34m"+e+"\x1b[0m",eO=e=>"\x1b[3m\x1b[2m"+e+"\x1b[0m";var eH=function(e){e.docs.forEach(e=>{console.log(eT("\n  ┌─────────")),e.forEach(e=>{let t=`  ${eO("│")}  `,n=e.implicit||e.text||"-";!0===e.frozen?t+=`${eT(n)} ❄️`:t+=eO(n),console.log(t)})})},eE=function(e,t,n){let{model:r,methods:a}=n,i=a.one.setTag,o=r.one._multiCache||{},{lexicon:s}=r.one||{},l=e[t],u=l.machine||l.normal;if(void 0!==o[u]&&e[t+1]){let r=t+o[u]-1;for(let a=r;a>t;a-=1){let r=e.slice(t,a+1);if(r.length<=1)break;let o=r.map(e=>e.machine||e.normal).join(" ");if(!0===s.hasOwnProperty(o)){let e=s[o];return i(r,e,n,!1,"1-multi-lexicon"),e&&2===e.length&&("PhrasalVerb"===e[0]||"PhrasalVerb"===e[1])&&i([r[1]],"Particle",n,!1,"1-phrasal-particle"),!0}}return!1}return null};let eF=/^(under|over|mis|re|un|dis|semi|pre|post)-?/,eG=new Set(["Verb","Infinitive","PastTense","Gerund","PresentTense","Adjective","Participle"]);var eV=function(e,t,n){let{model:r,methods:a}=n,i=a.one.setTag,{lexicon:o}=r.one,s=e[t],l=s.machine||s.normal;if(void 0!==o[l]&&o.hasOwnProperty(l))return i([s],o[l],n,!1,"1-lexicon"),!0;if(s.alias){let e=s.alias.find(e=>o.hasOwnProperty(e));if(e)return i([s],o[e],n,!1,"1-lexicon-alias"),!0}if(!0===eF.test(l)){let e=l.replace(eF,"");if(o.hasOwnProperty(e)&&e.length>3&&eG.has(o[e]))return i([s],o[e],n,!1,"1-lexicon-prefix"),!0}return null};let ez=function(e,t){let{methods:n,model:r}=t;return n.one.tokenize.splitTerms(e,r).map(e=>n.one.tokenize.splitWhitespace(e,r)).map(e=>e.text.toLowerCase())};var eM=function(e,t){let n=[{}],r=[null],a=[0],i=[],o=0;for(let s in e.forEach(function(e){let a=0,i=ez(e,t);for(let e=0;e<i.length;e++){let t=i[e];n[a]&&n[a].hasOwnProperty(t)?a=n[a][t]:(o++,n[a][t]=o,n[o]={},a=o,r[o]=null)}r[a]=[i.length]}),n[0])a[o=n[0][s]]=0,i.push(o);for(;i.length;){let e=i.shift(),t=Object.keys(n[e]);for(let s=0;s<t.length;s+=1){let l=t[s],u=n[e][l];for(i.push(u),o=a[e];o>0&&!n[o].hasOwnProperty(l);)o=a[o];if(n.hasOwnProperty(o)){let e=n[o][l];a[u]=e,r[e]&&(r[u]=r[u]||[],r[u]=r[u].concat(r[e]))}else a[u]=0}}return{goNext:n,endAs:r,failTo:a}};let eB=function(e,t,n){let r=0,a=[];for(let i=0;i<e.length;i++){let o=e[i][n.form]||e[i].normal;for(;r>0&&(void 0===t.goNext[r]||!t.goNext[r].hasOwnProperty(o));)r=t.failTo[r]||0;if(t.goNext[r].hasOwnProperty(o)&&(r=t.goNext[r][o],t.endAs[r])){let n=t.endAs[r];for(let t=0;t<n.length;t++){let r=n[t],o=e[i-r+1],[s,l]=o.index;a.push([s,l,l+r,o.id])}}}return a},eS=function(e,t){for(let n=0;n<e.length;n+=1)if(!0===t.has(e[n]))return!1;return!0};var e$=function(e,t,n){let r=[];n.form=n.form||"normal";let a=e.docs;if(!t.goNext||!t.goNext[0])return console.error("Compromise invalid lookup trie"),e.none();let i=Object.keys(t.goNext[0]);for(let o=0;o<a.length;o++){if(e._cache&&e._cache[o]&&!0===eS(i,e._cache[o]))continue;let s=eB(a[o],t,n);s.length>0&&(r=r.concat(s))}return e.update(r)};let eL=e=>"[object Object]"===Object.prototype.toString.call(e),eK=(e,t)=>{for(let n=e.length-1;n>=0;n-=1)if(e[n]!==t){e=e.slice(0,n+1);break}return e},eJ={buildTrie:function(e){var t;return(t=eM(e,this.world())).goNext=t.goNext.map(e=>{if(0!==Object.keys(e).length)return e}),t.goNext=eK(t.goNext,void 0),t.failTo=eK(t.failTo,0),t.endAs=eK(t.endAs,null),t}};eJ.compile=eJ.buildTrie;let eW=function(e,t){return t&&e.forEach(e=>{let n=e[0];t[n]&&(e[0]=t[n][0],e[1]+=t[n][1],e[2]+=t[n][1])}),e},eq=function(e,t){let{ptrs:n,byGroup:r}=e;return n=eW(n,t),Object.keys(r).forEach(e=>{r[e]=eW(r[e],t)}),{ptrs:n,byGroup:r}},eR=function(e,t,n){let r=n.methods.one;return"number"==typeof e&&(e=String(e)),"string"==typeof e&&(e=r.killUnicode(e,n),e=r.parseMatch(e,t,n)),e},eU=e=>"[object Object]"===Object.prototype.toString.call(e),e_=e=>e&&eU(e)&&!0===e.isView,eQ=e=>e&&eU(e)&&!0===e.isNet,eZ=function(e,t){return[e[0],e[1],t[2]]},eY=(e,t,n)=>"string"==typeof e||"[object Array]"===Object.prototype.toString.call(e)?t.match(e,n):e||t.none(),eX=function(e,t){let[n,r,a]=e;return t.document[n]&&t.document[n][r]&&(e[3]=e[3]||t.document[n][r].id,t.document[n][a-1]&&(e[4]=e[4]||t.document[n][a-1].id)),e},e0={};e0.splitOn=function(e,t){let{splitAll:n}=this.methods.one.pointer,r=eY(e,this,t).fullPointer,a=n(this.fullPointer,r),i=[];return a.forEach(e=>{i.push(e.passthrough),i.push(e.before),i.push(e.match),i.push(e.after)}),i=(i=i.filter(e=>e)).map(e=>eX(e,this)),this.update(i)},e0.splitBefore=function(e,t){let{splitAll:n}=this.methods.one.pointer,r=eY(e,this,t).fullPointer,a=n(this.fullPointer,r);for(let e=0;e<a.length;e+=1)!a[e].after&&a[e+1]&&a[e+1].before&&a[e].match&&a[e].match[0]===a[e+1].before[0]&&(a[e].after=a[e+1].before,delete a[e+1].before);let i=[];return a.forEach(e=>{i.push(e.passthrough),i.push(e.before),e.match&&e.after?i.push(eZ(e.match,e.after)):i.push(e.match)}),i=(i=i.filter(e=>e)).map(e=>eX(e,this)),this.update(i)},e0.splitAfter=function(e,t){let{splitAll:n}=this.methods.one.pointer,r=eY(e,this,t).fullPointer,a=n(this.fullPointer,r),i=[];return a.forEach(e=>{i.push(e.passthrough),e.before&&e.match?i.push(eZ(e.before,e.match)):(i.push(e.before),i.push(e.match)),i.push(e.after)}),i=(i=i.filter(e=>e)).map(e=>eX(e,this)),this.update(i)},e0.split=e0.splitAfter;let e1=function(e,t,n){let r=e.world,a=r.methods.one.parseMatch;n=n||"^.";let i=a(t=t||".$",{},r),o=a(n,{},r);i[i.length-1].end=!0,o[0].start=!0;let s=e.fullPointer,l=[s[0]];for(let t=1;t<s.length;t+=1){let n=l[l.length-1],r=s[t],a=e.update([n]),u=e.update([r]);n&&r&&n[0]===r[0]&&n[2]===r[1]&&a.has(i)&&u.has(o)?l[l.length-1]=[n[0],n[1],r[2],n[3],r[4]]:l.push(r)}return e.update(l)},e2=Object.assign({},{matchOne:function(e,t,n){let r=this.methods.one;if(e_(e))return this.intersection(e).eq(0);if(eQ(e))return this.sweep(e,{tagger:!1,matchOne:!0}).view;let a={regs:e=eR(e,n,this.world),group:t,justOne:!0},{ptrs:i,byGroup:o}=eq(r.match(this.docs,a,this._cache),this.fullPointer),s=this.toView(i);return s._groups=o,s},match:function(e,t,n){let r=this.methods.one;if(e_(e))return this.intersection(e);if(eQ(e))return this.sweep(e,{tagger:!1}).view.settle();let a={regs:e=eR(e,n,this.world),group:t},{ptrs:i,byGroup:o}=eq(r.match(this.docs,a,this._cache),this.fullPointer),s=this.toView(i);return s._groups=o,s},has:function(e,t,n){let r=this.methods.one;if(e_(e))return this.intersection(e).fullPointer.length>0;if(eQ(e))return this.sweep(e,{tagger:!1}).view.found;let a={regs:e=eR(e,n,this.world),group:t,justOne:!0};return r.match(this.docs,a,this._cache).ptrs.length>0},if:function(e,t,n){let r=this.methods.one;if(e_(e))return this.filter(t=>t.intersection(e).found);if(eQ(e)){let t=this.sweep(e,{tagger:!1}).view.settle();return this.if(t)}let a={regs:e=eR(e,n,this.world),group:t,justOne:!0},i=this.fullPointer,o=this._cache||[];i=i.filter((e,t)=>{let n=this.update([e]);return r.match(n.docs,a,o[t]).ptrs.length>0});let s=this.update(i);return this._cache&&(s._cache=i.map(e=>o[e[0]])),s},ifNo:function(e,t,n){let{methods:r}=this,a=r.one;if(e_(e))return this.filter(t=>!t.intersection(e).found);if(eQ(e)){let t=this.sweep(e,{tagger:!1}).view.settle();return this.ifNo(t)}e=eR(e,n,this.world);let i=this._cache||[],o=this.filter((n,r)=>{let o={regs:e,group:t,justOne:!0};return 0===a.match(n.docs,o,i[r]).ptrs.length});return this._cache&&(o._cache=o.ptrs.map(e=>i[e[0]])),o}},{before:function(e,t,n){let{indexN:r}=this.methods.one.pointer,a=[],i=r(this.fullPointer);Object.keys(i).forEach(e=>{let t=i[e].sort((e,t)=>e[1]>t[1]?1:-1)[0];t[1]>0&&a.push([t[0],0,t[1]])});let o=this.toView(a);return e?o.match(e,t,n):o},after:function(e,t,n){let{indexN:r}=this.methods.one.pointer,a=[],i=r(this.fullPointer),o=this.document;Object.keys(i).forEach(e=>{let[t,,n]=i[e].sort((e,t)=>e[1]>t[1]?-1:1)[0];n<o[t].length&&a.push([t,n,o[t].length])});let s=this.toView(a);return e?s.match(e,t,n):s},growLeft:function(e,t,n){"string"==typeof e&&(e=this.world.methods.one.parseMatch(e,n,this.world)),e[e.length-1].end=!0;let r=this.fullPointer;return this.forEach((n,a)=>{let i=n.before(e,t);if(i.found){let e=i.terms();r[a][1]-=e.length,r[a][3]=e.docs[0][0].id}}),this.update(r)},growRight:function(e,t,n){"string"==typeof e&&(e=this.world.methods.one.parseMatch(e,n,this.world)),e[0].start=!0;let r=this.fullPointer;return this.forEach((n,a)=>{let i=n.after(e,t);if(i.found){let e=i.terms();r[a][2]+=e.length,r[a][4]=null}}),this.update(r)},grow:function(e,t,n){return this.growRight(e,t,n).growLeft(e,t,n)}},e0,{joinIf:function(e,t){return e1(this,e,t)},join:function(){return e1(this)}});e2.lookBehind=e2.before,e2.lookBefore=e2.before,e2.lookAhead=e2.after,e2.lookAfter=e2.after,e2.notIf=e2.ifNo;let e3=/(?:^|\s)([![^]*(?:<[^<]*>)?\/.*?[^\\/]\/[?\]+*$~]*)(?:\s|$)/,e5=/([!~[^]*(?:<[^<]*>)?\([^)]+[^\\)]\)[?\]+*$~]*)(?:\s|$)/,e4=/ /g,e6=e=>/^[![^]*(<[^<]*>)?\(/.test(e)&&/\)[?\]+*$~]*$/.test(e),e9=e=>/^[![^]*(<[^<]*>)?\//.test(e)&&/\/[?\]+*$~]*$/.test(e),e7=function(e){return e=(e=e.map(e=>e.trim())).filter(e=>e)};var e8=function(e){let t=e.split(e3),n=[];t.forEach(e=>{if(e9(e)){n.push(e);return}n=n.concat(e.split(e5))});let r=[];return(n=e7(n)).forEach(e=>{e6(e)?r.push(e):e9(e)?r.push(e):r=r.concat(e.split(e4))}),r=e7(r)};let te=/\{([0-9]+)?(, *[0-9]*)?\}/,tt=/&&/,tn=new RegExp(/^<\s*(\S+)\s*>/),tr=e=>e.charAt(0).toUpperCase()+e.substring(1),ta=e=>e.charAt(e.length-1),ti=e=>e.charAt(0),to=e=>e.substring(1),ts=e=>e.substring(0,e.length-1),tl=function(e){return e=ts(e=to(e))},tu=function(e,t){let n={};for(let r=0;r<2;r+=1){if("$"===ta(e)&&(n.end=!0,e=ts(e)),"^"===ti(e)&&(n.start=!0,e=to(e)),"?"===ta(e)&&(n.optional=!0,e=ts(e)),("["===ti(e)||"]"===ta(e))&&(n.group=null,"["===ti(e)&&(n.groupStart=!0),"]"===ta(e)&&(n.groupEnd=!0),"<"===ti(e=(e=e.replace(/^\[/,"")).replace(/\]$/,"")))){let t=tn.exec(e);t.length>=2&&(n.group=t[1],e=e.replace(t[0],""))}if("+"===ta(e)&&(n.greedy=!0,e=ts(e)),"*"!==e&&"*"===ta(e)&&"\\*"!==e&&(n.greedy=!0,e=ts(e)),"!"===ti(e)&&(n.negative=!0,e=to(e)),"~"===ti(e)&&"~"===ta(e)&&e.length>2&&(e=tl(e),n.fuzzy=!0,n.min=t.fuzzy||.85,!1===/\(/.test(e)))return n.word=e,n;if("/"===ti(e)&&"/"===ta(e))return e=tl(e),t.caseSensitive&&(n.use="text"),n.regex=new RegExp(e),n;if(!0===te.test(e)&&(e=e.replace(te,(e,t,r)=>(void 0===r?(n.min=Number(t),n.max=Number(t)):(r=r.replace(/, */,""),void 0===t?(n.min=0,n.max=Number(r)):(n.min=Number(t),n.max=Number(r||999))),n.greedy=!0,n.min||(n.optional=!0),""))),"("===ti(e)&&")"===ta(e)){tt.test(e)?(n.choices=e.split(tt),n.operator="and"):(n.choices=e.split("|"),n.operator="or"),n.choices[0]=to(n.choices[0]);let r=n.choices.length-1;n.choices[r]=ts(n.choices[r]),n.choices=n.choices.map(e=>e.trim()),n.choices=n.choices.filter(e=>e),n.choices=n.choices.map(e=>e.split(/ /g).map(e=>tu(e,t))),e=""}if("{"===ti(e)&&"}"===ta(e)){if(e=tl(e),n.root=e,/\//.test(e)){let e=n.root.split(/\//);n.root=e[0],n.pos=e[1],"adj"===n.pos&&(n.pos="Adjective"),n.pos=n.pos.charAt(0).toUpperCase()+n.pos.substr(1).toLowerCase(),void 0!==e[2]&&(n.sense=e[2])}return n}if("<"===ti(e)&&">"===ta(e))return e=tl(e),n.chunk=tr(e),n.greedy=!0,n;if("%"===ti(e)&&"%"===ta(e))return e=tl(e),n.switch=e,n}return"#"===ti(e)?(n.tag=to(e),n.tag=tr(n.tag)):"@"===ti(e)?n.method=to(e):"."===e?n.anything=!0:"*"===e?(n.anything=!0,n.greedy=!0,n.optional=!0):e&&(e=(e=e.replace("\\*","*")).replace("\\.","."),t.caseSensitive?n.use="text":e=e.toLowerCase(),n.word=e),n},tc=/[a-z0-9][-–—][a-z]/i;var th=function(e,t){let n=t.model.one.prefixes;for(let t=e.length-1;t>=0;t-=1){let r=e[t];if(r.word&&tc.test(r.word)){let a=r.word.split(/[-–—]/g);if(n.hasOwnProperty(a[0]))continue;a=a.filter(e=>e).reverse(),e.splice(t,1),a.forEach(n=>{let a=Object.assign({},r);a.word=n,e.splice(t,0,a)})}}return e};let td=function(e,t){let{all:n}=t.methods.two.transform.verb||{},r=e.root;return n?n(r,t.model):[]},tg=function(e,t){let{all:n}=t.methods.two.transform.noun||{};return n?n(e.root,t.model):[e.root]},tp=function(e,t){let{all:n}=t.methods.two.transform.adjective||{};return n?n(e.root,t.model):[e.root]},tm=function(e){let t=0,n=null;for(let r=0;r<e.length;r++){let a=e[r];!0===a.groupStart&&null===(n=a.group)&&(n=String(t),t+=1),null!==n&&(a.group=n),!0===a.groupEnd&&(n=null)}return e},tf=function(e,t){for(let n of t)if(e.has(n))return!0;return!1};var tb=function(e,t){for(let n=0;n<e.length;n+=1){let r=e[n];if(!0!==r.optional&&!0!==r.negative&&!0!==r.fuzzy){if(void 0!==r.word&&!1===t.has(r.word)||void 0!==r.tag&&!1===t.has("#"+r.tag))return!0;if(r.fastOr&&!1===tf(r.fastOr,t))break}}return!1};let tv=function(e,t){let n,r,a,i,o,s,l=e.length,u=t.length;if(0===l)return u;if(0===u)return l;let c=(u>l?u:l)+1;if(Math.abs(l-u)>(c||100))return c||100;let h=[];for(let e=0;e<c;e++)h[e]=[e],h[e].length=c;for(let e=0;e<c;e++)h[0][e]=e;for(let c=1;c<=l;++c)for(n=1,r=e[c-1];n<=u;++n){if(c===n&&h[c][n]>4)return l;i=r===(a=t[n-1])?0:1,o=h[c-1][n]+1,(s=h[c][n-1]+1)<o&&(o=s),(s=h[c-1][n-1]+i)<o&&(o=s),c>1&&n>1&&r===t[n-2]&&e[c-2]===a&&(s=h[c-2][n-2]+i)<o?h[c][n]=s:h[c][n]=o}return h[l][u]};var ty=function(e,t,n=3){if(e===t)return 1;if(e.length<n||t.length<n)return 0;let r=tv(e,t),a=Math.max(e.length,t.length);return 1-(0===a?0:r/a)};let tw=/([\u0022\uFF02\u0027\u201C\u2018\u201F\u201B\u201E\u2E42\u201A\u00AB\u2039\u2035\u2036\u2037\u301D\u0060\u301F])/,tk=/([\u0022\uFF02\u0027\u201D\u2019\u00BB\u203A\u2032\u2033\u2034\u301E\u00B4])/,tP=/^[-–—]$/,tA=/ [-–—]{1,3} /,tx=(e,t)=>-1!==e.post.indexOf(t),tC={hasQuote:e=>tw.test(e.pre)||tk.test(e.post),hasComma:e=>tx(e,","),hasPeriod:e=>!0===tx(e,".")&&!1===tx(e,"..."),hasExclamation:e=>tx(e,"!"),hasQuestionMark:e=>tx(e,"?")||tx(e,"\xbf"),hasEllipses:e=>tx(e,"..")||tx(e,"…"),hasSemicolon:e=>tx(e,";"),hasColon:e=>tx(e,":"),hasSlash:e=>/\//.test(e.text),hasHyphen:e=>tP.test(e.post)||tP.test(e.pre),hasDash:e=>tA.test(e.post)||tA.test(e.pre),hasContraction:e=>!!e.implicit,isAcronym:e=>e.tags.has("Acronym"),isKnown:e=>e.tags.size>0,isTitleCase:e=>/^\p{Lu}[a-z'\u00C0-\u00FF]/u.test(e.text),isUpperCase:e=>/^\p{Lu}+$/u.test(e.text)};tC.hasQuotation=tC.hasQuote;let tj=function(){},tN=function(e,t,n,r){if(!0===t.anything)return!0;if(!0===t.start&&0!==n||!0===t.end&&n!==r-1)return!1;if(void 0!==t.id&&t.id===e.id)return!0;if(void 0!==t.word)return t.use?t.word===e[t.use]:!!(null!==e.machine&&e.machine===t.word||void 0!==e.alias&&e.alias.hasOwnProperty(t.word)||!0===t.fuzzy&&(t.word===e.root||ty(t.word,e.normal)>=t.min)||e.alias&&e.alias.some(e=>e===t.word))||t.word===e.text||t.word===e.normal;if(void 0!==t.tag)return!0===e.tags.has(t.tag);if(void 0!==t.method)return"function"==typeof tC[t.method]&&!0===tC[t.method](e);if(void 0!==t.pre)return e.pre&&e.pre.includes(t.pre);if(void 0!==t.post)return e.post&&e.post.includes(t.post);if(void 0!==t.regex){let n=e.normal;return t.use&&(n=e[t.use]),t.regex.test(n)}if(void 0!==t.chunk)return e.chunk===t.chunk;if(void 0!==t.switch)return e.switch===t.switch;if(void 0!==t.machine)return e.normal===t.machine||e.machine===t.machine||e.root===t.machine;if(void 0!==t.sense)return e.sense===t.sense;if(void 0!==t.fastOr){if(t.pos&&!e.tags.has(t.pos))return null;let n=e.root||e.implicit||e.machine||e.normal;return t.fastOr.has(n)||t.fastOr.has(e.text)}return void 0!==t.choices&&("and"===t.operator?t.choices.every(t=>tj(e,t,n,r)):t.choices.some(t=>tj(e,t,n,r)))};var tI=tj=function(e,t,n,r){let a=tN(e,t,n,r);return!0===t.negative?!a:a};let tD=function(e,t){let n=Object.assign({},e.regs[e.r],{start:!1,end:!1}),r=e.t;for(;e.t<e.terms.length&&!(t&&tI(e.terms[e.t],t,e.start_i+e.t,e.phrase_length));e.t+=1){let t=e.t-r+1;if(void 0!==n.max&&t===n.max)break;if(!1===tI(e.terms[e.t],n,e.start_i+e.t,e.phrase_length)){if(void 0!==n.min&&t<n.min)return null;break}}return e.t},tT=function(e,t){let n=e.t;if(!t)return e.terms.length;for(;n<e.terms.length;n+=1)if(!0===tI(e.terms[n],t,e.start_i+n,e.phrase_length))return n;return null},tO=function(e,t){if(!0===e.end&&!0===e.greedy&&t.start_i+t.t<t.phrase_length-1){let n=Object.assign({},e,{end:!1});if(!0===tI(t.terms[t.t],n,t.start_i+t.t,t.phrase_length))return!0}return!1},tH=function(e,t){return e.groups[e.inGroup]||(e.groups[e.inGroup]={start:t,length:0}),e.groups[e.inGroup]};var tE=function(e){let{regs:t}=e,n=t[e.r],r=tT(e,t[e.r+1]);return null===r||0===r||void 0!==n.min&&r-e.t<n.min?null:(void 0!==n.max&&r-e.t>n.max?e.t=e.t+n.max:(!0===e.hasGroup&&(tH(e,e.t).length=r-e.t),e.t=r),!0)};let tF=function(e,t=0){let n=e.regs[e.r],r=!1;for(let a=0;a<n.choices.length;a+=1){let i=n.choices[a];if("[object Array]"!==Object.prototype.toString.call(i))return!1;if(r=i.every((n,r)=>{let a=0,i=e.t+r+t+a;if(void 0===e.terms[i])return!1;let o=tI(e.terms[i],n,i+e.start_i,e.phrase_length);if(!0===o&&!0===n.greedy)for(let t=1;t<e.terms.length;t+=1){let r=e.terms[i+t];if(r){if(!0===tI(r,n,e.start_i+t,e.phrase_length))a+=1;else break}}return t+=a,o})){t+=i.length;break}}return r&&!0===n.greedy?tF(e,t):t},tG=function(e){let t=0;return!0===e.regs[e.r].choices.every(n=>{let r=n.every((t,n)=>{let r=e.t+n;return void 0!==e.terms[r]&&tI(e.terms[r],t,r,e.phrase_length)});return!0===r&&n.length>t&&(t=n.length),r})&&t};var tV=function(e){let{regs:t}=e,n=t[e.r],r=tF(e);if(r){if(!0===n.negative)return null;if(!0===e.hasGroup){let t=tH(e,e.t);t.length+=r}if(!0===n.end){let t=e.phrase_length;if(e.t+e.start_i+r!==t)return null}e.t+=r}else if(!n.optional)return null;return!0},tz=function(e){let{regs:t}=e,n=t[e.r],r=tG(e);if(r){if(!0===n.negative)return null;if(!0===e.hasGroup){let t=tH(e,e.t);t.length+=r}if(!0===n.end){let t=e.phrase_length-1;if(e.t+e.start_i!==t)return null}e.t+=r}else if(!n.optional)return null;return!0},tM=function(e,t,n){let r=0;for(let a=e.t;a<e.terms.length;a+=1){let i=tI(e.terms[a],t,e.start_i+e.t,e.phrase_length);if(i||n&&(i=tI(e.terms[a],n,e.start_i+e.t,e.phrase_length))||(r+=1,void 0!==t.max&&r===t.max))break}return 0!==r&&(!t.min||!(t.min>r))&&(e.t+=r,!0)},tB=function(e){let{regs:t}=e,n=t[e.r],r=Object.assign({},n);if(r.negative=!1,tI(e.terms[e.t],r,e.start_i+e.t,e.phrase_length))return!1;if(n.optional){let n=t[e.r+1];n&&(tI(e.terms[e.t],n,e.start_i+e.t,e.phrase_length)?e.r+=1:n.optional&&t[e.r+2]&&tI(e.terms[e.t],t[e.r+2],e.start_i+e.t,e.phrase_length)&&(e.r+=2))}return n.greedy?tM(e,r,t[e.r+1]):(e.t+=1,!0)},tS=function(e){let{regs:t}=e,n=t[e.r],r=tI(e.terms[e.t],t[e.r+1],e.start_i+e.t,e.phrase_length);if(n.negative||r){let n=e.terms[e.t+1];n&&tI(n,t[e.r+1],e.start_i+e.t,e.phrase_length)||(e.r+=1)}},t$=function(e){let{regs:t,phrase_length:n}=e,r=t[e.r];return e.t=tD(e,t[e.r+1]),null!==e.t&&(!r.min||!(r.min>e.t))&&(!0!==r.end||e.start_i+e.t===n)||null},tL=function(e){let t=e.terms[e.t],n=e.regs[e.r];if(t.implicit&&e.terms[e.t+1]){if(!e.terms[e.t+1].implicit)return;n.word===t.normal&&(e.t+=1),"hasContraction"===n.method&&(e.t+=1)}};let tK=function(e,t){let n=e.regs[e.r],r=tH(e,t);e.t>1&&n.greedy?r.length+=e.t-t:r.length++};var tJ=function(e){let{regs:t}=e,n=t[e.r],r=e.terms[e.t],a=e.t;return!!n.optional&&!!t[e.r+1]&&!!n.negative||((n.optional&&t[e.r+1]&&tS(e),r.implicit&&e.terms[e.t+1]&&tL(e),e.t+=1,(!0!==n.end||e.t===e.terms.length||!0===n.greedy)&&(!0!==n.greedy||t$(e)))?(!0===e.hasGroup&&tK(e,a),!0):null)},tW=function(e,t,n,r){if(0===e.length||0===t.length)return null;let a={t:0,terms:e,r:0,regs:t,groups:{},start_i:n,phrase_length:r,inGroup:null};for(;a.r<t.length;a.r+=1){let e=t[a.r];if(a.hasGroup=!!e.group,!0===a.hasGroup?a.inGroup=e.group:a.inGroup=null,!a.terms[a.t]){if(!1===t.slice(a.r).some(e=>!e.optional))break;return null}if(!0===e.anything&&!0===e.greedy){if(!tE(a))return null;continue}if(void 0!==e.choices&&"or"===e.operator){if(!tV(a))return null;continue}if(void 0!==e.choices&&"and"===e.operator){if(!tz(a))return null;continue}if(!0===e.anything){if(e.negative&&e.anything||!tJ(a))return null;continue}if(!0===tO(e,a)){if(!tJ(a))return null;continue}if(e.negative){if(!tB(a))return null;continue}if(!0===tI(a.terms[a.t],e,a.start_i+a.t,a.phrase_length)){if(!tJ(a))return null;continue}if(!0!==e.optional)return null}let i=[null,n,a.t+n];if(i[1]===i[2])return null;let o={};return Object.keys(a.groups).forEach(e=>{let t=a.groups[e],r=n+t.start;o[e]=[null,r,r+t.length]}),{pointer:i,groups:o}},tq=function(e,t){let n=[],r={};return 0===e.length||("number"==typeof t&&(t=String(t)),t?e.forEach(e=>{e.groups[t]&&n.push(e.groups[t])}):e.forEach(e=>{n.push(e.pointer),Object.keys(e.groups).forEach(t=>{r[t]=r[t]||[],r[t].push(e.groups[t])})})),{ptrs:n,byGroup:r}};let tR=function(e,t){return e.pointer[0]=t,Object.keys(e.groups).forEach(n=>{e.groups[n][0]=t}),e},tU=function(e,t,n){let r=tW(e,t,0,e.length);return r?r=tR(r,n):null},t_=/^\../,tQ=/^#./,tZ=e=>e=(e=(e=(e=(e=e.replace(/&/g,"&amp;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;")).replace(/"/g,"&quot;")).replace(/'/g,"&apos;"),tY=function(e){let t="",n="</span>";return e=tZ(e),t_.test(e)?t=`<span class="${e.replace(/^\./,"")}"`:tQ.test(e)?t=`<span id="${e.replace(/^#/,"")}"`:(t=`<${e}`,n=`</${e}>`),{start:t+=">",end:n}},tX=function(e,t){let n={},r={};return Object.keys(t).forEach(a=>{let i=t[a],o=tY(a);"string"==typeof i&&(i=e.match(i)),i.docs.forEach(e=>{if(e.every(e=>e.implicit))return;let t=e[0].id;n[t]=n[t]||[],n[t].push(o.start);let a=e[e.length-1].id;r[a]=r[a]||[],r[a].push(o.end)})}),{starts:n,ends:r}},t0=/[,:;)\]*.?~!\u0022\uFF02\u201D\u2019\u00BB\u203A\u2032\u2033\u2034\u301E\u00B4—-]+$/,t1=/^[(['"*~\uFF02\u201C\u2018\u201F\u201B\u201E\u2E42\u201A\u00AB\u2039\u2035\u2036\u2037\u301D\u0060\u301F]+/,t2=/[,:;)('"\u201D\]]/,t3=/^[-–—]$/,t5=/ /,t4=function(e,t,n=!0){let r="";return e.forEach(e=>{let n=e.pre||"",a=e.post||"";"some"===t.punctuation&&(n=n.replace(t1,""),t3.test(a)&&(a=" "),a=(a=(a=(a=(a=a.replace(t2,"")).replace(/\?!+/,"?")).replace(/!+/,"!")).replace(/\?+/,"?")).replace(/\.{2,}/,""),e.tags.has("Abbreviation")&&(a=a.replace(/\./,""))),"some"===t.whitespace&&(n=n.replace(/\s/,""),a=a.replace(/\s+/," ")),t.keepPunct||(n=n.replace(t1,""),a="-"===a?" ":a.replace(t0,""));let i=e[t.form||"text"]||e.normal||"";"implicit"===t.form&&(i=e.implicit||e.text),"root"===t.form&&e.implicit&&(i=e.root||e.implicit||e.normal),"machine"!==t.form&&"implicit"!==t.form&&"root"!==t.form||!e.implicit||a&&t5.test(a)||(a+=" "),r+=n+i+a}),!1===n&&(r=r.trim()),!0===t.lowerCase&&(r=r.toLowerCase()),r},t6=function(e,t){let n="";if(!e||!e[0]||!e[0][0])return n;for(let r=0;r<e.length;r+=1)n+=t4(e[r],t,!0);if(t.keepSpace||(n=n.trim()),!1===t.keepEndPunct){e[0][0].tags.has("Emoticon")||(n=n.replace(t1,""));let t=e[e.length-1];t[t.length-1].tags.has("Emoticon")||(n=n.replace(t0,"")),n.endsWith("'")&&!n.endsWith("s'")&&(n=n.replace(/'/,""))}return!0===t.cleanWhitespace&&(n=n.trim()),n},t9={text:{form:"text"},normal:{whitespace:"some",punctuation:"some",case:"some",unicode:"some",form:"normal"},machine:{keepSpace:!1,whitespace:"some",punctuation:"some",case:"none",unicode:"some",form:"machine"},root:{keepSpace:!1,whitespace:"some",punctuation:"some",case:"some",unicode:"some",form:"root"},implicit:{form:"implicit"}};t9.clean=t9.normal,t9.reduced=t9.root;let t7=[],t8=0;for(;t8<64;)t7[t8]=0|4294967296*Math.sin(++t8%Math.PI);var ne=function(e){let t,n,r,a=[t=1732584193,n=4023233417,~t,~n],i=[],o=decodeURI(encodeURI(e))+"\x80",s=o.length;for(e=--s/4+2|15,i[--e]=8*s;~s;)i[s>>2]|=o.charCodeAt(s)<<8*s--;for(t8=o=0;t8<e;t8+=16){for(s=a;o<64;s=[r=s[3],t+((r=s[0]+[t&n|~t&r,r&t|~r&n,t^n^r,n^(t|~r)][s=o>>4]+t7[o]+~~i[t8|15&[o,5*o+1,3*o+5,7*o][s]])<<(s=[7,12,17,22,5,9,14,20,4,11,16,23,6,10,15,21][4*s+o++%4])|r>>>-s),t,n])t=0|s[1],n=s[2];for(o=4;o;)a[--o]+=s[o]}for(e="";o<32;)e+=(a[o>>3]>>(1^o++)*4&15).toString(16);return e};let nt={text:!0,terms:!0},nn={case:"none",unicode:"some",form:"machine",punctuation:"some"},nr=function(e,t){return Object.assign({},e,t)},na={text:e=>t4(e,{keepPunct:!0},!1),normal:e=>t4(e,nr(t9.normal,{keepPunct:!0}),!1),implicit:e=>t4(e,nr(t9.implicit,{keepPunct:!0}),!1),machine:e=>t4(e,nn,!1),root:e=>t4(e,nr(nn,{form:"root"}),!1),hash:e=>ne(t4(e,{keepPunct:!0},!1)),offset:e=>{let t=na.text(e).length;return{index:e[0].offset.index,start:e[0].offset.start,length:t}},terms:e=>e.map(e=>{let t=Object.assign({},e);return t.tags=Array.from(e.tags),t}),confidence:(e,t,n)=>t.eq(n).confidence(),syllables:(e,t,n)=>t.eq(n).syllables(),sentence:(e,t,n)=>t.eq(n).fullSentence().text(),dirty:e=>e.some(e=>!0===e.dirty)};na.sentences=na.sentence,na.clean=na.normal,na.reduced=na.root;let ni={json:function(e){var t,n;let r=(t=this,"string"==typeof(n=(n=e)||{})&&(n={}),(n=Object.assign({},nt,n)).offset&&t.compute("offset"),t.docs.map((e,r)=>{let a={};return Object.keys(n).forEach(i=>{n[i]&&na[i]&&(a[i]=na[i](e,t,r))}),a}));return"number"==typeof e?r[e]:r}};ni.data=ni.json;let no=()=>"undefined"!=typeof window&&window.document,ns=function(e){let t=e.pre||"",n=e.post||"";return t+e.text+n},nl=function(e,t){let n={};return Object.keys(t).forEach(r=>{e.match(r).fullPointer.forEach(e=>{n[e[3]]={fn:t[r],end:e[2]}})}),n};var nu=function(e,t){let n=nl(e,t),r="";return e.docs.forEach((t,a)=>{for(let i=0;i<t.length;i+=1){let o=t[i];if(n.hasOwnProperty(o.id)){let{fn:s,end:l}=n[o.id],u=e.update([[a,i,l]]);r+=(t[i].pre||"")+s(u)+(t[i=l-1].post||"")}else r+=ns(o)}}),r};let nc=e=>"[object Object]"===Object.prototype.toString.call(e),nh=function(e){let t={};return e.forEach(e=>{t[e]=t[e]||0,t[e]+=1}),Object.keys(t).map(e=>({normal:e,count:t[e]})).sort((e,t)=>e.count>t.count?-1:0)},nd=e=>"[object Object]"===Object.prototype.toString.call(e),ng=Object.assign({},{debug:function(e){let t=this.methods.one.debug||{};return e&&t.hasOwnProperty(e)?t[e](this):no()?t.clientSide(this):t.tags(this),this},out:function(e){if(nc(e))return nu(this,e);if("text"===e)return this.text();if("normal"===e)return this.text("normal");if("root"===e)return this.text("root");if("machine"===e||"reduced"===e)return this.text("machine");if("hash"===e||"md5"===e)return ne(this.text());if("json"===e)return this.json();if("offset"===e||"offsets"===e)return this.compute("offset"),this.json({offset:!0});if("array"===e)return this.docs.map(e=>e.reduce((e,t)=>e+t.pre+t.text+t.post,"").trim()).filter(e=>e);if("freq"===e||"frequency"===e||"topk"===e)return nh(this.json({normal:!0}).map(e=>e.normal));if("terms"===e){let e=[];return this.docs.forEach(t=>{let n=t.map(e=>e.text);n=n.filter(e=>e),e=e.concat(n)}),e}return"tags"===e?this.docs.map(e=>e.reduce((e,t)=>(e[t.implicit||t.normal]=Array.from(t.tags),e),{})):"debug"===e?this.debug():this.text()},wrap:function(e){return nu(this,e)}},{text:function(e){let t={};if(e&&"string"==typeof e&&t9.hasOwnProperty(e)?t=Object.assign({},t9[e]):e&&nd(e)&&(t=Object.assign({},e)),void 0!==t.keepSpace||this.isFull()||(t.keepSpace=!1),void 0===t.keepEndPunct&&this.pointer){let e=this.pointer[0];e&&e[1]?t.keepEndPunct=!1:t.keepEndPunct=!0}return void 0===t.keepPunct&&(t.keepPunct=!0),void 0===t.keepSpace&&(t.keepSpace=!0),t6(this.docs,t)}},ni,{html:function(e){let{starts:t,ends:n}=tX(this,e),r="";return this.docs.forEach(e=>{for(let a=0;a<e.length;a+=1){let i=e[a];t.hasOwnProperty(i.id)&&(r+=t[i.id].join("")),r+=(i.pre||"")+(i.text||""),n.hasOwnProperty(i.id)&&(r+=n[i.id].join("")),r+=i.post||""}}),r}}),np="\x1b[0m";var nm={green:e=>"\x1b[32m"+e+np,red:e=>"\x1b[31m"+e+np,blue:e=>"\x1b[34m"+e+np,magenta:e=>"\x1b[35m"+e+np,cyan:e=>"\x1b[36m"+e+np,yellow:e=>"\x1b[33m"+e+np,black:e=>"\x1b[30m"+e+np,dim:e=>"\x1b[2m"+e+np,i:e=>"\x1b[3m"+e+np};let nf=(e,t,n)=>{let r=t.start+9*n,a=r+t.length;return[e.substring(0,r),e.substring(r,a),e.substring(a,e.length)]},nb=function(e,t,n){let r=nf(e,t,n);return`${r[0]}${nm.blue(r[1])}${r[2]}`},nv=function(e,t){if(e[0]!==t[0])return!1;let[,n,r]=e,[,a,i]=t;return n<=a&&r>a||a<=n&&i>n},ny=function(e){let t=e[0][1],n=e[0][2];return e.forEach(e=>{e[1]<t&&(t=e[1]),e[2]>n&&(n=e[2])}),[e[0][0],t,n]},nw=function(e){let t={};return e.forEach(e=>{t[e[0]]=t[e[0]]||[],t[e[0]].push(e)}),t},nk=function(e){let t={};for(let n=0;n<e.length;n+=1)t[e[n].join(",")]=e[n];return Object.values(t)},nP=function(e,t){let[n,r]=e,a=t[1],i=t[2],o={};if(r<a){let t=a<e[2]?a:e[2];o.before=[n,r,t]}return o.match=t,e[2]>i&&(o.after=[n,i,e[2]]),o};var nA=function(e,t){let n=nw(t),r=[];return e.forEach(e=>{let[t]=e,a=n[t]||[];if(0===(a=a.filter(t=>e[1]<=t[1]&&t[2]<=e[2])).length){r.push({passthrough:e});return}a=a.sort((e,t)=>e[1]-t[1]);let i=e;a.forEach((e,t)=>{let n=nP(i,e);a[t+1]?(r.push({before:n.before,match:n.match}),n.after&&(i=n.after)):r.push(n)})}),r};let nx=function(e,t,n){for(let r=0;r<20;r+=1){if(t[n-r]){let a=t[n-r].findIndex(t=>t.id===e);if(-1!==a)return[n-r,a]}if(t[n+r]){let a=t[n+r].findIndex(t=>t.id===e);if(-1!==a)return[n+r,a]}}return null},nC=function(e,t){let[n,r,,,a]=e,i=t[n],o=i.findIndex(e=>e.id===a);return -1===o?(e[2]=t[n].length,e[4]=i.length?i[i.length-1].id:null):e[2]=o,t[n].slice(r,e[2]+1)};var nj=function(e,t){let n=e.concat(t),r=nw(n),a=[];return n.forEach(e=>{let[t]=e;if(1===r[t].length){a.push(e);return}let n=r[t].filter(t=>nv(e,t));n.push(e);let i=ny(n);a.push(i)}),a=nk(a)},nN=function(e,t){let n=[];return nA(e,t).forEach(e=>{e.passthrough&&n.push(e.passthrough),e.before&&n.push(e.before),e.after&&n.push(e.after)}),n};let nI=function(e,t){let n=e[1]<t[1]?t[1]:e[1],r=e[2]>t[2]?t[2]:e[2];return n<r?[e[0],n,r]:null};var nD=function(e,t){let n=nw(t),r=[];return e.forEach(e=>{let t=n[e[0]]||[];0!==(t=t.filter(t=>nv(e,t))).length&&t.forEach(t=>{let n=nI(e,t);n&&r.push(n)})}),r};let nT=(e,t)=>"string"==typeof e||"[object Array]"===Object.prototype.toString.call(e)?t.match(e):e||t.none(),nO=function(e,t){return e.map(e=>{let[n,r]=e;return t[n]&&t[n][r]&&(e[3]=t[n][r].id),e})},nH={};nH.union=function(e){e=nT(e,this);let t=nj(this.fullPointer,e.fullPointer);return t=nO(t,this.document),this.toView(t)},nH.and=nH.union,nH.intersection=function(e){e=nT(e,this);let t=nD(this.fullPointer,e.fullPointer);return t=nO(t,this.document),this.toView(t)},nH.not=function(e){e=nT(e,this);let t=nN(this.fullPointer,e.fullPointer);return t=nO(t,this.document),this.toView(t)},nH.difference=nH.not,nH.complement=function(){let e=nN(this.all().fullPointer,this.fullPointer);return e=nO(e,this.document),this.toView(e)},nH.settle=function(){let e=this.fullPointer;return e.forEach(t=>{e=nj(e,[t])}),e=nO(e,this.document),this.update(e)};let nE=function(e){return!0===e.optional||!0===e.negative?null:e.tag?"#"+e.tag:e.word?e.word:e.switch?`%${e.switch}%`:null},nF=function(e){let t=[];return e.forEach(e=>{t.push(nE(e)),"and"===e.operator&&e.choices&&e.choices.forEach(e=>{e.forEach(e=>{t.push(nE(e))})})}),t.filter(e=>e)},nG=function(e){let t=[],n=0;return e.forEach(e=>{"or"!==e.operator||e.optional||e.negative||(e.fastOr&&Array.from(e.fastOr).forEach(e=>{t.push(e)}),e.choices&&e.choices.forEach(e=>{e.forEach(e=>{let n=nE(e);n&&t.push(n)})}),n+=1)}),{wants:t,count:n}};var nV=function(e,t){let n=t.methods.one.parseMatch;return e.forEach(e=>{e.regs=n(e.match,{},t),"string"==typeof e.ifNo&&(e.ifNo=[e.ifNo]),e.notIf&&(e.notIf=n(e.notIf,{},t)),e.needs=nF(e.regs);let{wants:r,count:a}=nG(e.regs);e.wants=r,e.minWant=a,e.minWords=e.regs.filter(e=>!e.optional).length}),e},nz=function(e,t,n,r,a){let i=[];for(let n=0;n<e.length;n+=1)for(let o=0;o<e[n].length;o+=1){let s=e[n][o],l=r.one.match([t[n]],s);if(l.ptrs.length>0&&(l.ptrs.forEach(e=>{e[0]=n;let t=Object.assign({},s,{pointer:e});void 0!==s.unTag&&(t.unTag=s.unTag),i.push(t)}),!0===a.matchOne))return[i[0]]}return i},nM=function(e,t,n){let r=n.one.tagSet;if(!r.hasOwnProperty(t))return!0;let a=r[t].not||[];for(let t=0;t<e.length;t+=1){let n=e[t];for(let e=0;e<a.length;e+=1)if(!0===n.tags.has(a[e]))return!1}return!0},nB=n(20357),nS=n(20357);let n$=/ /,nL=function(e,t){"Noun"===t&&(e.chunk=t),"Verb"===t&&(e.chunk=t)},nK=function(e,t,n,r){if(!0===e.tags.has(t)||"."===t)return null;!0===e.frozen&&(r=!0);let a=n[t];if(a){if(a.not&&a.not.length>0)for(let t=0;t<a.not.length;t+=1){if(!0===r&&e.tags.has(a.not[t]))return null;e.tags.delete(a.not[t])}if(a.parents&&a.parents.length>0)for(let t=0;t<a.parents.length;t+=1)e.tags.add(a.parents[t]),nL(e,a.parents[t])}return e.tags.add(t),e.dirty=!0,nL(e,t),!0},nJ=function(e,t,n,r){let a=t.split(n$);e.forEach((e,t)=>{let i=a[t];i&&nK(e,i=i.replace(/^#/,""),n,r)})},nW=(e,t,n="")=>{let r=e.map(e=>e.text||"["+e.implicit+"]").join(" ");"string"!=typeof t&&t.length>2&&(t=t.slice(0,2).join(", #")+" +"),t="string"!=typeof t?t.join(", #"):t,console.log(` ${("\x1b[33m\x1b[3m"+r+"\x1b[0m").padEnd(24)} \x1b[32m→\x1b[0m #${t.padEnd(22)}  ${"\x1b[3m"+n+"\x1b[0m"}`)},nq=function(e,t,n={},r,a){var i;let o=n.model.one.tagSet||{};if(!t)return;let s=void 0!==nS&&nS.env?nS.env:self.env||{};if(s&&s.DEBUG_TAGS&&nW(e,t,a),!0==(i=t,"[object Array]"===Object.prototype.toString.call(i))){t.forEach(t=>nq(e,t,n,r));return}if("string"!=typeof t){console.warn(`compromise: Invalid tag '${t}'`);return}if(t=t.trim(),n$.test(t)){nJ(e,t,o,r);return}t=t.replace(/^#/,"");for(let n=0;n<e.length;n+=1)nK(e[n],t,o,r)},nR=function(e){return e.children=e.children||[],e._cache=e._cache||{},e.props=e.props||{},e._cache.parents=e._cache.parents||[],e._cache.children=e._cache.children||[],e},nU=/^ *(#|\/\/)/,n_=function(e){let t=e.trim().split(/->/),n=[];t.forEach(e=>{n=n.concat(function(e){if(!(e=e.trim()))return null;if(/^\[/.test(e)&&/\]$/.test(e)){let t=(e=(e=e.replace(/^\[/,"")).replace(/\]$/,"")).split(/,/);return(t=t.map(e=>e.trim()).filter(e=>e)).map(e=>nR({id:e}))}return[nR({id:e})]}(e))});let r=(n=n.filter(e=>e))[0];for(let e=1;e<n.length;e+=1)r.children.push(n[e]),r=n[e];return n[0]},nQ=(e,t)=>{let n=[],r=[e];for(;r.length>0;){let e=r.pop();n.push(e),e.children&&e.children.forEach(n=>{t&&t(e,n),r.push(n)})}return n},nZ=e=>"[object Array]"===Object.prototype.toString.call(e),nY=e=>(e=e||"").trim(),nX=function(e=[]){let t,n,r;return"string"==typeof e?(t=e.split(/\r?\n/),n=[],t.forEach(e=>{if(!e.trim()||nU.test(e))return;let t=(e=>{let t=/^( {2}|\t)/,n=0;for(;t.test(e);)e=e.replace(t,""),n+=1;return n})(e);n.push({indent:t,node:n_(e)})}),nR((r={children:[]},n.forEach((e,t)=>{0===e.indent?r.children=r.children.concat(e.node):n[t-1]&&(function(e,t){let n=e[t].indent;for(;t>=0;t-=1)if(e[t].indent<n)return e[t];return e[0]})(n,t).node.children.push(e.node)}),r))):nZ(e)?function(e){let t={};e.forEach(e=>{t[e.id]=e});let n=nR({});return e.forEach(e=>{if((e=nR(e)).parent){if(t.hasOwnProperty(e.parent)){let n=t[e.parent];delete e.parent,n.children.push(e)}else console.warn(`[Grad] - missing node '${e.parent}'`)}else n.children.push(e)}),n}(e):(nQ(e).forEach(nR),e)},n0=e=>"\x1b[31m"+e+"\x1b[0m",n1=e=>"\x1b[2m"+e+"\x1b[0m",n2=function(e,t){let n="-> ";t&&(n=n1("→ "));let r="";return nQ(e).forEach((e,a)=>{let i=e.id||"";if(t&&(i=n0(i)),0===a&&!e.id)return;let o=e._cache.parents.length;r+="    ".repeat(o)+n+i+"\n"}),r},n3=function(e){let t=nQ(e);t.forEach(e=>{delete(e=Object.assign({},e)).children});let n=t[0];return n&&!n.id&&0===Object.keys(n.props).length&&t.shift(),t},n5={text:n2,txt:n2,array:n3,flat:n3},n4=function(e,t){return"nested"===t||"json"===t?e:"debug"===t?(console.log(n2(e,!0)),null):n5.hasOwnProperty(t)?n5[t](e):e},n6=e=>{nQ(e,(e,t)=>{e.id&&(e._cache.parents=e._cache.parents||[],t._cache.parents=e._cache.parents.concat([e.id]))})},n9=(e,t)=>(Object.keys(t).forEach(n=>{if(t[n]instanceof Set){let r=e[n]||new Set;e[n]=new Set([...r,...t[n]])}else{let r;if((r=t[n])&&"object"==typeof r&&!Array.isArray(r)){let r=e[n]||{};e[n]=Object.assign({},t[n],r)}else nZ(t[n])?e[n]=t[n].concat(e[n]||[]):void 0===e[n]&&(e[n]=t[n])}}),e),n7=/\//;class n8{constructor(e={}){Object.defineProperty(this,"json",{enumerable:!1,value:e,writable:!0})}get children(){return this.json.children}get id(){return this.json.id}get found(){return this.json.id||this.json.children.length>0}props(e={}){let t=this.json.props||{};return"string"==typeof e&&(t[e]=!0),this.json.props=Object.assign(t,e),this}get(e){return new n8((e=nY(e),n7.test(e))?((e,t)=>{let n;let r="string"!=typeof(n=t=t||"")?n:(n=n.replace(/^\//,"")).split(/\//);for(let t=0;t<r.length;t+=1){let n=e.children.find(e=>e.id===r[t]);if(!n)return null;e=n}return e})(this.json,e)||nR({}):this.json.children.find(t=>t.id===e))}add(e,t={}){if(nZ(e))return e.forEach(e=>this.add(nY(e),t)),this;let n=nR({id:e=nY(e),props:t});return this.json.children.push(n),new n8(n)}remove(e){return e=nY(e),this.json.children=this.json.children.filter(t=>t.id!==e),this}nodes(){return nQ(this.json).map(e=>(delete(e=Object.assign({},e)).children,e))}cache(){var e;let t,n;return t=nQ(e=this.json,(e,t)=>{e.id&&(e._cache.parents=e._cache.parents||[],e._cache.children=e._cache.children||[],t._cache.parents=e._cache.parents.concat([e.id]))}),n={},t.forEach(e=>{e.id&&(n[e.id]=e)}),t.forEach(e=>{e._cache.parents.forEach(t=>{n.hasOwnProperty(t)&&n[t]._cache.children.push(e.id)})}),e._cache.children=Object.keys(n),this}list(){return nQ(this.json)}fillDown(){return nQ(this.json,(e,t)=>{t.props=n9(t.props,e.props)}),this}depth(){n6(this.json);let e=nQ(this.json),t=e.length>1?1:0;return e.forEach(e=>{if(0===e._cache.parents.length)return;let n=e._cache.parents.length+1;n>t&&(t=n)}),t}out(e){return n6(this.json),n4(this.json,e)}debug(){return n6(this.json),n4(this.json,"debug"),this}}let re=function(e){return new n8(nX(e))};re.prototype.plugin=function(e){e(this)};var rt={Noun:"blue",Verb:"green",Negative:"green",Date:"red",Value:"red",Adjective:"magenta",Preposition:"cyan",Conjunction:"cyan",Determiner:"cyan",Hyphenated:"cyan",Adverb:"cyan"},rn=function(e){let t={};return e.forEach(e=>{let{not:n,also:r,is:a,novel:i}=e.props,o=e._cache.parents;r&&(o=o.concat(r)),t[e.id]={is:a,not:n,novel:i,also:r,parents:o,children:e._cache.children,color:rt.hasOwnProperty(e.id)?rt[e.id]:rt.hasOwnProperty(e.is)?rt[e.is]:rt[e._cache.parents.find(e=>rt[e])]}}),Object.keys(t).forEach(e=>{let n=new Set(t[e].not);t[e].not.forEach(e=>{t[e]&&t[e].children.forEach(e=>n.add(e))}),t[e].not=Array.from(n)}),t};let rr=function(e){return e?"string"==typeof e?[e]:e:[]};var ra=function(e,t){var n;return Object.keys(n=e).forEach(e=>{n[e].isA&&(n[e].is=n[e].isA),n[e].notA&&(n[e].not=n[e].notA),!n[e].is||"string"!=typeof n[e].is||t.hasOwnProperty(n[e].is)||n.hasOwnProperty(n[e].is)||(n[n[e].is]={}),!n[e].not||"string"!=typeof n[e].not||n.hasOwnProperty(n[e].not)||t.hasOwnProperty(n[e].not)||n.hasOwnProperty(n[e].not)||(n[n[e].not]={})}),Object.keys(e=n).forEach(t=>{e[t].children=rr(e[t].children),e[t].not=rr(e[t].not)}),Object.keys(e).forEach(t=>{(e[t].not||[]).forEach(n=>{e[n]&&e[n].not&&e[n].not.push(t)})}),e};let ri=function(e){return"[object Array]"===Object.prototype.toString.call(e)};var ro={tag:function(e,t="",n){if(!this.found||!e)return this;let r=this.termList();if(0===r.length)return this;let{methods:a,verbose:i,world:o}=this;return!0===i&&console.log(" +  ",e,t||""),ri(e)?e.forEach(e=>a.one.setTag(r,e,o,n,t)):a.one.setTag(r,e,o,n,t),this.uncache(),this},tagSafe:function(e,t=""){return this.tag(e,t,!0)},unTag:function(e,t){if(!this.found||!e)return this;let n=this.termList();if(0===n.length)return this;let{methods:r,verbose:a,model:i}=this;!0===a&&console.log(" -  ",e,t||"");let o=i.one.tagSet;return ri(e)?e.forEach(e=>r.one.unTag(n,e,o)):r.one.unTag(n,e,o),this.uncache(),this},canBe:function(e){e=e.replace(/^#/,"");let t=this.model.one.tagSet,n=this.methods.one.canBe,r=[];this.document.forEach((a,i)=>{a.forEach((a,o)=>{n(a,e,t)||r.push([i,o,o+1])})});let a=this.update(r);return this.difference(a)}};let rs=new Set(["Auxiliary","Possessive"]),rl=/([.!?\u203D\u2E18\u203C\u2047-\u2049\u3002]+\s)/g,ru=/^[.!?\u203D\u2E18\u203C\u2047-\u2049\u3002]+\s$/,rc=/((?:\r?\n|\r)+)/;var rh=function(e){let t=[],n=e.split(rc);for(let e=0;e<n.length;e++){let r=n[e].split(rl);for(let e=0;e<r.length;e++)r[e+1]&&!0===ru.test(r[e+1])&&(r[e]+=r[e+1],r[e+1]=""),""!==r[e]&&t.push(r[e])}return t};let rd=/[a-z0-9\u00C0-\u00FF\u00a9\u00ae\u2000-\u3300\ud000-\udfff]/i,rg=/\S/;var rp=function(e){let t=[];for(let n=0;n<e.length;n++){let r=e[n];if(void 0!==r&&""!==r){if(!1===rg.test(r)||!1===rd.test(r)){if(t[t.length-1]){t[t.length-1]+=r;continue}if(e[n+1]){e[n+1]=r+e[n+1];continue}}t.push(r)}}return t},rm=function(e,t){let n=t.methods.one.tokenize.isSentence,r=t.model.one.abbreviations||new Set,a=[];for(let t=0;t<e.length;t++){let i=e[t];e[t+1]&&!1===n(i,r)?e[t+1]=i+(e[t+1]||""):i&&i.length>0&&(a.push(i),e[t]="")}return a};let rf={'"':'"',"＂":"＂","“":"”","‟":"”","„":"”","⹂":"”","‚":"’","\xab":"\xbb","‹":"›","‵":"′","‶":"″","‷":"‴","〝":"〞","〟":"〞"},rb=RegExp("["+Object.keys(rf).join("")+"]","g"),rv=RegExp("["+Object.values(rf).join("")+"]","g"),ry=function(e){if(!e)return!1;let t=e.match(rv);return null!==t&&1===t.length};var rw=function(e){let t=[];for(let n=0;n<e.length;n+=1){let r=e[n].match(rb);if(null!==r&&1===r.length){if(ry(e[n+1])&&e[n+1].length<280){e[n]+=e[n+1],t.push(e[n]),e[n+1]="",n+=1;continue}if(ry(e[n+2])){let r=e[n+1]+e[n+2];if(r.length<280){e[n]+=r,t.push(e[n]),e[n+1]="",e[n+2]="",n+=2;continue}}}t.push(e[n])}return t};let rk=/\(/g,rP=/\)/g;var rA=function(e){let t=[];for(let n=0;n<e.length;n+=1){let r=e[n].match(rk);if(null!==r&&1===r.length&&e[n+1]&&e[n+1].length<250&&null!==e[n+1].match(rP)&&1===r.length&&!rk.test(e[n+1])){e[n]+=e[n+1],t.push(e[n]),e[n+1]="",n+=1;continue}t.push(e[n])}return t};let rx=/\S/,rC=/^\s+/,rj=function(e,t){let n=e.split(/[-–—]/);if(n.length<=1)return!1;let{prefixes:r,suffixes:a}=t.one;return!(1===n[0].length&&/[a-z]/i.test(n[0])||r.hasOwnProperty(n[0]))&&(n[1]=n[1].trim().replace(/[.?!]$/,""),!a.hasOwnProperty(n[1])&&(!0===/^([a-z\u00C0-\u00FF`"'/]+)[-–—]([a-z0-9\u00C0-\u00FF].*)/i.test(e)||!0===/^[('"]?([0-9]{1,4})[-–—]([a-z\u00C0-\u00FF`"'/-]+[)'"]?$)/i.test(e)))},rN=function(e){let t=[],n=e.split(/[-–—]/),r="-",a=e.match(/[-–—]/);a&&a[0]&&(r=a);for(let e=0;e<n.length;e++)e===n.length-1?t.push(n[e]):t.push(n[e]+r);return t};var rI=function(e){let t=/^[0-9]{1,4}(:[0-9][0-9])?([a-z]{1,2})? ?[-–—] ?$/,n=/^[0-9]{1,4}([a-z]{1,2})? ?$/;for(let r=0;r<e.length-1;r+=1)e[r+1]&&t.test(e[r])&&n.test(e[r+1])&&(e[r]=e[r]+e[r+1],e[r+1]=null);return e};let rD=/\p{L} ?\/ ?\p{L}+$/u;var rT=function(e){for(let t=1;t<e.length-1;t++)rD.test(e[t])&&(e[t-1]+=e[t]+e[t+1],e[t]=null,e[t+1]=null);return e};let rO=/\S/,rH=/^[!?.]+$/,rE=/(\S+)/,rF=[".","?","!",":",";","-","–","—","--","...","(",")","[","]",'"',"'","`","\xab","\xbb","*","•"];rF=rF.reduce((e,t)=>(e[t]=!0,e),{});let rG=/\p{Letter}/u,rV=/[\p{Number}\p{Currency_Symbol}]/u,rz=/^[a-z]\.([a-z]\.)+/i,rM=/[sn]['’]$/;var rB=function(e,t){let{prePunctuation:n,postPunctuation:r,emoticons:a}=t.one,i=e,o="",s="",l=Array.from(e);if(a.hasOwnProperty(e.trim()))return{str:e.trim(),pre:o,post:" "};let u=l.length;for(let e=0;e<u;e+=1){let e=l[0];if(!0!==n[e]){if(("+"===e||"-"===e)&&rV.test(l[1])||"'"===e&&3===e.length&&rV.test(l[1])||rG.test(e)||rV.test(e))break;o+=l.shift()}}u=l.length;for(let e=0;e<u;e+=1){let e=l[l.length-1];if(!0!==r[e]){if(rG.test(e)||rV.test(e))break;("."!==e||!0!==rz.test(i))&&("'"!==e||!0!==rM.test(i))&&(s=l.pop()+s)}}return""===(e=l.join(""))&&(e=i=i.replace(/ *$/,e=>(s=e||"","")),o=""),{str:e,pre:o,post:s}},rS=function(e){let t=e=(e=(e=e||"").toLowerCase()).trim();return e=(e=(e=e.replace(/[,;.!?]+$/,"")).replace(/\u2026/g,"...")).replace(/\u2013/g,"-"),!1===/^[:;]/.test(e)&&(e=(e=(e=e.replace(/\.{3,}$/g,"")).replace(/[",.!:;?)]+$/g,"")).replace(/^['"(]+/g,"")),""===(e=(e=e.replace(/[\u200B-\u200D\uFEFF]/g,"")).trim())&&(e=t),e=e.replace(/([0-9]),([0-9])/g,"$1$2")};let r$=/([A-Z]\.)+[A-Z]?,?$/,rL=/^[A-Z]\.,?$/,rK=/[A-Z]{2,}('s|,)?$/,rJ=/([a-z]\.)+[a-z]\.?$/;var rW=function(e){var t;return t=e,(!0===r$.test(t)||!0===rJ.test(t)||!0===rL.test(t)||!0===rK.test(t))&&(e=e.replace(/\./g,"")),e},rq=function(e,t){let n=t.methods.one.killUnicode,r=e.text||"";r=rW(r=n(r=rS(r),t)),e.normal=r};let rR=/[ .][A-Z]\.? *$/i,rU=/(?:\u2026|\.{2,}) *$/,r_=/\p{L}/u,rQ=/\. *$/,rZ=/^[A-Z]\. $/,rY={},rX={};[[["approx","apt","bc","cyn","eg","esp","est","etc","ex","exp","prob","pron","gal","min","pseud","fig","jd","lat","lng","vol","fm","def","misc","plz","ea","ps","sec","pt","pref","pl","pp","qt","fr","sq","nee","ss","tel","temp","vet","ver","fem","masc","eng","adj","vb","rb","inf","situ","vivo","vitro","wr"]],[["dl","ml","gal","qt","pt","tbl","tsp","tbsp","km","dm","cm","mm","mi","td","hr","hrs","kg","hg","dg","cg","mg","\xb5g","lb","oz","sq ft","hz","mps","mph","kmph","kb","mb","tb","lx","lm","fl oz","yb"],"Unit"],[["ad","al","arc","ba","bl","ca","cca","col","corp","ft","fy","ie","lit","ma","md","pd","tce"],"Noun"],[["adj","adm","adv","asst","atty","bldg","brig","capt","cmdr","comdr","cpl","det","dr","esq","gen","gov","hon","jr","llb","lt","maj","messrs","mlle","mme","mr","mrs","ms","mstr","phd","prof","pvt","rep","reps","res","rev","sen","sens","sfc","sgt","sir","sr","supt","surg"],"Honorific"],[["jan","feb","mar","apr","jun","jul","aug","sep","sept","oct","nov","dec"],"Month"],[["dept","univ","assn","bros","inc","ltd","co"],"Organization"],[["rd","st","dist","mt","ave","blvd","cl","cres","hwy","ariz","cal","calif","colo","conn","fla","fl","ga","ida","ia","kan","kans","minn","neb","nebr","okla","penna","penn","pa","dak","tenn","tex","ut","vt","va","wis","wisc","wy","wyo","usafa","alta","ont","que","sask"],"Place"]].forEach(e=>{e[0].forEach(t=>{rY[t]=!0,rX[t]="Abbreviation",void 0!==e[1]&&(rX[t]=[rX[t],e[1]])})});var r0=["anti","bi","co","contra","de","extra","infra","inter","intra","macro","micro","mis","mono","multi","peri","pre","pro","proto","pseudo","re","sub","supra","trans","tri","un","out","ex"].reduce((e,t)=>(e[t]=!0,e),{});let r1={"!":"\xa1","?":"\xbfɁ",'"':'“”"❝❞',"'":"‘‛❛❜’","-":"—–",a:"\xaa\xc0\xc1\xc2\xc3\xc4\xc5\xe0\xe1\xe2\xe3\xe4\xe5ĀāĂăĄąǍǎǞǟǠǡǺǻȀȁȂȃȦȧȺΆΑΔΛάαλАаѦѧӐӑӒӓƛ\xe6",b:"\xdf\xfeƀƁƂƃƄƅɃΒβϐϦБВЪЬвъьѢѣҌҍ",c:"\xa2\xa9\xc7\xe7ĆćĈĉĊċČčƆƇƈȻȼͻͼϲϹϽϾСсєҀҁҪҫ",d:"\xd0ĎďĐđƉƊȡƋƌ",e:"\xc8\xc9\xca\xcb\xe8\xe9\xea\xebĒēĔĕĖėĘęĚěƐȄȅȆȇȨȩɆɇΈΕΞΣέεξϵЀЁЕеѐёҼҽҾҿӖӗễ",f:"ƑƒϜϝӺӻҒғſ",g:"ĜĝĞğĠġĢģƓǤǥǦǧǴǵ",h:"ĤĥĦħƕǶȞȟΉΗЂЊЋНнђћҢңҤҥҺһӉӊ",I:"\xcc\xcd\xce\xcf",i:"\xec\xed\xee\xefĨĩĪīĬĭĮįİıƖƗȈȉȊȋΊΐΪίιϊІЇіїi̇",j:"ĴĵǰȷɈɉϳЈј",k:"ĶķĸƘƙǨǩΚκЌЖКжкќҚқҜҝҞҟҠҡ",l:"ĹĺĻļĽľĿŀŁłƚƪǀǏǐȴȽΙӀӏ",m:"ΜϺϻМмӍӎ",n:"\xd1\xf1ŃńŅņŇňŉŊŋƝƞǸǹȠȵΝΠήηϞЍИЙЛПийлпѝҊҋӅӆӢӣӤӥπ",o:"\xd2\xd3\xd4\xd5\xd6\xd8\xf0\xf2\xf3\xf4\xf5\xf6\xf8ŌōŎŏŐőƟƠơǑǒǪǫǬǭǾǿȌȍȎȏȪȫȬȭȮȯȰȱΌΘΟθοσόϕϘϙϬϴОФоѲѳӦӧӨөӪӫ",p:"ƤΡρϷϸϼРрҎҏ\xde",q:"Ɋɋ",r:"ŔŕŖŗŘřƦȐȑȒȓɌɍЃГЯгяѓҐґ",s:"ŚśŜŝŞşŠšƧƨȘșȿЅѕ",t:"ŢţŤťŦŧƫƬƭƮȚțȶȾΓΤτϮТт",u:"\xd9\xda\xdb\xdc\xf9\xfa\xfb\xfcŨũŪūŬŭŮůŰűŲųƯưƱƲǓǔǕǖǗǘǙǚǛǜȔȕȖȗɄΰυϋύ",v:"νѴѵѶѷ",w:"ŴŵƜωώϖϢϣШЩшщѡѿ",x:"\xd7ΧχϗϰХхҲҳӼӽӾӿ",y:"\xdd\xfd\xffŶŷŸƳƴȲȳɎɏΎΥΫγψϒϓϔЎУучўѰѱҮүҰұӮӯӰӱӲӳ",z:"ŹźŻżŽžƵƶȤȥɀΖ"},r2={};Object.keys(r1).forEach(function(e){r1[e].split("").forEach(function(t){r2[t]=e})});let r3=/\//,r5=/[a-z]\.[a-z]/i,r4=/[0-9]/;var r6=function(e,t){let n=e.normal||e.text||e.machine,r=t.model.one.aliases;if(r.hasOwnProperty(n)&&(e.alias=e.alias||[],e.alias.push(r[n])),r3.test(n)&&!r5.test(n)&&!r4.test(n)){let t=n.split(r3);t.length<=3&&t.forEach(t=>{""!==(t=t.trim())&&(e.alias=e.alias||[],e.alias.push(t))})}return e};let r9=/^\p{Letter}+-\p{Letter}+$/u;var r7=function(e){let t=e.implicit||e.normal||e.text;t=(t=(t=t.replace(/['’]s$/,"")).replace(/s['’]$/,"s")).replace(/([aeiou][ktrp])in'$/,"$1ing"),r9.test(t)&&(t=t.replace(/-/g,"")),(t=t.replace(/^[#@]/,""))!==e.normal&&(e.machine=t)};let r8=function(e,t){let n=e.docs;for(let r=0;r<n.length;r+=1)for(let a=0;a<n[r].length;a+=1)t(n[r][a],e.world)},ae=function(){let e=this.docs;if(0===e.length)return this;let t=e[e.length-1]||[],n=t[t.length-1];return!0===n.typeahead&&n.machine&&(n.text=n.machine,n.normal=n.machine),this};var at=function(e,t,n){let r={},a=[],i=n.prefixes||{};return e.forEach(e=>{let o=(e=e.toLowerCase().trim()).length;t.max&&o>t.max&&(o=t.max);for(let s=t.min;s<o;s+=1){let o=e.substring(0,s);if(!(t.safe&&n.model.one.lexicon.hasOwnProperty(o))){if(!0===i.hasOwnProperty(o)||!0===r.hasOwnProperty(o)){a.push(o);continue}r[o]=e}}}),r=Object.assign({},i,r),a.forEach(e=>{delete r[e]}),r};let an=e=>"[object Object]"===Object.prototype.toString.call(e),ar={safe:!0,min:3};y.extend({api:function(e){Object.assign(e.prototype,en)},compute:{id:function(e){let t=e.docs;for(let e=0;e<t.length;e+=1)for(let n=0;n<t[e].length;n+=1){let r=t[e][n];r.id=r.id||H(r)}}}}),y.extend({api:function(e){Object.assign(e.prototype,ng)},methods:{one:{hash:ne,debug:{tags:function(e){let{docs:t,model:n}=e;0===t.length&&console.log(nm.blue("\n     ──────")),t.forEach(t=>{console.log(nm.blue("\n  ┌─────────")),t.forEach(t=>{var r;let a=[...t.tags||[]],i=t.text||"-";t.sense&&(i=`{${t.normal}/${t.sense}}`),t.implicit&&(i="["+t.implicit+"]");let o="'"+(i=nm.yellow(i))+"'";if(t.reference){let n=e.update([t.reference]).text("normal");o+=` - ${nm.dim(nm.i("["+n+"]"))}`}o=o.padEnd(18),console.log(nm.blue("  │ ")+nm.i(o)+"  - "+(r=a,n.one.tagSet&&(r=r.map(e=>n.one.tagSet.hasOwnProperty(e)?nm[n.one.tagSet[e].color||"blue"](e):e)),r.join(", ")))})}),console.log("\n")},clientSide:function(e){console.log("%c -=-=- ","background-color:#6699cc;"),e.forEach(e=>{console.groupCollapsed(e.text()),console.table(e.docs[0].map(e=>{let t=e.text||"-";return e.implicit&&(t="["+e.implicit+"]"),{text:t,tags:"["+Array.from(e.tags).join(", ")+"]"}}),["text","tags"]),console.groupEnd()})},chunks:function(e){let{docs:t}=e;console.log(""),t.forEach(e=>{let t=[];e.forEach(e=>{"Noun"===e.chunk?t.push(nm.blue(e.implicit||e.normal)):"Verb"===e.chunk?t.push(nm.green(e.implicit||e.normal)):"Adjective"===e.chunk?t.push(nm.yellow(e.implicit||e.normal)):"Pivot"===e.chunk?t.push(nm.red(e.implicit||e.normal)):t.push(e.implicit||e.normal)}),console.log(t.join(" "),"\n")}),console.log("\n")},highlight:function(e){if(!e.found)return;let t={};e.fullPointer.forEach(e=>{t[e[0]]=t[e[0]]||[],t[e[0]].push(e)}),Object.keys(t).forEach(n=>{let r=e.update([[Number(n)]]).text();e.update(t[n]).json({offset:!0}).forEach((e,t)=>{r=nb(r,e.offset,t)}),console.log(r)}),console.log("\n")}}}}}),y.extend({api:function(e){Object.assign(e.prototype,e2)},methods:{one:{termMethods:tC,parseMatch:function(e,t,n){var r,a;if(null==e||""===e)return[];t=t||{},"number"==typeof e&&(e=String(e));let i=e8(e);return(a=(a=tm(a=i=(i=th(i=i.map(e=>tu(e,t)),n)).map(e=>{if(e.root){if(n.methods.two&&n.methods.two.transform){let t=[];e.pos?"Verb"===e.pos?t=t.concat(td(e,n)):"Noun"===e.pos?t=t.concat(tg(e,n)):"Adjective"===e.pos&&(t=t.concat(tp(e,n))):t=(t=(t=t.concat(td(e,n))).concat(tg(e,n))).concat(tp(e,n)),(t=t.filter(e=>e)).length>0&&(e.operator="or",e.fastOr=new Set(t))}else e.machine=e.root,delete e.id,delete e.root}return e}))).map(e=>{if(void 0!==e.choices){if("or"!==e.operator||!0===e.fuzzy)return e;!0===e.choices.every(e=>{if(1!==e.length)return!1;let t=e[0];return!0!==t.fuzzy&&!t.start&&!t.end&&void 0!==t.word&&!0!==t.negative&&!0!==t.optional&&!0!==t.method})&&(e.fastOr=new Set,e.choices.forEach(t=>{e.fastOr.add(t[0].word)}),delete e.choices)}return e})).map(e=>(e.fuzzy&&e.choices&&e.choices.forEach(t=>{1===t.length&&t[0].word&&(t[0].fuzzy=!0,t[0].min=e.min)}),e))},match:function(e,t,n){n=n||[];let{regs:r,group:a,justOne:i}=t,o=[];if(!r||0===r.length)return{ptrs:[],byGroup:{}};let s=r.filter(e=>!0!==e.optional&&!0!==e.negative).length;e:for(let t=0;t<e.length;t+=1){let l=e[t];if(!(n[t]&&tb(r,n[t]))){if(!0===r[0].start){let e=tU(l,r,t,a);e&&o.push(e);continue}for(let e=0;e<l.length;e+=1){let n=l.slice(e);if(n.length<s)break;let a=tW(n,r,e,l.length);if(a){if(a=tR(a,t),o.push(a),!0===i)break e;let n=a.pointer[2];Math.abs(n-1)>e&&(e=Math.abs(n-1))}}}}if(!0===r[r.length-1].end&&(o=o.filter(t=>e[t.pointer[0]].length===t.pointer[2])),t.notIf){var l,u;l=o,u=t.notIf,o=l=l.filter(t=>{let[n,r,a]=t.pointer,i=e[n].slice(r,a);for(let e=0;e<i.length;e+=1)if(null!==tW(i.slice(e),u,e,i.length))return!1;return!0})}return(o=tq(o,a)).ptrs.forEach(t=>{let[n,r,a]=t;t[3]=e[n][r].id,t[4]=e[n][a-1].id}),o}}},lib:{parseMatch:function(e,t){let n=this.world(),r=n.methods.one.killUnicode;return r&&(e=r(e,n)),n.methods.one.parseMatch(e,t,n)}}}),y.extend({methods:{one:{termList:function(e){let t=[];for(let n=0;n<e.length;n+=1)for(let r=0;r<e[n].length;r+=1)t.push(e[n][r]);return t},getDoc:function(e,t){let n=[];return e.forEach((r,a)=>{if(!r)return;let[i,o,s,l,u]=r,c=t[i]||[];if(void 0===o&&(o=0),void 0===s&&(s=c.length),l&&(!c[o]||c[o].id!==l)){let n=nx(l,t,i);if(null!==n){let r=s-o,i=(c=t[n[0]].slice(n[1],n[1]+r))[0]?c[0].id:null;e[a]=[n[0],n[1],n[1]+r,i]}}else c=c.slice(o,s);0!==c.length&&o!==s&&(u&&c[c.length-1].id!==u&&(c=nC(r,t)),n.push(c))}),n=n.filter(e=>e.length>0)},pointer:{indexN:nw,splitAll:nA}}},api:function(e){Object.assign(e.prototype,nH)}}),y.extend({model:{one:{tagSet:{}}},compute:{tagRank:function(e){let{document:t,world:n}=e,r=n.model.one.tagSet;t.forEach(e=>{e.forEach(e=>{var t;let n=Array.from(e.tags);e.tagRank=n.sort((e,t)=>{if(rs.has(e)||!r.hasOwnProperty(t))return 1;if(rs.has(t)||!r.hasOwnProperty(e))return -1;let n=r[e].children||[];return n.length-(n=r[t].children||[]).length})})})}},methods:{one:{setTag:nq,unTag:function(e,t,n){t=t.trim().replace(/^#/,"");for(let r=0;r<e.length;r+=1){let a=e[r];if(!0===a.frozen)continue;if("*"===t){a.tags.clear();continue}let i=n[t];if(i&&i.children.length>0)for(let e=0;e<i.children.length;e+=1)a.tags.delete(i.children[e]);a.tags.delete(t)}},addTags:function(e,t){var n,r;return Object.keys(t).length>0&&(Object.keys(n=e).forEach(e=>{n[e]=Object.assign({},n[e]),n[e].novel=!0}),e=n),e=ra(e,t),rn(re(Object.keys(r=Object.assign({},t,e)).map(e=>{let t=r[e],n={not:new Set(t.not),also:t.also,is:t.is,novel:t.novel};return{id:e,parent:t.is,props:n,children:[]}})).cache().fillDown().out("array"))},canBe:function(e,t,n){if(!n.hasOwnProperty(t))return!0;let r=n[t].not||[];for(let t=0;t<r.length;t+=1)if(e.tags.has(r[t]))return!1;return!0}}},api:function(e){Object.assign(e.prototype,ro)},lib:{addTags:function(e){let{model:t,methods:n}=this.world(),r=t.one.tagSet,a=(0,n.one.addTags)(e,r);return t.one.tagSet=a,this}}}),y.plugin({model:{one:{contractions:[{word:"@",out:["at"]},{word:"arent",out:["are","not"]},{word:"alot",out:["a","lot"]},{word:"brb",out:["be","right","back"]},{word:"cannot",out:["can","not"]},{word:"dun",out:["do","not"]},{word:"can't",out:["can","not"]},{word:"shan't",out:["should","not"]},{word:"won't",out:["will","not"]},{word:"that's",out:["that","is"]},{word:"what's",out:["what","is"]},{word:"let's",out:["let","us"]},{word:"dunno",out:["do","not","know"]},{word:"gonna",out:["going","to"]},{word:"gotta",out:["have","got","to"]},{word:"gimme",out:["give","me"]},{word:"outta",out:["out","of"]},{word:"tryna",out:["trying","to"]},{word:"gtg",out:["got","to","go"]},{word:"im",out:["i","am"]},{word:"imma",out:["I","will"]},{word:"imo",out:["in","my","opinion"]},{word:"irl",out:["in","real","life"]},{word:"ive",out:["i","have"]},{word:"rn",out:["right","now"]},{word:"tbh",out:["to","be","honest"]},{word:"wanna",out:["want","to"]},{word:"c'mere",out:["come","here"]},{word:"c'mon",out:["come","on"]},{word:"shoulda",out:["should","have"]},{word:"coulda",out:["coulda","have"]},{word:"woulda",out:["woulda","have"]},{word:"musta",out:["must","have"]},{word:"tis",out:["it","is"]},{word:"twas",out:["it","was"]},{word:"y'know",out:["you","know"]},{word:"ne'er",out:["never"]},{word:"o'er",out:["over"]},{after:"ll",out:["will"]},{after:"ve",out:["have"]},{after:"re",out:["are"]},{after:"m",out:["am"]},{before:"c",out:["ce"]},{before:"m",out:["me"]},{before:"n",out:["ne"]},{before:"qu",out:["que"]},{before:"s",out:["se"]},{before:"t",out:["tu"]},{word:"shouldnt",out:["should","not"]},{word:"couldnt",out:["could","not"]},{word:"wouldnt",out:["would","not"]},{word:"hasnt",out:["has","not"]},{word:"wasnt",out:["was","not"]},{word:"isnt",out:["is","not"]},{word:"cant",out:["can","not"]},{word:"dont",out:["do","not"]},{word:"wont",out:["will","not"]},{word:"howd",out:["how","did"]},{word:"whatd",out:["what","did"]},{word:"whend",out:["when","did"]},{word:"whered",out:["where","did"]}],numberSuffixes:{st:!0,nd:!0,rd:!0,th:!0,am:!0,pm:!0,max:!0,"\xb0":!0,s:!0,e:!0,er:!0,ère:!0,ème:!0}}},compute:{contractions:e=>{let{world:t,document:n}=e,{model:r,methods:a}=t,i=r.one.contractions||[];n.forEach((r,o)=>{for(let s=r.length-1;s>=0;s-=1){let l=null,u=null;if(!0===ek.test(r[s].normal)){let e=r[s].normal.split(ek);l=e[0],u=e[1]}let c=ej(i,r[s],l,u);if(!c&&ex.hasOwnProperty(u)&&(c=ex[u](r,s,t)),!c&&eC.hasOwnProperty(l)&&(c=eC[l](r,s)),"there"===l&&"s"===u&&(c=eI(r,s)),c){er(n,[o,s],c=eN(c,e)),eA(n[o],e,s,c.length);continue}if(eP.test(r[s].normal)){(c=ev(r,s))&&(er(n,[o,s],c=eN(c,e)),a.one.setTag(c,"NumberRange",t),c[2]&&c[2].tags.has("Time")&&a.one.setTag([c[0]],"Time",t,null,"time-range"),eA(n[o],e,s,c.length));continue}(c=ew(r,s,t))&&(er(n,[o,s],c=eN(c,e)),a.one.setTag([c[1]],"Unit",t,null,"contraction-unit"))}})}},hooks:["contractions"]}),y.extend({compute:{alias:e=>r8(e,r6),machine:e=>r8(e,r7),normal:e=>r8(e,rq),freq:function(e){let t=e.docs,n={};for(let e=0;e<t.length;e+=1)for(let r=0;r<t[e].length;r+=1){let a=t[e][r],i=a.machine||a.normal;n[i]=n[i]||0,n[i]+=1}for(let e=0;e<t.length;e+=1)for(let r=0;r<t[e].length;r+=1){let a=t[e][r],i=a.machine||a.normal;a.freq=n[i]}},offset:function(e){let t=0,n=0,r=e.document;for(let e=0;e<r.length;e+=1)for(let a=0;a<r[e].length;a+=1){let i=r[e][a];i.offset={index:n,start:t+i.pre.length,length:i.text.length},t+=i.pre.length+i.text.length+i.post.length,n+=1}},index:function(e){let t=e.document;for(let e=0;e<t.length;e+=1)for(let n=0;n<t[e].length;n+=1)t[e][n].index=[e,n]},wordCount:function(e){let t=0,n=e.docs;for(let e=0;e<n.length;e+=1)for(let r=0;r<n[e].length;r+=1)""!==n[e][r].normal&&(t+=1,n[e][r].wordCount=t)}},methods:{one:{killUnicode:function(e,t){let n=t.model.one.unicode||{},r=(e=e||"").split("");return r.forEach((e,t)=>{n[e]&&(r[t]=n[e])}),r.join("")},tokenize:{splitSentences:function(e,t){if(!(e=String(e=e||""))||"string"!=typeof e||!1===rx.test(e))return[];let n=rp(rh(e=e.replace("\xa0"," ")));if(0===(n=rA(n=rw(n=rm(n,t)))).length)return[e];for(let e=1;e<n.length;e+=1){let t=n[e].match(rC);null!==t&&(n[e-1]+=t[0],n[e]=n[e].replace(rC,""))}return n},isSentence:function(e,t){if(!1===r_.test(e)||!0===rR.test(e)||3===e.length&&rZ.test(e)||!0===rU.test(e))return!1;let n=e.replace(/[.!?\u203D\u2E18\u203C\u2047-\u2049] *$/,"").split(" "),r=n[n.length-1].toLowerCase();return!0!==t.hasOwnProperty(r)||!0!==rQ.test(e)},splitTerms:function(e,t){var n;let r=[],a=[];if("number"==typeof(e=e||"")&&(e=String(e)),n=e,"[object Array]"===Object.prototype.toString.call(n))return e;let i=e.split(rE);for(let e=0;e<i.length;e++){if(!0===rj(i[e],t)){a=a.concat(rN(i[e]));continue}a.push(i[e])}let o="";for(let e=0;e<a.length;e++){let t=a[e];!0===rO.test(t)&&!1===rF.hasOwnProperty(t)&&!1===rH.test(t)?(r.length>0?(r[r.length-1]+=o,r.push(t)):r.push(o+t),o=""):o+=t}return o&&(0===r.length&&(r[0]=""),r[r.length-1]+=o),r=(r=rI(r=rT(r))).filter(e=>e)},splitWhitespace:(e,t)=>{let{str:n,pre:r,post:a}=rB(e,t);return{text:n,pre:r,post:a,tags:new Set}},fromString:function(e,t){let{methods:n,model:r}=t,{splitSentences:a,splitTerms:i,splitWhitespace:o}=n.one.tokenize;return e=a(e=e||"",t).map(e=>{let n=i(e,r);return(n=n.map(e=>o(e,r))).forEach(e=>{rq(e,t)}),n})}}}},model:{one:{aliases:{"&":"and","@":"at","%":"percent",plz:"please",bein:"being"},abbreviations:rY,prefixes:r0,suffixes:{like:!0,ish:!0,less:!0,able:!0,elect:!0,type:!0,designate:!0},prePunctuation:{"#":!0,"@":!0,_:!0,"\xb0":!0,"​":!0,"‌":!0,"‍":!0,"\uFEFF":!0},postPunctuation:{"%":!0,_:!0,"\xb0":!0,"​":!0,"‌":!0,"‍":!0,"\uFEFF":!0},lexicon:rX,unicode:r2,emoticons:{"<3":!0,"</3":!0,"<\\3":!0,":^P":!0,":^p":!0,":^O":!0,":^3":!0}}},hooks:["alias","machine","index","id"]}),y.extend({compute:{frozen:eD,freeze:eD,unfreeze:function(e){return e.docs.forEach(e=>{e.forEach(e=>{delete e.frozen})}),e}},mutate:e=>{let t=e.methods.one;t.termMethods.isFrozen=e=>!0===e.frozen,t.debug.freeze=eH,t.debug.frozen=eH},api:function(e){e.prototype.freeze=function(){return this.docs.forEach(e=>{e.forEach(e=>{e.frozen=!0})}),this},e.prototype.unfreeze=function(){this.compute("unfreeze")},e.prototype.isFrozen=function(){return this.match("@isFrozen+")}},hooks:["freeze"]}),y.plugin({api:function(e){Object.assign(e.prototype,w)},compute:{cache:function(e){e._cache=e.methods.one.cacheDoc(e.document)}},methods:{one:{cacheDoc:function(e){return e.map(e=>{let t=new Set;return e.forEach(e=>{""!==e.normal&&t.add(e.normal),e.switch&&t.add(`%${e.switch}%`),e.implicit&&t.add(e.implicit),e.machine&&t.add(e.machine),e.root&&t.add(e.root),e.alias&&e.alias.forEach(e=>t.add(e));let n=Array.from(e.tags);for(let e=0;e<n.length;e+=1)t.add("#"+n[e])}),t})}}}}),y.extend({api:function(e){e.prototype.lookup=function(e,t={}){return e?("string"==typeof e&&(e=[e]),e$(this,eL(e)?e:eM(e,this.world),t).settle()):this.none()}},lib:eJ}),y.extend({model:{one:{typeahead:{}}},api:function(e){e.prototype.autoFill=ae},lib:{typeahead:function(e=[],t={}){let n=this.model();t=Object.assign({},ar,t),an(e)&&(Object.assign(n.one.lexicon,e),e=Object.keys(e));let r=at(e,t,this.world());return Object.keys(r).forEach(e=>{if(n.one.typeahead.hasOwnProperty(e)){delete n.one.typeahead[e];return}n.one.typeahead[e]=r[e]}),this}},compute:{typeahead:function(e){let t=e.model.one.typeahead,n=e.docs;if(0===n.length||0===Object.keys(t).length)return;let r=n[n.length-1]||[],a=r[r.length-1];if(!a.post&&t.hasOwnProperty(a.normal)){let n=t[a.normal];a.implicit=n,a.machine=n,a.typeahead=!0,e.compute.preTagger&&e.last().unTag("*").compute(["lexicon","preTagger"])}}},hooks:["typeahead"]}),y.extend({model:{one:{lexicon:{},_multiCache:{},frozenLex:{}}},methods:{one:{expandLexicon:function(e){let t={},n={};return Object.keys(e).forEach(r=>{let a=e[r],i=(r=(r=r.toLowerCase().trim()).replace(/'s\b/,"")).split(/ /);i.length>1&&(void 0===n[i[0]]||i.length>n[i[0]])&&(n[i[0]]=i.length),t[r]=t[r]||a}),delete t[""],delete t[null],delete t[" "],{lex:t,_multi:n}}}},compute:{lexicon:function(e){let t=e.world;e.docs.forEach(e=>{for(let n=0;n<e.length;n+=1)0===e[n].tags.size&&(eE(e,n,t)||eV(e,n,t))})}},lib:{addWords:function(e,t=!1){let n=this.world(),{methods:r,model:a}=n;if(!e)return;if(Object.keys(e).forEach(t=>{"string"==typeof e[t]&&e[t].startsWith("#")&&(e[t]=e[t].replace(/^#/,""))}),!0===t){let{lex:t,_multi:i}=r.one.expandLexicon(e,n);Object.assign(a.one._multiCache,i),Object.assign(a.one.frozenLex,t);return}if(r.two.expandLexicon){let{lex:t,_multi:i}=r.two.expandLexicon(e,n);Object.assign(a.one.lexicon,t),Object.assign(a.one._multiCache,i)}let{lex:i,_multi:o}=r.one.expandLexicon(e,n);Object.assign(a.one.lexicon,i),Object.assign(a.one._multiCache,o)}},hooks:["lexicon"]}),y.extend({lib:{buildNet:function(e){let t=this.methods().one.buildNet(e,this.world());return t.isNet=!0,t}},api:function(e){e.prototype.sweep=function(e,t={}){let{world:n,docs:r}=this,{methods:a}=n,i=a.one.bulkMatch(r,e,this.methods,t);!1!==t.tagger&&a.one.bulkTagger(i,r,this.world);let o=(i=i.map(e=>{let t=e.pointer,n=r[t[0]][t[1]],a=t[2]-t[1];return n.index&&(e.pointer=[n.index[0],n.index[1],t[1]+a]),e})).map(e=>e.pointer);return i=i.map(e=>(e.view=this.update([e.pointer]),delete e.regs,delete e.needs,delete e.pointer,delete e._expanded,e)),{view:this.update(o),found:i}}},methods:{one:{buildNet:function(e,t){e=nV(e,t);let n={};return e.forEach(e=>{e.needs.forEach(t=>{n[t]=Array.isArray(n[t])?n[t]:[],n[t].push(e)}),e.wants.forEach(t=>{n[t]=Array.isArray(n[t])?n[t]:[],n[t].push(e)})}),Object.keys(n).forEach(e=>{let t={};n[e]=n[e].filter(e=>"boolean"!=typeof t[e.match]&&(t[e.match]=!0,!0))}),{hooks:n,always:e.filter(e=>0===e.needs.length&&0===e.wants.length)}},bulkMatch:function(e,t,n,r={}){var a;let i=n.one.cacheDoc(e),o=(a=t.hooks,i.map((e,t)=>{let n=[];Object.keys(a).forEach(e=>{i[t].has(e)&&(n=n.concat(a[e]))});let r={};return n=n.filter(e=>"boolean"!=typeof r[e.match]&&(r[e.match]=!0,!0))}));return o=o.map((e,t)=>{let n=i[t];return e=(e=(e=e.filter(e=>e.needs.every(e=>n.has(e)))).filter(e=>void 0===e.ifNo||!0!==e.ifNo.some(e=>n.has(e)))).filter(e=>0===e.wants.length||e.wants.filter(e=>n.has(e)).length>=e.minWant)}),t.always.length>0&&(o=o.map(e=>e.concat(t.always))),nz(o=o.map((t,n)=>{let r=e[n].length;return t=t.filter(e=>r>=e.minWords)}),e,i,n,r)},bulkTagger:function(e,t,n){let{model:r,methods:a}=n,{getDoc:i,setTag:o,unTag:s}=a.one,l=a.two.looksPlural;return 0===e.length?e:((void 0!==nB&&nB.env?nB.env:self.env||{}).DEBUG_TAGS&&console.log(`

  \x1b[32m→ ${e.length} post-tagger:\x1b[0m`),e.map(e=>{if(!e.tag&&!e.chunk&&!e.unTag)return;let a=e.reason||e.match,u=i([e.pointer],t)[0];if(!0!==e.safe||!1!==nM(u,e.tag,r)&&"-"!==u[u.length-1].post){if(void 0!==e.tag){if(o(u,e.tag,n,e.safe,`[post] '${a}'`),"Noun"===e.tag&&l){let t=u[u.length-1];l(t.text)?o([t],"Plural",n,e.safe,"quick-plural"):o([t],"Singular",n,e.safe,"quick-singular")}!0===e.freeze&&u.forEach(e=>e.frozen=!0)}void 0!==e.unTag&&s(u,e.unTag,n,e.safe,a),e.chunk&&u.forEach(t=>t.chunk=e.chunk)}}))}}}});var aa={addendum:"addenda",corpus:"corpora",criterion:"criteria",curriculum:"curricula",genus:"genera",memorandum:"memoranda",opus:"opera",ovum:"ova",phenomenon:"phenomena",referendum:"referenda",alga:"algae",alumna:"alumnae",antenna:"antennae",formula:"formulae",larva:"larvae",nebula:"nebulae",vertebra:"vertebrae",analysis:"analyses",axis:"axes",diagnosis:"diagnoses",parenthesis:"parentheses",prognosis:"prognoses",synopsis:"synopses",thesis:"theses",neurosis:"neuroses",appendix:"appendices",index:"indices",matrix:"matrices",ox:"oxen",sex:"sexes",alumnus:"alumni",bacillus:"bacilli",cactus:"cacti",fungus:"fungi",hippopotamus:"hippopotami",libretto:"libretti",modulus:"moduli",nucleus:"nuclei",octopus:"octopi",radius:"radii",stimulus:"stimuli",syllabus:"syllabi",cookie:"cookies",calorie:"calories",auntie:"aunties",movie:"movies",pie:"pies",rookie:"rookies",tie:"ties",zombie:"zombies",leaf:"leaves",loaf:"loaves",thief:"thieves",foot:"feet",goose:"geese",tooth:"teeth",beau:"beaux",chateau:"chateaux",tableau:"tableaux",bus:"buses",gas:"gases",circus:"circuses",crisis:"crises",virus:"viruses",database:"databases",excuse:"excuses",abuse:"abuses",avocado:"avocados",barracks:"barracks",child:"children",clothes:"clothes",echo:"echoes",embargo:"embargoes",epoch:"epochs",deer:"deer",halo:"halos",man:"men",woman:"women",mosquito:"mosquitoes",mouse:"mice",person:"people",quiz:"quizzes",rodeo:"rodeos",shoe:"shoes",sombrero:"sombreros",stomach:"stomachs",tornado:"tornados",tuxedo:"tuxedos",volcano:"volcanoes"},ai={Comparative:"true\xa6bett1f0;arth0ew0in0;er",Superlative:"true\xa6earlier",PresentTense:"true\xa6bests,sounds",Condition:"true\xa6lest,unless",PastTense:"true\xa6began,came,d4had,kneel3l2m0sa4we1;ea0sg2;nt;eap0i0;ed;id",Participle:"true\xa60:09;a06b01cZdXeat0fSgQhPoJprov0rHs7t6u4w1;ak0ithdra02o2r1;i02uY;k0v0;nd1pr04;ergoJoJ;ak0hHo3;e9h7lain,o6p5t4un3w1;o1um;rn;g,k;ol0reS;iQok0;ught,wn;ak0o1runk;ne,wn;en,wn;ewriNi1uJ;dd0s0;ut3ver1;do4se0t1;ak0h2;do2g1;roG;ne;ast0i7;iv0o1;ne,tt0;all0loBor1;bi3g2s1;ak0e0;iv0o9;dd0;ove,r1;a5eamt,iv0;hos0lu1;ng;e4i3lo2ui1;lt;wn;tt0;at0en,gun;r2w1;ak0ok0;is0;en",Gerund:"true\xa6accord0be0doin,go0result0stain0;ing",Expression:"true\xa6a0Yb0Uc0Sd0Oe0Mfarew0Lg0FhZjeez,lWmVnToOpLsJtIuFvEw7y0;a5e3i1u0;ck,p;k04p0;ee,pee;a0p,s;!h;!a,h,y;a5h2o1t0;af,f;rd up,w;atsoever,e1o0;a,ops;e,w;hoo,t;ery w06oi0L;gh,h0;! 0h,m;huh,oh;here nPsk,ut tut;h0ic;eesh,hh,it,oo;ff,h1l0ow,sst;ease,s,z;ew,ooey;h1i,mg,o0uch,w,y;h,o,ps;! 0h;hTmy go0wT;d,sh;a7evertheless,o0;!pe;eh,mm;ah,eh,m1ol0;!s;ao,fao;aCeBi9o2u0;h,mph,rra0zzC;h,y;l1o0;r6y9;la,y0;! 0;c1moCsmok0;es;ow;!p hip hoor0;ay;ck,e,llo,y;ha1i,lleluj0;ah;!ha;ah,ee4o1r0;eat scott,r;l1od0sh; grief,bye;ly;! whiz;ell;e0h,t cetera,ureka,ww,xcuse me;k,p;'oh,a0rat,uh;m0ng;mit,n0;!it;mon,o0;ngratulations,wabunga;a2oo1r0tw,ye;avo,r;!ya;h,m; 1h0ka,las,men,rgh,ye;!a,em,h,oy;la",Negative:"true\xa6n0;ever,o0;n,t",QuestionWord:"true\xa6how3wh0;at,e1ich,o0y;!m,se;n,re; come,'s",Reflexive:"true\xa6h4it5my5o1the0your2;ir1m1;ne3ur0;sel0;f,ves;er0im0;self",Plural:"true\xa6dick0gre0ones,records;ens","Unit|Noun":"true\xa6cEfDgChBinchAk9lb,m6newt5oz,p4qt,t1y0;ardEd;able1b0ea1sp;!l,sp;spo1;a,t,x;on9;!b,g,i1l,m,p0;h,s;!les;!b,elvin,g,m;!es;g,z;al,b;eet,oot,t;m,up0;!s",Value:"true\xa6a few",Imperative:"true\xa6bewa0come he0;re","Plural|Verb":"true\xa6leaves",Demonym:"true\xa60:15;1:12;a0Vb0Oc0Dd0Ce08f07g04h02iYjVkTlPmLnIomHpEqatari,rCs7t5u4v3welAz2;am0Gimbabwe0;enezuel0ietnam0I;gAkrai1;aiwTex0hai,rinida0Ju2;ni0Prkmen;a5cotti4e3ingapoOlovak,oma0Spaniard,udRw2y0W;ede,iss;negal0Cr09;sh;mo0uT;o5us0Jw2;and0;a2eru0Fhilippi0Nortugu07uerto r0S;kist3lesti1na2raguay0;ma1;ani;ami00i2orweP;caragu0geri2;an,en;a3ex0Lo2;ngo0Drocc0;cedo1la2;gasy,y07;a4eb9i2;b2thua1;e0Cy0;o,t01;azakh,eny0o2uwaiI;re0;a2orda1;ma0Ap2;anO;celandic,nd4r2sraeli,ta01vo05;a2iB;ni0qi;i0oneU;aiAin2ondur0unO;di;amEe2hanai0reek,uatemal0;or2rm0;gi0;ilipino,ren8;cuadoVgyp4mira3ngli2sto1thiopi0urope0;shm0;ti;ti0;aPominUut3;a9h6o4roat3ub0ze2;ch;!i0;lom2ngol5;bi0;a6i2;le0n2;ese;lifor1m2na3;bo2eroo1;di0;angladeshi,el6o4r3ul2;gaE;azi9it;li2s1;vi0;aru2gi0;si0;fAl7merBngol0r5si0us2;sie,tr2;a2i0;li0;genti2me1;ne;ba1ge2;ri0;ni0;gh0r2;ic0;an",Organization:"true\xa60:4Q;a3Tb3Bc2Od2He2Df27g1Zh1Ti1Pj1Nk1Ll1Gm12n0Po0Mp0Cqu0Br02sTtHuCv9w3xiaomi,y1;amaha,m1Bou1w1B;gov,tu3C;a4e2iki1orld trade organizati33;leaRped0O;lls fargo,st1;fie2Hinghou2R;l1rner br3U;gree3Jl street journ2Im1E;an halOeriz2Xisa,o1;dafo2Yl1;kswagMvo;b4kip,n2ps,s1;a tod3Aps;es3Mi1;lev3Fted natio3C;er,s; mobi32aco beRd bOe9gi frida3Lh3im horto3Amz,o1witt3D;shi49y1;ota,s r 05;e 1in lizzy;b3carpen3Jdaily ma3Dguess w2holli0s1w2;mashing pumpki35uprem0;ho;ea1lack eyed pe3Xyr0Q;ch bo3Dtl0;l2n3Qs1xas instrumen1U;co,la m1F;efoni0Kus;a8cientology,e5ieme2Ymirnoff,np,o3pice gir6quare0Ata1ubaru;rbuc1to34;ks;ny,undgard1;en;a2x pisto1;ls;g1Wrs;few2Minsbur31lesfor03msu2E;adiohead,b8e4o1yana3C;man empi1Xyal 1;b1dutch she4;ank;a3d 1max,vl20;bu1c2Ahot chili peppe2Ylobst2N;ll;ders dige1Ll madrid;c,s;ant3Aizn2Q;a8bs,e5fiz2Ihilip4i3r1;emier 1udenti1D;leagTo2K;nk floyd,zza hut; morrBs;psi2tro1uge0E;br33chi0Tn33;!co;lant2Un1yp16; 2ason27da2P;ld navy,pec,range juli2xf1;am;us;aAb9e6fl,h5i4o1sa,vid3wa;k2tre dame,vart1;is;ia;ke,ntendo,ss0QvZ;l,s;c,st1Otflix,w1; 1sweek;kids on the block,york0D;a,c;nd22s2t1;ional aca2Po,we0U;a,c02d0S;aDcdonalCe9i6lb,o3tv,y1;spa1;ce;b1Tnsanto,ody blu0t1;ley cr1or0T;ue;c2t1;as,subisO;helin,rosoft;dica2rcedes benz,talli1;ca;id,re;ds;cs milk,tt19z24;a3e1g,ittle caesa1P; ore09novo,x1;is,mark,us; 1bour party;pres0Dz boy;atv,fc,kk,lm,m1od1O;art;iffy lu0Roy divisi0Jpmorgan1sa;! cha09;bm,hop,k3n1tv;g,te1;l,rpol;ea;a5ewlett pack1Vi3o1sbc,yundai;me dep1n1P;ot;tac1zbollah;hi;lliburt08sbro;eneral 6hq,ithub,l5mb,o2reen d0Ou1;cci,ns n ros0;ldman sachs,o1;dye1g0H;ar;axo smith kli04encoW;electr0Nm1;oto0Z;a5bi,c barcelo4da,edex,i2leetwood m03o1rito l0G;rd,xcY;at,fa,nancial1restoZ; tim0;na;cebook,nnie mae;b0Asa,u3xxon1; m1m1;ob0J;!rosceptics;aiml0De5isney,o4u1;nkin donu2po0Zran dur1;an;ts;j,w jon0;a,f lepp12ll,peche mode,r spieg02stiny's chi1;ld;aJbc,hFiDloudflaCnn,o3r1;aigsli5eedence clearwater reviv1ossra09;al;c7inba6l4m1o0Est09;ca2p1;aq;st;dplSg1;ate;se;a c1o chanQ;ola;re;a,sco1tigroup;! systems;ev2i1;ck fil a,na daily;r1y;on;d2pital o1rls jr;ne;bury,ill1;ac;aEbc,eBf9l5mw,ni,o1p,rexiteeU;ei3mbardiIston 1;glo1pizza;be;ng;o2ue c1;roV;ckbuster video,omingda1;le; g1g1;oodriL;cht2e ge0rkshire hathaw1;ay;el;cardi,idu,nana republ3s1xt5y5;f,kin robbi1;ns;ic;bYcTdidSerosmith,iRlKmEnheuser busDol,ppleAr6s4u3v2y1;er;is,on;di,todesk;hland o1sociated E;il;b3g2m1;co;os;ys; compu1be0;te1;rs;ch;c,d,erican3t1;!r1;ak; ex1;pre1;ss; 5catel2ta1;ir;! lu1;ce1;nt;jazeera,qae1;da;g,rbnb;as;/dc,a3er,tivision1;! blizz1;ard;demy of scienc0;es;ba",Possessive:"true\xa6its,my,our0thy;!s","Noun|Verb":"true\xa60:9W;1:AA;2:96;3:A3;4:9R;5:A2;6:9K;7:8N;8:7L;9:A8;A:93;B:8D;C:8X;a9Ob8Qc7Id6Re6Gf5Sg5Hh55i4Xj4Uk4Rl4Em40n3Vo3Sp2Squ2Rr21s0Jt02u00vVwGyFzD;ip,oD;ne,om;awn,e6Fie68;aOeMhJiHoErD;ap,e9Oink2;nd0rDuC;kDry,sh5Hth;!shop;ck,nDpe,re,sh;!d,g;e86iD;p,sD;k,p0t2;aDed,lco8W;r,th0;it,lk,rEsDt4ve,x;h,te;!ehou1ra9;aGen5FiFoD;iDmAte,w;ce,d;be,ew,sA;cuum,l4B;pDr7;da5gra6Elo6A;aReQhrPiOoMrGuEwiDy5Z;n,st;nDrn;e,n7O;aGeFiEoDu6;t,ub2;bu5ck4Jgg0m,p;at,k,nd;ck,de,in,nsDp,v7J;f0i8R;ll,ne,p,r4Yss,t94uD;ch,r;ck,de,e,le,me,p,re;e5Wow,u6;ar,e,ll,mp0st,xt;g,lDng2rg7Ps5x;k,ly;a0Sc0Ne0Kh0Fi0Dk0Cl0Am08n06o05pXquaBtKuFwD;ea88iD;ng,pe,t4;bGit,m,ppErD;fa3ge,pri1v2U;lDo6S;e6Py;!je8;aMeLiKoHrEuDy2;dy,ff,mb2;a85eEiDo5Pugg2;ke,ng;am,ss,t4;ckEop,p,rD;e,m;ing,pi2;ck,nk,t4;er,m,p;ck,ff,ge,in,ke,lEmp,nd,p2rDte,y;!e,t;k,l;aJeIiHlGoFrDur,y;ay,e56inDu3;g,k2;ns8Bt;a5Qit;ll,n,r87te;ed,ll;m,n,rk;b,uC;aDee1Tow;ke,p;a5Je4FiDo53;le,rk;eep,iDou4;ce,p,t;ateboa7Ii;de,gnDl2Vnk,p,ze;!al;aGeFiEoDuff2;ck,p,re,w;ft,p,v0;d,i3Ylt0;ck,de,pe,re,ve;aEed,nDrv1It;se,t2N;l,r4t;aGhedu2oBrD;aEeDibb2o3Z;en,w;pe,t4;le,n,r2M;cDfegua72il,mp2;k,rifi3;aZeHhy6LiGoEuD;b,in,le,n,s5X;a6ck,ll,oDpe,u5;f,t;de,ng,ot,p,s1W;aTcSdo,el,fQgPje8lOmMnLo17pJque6sFturn,vDwa6V;eDi27;al,r1;er74oFpe8tEuD;lt,me;!a55;l71rt;air,eaDly,o53;l,t;dezvo2Zt;aDedy;ke,rk;ea1i4G;a6Iist0r5N;act6Yer1Vo71uD;nd,se;a38o6F;ch,s6G;c1Dge,iEke,lly,nDp1Wt1W;ge,k,t;n,se;es6Biv0;a04e00hYiXlToNrEsy4uD;mp,n4rcha1sh;aKeIiHoDu4O;be,ceFdu3fi2grDje8mi1p,te6;amDe6W;!me;ed,ss;ce,de,nt;sDy;er6Cs;cti3i1;iHlFoEp,re,sDuCw0;e,i5Yt;l,p;iDl;ce,sh;nt,s5V;aEce,e32uD;g,mp,n7;ce,nDy;!t;ck,le,n17pe,tNvot;a1oD;ne,tograph;ak,eFnErDt;fu55mA;!c32;!l,r;ckJiInHrFsEtDu1y;ch,e9;s,te;k,tD;!y;!ic;nt,r,se;!a7;bje8ff0il,oErDutli3Qver4B;bAd0ie9;ze;a4ReFoDur1;d,tD;e,i3;ed,gle8tD;!work;aMeKiIoEuD;rd0;ck,d3Rld,nEp,uDve;nt,th;it5EkD;ey;lk,n4Brr5CsDx;s,ta2B;asuBn4UrDss;ge,it;il,nFp,rk3WsEtD;ch,t0;h,k,t0;da5n0oeuvB;aLeJiHoEuD;mp,st;aEbby,ck,g,oDve;k,t;d,n;cDe,ft,mAnIst;en1k;aDc0Pe4vK;ch,d,k,p,se;bFcEnd,p,t4uD;gh,n4;e,k;el,o2U;eEiDno4E;ck,d,ll,ss;el,y;aEo1OuD;i3mp;m,zz;mpJnEr46ssD;ue;c1Rdex,fluGha2k,se2HteDvoi3;nt,rD;e6fa3viD;ew;en3;a8le2A;aJeHiGoEuD;g,nt;l3Ano2Dok,pDr1u1;!e;ghli1Fke,nt,re,t;aDd7lp;d,t;ck,mGndFrEsh,tDu9;ch,e;bo3Xm,ne4Eve6;!le;!m0;aMear,ift,lKossJrFuD;arDe4Alp,n;antee,d;aFiEoDumb2;uCwth;ll,nd,p;de,sp;ip;aBoDue;ss,w;g,in,me,ng,s,te,ze;aZeWiRlNoJrFuD;ck,el,nDss,zz;c38d;aEoDy;st,wn;cDgme,me,nchi1;tuB;cFg,il,ld,rD;ce,e29mDwa31;!at;us;aFe0Vip,oDy;at,ck,od,wD;!er;g,ke,me,re,sh,vo1E;eGgFlEnDre,sh,t,x;an3i0Q;e,m,t0;ht,uB;ld;aEeDn3;d,l;r,tuB;ce,il,ll,rm,vo2W;cho,d7ffe8nMsKxFyeD;!baD;ll;cGerci1hFpDtra8;eriDo0W;en3me9;au6ibA;el,han7u1;caDtima5;pe;count0d,vy;a01eSiMoJrEuDye;b,el,mp,pli2X;aGeFiEoD;ne,p;ft,ll,nk,p,ve;am,ss;ft,g,in;cEd7ubt,wnloD;ad;k,u0E;ge6p,sFt4vD;e,iDor3;de;char7gui1h,liEpD;at4lay,u5;ke;al,bKcJfeIlGmaCposAsEtaD;il;e07iD;gn,re;ay,ega5iD;ght;at,ct;li04rea1;a5ut;b,ma7n3rDte;e,t;a0Eent0Dh06irc2l03oKrFuD;be,e,rDt;b,e,l,ve;aGeFoEuDy;sh;p,ss,wd;dAep;ck,ft,sh;at,de,in,lTmMnFordina5py,re,st,uDv0;gh,nDp2rt;s01t;ceHdu8fli8glomeIsFtDveN;a8rD;a6ol;e9tru8;ct;ntDrn;ra5;bHfoGmFpD;leDouCromi1;me9;aCe9it,u5;rt;at,iD;ne;lap1oD;r,ur;aEiDoud,ub;ck,p;im,w;aEeDip;at,ck,er;iGllen7nErD;ge,m,t;ge,nD;el;n,r;er,re;ke,ll,mp,noe,pGrXsFtEuDve;se,ti0I;alog,ch;h,t;!tuB;re;a03eZiXlToPrHuEyD;pa11;bb2ck2dgEff0mp,rDst,zz;den,n;et;anJeHiFoadEuD;i1sh;ca6;be,d7;ge;aDed;ch,k;ch,d;aFg,mb,nEoDrd0tt2x,ycott;k,st,t;d,e;rd,st;aFeCiDoYur;nk,tz;nd;me;as,d,ke,nd,opsy,tD;!ch,e;aFef,lt,nDt;d,efA;it;r,t;ck,il,lan3nIrFsEtt2;le;e,h;!gDk;aDe;in;!d,g,k;bu1c05dZge,iYlVnTppQrLsIttGucEwaD;rd;tiD;on;aDempt;ck;k,sD;i6ocia5;st;chFmD;!oD;ur;!iD;ve;eEroa4;ch;al;chDg0sw0;or;aEt0;er;rm;d,m,r;dreHvD;an3oD;ca5;te;ce;ss;cDe,he,t;eFoD;rd,u9;nt;nt,ss;se",Actor:"true\xa60:7B;1:7G;2:6A;3:7F;4:7O;5:7K;a6Nb62c4Ud4Be41f3Sg3Bh30i2Uj2Qkin2Pl2Km26n1Zo1Sp0Vqu0Tr0JsQtJuHvEw8yo6;gi,ut6;h,ub0;aAe9i8o7r6;estl0it0;m2rk0;fe,nn0t2Bza2H;atherm2ld0;ge earn0it0nder0rri1;eter7i6oyF;ll5Qp,s3Z;an,ina2U;n6s0;c6Uder03;aoisea23e9herapi5iktok0o8r6ut1yco6S;a6endseLo43;d0mp,nscri0Bvel0;ddl0u1G;a0Qchn7en6na4st0;ag0;i3Oo0D;aiXcUeRhPiMki0mu26oJpGquaFtBu7wee6;p0theart;lt2per7r6;f0ge6Iviv1;h6inten0Ist5Ivis1;ero,um2;a8ep7r6;ang0eam0;bro2Nc2Ofa2Nmo2Nsi20;ff0tesm2;tt0;ec7ir2Do6;kesp59u0M;ia5Jt3;l7me6An,rcere6ul;r,ss;di0oi5;n7s6;sy,t0;g0n0;am2ephe1Iow6;girl,m2r2Q;cretInior cit3Fr6;gea4v6;a4it1;hol4Xi7reen6ulpt1;wr2C;e01on;l1nt;aEe9o8u6;l0nn6;er up,ingE;g40le mod3Zof0;a4Zc8fug2Ppo32searQv6;ere4Uolution6;ary;e6luYru22;ptio3T;bbi,dic5Vpp0;arter6e2Z;back;aYeWhSiRlOoKr8sycho7u6;nk,p31;logi5;aGeDiBo6;d9fess1g7ph47s6;pe2Ktitu51;en6ramm0;it1y;igy,uc0;est4Nme mini0Unce6s3E;!ss;a7si6;de4;ch0;ctiti39nk0P;dca0Oet,li6pula50rnst42;c2Itic6;al scie6i2;nti5;a6umb0;nn0y6;er,ma4Lwright;lgrim,one0;a8iloso7otogra7ra6ysi1V;se;ph0;ntom,rmaci5;r6ssi1T;form0s4O;i3El,nel3Yr8st1tr6wn;i6on;arWot;ent4Wi42tn0;ccupa4ffBp8r7ut6;ca5l0B;ac4Iganiz0ig2Fph2;er3t6;i1Jomet6;ri5;ic0spring;aBe9ie4Xo7u6;n,rser3J;b6mad,vi4V;le2Vo4D;i6mesis,phew;ce,ghb1;nny,rr3t1X;aEeDiAo7u6yst1Y;m8si16;der3gul,m7n6th0;arDk;!my;ni7s6;f02s0Jt0;on,st0;chan1Qnt1rcha4;gi9k0n8rtyr,t6y1;e,riar6;ch;ag0iac;ci2stra3I;a7e2Aieutena4o6;rd,s0v0;bor0d7ndlo6ss,urea3Fwy0ym2;rd;!y;!s28;e8o7u6;ggl0;gg0urna2U;st0;c3Hdol,llu3Ummigra4n6; l9c1Qfa4habi42nov3s7ve6;nt1stig3;pe0Nt6;a1Fig3ru0M;aw;airFeBistoAo8u6ygie1K;man6sba2H;!ita8;bo,st6usekN;age,e3P;ri2;ir,r6;m7o6;!ine;it;dress0sty2C;aLeIhostGirl26ladi3oCrand7u6;e5ru;c9daug0Jfa8m7pa6s2Y;!re4;a,o6;th0;hi1B;al7d6lf0;!de3A;ie,k6te26;eep0;!wr6;it0;isha,n6;i6tl04;us;mbl0rden0;aDella,iAo7r6;eela2Nie1P;e,re6ster pare4;be1Hm2r6st0;unn0;an2ZgZlmm17nanci0r6tt0;e6st la2H; marsh2OfigXm2;rm0th0;conoEdDlectriCm8n7x6;amin0cellency,i2A;emy,trepreneur,vironmenta1J;c8p6;er1loye6;e,r;ee;ci2;it1;mi5;aKeBi8ork,ri7u6we02;de,tche2H;ft0v0;ct3eti7plom2Hre6va;ct1;ci2ti2;aDcor3fencCi0InAput9s7tectLvel6;op0;ce1Ge6ign0;rt0;ee,y;iz6;en;em2;c1Ml0;d8nc0redev7ug6;ht0;il;!dy;a06e04fo,hXitizenWlToBr9u6;r3stomer6;! representat6;ive;e3it6;ic;lJmGnAord9rpor1Nu7w6;boy,ork0;n6ri0;ciTte1Q;in3;fidantAgressSs9t6;e0Kr6;ibut1o6;ll0;tab13ul1O;!e;edi2m6pos0rade;a0EeQissi6;on0;leag8on7um6;ni5;el;ue;e6own;an0r6;ic,k;!s;a9e7i6um;ld;erle6f;ad0;ir7nce6plFract0;ll1;m2wI;lebri6o;ty;dBptAr6shi0;e7pe6;nt0;r,t6;ak0;ain;et;aMeLiJlogg0oErBu6;dd0Fild0rgl9siness6;m2p7w6;om2;ers05;ar;i7o6;!k0th0;cklay0de,gadi0;hemi2oge8y6;!frie6;nd;ym2;an;cyc6sR;li5;atbox0ings;by,nk0r6;b0on7te6;nd0;!e07;c04dWge4nQpLrHsFtAu7yatull6;ah;nt7t6;h1oG;!ie;h8t6;e6orney;nda4;ie5le6;te;sis00tron6;aut,om0;chbis8isto7tis6;an,t;crU;hop;ost9p6;ari6rentiS;ti6;on;le;a9cest1im3nou8y6;bo6;dy;nc0;ly5rc6;hi5;mi8v6;entur0is1;er;ni7r6;al;str3;at1;or;counBquaintanArob9t6;ivi5or,re6;ss;st;at;ce;ta4;nt","Adj|Noun":"true\xa60:16;a1Db17c0Ud0Re0Mf0Dg0Ah08i06ju05l02mWnUoSpNrIsBt7u4v1watershed;a1ision0Z;gabo4nilla,ria1;b0Vnt;ndergr1pstairs;adua14ou1;nd;a3e1oken,ri0;en,r1;min0rori13;boo,n;age,e5ilv0Flack,o3quat,ta2u1well;bordina0Xper5;b0Lndard;ciali0Yl1vereign;e,ve16;cret,n1ri0;ior;a4e2ou1ubbiL;nd,tiY;ar,bBl0Wnt0p1side11;resent0Vublican;ci0Qsh;a4eriodic0last0Zotenti0r1;emi2incip0o1;!fession0;er,um;rall4st,tie0U;ff1pposi0Hv0;ens0Oi0C;agg01ov1uts;el;a5e3iniatJo1;bi01der07r1;al,t0;di1tr0N;an,um;le,riG;attOi2u1;sh;ber0ght,qC;stice,veniT;de0mpressioYn1;cumbe0Edividu0no0Dsta0Eterim;alf,o1umdrum;bby,melF;en2old,ra1;ph0Bve;er0ious;a7e5i4l3u1;git03t1;ure;uid;ne;llow,m1;aFiL;ir,t,vo1;riOuriO;l3p00x1;c1ecutUpeV;ess;d1iK;er;ar2e1;mographUrivO;k,l2;hiGlassSo2rude,unn1;ing;m5n1operK;creCstitueOte2vertab1;le;mpor1nt;ary;ic,m2p1;anion,lex;er2u1;ni8;ci0;al;e5lank,o4r1;i2u1;te;ef;ttom,urgeois;st;cadem9d6l2ntarct9r1;ab,ct8;e3tern1;at1;ive;rt;oles1ult;ce1;nt;ic","Adj|Past":"true\xa60:4Q;1:4C;2:4H;3:4E;a44b3Tc36d2Je29f20g1Wh1Si1Jj1Gkno1Fl1Am15n12o0Xp0Mqu0Kr08sLtEuAv9w4yellow0;a7ea6o4rinkl0;r4u3Y;n,ri0;k31th3;rp0sh0tZ;ari0e1O;n5p4s0;d1li1Rset;cov3derstood,i4;fi0t0;a8e3Rhr7i6ouTr4urn0wi4C;a4imm0ou2G;ck0in0pp0;ed,r0;eat2Qi37;m0nn0r4;get0ni2T;aOcKeIhGimFm0Hoak0pDt7u4;bsid3Ogge44s4;pe4ta2Y;ct0nd0;a8e7i2Eok0r5u4;ff0mp0nn0;ength2Hip4;ed,p0;am0reotyp0;in0t0;eci4ik0oH;al3Efi0;pRul1;a4ock0ut;d0r0;a4c1Jle2t31;l0s3Ut0;a6or5r4;at4e25;ch0;r0tt3;t4ut0;is2Mur1;aEe5o4;tt0;cAdJf2Bg9je2l8m0Knew0p7qu6s4;eTpe2t4;or0ri2;e3Dir0;e1lac0;at0e2Q;i0Rul1;eiv0o4ycl0;mme2Lrd0v3;in0lli0ti2A;a4ot0;li28;aCer30iBlAo9r5u4;mp0zzl0;e6i2Oo4;ce2Fd4lo1Anou30pos0te2v0;uc0;fe1CocCp0Iss0;i2Kli1L;ann0e2CuS;ck0erc0ss0;ck0i2Hr4st0;allLk0;bse7c6pp13rgan2Dver4;lo4whelm0;ok0;cupi0;rv0;aJe5o4;t0uri1A;ed0gle2;a6e5ix0o4ut0ys1N;di1Nt15u26;as0Clt0;n4rk0;ag0ufact0A;e6i5o4;ad0ck0st,v0;cens0m04st0;ft,v4;el0;tt0wn;a5o15u4;dg0s1B;gg0;llumSmpAn4sol1;br0cre1Ldebt0f8jZspir0t5v4;it0olv0;e4ox0Y;gr1n4re23;d0si15;e2l1o1Wuri1;li0o01r4;ov0;a6e1o4um03;ok0r4;ri0Z;mm3rm0;i6r5u4;a1Bid0;a0Ui0Rown;ft0;aAe9i8l6oc0Ir4;a4i0oz0Y;ctHg19m0;avo0Ju4;st3;ni08tt0x0;ar0;d0il0sc4;in1;dCl1mBn9quipp0s8x4;agger1c6p4te0T;a0Se4os0;ct0rie1D;it0;cap0tabliZ;cha0XgFha1As4;ur0;a0Zbarra0N;i0Buc1;aMeDi5r4;a01i0;gni08miniSre2s4;a9c6grun0Ft4;o4re0Hu17;rt0;iplWou4;nt0r4;ag0;bl0;cBdRf9l8p7ra6t5v4;elop0ot0;ail0ermQ;ng0;re07;ay0ight0;e4in0o0M;rr0;ay0enTor1;m5t0z4;ed,zl0;ag0p4;en0;aPeLhIlHo9r6u4;lt4r0stom03;iv1;a5owd0u4;sh0;ck0mp0;d0loAm7n4ok0v3;centr1f5s4troC;id3olid1;us0;b5pl4;ic1;in0;r0ur0;assi9os0utt3;ar5i4;ll0;g0m0;lebr1n6r4;ti4;fi0;tralJ;g0lcul1;aDewild3iCl9o7r5urn4;ed,t;ok4uis0;en;il0r0t4und;tl0;e5i4;nd0;ss0;as0;ffl0k0laMs0tt3;bPcNdKfIg0lFmaz0nDppBrm0ss9u5wa4;rd0;g5thor4;iz0;me4;nt0;o6u4;m0r0;li0re4;ci1;im1ticip1;at0;a5leg0t3;er0;rm0;fe2;ct0;ju5o7va4;nc0;st0;ce4knowledg0;pt0;and5so4;rb0;on0;ed",Singular:"true\xa60:5J;1:5H;2:4W;3:4S;4:52;5:57;6:5L;7:56;8:5B;a52b4Lc3Nd35e2Xf2Og2Jh28in24j23k22l1Um1Ln1Ho1Bp0Rqu0Qr0FsZtMuHvCw9x r58yo yo;a9ha3Po3Q;f3i4Rt0Gy9;! arou39;arCeAideo ga2Qo9;cabu4Jl5C;gOr9t;di4Zt1Y;iety,ni4P;nBp30rAs 9;do43s5E;bani1in0;coordinat3Ader9;estima1to24we41; rex,aKeJhHiFoErBuAv9;! show;m2On2rntLto1D;agedy,ib9o4E;e,u9;n0ta46;ni1p2rq3L;c,er,m9;etF;ing9ree26;!y;am,mp3F;ct2le6x return;aNcMeKhor4QiJkHoGpin off,tDuBy9;ll9ner7st4T;ab2X;b9i1n28per bowl,rro1X;st3Ltot0;atAipe2Go1Lrate7udent9;! lo0I;i39u1;ft ser4Lmeo1I;elet5i9;ll,r3V;b38gn2Tte;ab2Jc9min3B;t,urity gua2N;e6ho2Y;bbatic0la3Jndwi0Qpi5;av5eDhetor2iAo9;de6om,w;tAv9;erb2C;e,u0;bDcBf9publ2r10spi1;er9orm3;e6r0;i9ord label;p2Ht0;a1u46;estion mark,ot2F;aPeMhoLiIlGoErAu9yram1F;ddi3HpErpo1Js3J;eBo9;bl3Zs9;pe3Jta1;dic1Rmi1Fp1Qroga8ss relea1F;p9rt0;py;a9ebisci1;q2Dte;cn2eAg9;!gy;!r;ne call,tocoK;anut,dAr9t0yo1;cen3Jsp3K;al,est0;nop4rAt9;e,hog5;adi11i2V;atme0bj3FcBpia1rde0thers,utspok5ve9wn3;n,r9;ti0Pview;cuAe9;an;pi3;arBitAot9umb3;a2Fhi2R;e,ra1;cot2ra8;aFeCiAo9ur0;nopo4p18rni2Nsq1Rti36uld;c,li11n0As9tt5;chief,si34;dAnu,t9;al,i3;al,ic;gna1mm0nd15rsupi0te9yf4;ri0;aDegCiBu9;ddi1n9;ch;me,p09; Be0M;bor14y9; 9er;up;eyno1itt5;el4ourn0;cBdices,itia8ni25sAtel0Lvert9;eb1J;e28titu1;en8i2T;aIeEighDoAu9;man right,s22;me9rmoFsp1Ftb0K;! r9;un; scho0YriY;a9i1N;d9v5; start,pho9;ne;ndful,sh brown,v5ze;aBelat0Ilaci3r9ul4yp1S;an9enadi3id;a1Cd slam,ny;df4r9;l2ni1I;aGeti1HiFlu1oCrAun9;er0;ee market,i9onti3;ga1;l4ur9;so9;me;ePref4;br2mi4;conoFffi7gg,lecto0Rmbas1EnCpidem2s1Zth2venBxAyel9;id;ampZempl0Nte6;i19t;er7terp9;ri9;se;my;eLiEoBr9ump tru0U;agonf4i9;er,ve thru;cAg7i4or,ssi3wn9;side;to0EumenE;aEgniDnn3sAvide9;nd;conte6incen8p9tri11;osi9;ti0C;ta0H;le0X;athBcAf9ni0terre6;ault 05err0;al,im0;!b9;ed;aWeThMiLlJoDr9;edit caBuc9;ib9;le;rd;efficDke,lCmmuniqLnsApi3rr0t0Xus9yo1;in;erv9uI;ato02;ic,lQ;ie6;er7i9oth;e6n2;ty,vil wM;aDeqCick5ocoBr9;istmas car9ysanthemum;ol;la1;ue;ndeli3racteri9;st2;iAllEr9;e0tifica1;liZ;hi3nFpErCt9ucus;erpi9hedr0;ll9;ar;!bohyd9ri3;ra1;it0;aAe,nib0t9;on;l,ry;aMeLiop2leJoHrDu9;nny,r9tterf4;g9i0;la9;ry;eakAi9;ck;fa9throB;st;dy,ro9wl;ugh;mi9;sh;an,l4;nkiArri3;er;ng;cSdMlInFppeti1rDsBtt2utop9;sy;ic;ce6pe9;ct;r9sen0;ay;ecAoma4tiA;ly;do1;i5l9;er7y;gy;en; hominDjAvan9;tage;ec8;ti9;ve;em;cCeAqui9;tt0;ta1;te;iAru0;al;de6;nt","Person|Noun":"true\xa6a0Eb07c03dWeUfQgOhLjHkiGlFmCnBolive,p7r4s3trini06v1wa0;ng,rd,tts;an,enus,iol0;a,et;ky,onPumm09;ay,e1o0uby;bin,d,se;ed,x;a2e1o0;l,tt04;aLnJ;dYge,tR;at,orm;a0eloW;t0x,ya;!s;a9eo,iH;ng,tP;a2e1o0;lGy;an,w3;de,smi4y;a0erb,iOolBuntR;ll,z0;el;ail,e0iLuy;ne;a1ern,i0lo;elds,nn;ith,n0;ny;a0dEmir,ula,ve;rl;a4e3i1j,ol0;ly;ck,x0;ie;an,ja;i0wn;sy;am,h0liff,rystal;a0in,ristian;mbers,ri0;ty;a4e3i2o,r0ud;an0ook;dy;ll;nedict,rg;k0nks;er;l0rt;fredo,ma","Actor|Verb":"true\xa6aCb8c5doctor,engineAfool,g3host,judge,m2nerd,p1recruit,scout,ushAvolunteAwi0;mp,tneA;arent,ilot;an,ime;eek,oof,r0uide;adu8oom;ha1o0;ach,nscript,ok;mpion,uffeur;o2u0;lly,tch0;er;ss;ddi1ffili0rchite1;ate;ct",MaleName:"true\xa60:H6;1:FZ;2:DS;3:GQ;4:CZ;5:FV;6:GM;7:FP;8:GW;9:ET;A:C2;B:GD;aF8bE1cCQdBMeASfA1g8Yh88i7Uj6Sk6Bl5Mm48n3So3Ip33qu31r26s1Et0Ru0Ov0CwTxSyHzC;aCor0;cChC1karia,nAT;!hDkC;!aF6;!ar7CeF5;aJevgenBSoEuC;en,rFVsCu3FvEF;if,uf;nDs6OusC;ouf,s6N;aCg;s,tC;an,h0;hli,nCrosE1ss09;is,nC;!iBU;avi2ho5;aPeNiDoCyaEL;jcieBJlfgang,odrFutR;lFnC;f8TsC;lCt1;ow;bGey,frEhe4QlC;aE5iCy;am,e,s;ed8iC;d,ed;eAur;i,ndeD2rn2sC;!l9t1;lDyC;l1ne;lDtC;!er;aCHy;aKernDAiFladDoC;jteB0lodymyr;!iC;mFQsDB;cFha0ktBZnceDrgCOvC;a0ek;!nC;t,zo;!e4StBV;lCnC7sily;!entC;in9J;ghE2lCm70nax,ri,sm0;riCyss87;ch,k;aWeRhNiLoGrEuDyC;!l2roEDs1;n6r6E;avD0eCist0oy,um0;ntCRvBKy;bFdAWmCny;!asDmCoharu;aFFie,y;!z;iA6y;mCt4;!my,othy;adEeoDia0SomC;!as;!dor91;!de4;dFrC;enBKrC;anBJeCy;ll,nBI;!dy;dgh,ha,iCnn2req,tsu5V;cDAka;aYcotWeThPiMlobod0oKpenc2tEurDvenAEyCzym1;ed,lvest2;aj,e9V;anFeDuC;!aA;fan17phEQvCwaA;e77ie;!islaCl9;v,w;lom1rBuC;leymaDHta;dDgmu9UlCm1yabonga;as,v8B;!dhart8Yn9;aEeClo75;lCrm0;d1t1;h9Jne,qu1Jun,wn,yne;aDbastiEDk2Yl5Mpp,rgCth,ymoCU;e1Dio;m4n;!tC;!ie,y;eDPlFmEnCq67tosCMul;dCj2UtiA5;e01ro;!iATkeB6mC4u5;!ik,vato9K;aZeUheC8iRoGuDyC;an,ou;b99dDf4peAssC;!elEG;ol00y;an,bLc7MdJel,geIh0lHmGnEry,sDyC;!ce;ar7Ocoe,s;!aCnBU;ld,n;an,eo;a7Ef;l7Jr;e3Eg2n9olfo,riC;go;bBNeDH;cCl9;ar87c86h54kCo;!ey,ie,y;cFeA3gDid,ubByCza;an8Ln06;g85iC;naC6s;ep;ch8Kfa5hHin2je8HlGmFndEoHpha5sDul,wi36yC;an,mo8O;h9Im4;alDSol3O;iD0on;f,ph;ul;e9CinC;cy,t1;aOeLhilJiFrCyoG;aDeC;m,st1;ka85v2O;eDoC;tr;r8GtC;er,ro;!ipCl6H;!p6U;dCLrcy,tC;ar,e9JrC;!o7;b9Udra8So9UscAHtri62ulCv8I;!ie,o7;ctav6Ji2lImHndrBRrGsDtCum6wB;is,to;aDc6k6m0vCwaBE;al79;ma;i,vR;ar,er;aDeksandr,ivC;er,i2;f,v;aNeLguyBiFoCu3O;aDel,j4l0ma0rC;beAm0;h,m;cFels,g5i9EkDlC;es,s;!au,h96l78olaC;!i,y;hCkCol76;ol75;al,d,il,ls1vC;ilAF;hom,tC;e,hC;anCy;!a5i5;aYeViLoGuDyC;l4Nr1;hamDr84staC;fa,p6E;ed,mG;di10e,hamEis4JntDritz,sCussa;es,he;e,y;ad,ed,mC;ad,ed;cGgu5hai,kFlEnDtchC;!e8O;a9Pik;house,o7t1;ae73eC3ha8Iolaj;ah,hDkC;!ey,y;aDeC;al,l;el,l;hDlv3rC;le,ri8Ev4T;di,met;ay0c00gn4hWjd,ks2NlTmadZnSrKsXtDuric7VxC;imilBKwe8B;eHhEi69tCus,y69;!eo,hCia7;ew,i67;eDiC;as,eu,s;us,w;j,o;cHiGkFlEqu8Qsha83tCv3;iCy;!m,n;in,on;el,o7us;a6Yo7us;!elCin,o7us;!l8o;frAEi5Zny,u5;achDcoCik;lm;ai,y;amDdi,e5VmC;oud;adCm6W;ou;aulCi9P;ay;aWeOiMloyd,oJuDyC;le,nd1;cFdEiDkCth2uk;a7e;gi,s,z;ov7Cv6Hw6H;!as,iC;a6Een;g0nn52renDuCvA4we7D;!iS;!zo;am,n4oC;n5r;a9Yevi,la5KnHoFst2thaEvC;eCi;nte;bo;nCpo8V;!a82el,id;!nC;aAy;mEnd1rDsz73urenCwr6K;ce,t;ry,s;ar,beAont;aOeIhalHiFla4onr63rDu5SylC;e,s;istCzysztof;i0oph2;er0ngsl9p,rC;ilA9k,ollos;ed,id;en0iGnDrmCv4Z;it;!dDnCt1;e2Ny;ri4Z;r,th;cp2j4mEna8BrDsp6them,uC;ri;im,l;al,il;a03eXiVoFuC;an,lCst3;en,iC;an,en,o,us;aQeOhKkub4AnIrGsDzC;ef;eDhCi9Wue;!ua;!f,ph;dCge;i,on;!aCny;h,s,th6J;anDnC;!ath6Hie,n72;!nC;!es;!l,sCy;ph;o,qu3;an,mC;!i,m6V;d,ffFns,rCs4;a7JemDmai7QoCry;me,ni1H;i9Dy;!e73rC;ey,y;cKdBkImHrEsDvi2yC;dBs1;on,p2;ed,oDrCv67;e6Qod;d,s61;al,es5Wis1;a,e,oCub;b,v;ob,qu13;aTbNchiMgLke53lija,nuKonut,rIsEtCv0;ai,suC;ki;aDha0i8XmaCsac;el,il;ac,iaC;h,s;a,vinCw3;!g;k,nngu6X;nac1Xor;ka;ai,rahC;im;aReLoIuCyd6;beAgGmFsC;eyDsC;a3e3;in,n;ber5W;h,o;m2raDsse3wC;a5Pie;c49t1K;a0Qct3XiGnDrC;beAman08;dr7VrC;iCy2N;!k,q1R;n0Tt3S;bKlJmza,nIo,rEsDyC;a5KdB;an,s0;lEo67r2IuCv9;hi5Hki,tC;a,o;an,ey;k,s;!im;ib;a08e00iUlenToQrMuCyorgy;iHnFsC;!taC;f,vC;!e,o;n6tC;er,h2;do,lC;herDlC;auCerQ;me;aEegCov2;!g,orC;!io,y;dy,h7C;dfr9nza3XrDttfC;ri6C;an,d47;!n;acoGlEno,oCuseppe;rgiCvan6O;!o,s;be6Ies,lC;es;mo;oFrC;aDha4HrC;it,y;ld,rd8;ffErgC;!e7iCy;!os;!r9;bElBrCv3;eCla1Nr4Hth,y;th;e,rC;e3YielC;!i4;aXeSiQlOorrest,rCyod2E;aHedFiC;edDtC;s,z;ri18;!d42eri11riC;ck,k;nCs2;cEkC;ie,lC;in,yn;esLisC;!co,z3M;etch2oC;ri0yd;d5lConn;ip;deriFliEng,rC;dinaCg4nan0B;nd8;pe,x;co;bCdi,hd;iEriC;ce,zC;io;an,en,o;benez2dZfrYit0lTmMnJo3rFsteb0th0ugenEvCymBzra;an,eCge4D;ns,re3K;!e;gi,iDnCrol,v3w3;est8ie,st;cCk;!h,k;o0DriCzo;co,qC;ue;aHerGiDmC;aGe3A;lCrh0;!iC;a10o,s;s1y;nu5;beAd1iEliDm2t1viCwood;n,s;ot28s;!as,j5Hot,sC;ha;a3en;!dGg6mFoDua2QwC;a2Pin;arC;do;oZuZ;ie;a04eTiOmitrNoFrag0uEwDylC;an,l0;ay3Hig4D;a3Gdl9nc0st3;minFnDri0ugCvydGy2S;!lF;!a36nCov0;e1Eie,y;go,iDykC;as;cCk;!k;i,y;armuFetDll1mitri7neCon,rk;sh;er,m6riC;ch;id;andLepak,j0lbeAmetri4nIon,rGsEvDwCxt2;ay30ey;en,in;hawn,moC;nd;ek,riC;ck;is,nC;is,y;rt;re;an,le,mKnIrEvC;e,iC;!d;en,iEne0PrCyl;eCin,yl;l45n;n,o,us;!iCny;el,lo;iCon;an,en,on;a0Fe0Ch03iar0lRoJrFuDyrC;il,us;rtC;!is;aEistC;iaCob12;no;ig;dy,lInErC;ey,neliCy;s,us;nEor,rDstaC;nt3;ad;or;by,e,in,l3t1;aHeEiCyde;fCnt,ve;fo0Xt1;menDt4;us;s,t;rFuDyC;!t1;dCs;e,io;enC;ce;aHeGrisC;!toC;phCs;!eC;!r;st2t;d,rCs;b5leC;s,y;cDdrCs6;ic;il;lHmFrC;ey,lDroCy;ll;!o7t1;er1iC;lo;!eb,v3;a09eZiVjorn,laUoSrEuCyr1;ddy,rtKst2;er;aKeFiEuDyC;an,ce,on;ce,no;an,ce;nDtC;!t;dDtC;!on;an,on;dFnC;dDisC;lav;en,on;!foOl9y;bby,gd0rCyd;is;i0Lke;bElDshC;al;al,lL;ek;nIrCshoi;at,nEtC;!raC;m,nd;aDhaCie;rd;rd8;!iDjam3nCs1;ie,y;to;kaMlazs,nHrC;n9rDtC;!holomew;eCy;tt;ey;dCeD;ar,iC;le;ar1Nb1Dd16fon15gust3hm12i0Zja0Yl0Bm07nTputsiSrGsaFugustEveDyCziz;a0kh0;ry;o,us;hi;aMchiKiJjun,mHnEon,tCy0;em,hCie,ur8;ur;aDoC;!ld;ud,v;aCin;an,nd8;!el,ki;baCe;ld;ta;aq;aMdHgel8tCw6;hoFoC;iDnC;!i8y;ne;ny;er7rCy;eDzC;ej;!as,i,j,s,w;!s;s,tolC;iCy;!y;ar,iEmaCos;nu5r;el;ne,r,t;aVbSdBeJfHiGl01onFphonsEt1vC;aPin;on;e,o;so,zo;!sR;!onZrC;ed;c,jaHksFssaHxC;!andC;er,rC;e,os,u;andCei;ar,er,r;ndC;ro;en;eDrecC;ht;rt8;dd3in,n,sC;taC;ir;ni;dDm6;ar;an,en;ad,eC;d,t;in;so;aGi,olErDvC;ik;ian8;f8ph;!o;mCn;!a;dGeFraDuC;!bakr,lfazl;hCm;am;!l;allFel,oulaye,ulC;!lDrahm0;an;ah,o;ah;av,on",Uncountable:"true\xa60:2E;1:2L;2:33;a2Ub2Lc29d22e1Rf1Ng1Eh16i11j0Yk0Wl0Rm0Hn0Do0Cp03rZsLt9uran2Jv7w3you gu0E;a5his17i4oo3;d,l;ldlife,ne;rm8t1;apor,ernacul29i3;neg28ol1Otae;eDhBiAo8r4un3yranny;a,gst1B;aff2Oea1Ko4ue nor3;th;o08u3;bleshoot2Ose1Tt;night,othpas1Vwn3;foEsfoE;me off,n;er3und1;e,mod2S;a,nnis;aDcCeBhAi9ki8o7p6t4u3weepstak0;g1Unshi2Hshi;ati08e3;am,el;ace2Keci0;ap,cc1meth2C;n,ttl0;lk;eep,ingl0or1C;lf,na1Gri0;ene1Kisso1C;d0Wfe2l4nd,t3;i0Iurn;m1Ut;abi0e4ic3;e,ke15;c3i01laxa11search;ogni10rea10;a9e8hys7luto,o5re3ut2;amble,mis0s3ten20;en1Zs0L;l3rk;i28l0EyH; 16i28;a24tr0F;nt3ti0M;i0s;bstetri24vercrowd1Qxyg09;a5e4owada3utella;ys;ptu1Ows;il poliZtional securi2;aAe8o5u3;m3s1H;ps;n3o1K;ey,o3;gamy;a3cha0Elancholy,rchandi1Htallurgy;sl0t;chine3g1Aj1Hrs,thema1Q; learn1Cry;aught1e6i5ogi4u3;ck,g12;c,s1M;ce,ghtn18nguis1LteratWv1;ath1isVss;ara0EindergartPn3;icke0Aowled0Y;e3upit1;a3llyfiGwel0G;ns;ce,gnor6mp5n3;forma00ter3;net,sta07;atiSort3rov;an18;a7e6isto09o3ung1;ckey,mework,ne4o3rseradi8spitali2use arrest;ky;s2y;adquarteXre;ir,libut,ppiHs3;hi3te;sh;ene8l6o5r3um,ymnas11;a3eZ;niUss;lf,re;ut3yce0F;en; 3ti0W;edit0Hpo3;ol;aNicFlour,o4urnit3;ure;od,rgive3uri1wl;ness;arCcono0LducaBlectr9n7quip8thi0Pvery6x3;ist4per3;ti0B;en0J;body,o08th07;joy3tertain3;ment;ici2o3;ni0H;tiS;nings,th;emi02i6o4raugh3ynas2;ts;pe,wnstai3;rs;abet0ce,s3;honZrepu3;te;aDelciChAivi07l8o3urrency;al,ld w6mmenta5n3ral,ttIuscoB;fusiHt 3;ed;ry;ar;assi01oth0;es;aos,e3;eMwK;us;d,rO;a8i6lood,owlHread5u3;ntGtt1;er;!th;lliarJs3;on;g3ss;ga3;ge;cKdviJeroGirFmBn6ppeal court,r4spi3thleL;rin;ithmet3sen3;ic;i6y3;o4th3;ing;ne;se;en5n3;es2;ty;ds;craft;bi8d3nau7;yna3;mi6;ce;id,ous3;ti3;cs",Infinitive:"true\xa60:9G;1:9T;2:AD;3:90;4:9Z;5:84;6:AH;7:A9;8:92;9:A0;A:AG;B:AI;C:9V;D:8R;E:8O;F:97;G:6H;H:7D;a94b8Hc7Jd68e4Zf4Mg4Gh4Ai3Qj3Nk3Kl3Bm34nou48o2Vp2Equ2Dr1Es0CtZuTvRwI;aOeNiLors5rI;eJiI;ng,te;ak,st3;d5e8TthI;draw,er;a2d,ep;i2ke,nIrn;d1t;aIie;liADniAry;nJpI;ho8Llift;cov1dJear8Hfound8DlIplug,rav82tie,ve94;eaAo3X;erIo;cut,go,staAFvalA3w2G;aSeQhNoMrIu73;aIe72;ffi3Smp3nsI;aBfo7CpI;i8oD;pp3ugh5;aJiJrIwaD;eat5i2;nk;aImA0;ch,se;ck3ilor,keImp1r8L;! paD;a0Ic0He0Fh0Bi0Al08mugg3n07o05p02qu01tUuLwI;aJeeIim;p,t5;ll7Wy;bNccMffLggeCmmKppJrI;mouFpa6Zvi2;o0re6Y;ari0on;er,i4;e7Numb;li9KmJsiIveD;de,st;er9it;aMe8MiKrI;ang3eIi2;ng27w;fIng;f5le;b,gg1rI;t3ve;a4AiA;a4UeJit,l7DoI;il,of;ak,nd;lIot7Kw;icEve;atGeak,i0O;aIi6;m,y;ft,ng,t;aKi6CoJriIun;nk,v6Q;ot,rt5;ke,rp5tt1;eIll,nd,que8Gv1w;!k,m;aven9ul8W;dd5tis1Iy;a0FeKiJoI;am,t,ut;d,p5;a0Ab08c06d05f01group,hea00iZjoi4lXmWnVpTq3MsOtMup,vI;amp,eJiIo3B;sEve;l,rI;e,t;i8rI;ie2ofE;eLiKpo8PtIurfa4;o24rI;aHiBuctu8;de,gn,st;mb3nt;el,hra0lIreseF;a4e71;d1ew,o07;aHe3Fo2;a7eFiIo6Jy;e2nq41ve;mbur0nf38;r0t;inKleBocus,rJuI;el,rbiA;aBeA;an4e;aBu4;ei2k8Bla43oIyc3;gni39nci3up,v1;oot,uI;ff;ct,d,liIp;se,ze;tt3viA;aAenGit,o7;aWerUinpoiFlumm1LoTrLuI;b47ke,niArIt;poDsuI;aFe;eMoI;cKd,fe4XhibEmo7noJpo0sp1tru6vI;e,i6o5L;un4;la3Nu8;aGclu6dJf1occupy,sup0JvI;a6BeF;etermi4TiB;aGllu7rtr5Ksse4Q;cei2fo4NiAmea7plex,sIva6;eve8iCua6;mp1rItrol,ve;a6It6E;bOccuNmEpMutLverIwe;l07sJtu6Yu0wI;helm;ee,h1F;gr5Cnu2Cpa4;era7i4Ipo0;py,r;ey,seItaH;r2ss;aMe0ViJoIultiply;leCu6Pw;micJnIspla4;ce,g3us;!k;iIke,na9;m,ntaH;aPeLiIo0u3N;ke,ng1quIv5;eIi6S;fy;aKnIss5;d,gI;th5;rn,ve;ng2Gu1N;eep,idnJnI;e4Cow;ap;oHuI;gg3xtaI;po0;gno8mVnIrk;cTdRfQgeChPitia7ju8q1CsNtKun6EvI;a6eIo11;nt,rt,st;erJimi6BoxiPrI;odu4u6;aBn,pr03ru6C;iCpi8tIu8;all,il,ruB;abEibE;eCo3Eu0;iIul9;ca7;i7lu6;b5Xmer0pI;aLer4Uin9ly,oJrI;e3Ais6Bo2;rt,se,veI;riA;le,rt;aLeKiIoiCuD;de,jaInd1;ck;ar,iT;mp1ng,pp5raIve;ng5Mss;ath1et,iMle27oLrI;aJeIow;et;b,pp3ze;!ve5A;gg3ve;aTer45i5RlSorMrJuI;lf4Cndrai0r48;eJiIolic;ght5;e0Qsh5;b3XeLfeEgJsI;a3Dee;eIi2;!t;clo0go,shIwa4Z;ad3F;att1ee,i36;lt1st5;a0OdEl0Mm0FnXquip,rWsVtGvTxI;aRcPeDhOiNpJtIu6;ing0Yol;eKi8lIo0un9;aHoI;it,re;ct,di7l;st,t;a3oDu3B;e30lI;a10u6;lt,mi28;alua7oI;ke,l2;chew,pou0tab19;a0u4U;aYcVdTfSgQhan4joy,lPqOrNsuMtKvI;e0YisI;a9i50;er,i4rI;aHenGuC;e,re;iGol0F;ui8;ar9iC;a9eIra2ulf;nd1;or4;ang1oIu8;r0w;irc3lo0ou0ErJuI;mb1;oaGy4D;b3ct;bKer9pI;hasiIow1;ze;aKody,rI;a4oiI;d1l;lm,rk;ap0eBuI;ci40de;rIt;ma0Rn;a0Re04iKo,rIwind3;aw,ed9oI;wn;agno0e,ff1g,mi2Kne,sLvI;eIul9;rIst;ge,t;aWbVcQlod9mant3pNru3TsMtI;iIoDu37;lJngI;uiA;!l;ol2ua6;eJlIo0ro2;a4ea0;n0r0;a2Xe36lKoIu0S;uIv1;ra9;aIo0;im;a3Kur0;b3rm;af5b01cVduBep5fUliTmQnOpMrLsiCtaGvI;eIol2;lop;ch;a20i2;aDiBloIoD;re,y;oIy;te,un4;eJoI;liA;an;mEv1;a4i0Ao06raud,y;ei2iMla8oKrI;ee,yI;!pt;de,mIup3;missi34po0;de,ma7ph1;aJrief,uI;g,nk;rk;mp5rk5uF;a0Dea0h0Ai09l08oKrIurta1G;a2ea7ipp3uI;mb3;ales4e04habEinci6ll03m00nIrro6;cXdUfQju8no7qu1sLtKvI;eIin4;ne,r9y;aHin2Bribu7;er2iLoli2Epi8tJuI;lt,me;itu7raH;in;d1st;eKiJoIroFu0;rm;de,gu8rm;ss;eJoI;ne;mn,n0;eIlu6ur;al,i2;buCe,men4pI;eIi3ly;l,te;eBi6u6;r4xiC;ean0iT;rcumveFte;eJirp,oI;o0p;riAw;ncIre5t1ulk;el;a02eSi6lQoPrKuI;iXrIy;st,y;aLeaKiJoad5;en;ng;stfeLtX;ke;il,l11mba0WrrMth1;eIow;ed;!coQfrie1LgPhMliLqueaKstJtrIwild1;ay;ow;th;e2tt3;a2eJoI;ld;ad;!in,ui3;me;bysEckfi8ff3tI;he;b15c0Rd0Iff0Ggree,l0Cm09n03ppZrXsQttOuMvJwaE;it;eDoI;id;rt;gIto0X;meF;aIeCraB;ch,in;pi8sJtoI;niA;aKeIi04u8;mb3rt,ss;le;il;re;g0Hi0ou0rI;an9i2;eaKly,oiFrI;ai0o2;nt;r,se;aMi0GnJtI;icipa7;eJoIul;un4y;al;ly0;aJu0;se;lga08ze;iKlI;e9oIu6;t,w;gn;ix,oI;rd;a03jNmiKoJsoI;rb;pt,rn;niIt;st1;er;ouJuC;st;rn;cLhie2knowled9quiItiva7;es4re;ce;ge;eQliOoKrJusI;e,tom;ue;mIst;moJpI;any,liA;da7;ma7;te;pt;andPduBet,i6oKsI;coKol2;ve;liArt,uI;nd;sh;de;ct;on",Person:"true\xa60:1Q;a29b1Zc1Md1Ee18f15g13h0Ri0Qj0Nk0Jl0Gm09n06o05p00rPsItCusain bolt,v9w4xzibit,y1;anni,oko on2uji,v1;an,es;en,o;a3ednesday adams,i2o1;lfram,o0Q;ll ferrell,z khalifa;lt disn1Qr1;hol,r0G;a2i1oltai06;n dies0Zrginia wo17;lentino rossi,n goG;a4h3i2ripp,u1yra banks;lZpac shakur;ger woods,mba07;eresa may,or;kashi,t1ylor;um,ya1B;a5carlett johanss0h4i3lobodan milosevic,no2ocr1Lpider1uperm0Fwami; m0Em0E;op dogg,w whi1H;egfried,nbad;akespeaTerlock holm1Sia labeouf;ddam hussa16nt1;a cla11ig9;aAe6i5o3u1za;mi,n dmc,paul,sh limbau1;gh;bin hood,d stew16nald1thko;in0Mo;han0Yngo starr,valdo;ese witherspo0i1mbrandt;ll2nh1;old;ey,y;chmaninoff,ffi,iJshid,y roma1H;a4e3i2la16o1uff daddy;cahont0Ie;lar,p19;le,rZ;lm17ris hilt0;leg,prah winfr0Sra;a2e1iles cra1Bostradam0J; yo,l5tt06wmQ;pole0s;a5e4i2o1ubar03;by,lie5net,rriss0N;randa ju1tt romn0M;ly;rl0GssiaB;cklemo1rkov,s0ta hari,ya angelou;re;ady gaga,e1ibera0Pu;bron jam0Xch wale1e;sa;anye west,e3i1obe bryant;d cudi,efer suther1;la0P;ats,sha;a2effers0fk,k rowling,rr tolki1;en;ck the ripp0Mwaharlal nehru,y z;liTnez,ron m7;a7e5i3u1;lk hog5mphrey1sa01;! bog05;l1tl0H;de; m1dwig,nry 4;an;ile selassFlle ber4m3rrison1;! 1;ford;id,mo09;ry;ast0iannis,o1;odwPtye;ergus0lorence nightinga08r1;an1ederic chopN;s,z;ff5m2nya,ustaXzeki1;el;eril lagasse,i1;le zatop1nem;ek;ie;a6e4i2octor w1rake;ho;ck w1ego maradoC;olf;g1mi lovaOnzel washingt0;as;l1nHrth vadR;ai lNt0;a8h5lint0o1thulhu;n1olio;an,fuci1;us;on;aucKop2ristian baMy1;na;in;millo,ptain beefhe4r1;dinal wols2son1;! palmF;ey;art;a8e5hatt,i3oHro1;ck,n1;te;ll g1ng crosby;atB;ck,nazir bhut2rtil,yon1;ce;to;nksy,rack ob1;ama;l 6r3shton kutch2vril lavig8yn ra1;nd;er;chimed2istot1;le;es;capo2paci1;no;ne",Adjective:"true\xa60:AI;1:BS;2:BI;3:BA;4:A8;5:84;6:AV;7:AN;8:AF;9:7H;A:BQ;B:AY;C:BC;D:BH;E:9Y;aA2b9Ec8Fd7We79f6Ng6Eh61i4Xj4Wk4Tl4Im41n3Po36p2Oquart7Pr2Ds1Dt14uSvOwFye29;aMeKhIiHoF;man5oFrth7G;dADzy;despreB1n w97s86;acked1UoleF;!sa6;ather1PeFll o70ste1D;!k5;nt1Ist6Ate4;aHeGiFola5T;bBUce versa,gi3Lle;ng67rsa5R;ca1gBSluAV;lt0PnLpHrGsFttermoBL;ef9Ku3;b96ge1; Hb32pGsFtiAH;ca6ide d4R;er,i85;f52to da2;a0Fbeco0Hc0Bd04e02f01gu1XheaBGiXkn4OmUnTopp06pRrNsJtHus0wF;aFiel3K;nt0rra0P;app0eXoF;ld,uS;eHi37o5ApGuF;perv06spec39;e1ok9O;en,ttl0;eFu5;cogn06gul2RlGqu84sF;erv0olv0;at0en33;aFrecede0E;id,rallel0;am0otic0;aFet;rri0tF;ch0;nFq26vers3;sur0terFv7U;eFrupt0;st0;air,inish0orese98;mploy0n7Ov97xpF;ect0lain0;eHisFocume01ue;clFput0;os0;cid0rF;!a8Scov9ha8Jlyi8nea8Gprivileg0sMwF;aFei9I;t9y;hGircumcFonvin2U;is0;aFeck0;lleng0rt0;b20ppea85ssuGttend0uthorF;iz0;mi8;i4Ara;aLeIhoHip 25oGrF;anspare1encha1i2;geth9leADp notch,rpB;rny,ugh6H;ena8DmpGrFs6U;r49tia4;eCo8P;leFst4M;nt0;a0Dc09e07h06i04ki03l01mug,nobbi4XoVpRqueami4XtKuFymb94;bHccinAi generis,pFr5;erFre7N;! dup9b,vi70;du0li7Lp6IsFurb7J;eq9Atanda9X;aKeJi16o2QrGubboFy4Q;rn;aightFin5GungS; fFfF;or7V;adfa9Pri6;lwa6Ftu82;arHeGir6NlendBot Fry;on;c3Qe1S;k5se; call0lImb9phistic16rHuFviV;ndFth1B;proof;dBry;dFub6; o2A;e60ipF;pe4shod;ll0n d7R;g2HnF;ceEg6ist9;am3Se9;co1Zem5lfFn6Are7; suf4Xi43;aGholFient3A;ar5;rlFt4A;et;cr0me,tisfac7F;aOeIheumatoBiGoF;bu8Ztt7Gy3;ghtFv3; 1Sf6X;cJdu8PlInown0pro69sGtF;ard0;is47oF;lu2na1;e1Suc45;alcit8Xe1ondi2;bBci3mpa1;aSePicayu7laOoNrGuF;bl7Tnjabi;eKiIoF;b7VfGmi49pFxi2M;er,ort81;a7uD;maFor,sti7va2;!ry;ciDexis0Ima2CpaB;in55puli8G;cBid;ac2Ynt 3IrFti2;ma40tFv7W;!i3Z;i2YrFss7R;anoBtF; 5XiF;al,s5V;bSffQkPld OnMrLth9utKverF;!aIbMdHhGni75seas,t,wF;ei74rou74;a63e7A;ue;ll;do1Ger,si6A;d3Qg2Aotu5Z; bFbFe on o7g3Uli7;oa80;fashion0school;!ay; gua7XbFha5Uli7;eat;eHligGsF;ce7er0So1C;at0;diFse;a1e1;aOeNiMoGuF;anc0de; moEnHrthFt6V;!eFwe7L;a7Krn;chaGdescri7Iprof30sF;top;la1;ght5;arby,cessa4ighbor5wlyw0xt;k0usiaFv3;ti8;aQeNiLoHuF;dIltiF;facet0p6;deHlGnFot,rbBst;ochro4Xth5;dy;rn,st;ddle ag0nF;dbloZi,or;ag9diocEga,naGrFtropolit4Q;e,ry;ci8;cIgenta,inHj0Fkeshift,mmGnFri4Oscu61ver18;da5Dy;ali4Lo4U;!stream;abEho;aOeLiIoFumberi8;ngFuti1R;stan3RtF;erm,i4H;ghtGteraF;l,ry,te;heart0wei5O;ft JgFss9th3;al,eFi0M;nda4;nguBps0te5;apGind5noF;wi8;ut;ad0itte4uniW;ce co0Hgno6Mll0Cm04nHpso 2UrF;a2releF;va1; ZaYcoWdReQfOgrNhibi4Ri05nMoLsHtFvalu5M;aAeF;nDrdepe2K;a7iGolFuboI;ub6ve1;de,gF;nifica1;rdi5N;a2er;own;eriIiLluenVrF;ar0eq5H;pt,rt;eHiGoFul1O;or;e,reA;fiFpe26termi5E;ni2;mpFnsideCrreA;le2;ccuCdeq5Ene,ppr4J;fFsitu,vitro;ro1;mJpF;arHeGl15oFrop9;li2r11;n2LrfeA;ti3;aGeFi18;d4BnD;tuE;egGiF;c0YteC;al,iF;tiF;ma2;ld;aOelNiLoFuma7;a4meInHrrGsFur5;ti6;if4E;e58o3U; ma3GsF;ick;ghfalut2HspF;an49;li00pf33;i4llow0ndGrdFtM; 05coEworki8;sy,y;aLener44iga3Blob3oKrGuF;il1Nng ho;aFea1Fizzl0;cGtF;ef2Vis;ef2U;ld3Aod;iFuc2D;nf2R;aVeSiQlOoJrF;aGeFil5ug3;q43tf2O;gFnt3S;i6ra1;lk13oHrF; keeps,eFge0Vm9tu41;g0Ei2Ds3R;liF;sh;ag4Mowe4uF;e1or45;e4nF;al,i2;d Gmini7rF;ti6ve1;up;bl0lDmIr Fst pac0ux;oGreacF;hi8;ff;ed,ili0R;aXfVlTmQnOqu3rMthere3veryday,xF;aApIquisi2traHuF;be48lF;ta1;!va2L;edRlF;icF;it;eAstF;whi6; Famor0ough,tiE;rou2sui2;erGiF;ne1;ge1;dFe2Aoq34;er5;ficF;ie1;g9sF;t,ygF;oi8;er;aWeMiHoGrFue;ea4owY;ci6mina1ne,r31ti8ubQ;dact2Jfficult,m,sGverF;ge1se;creGePjoi1paCtF;a1inA;et,te; Nadp0WceMfiLgeneCliJmuEpeIreliAsGvoF;id,ut;pFtitu2ul1L;eCoF;nde1;ca2ghF;tf13;a1ni2;as0;facto;i5ngero0I;ar0Ce09h07i06l05oOrIuF;rmudgeon5stoma4teF;sy;ly;aIeHu1EystalF; cleFli7;ar;epy;fFv17z0;ty;erUgTloSmPnGrpoCunterclVveFy;rt;cLdJgr21jIsHtrF;aFi2;dic0Yry;eq1Yta1;oi1ug3;escenFuN;di8;a1QeFiD;it0;atoDmensuCpF;ass1SulF;so4;ni3ss3;e1niza1;ci1J;ockwiD;rcumspeAvil;eFintzy;e4wy;leGrtaF;in;ba2;diac,ef00;a00ePiLliJoGrFuck nak0;and new,isk,on22;gGldface,naF; fi05fi05;us;nd,tF;he;gGpartisFzarE;an;tiF;me;autifOhiNlLnHsFyoN;iWtselF;li8;eGiFt;gn;aFfi03;th;at0oF;v0w;nd;ul;ckwards,rF;e,rT; priori,b13c0Zd0Tf0Ng0Ihe0Hl09mp6nt06pZrTsQttracti0MuLvIwF;aGkF;wa1B;ke,re;ant garGeraF;ge;de;diIsteEtF;heFoimmu7;nt07;re;to4;hGlFtu2;eep;en;bitIchiv3roHtF;ifiFsy;ci3;ga1;ra4;ry;pFt;aHetizi8rF;oprF;ia2;llFre1;ed,i8;ng;iquFsy;at0e;ed;cohKiJkaHl,oGriFterX;ght;ne,of;li7;ne;ke,ve;olF;ic;ad;ain07gressiIi6rF;eeF;ab6;le;ve;fGraB;id;ectGlF;ue1;ioF;na2; JaIeGvF;erD;pt,qF;ua2;ma1;hoc,infinitum;cuCquiGtu3u2;al;esce1;ra2;erSjeAlPoNrKsGuF;nda1;e1olu2trF;aAuD;se;te;eaGuF;pt;st;aFve;rd;aFe;ze;ct;ra1;nt",Pronoun:"true\xa6elle,h3i2me,she,th0us,we,you;e0ou;e,m,y;!l,t;e,im",Preposition:"true\xa6aPbMcLdKexcept,fIinGmid,notwithstandiWoDpXqua,sCt7u4v2w0;/o,hereSith0;! whHin,oW;ersus,i0;a,s a vis;n1p0;!on;like,til;h1ill,oward0;!s;an,ereby,r0;ough0u;!oM;ans,ince,o that,uch G;f1n0ut;!to;!f;! 0to;effect,part;or,r0;om;espite,own,u3;hez,irca;ar1e0oBy;sides,tween;ri7;bo8cross,ft7lo6m4propos,round,s1t0;!op;! 0;a whole,long 0;as;id0ong0;!st;ng;er;ut",SportsTeam:"true\xa60:18;1:1E;2:1D;3:14;a1Db15c0Sd0Kfc dallas,g0Ihouston 0Hindiana0Gjacksonville jagua0k0El0Am01new UoRpKqueens parkJreal salt lake,sBt6utah jazz,vancouver whitecaps,w4yW;ashington 4h10;natio1Mredski2wizar0W;ampa bay 7e6o4;ronto 4ttenham hotspur;blue ja0Mrapto0;nnessee tita2xasD;buccanee0ra0K;a8eattle 6porting kansas0Wt4; louis 4oke0V;c1Drams;marine0s4;eah13ounH;cramento Rn 4;antonio spu0diego 4francisco gJjose earthquak1;char08paB; ran07;a9h6ittsburgh 5ortland t4;imbe0rail blaze0;pirat1steele0;il4oenix su2;adelphia 4li1;eagl1philNunE;dr1;akland 4klahoma city thunder,rlando magic;athle0Lrai4;de0;england 8orleans 7york 4;g5je3knYme3red bul0Xy4;anke1;ian3;pelica2sain3;patrio3revolut4;ion;anchEeAi4ontreal impact;ami 8lwaukee b7nnesota 4;t5vi4;kings;imberwolv1wi2;rewe0uc0J;dolphi2heat,marli2;mphis grizz4ts;li1;a6eic5os angeles 4;clippe0dodFlaB;esterV; galaxy,ke0;ansas city 4nF;chiefs,roya0D; pace0polis col3;astr05dynamo,rocke3texa2;olden state warrio0reen bay pac4;ke0;allas 8e4i04od6;nver 6troit 4;lio2pisto2ti4;ge0;broncYnugge3;cowbo5maver4;icZ;ys;arEelLhAincinnati 8leveland 6ol4;orado r4umbus crew sc;api7ocki1;brow2cavalie0guar4in4;dia2;bengaVre4;ds;arlotte horAicago 4;b5cubs,fire,wh4;iteB;ea0ulQ;diff4olina panthe0; city;altimore Alackburn rove0oston 6rooklyn 4uffalo bilN;ne3;ts;cel5red4; sox;tics;rs;oriol1rave2;rizona Ast8tlanta 4;brav1falco2h4;awA;ns;es;on villa,r4;os;c6di4;amondbac4;ks;ardi4;na4;ls",Unit:"true\xa6a07b04cXdWexVfTgRhePinYjoule0BkMlJmDnan08oCp9quart0Bsq ft,t7volts,w6y2ze3\xb01\xb50;g,s;c,f,n;dVear1o0;ttR; 0s 0;old;att,b;erNon0;!ne02;ascals,e1i0;cXnt00;rcent,tJ;hms,unceY;/s,e4i0m\xb2,\xb2,\xb3;/h,cro2l0;e0liK;!\xb2;grLsR;gCtJ;it1u0;menQx;erPreP;b5elvins,ilo1m0notO;/h,ph,\xb2;!byGgrEmCs;ct0rtzL;aJogrC;allonJb0ig3rB;ps;a0emtEl oz,t4;hrenheit,radG;aby9;eci3m1;aratDe1m0oulombD;\xb2,\xb3;lsius,nti0;gr2lit1m0;et0;er8;am7;b1y0;te5;l,ps;c2tt0;os0;econd1;re0;!s","Noun|Gerund":"true\xa60:3O;1:3M;2:3N;3:3D;4:32;5:2V;6:3E;7:3K;8:36;9:3J;A:3B;a3Pb37c2Jd27e23f1Vg1Sh1Mi1Ij1Gk1Dl18m13n11o0Wp0Pques0Sr0EsTtNunderMvKwFyDzB;eroi0oB;ni0o3P;aw2eB;ar2l3;aEed4hispe5i5oCrB;ap8est3i1;n0ErB;ki0r31;i1r2s9tc9;isualizi0oB;lunt1Vti0;stan4ta6;aFeDhin6iCraBy8;c6di0i2vel1M;mi0p8;aBs1;c9si0;l6n2s1;aUcReQhOiMkatKl2Wmo6nowJpeItFuCwB;ea5im37;b35f0FrB;fi0vB;e2Mi2J;aAoryt1KrCuB;d2KfS;etc9ugg3;l3n4;bCi0;ebBi0;oar4;gnBnAt1;a3i0;ip8oB;p8rte2u1;a1r27t1;hCo5reBulp1;a2Qe2;edu3oo3;i3yi0;aKeEi4oCuB;li0n2;oBwi0;fi0;aFcEhear7laxi0nDpor1sB;pon4tructB;r2Iu5;de5;or4yc3;di0so2;p8ti0;aFeacek20laEoCrBublis9;a1Teten4in1oces7;iso2siB;tio2;n2yi0;ckaAin1rB;ki0t1O;fEpeDrganiCvB;erco24ula1;si0zi0;ni0ra1;fe5;avi0QeBur7;gotia1twor6;aDeCi2oB;de3nito5;a2dita1e1ssaA;int0XnBrke1;ifUufactu5;aEeaDiBodAyi0;cen7f1mi1stB;e2i0;r2si0;n4ug9;iCnB;ea4it1;c6l3;ogAuB;dAgg3stif12;ci0llust0VmDnBro2;nova1sp0NterBven1;ac1vie02;agi2plo4;aDea1iCoBun1;l4w3;ki0ri0;nd3rB;roWvB;es1;aCene0Lli4rBui4;ee1ie0N;rde2the5;aHeGiDlCorBros1un4;e0Pmat1;ir1oo4;gh1lCnBs9;anZdi0;i0li0;e3nX;r0Zscina1;a1du01nCxB;erci7plo5;chan1di0ginB;ee5;aLeHiGoub1rCum8wB;el3;aDeCiB;bb3n6vi0;a0Qs7;wi0;rTscoDvi0;ba1coZlBvelo8;eCiB;ve5;ga1;nGti0;aVelebUhSlPoDrBur3yc3;aBos7yi0;f1w3;aLdi0lJmFnBo6pi0ve5;dDsCvinB;ci0;trBul1;uc1;muniDpB;lBo7;ai2;ca1;lBo5;ec1;c9ti0;ap8eaCimToBubT;ni0t9;ni0ri0;aBee5;n1t1;ra1;m8rCs1te5;ri0;vi0;aPeNitMlLoGrDuB;dge1il4llBr8;yi0;an4eat9oadB;cas1;di0;a1mEokB;i0kB;ee8;pi0;bi0;es7oa1;c9i0;gin2lonAt1;gi0;bysit1c6ki0tt3;li0;ki0;bando2cGdverti7gi0pproac9rgDssuCtB;trac1;mi0;ui0;hi0;si0;coun1ti0;ti0;ni0;ng",PhrasalVerb:"true\xa60:92;1:96;2:8H;3:8V;4:8A;5:83;6:85;7:98;8:90;9:8G;A:8X;B:8R;C:8U;D:8S;E:70;F:97;G:8Y;H:81;I:7H;J:79;a9Fb7Uc6Rd6Le6Jf5Ig50h4Biron0j47k40l3Em31n2Yo2Wp2Cquiet Hr1Xs0KtZuXvacuu6QwNyammerBzK;ero Dip LonK;e0k0;by,ov9up;aQeMhLiKor0Mrit19;mp0n3Fpe0r5s5;ackAeel Di0S;aLiKn33;gh 3Wrd0;n Dr K;do1in,oJ;it 79k5lk Lrm 69sh Kt83v60;aw3do1o7up;aw3in,oC;rgeBsK;e 2herE;a00eYhViRoQrMuKypP;ckErn K;do1in,oJup;aLiKot0y 30;ckl7Zp F;ck HdK;e 5Y;n7Wp 3Es5K;ck MdLe Kghten 6me0p o0Rre0;aw3ba4do1in,up;e Iy 2;by,oG;ink Lrow K;aw3ba4in,up;ba4ov9up;aKe 77ll62;m 2r 5M;ckBke Llk K;ov9shit,u47;aKba4do1in,leave,o4Dup;ba4ft9pa69w3;a0Vc0Te0Mh0Ii0Fl09m08n07o06p01quar5GtQuOwK;earMiK;ngLtch K;aw3ba4o8K; by;cKi6Bm 2ss0;k 64;aReQiPoNrKud35;aigh2Det75iK;ke 7Sng K;al6Yup;p Krm2F;by,in,oG;c3Ln3Lr 2tc4O;p F;c3Jmp0nd LrKveAy 2O;e Ht 2L;ba4do1up;ar3GeNiMlLrKurB;ead0ingBuc5;a49it 6H;c5ll o3Cn 2;ak Fe1Xll0;a3Bber 2rt0und like;ap 5Vow Duggl5;ash 6Noke0;eep NiKow 6;cLp K;o6Dup;e 68;in,oK;ff,v9;de19gn 4NnKt 6Gz5;gKkE; al6Ale0;aMoKu5W;ot Kut0w 7M;aw3ba4f48oC;c2WdeEk6EveA;e Pll1Nnd Orv5tK; Ktl5J;do1foLin,o7upK;!on;ot,r5Z;aw3ba4do1in,o33up;oCto;al66out0rK;ap65ew 6J;ilAv5;aXeUiSoOuK;b 5Yle0n Kstl5;aLba4do1inKo2Ith4Nu5P;!to;c2Xr8w3;ll Mot LpeAuK;g3Ind17;a2Wf3Po7;ar8in,o7up;ng 68p oKs5;ff,p18;aKelAinEnt0;c6Hd K;o4Dup;c27t0;aZeYiWlToQrOsyc35uK;ll Mn5Kt K;aKba4do1in,oJto47up;pa4Dw3;a3Jdo1in,o21to45up;attleBess KiNop 2;ah2Fon;iLp Kr4Zu1Gwer 6N;do1in,o6Nup;nt0;aLuK;gEmp 6;ce u20y 6D;ck Kg0le 4An 6p5B;oJup;el 5NncilE;c53ir 39n0ss MtLy K;ba4oG; Hc2R;aw3ba4in,oJ;pKw4Y;e4Xt D;aLerd0oK;dAt53;il Hrrow H;aTeQiPoLuK;ddl5ll I;c1FnkeyMp 6uthAve K;aKdo1in,o4Lup;l4Nw3; wi4K;ss0x 2;asur5e3SlLss K;a21up;t 6;ke Ln 6rKs2Ax0;k 6ryA;do,fun,oCsure,up;a02eViQoLuK;ck0st I;aNc4Fg MoKse0;k Kse4D;aft9ba4do1forw37in56o0Zu46;in,oJ;d 6;e NghtMnLsKve 00;ten F;e 2k 2; 2e46;ar8do1in;aMt LvelK; oC;do1go,in,o7up;nEve K;in,oK;pKut;en;c5p 2sh LtchBughAy K;do1o59;in4Po7;eMick Lnock K;do1oCup;oCup;eLy K;in,up;l Ip K;aw3ba4do1f04in,oJto,up;aMoLuK;ic5mpE;ke3St H;c43zz 2;a01eWiToPuK;nLrrKsh 6;y 2;keLt K;ar8do1;r H;lKneErse3K;d Ke 2;ba4dKfast,o0Cup;ear,o1;de Lt K;ba4on,up;aw3o7;aKlp0;d Ml Ir Kt 2;fKof;rom;f11in,o03uW;cPm 2nLsh0ve Kz2P;at,it,to;d Lg KkerP;do1in,o2Tup;do1in,oK;ut,v9;k 2;aZeTive Rloss IoMrLunK; f0S;ab hold,in43ow 2U; Kof 2I;aMb1Mit,oLr8th1IuK;nd9;ff,n,v9;bo7ft9hQw3;aw3bKdo1in,oJrise,up,w3;a4ir2H;ar 6ek0t K;aLb1Fdo1in,oKr8up;ff,n,ut,v9;cLhKl2Fr8t,w3;ead;ross;d aKng 2;bo7;a0Ee07iYlUoQrMuK;ck Ke2N;ar8up;eLighten KownBy 2;aw3oG;eKshe27; 2z5;g 2lMol Krk I;aKwi20;bo7r8;d 6low 2;aLeKip0;sh0;g 6ke0mKrKtten H;e F;gRlPnNrLsKzzle0;h F;e Km 2;aw3ba4up;d0isK;h 2;e Kl 1T;aw3fPin,o7;ht ba4ure0;ePnLsK;s 2;cMd K;fKoG;or;e D;d04l 2;cNll Krm0t1G;aLbKdo1in,o09sho0Eth08victim;a4ehi2O;pa0C;e K;do1oGup;at Kdge0nd 12y5;in,o7up;aOi1HoNrK;aLess 6op KuN;aw3b03in,oC;gBwB; Ile0ubl1B;m 2;a0Ah05l02oOrLut K;aw3ba4do1oCup;ackBeep LoKy0;ss Dwd0;by,do1in,o0Uup;me NoLuntK; o2A;k 6l K;do1oG;aRbQforOin,oNtKu0O;hLoKrue;geth9;rough;ff,ut,v9;th,wK;ard;a4y;paKr8w3;rt;eaLose K;in,oCup;n 6r F;aNeLiK;ll0pE;ck Der Kw F;on,up;t 2;lRncel0rOsMtch LveE; in;o1Nup;h Dt K;doubt,oG;ry LvK;e 08;aw3oJ;l Km H;aLba4do1oJup;ff,n,ut;r8w3;a0Ve0MiteAl0Fo04rQuK;bblNckl05il0Dlk 6ndl05rLsKtMy FzzA;t 00;n 0HsK;t D;e I;ov9;anWeaUiLush K;oGup;ghQng K;aNba4do1forMin,oLuK;nd9p;n,ut;th;bo7lKr8w3;ong;teK;n 2;k K;do1in,o7up;ch0;arTg 6iRn5oPrNssMttlLunce Kx D;aw3ba4;e 6; ar8;e H;do1;k Dt 2;e 2;l 6;do1up;d 2;aPeed0oKurt0;cMw K;aw3ba4do1o7up;ck;k K;in,oC;ck0nk0stA; oQaNef 2lt0nd K;do1ov9up;er;up;r Lt K;do1in,oCup;do1o7;ff,nK;to;ck Pil0nMrgLsK;h D;ainBe D;g DkB; on;in,o7;aw3do1in,oCup;ff,ut;ay;ct FdQir0sk MuctionA; oG;ff;ar8o7;ouK;nd; o7;d K;do1oKup;ff,n;wn;o7up;ut",ProperNoun:"true\xa6aIbDc8dalhousHe7f5gosford,h4iron maiden,kirby,landsdowne,m2nis,r1s0wembF;herwood,paldiB;iel,othwe1;cgi0ercedes,issy;ll;intBudsB;airview,lorence,ra0;mpt9nco;lmo,uro;a1h0;arlt6es5risti;rl0talina;et4i0;ng;arb3e0;et1nt0rke0;ley;on;ie;bid,jax","Person|Place":"true\xa6a8d6h4jordan,k3orlando,s1vi0;ctor9rgin9;a0ydney;lvador,mara,ntia4;ent,obe;amil0ous0;ton;arw2ie0;go;lexandr1ust0;in;ia",LastName:"true\xa60:BR;1:BF;2:B5;3:BH;4:AX;5:9Y;6:B6;7:BK;8:B0;9:AV;A:AL;B:8Q;C:8G;D:7K;E:BM;F:AH;aBDb9Zc8Wd88e81f7Kg6Wh64i60j5Lk4Vl4Dm39n2Wo2Op25quispe,r1Ls0Pt0Ev03wTxSyKzG;aIhGimmerm6A;aGou,u;ng,o;khar5ytsE;aKeun9BiHoGun;koya32shiBU;!lG;diGmaz;rim,z;maGng;da,g52mo83sGzaC;aChiBV;iao,u;aLeJiHoGright,u;jcA5lff,ng;lGmm0nkl0sniewsC;kiB1liams33s3;bGiss,lt0;b,er,st0;a6Vgn0lHtG;anabe,s3;k0sh,tG;e2Non;aLeKiHoGukD;gt,lk5roby5;dHllalGnogr3Kr1Css0val3S;ba,ob1W;al,ov4;lasHsel8W;lJn dIrgBEsHzG;qu7;ilyEqu7siljE;en b6Aijk,yk;enzueAIverde;aPeix1VhKi2j8ka43oJrIsui,uG;om5UrG;c2n0un1;an,emblA7ynisC;dorAMlst3Km4rrAth;atch0i8UoG;mHrG;are84laci79;ps3sG;en,on;hirDkah9Mnaka,te,varA;a06ch01eYhUiRmOoMtIuHvGzabo;en9Jobod3N;ar7bot4lliv2zuC;aIeHoG;i7Bj4AyanAB;ele,in2FpheBvens25;l8rm0;kol5lovy5re7Tsa,to,uG;ng,sa;iGy72;rn5tG;!h;l71mHnGrbu;at9cla9Egh;moBo7M;aIeGimizu;hu,vchG;en8Luk;la,r1G;gu9infe5YmGoh,pulveA7rra5P;jGyG;on5;evi6iltz,miHneid0roed0uGwarz;be3Elz;dHtG;!t,z;!t;ar4Th8ito,ka4OlJnGr4saCto,unde19v4;ch7dHtGz;a5Le,os;b53e16;as,ihDm4Po0Y;aVeSiPoJuHyG;a6oo,u;bio,iz,sG;so,u;bKc8Fdrigue67ge10j9YmJosevelt,sItHux,wG;e,li6;a9Ch;enb4Usi;a54e4L;erts15i93;bei4JcHes,vGzzo;as,e9;ci,hards12;ag2es,iHut0yG;es,nol5N;s,t0;dImHnGsmu97v6C;tan1;ir7os;ic,u;aUeOhMiJoHrGut8;asad,if6Zochazk27;lishc2GpGrti72u10we76;e3Aov51;cHe45nG;as,to;as70hl0;aGillips;k,m,n6I;a3Hde3Wete0Bna,rJtG;ersHrovGters54;!a,ic;!en,on;eGic,kiBss3;i9ra,tz,z;h86k,padopoulIrk0tHvG;ic,l4N;el,te39;os;bMconn2Ag2TlJnei6PrHsbor6XweBzG;dem7Rturk;ella4DtGwe6N;ega,iz;iGof7Hs8I;vGyn1R;ei9;aSri1;aPeNiJoGune50ym2;rHvGwak;ak4Qik5otn66;odahl,r4S;cholsZeHkolGls4Jx3;ic,ov84;ls1miG;!n1;ils3mG;co4Xec;gy,kaGray2sh,var38;jiGmu9shiG;ma;a07c04eZiWoMuHyeG;rs;lJnIrGssoli6S;atGp03r7C;i,ov4;oz,te58;d0l0;h2lOnNo0RrHsGza1A;er,s;aKeJiIoz5risHtG;e56on;!on;!n7K;au,i9no,t5J;!lA;r1Btgome59;i3El0;cracFhhail5kkeHlG;l0os64;ls1;hmeJiIj30lHn3Krci0ssiGyer2N;!er;n0Po;er,j0;dDti;cartHlG;aughl8e2;hy;dQe7Egnu68i0jer3TkPmNnMrItHyG;er,r;ei,ic,su21thews;iHkDquAroqu8tinG;ez,s;a5Xc,nG;!o;ci5Vn;a5UmG;ad5;ar5e6Kin1;rig77s1;aVeOiLoJuHyG;!nch;k4nGo;d,gu;mbarGpe3Fvr4we;di;!nGu,yana2B;coln,dG;b21holm,strom;bedEfeKhIitn0kaHn8rGw35;oy;!j;m11tG;in1on1;bvGvG;re;iGmmy,ng,rs2Qu,voie,ws3;ne,t1F;aZeYh2iWlUnez50oNrJuHvar2woG;k,n;cerGmar68znets5;a,o34;aHem0isGyeziu;h23t3O;m0sni4Fus3KvG;ch4O;bay57ch,rh0Usk16vaIwalGzl5;czGsC;yk;cIlG;!cGen4K;huk;!ev4ic,s;e8uiveG;rt;eff0kGl4mu9nnun1;ucF;ll0nnedy;hn,llKminsCne,pIrHstra3Qto,ur,yGzl5;a,s0;j0Rls22;l2oG;or;oe;aPenOha6im14oHuG;ng,r4;e32hInHrge32u6vG;anD;es,ss3;anHnsG;en,on,t3;nesGs1R;en,s1;kiBnings,s1;cJkob4EnGrv0E;kDsG;en,sG;en0Ion;ks3obs2A;brahimDglesi5Nke5Fl0Qno07oneIshikHto,vanoG;u,v54;awa;scu;aVeOiNjaltal8oIrist50uG;!aGb0ghAynh;m2ng;a6dz4fIjgaa3Hk,lHpUrGwe,x3X;ak1Gvat;mAt;er,fm3WmG;ann;ggiBtchcock;iJmingw4BnHrGss;nand7re9;deGriks1;rs3;kkiHnG;on1;la,n1;dz4g1lvoQmOns0ZqNrMsJuIwHyG;asFes;kiB;g1ng;anHhiG;mo14;i,ov0J;di6p0r10t;ue;alaG;in1;rs1;aVeorgUheorghe,iSjonRoLrJuGw3;errGnnar3Co,staf3Ctierr7zm2;a,eG;ro;ayli6ee2Lg4iffithGub0;!s;lIme0UnHodGrbachE;e,m2;calvAzale0S;dGubE;bGs0E;erg;aj,i;bs3l,mGordaO;en7;iev3U;gnMlJmaIndFo,rGsFuthi0;cGdn0za;ia;ge;eaHlG;agh0i,o;no;e,on;aVerQiLjeldsted,lKoIrHuG;chs,entAji41ll0;eem2iedm2;ntaGrt8urni0wl0;na;emi6orA;lipIsHtzgeraG;ld;ch0h0;ovG;!ic;hatDnanIrG;arGei9;a,i;deY;ov4;b0rre1D;dKinsJriksIsGvaB;cob3GpGtra3D;inoza,osiQ;en,s3;te8;er,is3warG;ds;aXePiNjurhuMoKrisco15uHvorakG;!oT;arte,boHmitru,nn,rGt3C;and,ic;is;g2he0Omingu7nErd1ItG;to;us;aGcki2Hmitr2Ossanayake,x3;s,z; JbnaIlHmirGrvisFvi,w2;!ov4;gado,ic;th;bo0groot,jo6lHsilGvriA;va;a cruz,e3uG;ca;hl,mcevsCnIt2WviG;dGes,s;ov,s3;ielsGku22;!en;ki;a0Be06hRiobQlarkPoIrGunningh1H;awfo0RivGuz;elli;h1lKntJoIrGs2Nx;byn,reG;a,ia;ke,p0;i,rer2K;em2liB;ns;!e;anu;aOeMiu,oIristGu6we;eGiaG;ns1;i,ng,p9uHwGy;!dH;dGng;huJ;!n,onGu6;!g;kJnIpm2ttHudhGv7;ry;erjee,o14;!d,g;ma,raboG;rty;bJl0Cng4rG;eghetHnG;a,y;ti;an,ota1C;cerAlder3mpbeLrIstGvadi0B;iGro;llo;doHl0Er,t0uGvalho;so;so,zo;ll;a0Fe01hYiXlUoNrKuIyG;rLtyG;qi;chan2rG;ke,ns;ank5iem,oGyant;oks,wG;ne;gdan5nIruya,su,uchaHyKziG;c,n5;rd;darGik;enG;ko;ov;aGond15;nco,zG;ev4;ancFshw16;a08oGuiy2;umGwmG;ik;ckRethov1gu,ktPnNrG;gJisInG;ascoGds1;ni;ha;er,mG;anG;!n;gtGit7nP;ss3;asF;hi;er,hG;am;b4ch,ez,hRiley,kk0ldw8nMrIshHtAu0;es;ir;bInHtlGua;ett;es,i0;ieYosa;dGik;a9yoG;padhyG;ay;ra;k,ng;ic;bb0Acos09d07g04kht05lZnPrLsl2tJyG;aHd8;in;la;chis3kiG;ns3;aImstro6sl2;an;ng;ujo,ya;dJgelHsaG;ri;ovG;!a;ersJov,reG;aGjEws;ss1;en;en,on,s3;on;eksejEiyEmeiIvG;ar7es;ez;da;ev;arwHuilG;ar;al;ams,l0;er;ta;as",Ordinal:"true\xa6eBf7nin5s3t0zeroE;enDhir1we0;lfCn7;d,t3;e0ixt8;cond,vent7;et0th;e6ie7;i2o0;r0urt3;tie4;ft1rst;ight0lev1;e0h,ie1;en0;th",Cardinal:"true\xa6bEeBf5mEnine7one,s4t0zero;en,h2rDw0;e0o;lve,n5;irt6ousands,ree;even2ix2;i3o0;r1ur0;!t2;ty;ft0ve;e2y;ight0lev1;!e0y;en;illions",Multiple:"true\xa6b3hundred,m3qu2se1t0;housand,r2;pt1xt1;adr0int0;illion",City:"true\xa60:74;1:61;2:6G;3:6J;4:5S;a68b53c4Id48e44f3Wg3Hh39i31j2Wk2Fl23m1Mn1Co19p0Wq0Ur0Os05tRuQvLwDxiBy9z5;a7h5i4Muri4O;a5e5ongsh0;ng3H;greb,nzib5G;ang2e5okoha3Sunfu;katerin3Hrev0;a5n0Q;m5Hn;arsBeAi6roclBu5;h0xi,zh5P;c7n5;d5nipeg,terth4;hoek,s1L;hi5Zkl3A;l63xford;aw;a8e6i5ladivost5Molgogr6L;en3lni6S;ni22r5;o3saill4N;lenc4Wncouv3Sr3ughn;lan bat1Crumqi,trecht;aFbilisi,eEheDiBo9r7u5;l21n63r5;in,ku;i5ondh62;es51poli;kyo,m2Zron1Pulo5;n,uS;an5jua3l2Tmisoa6Bra3;j4Tshui; hag62ssaloni2H;gucigal26hr0l av1U;briz,i6llinn,mpe56ng5rtu,shk2R;i3Esh0;an,chu1n0p2Eyu0;aEeDh8kopje,owe1Gt7u5;ra5zh4X;ba0Ht;aten is55ockholm,rasbou67uttga2V;an8e6i5;jiazhua1llo1m5Xy0;f50n5;ya1zh4H;gh3Kt4Q;att45o1Vv44;cramen16int ClBn5o paulo,ppo3Rrajevo; 7aa,t5;a 5o domin3E;a3fe,m1M;antonio,die3Cfrancisco,j5ped3Nsalvad0J;o5u0;se;em,t lake ci5Fz25;lou58peters24;a9e8i6o5;me,t59;ga,o5yadh;! de janei3F;cife,ims,nn3Jykjavik;b4Sip4lei2Inc2Pwalpindi;ingdao,u5;ez2i0Q;aFeEhDiCo9r7u6yong5;ya1;eb59ya1;a5etor3M;g52to;rt5zn0; 5la4Co;au prin0Melizabe24sa03;ls3Prae5Atts26;iladelph3Gnom pe1Aoenix;ki1tah tik3E;dua,lerYnaji,r4Ot5;na,r32;ak44des0Km1Mr6s5ttawa;a3Vlo;an,d06;a7ew5ing2Fovosibir1Jyc; 5cast36;del24orlea44taip14;g8iro4Wn5pl2Wshv33v0;ch6ji1t5;es,o1;a1o1;a6o5p4;ya;no,sa0W;aEeCi9o6u5;mb2Ani26sc3Y;gadishu,nt6s5;c13ul;evideo,pelli1Rre2Z;ami,l6n14s5;kolc,sissauga;an,waukee;cca,d5lbour2Mmph41ndo1Cssi3;an,ell2Xi3;cau,drAkass2Sl9n8r5shh4A;aca6ib5rakesh,se2L;or;i1Sy;a4EchFdal0Zi47;mo;id;aDeAi8o6u5vSy2;anMckn0Odhia3;n5s angel26;d2g bea1N;brev2Be3Lma5nz,sb2verpo28;!ss27; ma39i5;c5pzig;est16; p6g5ho2Wn0Cusan24;os;az,la33;aHharFiClaipeBo9rak0Du7y5;iv,o5;to;ala lump4n5;mi1sh0;hi0Hlka2Xpavog4si5wlo2;ce;da;ev,n5rkuk;gst2sha5;sa;k5toum;iv;bHdu3llakuric0Qmpa3Fn6ohsiu1ra5un1Iwaguc0Q;c0Pj;d5o,p4;ah1Ty;a7e6i5ohannesV;l1Vn0;dd36rusalem;ip4k5;ar2H;bad0mph1OnArkutUs7taXz5;mir,tapala5;pa;fah0l6tanb5;ul;am2Zi2H;che2d5;ianap2Mo20;aAe7o5yder2W; chi mi5ms,nolulu;nh;f6lsin5rakli2;ki;ei;ifa,lifax,mCn5rb1Dva3;g8nov01oi;aFdanEenDhCiPlasgBo9raz,u5;a5jr23;dal6ng5yaquil;zh1J;aja2Oupe;ld coa1Bthen5;bu2S;ow;ent;e0Uoa;sk;lw7n5za;dhi5gt1E;nag0U;ay;aisal29es,o8r6ukuya5;ma;ankfu5esno;rt;rt5sh0; wor6ale5;za;th;d5indhov0Pl paso;in5mont2;bur5;gh;aBe8ha0Xisp4o7resd0Lu5;b5esseldorf,nkirk,rb0shanbe;ai,l0I;ha,nggu0rtmu13;hradSl6nv5troit;er;hi;donghIe6k09l5masc1Zr es sala1KugavpiY;i0lU;gu,je2;aJebu,hAleve0Vo5raio02uriti1Q;lo7n6penhag0Ar5;do1Ok;akKst0V;gUm5;bo;aBen8i6ongqi1ristchur5;ch;ang m7ca5ttago1;go;g6n5;ai;du,zho1;ng5ttogr14;ch8sha,zh07;gliari,i9lga8mayenJn6pe town,r5tanO;acCdiff;ber1Ac5;un;ry;ro;aWeNhKirmingh0WoJr9u5;chareTdapeTenos air7r5s0tu0;g5sa;as;es;a9is6usse5;ls;ba6t5;ol;ne;sil8tisla7zzav5;il5;le;va;ia;goZst2;op6ubaneshw5;ar;al;iCl9ng8r5;g6l5n;in;en;aluru,hazi;fa6grade,o horizon5;te;st;ji1rut;ghd0BkFn9ot8r7s6yan n4;ur;el,r07;celo3i,ranquil09;ou;du1g6ja lu5;ka;alo6k5;ok;re;ng;ers5u;field;a05b02cc01ddis aba00gartaZhmedXizawl,lSmPnHqa00rEsBt7uck5;la5;nd;he7l5;an5;ta;ns;h5unci2;dod,gab5;at;li5;ngt2;on;a8c5kaOtwerp;hora6o3;na;ge;h7p5;ol5;is;eim;aravati,m0s5;terd5;am; 7buquerq6eppo,giers,ma5;ty;ue;basrah al qadim5mawsil al jadid5;ah;ab5;ad;la;ba;ra;idj0u dha5;bi;an;lbo6rh5;us;rg",Region:"true\xa60:2O;1:2L;2:2U;3:2F;a2Sb2Fc21d1Wes1Vf1Tg1Oh1Ki1Fj1Bk16l13m0Sn09o07pYqVrSsJtEuBverAw6y4zacatec2W;akut0o0Fu4;cat1k09;a5est 4isconsin,yomi1O;bengal,virgin0;rwick3shington4;! dc;acruz,mont;dmurt0t4;ah,tar4; 2Pa12;a6e5laxca1Vripu21u4;scaEva;langa2nnessee,x2J;bas10m4smQtar29;aulip2Hil nadu;a9elang07i7o5taf16u4ylh1J;ff02rr09s1E;me1Gno1Uuth 4;cZdY;ber0c4kkim,naloa;hu1ily;n5rawak,skatchew1xo4;ny; luis potosi,ta catari2;a4hodeA;j4ngp0C;asth1shahi;ingh29u4;e4intana roo;bec,en6retaro;aAe6rince edward4unjab; i4;sl0G;i,n5r4;ak,nambu0F;a0Rnsylv4;an0;ha0Pra4;!na;axa0Zdisha,h4klaho21ntar4reg7ss0Dx0I;io;aLeEo6u4;evo le4nav0X;on;r4tt18va scot0;f9mandy,th4; 4ampton3;c6d5yo4;rk3;ako1O;aroli2;olk;bras1Nva0Dw4; 6foundland4;! and labrad4;or;brunswick,hamp3jers5mexiTyork4;! state;ey;galPyarit;aAeghala0Mi6o4;nta2r4;dov0elos;ch6dlanDn5ss4zor11;issippi,ouri;as geraPneso18;ig1oac1;dhy12harasht0Gine,lac07ni5r4ssachusetts;anhao,i el,ylG;p4toba;ur;anca3e4incoln3ouisI;e4iR;ds;a6e5h4omi;aka06ul2;dah,lant1ntucky,ra01;bardino,lmyk0ns0Qr4;achay,el0nata0X;alis6har4iangxi;kh4;and;co;daho,llino7n4owa;d5gush4;et0;ia2;is;a6ert5i4un1;dalFm0D;ford3;mp3rya2waii;ansu,eorg0lou7oa,u4;an4izhou,jarat;ajuato,gdo4;ng;cester3;lori4uji1;da;sex;ageUe7o5uran4;go;rs4;et;lawaMrby3;aFeaEh9o4rim08umbr0;ahui7l6nnectic5rsi4ventry;ca;ut;i03orado;la;e5hattisgarh,i4uvash0;apRhuahua;chn5rke4;ss0;ya;ra;lGm4;bridge3peche;a9ihar,r8u4;ck4ryat0;ingham3;shi4;re;emen,itish columb0;h0ja cal8lk7s4v7;hkorto4que;st1;an;ar0;iforn0;ia;dygHguascalientes,lBndhr9r5ss4;am;izo2kans5un4;achal 7;as;na;a 4;pradesh;a6ber5t4;ai;ta;ba5s4;ka;ma;ea",Place:"true\xa60:4T;1:4V;2:44;3:4B;4:3I;a4Eb3Gc2Td2Ge26f25g1Vh1Ji1Fk1Cl14m0Vn0No0Jp08r04sTtNuLvJw7y5;a5o0Syz;kut1Bngtze;aDeChitBi9o5upatki,ycom2P;ki26o5;d5l1B;b3Ps5;i4to3Y;c0SllowbroCn5;c2Qgh2;by,chur1P;ed0ntw3Gs22;ke6r3St5;erf1f1; is0Gf3V;auxha3Mirgin is0Jost5;ok;laanbaatar,pto5xb3E;n,wn;a9eotihuac43h7ive49o6ru2Nsarskoe selo,u5;l2Dzigo47;nto,rquay,tt2J;am3e 5orn3E;bronx,hamptons;hiti,j mah0Iu1N;aEcotts bluff,eCfo,herbroQoApring9t7u5yd2F;dbu1Wn5;der03set3B;aff1ock2Nr5;atf1oud;hi37w24;ho,uth5; 1Iam1Zwo3E;a5i2O;f2Tt0;int lawrence riv3Pkhal2D;ayleigh,ed7i5oc1Z;chmo1Eo gran4ver5;be1Dfr09si4; s39cliffe,hi2Y;aCe9h8i5ompeii,utn2;c6ne5tcai2T; 2Pc0G;keri13t0;l,x;k,lh2mbr6n5r2J;n1Hzance;oke;cif38pahanaumokuak30r5;k5then0;si4w1K;ak7r6x5;f1l2X;ange county,d,f1inoco;mTw1G;e8i1Uo5;r5tt2N;th5wi0E; 0Sam19;uschwanste1Pw5; eng6a5h2market,po36;rk;la0P;a8co,e6i5uc;dt1Yll0Z;adow5ko0H;lands;chu picchu,gad2Ridsto1Ql8n7ple6r5;kh2; g1Cw11;hatt2Osf2B;ibu,t0ve1Z;a8e7gw,hr,in5owlOynd02;coln memori5dl2C;al;asi4w3;kefr7mbe1On5s,x;ca2Ig5si05;f1l27t0;ont;azan kreml14e6itchen2Gosrae,rasnoyar5ul;sk;ns0Hs1U;ax,cn,lf1n6ps5st;wiN;d5glew0Lverness;ian27ochina;aDeBi6kg,nd,ov5unti2H;d,enweep;gh6llc5;reL;bu03l5;and5;!s;r5yw0C;ef1tf1;libu24mp6r5stings;f1lem,row;stead,t0;aDodavari,r5uelph;avenAe5imsS;at 8en5; 6f1Fwi5;ch;acr3vall1H;brita0Flak3;hur5;st;ng3y villa0W;airhavHco,ra;aAgli9nf17ppi8u7ver6x5;et1Lf1;glad3t0;rope,st0;ng;nt0;rls1Ls5;t 5;e5si4;nd;aCe9fw,ig8o7ryd6u5xb;mfri3nstab00rh2tt0;en;nca18rcKv19wnt0B;by;n6r5vonpo1D;ry;!h2;nu8r5;l6t5;f1moor;ingt0;be;aLdg,eIgk,hClBo5royd0;l6m5rnwa0B;pt0;c7lingw6osse5;um;ood;he0S;earwat0St;a8el6i5uuk;chen itza,mney ro07natSricahua;m0Zt5;enh2;mor5rlottetPth2;ro;dar 5ntervilA;breaks,faZg5;rove;ld9m8r5versh2;lis6rizo pla5;in;le;bLpbellf1;weQ;aZcn,eNingl01kk,lackLolt0r5uckV;aGiAo5;ckt0ok5wns cany0;lyn,s5;i4to5;ne;de;dge6gh5;am,t0;n6t5;own;or5;th;ceb6m5;lNpt0;rid5;ge;bu5pool,wa8;rn;aconsfEdf1lBr9verly7x5;hi5;ll; hi5;lls;wi5;ck; air,l5;ingh2;am;ie5;ld;ltimore,rnsl6tters5;ea;ey;bLct0driadic,frica,ginJlGmFn9rc8s7tl6yleOzor3;es;!ant8;hcroft,ia; de triomphe,t6;adyr,ca8dov9tarct5;ic5; oce5;an;st5;er;ericas,s;be6dersh5hambra,list0;ot;rt0;cou5;rt;bot7i5;ngd0;on;sf1;ord",Country:"true\xa60:38;1:2L;2:3B;a2Xb2Ec22d1Ye1Sf1Mg1Ch1Ai14j12k0Zl0Um0Gn05om2pZqat1KrXsKtCu7v5wal4yemTz3;a25imbabwe;es,lis and futu2Y;a3enezue32ietnam;nuatu,tican city;gTk6nited 4ruXs3zbeE; 2Ca,sr;arab emirat0Kkingdom,states3;! of am2Y;!raiV;a8haCimor les0Co7rinidad 5u3;nis0rk3valu;ey,me2Zs and caic1V;and t3t3;oba1L;go,kel10nga;iw2ji3nz2T;ki2V;aDcotl1eCi9lov8o6pa2Dri lanka,u5w3yr0;az3edAitzerl1;il1;d2riname;lomon1Xmal0uth 3;afr2KkMsud2;ak0en0;erra leoFn3;gapo1Yt maart3;en;negLrb0ychellZ;int 3moa,n marino,udi arab0;hele26luc0mart21;epublic of ir0Eom2Euss0w3;an27;a4eIhilippinUitcairn1Mo3uerto riN;l1rtugF;ki2Dl4nama,pua new0Vra3;gu7;au,esti3;ne;aBe9i7or3;folk1Ith4w3;ay; k3ern mariana1D;or0O;caragua,ger3ue;!ia;p3ther1Aw zeal1;al;mib0u3;ru;a7exi6icro0Bo3yanm06;ldova,n3roc5zambA;a4gol0t3;enegro,serrat;co;cAdagasc01l7r5urit4yot3;te;an0i16;shall0Xtin3;ique;a4div3i,ta;es;wi,ys0;ao,ed02;a6e5i3uxembourg;b3echtenste12thu1G;er0ya;ban0Isotho;os,tv0;azakh1Fe4iriba04o3uwait,yrgyz1F;rXsovo;eling0Knya;a3erG;ma16p2;c7nd6r4s3taly,vory coast;le of m2rael;a3el1;n,q;ia,oJ;el1;aiTon3ungary;dur0Ng kong;aBermany,ha0QibraltAre8u3;a6ern5inea3ya0P;! biss3;au;sey;deloupe,m,tema0Q;e3na0N;ce,nl1;ar;bUmb0;a7i6r3;ance,ench 3;guia0Epoly3;nes0;ji,nl1;lklandUroeU;ast tim7cu6gypt,l salv6ngl1quatorial4ritr5st3thiop0;on0; guin3;ea;ad3;or;enmark,jibou5ominica4r con3;go;!n C;ti;aBentral african Ah8o5roat0u4yprRzech3; 9ia;ba,racao;c4lo3morQngo brazzaville,okGsta r04te de ivoiL;mb0;osE;i3ristmasG;le,na;republic;m3naUpe verde,ymanA;bod0ero3;on;aGeDhut2o9r5u3;lgar0r3;kina faso,ma,undi;azil,itish 3unei;virgin3; is3;lands;liv0nai5snia and herzegoviHtswaHuvet3; isl1;and;re;l3n8rmuG;ar3gium,ize;us;h4ngladesh,rbad3;os;am4ra3;in;as;fghaGlDmBn6r4ustr3zerbaij2;al0ia;genti3men0uba;na;dorra,g5t3;arct7igua and barbu3;da;o3uil3;la;er3;ica;b3ger0;an0;ia;ni3;st2;an",FirstName:"true\xa6aTblair,cQdOfrancoZgabMhinaLilya,jHkClBm6ni4quinn,re3s0;h0umit,yd;ay,e0iloh;a,lby;g9ne;co,ko0;!s;a1el0ina,org6;!okuhF;ds,naia,r1tt0xiB;i,y;ion,lo;ashawn,eif,uca;a3e1ir0rM;an;lsFn0rry;dall,yat5;i,sD;a0essIie,ude;i1m0;ie,mG;me;ta;rie0y;le;arcy,ev0;an,on;as1h0;arl8eyenne;ey,sidy;drien,kira,l4nd1ubr0vi;ey;i,r0;a,e0;a,y;ex2f1o0;is;ie;ei,is",WeekDay:"true\xa6fri2mon2s1t0wednesd3;hurs1ues1;aturd1und1;!d0;ay0;!s",Month:"true\xa6dec0february,july,nov0octo1sept0;em0;ber",Date:"true\xa6ago,on4som4t1week0yesterd5; end,ends;mr1o0;d2morrow;!w;ed0;ay",Duration:"true\xa6centurAd8h7m5q4se3w1y0;ear8r8;eek0k7;!end,s;ason,c5;tr,uarter;i0onth3;llisecond2nute2;our1r1;ay0ecade0;!s;ies,y",FemaleName:"true\xa60:J7;1:JB;2:IJ;3:IK;4:J1;5:IO;6:JS;7:JO;8:HB;9:JK;A:H4;B:I2;C:IT;D:JH;E:IX;F:BA;G:I4;aGTbFLcDRdD0eBMfB4gADh9Ti9Gj8Dk7Cl5Wm48n3Lo3Hp33qu32r29s15t0Eu0Cv02wVxiTyOzH;aLeIineb,oHsof3;e3Sf3la,ra;h2iKlIna,ynH;ab,ep;da,ma;da,h2iHra;nab;aKeJi0FolB7uIvH;et8onDP;i0na;le0sen3;el,gm3Hn,rGLs8W;aoHme0nyi;m5XyAD;aMendDZhiDGiH;dele9lJnH;if48niHo0;e,f47;a,helmi0lHma;a,ow;ka0nB;aNeKiHusa5;ck84kIl8oleAviH;anFenJ4;ky,toriBK;da,lA8rHs0;a,nHoniH9;a,iFR;leHnesH9;nILrH;i1y;g9rHs6xHA;su5te;aYeUhRiNoLrIuHy2;i,la;acJ3iHu0J;c3na,sH;hFta;nHr0F;iFya;aJffaEOnHs6;a,gtiH;ng;!nFSra;aIeHomasi0;a,l9Oo8Ares1;l3ndolwethu;g9Fo88rIssH;!a,ie;eHi,ri7;sa,za;bOlMmKnIrHs6tia0wa0;a60yn;iHya;a,ka,s6;arFe2iHm77ra;!ka;a,iH;a,t6;at6it6;a0Ecarlett,e0AhWiSkye,neza0oQri,tNuIyH;bIGlvi1;ha,mayIJniAsIzH;an3Net8ie,y;anHi7;!a,e,nH;aCe;aIeH;fan4l5Dphan6E;cI5r5;b3fiAAm0LnHphi1;d2ia,ja,ya;er2lJmon1nIobh8QtH;a,i;dy;lETv3;aMeIirHo0risFDy5;a,lDM;ba,e0i5lJrH;iHr6Jyl;!d8Ifa;ia,lDZ;hd,iMki2nJrIu0w0yH;la,ma,na;i,le9on,ron,yn;aIda,ia,nHon;a,on;!ya;k6mH;!aa;lJrItaye82vH;da,inj;e0ife;en1i0ma;anA9bLd5Oh1SiBkKlJmInd2rHs6vannaC;aCi0;ant6i2;lDOma,ome;ee0in8Tu2;in1ri0;a05eZhXiUoHuthDM;bScRghQl8LnPsJwIxH;anB3ie,y;an,e0;aIeHie,lD;ann7ll1marDGtA;!lHnn1;iHyn;e,nH;a,dF;da,i,na;ayy8G;hel67io;bDRerAyn;a,cIkHmas,nFta,ya;ki,o;h8Xki;ea,iannGMoH;da,n1P;an0bJemFgi0iInHta,y0;a8Bee;han86na;a,eH;cHkaC;a,ca;bi0chIe,i0mo0nHquETy0;di,ia;aERelHiB;!e,le;een4ia0;aPeOhMiLoJrHute6A;iHudenCV;scil3LyamvaB;lHrt3;i0ly;a,paluk;ilome0oebe,ylH;is,lis;ggy,nelope,r5t2;ige,m0VnKo5rvaDMtIulH;a,et8in1;ricHt4T;a,e,ia;do2i07;ctav3dIfD3is6ksa0lHphD3umC5yunbileg;a,ga,iv3;eHvAF;l3t8;aWeUiMoIurHy5;!ay,ul;a,eJor,rIuH;f,r;aCeEma;ll1mi;aNcLhariBQkKlaJna,sHta,vi;anHha;ur;!y;a,iDZki;hoGk9YolH;a,e4P;!mh;hir,lHna,risDEsreE;!a,lBV;asuMdLh3i6Dl5nKomi7rgEVtH;aHhal4;lHs6;i1ya;cy,et8;e9iF0ya;nngu2X;a0Ackenz4e02iMoJrignayani,uriDJyH;a,rH;a,iOlNna,tG;bi0i2llBJnH;a,iH;ca,ka,qD9;a,cUdo4ZkaTlOmi,nMrItzi,yH;ar;aJiIlH;anET;am;!l,nB;dy,eHh,n4;nhGrva;aKdJe0iCUlH;iHy;cent,e;red;!gros;!e5;ae5hH;ae5el3Z;ag5DgNi,lKrH;edi7AiIjem,on,yH;em,l;em,sCG;an4iHliCF;nHsCJ;a,da;!an,han;b09cASd07e,g05ha,i04ja,l02n00rLsoum5YtKuIv84xBKyHz4;bell,ra,soBB;d7rH;a,eE;h8Gild1t4;a,cUgQiKjor4l7Un4s6tJwa,yH;!aHbe6Xja9lAE;m,nBL;a,ha,in1;!aJbCGeIja,lDna,sHt63;!a,ol,sa;!l1D;!h,mInH;!a,e,n1;!awit,i;arJeIie,oHr48ueri8;!t;!ry;et46i3B;el4Xi7Cy;dHon,ue5;akranAy;ak,en,iHlo3S;a,ka,nB;a,re,s4te;daHg4;!l3E;alDd4elHge,isDJon0;ei9in1yn;el,le;a0Ne0CiXoQuLyH;d3la,nH;!a,dIe2OnHsCT;!a,e2N;a,sCR;aD4cJel0Pis1lIna,pHz;e,iA;a,u,wa;iHy;a0Se,ja,l2NnB;is,l1UrItt1LuHvel4;el5is1;aKeIi7na,rH;aADi7;lHn1tA;ei;!in1;aTbb9HdSepa,lNnKsJvIzH;!a,be5Ret8z4;!ia;a,et8;!a,dH;a,sHy;ay,ey,i,y;a,iJja,lH;iHy;aA8e;!aH;!nF;ia,ya;!nH;!a,ne;aPda,e0iNjYla,nMoKsJtHx93y5;iHt4;c3t3;e2PlCO;la,nHra;a,ie,o2;a,or1;a,gh,laH;!ni;!h,nH;a,d2e,n5V;cOdon9DiNkes6mi9Gna,rMtJurIvHxmi,y5;ern1in3;a,e5Aie,yn;as6iIoH;nya,ya;fa,s6;a,isA9;a,la;ey,ie,y;a04eZhXiOlASoNrJyH;lHra;a,ee,ie;istHy6I;a,en,iIyH;!na;!e,n5F;nul,ri,urtnB8;aOerNlB7mJrHzzy;a,stH;en,in;!berlImernH;aq;eHi,y;e,y;a,stE;!na,ra;aHei2ongordzol;dij1w5;el7UiKjsi,lJnIrH;a,i,ri;d2na,za;ey,i,lBLs4y;ra,s6;biAcARdiat7MeBAiSlQmPnyakuma1DrNss6NtKviAyH;!e,lH;a,eH;e,i8T;!a6HeIhHi4TlDri0y;ar8Her8Hie,leErBAy;!lyn8Ori0;a,en,iHl5Xoli0yn;!ma,nFs95;a5il1;ei8Mi,lH;e,ie;a,tl6O;a0AeZiWoOuH;anMdLlHst88;es,iH;a8NeHs8X;!n9tH;!a,te;e5Mi3My;a,iA;!anNcelDdMelGhan7VleLni,sIva0yH;a,ce;eHie;fHlDph7Y;a,in1;en,n1;i7y;!a,e,n45;lHng;!i1DlH;!i1C;anNle0nKrJsH;i8JsH;!e,i8I;i,ri;!a,elGif2CnH;a,et8iHy;!e,f2A;a,eJiInH;a,eIiH;e,n1;!t8;cMda,mi,nIque4YsminFvie2y9zH;min7;a7eIiH;ce,e,n1s;!lHs82t0F;e,le;inIk6HlDquelH;in1yn;da,ta;da,lRmPnOo0rNsIvaHwo0zaro;!a0lu,na;aJiIlaHob89;!n9R;do2;belHdo2;!a,e,l3B;a7Ben1i0ma;di2es,gr72ji;a9elBogH;en1;a,e9iHo0se;a0na;aSeOiJoHus7Kyacin2C;da,ll4rten24snH;a,i9U;lImaH;ri;aIdHlaI;a,egard;ry;ath1BiJlInrietArmi9sH;sa,t1A;en2Uga,mi;di;bi2Fil8MlNnMrJsItHwa,yl8M;i5Tt4;n60ti;iHmo51ri53;etH;!te;aCnaC;a,ey,l4;a02eWiRlPoNrKunJwH;enHyne1R;!dolD;ay,el;acieIetHiselB;a,chE;!la;ld1CogooH;sh;adys,enHor3yn2K;a,da,na;aKgi,lIna,ov8EselHta;a,e,le;da,liH;an;!n0;mLnJorgIrH;ald5Si,m3Etrud7;et8i4X;a,eHna;s29vieve;ma;bIle,mHrnet,yG;al5Si5;iIrielH;a,l1;!ja;aTeQiPlorOoz3rH;anJeIiH;da,eB;da,ja;!cH;esIiHoi0P;n1s66;!ca;a,enc3;en,o0;lIn0rnH;anB;ec3ic3;jr,nArKtHy7;emIiHma,oumaA;ha,ma,n;eh;ah,iBrah,za0;cr4Rd0Re0Qi0Pk0Ol07mXn54rUsOtNuMvHwa;aKelIiH;!e,ta;inFyn;!a;!ngel4V;geni1ni47;h5Yien9ta;mLperanKtH;eIhHrel5;er;l31r7;za;a,eralB;iHma,ne4Lyn;cHka,n;a,ka;aPeNiKmH;aHe21ie,y;!li9nuH;elG;lHn1;e7iHy;a,e,ja;lHrald;da,y;!nue5;aWeUiNlMma,no2oKsJvH;a,iH;na,ra;a,ie;iHuiH;se;a,en,ie,y;a0c3da,e,f,nMsJzaH;!betHveA;e,h;aHe,ka;!beH;th;!a,or;anor,nH;!a,i;!in1na;ate1Rta;leEs6;vi;eIiHna,wi0;e,th;l,n;aYeMh3iLjeneKoH;lor5Vminiq4Ln3FrHtt4;a,eEis,la,othHthy;ea,y;ba;an09naCon9ya;anQbPde,eOiMlJmetr3nHsir5M;a,iH;ce,se;a,iIla,orHphi9;es,is;a,l6F;dHrdH;re;!d5Ena;!b2ForaCraC;a,d2nH;!a,e;hl3i0l0GmNnLphn1rIvi1WyH;le,na;a,by,cIia,lH;a,en1;ey,ie;a,et8iH;!ca,el1Aka,z;arHia;is;a0Re0Nh04i02lUoJristIynH;di,th3;al,i0;lPnMrIurH;tn1D;aJd2OiHn2Ori9;!nH;a,e,n1;!l4;cepci5Cn4sH;tanHuelo;ce,za;eHleE;en,t8;aJeoIotH;il54;!pat2;ir7rJudH;et8iH;a,ne;a,e,iH;ce,sZ;a2er2ndH;i,y;aReNloe,rH;isJyH;stH;al;sy,tH;a1Sen,iHy;an1e,n1;deJlseIrH;!i7yl;a,y;li9;nMrH;isKlImH;ai9;a,eHot8;n1t8;!sa;d2elGtH;al,elG;cIlH;es8i47;el3ilH;e,ia,y;itlYlXmilWndVrMsKtHy5;aIeIhHri0;er1IleErDy;ri0;a38sH;a37ie;a,iOlLmeJolIrH;ie,ol;!e,in1yn;lHn;!a,la;a,eIie,otHy;a,ta;ne,y;na,s1X;a0Ii0I;a,e,l1;isAl4;in,yn;a0Ke02iZlXoUrH;andi7eRiJoIyH;an0nn;nwDoke;an3HdgMgiLtH;n31tH;!aInH;ey,i,y;ny;d,t8;etH;!t7;an0e,nH;da,na;bbi7glarIlo07nH;iAn4;ka;ancHythe;a,he;an1Clja0nHsm3M;iAtH;ou;aWcVlinUniArPssOtJulaCvH;!erlH;ey,y;hJsy,tH;e,iHy7;e,na;!anH;ie,y;!ie;nItHyl;ha,ie;adIiH;ce;et8i9;ay,da;ca,ky;!triH;ce,z;rbJyaH;rmH;aa;a2o2ra;a2Ub2Od25g21i1Sj5l18m0Zn0Boi,r06sWtVuPvOwa,yIzH;ra,u0;aKes6gJlIn,seH;!l;in;un;!nH;a,na;a,i2K;drLguJrIsteH;ja;el3;stH;in1;a,ey,i,y;aahua,he0;hIi2Gja,miAs2DtrH;id;aMlIraqHt21;at;eIi7yH;!n;e,iHy;gh;!nH;ti;iJleIo6piA;ta;en,n1t8;aHelG;!n1J;a01dje5eZgViTjRnKohito,toHya;inet8nH;el5ia;te;!aKeIiHmJ;e,ka;!mHtt7;ar4;!belIliHmU;sa;!l1;a,eliH;ca;ka,sHta;a,sa;elHie;a,iH;a,ca,n1qH;ue;!tH;a,te;!bImHstasiMya;ar3;el;aLberKeliJiHy;e,l3naH;!ta;a,ja;!ly;hGiIl3nB;da;a,ra;le;aWba,ePiMlKthJyH;a,c3sH;a,on,sa;ea;iHys0N;e,s0M;a,cIn1sHza;a,e,ha,on,sa;e,ia,ja;c3is6jaKksaKna,sJxH;aHia;!nd2;ia,saH;nd2;ra;ia;i0nIyH;ah,na;a,is,naCoud;la;c6da,leEmNnLsH;haClH;inHyY;g,n;!h;a,o,slH;ey;ee;en;at6g4nIusH;ti0;es;ie;aWdiTelMrH;eJiH;anMenH;a,e,ne;an0;na;!aLeKiIyH;nn;a,n1;a,e;!ne;!iH;de;e,lDsH;on;yn;!lH;i9yn;ne;aKbIiHrL;!e,gaK;ey,i7y;!e;gaH;il;dKliyJradhIs6;ha;ya;ah;a,ya",Honorific:"true\xa6director1field marsh2lieutenant1rear0sergeant major,vice0; admir1; gener0;al","Adj|Gerund":"true\xa60:3F;1:3H;2:31;3:2X;4:35;5:33;6:3C;7:2Z;8:36;9:29;a33b2Tc2Bd1Te1If19g12h0Zi0Rl0Nm0Gnu0Fo0Ap04rYsKtEuBvAw1Ayiel3;ar6e08;nBpA;l1Rs0B;fol3n1Zsett2;aEeDhrBi4ouc7rAwis0;e0Bif2oub2us0yi1;ea1SiA;l2vi1;l2mp0rr1J;nt1Vxi1;aMcreec7enten2NhLkyrocke0lo0Vmi2oJpHtDuBweA;e0Ul2;pp2ArA;gi1pri5roun3;aBea8iAri2Hun9;mula0r4;gge4rA;t2vi1;ark2eAraw2;e3llb2F;aAot7;ki1ri1;i9oc29;dYtisf6;aEeBive0oAus7;a4l2;assu4defi9fres7ig9juve07mai9s0vAwar3;ea2italiAol1G;si1zi1;gi1ll6mb2vi1;a6eDier23lun1VrAun2C;eBoA;mi5vo1Z;ce3s5vai2;n3rpleA;xi1;ffCpWutBverAwi1;arc7lap04p0Pri3whel8;goi1l6st1J;en3sA;et0;m2Jrtu4;aEeDiCoBuAyst0L;mb2;t1Jvi1;s5tiga0;an1Rl0n3smeri26;dAtu4;de9;aCeaBiAo0U;fesa0Tvi1;di1ni1;c1Fg19s0;llumiGmFnArri0R;cDfurHsCtBviA;go23ti1;e1Oimi21oxica0rig0V;pi4ul0;orpo20r0K;po5;na0;eaBorr02umilA;ia0;li1rtwar8;lFrA;atiDipCoBuelA;i1li1;undbrea10wi1;pi1;f6ng;a4ea8;a3etc7it0lEoCrBulfA;il2;ee1FighXust1L;rAun3;ebo3thco8;aCoA;a0wA;e4i1;mi1tte4;lectrJmHnExA;aCci0hBis0pA;an3lo3;aOila1B;c0spe1A;ab2coura0CdBergi13ga0Clive9ric7s02tA;hral2i0J;ea4u4;barras5er09pA;owe4;if6;aQeIiBrA;if0;sAzz6;aEgDhearCsen0tA;rAur11;ac0es5;te9;us0;ppoin0r8;biliGcDfi9gra3ligh0mBpres5sAvasG;erE;an3ea9orA;ali0L;a6eiBli9rA;ea5;vi1;ta0;maPri1s7un0zz2;aPhMlo5oAripp2ut0;mGnArrespon3;cer9fDspi4tA;inBrA;as0ibu0ol2;ui1;lic0u5;ni1;fDmCpA;eAromi5;l2ti1;an3;or0;aAil2;llenAnAr8;gi1;l8ptAri1;iva0;aff2eGin3lFoDrBuA;d3st2;eathtaAui5;ki1;gg2i2o8ri1unA;ci1;in3;co8wiA;lAtc7;de4;bsorVcOgonMlJmHnno6ppea2rFsA;pi4su4toA;nBun3;di1;is7;hi1;res0;li1;aFu5;si1;ar8lu4;ri1;mi1;iAzi1;zi1;cAhi1;eleDomA;moBpan6;yi1;da0;ra0;ti1;bi1;ng",Comparable:"true\xa60:3C;1:3Q;2:3F;a3Tb3Cc33d2Te2Mf2Ag1Wh1Li1Fj1Ek1Bl13m0Xn0So0Rp0Iqu0Gr07sHtCug0vAw4y3za0Q;el10ouN;ary,e6hi5i3ry;ck0Cde,l3n1ry,se;d,y;ny,te;a3i3R;k,ry;a3erda2ulgar;gue,in,st;a6en2Xhi5i4ouZr3;anqu2Cen1ue;dy,g36me0ny;ck,rs28;ll,me,rt,wd3I;aRcaPeOhMiLkin0BlImGoEpDt6u4w3;eet,ift;b3dd0Wperfi21rre28;sta26t21;a8e7iff,r4u3;pUr1;a4ict,o3;ng;ig2Vn0N;a1ep,rn;le,rk,te0;e1Si2Vright0;ci1Yft,l3on,re;emn,id;a3el0;ll,rt;e4i3y;g2Mm0Z;ek,nd2T;ck24l0mp1L;a3iRrill,y;dy,l01rp;ve0Jxy;n1Jr3;ce,y;d,fe,int0l1Hv0V;a8e6i5o3ude;mantic,o19sy,u3;gh;pe,t1P;a3d,mo0A;dy,l;gg4iFndom,p3re,w;id;ed;ai2i3;ck,et;hoAi1Fl9o8r5u3;ny,r3;e,p11;egna2ic4o3;fouSud;ey,k0;liXor;ain,easa2;ny;dd,i0ld,ranL;aive,e5i4o3u14;b0Sisy,rm0Ysy;bb0ce,mb0R;a3r1w;r,t;ad,e5ild,o4u3;nda12te;ist,o1;a4ek,l3;low;s0ty;a8e7i6o3ucky;f0Jn4o15u3ve0w10y0N;d,sy;e0g;ke0l,mp,tt0Eve0;e1Qwd;me,r3te;ge;e4i3;nd;en;ol0ui19;cy,ll,n3;secu6t3;e3ima4;llege2rmedia3;te;re;aAe7i6o5u3;ge,m3ng1C;bYid;me0t;gh,l0;a3fXsita2;dy,rWv3;en0y;nd13ppy,r3;d3sh;!y;aFenEhCiBlAoofy,r3;a8e6i5o3ue0Z;o3ss;vy;m,s0;at,e3y;dy,n;nd,y;ad,ib,ooD;a2d1;a3o3;st0;tDuiS;u1y;aCeebBi9l8o6r5u3;ll,n3r0N;!ny;aCesh,iend0;a3nd,rmD;my;at,ir7;erce,nan3;ci9;le;r,ul3;ty;a6erie,sse4v3xtre0B;il;nti3;al;r4s3;tern,y;ly,th0;appZe9i5ru4u3;mb;nk;r5vi4z3;zy;ne;e,ty;a3ep,n9;d3f,r;!ly;agey,h8l7o5r4u3;dd0r0te;isp,uel;ar3ld,mmon,st0ward0zy;se;evKou1;e3il0;ap,e3;sy;aHiFlCoAr5u3;ff,r0sy;ly;a6i3oad;g4llia2;nt;ht;sh,ve;ld,un3;cy;a4o3ue;nd,o1;ck,nd;g,tt3;er;d,ld,w1;dy;bsu6ng5we3;so3;me;ry;rd",Adverb:"true\xa6a08b05d00eYfSheQinPjustOkinda,likewiZmMnJoEpCquite,r9s5t2u0very,well;ltima01p0; to,wards5;h1iny bit,o0wiO;o,t6;en,us;eldom,o0uch;!me1rt0; of;how,times,w0C;a1e0;alS;ndomRth05;ar excellenEer0oint blank; Lhaps;f3n0utright;ce0ly;! 0;ag05moX; courGten;ewJo0; longWt 0;onHwithstand9;aybe,eanwhiNore0;!ovT;! aboX;deed,steY;lla,n0;ce;or3u0;ck1l9rther0;!moK;ing; 0evK;exampCgood,suH;n mas0vI;se;e0irect2; 2fini0;te0;ly;juAtrop;ackward,y 0;far,no0; means,w; GbroFd nauseam,gEl7ny5part,s4t 2w0;ay,hi0;le;be7l0mo7wor7;arge,ea6; soon,i4;mo0way;re;l 3mo2ongsi1ready,so,togeth0ways;er;de;st;b1t0;hat;ut;ain;ad;lot,posteriori",Conjunction:"true\xa6aXbTcReNhowMiEjust00noBo9p8supposing,t5wh0yet;e1il0o3;e,st;n1re0thN; if,by,vM;evL;h0il,o;erefOo0;!uU;lus,rovided th9;r0therwiM;! not; mattEr,w0;! 0;since,th4w7;f4n0; 0asmuch;as mIcaForder t0;h0o;at;! 0;only,t0w0;hen;!ev3;ith2ven0;! 0;if,tB;er;o0uz;s,z;e0ut,y the time;cau1f0;ore;se;lt3nd,s 0;far1if,m0soon1t2;uch0; as;hou0;gh",Currency:"true\xa6$,aud,bQcOdJeurIfHgbp,hkd,iGjpy,kElDp8r7s3usd,x2y1z0\xa2,\xa3,\xa5,ден,лв,руб,฿,₡,₨,€,₭,﷼;lotyQł;en,uanP;af,of;h0t5;e0il5;k0q0;elK;oubleJp,upeeJ;e2ound st0;er0;lingG;n0soF;ceEnies;empi7i7;n,r0wanzaCyatC;!onaBw;ls,nr;ori7ranc9;!os;en3i2kk,o0;b0ll2;ra5;me4n0rham4;ar3;e0ny;nt1;aht,itcoin0;!s",Determiner:"true\xa6aBboth,d9e6few,le5mu8neiDplenty,s4th2various,wh0;at0ich0;evC;a0e4is,ose;!t;everal,ome;!ast,s;a1l0very;!se;ch;e0u;!s;!n0;!o0y;th0;er","Adj|Present":"true\xa6a07b04cVdQeNfJhollIidRlEmCnarrIoBp9qua8r7s3t2uttFw0;aKet,ro0;ng,u08;endChin;e2hort,l1mooth,our,pa9tray,u0;re,speU;i2ow;cu6da02leSpaN;eplica01i02;ck;aHerfePr0;eseUime,omV;bscu1pen,wn;atu0e3odeH;re;a2e1ive,ow0;er;an;st,y;ow;a2i1oul,r0;ee,inge;rm;iIke,ncy,st;l1mpty,x0;emHpress;abo4ic7;amp,e2i1oub0ry,ull;le;ffu9re6;fu8libe0;raE;alm,l5o0;mpleCn3ol,rr1unterfe0;it;e0u7;ct;juga8sum7;ea1o0;se;n,r;ankru1lu0;nt;pt;li2pproxi0rticula1;ma0;te;ght","Person|Adj":"true\xa6b3du2earnest,frank,mi2r0san1woo1;an0ich,u1;dy;sty;ella,rown",Modal:"true\xa6c5lets,m4ought3sh1w0;ill,o5;a0o4;ll,nt;! to,a;ight,ust;an,o0;uld",Verb:"true\xa6born,cannot,gonna,has,keep tabs,msg","Person|Verb":"true\xa6b8ch7dr6foster,gra5ja9lan4ma2ni9ollie,p1rob,s0wade;kip,pike,t5ue;at,eg,ier2;ck,r0;k,shal;ce;ce,nt;ew;ase,u1;iff,l1ob,u0;ck;aze,ossom","Person|Date":"true\xa6a2j0sep;an0une;!uary;p0ugust,v0;ril"};let ao="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("").reduce(function(e,t,n){return e[t]=n,e},{});var as={fromAlphaCode:function(e){if(void 0!==ao[e])return ao[e];let t=0,n=1,r=36,a=1;for(;n<e.length;t+=r,n++,r*=36);for(let n=e.length-1;n>=0;n--,a*=36){let r=e.charCodeAt(n)-48;r>10&&(r-=7),t+=r*a}return t}},al=function(e){let t=RegExp("([0-9A-Z]+):([0-9A-Z]+)");for(let n=0;n<e.nodes.length;n++){let r=t.exec(e.nodes[n]);if(!r){e.symCount=n;break}e.syms[as.fromAlphaCode(r[1])]=as.fromAlphaCode(r[2])}e.nodes=e.nodes.slice(e.symCount,e.nodes.length)};let au=function(e,t,n){let r=as.fromAlphaCode(t);return r<e.symCount?e.syms[r]:n+r+1-e.symCount},ac=function(e){let t=[],n=(r,a)=>{let i=e.nodes[r];"!"===i[0]&&(t.push(a),i=i.slice(1));let o=i.split(/([A-Z0-9,]+)/g);for(let i=0;i<o.length;i+=2){let s=o[i],l=o[i+1];if(!s)continue;let u=a+s;if(","===l||void 0===l){t.push(u);continue}n(au(e,l,r),u)}};return n(0,""),t};var ah=function(e){let t={nodes:e.split(";"),syms:[],symCount:0};return e.match(":")&&al(t),ac(t)},ad=function(e){if(!e)return{};let t=e.split("|").reduce((e,t)=>{let n=t.split("\xa6");return e[n[0]]=n[1],e},{}),n={};return Object.keys(t).forEach(function(e){let r=ah(t[e]);"true"===e&&(e=!0);for(let t=0;t<r.length;t++){let a=r[t];!0===n.hasOwnProperty(a)?!1===Array.isArray(n[a])?n[a]=[n[a],e]:n[a].push(e):n[a]=e}}),n};let ag=["Possessive","Pronoun"];var ap={a:[[/(antenn|formul|nebul|vertebr|vit)a$/i,"$1ae"],[/ia$/i,"ia"]],e:[[/(kn|l|w)ife$/i,"$1ives"],[/(hive)$/i,"$1s"],[/([m|l])ouse$/i,"$1ice"],[/([m|l])ice$/i,"$1ice"]],f:[[/^(dwar|handkerchie|hoo|scar|whar)f$/i,"$1ves"],[/^((?:ca|e|ha|(?:our|them|your)?se|she|wo)l|lea|loa|shea|thie)f$/i,"$1ves"]],i:[[/(octop|vir)i$/i,"$1i"]],m:[[/([ti])um$/i,"$1a"]],n:[[/^(oxen)$/i,"$1"]],o:[[/(al|ad|at|er|et|ed)o$/i,"$1oes"]],s:[[/(ax|test)is$/i,"$1es"],[/(alias|status)$/i,"$1es"],[/sis$/i,"ses"],[/(bu)s$/i,"$1ses"],[/(sis)$/i,"ses"],[/^(?!talis|.*hu)(.*)man$/i,"$1men"],[/(octop|vir|radi|nucle|fung|cact|stimul)us$/i,"$1i"]],x:[[/(matr|vert|ind|cort)(ix|ex)$/i,"$1ices"],[/^(ox)$/i,"$1en"]],y:[[/([^aeiouy]|qu)y$/i,"$1ies"]],z:[[/(quiz)$/i,"$1zes"]]};let am=/([xsz]|ch|sh)$/,af=function(e){let t=e[e.length-1];if(!0===ap.hasOwnProperty(t))for(let n=0;n<ap[t].length;n+=1){let r=ap[t][n][0];if(!0===r.test(e))return e.replace(r,ap[t][n][1])}return null};var ab=function(e="",t){let{irregularPlurals:n,uncountable:r}=t.two;if(r.hasOwnProperty(e))return e;if(n.hasOwnProperty(e))return n[e];let a=af(e);return null!==a?a:am.test(e)?e+"es":e+"s"};let av=/\|/,ay={"20th century fox":"Organization","7 eleven":"Organization","motel 6":"Organization",g8:"Organization",vh1:"Organization","76ers":"SportsTeam","49ers":"SportsTeam",q1:"Date",q2:"Date",q3:"Date",q4:"Date",km2:"Unit",m2:"Unit",dm2:"Unit",cm2:"Unit",mm2:"Unit",mile2:"Unit",in2:"Unit",yd2:"Unit",ft2:"Unit",m3:"Unit",dm3:"Unit",cm3:"Unit",in3:"Unit",ft3:"Unit",yd3:"Unit","at&t":"Organization","black & decker":"Organization","h & m":"Organization","johnson & johnson":"Organization","procter & gamble":"Organization","ben & jerry's":"Organization","&":"Conjunction",i:["Pronoun","Singular"],he:["Pronoun","Singular"],she:["Pronoun","Singular"],it:["Pronoun","Singular"],they:["Pronoun","Plural"],we:["Pronoun","Plural"],was:["Copula","PastTense"],is:["Copula","PresentTense"],are:["Copula","PresentTense"],am:["Copula","PresentTense"],were:["Copula","PastTense"],her:ag,his:ag,hers:ag,their:ag,theirs:ag,themselves:ag,your:ag,our:ag,ours:ag,my:ag,its:ag,vs:["Conjunction","Abbreviation"],if:["Condition","Preposition"],closer:"Comparative",closest:"Superlative",much:"Adverb",may:"Modal",babysat:"PastTense",blew:"PastTense",drank:"PastTense",drove:"PastTense",forgave:"PastTense",skiied:"PastTense",spilt:"PastTense",stung:"PastTense",swam:"PastTense",swung:"PastTense",guaranteed:"PastTense",shrunk:"PastTense",nears:"PresentTense",nearing:"Gerund",neared:"PastTense",no:["Negative","Expression"]},aw={},ak={two:{irregularPlurals:aa,uncountable:{}}};Object.keys(ai).forEach(e=>{let t=ad(ai[e]);if(!av.test(e)){Object.keys(t).forEach(t=>{ay[t]=e});return}Object.keys(t).forEach(t=>{aw[t]=e,"Noun|Verb"===e&&(aw[ab(t,ak)]="Plural|Verb")})}),[":(",":)",":P",":p",":O",";(",";)",";P",";p",";O",":3",":|",":/",":\\",":$",":*",":@",":-(",":-)",":-P",":-p",":-O",":-3",":-|",":-/",":-\\",":-$",":-*",":-@",":^(",":^)",":^P",":^p",":^O",":^3",":^|",":^/",":^\\",":^$",":^*",":^@","):","(:","$:","*:",")-:","(-:","$-:","*-:",")^:","(^:","$^:","*^:","<3","</3","<\\3","=("].forEach(e=>ay[e]="Emoticon"),delete ay[""],delete ay[null],delete ay[" "];let aP="Singular";var aA={Determiner:aP,Possessive:aP,Acronym:aP,Noun:aP,Adjective:aP,PresentTense:aP,Gerund:aP,PastTense:aP,Infinitive:aP,Date:aP,Ordinal:aP,Demonym:aP},ax={Value:aP,Modal:aP,Copula:aP,PresentTense:aP,PastTense:aP,Demonym:aP,Actor:aP},aC={the:aP,with:aP,without:aP,of:aP,for:aP,any:aP,all:aP,on:aP,cut:aP,cuts:aP,increase:aP,decrease:aP,raise:aP,drop:aP,save:aP,saved:aP,saves:aP,make:aP,makes:aP,made:aP,minus:aP,plus:aP,than:aP,another:aP,versus:aP,neither:aP,about:aP,favorite:aP,best:aP,daily:aP,weekly:aP,linear:aP,binary:aP,mobile:aP,lexical:aP,technical:aP,computer:aP,scientific:aP,security:aP,government:aP,popular:aP,formal:aP,no:aP,more:aP,one:aP,let:aP,her:aP,his:aP,their:aP,our:aP,us:aP,sheer:aP,monthly:aP,yearly:aP,current:aP,previous:aP,upcoming:aP,last:aP,next:aP,main:aP,initial:aP,final:aP,beginning:aP,end:aP,top:aP,bottom:aP,future:aP,past:aP,major:aP,minor:aP,side:aP,central:aP,peripheral:aP,public:aP,private:aP},aj={of:aP,system:aP,aid:aP,method:aP,utility:aP,tool:aP,reform:aP,therapy:aP,philosophy:aP,room:aP,authority:aP,says:aP,said:aP,wants:aP,wanted:aP,is:aP,did:aP,do:aP,can:aP,wise:aP};let aN="Infinitive";var aI={Modal:aN,Adverb:aN,Negative:aN,Plural:aN},aD={Determiner:aN,Adverb:aN,Possessive:aN,Reflexive:aN,Preposition:aN,Cardinal:aN,Comparative:aN,Superlative:aN},aT={i:aN,we:aN,you:aN,they:aN,to:aN,please:aN,will:aN,have:aN,had:aN,would:aN,could:aN,should:aN,do:aN,did:aN,does:aN,can:aN,must:aN,us:aN,me:aN,let:aN,even:aN,when:aN,help:aN,he:aN,she:aN,it:aN,being:aN,bi:aN,co:aN,contra:aN,de:aN,inter:aN,intra:aN,mis:aN,pre:aN,out:aN,counter:aN,nobody:aN,somebody:aN,anybody:aN,everybody:aN},aO={the:aN,me:aN,you:aN,him:aN,us:aN,her:aN,his:aN,them:aN,they:aN,it:aN,himself:aN,herself:aN,itself:aN,myself:aN,ourselves:aN,themselves:aN,something:aN,anything:aN,a:aN,an:aN,up:aN,down:aN,by:aN,out:aN,off:aN,under:aN,what:aN,all:aN,to:aN,because:aN,although:aN,how:aN,otherwise:aN,together:aN,though:aN,into:aN,yet:aN,more:aN,here:aN,there:aN,away:aN};let aH={beforeTags:Object.assign({},aI,aA,{}),afterTags:Object.assign({},aD,ax,{}),beforeWords:Object.assign({},aT,aC,{}),afterWords:Object.assign({},aO,aj,{})},aE="Adjective";var aF={Determiner:aE,Possessive:aE,Hyphenated:aE},aG={Adjective:aE},aV={seem:aE,seemed:aE,seems:aE,feel:aE,feels:aE,felt:aE,stay:aE,appear:aE,appears:aE,appeared:aE,also:aE,over:aE,under:aE,too:aE,it:aE,but:aE,still:aE,really:aE,quite:aE,well:aE,very:aE,truly:aE,how:aE,deeply:aE,hella:aE,profoundly:aE,extremely:aE,so:aE,badly:aE,mostly:aE,totally:aE,awfully:aE,rather:aE,nothing:aE,something:aE,anything:aE,not:aE,me:aE,is:aE,face:aE,faces:aE,faced:aE,look:aE,looks:aE,looked:aE,reveal:aE,reveals:aE,revealed:aE,sound:aE,sounded:aE,sounds:aE,remains:aE,remained:aE,prove:aE,proves:aE,proved:aE,becomes:aE,stays:aE,tastes:aE,taste:aE,smells:aE,smell:aE,gets:aE,grows:aE,as:aE,rings:aE,radiates:aE,conveys:aE,convey:aE,conveyed:aE,of:aE},az={too:aE,also:aE,or:aE,enough:aE,as:aE};let aM="Gerund";var aB={Adverb:aM,Preposition:aM,Conjunction:aM},aS={Adverb:aM,Possessive:aM,Person:aM,Pronoun:aM,Determiner:aM,Copula:aM,Preposition:aM,Conjunction:aM,Comparative:aM},a$={been:aM,keep:aM,continue:aM,stop:aM,am:aM,be:aM,me:aM,began:aM,start:aM,starts:aM,started:aM,stops:aM,stopped:aM,help:aM,helps:aM,avoid:aM,avoids:aM,love:aM,loves:aM,loved:aM,hate:aM,hates:aM,hated:aM},aL={you:aM,me:aM,her:aM,him:aM,his:aM,them:aM,their:aM,it:aM,this:aM,there:aM,on:aM,about:aM,for:aM,up:aM,down:aM};let aK="Gerund",aJ="Adjective",aW={beforeTags:Object.assign({},aF,aB,{Imperative:aK,Infinitive:aJ,Plural:aK}),afterTags:Object.assign({},aG,aS,{Noun:aJ}),beforeWords:Object.assign({},aV,a$,{is:aJ,are:aK,was:aJ,of:aJ,suggest:aK,suggests:aK,suggested:aK,recommend:aK,recommends:aK,recommended:aK,imagine:aK,imagines:aK,imagined:aK,consider:aK,considered:aK,considering:aK,resist:aK,resists:aK,resisted:aK,avoid:aK,avoided:aK,avoiding:aK,except:aJ,accept:aJ,assess:aK,explore:aK,fear:aK,fears:aK,appreciate:aK,question:aK,help:aK,embrace:aK,with:aJ}),afterWords:Object.assign({},az,aL,{to:aK,not:aK,the:aK})},aq={beforeTags:Object.assign({},aF,aA,{Determiner:void 0,Cardinal:"Noun",PhrasalVerb:"Adjective"}),afterTags:Object.assign({},aG,ax,{}),beforeWords:Object.assign({},aV,aC,{are:"Adjective",is:"Adjective",was:"Adjective",be:"Adjective",off:"Adjective",out:"Adjective"}),afterWords:Object.assign({},az,aj)},aR="PastTense",aU="Adjective";var a_={beforeTags:Object.assign({},aF,{Adverb:aR,Pronoun:aR,ProperNoun:aR,Auxiliary:aR,Noun:aR}),afterTags:Object.assign({},aG,{Possessive:aR,Pronoun:aR,Determiner:aR,Adverb:aR,Comparative:aR,Date:aR,Gerund:aR}),beforeWords:Object.assign({},aV,{be:aR,who:aR,get:aU,had:aR,has:aR,have:aR,been:aR,it:aR,as:aR,for:aU,more:aU,always:aU}),afterWords:Object.assign({},az,{by:aR,back:aR,out:aR,in:aR,up:aR,down:aR,before:aR,after:aR,for:aR,the:aR,with:aR,as:aR,on:aR,at:aR,between:aR,to:aR,into:aR,us:aR,them:aR,his:aR,her:aR,their:aR,our:aR,me:aR,about:aU})};let aQ={beforeTags:Object.assign({},aF,aI,{Adverb:void 0,Negative:void 0}),afterTags:Object.assign({},aG,aD,{Noun:"Adjective",Conjunction:void 0}),beforeWords:Object.assign({},aV,aT,{have:void 0,had:void 0,not:void 0,went:"Adjective",goes:"Adjective",got:"Adjective",be:"Adjective"}),afterWords:Object.assign({},az,aO,{to:void 0,as:"Adjective"})},aZ={beforeTags:Object.assign({},aB,aA,{Copula:"Gerund",PastTense:"Gerund",PresentTense:"Gerund",Infinitive:"Gerund"}),afterTags:Object.assign({},aS,ax,{Value:"Gerund"}),beforeWords:Object.assign({},a$,aC,{are:"Gerund",were:"Gerund",be:"Gerund",no:"Gerund",without:"Gerund",you:"Gerund",we:"Gerund",they:"Gerund",he:"Gerund",she:"Gerund",us:"Gerund",them:"Gerund"}),afterWords:Object.assign({},aL,aj,{the:"Gerund",this:"Gerund",that:"Gerund",me:"Gerund",us:"Gerund",them:"Gerund"})},aY="Singular",aX="Infinitive",a0={beforeTags:Object.assign({},aI,aA,{Adjective:aY,Particle:aY}),afterTags:Object.assign({},aD,ax,{ProperNoun:aX,Gerund:aX,Adjective:aX,Copula:aY}),beforeWords:Object.assign({},aT,aC,{is:aY,was:aY,of:aY,have:null}),afterWords:Object.assign({},aO,aj,{instead:aX,about:aX,his:aX,her:aX,to:null,by:null,in:null})},a1="Person";var a2={Honorific:a1,Person:a1},a3={Person:a1,ProperNoun:a1,Verb:a1},a5={hi:a1,hey:a1,yo:a1,dear:a1,hello:a1},a4={said:a1,says:a1,told:a1,tells:a1,feels:a1,felt:a1,seems:a1,thinks:a1,thought:a1,spends:a1,spendt:a1,plays:a1,played:a1,sing:a1,sang:a1,learn:a1,learned:a1,wants:a1,wanted:a1};let a6="Month";var a9={beforeTags:Object.assign({},a2,{Date:a6,Value:a6}),afterTags:Object.assign({},a3,{Date:a6,Value:a6}),beforeWords:Object.assign({},a5,{by:a6,in:a6,on:a6,during:a6,after:a6,before:a6,between:a6,until:a6,til:a6,sometime:a6,of:a6,this:a6,next:a6,last:a6,previous:a6,following:a6,with:"Person"}),afterWords:Object.assign({},a4,{sometime:a6,in:a6,of:a6,until:a6,the:a6})};let a7={beforeTags:Object.assign({},aA,a2),afterTags:Object.assign({},ax,a3),beforeWords:Object.assign({},aC,a5,{i:"Infinitive",we:"Infinitive"}),afterWords:Object.assign({},aj,a4)},a8={beforeTags:Object.assign({},aA,a2,aI),afterTags:Object.assign({},ax,a3,aD),beforeWords:Object.assign({},aC,a5,aT),afterWords:Object.assign({},aj,a4,aO)},ie="Place",it={beforeTags:Object.assign({},{Place:ie},a2),afterTags:Object.assign({},{Place:ie,Abbreviation:ie},a3),beforeWords:Object.assign({},{in:ie,by:ie,near:ie,from:ie,to:ie},a5),afterWords:Object.assign({},{in:ie,by:ie,near:ie,from:ie,to:ie,government:ie,council:ie,region:ie,city:ie},a4)},ir={beforeTags:Object.assign({},a2,aF),afterTags:Object.assign({},a3,aG),beforeWords:Object.assign({},a5,aV),afterWords:Object.assign({},a4,az)},ia="Unit",ii={"Actor|Verb":aH,"Adj|Gerund":aW,"Adj|Noun":aq,"Adj|Past":a_,"Adj|Present":aQ,"Noun|Verb":a0,"Noun|Gerund":aZ,"Person|Noun":a7,"Person|Date":a9,"Person|Verb":a8,"Person|Place":it,"Person|Adj":ir,"Unit|Noun":{beforeTags:{Value:ia},afterTags:{},beforeWords:{per:ia,every:ia,each:ia,square:ia,cubic:ia,sq:ia,metric:ia},afterWords:{per:ia,squared:ia,cubed:ia,long:ia}}},io=(e,t)=>Object.assign(Object.keys(e).reduce((t,n)=>(t[n]="Infinitive"===e[n]?"PresentTense":"Plural",t),{}),t);ii["Plural|Verb"]={beforeWords:io(ii["Noun|Verb"].beforeWords,{had:"Plural",have:"Plural"}),afterWords:io(ii["Noun|Verb"].afterWords,{his:"PresentTense",her:"PresentTense",its:"PresentTense",in:null,to:null,is:"PresentTense",by:"PresentTense"}),beforeTags:io(ii["Noun|Verb"].beforeTags,{Conjunction:"PresentTense",Noun:void 0,ProperNoun:"PresentTense"}),afterTags:io(ii["Noun|Verb"].afterTags,{Gerund:"Plural",Noun:"PresentTense",Value:"PresentTense"})};let is="Adjective",il="Infinitive",iu="PresentTense",ic="Singular",ih="PastTense",id="Adverb",ig="Plural",ip="Actor",im="Verb",ib="Noun",iv="LastName",iy="Modal",iw="Place",ik="Participle",iP="Adjective",iA="Noun",ix="Verb",iC="Adjective",ij="Infinitive",iN="PresentTense",iI="Singular",iD="PastTense",iT="Adverb",iO="Expression",iH="Actor",iE="Verb",iF="Noun",iG="LastName",iV="Verb",iz="Noun",iM=/^([0-9]+)/,iB=function(e){let t={};return e.split("\xa6").forEach(e=>{let[n,r]=e.split(":");(r=(r||"").split(",")).forEach(e=>{t[e]=n})}),t},iS=function(e="",t=""){let n=(t=String(t)).match(iM);if(null===n)return t;let r=Number(n[1])||0;return e.substring(0,r)+t.replace(iM,"")},i$=function(e){let t=iB(e);return Object.keys(t).reduce((e,n)=>(e[n]=iS(n,t[n]),e),{})};var iL=function(e={}){return"string"==typeof e&&(e=JSON.parse(e)),e.fwd=i$(e.fwd||""),e.both=i$(e.both||""),e.rev=i$(e.rev||""),e.ex=i$(e.ex||""),e};let iK=function(e){return Object.entries(e).reduce((e,t)=>(e[t[1]]=t[0],e),{})};var iJ=function(e={}){return{reversed:!0,both:iK(e.both),ex:iK(e.ex),fwd:e.rev||{}}};let iW=iL({fwd:"1:tted,wed,gged,nned,een,rred,pped,yed,bbed,oed,dded,rd,wn,mmed\xa62:eed,nded,et,hted,st,oled,ut,emed,eled,lded,ken,rt,nked,apt,ant,eped,eked\xa63:eared,eat,eaded,nelled,ealt,eeded,ooted,eaked,eaned,eeted,mited,bid,uit,ead,uited,ealed,geted,velled,ialed,belled\xa64:ebuted,hined,comed\xa6y:ied\xa6ome:ame\xa6ear:ore\xa6ind:ound\xa6ing:ung,ang\xa6ep:pt\xa6ink:ank,unk\xa6ig:ug\xa6all:ell\xa6ee:aw\xa6ive:ave\xa6eeze:oze\xa6old:eld\xa6ave:ft\xa6ake:ook\xa6ell:old\xa6ite:ote\xa6ide:ode\xa6ine:one\xa6in:un,on\xa6eal:ole\xa6im:am\xa6ie:ay\xa6and:ood\xa61ise:rose\xa61eak:roke\xa61ing:rought\xa61ive:rove\xa61el:elt\xa61id:bade\xa61et:got\xa61y:aid\xa61it:sat\xa63e:lid\xa63d:pent",both:"1:aed,fed,xed,hed\xa62:sged,xted,wled,rped,lked,kied,lmed,lped,uped,bted,rbed,rked,wned,rled,mped,fted,mned,mbed,zzed,omed,ened,cked,gned,lted,sked,ued,zed,nted,ered,rted,rmed,ced,sted,rned,ssed,rded,pted,ved,cted\xa63:cled,eined,siped,ooned,uked,ymed,jored,ouded,ioted,oaned,lged,asped,iged,mured,oided,eiled,yped,taled,moned,yled,lit,kled,oaked,gled,naled,fled,uined,oared,valled,koned,soned,aided,obed,ibed,meted,nicked,rored,micked,keted,vred,ooped,oaded,rited,aired,auled,filled,ouled,ooded,ceted,tolled,oited,bited,aped,tled,vored,dled,eamed,nsed,rsed,sited,owded,pled,sored,rged,osed,pelled,oured,psed,oated,loned,aimed,illed,eured,tred,ioned,celled,bled,wsed,ooked,oiled,itzed,iked,iased,onged,ased,ailed,uned,umed,ained,auded,nulled,ysed,eged,ised,aged,oined,ated,used,dged,doned\xa64:ntied,efited,uaked,caded,fired,roped,halled,roked,himed,culed,tared,lared,tuted,uared,routed,pited,naked,miled,houted,helled,hared,cored,caled,tired,peated,futed,ciled,called,tined,moted,filed,sided,poned,iloted,honed,lleted,huted,ruled,cured,named,preted,vaded,sured,talled,haled,peded,gined,nited,uided,ramed,feited,laked,gured,ctored,unged,pired,cuted,voked,eloped,ralled,rined,coded,icited,vided,uaded,voted,mined,sired,noted,lined,nselled,luted,jured,fided,puted,piled,pared,olored,cided,hoked,enged,tured,geoned,cotted,lamed,uiled,waited,udited,anged,luded,mired,uired,raded\xa65:modelled,izzled,eleted,umpeted,ailored,rseded,treated,eduled,ecited,rammed,eceded,atrolled,nitored,basted,twined,itialled,ncited,gnored,ploded,xcited,nrolled,namelled,plored,efeated,redited,ntrolled,nfined,pleted,llided,lcined,eathed,ibuted,lloted,dhered,cceded\xa63ad:sled\xa62aw:drew\xa62ot:hot\xa62ke:made\xa62ow:hrew,grew\xa62ose:hose\xa62d:ilt\xa62in:egan\xa61un:ran\xa61ink:hought\xa61ick:tuck\xa61ike:ruck\xa61eak:poke,nuck\xa61it:pat\xa61o:did\xa61ow:new\xa61ake:woke\xa6go:went",rev:"3:rst,hed,hut,cut,set\xa64:tbid\xa65:dcast,eread,pread,erbid\xa6ought:uy,eek\xa61ied:ny,ly,dy,ry,fy,py,vy,by,ty,cy\xa61ung:ling,ting,wing\xa61pt:eep\xa61ank:rink\xa61ore:bear,wear\xa61ave:give\xa61oze:reeze\xa61ound:rind,wind\xa61ook:take,hake\xa61aw:see\xa61old:sell\xa61ote:rite\xa61ole:teal\xa61unk:tink\xa61am:wim\xa61ay:lie\xa61ood:tand\xa61eld:hold\xa62d:he,ge,re,le,leed,ne,reed,be,ye,lee,pe,we\xa62ed:dd,oy,or,ey,gg,rr,us,ew,to\xa62ame:ecome,rcome\xa62ped:ap\xa62ged:ag,og,ug,eg\xa62bed:ub,ab,ib,ob\xa62lt:neel\xa62id:pay\xa62ang:pring\xa62ove:trive\xa62med:um\xa62ode:rride\xa62at:ysit\xa63ted:mit,hat,mat,lat,pot,rot,bat\xa63ed:low,end,tow,und,ond,eem,lay,cho,dow,xit,eld,ald,uld,law,lel,eat,oll,ray,ank,fin,oam,out,how,iek,tay,haw,ait,vet,say,cay,bow\xa63d:ste,ede,ode,ete,ree,ude,ame,oke,ote,ime,ute,ade\xa63red:lur,cur,pur,car\xa63ped:hop,rop,uip,rip,lip,tep,top\xa63ded:bed,rod,kid\xa63ade:orbid\xa63led:uel\xa63ned:lan,can,kin,pan,tun\xa63med:rim,lim\xa64ted:quit,llot\xa64ed:pear,rrow,rand,lean,mand,anel,pand,reet,link,abel,evel,imit,ceed,ruit,mind,peal,veal,hool,head,pell,well,mell,uell,band,hear,weak\xa64led:nnel,qual,ebel,ivel\xa64red:nfer,efer,sfer\xa64n:sake,trew\xa64d:ntee\xa64ded:hred\xa64ned:rpin\xa65ed:light,nceal,right,ndear,arget,hread,eight,rtial,eboot\xa65d:edite,nvite\xa65ted:egret\xa65led:ravel",ex:"2:been,upped\xa63:added,aged,aided,aimed,aired,bid,died,dyed,egged,erred,eyed,fit,gassed,hit,lied,owed,pent,pied,tied,used,vied,oiled,outed,banned,barred,bet,canned,cut,dipped,donned,ended,feed,inked,jarred,let,manned,mowed,netted,padded,panned,pitted,popped,potted,put,set,sewn,sowed,tanned,tipped,topped,vowed,weed,bowed,jammed,binned,dimmed,hopped,mopped,nodded,pinned,rigged,sinned,towed,vetted\xa64:ached,baked,baled,boned,bored,called,caned,cared,ceded,cited,coded,cored,cubed,cured,dared,dined,edited,exited,faked,fared,filed,fined,fired,fuelled,gamed,gelled,hired,hoped,joked,lined,mined,named,noted,piled,poked,polled,pored,pulled,reaped,roamed,rolled,ruled,seated,shed,sided,timed,tolled,toned,voted,waited,walled,waned,winged,wiped,wired,zoned,yelled,tamed,lubed,roped,faded,mired,caked,honed,banged,culled,heated,raked,welled,banded,beat,cast,cooled,cost,dealt,feared,folded,footed,handed,headed,heard,hurt,knitted,landed,leaked,leapt,linked,meant,minded,molded,neared,needed,peaked,plodded,plotted,pooled,quit,read,rooted,sealed,seeded,seeped,shipped,shunned,skimmed,slammed,sparred,stemmed,stirred,suited,thinned,twinned,swayed,winked,dialed,abutted,blotted,fretted,healed,heeded,peeled,reeled\xa65:basted,cheated,equalled,eroded,exiled,focused,opined,pleated,primed,quoted,scouted,shored,sloped,smoked,sniped,spelled,spouted,routed,staked,stored,swelled,tasted,treated,wasted,smelled,dwelled,honored,prided,quelled,eloped,scared,coveted,sweated,breaded,cleared,debuted,deterred,freaked,modeled,pleaded,rebutted,speeded\xa66:anchored,defined,endured,impaled,invited,refined,revered,strolled,cringed,recast,thrust,unfolded\xa67:authored,combined,competed,conceded,convened,excreted,extruded,redefined,restored,secreted,rescinded,welcomed\xa68:expedited,infringed\xa69:interfered,intervened,persevered\xa610:contravened\xa6eat:ate\xa6is:was\xa6go:went\xa6are:were\xa63d:bent,lent,rent,sent\xa63e:bit,fled,hid,lost\xa63ed:bled,bred\xa62ow:blew,grew\xa61uy:bought\xa62tch:caught\xa61o:did\xa61ive:dove,gave\xa62aw:drew\xa62ed:fed\xa62y:flew,laid,paid,said\xa61ight:fought\xa61et:got\xa62ve:had\xa61ang:hung\xa62ad:led\xa62ght:lit\xa62ke:made\xa62et:met\xa61un:ran\xa61ise:rose\xa61it:sat\xa61eek:sought\xa61each:taught\xa61ake:woke,took\xa61eave:wove\xa62ise:arose\xa61ear:bore,tore,wore\xa61ind:bound,found,wound\xa62eak:broke\xa62ing:brought,wrung\xa61ome:came\xa62ive:drove\xa61ig:dug\xa61all:fell\xa62el:felt\xa64et:forgot\xa61old:held\xa62ave:left\xa61ing:rang,sang\xa61ide:rode\xa61ink:sank\xa61ee:saw\xa62ine:shone\xa64e:slid\xa61ell:sold,told\xa64d:spent\xa62in:spun\xa61in:won"}),iq=iL({fwd:"1:oes\xa61ve:as",both:"1:xes\xa62:zzes,ches,shes,sses\xa63:iases\xa62y:llies,plies\xa61y:cies,bies,ties,vies,nies,pies,dies,ries,fies\xa6:s",rev:"1ies:ly\xa62es:us,go,do\xa63es:cho,eto",ex:"2:does,goes\xa63:gasses\xa65:focuses\xa6is:are\xa63y:relies\xa62y:flies\xa62ve:has"}),iR=iL({fwd:"1:nning,tting,rring,pping,eing,mming,gging,dding,bbing,kking\xa62:eking,oling,eling,eming\xa63:velling,siting,uiting,fiting,loting,geting,ialing,celling\xa64:graming",both:"1:aing,iing,fing,xing,ying,oing,hing,wing\xa62:tzing,rping,izzing,bting,mning,sping,wling,rling,wding,rbing,uping,lming,wning,mping,oning,lting,mbing,lking,fting,hting,sking,gning,pting,cking,ening,nking,iling,eping,ering,rting,rming,cting,lping,ssing,nting,nding,lding,sting,rning,rding,rking\xa63:belling,siping,toming,yaking,uaking,oaning,auling,ooping,aiding,naping,euring,tolling,uzzing,ganing,haning,ualing,halling,iasing,auding,ieting,ceting,ouling,voring,ralling,garing,joring,oaming,oaking,roring,nelling,ooring,uelling,eaming,ooding,eaping,eeting,ooting,ooming,xiting,keting,ooking,ulling,airing,oaring,biting,outing,oiting,earing,naling,oading,eeding,ouring,eaking,aiming,illing,oining,eaning,onging,ealing,aining,eading\xa64:thoming,melling,aboring,ivoting,weating,dfilling,onoring,eriting,imiting,tialling,rgining,otoring,linging,winging,lleting,louding,spelling,mpelling,heating,feating,opelling,choring,welling,ymaking,ctoring,calling,peating,iloring,laiting,utoring,uditing,mmaking,loating,iciting,waiting,mbating,voiding,otalling,nsoring,nselling,ocusing,itoring,eloping\xa65:rselling,umpeting,atrolling,treating,tselling,rpreting,pringing,ummeting,ossoming,elmaking,eselling,rediting,totyping,onmaking,rfeiting,ntrolling\xa65e:chmaking,dkeeping,severing,erouting,ecreting,ephoning,uthoring,ravening,reathing,pediting,erfering,eotyping,fringing,entoring,ombining,ompeting\xa64e:emaking,eething,twining,rruling,chuting,xciting,rseding,scoping,edoring,pinging,lunging,agining,craping,pleting,eleting,nciting,nfining,ncoding,tponing,ecoding,writing,esaling,nvening,gnoring,evoting,mpeding,rvening,dhering,mpiling,storing,nviting,ploring\xa63e:tining,nuring,saking,miring,haling,ceding,xuding,rining,nuting,laring,caring,miling,riding,hoking,piring,lading,curing,uading,noting,taping,futing,paring,hading,loding,siring,guring,vading,voking,during,niting,laning,caping,luting,muting,ruding,ciding,juring,laming,caling,hining,uoting,liding,ciling,duling,tuting,puting,cuting,coring,uiding,tiring,turing,siding,rading,enging,haping,buting,lining,taking,anging,haring,uiring,coming,mining,moting,suring,viding,luding\xa62e:tring,zling,uging,oging,gling,iging,vring,fling,lging,obing,psing,pling,ubing,cling,dling,wsing,iking,rsing,dging,kling,ysing,tling,rging,eging,nsing,uning,osing,uming,using,ibing,bling,aging,ising,asing,ating\xa62ie:rlying\xa61e:zing,uing,cing,ving",rev:"ying:ie\xa61ing:se,ke,te,we,ne,re,de,pe,me,le,c,he\xa62ing:ll,ng,dd,ee,ye,oe,rg,us\xa62ning:un\xa62ging:og,ag,ug,ig,eg\xa62ming:um\xa62bing:ub,ab,eb,ob\xa63ning:lan,can,hin,pin,win\xa63ring:cur,lur,tir,tar,pur,car\xa63ing:ait,del,eel,fin,eat,oat,eem,lel,ool,ein,uin\xa63ping:rop,rap,top,uip,wap,hip,hop,lap,rip,cap\xa63ming:tem,wim,rim,kim,lim\xa63ting:mat,cut,pot,lit,lot,hat,set,pit,put\xa63ding:hed,bed,bid\xa63king:rek\xa63ling:cil,pel\xa63bing:rib\xa64ning:egin\xa64ing:isit,ruit,ilot,nsit,dget,rkel,ival,rcel\xa64ring:efer,nfer\xa64ting:rmit,mmit,ysit,dmit,emit,bmit,tfit,gret\xa64ling:evel,xcel,ivel\xa64ding:hred\xa65ing:arget,posit,rofit\xa65ring:nsfer\xa65ting:nsmit,orget,cquit\xa65ling:ancel,istil",ex:"3:adding,eating,aiming,aiding,airing,outing,gassing,setting,getting,putting,cutting,winning,sitting,betting,mapping,tapping,letting,bidding,hitting,tanning,netting,popping,fitting,capping,lapping,barring,banning,vetting,topping,rotting,tipping,potting,wetting,pitting,dipping,budding,hemming,pinning,jetting,kidding,padding,podding,sipping,wedding,bedding,donning,warring,penning,gutting,cueing,wadding,petting,ripping,napping,matting,tinning,binning,dimming,hopping,mopping,nodding,panning,rapping,ridding,sinning\xa64:selling,falling,calling,waiting,editing,telling,rolling,heating,boating,hanging,beating,coating,singing,tolling,felling,polling,discing,seating,voiding,gelling,yelling,baiting,reining,ruining,seeking,spanning,stepping,knitting,emitting,slipping,quitting,dialing,omitting,clipping,shutting,skinning,abutting,flipping,trotting,cramming,fretting,suiting\xa65:bringing,treating,spelling,stalling,trolling,expelling,rivaling,wringing,deterring,singeing,befitting,refitting\xa66:enrolling,distilling,scrolling,strolling,caucusing,travelling\xa67:installing,redefining,stencilling,recharging,overeating,benefiting,unraveling,programing\xa69:reprogramming\xa6is:being\xa62e:using,aging,owing\xa63e:making,taking,coming,noting,hiring,filing,coding,citing,doping,baking,coping,hoping,lading,caring,naming,voting,riding,mining,curing,lining,ruling,typing,boring,dining,firing,hiding,piling,taping,waning,baling,boning,faring,honing,wiping,luring,timing,wading,piping,fading,biting,zoning,daring,waking,gaming,raking,ceding,tiring,coking,wining,joking,paring,gaping,poking,pining,coring,liming,toting,roping,wiring,aching\xa64e:writing,storing,eroding,framing,smoking,tasting,wasting,phoning,shaking,abiding,braking,flaking,pasting,priming,shoring,sloping,withing,hinging\xa65e:defining,refining,renaming,swathing,fringing,reciting\xa61ie:dying,tying,lying,vying\xa67e:sunbathing"}),iU=iL({fwd:"1:mt\xa62:llen\xa63:iven,aken\xa6:ne\xa6y:in",both:"1:wn\xa62:me,aten\xa63:seen,bidden,isen\xa64:roven,asten\xa63l:pilt\xa63d:uilt\xa62e:itten\xa61im:wum\xa61eak:poken\xa61ine:hone\xa61ose:osen\xa61in:gun\xa61ake:woken\xa6ear:orn\xa6eal:olen\xa6eeze:ozen\xa6et:otten\xa6ink:unk\xa6ing:ung",rev:"2:un\xa6oken:eak\xa6ought:eek\xa6oven:eave\xa61ne:o\xa61own:ly\xa61den:de\xa61in:ay\xa62t:am\xa62n:ee\xa63en:all\xa64n:rive,sake,take\xa65n:rgive",ex:"2:been\xa63:seen,run\xa64:given,taken\xa65:shaken\xa62eak:broken\xa61ive:dove\xa62y:flown\xa63e:hidden,ridden\xa61eek:sought\xa61ake:woken\xa61eave:woven"}),i_=iJ(iW),iQ=iJ(iq),iZ=iJ(iR),iY=iJ(iU),iX=iL({fwd:"3:ser,ier\xa61er:h,t,f,l,n\xa61r:e\xa62er:ss,or,om",both:"3er:ver,ear,alm\xa63ner:hin\xa63ter:lat\xa62mer:im\xa62er:ng,rm,mb\xa62ber:ib\xa62ger:ig\xa61er:w,p,k,d\xa6ier:y",rev:"1:tter,yer\xa62:uer,ver,ffer,oner,eler,ller,iler,ster,cer,uler,sher,ener,gher,aner,adder,nter,eter,rter,hter,rner,fter\xa63:oser,ooler,eafer,user,airer,bler,maler,tler,eater,uger,rger,ainer,urer,ealer,icher,pler,emner,icter,nser,iser\xa64:arser,viner,ucher,rosser,somer,ndomer,moter,oother,uarer,hiter\xa65:nuiner,esser,emier\xa6ar:urther",ex:"worse:bad\xa6better:good\xa64er:fair,gray,poor\xa61urther:far\xa63ter:fat,hot,wet\xa63der:mad,sad\xa63er:shy,fun\xa64der:glad\xa6:\xa64r:cute,dire,fake,fine,free,lame,late,pale,rare,ripe,rude,safe,sore,tame,wide\xa65r:eerie,stale"}),i0=iL({fwd:"1st:e\xa61est:l,m,f,s\xa61iest:cey\xa62est:or,ir\xa63est:ver",both:"4:east\xa65:hwest\xa65lest:erful\xa64est:weet,lgar,tter,oung\xa64most:uter\xa63est:ger,der,rey,iet,ong,ear\xa63test:lat\xa63most:ner\xa62est:pt,ft,nt,ct,rt,ht\xa62test:it\xa62gest:ig\xa61est:b,k,n,p,h,d,w\xa6iest:y",rev:"1:ttest,nnest,yest\xa62:sest,stest,rmest,cest,vest,lmest,olest,ilest,ulest,ssest,imest,uest\xa63:rgest,eatest,oorest,plest,allest,urest,iefest,uelest,blest,ugest,amest,yalest,ealest,illest,tlest,itest\xa64:cerest,eriest,somest,rmalest,ndomest,motest,uarest,tiffest\xa65:leverest,rangest\xa6ar:urthest\xa63ey:riciest",ex:"best:good\xa6worst:bad\xa65est:great\xa64est:fast,full,fair,dull\xa63test:hot,wet,fat\xa64nest:thin\xa61urthest:far\xa63est:gay,shy,ill\xa64test:neat\xa64st:late,wide,fine,safe,cute,fake,pale,rare,rude,sore,ripe,dire\xa66st:severe"}),i1=iJ(iX),i2=iJ(i0);var i3={fromPast:iW,fromPresent:iq,fromGerund:iR,fromParticiple:iU,toPast:i_,toPresent:iQ,toGerund:iZ,toParticiple:iY,toComparative:iX,toSuperlative:i0,fromComparative:i1,fromSuperlative:i2,adjToNoun:iL({fwd:"1:tistic,eable,lful,sful,ting,tty\xa62:onate,rtable,geous,ced,seful,ctful\xa63:ortive,ented\xa6arity:ear\xa6y:etic\xa6fulness:begone\xa61ity:re\xa61y:tiful,gic\xa62ity:ile,imous,ilous,ime\xa62ion:ated\xa62eness:iving\xa62y:trious\xa62ation:iring\xa62tion:vant\xa63ion:ect\xa63ce:mant,mantic\xa63tion:irable\xa63y:est,estic\xa63m:mistic,listic\xa63ess:ning\xa64n:utious\xa64on:rative,native,vative,ective\xa64ce:erant",both:"1:king,wing\xa62:alous,ltuous,oyful,rdous\xa63:gorous,ectable,werful,amatic\xa64:oised,usical,agical,raceful,ocused,lined,ightful\xa65ness:stful,lding,itous,nuous,ulous,otous,nable,gious,ayful,rvous,ntous,lsive,peful,entle,ciful,osive,leful,isive,ncise,reful,mious\xa65ty:ivacious\xa65ties:ubtle\xa65ce:ilient,adiant,atient\xa65cy:icient\xa65sm:gmatic\xa65on:sessive,dictive\xa65ity:pular,sonal,eative,entic\xa65sity:uminous\xa65ism:conic\xa65nce:mperate\xa65ility:mitable\xa65ment:xcited\xa65n:bitious\xa64cy:brant,etent,curate\xa64ility:erable,acable,icable,ptable\xa64ty:nacious,aive,oyal,dacious\xa64n:icious\xa64ce:vient,erent,stent,ndent,dient,quent,ident\xa64ness:adic,ound,hing,pant,sant,oing,oist,tute\xa64icity:imple\xa64ment:fined,mused\xa64ism:otic\xa64ry:dantic\xa64ity:tund,eral\xa64edness:hand\xa64on:uitive\xa64lity:pitable\xa64sm:eroic,namic\xa64sity:nerous\xa63th:arm\xa63ility:pable,bable,dable,iable\xa63cy:hant,nant,icate\xa63ness:red,hin,nse,ict,iet,ite,oud,ind,ied,rce\xa63ion:lute\xa63ity:ual,gal,volous,ial\xa63ce:sent,fensive,lant,gant,gent,lent,dant\xa63on:asive\xa63m:fist,sistic,iastic\xa63y:terious,xurious,ronic,tastic\xa63ur:amorous\xa63e:tunate\xa63ation:mined\xa63sy:rteous\xa63ty:ain\xa63ry:ave\xa63ment:azed\xa62ness:de,on,ue,rn,ur,ft,rp,pe,om,ge,rd,od,ay,ss,er,ll,oy,ap,ht,ld,ad,rt\xa62inousness:umous\xa62ity:neous,ene,id,ane\xa62cy:bate,late\xa62ation:ized\xa62ility:oble,ible\xa62y:odic\xa62e:oving,aring\xa62s:ost\xa62itude:pt\xa62dom:ee\xa62ance:uring\xa62tion:reet\xa62ion:oted\xa62sion:ending\xa62liness:an\xa62or:rdent\xa61th:ung\xa61e:uable\xa61ness:w,h,k,f\xa61ility:mble\xa61or:vent\xa61ement:ging\xa61tiquity:ncient\xa61ment:hed\xa6verty:or\xa6ength:ong\xa6eat:ot\xa6pth:ep\xa6iness:y",rev:"",ex:"5:forceful,humorous\xa68:charismatic\xa613:understanding\xa65ity:active\xa611ness:adventurous,inquisitive,resourceful\xa68on:aggressive,automatic,perceptive\xa67ness:amorous,fatuous,furtive,ominous,serious\xa65ness:ample,sweet\xa612ness:apprehensive,cantankerous,contemptuous,ostentatious\xa613ness:argumentative,conscientious\xa69ness:assertive,facetious,imperious,inventive,oblivious,rapacious,receptive,seditious,whimsical\xa610ness:attractive,expressive,impressive,loquacious,salubrious,thoughtful\xa63edom:boring\xa64ness:calm,fast,keen,tame\xa68ness:cheerful,gracious,specious,spurious,timorous,unctuous\xa65sity:curious\xa69ion:deliberate\xa68ion:desperate\xa66e:expensive\xa67ce:fragrant\xa63y:furious\xa69ility:ineluctable\xa66ism:mystical\xa68ity:physical,proactive,sensitive,vertical\xa65cy:pliant\xa67ity:positive\xa69ity:practical\xa612ism:professional\xa66ce:prudent\xa63ness:red\xa66cy:vagrant\xa63dom:wise"})},i5=["academy","administration","agence","agences","agencies","agency","airlines","airways","army","assoc","associates","association","assurance","authority","autorite","aviation","bank","banque","board","boys","brands","brewery","brotherhood","brothers","bureau","cafe","co","caisse","capital","care","cathedral","center","centre","chemicals","choir","chronicle","church","circus","clinic","clinique","club","co","coalition","coffee","collective","college","commission","committee","communications","community","company","comprehensive","computers","confederation","conference","conseil","consulting","containers","corporation","corps","corp","council","crew","data","departement","department","departments","design","development","directorate","division","drilling","education","eglise","electric","electricity","energy","ensemble","enterprise","enterprises","entertainment","estate","etat","faculty","faction","federation","financial","fm","foundation","fund","gas","gazette","girls","government","group","guild","herald","holdings","hospital","hotel","hotels","inc","industries","institut","institute","institutes","insurance","international","interstate","investment","investments","investors","journal","laboratory","labs","llc","ltd","limited","machines","magazine","management","marine","marketing","markets","media","memorial","ministere","ministry","military","mobile","motor","motors","musee","museum","news","observatory","office","oil","optical","orchestra","organization","partners","partnership","petrol","petroleum","pharmacare","pharmaceutical","pharmaceuticals","pizza","plc","police","politburo","polytechnic","post","power","press","productions","quartet","radio","reserve","resources","restaurant","restaurants","savings","school","securities","service","services","societe","subsidiary","society","sons","subcommittee","syndicat","systems","telecommunications","telegraph","television","times","tribunal","tv","union","university","utilities","workers"].reduce((e,t)=>(e[t]=!0,e),{}),i4=["atoll","basin","bay","beach","bluff","bog","camp","canyon","canyons","cape","cave","caves","cliffs","coast","cove","coves","crater","crossing","creek","desert","dune","dunes","downs","estates","escarpment","estuary","falls","fjord","fjords","forest","forests","glacier","gorge","gorges","grove","gulf","gully","highland","heights","hollow","hill","hills","inlet","island","islands","isthmus","junction","knoll","lagoon","lake","lakeshore","marsh","marshes","mount","mountain","mountains","narrows","peninsula","plains","plateau","pond","rapids","ravine","reef","reefs","ridge","river","rivers","sandhill","shoal","shore","shoreline","shores","strait","straits","springs","stream","swamp","tombolo","trail","trails","trench","valley","vallies","village","volcano","waterfall","watershed","wetland","woods","acres","burough","county","district","municipality","prefecture","province","region","reservation","state","territory","borough","metropolis","downtown","uptown","midtown","city","town","township","hamlet","country","kingdom","enclave","neighbourhood","neighborhood","kingdom","ward","zone","airport","amphitheater","arch","arena","auditorium","bar","barn","basilica","battlefield","bridge","building","castle","centre","coliseum","cineplex","complex","dam","farm","field","fort","garden","gardens","gymnasium","hall","house","levee","library","manor","memorial","monument","museum","gallery","palace","pillar","pits","plantation","playhouse","quarry","sportsfield","sportsplex","stadium","terrace","terraces","theater","tower","park","parks","site","ranch","raceway","sportsplex","ave","st","street","rd","road","lane","landing","crescent","cr","way","tr","terrace","avenue"].reduce((e,t)=>(e[t]=!0,e),{}),i6=[[/([^v])ies$/i,"$1y"],[/(ise)s$/i,"$1"],[/(kn|[^o]l|w)ives$/i,"$1ife"],[/^((?:ca|e|ha|(?:our|them|your)?se|she|wo)l|lea|loa|shea|thie)ves$/i,"$1f"],[/^(dwar|handkerchie|hoo|scar|whar)ves$/i,"$1f"],[/(antenn|formul|nebul|vertebr|vit)ae$/i,"$1a"],[/(octop|vir|radi|nucle|fung|cact|stimul)(i)$/i,"$1us"],[/(buffal|tomat|tornad)(oes)$/i,"$1o"],[/(ause)s$/i,"$1"],[/(ease)s$/i,"$1"],[/(ious)es$/i,"$1"],[/(ouse)s$/i,"$1"],[/(ose)s$/i,"$1"],[/(..ase)s$/i,"$1"],[/(..[aeiu]s)es$/i,"$1"],[/(vert|ind|cort)(ices)$/i,"$1ex"],[/(matr|append)(ices)$/i,"$1ix"],[/([xo]|ch|ss|sh)es$/i,"$1"],[/men$/i,"man"],[/(n)ews$/i,"$1ews"],[/([ti])a$/i,"$1um"],[/([^aeiouy]|qu)ies$/i,"$1y"],[/(s)eries$/i,"$1eries"],[/(m)ovies$/i,"$1ovie"],[/(cris|ax|test)es$/i,"$1is"],[/(alias|status)es$/i,"$1"],[/(ss)$/i,"$1"],[/(ic)s$/i,"$1"],[/s$/i,""]],i9=function(e,t){let{irregularPlurals:n}=t.two,r=Object.keys(n).reduce((e,t)=>(e[n[t]]=t,e),{});if(r.hasOwnProperty(e))return r[e];for(let t=0;t<i6.length;t++)if(!0===i6[t][0].test(e)){e=e.replace(i6[t][0],i6[t][1]);break}return e};let i7=function(e,t=[]){for(let n=0;n<t.length;n+=1)if(e.endsWith(t[n]))return e;return null},i8=function(e,t,n={}){t=t||{};let r=e.length-1;for(let a=r;a>=1;a-=1){let r=e.length-a,i=e.substring(r,e.length);if(!0===t.hasOwnProperty(i))return e.slice(0,r)+t[i];if(!0===n.hasOwnProperty(i))return e.slice(0,r)+n[i]}return t.hasOwnProperty("")?e+t[""]:n.hasOwnProperty("")?e+n[""]:null};var oe=function(e="",t={}){let n=function(e,t={}){return t.hasOwnProperty(e)?t[e]:null}(e,t.ex);return(n=(n=n||i7(e,t.same))||i8(e,t.fwd,t.both))||e};let ot={Gerund:["ing"],Actor:["erer"],Infinitive:["ate","ize","tion","rify","then","ress","ify","age","nce","ect","ise","ine","ish","ace","ash","ure","tch","end","ack","and","ute","ade","ock","ite","ase","ose","use","ive","int","nge","lay","est","ain","ant","ent","eed","er","le","unk","ung","upt","en"],PastTense:["ept","ed","lt","nt","ew","ld"],PresentTense:["rks","cks","nks","ngs","mps","tes","zes","ers","les","acks","ends","ands","ocks","lays","eads","lls","els","ils","ows","nds","ays","ams","ars","ops","ffs","als","urs","lds","ews","ips","es","ts","ns"],Participle:["ken","wn"]};var on=ot=Object.keys(ot).reduce((e,t)=>(ot[t].forEach(n=>e[n]=t),e),{}),or=function(e){let t=e.substring(e.length-3);if(!0===on.hasOwnProperty(t))return on[t];let n=e.substring(e.length-2);return!0===on.hasOwnProperty(n)?on[n]:"s"===e.substring(e.length-1)?"PresentTense":null};let oa=function(e,t){let n="",r={};t.one&&t.one.prefixes&&(r=t.one.prefixes);let[a,i]=e.split(/ /);return i&&!0===r[a]&&(n=a,a=i,i=""),{prefix:n,verb:a,particle:i}},oi={are:"be",were:"be",been:"be",is:"be",am:"be",was:"be",be:"be",being:"be"};var oo=function(e,t,n){let{fromPast:r,fromPresent:a,fromGerund:i,fromParticiple:o}=t.two.models,{prefix:s,verb:l,particle:u}=oa(e,t),c="";if(n||(n=or(e)),oi.hasOwnProperty(e))c=oi[e];else if("Participle"===n)c=oe(l,o);else if("PastTense"===n)c=oe(l,r);else if("PresentTense"===n)c=oe(l,a);else{if("Gerund"!==n)return e;c=oe(l,i)}return u&&(c+=" "+u),s&&(c=s+" "+c),c};let os=e=>/ /.test(e)?e.split(/ /):[e,""];var ol=function(e,t){let{toPast:n,toPresent:r,toGerund:a,toParticiple:i}=t.two.models;if("be"===e)return{Infinitive:e,Gerund:"being",PastTense:"was",PresentTense:"is"};let[o,s]=os(e),l={Infinitive:o,PastTense:oe(o,n),PresentTense:oe(o,r),Gerund:oe(o,a),FutureTense:"will "+o},u=oe(o,i);if(u!==e&&u!==l.PastTense){let n=t.one.lexicon||{};("Participle"===n[u]||"Adjective"===n[u])&&("play"===e&&(u="played"),l.Participle=u)}return s&&Object.keys(l).forEach(e=>{l[e]+=" "+s}),l};let ou=function(e,t){return oe(e,t.two.models.toSuperlative)},oc=function(e,t){return oe(e,t.two.models.toComparative)};var oh=function(e="",t=[]){let n=e.length,r=n<=6?n-1:6;for(let a=r;a>=1;a-=1){let r=e.substring(n-a,e.length);if(!0===t[r.length].hasOwnProperty(r))return e.slice(0,n-a)+t[r.length][r]}return null};let od="ically",og=new Set(["analyt"+od,"chem"+od,"class"+od,"clin"+od,"crit"+od,"ecolog"+od,"electr"+od,"empir"+od,"frant"+od,"grammat"+od,"ident"+od,"ideolog"+od,"log"+od,"mag"+od,"mathemat"+od,"mechan"+od,"med"+od,"method"+od,"method"+od,"mus"+od,"phys"+od,"phys"+od,"polit"+od,"pract"+od,"rad"+od,"satir"+od,"statist"+od,"techn"+od,"technolog"+od,"theoret"+od,"typ"+od,"vert"+od,"whims"+od]),op=[null,{},{ly:""},{ily:"y",bly:"ble",ply:"ple"},{ally:"al",rply:"rp"},{ually:"ual",ially:"ial",cally:"cal",eally:"eal",rally:"ral",nally:"nal",mally:"mal",eeply:"eep",eaply:"eap"},{ically:"ic"}],om=new Set(["early","only","hourly","daily","weekly","monthly","yearly","mostly","duly","unduly","especially","undoubtedly","conversely","namely","exceedingly","presumably","accordingly","overly","best","latter","little","long","low"]),of={wholly:"whole",fully:"full",truly:"true",gently:"gentle",singly:"single",customarily:"customary",idly:"idle",publically:"public",quickly:"quick",superbly:"superb",cynically:"cynical",well:"good"},ob=[null,{y:"ily"},{ly:"ly",ic:"ically"},{ial:"ially",ual:"ually",tle:"tly",ble:"bly",ple:"ply",ary:"arily"},{},{},{}],ov={cool:"cooly",whole:"wholly",full:"fully",good:"well",idle:"idly",public:"publicly",single:"singly",special:"especially"};var oy=function(e){if(ov.hasOwnProperty(e))return ov[e];let t=oh(e,ob);return t||(t=e+"ly"),t},ow={Singular:(e,t,n,r)=>{let a=r.one.lexicon,i=n.two.transform.noun.toPlural(e,r);a[i]||(t[i]=t[i]||"Plural")},Actor:(e,t,n,r)=>{let a=r.one.lexicon,i=n.two.transform.noun.toPlural(e,r);a[i]||(t[i]=t[i]||["Plural","Actor"])},Comparable:(e,t,n,r)=>{let a=r.one.lexicon,{toSuperlative:i,toComparative:o}=n.two.transform.adjective,s=i(e,r);a[s]||(t[s]=t[s]||"Superlative");let l=o(e,r);a[l]||(t[l]=t[l]||"Comparative"),t[e]="Adjective"},Demonym:(e,t,n,r)=>{let a=n.two.transform.noun.toPlural(e,r);t[a]=t[a]||["Demonym","Plural"]},Infinitive:(e,t,n,r)=>{let a=r.one.lexicon;Object.entries(n.two.transform.verb.conjugate(e,r)).forEach(e=>{a[e[1]]||t[e[1]]||"FutureTense"===e[0]||(t[e[1]]=e[0])})},PhrasalVerb:(e,t,n,r)=>{let a=r.one.lexicon;t[e]=["PhrasalVerb","Infinitive"];let i=r.one._multiCache,[o,s]=e.split(" ");a[o]||(t[o]=t[o]||"Infinitive");let l=n.two.transform.verb.conjugate(o,r);delete l.FutureTense,Object.entries(l).forEach(e=>{if("Actor"===e[0]||""===e[1])return;t[e[1]]||a[e[1]]||(t[e[1]]=e[0]),i[e[1]]=2;let n=e[1]+" "+s;t[n]=t[n]||[e[0],"PhrasalVerb"]})},Multiple:(e,t)=>{t[e]=["Multiple","Cardinal"],t[e+"th"]=["Multiple","Ordinal"],t[e+"ths"]=["Multiple","Fraction"]},Cardinal:(e,t)=>{t[e]=["TextValue","Cardinal"]},Ordinal:(e,t)=>{t[e]=["TextValue","Ordinal"],t[e+"s"]=["TextValue","Fraction"]},Place:(e,t)=>{t[e]=["Place","ProperNoun"]},Region:(e,t)=>{t[e]=["Region","ProperNoun"]}};let ok=function(e,t){let n=e[t];if(!n)return!1;let r=new Set(["may","april","august","jan"]);if("like"===n.normal||r.has(n.normal)||n.tags.has("Place")||n.tags.has("Date"))return!1;if(e[t-1]){let a=e[t-1];if(a.tags.has("Date")||r.has(a.normal)||a.tags.has("Adjective")||n.tags.has("Adjective"))return!1}let a=n.normal;return!((1===a.length||2===a.length||4===a.length)&&/^[0-9]+$/.test(a))},oP={e:["mice","louse","antennae","formulae","nebulae","vertebrae","vitae"],i:["tia","octopi","viri","radii","nuclei","fungi","cacti","stimuli"],n:["men"],t:["feet"]},oA=new Set(["israelis","menus","logos"]),ox=["bus","mas","was","ias","xas","vas","cis","lis","nis","ois","ris","sis","tis","xis","aus","cus","eus","fus","gus","ius","lus","nus","das","ous","pus","rus","sus","tus","xus","aos","igos","ados","ogos","'s","ss"];var oC=function(e){if(!e||e.length<=3)return!1;if(oA.has(e))return!0;let t=e[e.length-1];return oP.hasOwnProperty(t)?oP[t].find(t=>e.endsWith(t)):!("s"!==t||ox.find(t=>e.endsWith(t)))},oj={two:{quickSplit:function(e){let t=/[,:;]/,n=[];return e.forEach(e=>{let r=0;e.forEach((a,i)=>{t.test(a.post)&&ok(e,i+1)&&(n.push(e.slice(r,i+1)),r=i+1)}),r<e.length&&n.push(e.slice(r,e.length))}),n},expandLexicon:function(e,t){let{methods:n,model:r}=t,a={},i={};return Object.keys(e).forEach(t=>{let o=e[t],s=(t=(t=t.toLowerCase().trim()).replace(/'s\b/,"")).split(/ /);s.length>1&&(void 0===i[s[0]]||s.length>i[s[0]])&&(i[s[0]]=s.length),!0===ow.hasOwnProperty(o)&&ow[o](t,a,n,r),a[t]=a[t]||o}),delete a[""],delete a[null],delete a[" "],{lex:a,_multi:i}},transform:{noun:{toPlural:ab,toSingular:i9,all:function(e,t){let n=[e],r=ab(e,t);r!==e&&n.push(r);let a=i9(e,t);return a!==e&&n.push(a),n}},verb:{toInfinitive:oo,conjugate:ol,all:function(e,t){let n=ol(e,t);return delete n.FutureTense,Object.values(n).filter(e=>e)}},adjective:{toSuperlative:ou,toComparative:oc,toAdverb:oy,toNoun:function(e,t){return oe(e,t.two.models.adjToNoun)},fromAdverb:function(e){return e.endsWith("ly")?og.has(e)?e.replace(/ically/,"ical"):om.has(e)?null:of.hasOwnProperty(e)?of[e]:oh(e,op)||e:null},fromSuperlative:function(e,t){return oe(e,t.two.models.fromSuperlative)},fromComparative:function(e,t){return oe(e,t.two.models.fromComparative)},all:function(e,t){let n=[e];return n.push(ou(e,t)),n.push(oc(e,t)),n.push(oy(e)),Array.from(n=new Set(n=n.filter(e=>e)))}}},looksPlural:oC}};let oN={one:{lexicon:{}},two:{models:i3}},oI={"Actor|Verb":"Actor","Adj|Gerund":"Adjective","Adj|Noun":"Adjective","Adj|Past":"Adjective","Adj|Present":"Adjective","Noun|Verb":"Singular","Noun|Gerund":"Gerund","Person|Noun":"Noun","Person|Date":"Month","Person|Verb":"FirstName","Person|Place":"Person","Person|Adj":"Comparative","Plural|Verb":"Plural","Unit|Noun":"Noun"},oD=function(e,t){let{lex:n,_multi:r}=oj.two.expandLexicon(e,{model:t,methods:oj});return Object.assign(t.one.lexicon,n),Object.assign(t.one._multiCache,r),t},oT=function(e,t,n){let r=ol(e,oN);t[r.PastTense]=t[r.PastTense]||"PastTense",t[r.Gerund]=t[r.Gerund]||"Gerund",!0===n&&(t[r.PresentTense]=t[r.PresentTense]||"PresentTense")},oO=function(e,t,n){let r=ou(e,n);t[r]=t[r]||"Superlative";let a=oc(e,n);t[a]=t[a]||"Comparative"},oH=function(e,t,n){let r=ab(e,n);t[r]=t[r]||"Plural"},oE={one:{_multiCache:{},lexicon:ay,frozenLex:{"20th century fox":"Organization","7 eleven":"Organization","motel 6":"Organization","excuse me":"Expression","financial times":"Organization","guns n roses":"Organization","la z boy":"Organization","labour party":"Organization","new kids on the block":"Organization","new york times":"Organization","the guess who":"Organization","thin lizzy":"Organization","prime minister":"Actor","free market":"Singular","lay up":"Singular","living room":"Singular","living rooms":"Plural","spin off":"Singular","appeal court":"Uncountable","cold war":"Uncountable","gene pool":"Uncountable","machine learning":"Uncountable","nail polish":"Uncountable","time off":"Uncountable","take part":"Infinitive","bill gates":"Person","doctor who":"Person","dr who":"Person","he man":"Person","iron man":"Person","kid cudi":"Person","run dmc":"Person","rush limbaugh":"Person","snow white":"Person","tiger woods":"Person","brand new":"Adjective","en route":"Adjective","left wing":"Adjective","off guard":"Adjective","on board":"Adjective","part time":"Adjective","right wing":"Adjective","so called":"Adjective","spot on":"Adjective","straight forward":"Adjective","super duper":"Adjective","tip top":"Adjective","top notch":"Adjective","up to date":"Adjective","win win":"Adjective","brooklyn nets":"SportsTeam","chicago bears":"SportsTeam","houston astros":"SportsTeam","houston dynamo":"SportsTeam","houston rockets":"SportsTeam","houston texans":"SportsTeam","minnesota twins":"SportsTeam","orlando magic":"SportsTeam","san antonio spurs":"SportsTeam","san diego chargers":"SportsTeam","san diego padres":"SportsTeam","iron maiden":"ProperNoun","isle of man":"Country","united states":"Country","united states of america":"Country","prince edward island":"Region","cedar breaks":"Place","cedar falls":"Place","point blank":"Adverb","tiny bit":"Adverb","by the time":"Conjunction","no matter":"Conjunction","civil wars":"Plural","credit cards":"Plural","default rates":"Plural","free markets":"Plural","head starts":"Plural","home runs":"Plural","lay ups":"Plural","phone calls":"Plural","press releases":"Plural","record labels":"Plural","soft serves":"Plural","student loans":"Plural","tax returns":"Plural","tv shows":"Plural","video games":"Plural","took part":"PastTense","takes part":"PresentTense","taking part":"Gerund","taken part":"Participle","light bulb":"Noun","rush hour":"Noun","fluid ounce":"Unit","the rolling stones":"Organization"}},two:{irregularPlurals:aa,models:i3,suffixPatterns:[null,null,{ea:ic,ia:ib,ic:is,ly:id,"'n":im,"'t":im},{oed:ih,ued:ih,xed:ih," so":id,"'ll":iy,"'re":"Copula",azy:is,eer:ib,end:im,ped:ih,ffy:is,ify:il,ing:"Gerund",ize:il,ibe:il,lar:is,mum:is,nes:iu,nny:is,ous:is,que:is,ger:ib,ber:ib,rol:ic,sis:ic,ogy:ic,oid:ic,ian:ic,zes:iu,eld:ih,ken:ik,ven:ik,ten:ik,ect:il,ict:il,ign:il,oze:il,ful:is,bal:is,ton:ib,pur:iw},{amed:ih,aped:ih,ched:ih,lked:ih,rked:ih,reed:ih,nded:ih,mned:is,cted:ih,dged:ih,ield:ic,akis:iv,cede:il,chuk:iv,czyk:iv,ects:iu,iend:ic,ends:im,enko:iv,ette:ic,iary:ic,wner:ic,fies:iu,fore:id,gate:il,gone:is,ices:ig,ints:ig,ruct:il,ines:ig,ions:ig,ners:ig,pers:ig,lers:ig,less:is,llen:is,made:is,nsen:iv,oses:iu,ould:iy,some:is,sson:iv,ians:ig,tion:ic,tage:ib,ique:ic,tive:is,tors:ib,vice:ic,lier:ic,fier:ic,wned:ih,gent:ic,tist:ip,pist:ip,rist:ip,mist:ip,yist:ip,vist:ip,ists:ip,lite:ic,site:ic,rite:ic,mite:ic,bite:ic,mate:ic,date:ic,ndal:ic,vent:ic,uist:ip,gist:ip,note:ic,cide:ic,ence:ic,wide:is,vide:il,ract:il,duce:il,pose:il,eive:il,lyze:il,lyse:il,iant:is,nary:is,ghty:is,uent:is,erer:ip,bury:iw,dorf:ib,esty:ib,wych:iw,dale:iw,folk:iw,vale:iw,abad:iw,sham:iw,wick:iw,view:iw},{elist:ip,holic:ic,phite:ic,tized:ih,urned:ih,eased:ih,ances:ig,bound:is,ettes:ig,fully:id,ishes:iu,ities:ig,marek:iv,nssen:iv,ology:ib,osome:ic,tment:ic,ports:ig,rough:is,tches:iu,tieth:"Ordinal",tures:ig,wards:id,where:id,archy:ib,pathy:ib,opoly:ib,embly:ib,phate:ib,ndent:ic,scent:ic,onist:ip,anist:ip,alist:ip,olist:ip,icist:ip,ounce:il,iable:is,borne:is,gnant:is,inant:is,igent:is,atory:is,rient:ic,dient:ic,maker:ip,burgh:iw,mouth:iw,ceter:iw,ville:iw,hurst:iw,stead:iw,endon:iw,brook:iw,shire:iw,worth:ib,field:"ProperNoun",ridge:iw},{auskas:iv,parent:ic,cedent:ic,ionary:ic,cklist:ic,brooke:iw,keeper:ip,logist:ip,teenth:"Value",worker:ip,master:ip,writer:ip,brough:iw,cester:iw,ington:iw,cliffe:iw,ingham:iw},{chester:iw,logists:ip,opoulos:iv,borough:iw,sdottir:iv}],prefixPatterns:[null,null,{},{neo:iA,bio:iA,"de-":ix,"re-":ix,"un-":ix,"ex-":iA},{anti:iA,auto:iA,faux:iP,hexa:iA,kilo:iA,mono:iA,nano:iA,octa:iA,poly:iA,semi:iP,tele:iA,"pro-":iP,"mis-":ix,"dis-":ix,"pre-":iP},{anglo:iA,centi:iA,ethno:iA,ferro:iA,grand:iA,hepta:iA,hydro:iA,intro:iA,macro:iA,micro:iA,milli:iA,nitro:iA,penta:iA,quasi:iP,radio:iA,tetra:iA,"omni-":iP,"post-":iP},{pseudo:iP,"extra-":iP,"hyper-":iP,"inter-":iP,"intra-":iP,"deca-":iP},{electro:iA}],endsWith:{a:[[/.[aeiou]na$/,iF,"tuna"],[/.[oau][wvl]ska$/,iG],[/.[^aeiou]ica$/,iI,"harmonica"],[/^([hyj]a+)+$/,iO,"haha"]],c:[[/.[^aeiou]ic$/,iC]],d:[[/[aeiou](pp|ll|ss|ff|gg|tt|rr|bb|nn|mm)ed$/,iD,"popped"],[/.[aeo]{2}[bdgmnprvz]ed$/,iD,"rammed"],[/.[aeiou][sg]hed$/,iD,"gushed"],[/.[aeiou]red$/,iD,"hired"],[/.[aeiou]r?ried$/,iD,"hurried"],[/[^aeiou]ard$/,iI,"steward"],[/[aeiou][^aeiou]id$/,iC,""],[/.[vrl]id$/,iC,"livid"],[/..led$/,iD,"hurled"],[/.[iao]sed$/,iD,""],[/[aeiou]n?[cs]ed$/,iD,""],[/[aeiou][rl]?[mnf]ed$/,iD,""],[/[aeiou][ns]?c?ked$/,iD,"bunked"],[/[aeiou]gned$/,iD],[/[aeiou][nl]?ged$/,iD],[/.[tdbwxyz]ed$/,iD],[/[^aeiou][aeiou][tvx]ed$/,iD],[/.[cdflmnprstv]ied$/,iD,"emptied"]],e:[[/.[lnr]ize$/,ij,"antagonize"],[/.[^aeiou]ise$/,ij,"antagonise"],[/.[aeiou]te$/,ij,"bite"],[/.[^aeiou][ai]ble$/,iC,"fixable"],[/.[^aeiou]eable$/,iC,"maleable"],[/.[ts]ive$/,iC,"festive"],[/[a-z]-like$/,iC,"woman-like"]],h:[[/.[^aeiouf]ish$/,iC,"cornish"],[/.v[iy]ch$/,iG,"..ovich"],[/^ug?h+$/,iO,"ughh"],[/^uh[ -]?oh$/,iO,"uhoh"],[/[a-z]-ish$/,iC,"cartoon-ish"]],i:[[/.[oau][wvl]ski$/,iG,"polish-male"]],k:[[/^(k){2}$/,iO,"kkkk"]],l:[[/.[gl]ial$/,iC,"familial"],[/.[^aeiou]ful$/,iC,"fitful"],[/.[nrtumcd]al$/,iC,"natal"],[/.[^aeiou][ei]al$/,iC,"familial"]],m:[[/.[^aeiou]ium$/,iI,"magnesium"],[/[^aeiou]ism$/,iI,"schism"],[/^[hu]m+$/,iO,"hmm"],[/^\d+ ?[ap]m$/,"Date","3am"]],n:[[/.[lsrnpb]ian$/,iC,"republican"],[/[^aeiou]ician$/,iH,"musician"],[/[aeiou][ktrp]in'$/,"Gerund","cookin'"]],o:[[/^no+$/,iO,"noooo"],[/^(yo)+$/,iO,"yoo"],[/^wo{2,}[pt]?$/,iO,"woop"]],r:[[/.[bdfklmst]ler$/,"Noun"],[/[aeiou][pns]er$/,iI],[/[^i]fer$/,ij],[/.[^aeiou][ao]pher$/,iH],[/.[lk]er$/,"Noun"],[/.ier$/,"Comparative"]],t:[[/.[di]est$/,"Superlative"],[/.[icldtgrv]ent$/,iC],[/[aeiou].*ist$/,iC],[/^[a-z]et$/,iE]],s:[[/.[^aeiou]ises$/,iN],[/.[rln]ates$/,iN],[/.[^z]ens$/,iE],[/.[lstrn]us$/,iI],[/.[aeiou]sks$/,iN],[/.[aeiou]kes$/,iN],[/[aeiou][^aeiou]is$/,iI],[/[a-z]'s$/,iF],[/^yes+$/,iO]],v:[[/.[^aeiou][ai][kln]ov$/,iG]],y:[[/.[cts]hy$/,iC],[/.[st]ty$/,iC],[/.[tnl]ary$/,iC],[/.[oe]ry$/,iI],[/[rdntkbhs]ly$/,iT],[/.(gg|bb|zz)ly$/,iC],[/...lly$/,iT],[/.[gk]y$/,iC],[/[bszmp]{2}y$/,iC],[/.[ai]my$/,iC],[/[ea]{2}zy$/,iC],[/.[^aeiou]ity$/,iI]]},neighbours:{leftTags:[["Adjective",iz],["Possessive",iz],["Determiner",iz],["Adverb",iV],["Pronoun",iV],["Value",iz],["Ordinal",iz],["Modal",iV],["Superlative",iz],["Demonym",iz],["Honorific","Person"]],leftWords:[["i",iV],["first",iz],["it",iV],["there",iV],["not",iV],["because",iz],["if",iz],["but",iz],["who",iV],["this",iz],["his",iz],["when",iz],["you",iV],["very","Adjective"],["old",iz],["never",iV],["before",iz],["a",iz],["the",iz],["been",iV]],rightTags:[["Copula",iz],["PastTense",iz],["Conjunction",iz],["Modal",iz]],rightWords:[["there",iV],["me",iV],["man","Adjective"],["him",iV],["it",iV],["were",iz],["took",iz],["himself",iV],["went",iz],["who",iz],["jr","Person"]]},regexNormal:[[/^[\w.]+@[\w.]+\.[a-z]{2,3}$/,"Email"],[/^(https?:\/\/|www\.)+\w+\.[a-z]{2,3}/,"Url","http.."],[/^[a-z0-9./].+\.(com|net|gov|org|ly|edu|info|biz|dev|ru|jp|de|in|uk|br|io|ai)/,"Url",".com"],[/^[PMCE]ST$/,"Timezone","EST"],[/^ma?c'[a-z]{3}/,"LastName","mc'neil"],[/^o'[a-z]{3}/,"LastName","o'connor"],[/^ma?cd[aeiou][a-z]{3}/,"LastName","mcdonald"],[/^(lol)+[sz]$/,"Expression","lol"],[/^wo{2,}a*h?$/,"Expression","wooah"],[/^(hee?){2,}h?$/,"Expression","hehe"],[/^(un|de|re)\\-[a-z\u00C0-\u00FF]{2}/,"Verb","un-vite"],[/^(m|k|cm|km)\/(s|h|hr)$/,"Unit","5 k/m"],[/^(ug|ng|mg)\/(l|m3|ft3)$/,"Unit","ug/L"],[/[^:/]\/\p{Letter}/u,"SlashedTerm","love/hate"]],regexText:[[/^#[\p{Number}_]*\p{Letter}/u,"HashTag"],[/^@\w{2,}$/,"AtMention"],[/^([A-Z]\.){2}[A-Z]?/i,["Acronym","Noun"],"F.B.I"],[/.{3}[lkmnp]in['‘’‛‵′`´]$/,"Gerund","chillin'"],[/.{4}s['‘’‛‵′`´]$/,"Possessive","flanders'"],[/^[\p{Emoji_Presentation}\p{Extended_Pictographic}]/u,"Emoji","emoji-class"]],regexNumbers:[[/^@1?[0-9](am|pm)$/i,"Time","3pm"],[/^@1?[0-9]:[0-9]{2}(am|pm)?$/i,"Time","3:30pm"],[/^'[0-9]{2}$/,"Year"],[/^[012]?[0-9](:[0-5][0-9])(:[0-5][0-9])$/,"Time","3:12:31"],[/^[012]?[0-9](:[0-5][0-9])?(:[0-5][0-9])? ?(am|pm)$/i,"Time","1:12pm"],[/^[012]?[0-9](:[0-5][0-9])(:[0-5][0-9])? ?(am|pm)?$/i,"Time","1:12:31pm"],[/^[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}/i,"Date","iso-date"],[/^[0-9]{1,4}-[0-9]{1,2}-[0-9]{1,4}$/,"Date","iso-dash"],[/^[0-9]{1,4}\/[0-9]{1,2}\/([0-9]{4}|[0-9]{2})$/,"Date","iso-slash"],[/^[0-9]{1,4}\.[0-9]{1,2}\.[0-9]{1,4}$/,"Date","iso-dot"],[/^[0-9]{1,4}-[a-z]{2,9}-[0-9]{1,4}$/i,"Date","12-dec-2019"],[/^utc ?[+-]?[0-9]+$/,"Timezone","utc-9"],[/^(gmt|utc)[+-][0-9]{1,2}$/i,"Timezone","gmt-3"],[/^[0-9]{3}-[0-9]{4}$/,"PhoneNumber","421-0029"],[/^(\+?[0-9][ -])?[0-9]{3}[ -]?[0-9]{3}-[0-9]{4}$/,"PhoneNumber","1-800-"],[/^[-+]?\p{Currency_Symbol}[-+]?[0-9]+(,[0-9]{3})*(\.[0-9]+)?([kmb]|bn)?\+?$/u,["Money","Value"],"$5.30"],[/^[-+]?[0-9]+(,[0-9]{3})*(\.[0-9]+)?\p{Currency_Symbol}\+?$/u,["Money","Value"],"5.30\xa3"],[/^[-+]?[$£]?[0-9]([0-9,.])+(usd|eur|jpy|gbp|cad|aud|chf|cny|hkd|nzd|kr|rub)$/i,["Money","Value"],"$400usd"],[/^[-+]?[0-9]+(,[0-9]{3})*(\.[0-9]+)?\+?$/,["Cardinal","NumericValue"],"5,999"],[/^[-+]?[0-9]+(,[0-9]{3})*(\.[0-9]+)?(st|nd|rd|r?th)$/,["Ordinal","NumericValue"],"53rd"],[/^\.[0-9]+\+?$/,["Cardinal","NumericValue"],".73th"],[/^[-+]?[0-9]+(,[0-9]{3})*(\.[0-9]+)?%\+?$/,["Percent","Cardinal","NumericValue"],"-4%"],[/^\.[0-9]+%$/,["Percent","Cardinal","NumericValue"],".3%"],[/^[0-9]{1,4}\/[0-9]{1,4}(st|nd|rd|th)?s?$/,["Fraction","NumericValue"],"2/3rds"],[/^[0-9.]{1,3}[a-z]{0,2}[-–—][0-9]{1,3}[a-z]{0,2}$/,["Value","NumberRange"],"3-4"],[/^[0-9]{1,2}(:[0-9][0-9])?(am|pm)? ?[-–—] ?[0-9]{1,2}(:[0-9][0-9])?(am|pm)$/,["Time","NumberRange"],"3-4pm"],[/^[0-9.]+([a-z°]{1,4})$/,"NumericValue","9km"]],switches:aw,clues:ii,uncountable:{},orgWords:i5,placeWords:i4}};a=(r=oD((r=oE).one.lexicon,r)).one.lexicon,i=r,Object.keys(a).forEach(e=>{"Uncountable"===a[e]&&(i.two.uncountable[e]=!0,a[e]="Uncountable")});var oF=oE=r=function(e){let{irregularPlurals:t}=e.two,{lexicon:n}=e.one;return Object.entries(t).forEach(e=>{n[e[0]]=n[e[0]]||"Singular",n[e[1]]=n[e[1]]||"Plural"}),e}(r=function(e,t){let n={},r=t.one.lexicon;return Object.keys(e).forEach(a=>{let i=e[a];if(n[a]=oI[i],("Noun|Verb"===i||"Person|Verb"===i||"Actor|Verb"===i)&&oT(a,r,!1),"Adj|Present"===i&&(oT(a,r,!0),oO(a,r,t)),"Person|Adj"===i&&oO(a,r,t),"Adj|Gerund"===i||"Noun|Gerund"===i){let e=oo(a,oN,"Gerund");r[e]||(n[e]="Infinitive")}if(("Noun|Gerund"===i||"Adj|Noun"===i||"Person|Noun"===i)&&oH(a,r,t),"Adj|Past"===i){let e=oo(a,oN,"PastTense");r[e]||(n[e]="Infinitive")}}),t=oD(n,t)}((r=i).two.switches,r)),oG=function(e,t,n,r){let a=r.methods.one.setTag;if(0===t&&e.length>=3&&e[0].post.match(/:/)){let t=e[1];if(t.tags.has("Value")||t.tags.has("Email")||t.tags.has("PhoneNumber"))return;a([e[0]],"Expression",r,null,"2-punct-colon''")}},oV=function(e,t,n,r){let a=r.methods.one.setTag;"-"===e[t].post&&e[t+1]&&a([e[t],e[t+1]],"Hyphenated",r,null,"1-punct-hyphen''")};let oz=/^(under|over|mis|re|un|dis|semi)-?/;var oM=function(e,t,n){let r=n.two.switches,a=e[t];if(r.hasOwnProperty(a.normal)){a.switch=r[a.normal];return}if(oz.test(a.normal)){let e=a.normal.replace(oz,"");e.length>3&&r.hasOwnProperty(e)&&(a.switch=r[e])}},oB=n(20357);let oS=(e,t,n="")=>{let r=e.text||"["+e.implicit+"]";"string"!=typeof t&&t.length>2&&(t=t.slice(0,2).join(", #")+" +"),t="string"!=typeof t?t.join(", #"):t,console.log(` ${("\x1b[33m\x1b[3m"+r+"\x1b[0m").padEnd(24)} \x1b[32m→\x1b[0m #${t.padEnd(22)}  ${"\x1b[3m"+n+"\x1b[0m"}`)};var o$=function(e,t,n){if(!t||0===t.length||!0===e.frozen)return;let r=void 0!==oB&&oB.env?oB.env:self.env||{};r&&r.DEBUG_TAGS&&oS(e,t,n),e.tags=e.tags||new Set,"string"==typeof t?e.tags.add(t):t.forEach(t=>e.tags.add(t))};let oL=["Acronym","Abbreviation","ProperNoun","Uncountable","Possessive","Pronoun","Activity","Honorific","Month"],oK=function(e){!e.tags.has("Noun")||e.tags.has("Plural")||e.tags.has("Singular")||oL.find(t=>e.tags.has(t))||(oC(e.normal)?o$(e,"Plural","3-plural-guess"):o$(e,"Singular","3-singular-guess"))},oJ=function(e){let t=e.tags;if(t.has("Verb")&&1===t.size){let t=or(e.normal);t&&o$(e,t,"3-verb-tense-guess")}};var oW=function(e,t,n){let r=e[t],a=Array.from(r.tags);for(let e=0;e<a.length;e+=1)n.one.tagSet[a[e]]&&o$(r,n.one.tagSet[a[e]].parents,` -inferred by #${a[e]}`);oK(r),oJ(r,n)};let oq=/^\p{Lu}[\p{Ll}'’]/u,oR=/[0-9]/,oU=["Date","Month","WeekDay","Unit","Expression"],o_=/[IVX]/,oQ=/^[IVXLCDM]{2,}$/,oZ=/^M{0,4}(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3})$/,oY={li:!0,dc:!0,md:!0,dm:!0,ml:!0};var oX=function(e,t,n){let r=e[t];r.index=r.index||[0,0];let a=r.index[1],i=r.text||"";return 0!==a&&!0===oq.test(i)&&!1===oR.test(i)?oU.find(e=>r.tags.has(e))||r.pre.match(/["']$/)||"the"===r.normal?null:(oW(e,t,n),r.tags.has("Noun")||r.frozen||r.tags.clear(),o$(r,"ProperNoun","2-titlecase"),!0):i.length>=2&&oQ.test(i)&&o_.test(i)&&oZ.test(i)&&!oY[r.normal]?(o$(r,"RomanNumeral","2-xvii"),!0):null};let o0=function(e="",t=[]){let n=e.length,r=7;n<=7&&(r=n-1);for(let a=r;a>1;a-=1){let r=e.substring(n-a,n);if(!0===t[r.length].hasOwnProperty(r))return t[r.length][r]}return null};var o1=function(e,t,n){let r=e[t];if(0===r.tags.size){let e=o0(r.normal,n.two.suffixPatterns);if(null!==e)return o$(r,e,"2-suffix"),r.confidence=.7,!0;if(r.implicit&&null!==(e=o0(r.implicit,n.two.suffixPatterns)))return o$(r,e,"2-implicit-suffix"),r.confidence=.7,!0}return null};let o2=/['‘’‛‵′`´]/,o3=function(e,t){for(let n=0;n<t.length;n+=1)if(!0===t[n][0].test(e))return t[n];return null},o5=function(e="",t){let n=e[e.length-1];if(!0===t.hasOwnProperty(n)){let r=t[n]||[];for(let t=0;t<r.length;t+=1)if(!0===r[t][0].test(e))return r[t]}return null};var o4=function(e,t,n,r){let a=r.methods.one.setTag,{regexText:i,regexNormal:o,regexNumbers:s,endsWith:l}=n.two,u=e[t],c=u.machine||u.normal,h=u.text;o2.test(u.post)&&!o2.test(u.pre)&&(h+=u.post.trim());let d=o3(h,i)||o3(c,o);return(!d&&/[0-9]/.test(c)&&(d=o3(c,s)),d||0!==u.tags.size||(d=o5(c,l)),d)?(a([u],d[1],r,null,`2-regex-'${d[2]||d[0]}'`),u.confidence=.6,!0):null};let o6=function(e="",t=[]){let n=e.length,r=7;7>n-3&&(r=n-3);for(let n=r;n>2;n-=1){let r=e.substring(0,n);if(!0===t[r.length].hasOwnProperty(r))return t[r.length][r]}return null};var o9=function(e,t,n){let r=e[t];if(0===r.tags.size){let e=o6(r.normal,n.two.prefixPatterns);if(null!==e)return o$(r,e,"2-prefix"),r.confidence=.5,!0}return null};let o7=new Set(["in","on","by","until","for","to","during","throughout","through","within","before","after","of","this","next","last","circa","around","post","pre","budget","classic","plan","may"]),o8=function(e){if(!e)return!1;let t=e.normal||e.implicit;return!!(o7.has(t)||e.tags.has("Date")||e.tags.has("Month")||e.tags.has("WeekDay")||e.tags.has("Year")||e.tags.has("ProperNoun"))},se=function(e){return!!e&&(!!(e.tags.has("Ordinal")||e.tags.has("Cardinal")&&e.normal.length<3)||"is"===e.normal||"was"===e.normal)},st=function(e){return e&&(e.tags.has("Date")||e.tags.has("Month")||e.tags.has("WeekDay")||e.tags.has("Year"))};var sn=function(e,t){let n=e[t];if(n.tags.has("NumericValue")&&n.tags.has("Cardinal")&&4===n.normal.length){let r=Number(n.normal);if(r&&!isNaN(r)&&r>1400&&r<2100){let a=e[t-1],i=e[t+1];if(o8(a)||o8(i))return o$(n,"Year","2-tagYear");if(r>=1920&&r<2025){if(se(a)||se(i))return o$(n,"Year","2-tagYear-close");if(st(e[t-2])||st(e[t+2]))return o$(n,"Year","2-tagYear-far");if(a&&(a.tags.has("Determiner")||a.tags.has("Possessive"))&&i&&i.tags.has("Noun")&&!i.tags.has("Plural"))return o$(n,"Year","2-tagYear-noun")}}}return null},sr=function(e,t,n,r){let a=r.methods.one.setTag,i=e[t];i.tags.has("Verb")&&!["PastTense","PresentTense","Auxiliary","Modal","Particle"].find(e=>i.tags.has(e))&&a([i],"Infinitive",r,null,"2-verb-type''")};let sa=/^[A-Z]('s|,)?$/,si=/^[A-Z-]+$/,so=/^[A-Z]+s$/,ss=/([A-Z]\.)+[A-Z]?,?$/,sl=/[A-Z]{2,}('s|,)?$/,su=/([a-z]\.)+[a-z]\.?$/,sc={I:!0,A:!0},sh={la:!0,ny:!0,us:!0,dc:!0,gb:!0},sd=function(e,t){let n=e.text;if(!1===si.test(n)){if(!(n.length>3)||!0!==so.test(n))return!1;n=n.replace(/s$/,"")}return!(n.length>5||sc.hasOwnProperty(n)||t.one.lexicon.hasOwnProperty(e.normal))&&(!0===ss.test(n)||!0===su.test(n)||!0===sa.test(n)||!0===sl.test(n))};var sg=function(e,t,n){let r=e[t];return r.tags.has("RomanNumeral")||r.tags.has("Acronym")||r.frozen?null:sd(r,n)?(r.tags.clear(),o$(r,["Acronym","Noun"],"3-no-period-acronym"),!0===sh[r.normal]&&o$(r,"Place","3-place-acronym"),!0===so.test(r.text)&&o$(r,"Plural","3-plural-acronym"),!0):!sc.hasOwnProperty(r.text)&&sa.test(r.text)?(r.tags.clear(),o$(r,["Acronym","Noun"],"3-one-letter-acronym"),!0):r.tags.has("Organization")&&r.text.length<=3?(o$(r,"Acronym","3-org-acronym"),!0):r.tags.has("Organization")&&si.test(r.text)&&r.text.length<=6?(o$(r,"Acronym","3-titlecase-acronym"),!0):null};let sp=function(e,t){if(!e)return null;let n=t.find(t=>e.normal===t[0]);return n?n[1]:null},sm=function(e,t){if(!e)return null;let n=t.find(t=>e.tags.has(t[0]));return n?n[1]:null};var sf=function(e,t,n){let{leftTags:r,leftWords:a,rightWords:i,rightTags:o}=n.two.neighbours,s=e[t];if(0===s.tags.size){let l=null;if(l=(l=(l=(l=sp(e[t-1],a))||sp(e[t+1],i))||sm(e[t-1],r))||sm(e[t+1],o))return o$(s,l,"3-[neighbour]"),oW(e,t,n),e[t].confidence=.2,!0}return null};let sb=e=>/^\p{Lu}[\p{Ll}'’]/u.test(e),sv=function(e,t,n){return!(!e||e.tags.has("FirstName")||e.tags.has("Place"))&&(!!(e.tags.has("ProperNoun")||e.tags.has("Organization")||e.tags.has("Acronym"))||!!(!n&&sb(e.text))&&(0!==t||e.tags.has("Singular")))};var sy=function(e,t,n,r){let a=n.model.two.orgWords,i=n.methods.one.setTag,o=e[t];if(!0===a[o.machine||o.normal]&&sv(e[t-1],t-1,r)){i([e[t]],"Organization",n,null,"3-[org-word]");for(let a=t;a>=0;a-=1)if(sv(e[a],a,r))i([e[a]],"Organization",n,null,"3-[org-word]");else break}return null};let sw=e=>/^\p{Lu}[\p{Ll}'’]/u.test(e),sk=/'s$/,sP=new Set(["athletic","city","community","eastern","federal","financial","great","historic","historical","local","memorial","municipal","national","northern","provincial","southern","state","western","spring","pine","sunset","view","oak","maple","spruce","cedar","willow"]),sA=new Set(["center","centre","way","range","bar","bridge","field","pit"]),sx=function(e,t,n){if(!e)return!1;let r=e.tags;return!(r.has("Organization")||r.has("Possessive")||sk.test(e.normal))&&(!!(r.has("ProperNoun")||r.has("Place"))||!!(!n&&sw(e.text))&&(0!==t||r.has("Singular")))};var sC=function(e,t,n,r){let a=n.model.two.placeWords,i=n.methods.one.setTag,o=e[t],s=o.machine||o.normal;if(!0===a[s]){for(let a=t-1;a>=0;a-=1)if(!sP.has(e[a].normal)){if(sx(e[a],a,r)){i(e.slice(a,t+1),"Place",n,null,"3-[place-of-foo]");continue}break}if(sA.has(s))return!1;for(let a=t+1;a<e.length;a+=1){if(sx(e[a],a,r))return i(e.slice(t,a+1),"Place",n,null,"3-[foo-place]"),!0;if(!("of"===e[a].normal||sP.has(e[a].normal)))break}}return null},sj=function(e,t,n){let r=!1,a=e[t].tags;0===a.size?r=!0:1===a.size&&(a.has("Hyphenated")||a.has("HashTag")||a.has("Prefix")||a.has("SlashedTerm"))&&(r=!0),r&&(o$(e[t],"Noun","3-[fallback]"),oW(e,t,n),e[t].confidence=.1)};let sN=/^[A-Z][a-z]/,sI=(e,t)=>e[t].tags.has("ProperNoun")&&sN.test(e[t].text)?"Noun":null,sD=(e,t,n)=>0!==t||e[1]?null:n;var sT={"Adj|Gerund":(e,t)=>sI(e,t),"Adj|Noun":(e,t)=>sI(e,t)||(!e[t+1]&&e[t-1]&&e[t-1].tags.has("Determiner")?"Noun":null),"Actor|Verb":(e,t)=>sI(e,t),"Adj|Past":(e,t)=>sI(e,t),"Adj|Present":(e,t)=>sI(e,t),"Noun|Gerund":(e,t)=>sI(e,t),"Noun|Verb":(e,t)=>t>0&&sI(e,t)||sD(e,t,"Infinitive"),"Plural|Verb":(e,t)=>sI(e,t)||sD(e,t,"PresentTense")||(0===t&&e.length>3?"Plural":null),"Person|Noun":(e,t)=>sI(e,t),"Person|Verb":(e,t)=>0!==t?sI(e,t):null,"Person|Adj":(e,t)=>0===t&&e.length>1?"Person":sI(e,t)?"Person":null},sO=n(20357);let sH=void 0!==sO&&sO.env?sO.env:self.env||{},sE=/^(under|over|mis|re|un|dis|semi)-?/,sF=(e,t)=>{if(!e||!t)return null;let n=e.normal||e.implicit,r=null;return t.hasOwnProperty(n)&&(r=t[n]),r&&sH.DEBUG_TAGS&&console.log(`
  \x1b[2m\x1b[3m     ↓ - '${n}' \x1b[0m`),r},sG=(e,t={},n)=>{if(!e||!t)return null;let r=Array.from(e.tags).sort((e,t)=>(n[e]?n[e].parents.length:0)>(n[t]?n[t].parents.length:0)?-1:1).find(e=>t[e]);return r&&sH.DEBUG_TAGS&&console.log(`  \x1b[2m\x1b[3m      ↓ - '${e.normal||e.implicit}' (#${r})  \x1b[0m`),r=t[r]},sV=function(e,t,n,r){if(!n)return null;let a=e[t-1]?.text!=="also"?t-1:Math.max(0,t-2),i=r.one.tagSet,o=sF(e[t+1],n.afterWords);return(o=(o=o||sF(e[a],n.beforeWords))||sG(e[a],n.beforeTags,i))||sG(e[t+1],n.afterTags,i)};var sz=function(e,t,n){let r=n.model,a=n.methods.one.setTag,{switches:i,clues:o}=r.two,s=e[t],l=s.normal||s.implicit||"";if(sE.test(l)&&!i[l]&&(l=l.replace(sE,"")),s.switch){let i=s.switch;if(s.tags.has("Acronym")||s.tags.has("PhrasalVerb"))return;let u=sV(e,t,o[i],r);sT[i]&&(u=sT[i](e,t)||u),u?(a([s],u,n,null,`3-[switch] (${i})`),oW(e,t,r)):sH.DEBUG_TAGS&&console.log(`
 -> X  - '${l}'  : (${i})  `)}};let sM={there:!0,this:!0,it:!0,him:!0,her:!0,us:!0};var sB=function(e,t){let n=t.methods.one.setTag,r=t.model.one._multiCache||{},a=e[0];if(("Noun|Verb"===a.switch||a.tags.has("Infinitive"))&&e.length>=2){if(e.length<4&&!sM[e[1].normal]||!a.tags.has("PhrasalVerb")&&r.hasOwnProperty(a.normal))return;(e[1].tags.has("Noun")||e[1].tags.has("Determiner"))&&(!e.slice(1,3).some(e=>e.tags.has("Verb"))||a.tags.has("#PhrasalVerb"))&&n([a],"Imperative",t,null,"3-[imperative]")}};let sS=function(e){if(e.filter(e=>!e.tags.has("ProperNoun")).length<=3)return!1;let t=/^[a-z]/;return e.every(e=>!t.test(e.text))},s$=function(e,t,n){e.forEach(e=>{oG(e,0,t,n)})},sL=function(e,t,n,r){for(let a=0;a<e.length;a+=1)!0!==e[a].frozen&&(oM(e,a,t),!1===r&&oX(e,a,t),o1(e,a,t),o4(e,a,t,n),o9(e,a,t),sn(e,a,t))},sK=function(e,t,n,r){for(let n=0;n<e.length;n+=1){let r=sg(e,n,t);oW(e,n,t),r=(r=r||sf(e,n,t))||sj(e,n,t)}for(let a=0;a<e.length;a+=1)!0!==e[a].frozen&&(sy(e,a,n,r),sC(e,a,n,r),sz(e,a,n),sr(e,a,t,n),oV(e,a,t,n));sB(e,n)},sJ={Possessive:e=>(e.machine||e.normal||e.text).replace(/'s$/,""),Plural:(e,t)=>{let n=e.machine||e.normal||e.text;return t.methods.two.transform.noun.toSingular(n,t.model)},Copula:()=>"is",PastTense:(e,t)=>{let n=e.machine||e.normal||e.text;return t.methods.two.transform.verb.toInfinitive(n,t.model,"PastTense")},Gerund:(e,t)=>{let n=e.machine||e.normal||e.text;return t.methods.two.transform.verb.toInfinitive(n,t.model,"Gerund")},PresentTense:(e,t)=>{let n=e.machine||e.normal||e.text;return e.tags.has("Infinitive")?n:t.methods.two.transform.verb.toInfinitive(n,t.model,"PresentTense")},Comparative:(e,t)=>{let n=e.machine||e.normal||e.text;return t.methods.two.transform.adjective.fromComparative(n,t.model)},Superlative:(e,t)=>{let n=e.machine||e.normal||e.text;return t.methods.two.transform.adjective.fromSuperlative(n,t.model)},Adverb:(e,t)=>{let{fromAdverb:n}=t.methods.two.transform.adjective;return n(e.machine||e.normal||e.text)}},sW={Adverb:"RB",Comparative:"JJR",Superlative:"JJS",Adjective:"JJ",TO:"Conjunction",Modal:"MD",Auxiliary:"MD",Gerund:"VBG",PastTense:"VBD",Participle:"VBN",PresentTense:"VBZ",Infinitive:"VB",Particle:"RP",Verb:"VB",Pronoun:"PRP",Cardinal:"CD",Conjunction:"CC",Determiner:"DT",Preposition:"IN",QuestionWord:"WP",Expression:"UH",Possessive:"POS",ProperNoun:"NNP",Person:"NNP",Place:"NNP",Organization:"NNP",Singular:"NN",Plural:"NNS",Noun:"NN",There:"EX"},sq=function(e){if(e.tags.has("ProperNoun")&&e.tags.has("Plural"))return"NNPS";if(e.tags.has("Possessive")&&e.tags.has("Pronoun"))return"PRP$";if("there"===e.normal)return"EX";if("to"===e.normal)return"TO";let t=e.tagRank||[];for(let e=0;e<t.length;e+=1)if(sW.hasOwnProperty(t[e]))return sW[t[e]];return null},sR=["Person","Place","Organization"],sU=Object.assign({},{Noun:{not:["Verb","Adjective","Adverb","Value","Determiner"]},Singular:{is:"Noun",not:["Plural","Uncountable"]},ProperNoun:{is:"Noun"},Person:{is:"Singular",also:["ProperNoun"],not:["Place","Organization","Date"]},FirstName:{is:"Person"},MaleName:{is:"FirstName",not:["FemaleName","LastName"]},FemaleName:{is:"FirstName",not:["MaleName","LastName"]},LastName:{is:"Person",not:["FirstName"]},Honorific:{is:"Person",not:["FirstName","LastName","Value"]},Place:{is:"Singular",not:["Person","Organization"]},Country:{is:"Place",also:["ProperNoun"],not:["City"]},City:{is:"Place",also:["ProperNoun"],not:["Country"]},Region:{is:"Place",also:["ProperNoun"]},Address:{},Organization:{is:"ProperNoun",not:["Person","Place"]},SportsTeam:{is:"Organization"},School:{is:"Organization"},Company:{is:"Organization"},Plural:{is:"Noun",not:["Singular","Uncountable"]},Uncountable:{is:"Noun"},Pronoun:{is:"Noun",not:sR},Actor:{is:"Noun",not:["Place","Organization"]},Activity:{is:"Noun",not:["Person","Place"]},Unit:{is:"Noun",not:sR},Demonym:{is:"Noun",also:["ProperNoun"],not:sR},Possessive:{is:"Noun"},Reflexive:{is:"Pronoun"}},{Verb:{not:["Noun","Adjective","Adverb","Value","Expression"]},PresentTense:{is:"Verb",not:["PastTense","FutureTense"]},Infinitive:{is:"PresentTense",not:["Gerund"]},Imperative:{is:"Verb",not:["PastTense","Gerund","Copula"]},Gerund:{is:"PresentTense",not:["Copula"]},PastTense:{is:"Verb",not:["PresentTense","Gerund","FutureTense"]},FutureTense:{is:"Verb",not:["PresentTense","PastTense"]},Copula:{is:"Verb"},Modal:{is:"Verb",not:["Infinitive"]},Participle:{is:"PastTense"},Auxiliary:{is:"Verb",not:["PastTense","PresentTense","Gerund","Conjunction"]},PhrasalVerb:{is:"Verb"},Particle:{is:"PhrasalVerb",not:["PastTense","PresentTense","Copula","Gerund"]},Passive:{is:"Verb"}},{Value:{not:["Verb","Adjective","Adverb"]},Ordinal:{is:"Value",not:["Cardinal"]},Cardinal:{is:"Value",not:["Ordinal"]},Fraction:{is:"Value",not:["Noun"]},Multiple:{is:"TextValue"},RomanNumeral:{is:"Cardinal",not:["TextValue"]},TextValue:{is:"Value",not:["NumericValue"]},NumericValue:{is:"Value",not:["TextValue"]},Money:{is:"Cardinal"},Percent:{is:"Value"}},{Date:{not:["Verb","Adverb","Adjective"]},Month:{is:"Date",also:["Noun"],not:["Year","WeekDay","Time"]},WeekDay:{is:"Date",also:["Noun"]},Year:{is:"Date",not:["RomanNumeral"]},FinancialQuarter:{is:"Date",not:"Fraction"},Holiday:{is:"Date",also:["Noun"]},Season:{is:"Date"},Timezone:{is:"Date",also:["Noun"],not:["ProperNoun"]},Time:{is:"Date",not:["AtMention"]},Duration:{is:"Date",also:["Noun"]}},{Adjective:{not:["Noun","Verb","Adverb","Value"]},Comparable:{is:"Adjective"},Comparative:{is:"Adjective"},Superlative:{is:"Adjective",not:["Comparative"]},NumberRange:{},Adverb:{not:["Noun","Verb","Adjective","Value"]},Determiner:{not:["Noun","Verb","Adjective","Adverb","QuestionWord","Conjunction"]},Conjunction:{not:["Noun","Verb","Adjective","Adverb","Value","QuestionWord"]},Preposition:{not:["Noun","Verb","Adjective","Adverb","QuestionWord","Determiner"]},QuestionWord:{not:["Determiner"]},Currency:{is:"Noun"},Expression:{not:["Noun","Adjective","Verb","Adverb"]},Abbreviation:{},Url:{not:["HashTag","PhoneNumber","Verb","Adjective","Value","AtMention","Email","SlashedTerm"]},PhoneNumber:{not:["HashTag","Verb","Adjective","Value","AtMention","Email"]},HashTag:{},AtMention:{is:"Noun",not:["HashTag","Email"]},Emoji:{not:["HashTag","Verb","Adjective","Value","AtMention"]},Emoticon:{not:["HashTag","Verb","Adjective","Value","AtMention","SlashedTerm"]},SlashedTerm:{not:["Emoticon","Url","Value"]},Email:{not:["HashTag","Verb","Adjective","Value","AtMention"]},Acronym:{not:["Plural","RomanNumeral","Pronoun","Date"]},Negative:{not:["Noun","Adjective","Value","Expression"]},Condition:{not:["Verb","Adjective","Noun","Value"]},There:{not:["Verb","Adjective","Noun","Value","Conjunction","Preposition"]},Prefix:{not:["Abbreviation","Acronym","ProperNoun"]},Hyphenated:{}}),s_=/[,)"';:\-–—.…]/,sQ=function(e,t){if(!e.found)return;let n=e.termList();for(let e=0;e<n.length-1;e++){let t=n[e];if(s_.test(t.post))return}n[0].implicit=n[0].normal,n[0].text+=t,n[0].normal+=t,n.slice(1).forEach(e=>{e.implicit=e.normal,e.text="",e.normal=""});for(let e=0;e<n.length-1;e++)n[e].post=n[e].post.replace(/ /,"")};var sZ=function(){let e=this.not("@hasContraction"),t=e.match("(we|they|you) are");return sQ(t,"'re"),sQ(t=e.match("(he|she|they|it|we|you) will"),"'ll"),sQ(t=e.match("(he|she|they|it|we) is"),"'s"),sQ(t=e.match("#Person is"),"'s"),sQ(t=e.match("#Person would"),"'d"),sQ(t=e.match("(is|was|had|would|should|could|do|does|have|has|can) not"),"n't"),sQ(t=e.match("(i|we|they) have"),"'ve"),sQ(t=e.match("(would|should|could) have"),"'ve"),sQ(t=e.match("i am"),"'m"),t=e.match("going to"),this};let sY=/^\p{Lu}[\p{Ll}'’]/u;var sX=function(e,t,n){let[r,a]=t;n&&0!==n.length&&((n=n.map((e,t)=>(e.implicit=e.text,e.machine=e.text,e.pre="",e.post="",e.text="",e.normal="",e.index=[r,a+t],e)))[0]&&(n[0].pre=e[r][a].pre,n[n.length-1].post=e[r][a].post,n[0].text=e[r][a].text,n[0].normal=e[r][a].normal),e[r].splice(a,1,...n))};let s0=/'/,s1=new Set(["been","become"]),s2=new Set(["what","how","when","if","too"]),s3=new Set(["too","also","enough"]),s5=(e,t)=>{for(let n=t+1;n<e.length;n+=1){let t=e[n];if(s1.has(t.normal))return"has";if(s2.has(t.normal)||t.tags.has("Gerund")||t.tags.has("Determiner")||t.tags.has("Adjective"))break;if("Adj|Past"===t.switch&&e[n+1]){if(s3.has(e[n+1].normal))return"is";if(e[n+1].tags.has("Preposition"))break}if(t.tags.has("PastTense")){if(e[n+1]&&"for"===e[n+1].normal)return"is";return"has"}}return"is"};var s4=function(e,t){let n=e[t].normal.split(s0)[0];if("let"===n)return[n,"us"];if("there"===n){let r=e[t+1];if(r&&r.tags.has("Plural"))return[n,"are"]}return"has"===s5(e,t)?[n,"has"]:[n,"is"]};let s6=/'/,s9=new Set(["better","done","before","it","had"]),s7=new Set(["have","be"]),s8=(e,t)=>{for(let n=t+1;n<e.length;n+=1){let t=e[n];if(s9.has(t.normal))return"had";if(s7.has(t.normal))return"would";if(t.tags.has("PastTense")||"Adj|Past"===t.switch)return"had";if(t.tags.has("PresentTense")||t.tags.has("Infinitive"))return"would";if(t.tags.has("#Determiner"))return"had";if(t.tags.has("Adjective"))return"would"}return!1};var le=function(e,t){let n=e[t].normal.split(s6)[0];return"how"===n||"what"===n?[n,"did"]:"had"===s8(e,t)?[n,"had"]:[n,"would"]};let lt=function(e,t){for(let n=t-1;n>=0;n-=1)if(e[n].tags.has("Noun")||e[n].tags.has("Pronoun")||e[n].tags.has("Plural")||e[n].tags.has("Singular"))return e[n];return null};var ln=function(e,t){if("ain't"===e[t].normal||"aint"===e[t].normal){if(e[t+1]&&"never"===e[t+1].normal)return["have"];let n=lt(e,t);if(n){if("we"===n.normal||"they"===n.normal)return["are","not"];if("i"===n.normal)return["am","not"];if(n.tags&&n.tags.has("Plural"))return["are","not"]}return["is","not"]}return[e[t].normal.replace(/n't/,""),"not"]};let lr={that:!0,there:!0,let:!0,here:!0,everywhere:!0},la={in:!0,by:!0,for:!0},li=new Set(["too","also","enough","about"]),lo=new Set(["is","are","did","were","could","should","must","had","have"]);var ls=(e,t)=>{let n=e[t];if(lr.hasOwnProperty(n.machine||n.normal))return!1;if(n.tags.has("Possessive"))return!0;if(n.tags.has("QuestionWord")||"he's"===n.normal||"she's"===n.normal)return!1;let r=e[t+1];if(!r)return!0;if("it's"===n.normal)return!!r.tags.has("#Noun");if("Noun|Gerund"==r.switch){let r=e[t+2];return r?!!r.tags.has("Copula")||("on"===r.normal||r.normal,!1):!!(n.tags.has("Actor")||n.tags.has("ProperNoun"))}if(r.tags.has("Verb"))return!!r.tags.has("Infinitive")||!r.tags.has("Gerund")&&!!r.tags.has("PresentTense");if("Adj|Noun"===r.switch){let n=e[t+2];if(!n)return!1;if(lo.has(n.normal))return!0;if(li.has(n.normal))return!1}if(r.tags.has("Noun")){let e=r.machine||r.normal;return!("here"===e||"there"===e||"everywhere"===e||r.tags.has("Possessive")||r.tags.has("ProperNoun")&&!n.tags.has("ProperNoun"))}if(e[t-1]&&!0===la[e[t-1].normal])return!0;if(r.tags.has("Adjective")){let n=e[t+2];if(!n)return!1;if(n.tags.has("Noun")&&!n.tags.has("Pronoun")){let e=r.normal;return"above"!==e&&"below"!==e&&"behind"!==e}return"Noun|Verb"===n.switch}return!!r.tags.has("Value")};let ll=/'/,lu=function(e){e.forEach((e,t)=>{e.index&&(e.index[1]=t)})},lc=function(e,t,n,r){let a=t.update();a.document=[e];let i=n+r;n>0&&(n-=1),e[i]&&(i+=1),a.ptrs=[[0,n,i]],a.compute(["freeze","lexicon","preTagger","unfreeze"]),lu(e)},lh={d:(e,t)=>le(e,t),t:(e,t)=>ln(e,t),s:(e,t,n)=>ls(e,t)?n.methods.one.setTag([e[t]],"Possessive",n,null,"2-contraction"):s4(e,t)},ld=function(e,t){let n=t.fromText(e.join(" "));return n.compute("id"),n.docs[0]},lg="(hard|fast|late|early|high|right|deep|close|direct)",lp="(i|we|they)",lm=[].concat([{match:"(got|were|was|is|are|am) (#PastTense|#Participle)",tag:"Passive",reason:"got-walked"},{match:"(was|were|is|are|am) being (#PastTense|#Participle)",tag:"Passive",reason:"was-being"},{match:"(had|have|has) been (#PastTense|#Participle)",tag:"Passive",reason:"had-been"},{match:"will be being? (#PastTense|#Participle)",tag:"Passive",reason:"will-be-cleaned"},{match:"#Noun [(#PastTense|#Participle)] by (the|a) #Noun",group:0,tag:"Passive",reason:"suffered-by"}],[{match:"[(all|both)] #Determiner #Noun",group:0,tag:"Noun",reason:"all-noun"},{match:"#Copula [(just|alone)]$",group:0,tag:"Adjective",reason:"not-adverb"},{match:"#Singular is #Adverb? [#PastTense$]",group:0,tag:"Adjective",reason:"is-filled"},{match:"[#PastTense] #Singular is",group:0,tag:"Adjective",reason:"smoked-poutine"},{match:"[#PastTense] #Plural are",group:0,tag:"Adjective",reason:"baked-onions"},{match:"well [#PastTense]",group:0,tag:"Adjective",reason:"well-made"},{match:"#Copula [fucked up?]",group:0,tag:"Adjective",reason:"swears-adjective"},{match:"#Singular (seems|appears) #Adverb? [#PastTense$]",group:0,tag:"Adjective",reason:"seems-filled"},{match:"#Copula #Adjective? [(out|in|through)]$",group:0,tag:"Adjective",reason:"still-out"},{match:"^[#Adjective] (the|your) #Noun",group:0,notIf:"(all|even)",tag:"Infinitive",reason:"shut-the"},{match:"the [said] #Noun",group:0,tag:"Adjective",reason:"the-said-card"},{match:"[#Hyphenated (#Hyphenated && #PastTense)] (#Noun|#Conjunction)",group:0,tag:"Adjective",notIf:"#Adverb",reason:"faith-based"},{match:"[#Hyphenated (#Hyphenated && #Gerund)] (#Noun|#Conjunction)",group:0,tag:"Adjective",notIf:"#Adverb",reason:"self-driving"},{match:"[#PastTense (#Hyphenated && #PhrasalVerb)] (#Noun|#Conjunction)",group:0,tag:"Adjective",reason:"dammed-up"},{match:"(#Hyphenated && #Value) fold",tag:"Adjective",reason:"two-fold"},{match:"must (#Hyphenated && #Infinitive)",tag:"Adjective",reason:"must-win"},{match:"(#Hyphenated && #Infinitive) #Hyphenated",tag:"Adjective",notIf:"#PhrasalVerb",reason:"vacuum-sealed"},{match:"too much",tag:"Adverb Adjective",reason:"bit-4"},{match:"a bit much",tag:"Determiner Adverb Adjective",reason:"bit-3"},{match:"[(un|contra|extra|inter|intra|macro|micro|mid|mis|mono|multi|pre|sub|tri|ex)] #Adjective",group:0,tag:["Adjective","Prefix"],reason:"un-skilled"}],[{match:"#Adverb [#Adverb] (and|or|then)",group:0,tag:"Adjective",reason:"kinda-sparkly-and"},{match:"[(dark|bright|flat|light|soft|pale|dead|dim|faux|little|wee|sheer|most|near|good|extra|all)] #Adjective",group:0,tag:"Adverb",reason:"dark-green"},{match:"#Copula [far too] #Adjective",group:0,tag:"Adverb",reason:"far-too"},{match:"#Copula [still] (in|#Gerund|#Adjective)",group:0,tag:"Adverb",reason:"was-still-walking"},{match:`#Plural ${lg}`,tag:"#PresentTense #Adverb",reason:"studies-hard"},{match:`#Verb [${lg}] !#Noun?`,group:0,notIf:"(#Copula|get|got|getting|become|became|becoming|feel|feels|feeling|#Determiner|#Preposition)",tag:"Adverb",reason:"shops-direct"},{match:"[#Plural] a lot",tag:"PresentTense",reason:"studies-a-lot"}],[{match:"as [#Gerund] as",group:0,tag:"Adjective",reason:"as-gerund-as"},{match:"more [#Gerund] than",group:0,tag:"Adjective",reason:"more-gerund-than"},{match:"(so|very|extremely) [#Gerund]",group:0,tag:"Adjective",reason:"so-gerund"},{match:"(found|found) it #Adverb? [#Gerund]",group:0,tag:"Adjective",reason:"found-it-gerund"},{match:"a (little|bit|wee) bit? [#Gerund]",group:0,tag:"Adjective",reason:"a-bit-gerund"},{match:"#Gerund [#Gerund]",group:0,tag:"Adjective",notIf:"(impersonating|practicing|considering|assuming)",reason:"looking-annoying"},{match:"(looked|look|looks) #Adverb? [%Adj|Gerund%]",group:0,tag:"Adjective",notIf:"(impersonating|practicing|considering|assuming)",reason:"looked-amazing"},{match:"[%Adj|Gerund%] #Determiner",group:0,tag:"Gerund",reason:"developing-a"},{match:"#Possessive [%Adj|Gerund%] #Noun",group:0,tag:"Adjective",reason:"leading-manufacturer"},{match:"%Noun|Gerund% %Adj|Gerund%",tag:"Gerund #Adjective",reason:"meaning-alluring"},{match:"(face|embrace|reveal|stop|start|resume) %Adj|Gerund%",tag:"#PresentTense #Adjective",reason:"face-shocking"},{match:"(are|were) [%Adj|Gerund%] #Plural",group:0,tag:"Adjective",reason:"are-enduring-symbols"}],[{match:"#Determiner [#Adjective] #Copula",group:0,tag:"Noun",reason:"the-adj-is"},{match:"#Adjective [#Adjective] #Copula",group:0,tag:"Noun",reason:"adj-adj-is"},{match:"(his|its) [%Adj|Noun%]",group:0,tag:"Noun",notIf:"#Hyphenated",reason:"his-fine"},{match:"#Copula #Adverb? [all]",group:0,tag:"Noun",reason:"is-all"},{match:"(have|had) [#Adjective] #Preposition .",group:0,tag:"Noun",reason:"have-fun"},{match:"#Gerund (giant|capital|center|zone|application)",tag:"Noun",reason:"brewing-giant"},{match:"#Preposition (a|an) [#Adjective]$",group:0,tag:"Noun",reason:"an-instant"},{match:"no [#Adjective] #Modal",group:0,tag:"Noun",reason:"no-golden"},{match:"[brand #Gerund?] new",group:0,tag:"Adverb",reason:"brand-new"},{match:"(#Determiner|#Comparative|new|different) [kind]",group:0,tag:"Noun",reason:"some-kind"},{match:"#Possessive [%Adj|Noun%] #Noun",group:0,tag:"Adjective",reason:"her-favourite"},{match:"must && #Hyphenated .",tag:"Adjective",reason:"must-win"},{match:"#Determiner [#Adjective]$",tag:"Noun",notIf:"(this|that|#Comparative|#Superlative)",reason:"the-south"},{match:"(#Noun && #Hyphenated) (#Adjective && #Hyphenated)",tag:"Adjective",notIf:"(this|that|#Comparative|#Superlative)",reason:"company-wide"},{match:"#Determiner [#Adjective] (#Copula|#Determiner)",notIf:"(#Comparative|#Superlative)",group:0,tag:"Noun",reason:"the-poor"},{match:"[%Adj|Noun%] #Noun",notIf:"(#Pronoun|#ProperNoun)",group:0,tag:"Adjective",reason:"stable-foundations"}],[{match:"[still] #Adjective",group:0,tag:"Adverb",reason:"still-advb"},{match:"[still] #Verb",group:0,tag:"Adverb",reason:"still-verb"},{match:"[so] #Adjective",group:0,tag:"Adverb",reason:"so-adv"},{match:"[way] #Comparative",group:0,tag:"Adverb",reason:"way-adj"},{match:"[way] #Adverb #Adjective",group:0,tag:"Adverb",reason:"way-too-adj"},{match:"[all] #Verb",group:0,tag:"Adverb",reason:"all-verb"},{match:"#Verb  [like]",group:0,notIf:"(#Modal|#PhrasalVerb)",tag:"Adverb",reason:"verb-like"},{match:"(barely|hardly) even",tag:"Adverb",reason:"barely-even"},{match:"[even] #Verb",group:0,tag:"Adverb",reason:"even-walk"},{match:"[even] #Comparative",group:0,tag:"Adverb",reason:"even-worse"},{match:"[even] (#Determiner|#Possessive)",group:0,tag:"#Adverb",reason:"even-the"},{match:"even left",tag:"#Adverb #Verb",reason:"even-left"},{match:"[way] #Adjective",group:0,tag:"#Adverb",reason:"way-over"},{match:"#PresentTense [(hard|quick|bright|slow|fast|backwards|forwards)]",notIf:"#Copula",group:0,tag:"Adverb",reason:"lazy-ly"},{match:"[much] #Adjective",group:0,tag:"Adverb",reason:"bit-1"},{match:"#Copula [#Adverb]$",group:0,tag:"Adjective",reason:"is-well"},{match:"a [(little|bit|wee) bit?] #Adjective",group:0,tag:"Adverb",reason:"a-bit-cold"},{match:"[(super|pretty)] #Adjective",group:0,tag:"Adverb",reason:"super-strong"},{match:"(become|fall|grow) #Adverb? [#PastTense]",group:0,tag:"Adjective",reason:"overly-weakened"},{match:"(a|an) #Adverb [#Participle] #Noun",group:0,tag:"Adjective",reason:"completely-beaten"},{match:"#Determiner #Adverb? [close]",group:0,tag:"Adjective",reason:"a-close"},{match:"#Gerund #Adverb? [close]",group:0,tag:"Adverb",notIf:"(getting|becoming|feeling)",reason:"being-close"},{match:"(the|those|these|a|an) [#Participle] #Noun",group:0,tag:"Adjective",reason:"blown-motor"},{match:"(#PresentTense|#PastTense) [back]",group:0,tag:"Adverb",notIf:"(#PhrasalVerb|#Copula)",reason:"charge-back"},{match:"#Verb [around]",group:0,tag:"Adverb",notIf:"#PhrasalVerb",reason:"send-around"},{match:"[later] #PresentTense",group:0,tag:"Adverb",reason:"later-say"},{match:"#Determiner [well] !#PastTense?",group:0,tag:"Noun",reason:"the-well"},{match:"#Adjective [enough]",group:0,tag:"Adverb",reason:"high-enough"}],[{match:"[sun] the #Ordinal",tag:"WeekDay",reason:"sun-the-5th"},{match:"[sun] #Date",group:0,tag:"WeekDay",reason:"sun-feb"},{match:"#Date (on|this|next|last|during)? [sun]",group:0,tag:"WeekDay",reason:"1pm-sun"},{match:"(in|by|before|during|on|until|after|of|within|all) [sat]",group:0,tag:"WeekDay",reason:"sat"},{match:"(in|by|before|during|on|until|after|of|within|all) [wed]",group:0,tag:"WeekDay",reason:"wed"},{match:"(in|by|before|during|on|until|after|of|within|all) [march]",group:0,tag:"Month",reason:"march"},{match:"[sat] #Date",group:0,tag:"WeekDay",reason:"sat-feb"},{match:"#Preposition [(march|may)]",group:0,tag:"Month",reason:"in-month"},{match:"(this|next|last) (march|may) !#Infinitive?",tag:"#Date #Month",reason:"this-month"},{match:"(march|may) the? #Value",tag:"#Month #Date #Date",reason:"march-5th"},{match:"#Value of? (march|may)",tag:"#Date #Date #Month",reason:"5th-of-march"},{match:"[(march|may)] .? #Date",group:0,tag:"Month",reason:"march-and-feb"},{match:"#Date .? [(march|may)]",group:0,tag:"Month",reason:"feb-and-march"},{match:"#Adverb [(march|may)]",group:0,tag:"Verb",reason:"quickly-march"},{match:"[(march|may)] #Adverb",group:0,tag:"Verb",reason:"march-quickly"},{match:"#Value (am|pm)",tag:"Time",reason:"2-am"}],[{match:"#Holiday (day|eve)",tag:"Holiday",reason:"holiday-day"},{match:"#Value of #Month",tag:"Date",reason:"value-of-month"},{match:"#Cardinal #Month",tag:"Date",reason:"cardinal-month"},{match:"#Month #Value to #Value",tag:"Date",reason:"value-to-value"},{match:"#Month the #Value",tag:"Date",reason:"month-the-value"},{match:"(#WeekDay|#Month) #Value",tag:"Date",reason:"date-value"},{match:"#Value (#WeekDay|#Month)",tag:"Date",reason:"value-date"},{match:"(#TextValue && #Date) #TextValue",tag:"Date",reason:"textvalue-date"},{match:"#Month #NumberRange",tag:"Date",reason:"aug 20-21"},{match:"#WeekDay #Month #Ordinal",tag:"Date",reason:"week mm-dd"},{match:"#Month #Ordinal #Cardinal",tag:"Date",reason:"mm-dd-yyy"},{match:"(#Place|#Demonmym|#Time) (standard|daylight|central|mountain)? time",tag:"Timezone",reason:"std-time"},{match:"(eastern|mountain|pacific|central|atlantic) (standard|daylight|summer)? time",tag:"Timezone",reason:"eastern-time"},{match:"#Time [(eastern|mountain|pacific|central|est|pst|gmt)]",group:0,tag:"Timezone",reason:"5pm-central"},{match:"(central|western|eastern) european time",tag:"Timezone",reason:"cet"}],[{match:"(the|any) [more]",group:0,tag:"Singular",reason:"more-noun"},{match:"[more] #Noun",group:0,tag:"Adjective",reason:"more-noun"},{match:"(right|rights) of .",tag:"Noun",reason:"right-of"},{match:"a [bit]",group:0,tag:"Singular",reason:"bit-2"},{match:"a [must]",group:0,tag:"Singular",reason:"must-2"},{match:"(we|us) [all]",group:0,tag:"Noun",reason:"we all"},{match:"due to [#Verb]",group:0,tag:"Noun",reason:"due-to"},{match:"some [#Verb] #Plural",group:0,tag:"Noun",reason:"determiner6"},{match:"#Possessive #Ordinal [#PastTense]",group:0,tag:"Noun",reason:"first-thought"},{match:"(the|this|those|these) #Adjective [%Verb|Noun%]",group:0,tag:"Noun",notIf:"#Copula",reason:"the-adj-verb"},{match:"(the|this|those|these) #Adverb #Adjective [#Verb]",group:0,tag:"Noun",reason:"determiner4"},{match:"the [#Verb] #Preposition .",group:0,tag:"Noun",reason:"determiner1"},{match:"(a|an|the) [#Verb] of",group:0,tag:"Noun",reason:"the-verb-of"},{match:"#Determiner #Noun of [#Verb]",group:0,tag:"Noun",notIf:"#Gerund",reason:"noun-of-noun"},{match:"#PastTense #Preposition [#PresentTense]",group:0,notIf:"#Gerund",tag:"Noun",reason:"ended-in-ruins"},{match:"#Conjunction [u]",group:0,tag:"Pronoun",reason:"u-pronoun-2"},{match:"[u] #Verb",group:0,tag:"Pronoun",reason:"u-pronoun-1"},{match:"#Determiner [(western|eastern|northern|southern|central)] #Noun",group:0,tag:"Noun",reason:"western-line"},{match:"(#Singular && @hasHyphen) #PresentTense",tag:"Noun",reason:"hyphen-verb"},{match:"is no [#Verb]",group:0,tag:"Noun",reason:"is-no-verb"},{match:"do [so]",group:0,tag:"Noun",reason:"so-noun"},{match:"#Determiner [(shit|damn|hell)]",group:0,tag:"Noun",reason:"swears-noun"},{match:"to [(shit|hell)]",group:0,tag:"Noun",reason:"to-swears"},{match:"(the|these) [#Singular] (were|are)",group:0,tag:"Plural",reason:"singular-were"},{match:"a #Noun+ or #Adverb+? [#Verb]",group:0,tag:"Noun",reason:"noun-or-noun"},{match:"(the|those|these|a|an) #Adjective? [#PresentTense #Particle?]",group:0,tag:"Noun",notIf:"(seem|appear|include|#Gerund|#Copula)",reason:"det-inf"},{match:"#Noun #Actor",tag:"Actor",notIf:"(#Person|#Pronoun)",reason:"thing-doer"},{match:"#Gerund #Actor",tag:"Actor",reason:"gerund-doer"},{match:"co #Singular",tag:"Actor",reason:"co-noun"},{match:"[#Noun+] #Actor",group:0,tag:"Actor",notIf:"(#Honorific|#Pronoun|#Possessive)",reason:"air-traffic-controller"},{match:"(urban|cardiac|cardiovascular|respiratory|medical|clinical|visual|graphic|creative|dental|exotic|fine|certified|registered|technical|virtual|professional|amateur|junior|senior|special|pharmaceutical|theoretical)+ #Noun? #Actor",tag:"Actor",reason:"fine-artist"},{match:"#Noun+ (coach|chef|king|engineer|fellow|personality|boy|girl|man|woman|master)",tag:"Actor",reason:"dance-coach"},{match:"chief . officer",tag:"Actor",reason:"chief-x-officer"},{match:"chief of #Noun+",tag:"Actor",reason:"chief-of-police"},{match:"senior? vice? president of #Noun+",tag:"Actor",reason:"president-of"},{match:"#Determiner [sun]",group:0,tag:"Singular",reason:"the-sun"},{match:"#Verb (a|an) [#Value]$",group:0,tag:"Singular",reason:"did-a-value"},{match:"the [(can|will|may)]",group:0,tag:"Singular",reason:"the can"},{match:"#FirstName #Acronym? (#Possessive && #LastName)",tag:"Possessive",reason:"name-poss"},{match:"#Organization+ #Possessive",tag:"Possessive",reason:"org-possessive"},{match:"#Place+ #Possessive",tag:"Possessive",reason:"place-possessive"},{match:"#Possessive #PresentTense #Particle?",notIf:"(#Gerund|her)",tag:"Noun",reason:"possessive-verb"},{match:"(my|our|their|her|his|its) [(#Plural && #Actor)] #Noun",tag:"Possessive",reason:"my-dads"},{match:"#Value of a [second]",group:0,unTag:"Value",tag:"Singular",reason:"10th-of-a-second"},{match:"#Value [seconds]",group:0,unTag:"Value",tag:"Plural",reason:"10-seconds"},{match:"in [#Infinitive]",group:0,tag:"Singular",reason:"in-age"},{match:"a [#Adjective] #Preposition",group:0,tag:"Noun",reason:"a-minor-in"},{match:"#Determiner [#Singular] said",group:0,tag:"Actor",reason:"the-actor-said"},{match:"#Determiner #Noun [(feel|sense|process|rush|side|bomb|bully|challenge|cover|crush|dump|exchange|flow|function|issue|lecture|limit|march|process)] !(#Preposition|to|#Adverb)?",group:0,tag:"Noun",reason:"the-noun-sense"},{match:"[#PresentTense] (of|by|for) (a|an|the) #Noun #Copula",group:0,tag:"Plural",reason:"photographs-of"},{match:"#Infinitive and [%Noun|Verb%]",group:0,tag:"Infinitive",reason:"fight and win"},{match:"#Noun and [#Verb] and #Noun",group:0,tag:"Noun",reason:"peace-and-flowers"},{match:"the #Cardinal [%Adj|Noun%]",group:0,tag:"Noun",reason:"the-1992-classic"},{match:"#Copula the [%Adj|Noun%] #Noun",group:0,tag:"Adjective",reason:"the-premier-university"},{match:"i #Verb [me] #Noun",group:0,tag:"Possessive",reason:"scottish-me"},{match:"[#PresentTense] (music|class|lesson|night|party|festival|league|ceremony)",group:0,tag:"Noun",reason:"dance-music"},{match:"[wit] (me|it)",group:0,tag:"Presposition",reason:"wit-me"},{match:"#PastTense #Possessive [#Verb]",group:0,tag:"Noun",notIf:"(saw|made)",reason:"left-her-boots"},{match:"#Value [%Plural|Verb%]",group:0,tag:"Plural",notIf:"(one|1|a|an)",reason:"35-signs"},{match:"had [#PresentTense]",group:0,tag:"Noun",notIf:"(#Gerund|come|become)",reason:"had-time"},{match:"%Adj|Noun% %Noun|Verb%",tag:"#Adjective #Noun",notIf:"#ProperNoun #Noun",reason:"instant-access"},{match:"#Determiner [%Adj|Noun%] #Conjunction",group:0,tag:"Noun",reason:"a-rep-to"},{match:"#Adjective #Noun [%Plural|Verb%]$",group:0,tag:"Plural",notIf:"#Pronoun",reason:"near-death-experiences"},{match:"#Possessive #Noun [%Plural|Verb%]$",group:0,tag:"Plural",reason:"your-guild-colors"}],[{match:"(this|that|the|a|an) [#Gerund #Infinitive]",group:0,tag:"Singular",reason:"the-planning-process"},{match:"(that|the) [#Gerund #PresentTense]",group:0,ifNo:"#Copula",tag:"Plural",reason:"the-paving-stones"},{match:"#Determiner [#Gerund] #Noun",group:0,tag:"Adjective",reason:"the-gerund-noun"},{match:"#Pronoun #Infinitive [#Gerund] #PresentTense",group:0,tag:"Noun",reason:"tipping-sucks"},{match:"#Adjective [#Gerund]",group:0,tag:"Noun",notIf:"(still|even|just)",reason:"early-warning"},{match:"[#Gerund] #Adverb? not? #Copula",group:0,tag:"Activity",reason:"gerund-copula"},{match:"#Copula [(#Gerund|#Activity)] #Copula",group:0,tag:"Gerund",reason:"are-doing-is"},{match:"[#Gerund] #Modal",group:0,tag:"Activity",reason:"gerund-modal"},{match:"#Singular for [%Noun|Gerund%]",group:0,tag:"Gerund",reason:"noun-for-gerund"},{match:"#Comparative (for|at) [%Noun|Gerund%]",group:0,tag:"Gerund",reason:"better-for-gerund"},{match:"#PresentTense the [#Gerund]",group:0,tag:"Noun",reason:"keep-the-touching"}],[{match:"#Infinitive (this|that|the) [#Infinitive]",group:0,tag:"Noun",reason:"do-this-dance"},{match:"#Gerund #Determiner [#Infinitive]",group:0,tag:"Noun",reason:"running-a-show"},{match:"#Determiner (only|further|just|more|backward) [#Infinitive]",group:0,tag:"Noun",reason:"the-only-reason"},{match:"(the|this|a|an) [#Infinitive] #Adverb? #Verb",group:0,tag:"Noun",reason:"determiner5"},{match:"#Determiner #Adjective #Adjective? [#Infinitive]",group:0,tag:"Noun",reason:"a-nice-inf"},{match:"#Determiner #Demonym [#PresentTense]",group:0,tag:"Noun",reason:"mexican-train"},{match:"#Adjective #Noun+ [#Infinitive] #Copula",group:0,tag:"Noun",reason:"career-move"},{match:"at some [#Infinitive]",group:0,tag:"Noun",reason:"at-some-inf"},{match:"(go|goes|went) to [#Infinitive]",group:0,tag:"Noun",reason:"goes-to-verb"},{match:"(a|an) #Adjective? #Noun [#Infinitive] (#Preposition|#Noun)",group:0,notIf:"from",tag:"Noun",reason:"a-noun-inf"},{match:"(a|an) #Noun [#Infinitive]$",group:0,tag:"Noun",reason:"a-noun-inf2"},{match:"#Gerund #Adjective? for [#Infinitive]",group:0,tag:"Noun",reason:"running-for"},{match:"about [#Infinitive]",group:0,tag:"Singular",reason:"about-love"},{match:"#Plural on [#Infinitive]",group:0,tag:"Noun",reason:"on-stage"},{match:"any [#Infinitive]",group:0,tag:"Noun",reason:"any-charge"},{match:"no [#Infinitive]",group:0,tag:"Noun",reason:"no-doubt"},{match:"number of [#PresentTense]",group:0,tag:"Noun",reason:"number-of-x"},{match:"(taught|teaches|learns|learned) [#PresentTense]",group:0,tag:"Noun",reason:"teaches-x"},{match:"(try|use|attempt|build|make) [#Verb #Particle?]",notIf:"(#Copula|#Noun|sure|fun|up)",group:0,tag:"Noun",reason:"do-verb"},{match:"^[#Infinitive] (is|was)",group:0,tag:"Noun",reason:"checkmate-is"},{match:"#Infinitive much [#Infinitive]",group:0,tag:"Noun",reason:"get-much"},{match:"[cause] #Pronoun #Verb",group:0,tag:"Conjunction",reason:"cause-cuz"},{match:"the #Singular [#Infinitive] #Noun",group:0,tag:"Noun",notIf:"#Pronoun",reason:"cardio-dance"},{match:"#Determiner #Modal [#Noun]",group:0,tag:"PresentTense",reason:"should-smoke"},{match:"this [#Plural]",group:0,tag:"PresentTense",notIf:"(#Preposition|#Date)",reason:"this-verbs"},{match:"#Noun that [#Plural]",group:0,tag:"PresentTense",notIf:"(#Preposition|#Pronoun|way)",reason:"voice-that-rocks"},{match:"that [#Plural] to",group:0,tag:"PresentTense",notIf:"#Preposition",reason:"that-leads-to"},{match:"(let|make|made) (him|her|it|#Person|#Place|#Organization)+ [#Singular] (a|an|the|it)",group:0,tag:"Infinitive",reason:"let-him-glue"},{match:"#Verb (all|every|each|most|some|no) [#PresentTense]",notIf:"#Modal",group:0,tag:"Noun",reason:"all-presentTense"},{match:"(had|have|#PastTense) #Adjective [#PresentTense]",group:0,tag:"Noun",notIf:"better",reason:"adj-presentTense"},{match:"#Value #Adjective [#PresentTense]",group:0,tag:"Noun",notIf:"#Copula",reason:"one-big-reason"},{match:"#PastTense #Adjective+ [#PresentTense]",group:0,tag:"Noun",notIf:"(#Copula|better)",reason:"won-wide-support"},{match:"(many|few|several|couple) [#PresentTense]",group:0,tag:"Noun",notIf:"#Copula",reason:"many-poses"},{match:"#Determiner #Adverb #Adjective [%Noun|Verb%]",group:0,tag:"Noun",notIf:"#Copula",reason:"very-big-dream"},{match:"from #Noun to [%Noun|Verb%]",group:0,tag:"Noun",reason:"start-to-finish"},{match:"(for|with|of) #Noun (and|or|not) [%Noun|Verb%]",group:0,tag:"Noun",notIf:"#Pronoun",reason:"for-food-and-gas"},{match:"#Adjective #Adjective [#PresentTense]",group:0,tag:"Noun",notIf:"#Copula",reason:"adorable-little-store"},{match:"#Gerund #Adverb? #Comparative [#PresentTense]",group:0,tag:"Noun",notIf:"#Copula",reason:"higher-costs"},{match:"(#Noun && @hasComma) #Noun (and|or) [#PresentTense]",group:0,tag:"Noun",notIf:"#Copula",reason:"noun-list"},{match:"(many|any|some|several) [#PresentTense] for",group:0,tag:"Noun",reason:"any-verbs-for"},{match:"to #PresentTense #Noun [#PresentTense] #Preposition",group:0,tag:"Noun",reason:"gas-exchange"},{match:"#PastTense (until|as|through|without) [#PresentTense]",group:0,tag:"Noun",reason:"waited-until-release"},{match:"#Gerund like #Adjective? [#PresentTense]",group:0,tag:"Plural",reason:"like-hot-cakes"},{match:"some #Adjective [#PresentTense]",group:0,tag:"Noun",reason:"some-reason"},{match:"for some [#PresentTense]",group:0,tag:"Noun",reason:"for-some-reason"},{match:"(same|some|the|that|a) kind of [#PresentTense]",group:0,tag:"Noun",reason:"some-kind-of"},{match:"(same|some|the|that|a) type of [#PresentTense]",group:0,tag:"Noun",reason:"some-type-of"},{match:"#Gerund #Adjective #Preposition [#PresentTense]",group:0,tag:"Noun",reason:"doing-better-for-x"},{match:"(get|got|have) #Comparative [#PresentTense]",group:0,tag:"Noun",reason:"got-better-aim"},{match:"whose [#PresentTense] #Copula",group:0,tag:"Noun",reason:"whos-name-was"},{match:"#PhrasalVerb #Particle #Preposition [#PresentTense]",group:0,tag:"Noun",reason:"given-up-on-x"},{match:"there (are|were) #Adjective? [#PresentTense]",group:0,tag:"Plural",reason:"there-are"},{match:"#Value [#PresentTense] of",group:0,notIf:"(one|1|#Copula|#Infinitive)",tag:"Plural",reason:"2-trains"},{match:"[#PresentTense] (are|were) #Adjective",group:0,tag:"Plural",reason:"compromises-are-possible"},{match:"^[(hope|guess|thought|think)] #Pronoun #Verb",group:0,tag:"Infinitive",reason:"suppose-i"},{match:"#Possessive #Adjective [#Verb]",group:0,tag:"Noun",notIf:"#Copula",reason:"our-full-support"},{match:"[(tastes|smells)] #Adverb? #Adjective",group:0,tag:"PresentTense",reason:"tastes-good"},{match:"#Copula #Gerund [#PresentTense] !by?",group:0,tag:"Noun",notIf:"going",reason:"ignoring-commute"},{match:"#Determiner #Adjective? [(shed|thought|rose|bid|saw|spelt)]",group:0,tag:"Noun",reason:"noun-past"},{match:"how to [%Noun|Verb%]",group:0,tag:"Infinitive",reason:"how-to-noun"},{match:"which [%Noun|Verb%] #Noun",group:0,tag:"Infinitive",reason:"which-boost-it"},{match:"#Gerund [%Plural|Verb%]",group:0,tag:"Plural",reason:"asking-questions"},{match:"(ready|available|difficult|hard|easy|made|attempt|try) to [%Noun|Verb%]",group:0,tag:"Infinitive",reason:"ready-to-noun"},{match:"(bring|went|go|drive|run|bike) to [%Noun|Verb%]",group:0,tag:"Noun",reason:"bring-to-noun"},{match:"#Modal #Noun [%Noun|Verb%]",group:0,tag:"Infinitive",reason:"would-you-look"},{match:"#Copula just [#Infinitive]",group:0,tag:"Noun",reason:"is-just-spam"},{match:"^%Noun|Verb% %Plural|Verb%",tag:"Imperative #Plural",reason:"request-copies"},{match:"#Adjective #Plural and [%Plural|Verb%]",group:0,tag:"#Plural",reason:"pickles-and-drinks"},{match:"#Determiner #Year [#Verb]",group:0,tag:"Noun",reason:"the-1968-film"},{match:"#Determiner [#PhrasalVerb #Particle]",group:0,tag:"Noun",reason:"the-break-up"},{match:"#Determiner [%Adj|Noun%] #Noun",group:0,tag:"Adjective",notIf:"(#Pronoun|#Possessive|#ProperNoun)",reason:"the-individual-goals"},{match:"[%Noun|Verb%] or #Infinitive",group:0,tag:"Infinitive",reason:"work-or-prepare"},{match:"to #Infinitive [#PresentTense]",group:0,tag:"Noun",notIf:"(#Gerund|#Copula|help)",reason:"to-give-thanks"},{match:"[#Noun] me",group:0,tag:"Verb",reason:"kills-me"},{match:"%Plural|Verb% %Plural|Verb%",tag:"#PresentTense #Plural",reason:"removes-wrinkles"}],[{match:"#Money and #Money #Currency?",tag:"Money",reason:"money-and-money"},{match:"#Value #Currency [and] #Value (cents|ore|centavos|sens)",group:0,tag:"money",reason:"and-5-cents"},{match:"#Value (mark|rand|won|rub|ore)",tag:"#Money #Currency",reason:"4-mark"},{match:"a pound",tag:"#Money #Unit",reason:"a-pound"},{match:"#Value (pound|pounds)",tag:"#Money #Unit",reason:"4-pounds"}],[{match:"[(half|quarter)] of? (a|an)",group:0,tag:"Fraction",reason:"millionth"},{match:"#Adverb [half]",group:0,tag:"Fraction",reason:"nearly-half"},{match:"[half] the",group:0,tag:"Fraction",reason:"half-the"},{match:"#Cardinal and a half",tag:"Fraction",reason:"and-a-half"},{match:"#Value (halves|halfs|quarters)",tag:"Fraction",reason:"two-halves"},{match:"a #Ordinal",tag:"Fraction",reason:"a-quarter"},{match:"[#Cardinal+] (#Fraction && /s$/)",tag:"Fraction",reason:"seven-fifths"},{match:"[#Cardinal+ #Ordinal] of .",group:0,tag:"Fraction",reason:"ordinal-of"},{match:"[(#NumericValue && #Ordinal)] of .",group:0,tag:"Fraction",reason:"num-ordinal-of"},{match:"(a|one) #Cardinal?+ #Ordinal",tag:"Fraction",reason:"a-ordinal"},{match:"#Cardinal+ out? of every? #Cardinal",tag:"Fraction",reason:"out-of"}],[{match:"#Cardinal [second]",tag:"Unit",reason:"one-second"},{match:"!once? [(a|an)] (#Duration|hundred|thousand|million|billion|trillion)",group:0,tag:"Value",reason:"a-is-one"},{match:"1 #Value #PhoneNumber",tag:"PhoneNumber",reason:"1-800-Value"},{match:"#NumericValue #PhoneNumber",tag:"PhoneNumber",reason:"(800) PhoneNumber"},{match:"#Demonym #Currency",tag:"Currency",reason:"demonym-currency"},{match:"#Value [(buck|bucks|grand)]",group:0,tag:"Currency",reason:"value-bucks"},{match:"[#Value+] #Currency",group:0,tag:"Money",reason:"15 usd"},{match:"[second] #Noun",group:0,tag:"Ordinal",reason:"second-noun"},{match:"#Value+ [#Currency]",group:0,tag:"Unit",reason:"5-yan"},{match:"#Value [(foot|feet)]",group:0,tag:"Unit",reason:"foot-unit"},{match:"#Value [#Abbreviation]",group:0,tag:"Unit",reason:"value-abbr"},{match:"#Value [k]",group:0,tag:"Unit",reason:"value-k"},{match:"#Unit an hour",tag:"Unit",reason:"unit-an-hour"},{match:"(minus|negative) #Value",tag:"Value",reason:"minus-value"},{match:"#Value (point|decimal) #Value",tag:"Value",reason:"value-point-value"},{match:"#Determiner [(half|quarter)] #Ordinal",group:0,tag:"Value",reason:"half-ordinal"},{match:"#Multiple+ and #Value",tag:"Value",reason:"magnitude-and-value"},{match:"#Value #Unit [(per|an) (hr|hour|sec|second|min|minute)]",group:0,tag:"Unit",reason:"12-miles-per-second"},{match:"#Value [(square|cubic)] #Unit",group:0,tag:"Unit",reason:"square-miles"}],[{match:"#Copula [(#Noun|#PresentTense)] #LastName",group:0,tag:"FirstName",reason:"copula-noun-lastname"},{match:"(sister|pope|brother|father|aunt|uncle|grandpa|grandfather|grandma) #ProperNoun",tag:"Person",reason:"lady-titlecase",safe:!0},{match:"#FirstName [#Determiner #Noun] #LastName",group:0,tag:"Person",reason:"first-noun-last"},{match:"#ProperNoun (b|c|d|e|f|g|h|j|k|l|m|n|o|p|q|r|s|t|u|v|w|x|y|z) #ProperNoun",tag:"Person",reason:"titlecase-acronym-titlecase",safe:!0},{match:"#Acronym #LastName",tag:"Person",reason:"acronym-lastname",safe:!0},{match:"#Person (jr|sr|md)",tag:"Person",reason:"person-honorific"},{match:"#Honorific #Acronym",tag:"Person",reason:"Honorific-TitleCase"},{match:"#Person #Person the? #RomanNumeral",tag:"Person",reason:"roman-numeral"},{match:"#FirstName [/^[^aiurck]$/]",group:0,tag:["Acronym","Person"],reason:"john-e"},{match:"#Noun van der? #Noun",tag:"Person",reason:"van der noun",safe:!0},{match:"(king|queen|prince|saint|lady) of #Noun",tag:"Person",reason:"king-of-noun",safe:!0},{match:"(prince|lady) #Place",tag:"Person",reason:"lady-place"},{match:"(king|queen|prince|saint) #ProperNoun",tag:"Person",notIf:"#Place",reason:"saint-foo"},{match:"al (#Person|#ProperNoun)",tag:"Person",reason:"al-borlen",safe:!0},{match:"#FirstName de #Noun",tag:"Person",reason:"bill-de-noun"},{match:"#FirstName (bin|al) #Noun",tag:"Person",reason:"bill-al-noun"},{match:"#FirstName #Acronym #ProperNoun",tag:"Person",reason:"bill-acronym-title"},{match:"#FirstName #FirstName #ProperNoun",tag:"Person",reason:"bill-firstname-title"},{match:"#Honorific #FirstName? #ProperNoun",tag:"Person",reason:"dr-john-Title"},{match:"#FirstName the #Adjective",tag:"Person",reason:"name-the-great"},{match:"#ProperNoun (van|al|bin) #ProperNoun",tag:"Person",reason:"title-van-title",safe:!0},{match:"#ProperNoun (de|du) la? #ProperNoun",tag:"Person",notIf:"#Place",reason:"title-de-title"},{match:"#Singular #Acronym #LastName",tag:"#FirstName #Person .",reason:"title-acro-noun",safe:!0},{match:"[#ProperNoun] #Person",group:0,tag:"Person",reason:"proper-person",safe:!0},{match:"#Person [#ProperNoun #ProperNoun]",group:0,tag:"Person",notIf:"#Possessive",reason:"three-name-person",safe:!0},{match:"#FirstName #Acronym? [#ProperNoun]",group:0,tag:"LastName",notIf:"#Possessive",reason:"firstname-titlecase"},{match:"#FirstName [#FirstName]",group:0,tag:"LastName",reason:"firstname-firstname"},{match:"#FirstName #Acronym #Noun",tag:"Person",reason:"n-acro-noun",safe:!0},{match:"#FirstName [(de|di|du|van|von)] #Person",group:0,tag:"LastName",reason:"de-firstname"},{match:"[(lieutenant|corporal|sergeant|captain|qeen|king|admiral|major|colonel|marshal|president|queen|king)+] #ProperNoun",group:0,tag:"Honorific",reason:"seargeant-john"},{match:"[(private|general|major|rear|prime|field|count|miss)] #Honorific? #Person",group:0,tag:["Honorific","Person"],reason:"ambg-honorifics"},{match:"#Honorific #FirstName [#Singular]",group:0,tag:"LastName",notIf:"#Possessive",reason:"dr-john-foo",safe:!0},{match:"[(his|her) (majesty|honour|worship|excellency|honorable)] #Person",group:0,tag:"Honorific",reason:"his-excellency"},{match:"#Honorific #Actor",tag:"Honorific",reason:"Lieutenant colonel"},{match:"(first|second|third|1st|2nd|3rd) #Actor",tag:"Honorific",reason:"first lady"},{match:"#Person #RomanNumeral",tag:"Person",reason:"louis-IV"}],[{match:"#FirstName #Noun$",tag:". #LastName",notIf:"(#Possessive|#Organization|#Place|#Pronoun|@hasTitleCase)",reason:"firstname-noun"},{match:"%Person|Date% #Acronym? #ProperNoun",tag:"Person",reason:"jan-thierson"},{match:"%Person|Noun% #Acronym? #ProperNoun",tag:"Person",reason:"switch-person",safe:!0},{match:"%Person|Noun% #Organization",tag:"Organization",reason:"olive-garden"},{match:"%Person|Verb% #Acronym? #ProperNoun",tag:"Person",reason:"verb-propernoun",ifNo:"#Actor"},{match:"[%Person|Verb%] (will|had|has|said|says|told|did|learned|wants|wanted)",group:0,tag:"Person",reason:"person-said"},{match:"[%Person|Place%] (harbor|harbour|pier|town|city|place|dump|landfill)",group:0,tag:"Place",reason:"sydney-harbour"},{match:"(west|east|north|south) [%Person|Place%]",group:0,tag:"Place",reason:"east-sydney"},{match:"#Modal [%Person|Verb%]",group:0,tag:"Verb",reason:"would-mark"},{match:"#Adverb [%Person|Verb%]",group:0,tag:"Verb",reason:"really-mark"},{match:"[%Person|Verb%] (#Adverb|#Comparative)",group:0,tag:"Verb",reason:"drew-closer"},{match:"%Person|Verb% #Person",tag:"Person",reason:"rob-smith"},{match:"%Person|Verb% #Acronym #ProperNoun",tag:"Person",reason:"rob-a-smith"},{match:"[will] #Verb",group:0,tag:"Modal",reason:"will-verb"},{match:"(will && @isTitleCase) #ProperNoun",tag:"Person",reason:"will-name"},{match:"(#FirstName && !#Possessive) [#Singular] #Verb",group:0,safe:!0,tag:"LastName",reason:"jack-layton"},{match:"^[#Singular] #Person #Verb",group:0,safe:!0,tag:"Person",reason:"sherwood-anderson"},{match:"(a|an) [#Person]$",group:0,unTag:"Person",reason:"a-warhol"}],[{match:"#Copula (pretty|dead|full|well|sure) (#Adjective|#Noun)",tag:"#Copula #Adverb #Adjective",reason:"sometimes-adverb"},{match:"(#Pronoun|#Person) (had|#Adverb)? [better] #PresentTense",group:0,tag:"Modal",reason:"i-better"},{match:"(#Modal|i|they|we|do) not? [like]",group:0,tag:"PresentTense",reason:"modal-like"},{match:"#Noun #Adverb? [left]",group:0,tag:"PastTense",reason:"left-verb"},{match:"will #Adverb? not? #Adverb? [be] #Gerund",group:0,tag:"Copula",reason:"will-be-copula"},{match:"will #Adverb? not? #Adverb? [be] #Adjective",group:0,tag:"Copula",reason:"be-copula"},{match:"[march] (up|down|back|toward)",notIf:"#Date",group:0,tag:"Infinitive",reason:"march-to"},{match:"#Modal [march]",group:0,tag:"Infinitive",reason:"must-march"},{match:"[may] be",group:0,tag:"Verb",reason:"may-be"},{match:"[(subject|subjects|subjected)] to",group:0,tag:"Verb",reason:"subject to"},{match:"[home] to",group:0,tag:"PresentTense",reason:"home to"},{match:"[open] #Determiner",group:0,tag:"Infinitive",reason:"open-the"},{match:"(were|was) being [#PresentTense]",group:0,tag:"PastTense",reason:"was-being"},{match:"(had|has|have) [been /en$/]",group:0,tag:"Auxiliary Participle",reason:"had-been-broken"},{match:"(had|has|have) [been /ed$/]",group:0,tag:"Auxiliary PastTense",reason:"had-been-smoked"},{match:"(had|has) #Adverb? [been] #Adverb? #PastTense",group:0,tag:"Auxiliary",reason:"had-been-adj"},{match:"(had|has) to [#Noun] (#Determiner|#Possessive)",group:0,tag:"Infinitive",reason:"had-to-noun"},{match:"have [#PresentTense]",group:0,tag:"PastTense",notIf:"(come|gotten)",reason:"have-read"},{match:"(does|will|#Modal) that [work]",group:0,tag:"PastTense",reason:"does-that-work"},{match:"[(sound|sounds)] #Adjective",group:0,tag:"PresentTense",reason:"sounds-fun"},{match:"[(look|looks)] #Adjective",group:0,tag:"PresentTense",reason:"looks-good"},{match:"[(start|starts|stop|stops|begin|begins)] #Gerund",group:0,tag:"Verb",reason:"starts-thinking"},{match:"(have|had) read",tag:"Modal #PastTense",reason:"read-read"},{match:"(is|was|were) [(under|over) #PastTense]",group:0,tag:"Adverb Adjective",reason:"was-under-cooked"},{match:"[shit] (#Determiner|#Possessive|them)",group:0,tag:"Verb",reason:"swear1-verb"},{match:"[damn] (#Determiner|#Possessive|them)",group:0,tag:"Verb",reason:"swear2-verb"},{match:"[fuck] (#Determiner|#Possessive|them)",group:0,tag:"Verb",reason:"swear3-verb"},{match:"#Plural that %Noun|Verb%",tag:". #Preposition #Infinitive",reason:"jobs-that-work"},{match:"[works] for me",group:0,tag:"PresentTense",reason:"works-for-me"},{match:"as #Pronoun [please]",group:0,tag:"Infinitive",reason:"as-we-please"},{match:"[(co|mis|de|inter|intra|pre|re|un|out|under|over|counter)] #Verb",group:0,tag:["Verb","Prefix"],notIf:"(#Copula|#PhrasalVerb)",reason:"co-write"},{match:"#PastTense and [%Adj|Past%]",group:0,tag:"PastTense",reason:"dressed-and-left"},{match:"[%Adj|Past%] and #PastTense",group:0,tag:"PastTense",reason:"dressed-and-left"},{match:"#Copula #Pronoun [%Adj|Past%]",group:0,tag:"Adjective",reason:"is-he-stoked"},{match:"to [%Noun|Verb%] #Preposition",group:0,tag:"Infinitive",reason:"to-dream-of"}],[{match:"(slowly|quickly) [#Adjective]",group:0,tag:"Verb",reason:"slowly-adj"},{match:"does (#Adverb|not)? [#Adjective]",group:0,tag:"PresentTense",reason:"does-mean"},{match:"[(fine|okay|cool|ok)] by me",group:0,tag:"Adjective",reason:"okay-by-me"},{match:"i (#Adverb|do)? not? [mean]",group:0,tag:"PresentTense",reason:"i-mean"},{match:"will #Adjective",tag:"Auxiliary Infinitive",reason:"will-adj"},{match:"#Pronoun [#Adjective] #Determiner #Adjective? #Noun",group:0,tag:"Verb",reason:"he-adj-the"},{match:"#Copula [%Adj|Present%] to #Verb",group:0,tag:"Verb",reason:"adj-to"},{match:"#Copula [#Adjective] (well|badly|quickly|slowly)",group:0,tag:"Verb",reason:"done-well"},{match:"#Adjective and [#Gerund] !#Preposition?",group:0,tag:"Adjective",reason:"rude-and-x"},{match:"#Copula #Adverb? (over|under) [#PastTense]",group:0,tag:"Adjective",reason:"over-cooked"},{match:"#Copula #Adjective+ (and|or) [#PastTense]$",group:0,tag:"Adjective",reason:"bland-and-overcooked"},{match:"got #Adverb? [#PastTense] of",group:0,tag:"Adjective",reason:"got-tired-of"},{match:"(seem|seems|seemed|appear|appeared|appears|feel|feels|felt|sound|sounds|sounded) (#Adverb|#Adjective)? [#PastTense]",group:0,tag:"Adjective",reason:"felt-loved"},{match:"(seem|feel|seemed|felt) [#PastTense #Particle?]",group:0,tag:"Adjective",reason:"seem-confused"},{match:"a (bit|little|tad) [#PastTense #Particle?]",group:0,tag:"Adjective",reason:"a-bit-confused"},{match:"not be [%Adj|Past% #Particle?]",group:0,tag:"Adjective",reason:"do-not-be-confused"},{match:"#Copula just [%Adj|Past% #Particle?]",group:0,tag:"Adjective",reason:"is-just-right"},{match:"as [#Infinitive] as",group:0,tag:"Adjective",reason:"as-pale-as"},{match:"[%Adj|Past%] and #Adjective",group:0,tag:"Adjective",reason:"faled-and-oppressive"},{match:"or [#PastTense] #Noun",group:0,tag:"Adjective",notIf:"(#Copula|#Pronoun)",reason:"or-heightened-emotion"},{match:"(become|became|becoming|becomes) [#Verb]",group:0,tag:"Adjective",reason:"become-verb"},{match:"#Possessive [#PastTense] #Noun",group:0,tag:"Adjective",reason:"declared-intentions"},{match:"#Copula #Pronoun [%Adj|Present%]",group:0,tag:"Adjective",reason:"is-he-cool"},{match:"#Copula [%Adj|Past%] with",group:0,tag:"Adjective",notIf:"(associated|worn|baked|aged|armed|bound|fried|loaded|mixed|packed|pumped|filled|sealed)",reason:"is-crowded-with"},{match:"#Copula #Adverb? [%Adj|Present%]$",group:0,tag:"Adjective",reason:"was-empty$"}],[{match:"will (#Adverb|not)+? [have] (#Adverb|not)+? #Verb",group:0,tag:"Auxiliary",reason:"will-have-vb"},{match:"[#Copula] (#Adverb|not)+? (#Gerund|#PastTense)",group:0,tag:"Auxiliary",reason:"copula-walking"},{match:"[(#Modal|did)+] (#Adverb|not)+? #Verb",group:0,tag:"Auxiliary",reason:"modal-verb"},{match:"#Modal (#Adverb|not)+? [have] (#Adverb|not)+? [had] (#Adverb|not)+? #Verb",group:0,tag:"Auxiliary",reason:"would-have"},{match:"[(has|had)] (#Adverb|not)+? #PastTense",group:0,tag:"Auxiliary",reason:"had-walked"},{match:"[(do|does|did|will|have|had|has|got)] (not|#Adverb)+? #Verb",group:0,tag:"Auxiliary",reason:"have-had"},{match:"[about to] #Adverb? #Verb",group:0,tag:["Auxiliary","Verb"],reason:"about-to"},{match:"#Modal (#Adverb|not)+? [be] (#Adverb|not)+? #Verb",group:0,tag:"Auxiliary",reason:"would-be"},{match:"[(#Modal|had|has)] (#Adverb|not)+? [been] (#Adverb|not)+? #Verb",group:0,tag:"Auxiliary",reason:"had-been"},{match:"[(be|being|been)] #Participle",group:0,tag:"Auxiliary",reason:"being-driven"},{match:"[may] #Adverb? #Infinitive",group:0,tag:"Auxiliary",reason:"may-want"},{match:"#Copula (#Adverb|not)+? [(be|being|been)] #Adverb+? #PastTense",group:0,tag:"Auxiliary",reason:"being-walked"},{match:"will [be] #PastTense",group:0,tag:"Auxiliary",reason:"will-be-x"},{match:"[(be|been)] (#Adverb|not)+? #Gerund",group:0,tag:"Auxiliary",reason:"been-walking"},{match:"[used to] #PresentTense",group:0,tag:"Auxiliary",reason:"used-to-walk"},{match:"#Copula (#Adverb|not)+? [going to] #Adverb+? #PresentTense",group:0,tag:"Auxiliary",reason:"going-to-walk"},{match:"#Imperative [(me|him|her)]",group:0,tag:"Reflexive",reason:"tell-him"},{match:"(is|was) #Adverb? [no]",group:0,tag:"Negative",reason:"is-no"},{match:"[(been|had|became|came)] #PastTense",group:0,notIf:"#PhrasalVerb",tag:"Auxiliary",reason:"been-told"},{match:"[(being|having|getting)] #Verb",group:0,tag:"Auxiliary",reason:"being-born"},{match:"[be] #Gerund",group:0,tag:"Auxiliary",reason:"be-walking"},{match:"[better] #PresentTense",group:0,tag:"Modal",notIf:"(#Copula|#Gerund)",reason:"better-go"},{match:"even better",tag:"Adverb #Comparative",reason:"even-better"}],[{match:"(#Verb && @hasHyphen) up",tag:"PhrasalVerb",reason:"foo-up"},{match:"(#Verb && @hasHyphen) off",tag:"PhrasalVerb",reason:"foo-off"},{match:"(#Verb && @hasHyphen) over",tag:"PhrasalVerb",reason:"foo-over"},{match:"(#Verb && @hasHyphen) out",tag:"PhrasalVerb",reason:"foo-out"},{match:"[#Verb (in|out|up|down|off|back)] (on|in)",notIf:"#Copula",tag:"PhrasalVerb Particle",reason:"walk-in-on"},{match:"(lived|went|crept|go) [on] for",group:0,tag:"PhrasalVerb",reason:"went-on"},{match:"#Verb (up|down|in|on|for)$",tag:"PhrasalVerb #Particle",notIf:"#PhrasalVerb",reason:"come-down$"},{match:"help [(stop|end|make|start)]",group:0,tag:"Infinitive",reason:"help-stop"},{match:"#PhrasalVerb (in && #Particle) #Determiner",tag:"#Verb #Preposition #Determiner",unTag:"PhrasalVerb",reason:"work-in-the"},{match:"[(stop|start|finish|help)] #Gerund",group:0,tag:"Infinitive",reason:"start-listening"},{match:"#Verb (him|her|it|us|himself|herself|itself|everything|something) [(up|down)]",group:0,tag:"Adverb",reason:"phrasal-pronoun-advb"}],[{match:"^do not? [#Infinitive #Particle?]",notIf:lp,group:0,tag:"Imperative",reason:"do-eat"},{match:"^please do? not? [#Infinitive #Particle?]",group:0,tag:"Imperative",reason:"please-go"},{match:"^just do? not? [#Infinitive #Particle?]",group:0,tag:"Imperative",reason:"just-go"},{match:"^[#Infinitive] it #Comparative",notIf:lp,group:0,tag:"Imperative",reason:"do-it-better"},{match:"^[#Infinitive] it (please|now|again|plz)",notIf:lp,group:0,tag:"Imperative",reason:"do-it-please"},{match:"^[#Infinitive] (#Adjective|#Adverb)$",group:0,tag:"Imperative",notIf:"(so|such|rather|enough)",reason:"go-quickly"},{match:"^[#Infinitive] (up|down|over) #Determiner",group:0,tag:"Imperative",reason:"turn-down"},{match:"^[#Infinitive] (your|my|the|a|an|any|each|every|some|more|with|on)",group:0,notIf:"like",tag:"Imperative",reason:"eat-my-shorts"},{match:"^[#Infinitive] (him|her|it|us|me|there)",group:0,tag:"Imperative",reason:"tell-him"},{match:"^[#Infinitive] #Adjective #Noun$",group:0,tag:"Imperative",reason:"avoid-loud-noises"},{match:"^[#Infinitive] (#Adjective|#Adverb)? and #Infinitive",group:0,tag:"Imperative",reason:"call-and-reserve"},{match:"^(go|stop|wait|hurry) please?$",tag:"Imperative",reason:"go"},{match:"^(somebody|everybody) [#Infinitive]",group:0,tag:"Imperative",reason:"somebody-call"},{match:"^let (us|me) [#Infinitive]",group:0,tag:"Imperative",reason:"lets-leave"},{match:"^[(shut|close|open|start|stop|end|keep)] #Determiner #Noun",group:0,tag:"Imperative",reason:"shut-the-door"},{match:"^[#PhrasalVerb #Particle] #Determiner #Noun",group:0,tag:"Imperative",reason:"turn-off-the-light"},{match:"^[go] to .",group:0,tag:"Imperative",reason:"go-to-toronto"},{match:"^#Modal you [#Infinitive]",group:0,tag:"Imperative",reason:"would-you-"},{match:"^never [#Infinitive]",group:0,tag:"Imperative",reason:"never-stop"},{match:"^come #Infinitive",tag:"Imperative",notIf:"on",reason:"come-have"},{match:"^come and? #Infinitive",tag:"Imperative . Imperative",notIf:"#PhrasalVerb",reason:"come-and-have"},{match:"^stay (out|away|back)",tag:"Imperative",reason:"stay-away"},{match:"^[(stay|be|keep)] #Adjective",group:0,tag:"Imperative",reason:"stay-cool"},{match:"^[keep it] #Adjective",group:0,tag:"Imperative",reason:"keep-it-cool"},{match:"^do not [#Infinitive]",group:0,tag:"Imperative",reason:"do-not-be"},{match:"[#Infinitive] (yourself|yourselves)",group:0,tag:"Imperative",reason:"allow-yourself"},{match:"[#Infinitive] what .",group:0,tag:"Imperative",reason:"look-what"},{match:"^[#Infinitive] #Gerund",group:0,tag:"Imperative",reason:"keep-playing"},{match:"^[#Infinitive] (to|for|into|toward|here|there)",group:0,tag:"Imperative",reason:"go-to"},{match:"^[#Infinitive] (and|or) #Infinitive",group:0,tag:"Imperative",reason:"inf-and-inf"},{match:"^[%Noun|Verb%] to",group:0,tag:"Imperative",reason:"commit-to"},{match:"^[#Infinitive] #Adjective? #Singular #Singular",group:0,tag:"Imperative",reason:"maintain-eye-contact"},{match:"do not (forget|omit|neglect) to [#Infinitive]",group:0,tag:"Imperative",reason:"do-not-forget"},{match:"^[(ask|wear|pay|look|help|show|watch|act|fix|kill|stop|start|turn|try|win)] #Noun",group:0,tag:"Imperative",reason:"pay-attention"}],[{match:"(that|which) were [%Adj|Gerund%]",group:0,tag:"Gerund",reason:"that-were-growing"},{match:"#Gerund [#Gerund] #Plural",group:0,tag:"Adjective",reason:"hard-working-fam"}],[{match:"u r",tag:"#Pronoun #Copula",reason:"u r"},{match:"#Noun [(who|whom)]",group:0,tag:"Determiner",reason:"captain-who"},{match:"[had] #Noun+ #PastTense",group:0,tag:"Condition",reason:"had-he"},{match:"[were] #Noun+ to #Infinitive",group:0,tag:"Condition",reason:"were-he"},{match:"some sort of",tag:"Adjective Noun Conjunction",reason:"some-sort-of"},{match:"of some sort",tag:"Conjunction Adjective Noun",reason:"of-some-sort"},{match:"[such] (a|an|is)? #Noun",group:0,tag:"Determiner",reason:"such-skill"},{match:"[right] (before|after|in|into|to|toward)",group:0,tag:"#Adverb",reason:"right-into"},{match:"#Preposition [about]",group:0,tag:"Adjective",reason:"at-about"},{match:"(are|#Modal|see|do|for) [ya]",group:0,tag:"Pronoun",reason:"are-ya"},{match:"[long live] .",group:0,tag:"#Adjective #Infinitive",reason:"long-live"},{match:"[plenty] of",group:0,tag:"#Uncountable",reason:"plenty-of"},{match:"(always|nearly|barely|practically) [there]",group:0,tag:"Adjective",reason:"always-there"},{match:"[there] (#Adverb|#Pronoun)? #Copula",group:0,tag:"There",reason:"there-is"},{match:"#Copula [there] .",group:0,tag:"There",reason:"is-there"},{match:"#Modal #Adverb? [there]",group:0,tag:"There",reason:"should-there"},{match:"^[do] (you|we|they)",group:0,tag:"QuestionWord",reason:"do-you"},{match:"^[does] (he|she|it|#ProperNoun)",group:0,tag:"QuestionWord",reason:"does-he"},{match:"#Determiner #Noun+ [who] #Verb",group:0,tag:"Preposition",reason:"the-x-who"},{match:"#Determiner #Noun+ [which] #Verb",group:0,tag:"Preposition",reason:"the-x-which"},{match:"a [while]",group:0,tag:"Noun",reason:"a-while"},{match:"guess who",tag:"#Infinitive #QuestionWord",reason:"guess-who"},{match:"[fucking] !#Verb",group:0,tag:"#Gerund",reason:"f-as-gerund"}],[{match:"university of #Place",tag:"Organization",reason:"university-of-Foo"},{match:"#Noun (&|n) #Noun",tag:"Organization",reason:"Noun-&-Noun"},{match:"#Organization of the? #ProperNoun",tag:"Organization",reason:"org-of-place",safe:!0},{match:"#Organization #Country",tag:"Organization",reason:"org-country"},{match:"#ProperNoun #Organization",tag:"Organization",notIf:"#FirstName",reason:"titlecase-org"},{match:"#ProperNoun (ltd|co|inc|dept|assn|bros)",tag:"Organization",reason:"org-abbrv"},{match:"the [#Acronym]",group:0,tag:"Organization",reason:"the-acronym",safe:!0},{match:"government of the? [#Place+]",tag:"Organization",reason:"government-of-x"},{match:"(health|school|commerce) board",tag:"Organization",reason:"school-board"},{match:"(nominating|special|conference|executive|steering|central|congressional) committee",tag:"Organization",reason:"special-comittee"},{match:"(world|global|international|national|#Demonym) #Organization",tag:"Organization",reason:"global-org"},{match:"#Noun+ (public|private) school",tag:"School",reason:"noun-public-school"},{match:"#Place+ #SportsTeam",tag:"SportsTeam",reason:"place-sportsteam"},{match:"(dc|atlanta|minnesota|manchester|newcastle|sheffield) united",tag:"SportsTeam",reason:"united-sportsteam"},{match:"#Place+ fc",tag:"SportsTeam",reason:"fc-sportsteam"},{match:"#Place+ #Noun{0,2} (club|society|group|team|committee|commission|association|guild|crew)",tag:"Organization",reason:"place-noun-society"}],[{match:"(west|north|south|east|western|northern|southern|eastern)+ #Place",tag:"Region",reason:"west-norfolk"},{match:"#City [(al|ak|az|ar|ca|ct|dc|fl|ga|id|il|nv|nh|nj|ny|oh|pa|sc|tn|tx|ut|vt|pr)]",group:0,tag:"Region",reason:"us-state"},{match:"portland [or]",group:0,tag:"Region",reason:"portland-or"},{match:"#ProperNoun+ (cliff|place|range|pit|place|point|room|grounds|ruins)",tag:"Place",reason:"foo-point"},{match:"in [#ProperNoun] #Place",group:0,tag:"Place",reason:"propernoun-place"},{match:"#Value #Noun (st|street|rd|road|crescent|cr|way|tr|terrace|avenue|ave)",tag:"Address",reason:"address-st"},{match:"(port|mount|mt) #ProperName",tag:"Place",reason:"port-name"}],[{match:"[so] #Noun",group:0,tag:"Conjunction",reason:"so-conj"},{match:"[(who|what|where|why|how|when)] #Noun #Copula #Adverb? (#Verb|#Adjective)",group:0,tag:"Conjunction",reason:"how-he-is-x"},{match:"#Copula [(who|what|where|why|how|when)] #Noun",group:0,tag:"Conjunction",reason:"when-he"},{match:"#Verb [that] #Pronoun",group:0,tag:"Conjunction",reason:"said-that-he"},{match:"#Noun [that] #Copula",group:0,tag:"Conjunction",reason:"that-are"},{match:"#Noun [that] #Verb #Adjective",group:0,tag:"Conjunction",reason:"that-seem"},{match:"#Noun #Copula not? [that] #Adjective",group:0,tag:"Adverb",reason:"that-adj"},{match:"#Verb #Adverb? #Noun [(that|which)]",group:0,tag:"Preposition",reason:"that-prep"},{match:"@hasComma [which] (#Pronoun|#Verb)",group:0,tag:"Preposition",reason:"which-copula"},{match:"#Noun [like] #Noun",group:0,tag:"Preposition",reason:"noun-like"},{match:"^[like] #Determiner",group:0,tag:"Preposition",reason:"like-the"},{match:"a #Noun [like] (#Noun|#Determiner)",group:0,tag:"Preposition",reason:"a-noun-like"},{match:"#Adverb [like]",group:0,tag:"Verb",reason:"really-like"},{match:"(not|nothing|never) [like]",group:0,tag:"Preposition",reason:"nothing-like"},{match:"#Infinitive #Pronoun [like]",group:0,tag:"Preposition",reason:"treat-them-like"},{match:"[#QuestionWord] (#Pronoun|#Determiner)",group:0,tag:"Preposition",reason:"how-he"},{match:"[#QuestionWord] #Participle",group:0,tag:"Preposition",reason:"when-stolen"},{match:"[how] (#Determiner|#Copula|#Modal|#PastTense)",group:0,tag:"QuestionWord",reason:"how-is"},{match:"#Plural [(who|which|when)] .",group:0,tag:"Preposition",reason:"people-who"}],[{match:"holy (shit|fuck|hell)",tag:"Expression",reason:"swears-expression"},{match:"^[(well|so|okay|now)] !#Adjective?",group:0,tag:"Expression",reason:"well-"},{match:"^come on",tag:"Expression",reason:"come-on"},{match:"(say|says|said) [sorry]",group:0,tag:"Expression",reason:"say-sorry"},{match:"^(ok|alright|shoot|hell|anyways)",tag:"Expression",reason:"ok-"},{match:"^(say && @hasComma)",tag:"Expression",reason:"say-"},{match:"^(like && @hasComma)",tag:"Expression",reason:"like-"},{match:"^[(dude|man|girl)] #Pronoun",group:0,tag:"Expression",reason:"dude-i"}]),lf=null,lb=e=>Math.round(100*e)/100;var lv=function(e,t){let n=Object.keys(t.hooks).filter(e=>!e.startsWith("#")&&!e.startsWith("%"));if(0===n.length)return e;e._cache||e.cache();let r=e._cache;return e.filter((e,t)=>n.some(e=>r[t].has(e)))};let ly=function(e,t){let n=(0,e.methods.two.transform.verb.conjugate)(t,e.model);return e.has("#Gerund")?n.Gerund:e.has("#PastTense")?n.PastTense:e.has("#PresentTense")?n.PresentTense:e.has("#Gerund")?n.Gerund:t};var lw=function(e,t){let n=t;return e.forEach(e=>{e.has("#Infinitive")||(n=ly(e,t)),e.replaceWith(n)}),e};let lk=function(e,t){let n=t;e.has("#Plural")&&(n=(0,e.methods.two.transform.noun.toPlural)(t,e.model)),e.replaceWith(n,{possessives:!0})},lP=function(e,t){let{toAdverb:n}=e.methods.two.transform.adjective,r=n(t);r&&e.replaceWith(r)},lA=function(e,t){let{toComparative:n,toSuperlative:r}=e.methods.two.transform.adjective,a=t;e.has("#Comparative")?a=n(a,e.model):e.has("#Superlative")&&(a=r(a,e.model)),a&&e.replaceWith(a)};var lx=function(e,t,n){let r=e.split(/ /g).map(e=>e.toLowerCase().trim());r=(r=r.filter(e=>e)).map(e=>`{${e}}`).join(" ");let a=this.match(r);return(n&&(a=a.if(n)),a.has("#Verb"))?lw(a,t):a.has("#Noun")?lk(a,t):a.has("#Adverb")?lP(a,t):a.has("#Adjective")?lA(a,t):this};y.plugin({compute:{preTagger:function(e){let{methods:t,model:n,world:r}=e,a=e.docs;s$(a,n,r);let i=t.two.quickSplit(a);for(let e=0;e<i.length;e+=1){let t=i[e],a=sS(t);sL(t,n,r,a),sK(t,n,r,a)}return i},root:function(e){let t=e.world,n=Object.keys(sJ);e.docs.forEach(e=>{for(let r=0;r<e.length;r+=1){let a=e[r];for(let e=0;e<n.length;e+=1)if(a.tags.has(n[e])){let r=(0,sJ[n[e]])(a,t);a.normal!==r&&(a.root=r);break}}})},penn:function(e){e.compute("tagRank"),e.docs.forEach(e=>{e.forEach(e=>{e.penn=sq(e)})})}},methods:oj,model:oF,tags:sU,hooks:["preTagger"]}),y.plugin({compute:{contractionTwo:e=>{let{world:t,document:n}=e;n.forEach((r,a)=>{for(let i=r.length-1;i>=0;i-=1){if(r[i].implicit)continue;let o=null;!0===ll.test(r[i].normal)&&(o=r[i].normal.split(ll)[1]);let s=null;if(lh.hasOwnProperty(o)&&(s=lh[o](r,i,t)),s){sX(n,[a,i],s=ld(s,e)),lc(n[a],e,i,s.length);continue}}})}},api:function(e){class t extends e{constructor(e,t,n){super(e,t,n),this.viewType="Contraction"}expand(){return this.docs.forEach(e=>{let t=sY.test(e[0].text);e.forEach((t,n)=>{t.text=t.implicit||"",delete t.implicit,n<e.length-1&&""===t.post&&(t.post+=" "),t.dirty=!0}),t&&(e[0].text=function(e=""){return e=e.replace(/^ *[a-z\u00C0-\u00FF]/,e=>e.toUpperCase())}(e[0].text))}),this.compute("normal"),this}}e.prototype.contractions=function(){let e=this.match("@hasContraction+");return new t(this.document,e.pointer)},e.prototype.contract=sZ},hooks:["contractionTwo"]}),y.plugin({api:function(e){e.prototype.confidence=function(){let e=0,t=0;return(this.docs.forEach(n=>{n.forEach(n=>{t+=1,e+=n.confidence||1})}),0===t)?1:lb(e/t)},e.prototype.tagger=function(){return this.compute(["tagger"])}},compute:{postTagger:function(e){let{world:t}=e,{model:n,methods:r}=t;lf=lf||r.one.buildNet(n.two.matches,t);let a=r.two.quickSplit(e.document).map(e=>{let t=e[0];return[t.index[0],t.index[1],t.index[1]+e.length]}),i=e.update(a);return i.cache(),i.sweep(lf),e.uncache(),e.unfreeze(),e},tagger:e=>e.compute(["freeze","lexicon","preTagger","postTagger","unfreeze"])},model:{two:{matches:lm}},hooks:["postTagger"]}),y.plugin({lib:{lazy:function(e,t){let n=t;"string"==typeof t&&(n=this.buildNet([{match:t}]));let r=this.tokenize(e),a=lv(r,n);return a.found?(a.compute(["index","tagger"]),a.match(t)):r.none()}}}),y.plugin({api:function(e){e.prototype.swap=lx}});let lC=function(e){let{fromComparative:t,fromSuperlative:n}=e.methods.two.transform.adjective,r=e.text("normal");return e.has("#Comparative")?t(r,e.model):e.has("#Superlative")?n(r,e.model):r},lj=function(e){let t=e.match("@hasComma");return t=t.filter(e=>{if(1===e.growLeft(".").wordCount()||1===e.growRight(". .").wordCount())return!1;let t=e.grow(".");return(t=(t=(t=(t=(t=(t=t.ifNo("@hasComma @hasComma")).ifNo("@hasComma (and|or) .")).ifNo("(#City && @hasComma) #Country")).ifNo("(#WeekDay && @hasComma) #Date")).ifNo("(#Date+ && @hasComma) #Value")).ifNo("(#Adjective && @hasComma) #Adjective")).found}),e.splitAfter(t)},lN=function(e){let t=e.parentheses();return t=t.filter(e=>e.wordCount()>=3&&e.has("#Verb")&&e.has("#Noun")),e.splitOn(t)},lI=function(e){let t=e.quotations();return t=t.filter(e=>e.wordCount()>=3&&e.has("#Verb")&&e.has("#Noun")),e.splitOn(t)};var lD=function(e){let t=this,n=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=lj(t=lI(t=lN(t)))).splitAfter("(@hasEllipses|@hasSemicolon|@hasDash|@hasColon)")).splitAfter("^#Pronoun (said|says)")).splitBefore("(said|says) #ProperNoun$")).splitBefore(". . if .{4}")).splitBefore("and while")).splitBefore("now that")).splitBefore("ever since")).splitBefore("(supposing|although)")).splitBefore("even (while|if|though)")).splitBefore("(whereas|whose)")).splitBefore("as (though|if)")).splitBefore("(til|until)")).match("#Verb .* [but] .* #Verb",0);n.found&&(t=t.splitBefore(n));let r=t.if("if .{2,9} then .").match("then");return t=t.splitBefore(r),"number"==typeof e&&(t=t.get(e)),t},lT=function(e){let t=[],n=null;return e.clauses().docs.forEach(e=>{e.forEach(e=>{e.chunk&&e.chunk===n?t[t.length-1][2]=e.index[1]+1:(n=e.chunk,t.push([e.index[0],e.index[1],e.index[1]+1]))}),n=null}),e.update(t)};let lO={this:"Noun",then:"Pivot"};var lH=function(e){for(let t=0;t<e.length;t+=1)for(let n=0;n<e[t].length;n+=1){let r=e[t][n];if(!0===lO.hasOwnProperty(r.normal)){r.chunk=lO[r.normal];continue}if(r.tags.has("Verb")){r.chunk="Verb";continue}if(r.tags.has("Noun")||r.tags.has("Determiner")||r.tags.has("Value")){r.chunk="Noun";continue}if(r.tags.has("QuestionWord")){r.chunk="Pivot";continue}}},lE=function(e){for(let t=0;t<e.length;t+=1)for(let n=0;n<e[t].length;n+=1){let r=e[t][n];if(r.chunk)continue;let a=e[t][n+1],i=e[t][n-1];if(r.tags.has("Adjective")){if(i&&i.tags.has("Copula")){r.chunk="Adjective";continue}if(i&&i.tags.has("Determiner")){r.chunk="Noun";continue}a&&a.tags.has("Noun")&&(r.chunk="Noun");continue}if(r.tags.has("Adverb")||r.tags.has("Negative")){if(i&&i.tags.has("Adjective")){r.chunk="Adjective";continue}if(i&&i.tags.has("Verb")){r.chunk="Verb";continue}if(a&&a.tags.has("Adjective")){r.chunk="Adjective";continue}if(a&&a.tags.has("Verb")){r.chunk="Verb";continue}}}};let lF=[{match:"[that] #Determiner #Noun",group:0,chunk:"Pivot"},{match:"#PastTense [that]",group:0,chunk:"Pivot"},{match:"[so] #Determiner",group:0,chunk:"Pivot"},{match:"#Copula #Adverb+? [#Adjective]",group:0,chunk:"Adjective"},{match:"#Adjective and #Adjective",chunk:"Adjective"},{match:"#Adverb+ and #Adverb #Verb",chunk:"Verb"},{match:"#Gerund #Adjective$",chunk:"Verb"},{match:"#Gerund to #Verb",chunk:"Verb"},{match:"#PresentTense and #PresentTense",chunk:"Verb"},{match:"#Adverb #Negative",chunk:"Verb"},{match:"(want|wants|wanted) to #Infinitive",chunk:"Verb"},{match:"#Verb #Reflexive",chunk:"Verb"},{match:"#Verb [to] #Adverb? #Infinitive",group:0,chunk:"Verb"},{match:"[#Preposition] #Gerund",group:0,chunk:"Verb"},{match:"#Infinitive [that] <Noun>",group:0,chunk:"Verb"},{match:"#Noun of #Determiner? #Noun",chunk:"Noun"},{match:"#Value+ #Adverb? #Adjective",chunk:"Noun"},{match:"the [#Adjective] #Noun",chunk:"Noun"},{match:"#Singular in #Determiner? #Singular",chunk:"Noun"},{match:"#Plural [in] #Determiner? #Noun",group:0,chunk:"Pivot"},{match:"#Noun and #Determiner? #Noun",notIf:"(#Possessive|#Pronoun)",chunk:"Noun"}],lG=null;var lV=function(e,t,n){let{methods:r}=n;lG=lG||r.one.buildNet(lF,n),e.sweep(lG)},lz=n(20357);let lM=function(e,t){if((void 0!==lz&&lz.env?lz.env:self.env||{}).DEBUG_CHUNKS){let n=(e.normal+"'").padEnd(8);console.log(`  | '${n}  →  \x1b[34m${t.padEnd(12)}\x1b[0m \x1b[2m -fallback- \x1b[0m`)}e.chunk=t};var lB=function(e){for(let t=0;t<e.length;t+=1)for(let n=0;n<e[t].length;n+=1){let r=e[t][n];void 0===r.chunk&&(r.tags.has("Conjunction")?lM(r,"Pivot"):r.tags.has("Preposition")?lM(r,"Pivot"):r.tags.has("Adverb")?lM(r,"Verb"):r.chunk="Noun")}},lS=function(e){let t=[],n=null;e.forEach(e=>{for(let r=0;r<e.length;r+=1){let a=e[r];n&&a.chunk===n?t[t.length-1].terms.push(a):(t.push({chunk:a.chunk,terms:[a]}),n=a.chunk)}}),t.forEach(e=>{"Verb"!==e.chunk||e.terms.find(e=>e.tags.has("Verb"))||e.terms.forEach(e=>e.chunk=null)})};let l$=/\./g;var lL=function(e){class t extends e{constructor(e,t,n){super(e,t,n),this.viewType="Acronyms"}strip(){return this.docs.forEach(e=>{e.forEach(e=>{e.text=e.text.replace(l$,""),e.normal=e.normal.replace(l$,"")})}),this}addPeriods(){return this.docs.forEach(e=>{e.forEach(e=>{e.text=e.text.replace(l$,""),e.normal=e.normal.replace(l$,""),e.text=e.text.split("").join(".")+".",e.normal=e.normal.split("").join(".")+"."})}),this}}e.prototype.acronyms=function(e){let n=this.match("#Acronym");return new t((n=n.getNth(e)).document,n.pointer)}};let lK=/\(/,lJ=/\)/,lW=function(e,t){for(;t<e.length;t+=1)if(e[t].post&&lJ.test(e[t].post)){let[,n]=e[t].index;return n||0}return null},lq=function(e){let t=[];return e.docs.forEach(e=>{for(let n=0;n<e.length;n+=1){let r=e[n];if(r.pre&&lK.test(r.pre)){let r=lW(e,n);if(null!==r){let[a,i]=e[n].index;t.push([a,i,r+1,e[n].id]),n=r}}}}),e.update(t)};var lR=function(e){class t extends e{constructor(e,t,n){super(e,t,n),this.viewType="Possessives"}strip(){return this.docs.forEach(e=>{e[0].pre=e[0].pre.replace(lK,"");let t=e[e.length-1];t.post=t.post.replace(lJ,"")}),this}}e.prototype.parentheses=function(e){let n=lq(this);return new t((n=n.getNth(e)).document,n.pointer)}};let lU=/'s$/,l_=function(e){let t=e.match("#Possessive+");return t.has("#Person")&&(t=t.growLeft("#Person+")),t.has("#Place")&&(t=t.growLeft("#Place+")),t.has("#Organization")&&(t=t.growLeft("#Organization+")),t};var lQ=function(e){class t extends e{constructor(e,t,n){super(e,t,n),this.viewType="Possessives"}strip(){return this.docs.forEach(e=>{e.forEach(e=>{e.text=e.text.replace(lU,""),e.normal=e.normal.replace(lU,"")})}),this}}e.prototype.possessives=function(e){let n=l_(this);return new t((n=n.getNth(e)).document,n.pointer)}};let lZ={'"':'"',"＂":"＂","'":"'","“":"”","‘":"’","‟":"”","‛":"’","„":"”","⹂":"”","‚":"’","\xab":"\xbb","‹":"›","‵":"′","‶":"″","‷":"‴","〝":"〞","`":"\xb4","〟":"〞"},lY=RegExp("["+Object.keys(lZ).join("")+"]"),lX=RegExp("["+Object.values(lZ).join("")+"]"),l0=function(e,t){let n=e[t].pre.match(lY)[0]||"";if(!n||!lZ[n])return null;let r=lZ[n];for(;t<e.length;t+=1)if(e[t].post&&e[t].post.match(r))return t;return null},l1=function(e){let t=[];return e.docs.forEach(e=>{for(let n=0;n<e.length;n+=1){let r=e[n];if(r.pre&&lY.test(r.pre)){let r=l0(e,n);if(null!==r){let[a,i]=e[n].index;t.push([a,i,r+1,e[n].id]),n=r}}}}),e.update(t)},l2=function(e){e.docs.forEach(e=>{e[0].pre=e[0].pre.replace(lY,"");let t=e[e.length-1];t.post=t.post.replace(lX,"")})};var l3=function(e){class t extends e{constructor(e,t,n){super(e,t,n),this.viewType="Possessives"}strip(){return l2(this)}}e.prototype.quotations=function(e){let n=l1(this);return new t((n=n.getNth(e)).document,n.pointer)}};let l5=function(e){let t=this.splitAfter("@hasComma");return(t=t.match("#PhoneNumber+")).getNth(e)},l4=[["hyphenated","@hasHyphen ."],["hashTags","#HashTag"],["emails","#Email"],["emoji","#Emoji"],["emoticons","#Emoticon"],["atMentions","#AtMention"],["urls","#Url"],["conjunctions","#Conjunction"],["prepositions","#Preposition"],["abbreviations","#Abbreviation"],["honorifics","#Honorific"]],l6=[["emojis","emoji"],["atmentions","atMentions"]];var l9=function(e){l4.forEach(t=>{e.prototype[t[0]]=function(e){let n=this.match(t[1]);return"number"==typeof e?n.get(e):n}}),e.prototype.phoneNumbers=l5,l6.forEach(t=>{e.prototype[t[0]]=e.prototype[t[1]]})};let l7=/\//;var l8=function(e){class t extends e{constructor(e,t,n){super(e,t,n),this.viewType="Slashes"}split(){return this.map(e=>{let t=e.text().split(l7);return(e=e.replaceWith(t.join(" "))).growRight("("+t.join("|")+")+")})}}e.prototype.slashes=function(e){let n=this.match("#SlashedTerm");return new t((n=n.getNth(e)).document,n.pointer)}};let ue=function(e,t){e.docs.forEach(e=>{e.forEach(t)})};var ut={case:e=>{ue(e,e=>{e.text=e.text.toLowerCase()})},unicode:e=>{let t=e.world,n=t.methods.one.killUnicode;ue(e,e=>e.text=n(e.text,t))},whitespace:e=>{ue(e,e=>{e.post=e.post.replace(/\s+/g," "),e.post=e.post.replace(/\s([.,?!:;])/g,"$1"),e.pre=e.pre.replace(/\s+/g,"")})},punctuation:e=>{ue(e,e=>{e.post=e.post.replace(/[–—-]/g," "),e.post=e.post.replace(/[,:;]/g,""),e.post=e.post.replace(/\.{2,}/g,""),e.post=e.post.replace(/\?{2,}/g,"?"),e.post=e.post.replace(/!{2,}/g,"!"),e.post=e.post.replace(/\?!+/g,"?")});let t=e.docs,n=t[t.length-1];if(n&&n.length>0){let e=n[n.length-1];e.post=e.post.replace(/ /g,"")}},contractions:e=>{e.contractions().expand()},acronyms:e=>{e.acronyms().strip()},parentheses:e=>{e.parentheses().strip()},possessives:e=>{e.possessives().strip()},quotations:e=>{e.quotations().strip()},emoji:e=>{e.emojis().remove()},honorifics:e=>{e.match("#Honorific+ #Person").honorifics().remove()},adverbs:e=>{e.adverbs().remove()},nouns:e=>{e.nouns().toSingular()},verbs:e=>{e.verbs().toInfinitive()},numbers:e=>{e.numbers().toNumber()},debullet:e=>{let t=/^\s*([-–—*•])\s*$/;return e.docs.forEach(e=>{t.test(e[0].pre)&&(e[0].pre=e[0].pre.replace(t,""))}),e}};let un=e=>e.split("|").reduce((e,t)=>(e[t]=!0,e),{}),ur="unicode|punctuation|whitespace|acronyms",ua="|case|contractions|parentheses|quotations|emoji|honorifics|debullet",ui={light:un(ur),medium:un(ur+ua),heavy:un(ur+ua+"|possessives|adverbs|nouns|verbs")};var uo=function(e){let t=e.clauses().match("<Noun>"),n=t.match("@hasComma");return(n=n.not("#Place")).found&&(t=t.splitAfter(n)),t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.splitOn("#Expression")).splitOn("(he|she|we|you|they|i)")).splitOn("(#Noun|#Adjective) [(he|him|she|it)]",0)).splitOn("[(he|him|she|it)] (#Determiner|#Value)",0)).splitBefore("#Noun [(the|a|an)] #Adjective? #Noun",0)).splitOn("[(here|there)] #Noun",0)).splitOn("[#Noun] (here|there)",0)).splitBefore("(our|my|their|your)")).splitOn("#Noun [#Determiner]",0)).if("#Noun")};let us=["after","although","as if","as long as","as","because","before","even if","even though","ever since","if","in order that","provided that","since","so that","than","that","though","unless","until","what","whatever","when","whenever","where","whereas","wherever","whether","which","whichever","who","whoever","whom","whomever","whose"];var ul=function(e){if(e.before("#Preposition$").found)return!0;if(!e.before().found)return!1;for(let t=0;t<us.length;t+=1)if(e.has(us[t]))return!0;return!1},uu=function(e,t){if(e.has("#Plural")||e.has("#Noun and #Noun")||e.has("(we|they)"))return!0;if(!0===t.has("(#Pronoun|#Place|#Value|#Person|#Uncountable|#Month|#WeekDay|#Holiday|#Possessive)")||e.has("#Singular"))return!1;let n=t.text("normal");return n.length>3&&n.endsWith("s")&&!n.endsWith("ss")};let uc=function(e){let t=e.clone();return(t=(t=(t=(t=t.match("#Noun+")).remove("(#Adjective|#Preposition|#Determiner|#Value)")).not("#Possessive")).first()).found?t:e};var uh=function(e){let t=uc(e);return{determiner:e.match("#Determiner").eq(0),adjectives:e.match("#Adjective"),number:e.values(),isPlural:uu(e,t),isSubordinate:ul(e),root:t}};let ud=e=>e.text(),ug=e=>e.json({terms:!1,normal:!0}).map(e=>e.normal),up=function(e){if(!e.found)return null;let t=e.values(0);return t.found?(t.parse()[0]||{}).num:null};var um=function(e){let t=uh(e);return{root:ud(t.root),number:up(t.number),determiner:ud(t.determiner),adjectives:ug(t.adjectives),isPlural:t.isPlural,isSubordinate:t.isSubordinate}},uf=function(e){return!e.has("^(#Uncountable|#ProperNoun|#Place|#Pronoun|#Acronym)+$")};let ub={tags:!0};var uv=function(e,t){if(!0===t.isPlural||(t.root.has("#Possessive")&&(t.root=t.root.possessives().strip()),!uf(t.root)))return e;let{methods:n,model:r}=e.world,{toPlural:a}=n.two.transform.noun,i=a(t.root.text({keepPunct:!1}),r);e.match(t.root).replaceWith(i,ub).tag("Plural","toPlural"),t.determiner.has("(a|an)")&&e.remove(t.determiner);let o=t.root.after("not? #Adverb+? [#Copula]",0);return o.found&&(o.has("is")?e.replace(o,"are"):o.has("was")&&e.replace(o,"were")),e};let uy={tags:!0};var uw=function(e,t){if(!1===t.isPlural)return e;let{methods:n,model:r}=e.world,{toSingular:a}=n.two.transform.noun,i=a(t.root.text("normal"),r);return e.replace(t.root,i,uy).tag("Singular","toPlural"),e},uk=function(e,t){let n=e.match("#Fraction+");return n=(n=n.filter(e=>!e.lookBehind("#Value and$").found)).notIf("#Value seconds"),"number"==typeof t&&(n=n.eq(t)),n},uP=e=>{let t=[{reg:/^(minus|negative)[\s-]/i,mult:-1},{reg:/^(a\s)?half[\s-](of\s)?/i,mult:.5}];for(let n=0;n<t.length;n++)if(!0===t[n].reg.test(e))return{amount:t[n].mult,str:e.replace(t[n].reg,"")};return{amount:1,str:e}},uA={ones:{zeroth:0,first:1,second:2,third:3,fourth:4,fifth:5,sixth:6,seventh:7,eighth:8,ninth:9,zero:0,one:1,two:2,three:3,four:4,five:5,six:6,seven:7,eight:8,nine:9},teens:{tenth:10,eleventh:11,twelfth:12,thirteenth:13,fourteenth:14,fifteenth:15,sixteenth:16,seventeenth:17,eighteenth:18,nineteenth:19,ten:10,eleven:11,twelve:12,thirteen:13,fourteen:14,fifteen:15,sixteen:16,seventeen:17,eighteen:18,nineteen:19},tens:{twentieth:20,thirtieth:30,fortieth:40,fourtieth:40,fiftieth:50,sixtieth:60,seventieth:70,eightieth:80,ninetieth:90,twenty:20,thirty:30,forty:40,fourty:40,fifty:50,sixty:60,seventy:70,eighty:80,ninety:90},multiples:{hundredth:100,thousandth:1e3,millionth:1e6,billionth:1e9,trillionth:1e12,quadrillionth:1e15,quintillionth:1e18,sextillionth:1e21,septillionth:1e24,hundred:100,thousand:1e3,million:1e6,billion:1e9,trillion:1e12,quadrillion:1e15,quintillion:1e18,sextillion:1e21,septillion:1e24,grand:1e3}},ux=(e,t)=>{if(uA.ones.hasOwnProperty(e)){if(t.ones||t.teens)return!1}else if(uA.teens.hasOwnProperty(e)){if(t.ones||t.teens||t.tens)return!1}else if(uA.tens.hasOwnProperty(e)&&(t.ones||t.teens||t.tens))return!1;return!0},uC=function(e){let t="0.";for(let n=0;n<e.length;n++){let r=e[n];if(!0===uA.ones.hasOwnProperty(r))t+=uA.ones[r];else if(!0===uA.teens.hasOwnProperty(r))t+=uA.teens[r];else if(!0===uA.tens.hasOwnProperty(r))t+=uA.tens[r];else{if(!0!==/^[0-9]$/.test(r))return 0;t+=r}}return parseFloat(t)},uj=e=>e=(e=(e=(e=(e=(e=(e=(e=e.replace(/1st$/,"1")).replace(/2nd$/,"2")).replace(/3rd$/,"3")).replace(/([4567890])r?th$/,"$1")).replace(/^[$€¥£¢]/,"")).replace(/[%$€¥£¢]$/,"")).replace(/,/g,"")).replace(/([0-9])([a-z\u00C0-\u00FF]{1,2})$/,"$1");let uN=/^([0-9,. ]+)\/([0-9,. ]+)$/,uI={"a few":3,"a couple":2,"a dozen":12,"two dozen":24,zero:0},uD=e=>Object.keys(e).reduce((t,n)=>t+=e[n],0);var uT=function(e){if(!0===uI.hasOwnProperty(e))return uI[e];if("a"===e||"an"===e)return 1;let t=uP(e);e=t.str;let n=null,r={},a=0,i=!1,o=e.split(/[ -]/);for(let e=0;e<o.length;e++){let s=o[e];if(!(s=uj(s))||"and"===s)continue;if("-"===s||"negative"===s){i=!0;continue}if("-"===s.charAt(0)&&(i=!0,s=s.substring(1)),"point"===s)return a+=uD(r)+uC(o.slice(e+1,o.length)),a*=t.amount;let l=s.match(uN);if(l){let e=parseFloat(l[1].replace(/[, ]/g,"")),t=parseFloat(l[2].replace(/[, ]/g,""));t&&(a+=e/t||0);continue}if(uA.tens.hasOwnProperty(s)&&r.ones&&1===Object.keys(r).length&&(a=100*r.ones,r={}),!1===ux(s,r))return null;if(/^[0-9.]+$/.test(s))r.ones=parseFloat(s);else if(!0===uA.ones.hasOwnProperty(s))r.ones=uA.ones[s];else if(!0===uA.teens.hasOwnProperty(s))r.teens=uA.teens[s];else if(!0===uA.tens.hasOwnProperty(s))r.tens=uA.tens[s];else if(!0===uA.multiples.hasOwnProperty(s)){let t=uA.multiples[s];if(t===n)return null;if(100===t&&void 0!==o[e+1]){let n=o[e+1];uA.multiples[n]&&(t*=uA.multiples[n],e+=1)}null===n||t<n?(a+=(uD(r)||1)*t,n=t):(a+=uD(r),n=t,a=(a||1)*t),r={}}}return(a+=uD(r),0==(a*=t.amount*(i?-1:1))&&0===Object.keys(r).length)?null:a};let uO=/s$/,uH=function(e){return uT(e.text("reduced"))},uE={half:2,halve:2,quarter:4},uF=function(e){let t=e.text("reduced").match(/^([-+]?[0-9]+)\/([-+]?[0-9]+)(st|nd|rd|th)?s?$/);return t&&t[1]&&t[0]?{numerator:Number(t[1]),denominator:Number(t[2])}:null},uG=function(e){let t=e.match("[<num>#Value+] out of every? [<den>#Value+]");if(!0!==t.found)return null;let{num:n,den:r}=t.groups();return n&&r&&(n=uH(n),r=uH(r),n&&r&&"number"==typeof n&&"number"==typeof r)?{numerator:n,denominator:r}:null},uV=function(e){let t=e.match("[<num>(#Cardinal|a)+] [<den>#Fraction+]");if(!0!==t.found)return null;let{num:n,den:r}=t.groups();n=n.has("a")?1:uH(n);let a=r.text("reduced");return(uO.test(a)&&(a=a.replace(uO,""),r=r.replaceWith(a)),r=uE.hasOwnProperty(a)?uE[a]:uH(r),"number"==typeof n&&"number"==typeof r)?{numerator:n,denominator:r}:null},uz=function(e){let t=e.match("^#Ordinal$");return!0!==t.found?null:e.lookAhead("^of .")?{numerator:1,denominator:uH(t)}:null},uM=function(e){let t=e.text("reduced");return uE.hasOwnProperty(t)?{numerator:1,denominator:uE[t]}:null},uB=e=>{let t=Math.round(1e3*e)/1e3;return 0===t&&0!==e?e:t};var uS=function(e){let t=uM(e=e.clone())||uF(e)||uG(e)||uV(e)||uz(e)||null;return null!==t&&t.numerator&&t.denominator&&(t.decimal=t.numerator/t.denominator,t.decimal=uB(t.decimal)),t},u$=function(e){let t;return e<1e6?String(e):-1===(t="number"==typeof e?e.toFixed(0):e).indexOf("e+")?t:t.replace(".","").split("e+").reduce(function(e,t){return e+Array(t-e.length+2).join(0)})};let uL=[["ninety",90],["eighty",80],["seventy",70],["sixty",60],["fifty",50],["forty",40],["thirty",30],["twenty",20]],uK=["","one","two","three","four","five","six","seven","eight","nine","ten","eleven","twelve","thirteen","fourteen","fifteen","sixteen","seventeen","eighteen","nineteen"],uJ=[[1e24,"septillion"],[1e20,"hundred sextillion"],[1e21,"sextillion"],[1e20,"hundred quintillion"],[1e18,"quintillion"],[1e17,"hundred quadrillion"],[1e15,"quadrillion"],[1e14,"hundred trillion"],[1e12,"trillion"],[1e11,"hundred billion"],[1e9,"billion"],[1e8,"hundred million"],[1e6,"million"],[1e5,"hundred thousand"],[1e3,"thousand"],[100,"hundred"],[1,"one"]],uW=function(e){let t=e,n=[];return uJ.forEach(r=>{if(e>=r[0]){let e=Math.floor(t/r[0]);t-=e*r[0],e&&n.push({unit:r[1],count:e})}}),n},uq=function(e){let t=[];if(e>100)return t;for(let n=0;n<uL.length;n++)e>=uL[n][1]&&(e-=uL[n][1],t.push(uL[n][0]));return uK[e]&&t.push(uK[e]),t},uR=e=>{let t=["zero","one","two","three","four","five","six","seven","eight","nine"],n=[],r=u$(e).match(/\.([0-9]+)/);if(!r||!r[0])return n;n.push("point");let a=r[0].split("");for(let e=0;e<a.length;e++)n.push(t[a[e]]);return n};var uU=function(e){let t=e.num;if(0===t||"0"===t)return"zero";t>1e21&&(t=u$(t));let n=[];t<0&&(n.push("minus"),t=Math.abs(t));let r=uW(t);for(let e=0;e<r.length;e++){let t=r[e].unit;"one"===t&&(t="",n.length>1&&n.push("and")),(n=n.concat(uq(r[e].count))).push(t)}return 0===(n=(n=n.concat(uR(t))).filter(e=>e)).length&&(n[0]=""),n.join(" ")},u_=function(e){if(!e.numerator||!e.denominator)return"";let t=uU({num:e.numerator}),n=uU({num:e.denominator});return`${t} out of ${n}`};let uQ={one:"first",two:"second",three:"third",five:"fifth",eight:"eighth",nine:"ninth",twelve:"twelfth",twenty:"twentieth",thirty:"thirtieth",forty:"fortieth",fourty:"fourtieth",fifty:"fiftieth",sixty:"sixtieth",seventy:"seventieth",eighty:"eightieth",ninety:"ninetieth"};var uZ=e=>{let t=uU(e).split(" "),n=t[t.length-1];return uQ.hasOwnProperty(n)?t[t.length-1]=uQ[n]:t[t.length-1]=n.replace(/y$/,"i")+"th",t.join(" ")},uY=function(e){if(!e.numerator||!e.denominator)return"";let t=uU({num:e.numerator}),n=uZ({num:e.denominator});return(2===e.denominator&&(n="half"),t&&n)?(1!==e.numerator&&(n+="s"),`${t} ${n}`):""},uX=function(e){class t extends e{constructor(e,t,n){super(e,t,n),this.viewType="Fractions"}parse(e){return this.getNth(e).map(uS)}get(e){return this.getNth(e).map(uS)}json(e){return this.getNth(e).map(t=>{let n=t.toView().json(e)[0],r=uS(t);return n.fraction=r,n},[])}toDecimal(e){return this.getNth(e).forEach(e=>{let{decimal:t}=uS(e);(e=e.replaceWith(String(t),!0)).tag("NumericValue"),e.unTag("Fraction")}),this}toFraction(e){return this.getNth(e).forEach(e=>{let t=uS(e);if(t&&"number"==typeof t.numerator&&"number"==typeof t.denominator){let n=`${t.numerator}/${t.denominator}`;this.replace(e,n)}}),this}toOrdinal(e){return this.getNth(e).forEach(e=>{let t=uY(uS(e));e.after("^#Noun").found&&(t+=" of"),e.replaceWith(t)}),this}toCardinal(e){return this.getNth(e).forEach(e=>{let t=u_(uS(e));e.replaceWith(t)}),this}toPercentage(e){return this.getNth(e).forEach(e=>{let{decimal:t}=uS(e),n=100*t;n=Math.round(100*n)/100,e.replaceWith(`${n}%`)}),this}}e.prototype.fractions=function(e){let n=uk(this);return n=n.getNth(e),new t(this.document,n.pointer)}};let u0="twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety|fourty";var u1=function(e){let t=e.match("#Value+");if(t.has("#NumericValue #NumericValue")&&(t.has("#Value @hasComma #Value")?t.splitAfter("@hasComma"):t.has("#NumericValue #Fraction")?t.splitAfter("#NumericValue #Fraction"):t=t.splitAfter("#NumericValue")),t.has("#Value #Value #Value")&&!t.has("#Multiple")&&t.has("("+u0+") #Cardinal #Cardinal")&&(t=t.splitAfter("("+u0+") #Cardinal")),t.has("#Value #Value")){t.has("#NumericValue #NumericValue")&&(t=t.splitOn("#Year")),t.has("("+u0+") (eleven|twelve|thirteen|fourteen|fifteen|sixteen|seventeen|eighteen|nineteen)")&&(t=t.splitAfter("("+u0+")"));let e=t.match("#Cardinal #Cardinal");if(e.found&&!t.has("(point|decimal|#Fraction)")&&!e.has("#Cardinal (#Multiple|point|decimal)")){let n=t.has(`(one|two|three|four|five|six|seven|eight|nine) (${u0})`),r=e.has("("+u0+") #Cardinal"),a=e.has("#Multiple #Value");n||r||a||e.terms().forEach(e=>{t=t.splitOn(e)})}!t.match("#Ordinal #Ordinal").match("#TextValue").found||t.has("#Multiple")||t.has("("+u0+") #Ordinal")||(t=t.splitAfter("#Ordinal")),(t=t.splitBefore("#Ordinal [#Cardinal]",0)).has("#TextValue #NumericValue")&&!t.has("("+u0+"|#Multiple)")&&(t=t.splitBefore("#TextValue #NumericValue"))}return t=(t=t.splitAfter("#NumberRange")).splitBefore("#Year")};let u2=function(e,t){let n=(e=e.replace(/,/g,"")).split(/([0-9.,]*)/),[r,a]=n,i=n.slice(2).join("");return""!==a&&t.length<2?("number"!=typeof(a=Number(a||e))&&(a=null),("st"===(i=i||"")||"nd"===i||"rd"===i||"th"===i)&&(i=""),{prefix:r||"",num:a,suffix:i}):null};var u3=function(e){if("string"==typeof e)return{num:uT(e)};let t=e.text("reduced"),n=e.growRight("#Unit").match("#Unit$").text("machine"),r=/[0-9],[0-9]/.test(e.text("text"));if(1===e.terms().length&&!e.has("#Multiple")){let a=u2(t,e);if(null!==a)return a.hasComma=r,a.unit=n,a}let a=e.match("#Fraction{2,}$");a=!1===a.found?e.match("^#Fraction$"):a;let i=null;a.found&&(a.has("#Value and #Value #Fraction")&&(a=a.match("and #Value #Fraction")),i=uS(a),t=(e=(e=e.not(a)).not("and$")).text("reduced"));let o=0;return t&&(o=uT(t)||0),i&&i.decimal&&(o+=i.decimal),{hasComma:r,prefix:"",num:o,suffix:"",isOrdinal:e.has("#Ordinal"),isText:e.has("#TextValue"),isFraction:e.has("#Fraction"),isMoney:e.has("#Money"),unit:n}},u5=function(e){let t=e.num;if(!t&&0!==t)return null;let n=t%100;if(n>10&&n<20)return String(t)+"th";let r={0:"th",1:"st",2:"nd",3:"rd"},a=u$(t),i=a.slice(a.length-1,a.length);return r[i]?a+=r[i]:a+="th",a};let u4={"\xa2":"cents",$:"dollars","\xa3":"pounds","\xa5":"yen","€":"euros","₡":"col\xf3n","฿":"baht","₭":"kip","₩":"won","₹":"rupees","₽":"ruble","₺":"liras"},u6={"%":"percent","\xb0":"degrees"};var u9=function(e){let t={suffix:"",prefix:e.prefix};return u4.hasOwnProperty(e.prefix)&&(t.suffix+=" "+u4[e.prefix],t.prefix=""),u6.hasOwnProperty(e.suffix)&&(t.suffix+=" "+u6[e.suffix]),t.suffix&&1===e.num&&(t.suffix=t.suffix.replace(/s$/,"")),!t.suffix&&e.suffix&&(t.suffix+=" "+e.suffix),t},u7=function(e,t){if("TextOrdinal"===t){let{prefix:t,suffix:n}=u9(e);return t+uZ(e)+n}if("Ordinal"===t)return e.prefix+u5(e)+e.suffix;if("TextCardinal"===t){let{prefix:t,suffix:n}=u9(e);return t+uU(e)+n}let n=e.num;return e.hasComma&&(n=n.toLocaleString()),e.prefix+String(n)+e.suffix};let u8=e=>"[object Array]"===Object.prototype.toString.call(e),ce=function(e){if("string"==typeof e||"number"==typeof e){let t={};return t[e]=!0,t}return u8(e)?e.reduce((e,t)=>(e[t]=!0,e),{}):e||{}};var ct=function(e){class t extends e{constructor(e,t,n){super(e,t,n),this.viewType="Numbers"}parse(e){return this.getNth(e).map(u3)}get(e){return this.getNth(e).map(u3).map(e=>e.num)}json(e){let t="object"==typeof e?e:{};return this.getNth(e).map(e=>{let n=e.toView().json(t)[0],r=u3(e);return n.number={prefix:r.prefix,num:r.num,suffix:r.suffix,hasComma:r.hasComma,unit:r.unit},n},[])}units(){return this.growRight("#Unit").match("#Unit$")}isUnit(e){return function(e,t={}){return t=ce(t),e.filter(e=>{let{unit:n}=u3(e);return!!n&&!0===t[n]})}(this,e)}isOrdinal(){return this.if("#Ordinal")}isCardinal(){return this.if("#Cardinal")}toNumber(){let e=this.map(e=>{if(!this.has("#TextValue"))return e;let t=u3(e);if(null===t.num)return e;let n=u7(t,e.has("#Ordinal")?"Ordinal":"Cardinal");return e.replaceWith(n,{tags:!0}),e.tag("NumericValue")});return new t(e.document,e.pointer)}toLocaleString(){return this.forEach(e=>{let t=u3(e);if(null===t.num)return;let n=t.num.toLocaleString();if(e.has("#Ordinal")){let e=u7(t,"Ordinal").match(/[a-z]+$/);e&&(n+=e[0]||"")}e.replaceWith(n,{tags:!0})}),this}toText(){let e=this.map(e=>{if(e.has("#TextValue"))return e;let t=u3(e);if(null===t.num)return e;let n=u7(t,e.has("#Ordinal")?"TextOrdinal":"TextCardinal");return e.replaceWith(n,{tags:!0}),e.tag("TextValue"),e});return new t(e.document,e.pointer)}toCardinal(){let e=this.map(e=>{if(!e.has("#Ordinal"))return e;let t=u3(e);if(null===t.num)return e;let n=u7(t,e.has("#TextValue")?"TextCardinal":"Cardinal");return e.replaceWith(n,{tags:!0}),e.tag("Cardinal"),e});return new t(e.document,e.pointer)}toOrdinal(){let e=this.map(e=>{if(e.has("#Ordinal"))return e;let t=u3(e);if(null===t.num)return e;let n=u7(t,e.has("#TextValue")?"TextOrdinal":"Ordinal");return e.replaceWith(n,{tags:!0}),e.tag("Ordinal"),e});return new t(e.document,e.pointer)}isEqual(e){return this.filter(t=>u3(t).num===e)}greaterThan(e){return this.filter(t=>u3(t).num>e)}lessThan(e){return this.filter(t=>u3(t).num<e)}between(e,t){return this.filter(n=>{let r=u3(n).num;return r>e&&r<t})}set(e){if(void 0===e)return this;"string"==typeof e&&(e=u3(e).num);let n=this.map(t=>{let n=u3(t);if(n.num=e,null===n.num)return t;let r=t.has("#Ordinal")?"Ordinal":"Cardinal";t.has("#TextValue")&&(r=t.has("#Ordinal")?"TextOrdinal":"TextCardinal");let a=u7(n,r);return n.hasComma&&"Cardinal"===r&&(a=Number(a).toLocaleString()),(t=t.not("#Currency")).replaceWith(a,{tags:!0}),t});return new t(n.document,n.pointer)}add(e){if(!e)return this;"string"==typeof e&&(e=u3(e).num);let n=this.map(t=>{let n=u3(t);if(null===n.num)return t;n.num+=e;let r=t.has("#Ordinal")?"Ordinal":"Cardinal";n.isText&&(r=t.has("#Ordinal")?"TextOrdinal":"TextCardinal");let a=u7(n,r);return t.replaceWith(a,{tags:!0}),t});return new t(n.document,n.pointer)}subtract(e,t){return this.add(-1*e,t)}increment(e){return this.add(1,e)}decrement(e){return this.add(-1,e)}update(e){let n=new t(this.document,e);return n._cache=this._cache,n}}t.prototype.toNice=t.prototype.toLocaleString,t.prototype.isBetween=t.prototype.between,t.prototype.minus=t.prototype.subtract,t.prototype.plus=t.prototype.add,t.prototype.equals=t.prototype.isEqual,e.prototype.numbers=function(e){let n=u1(this);return n=n.getNth(e),new t(this.document,n.pointer)},e.prototype.percentages=function(e){let n=u1(this);return n=(n=n.filter(e=>e.has("#Percent")||e.after("^percent"))).getNth(e),new t(this.document,n.pointer)},e.prototype.money=function(e){let n=u1(this);return n=(n=n.filter(e=>e.has("#Money")||e.after("^#Currency"))).getNth(e),new t(this.document,n.pointer)},e.prototype.values=e.prototype.numbers};let cn={people:!0,emails:!0,phoneNumbers:!0,places:!0},cr=function(e={}){return!1!==(e=Object.assign({},cn,e)).people&&this.people().replaceWith("██████████"),!1!==e.emails&&this.emails().replaceWith("██████████"),!1!==e.places&&this.places().replaceWith("██████████"),!1!==e.phoneNumbers&&this.phoneNumbers().replaceWith("███████"),this},ca=function(e){let t=e.clauses();return!(/\.\.$/.test(e.out("text"))||e.has("^#QuestionWord")&&e.has("@hasComma"))&&!!(e.has("or not$")||e.has("^#QuestionWord")||e.has("^(do|does|did|is|was|can|could|will|would|may) #Noun")||e.has("^(have|must) you")||t.has("(do|does|is|was) #Noun+ #Adverb? (#Adjective|#Infinitive)$"))};var ci=function(e){let t=/\?/,{document:n}=e;return e.filter(e=>{let r=e.docs[0]||[],a=r[r.length-1];return!!a&&n[a.index[0]].length===r.length&&(!!t.test(a.post)||ca(e))})},co=function(e){let t=e;return 1===t.length||1===(t=t.if("#Verb")).length||1===(t=(t=(t=(t=(t=t.ifNo("(after|although|as|because|before|if|since|than|that|though|when|whenever|where|whereas|wherever|whether|while|why|unless|until|once)")).ifNo("^even (if|though)")).ifNo("^so that")).ifNo("^rather than")).ifNo("^provided that")).length||1===(t=t.ifNo("(that|which|whichever|who|whoever|whom|whose|whomever)")).length||1===(t=t.ifNo("(^despite|^during|^before|^through|^throughout)")).length||1===(t=t.ifNo("^#Gerund")).length?t:(0===t.length&&(t=e),t.eq(0))};let cs=function(e){let t=null;return e.has("#PastTense")?t="PastTense":e.has("#FutureTense")?t="FutureTense":e.has("#PresentTense")&&(t="PresentTense"),{tense:t}};var cl=function(e){let t=co(e.clauses()).chunks(),n=e.none(),r=e.none(),a=e.none();return t.forEach((e,t)=>{if(0===t&&!e.has("<Verb>")){n=e;return}if(!r.found&&e.has("<Verb>")){r=e;return}r.found&&(a=a.concat(e))}),r.found&&!n.found&&(n=r.before("<Noun>+").first()),{subj:n,verb:r,pred:a,grammar:cs(r)}},cu=function(e){let t=e.verbs(),n=t.eq(0);if(n.has("#PastTense"))return e;if(n.toPastTense(),t.length>1){t=(t=(t=(t=t.slice(1)).filter(e=>!e.lookBehind("to$").found)).if("#PresentTense")).notIf("#Gerund");let n=e.match("to #Verb+ #Conjunction #Verb").terms();(t=t.not(n)).found&&t.verbs().toPastTense()}return e},cc=function(e){let t=e.verbs();return t.eq(0).toPresentTense(),t.length>1&&(t=(t=(t=t.slice(1)).filter(e=>!e.lookBehind("to$").found)).notIf("#Gerund")).found&&t.verbs().toPresentTense(),e},ch=function(e){let t=e.verbs();if(t.eq(0).toFutureTense(),(t=(e=e.fullSentence()).verbs()).length>1){let e=(t=t.slice(1)).filter(e=>!e.lookBehind("to$").found&&(!!e.has("#Copula #Gerund")||!e.has("#Gerund")&&(!!e.has("#Copula")||!e.has("#PresentTense")||!!e.has("#Infinitive")||!e.lookBefore("(he|she|it|that|which)$").found)));e.found&&e.forEach(e=>{if(e.has("#Copula")){e.match("was").replaceWith("is"),e.match("is").replaceWith("will be");return}e.toInfinitive()})}return e},cd=function(e){let t=e.splitAfter("@hasComma"),n=(t=t.match("#Honorific+? #Person+")).match("#Possessive").notIf("(his|her)");return t.splitAfter(n)},cg=function(e){let t={};t.firstName=e.match("#FirstName+"),t.lastName=e.match("#LastName+"),t.honorific=e.match("#Honorific+");let n=t.lastName,r=t.firstName;return!((!r.found||!n.found)&&!r.found&&!n.found&&e.has("^#Honorific .$"))||(t.lastName=e.match(".$")),t};let cp="male",cm="female",cf={mr:cp,mrs:cm,miss:cm,madam:cm,king:cp,queen:cm,duke:cp,duchess:cm,baron:cp,baroness:cm,count:cp,countess:cm,prince:cp,princess:cm,sire:cp,dame:cm,lady:cm,ayatullah:cp,congressman:cp,congresswoman:cm,"first lady":cm,mx:null};var cb=function(e,t){let{firstName:n,honorific:r}=e;if(n.has("#FemaleName"))return cm;if(n.has("#MaleName"))return cp;if(r.found){let e=r.text("normal");if(e=e.replace(/\./g,""),cf.hasOwnProperty(e))return cf[e];if(/^her /.test(e))return cm;if(/^his /.test(e))return cp}let a=t.after();if(!a.has("#Person")&&a.has("#Pronoun")){let e=a.match("#Pronoun");if(e.has("(they|their)"))return null;let t=e.has("(he|his)"),n=e.has("(she|her|hers)");if(t&&!n)return cp;if(n&&!t)return cm}return null},cv=function(e){class t extends e{constructor(e,t,n){super(e,t,n),this.viewType="People"}parse(e){return this.getNth(e).map(cg)}json(e){let t="object"==typeof e?e:{};return this.getNth(e).map(e=>{let n=e.toView().json(t)[0],r=cg(e);return n.person={firstName:r.firstName.text("normal"),lastName:r.lastName.text("normal"),honorific:r.honorific.text("normal"),presumed_gender:cb(r,e)},n},[])}presumedMale(){return this.filter(e=>e.has("(#MaleName|mr|mister|sr|jr|king|pope|prince|sir)"))}presumedFemale(){return this.filter(e=>e.has("(#FemaleName|mrs|miss|queen|princess|madam)"))}update(e){let n=new t(this.document,e);return n._cache=this._cache,n}}e.prototype.people=function(e){let n=cd(this);return n=n.getNth(e),new t(this.document,n.pointer)}},cy=function(e){let t=e.match("(#Place|#Address)+"),n=t.match("@hasComma");return n=n.filter(e=>!!e.has("(asia|africa|europe|america)$")||!e.has("(#City|#Region|#ProperNoun)$")||!e.after("^(#Country|#Region)").found),t=t.splitAfter(n)},cw=function(e){e.prototype.places=function(t){let n=cy(this);return n=n.getNth(t),new e(this.document,n.pointer)}},ck=function(e){e.prototype.organizations=function(e){return this.match("#Organization+").getNth(e)}};let cP=function(e){let t=this.clauses(),n=t.people();return(n=(n=(n=(n=n.concat(t.places())).concat(t.organizations())).not("(someone|man|woman|mother|brother|sister|father)")).sort("seq")).getNth(e)};var cA=function(e){e.prototype.topics=cP},cx=function(e){let t=e.match("<Verb>");return(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.not("#Conjunction")).not("#Preposition")).splitAfter("@hasComma")).splitAfter("[(do|did|am|was|is|will)] (is|was)",0)).splitBefore("(#Verb && !#Copula) [being] #Verb",0)).splitBefore("#Verb [to be] #Verb",0)).splitAfter("[help] #PresentTense",0)).splitBefore("(#PresentTense|#PastTense) [#Copula]$",0)).splitBefore("(#PresentTense|#PastTense) [will be]$",0)).splitBefore("(#PresentTense|#PastTense) [(had|has)]",0)).not("#Reflexive$")).not("#Adjective")).splitAfter("[#PastTense] #PastTense",0)).splitAfter("[#PastTense] #Auxiliary+ #PastTense",0)).splitAfter("#Copula [#Gerund] #PastTense",0)).if("#Verb")).has("(#Verb && !#Auxiliary) #Adverb+? #Copula")&&(t=t.splitBefore("#Copula")),t},cC=function(e){let t=e;return e.wordCount()>1&&(t=e.not("(#Negative|#Auxiliary|#Modal|#Adverb|#Prefix)")),t.length>1&&!t.has("#Phrasal #Particle")&&(t=t.last()),(t=t.not("(want|wants|wanted) to")).found||(t=e.not("#Negative")),t},cj=function(e,t){let n={pre:e.none(),post:e.none()};if(!e.has("#Adverb"))return n;let r=e.splitOn(t);return 3===r.length?{pre:r.eq(0).adverbs(),post:r.eq(2).adverbs()}:(r.eq(0).isDoc(t)?n.post=r.eq(1).adverbs():n.pre=r.eq(0).adverbs(),n)};let cN=function(e,t){let n=e.splitBefore(t);return n.length<=1?e.none():n.eq(0).not("(#Adverb|#Negative|#Prefix)")},cI=function(e){if(!e.has("(#Particle|#PhrasalVerb)"))return{verb:e.none(),particle:e.none()};let t=e.match("#Particle$");return{verb:e.not(t),particle:t}};var cD=function(e){let t=e.clone();t.contractions().expand();let n=cC(t);return{root:n,prefix:t.match("#Prefix"),adverbs:cj(t,n),auxiliary:cN(t,n),negative:t.match("#Negative"),phrasal:cI(n)}};let cT={tense:"PresentTense"},cO={conditional:!0},cH={tense:"FutureTense"},cE={progressive:!0},cF={tense:"PastTense"},cG={complete:!0,progressive:!1},cV={passive:!0},cz=function(e){let t={};return e.forEach(e=>{Object.assign(t,e)}),t},cM={imperative:[["#Imperative",[]]],"want-infinitive":[["^(want|wants|wanted) to #Infinitive$",[cT]],["^wanted to #Infinitive$",[cF]],["^will want to #Infinitive$",[cH]]],"gerund-phrase":[["^#PastTense #Gerund$",[cF]],["^#PresentTense #Gerund$",[cT]],["^#Infinitive #Gerund$",[cT]],["^will #Infinitive #Gerund$",[cH]],["^have #PastTense #Gerund$",[cF]],["^will have #PastTense #Gerund$",[cF]]],"simple-present":[["^#PresentTense$",[cT]],["^#Infinitive$",[cT]]],"simple-past":[["^#PastTense$",[cF]]],"simple-future":[["^will #Adverb? #Infinitive",[cH]]],"present-progressive":[["^(is|are|am) #Gerund$",[cT,cE]]],"past-progressive":[["^(was|were) #Gerund$",[cF,cE]]],"future-progressive":[["^will be #Gerund$",[cH,cE]]],"present-perfect":[["^(has|have) #PastTense$",[cF,cG]]],"past-perfect":[["^had #PastTense$",[cF,cG]],["^had #PastTense to #Infinitive",[cF,cG]]],"future-perfect":[["^will have #PastTense$",[cH,cG]]],"present-perfect-progressive":[["^(has|have) been #Gerund$",[cF,cE]]],"past-perfect-progressive":[["^had been #Gerund$",[cF,cE]]],"future-perfect-progressive":[["^will have been #Gerund$",[cH,cE]]],"passive-past":[["(got|were|was) #Passive",[cF,cV]],["^(was|were) being #Passive",[cF,cV]],["^(had|have) been #Passive",[cF,cV]]],"passive-present":[["^(is|are|am) #Passive",[cT,cV]],["^(is|are|am) being #Passive",[cT,cV]],["^has been #Passive",[cT,cV]]],"passive-future":[["will have been #Passive",[cH,cV,cO]],["will be being? #Passive",[cH,cV,cO]]],"present-conditional":[["would be #PastTense",[cT,cO]]],"past-conditional":[["would have been #PastTense",[cF,cO]]],"auxiliary-future":[["(is|are|am|was) going to (#Infinitive|#PresentTense)",[cH]]],"auxiliary-past":[["^did #Infinitive$",[cF,{plural:!1}]],["^used to #Infinitive$",[cF,cG]]],"auxiliary-present":[["^(does|do) #Infinitive$",[cT,cG,{plural:!0}]]],"modal-past":[["^(could|must|should|shall) have #PastTense$",[cF]]],"modal-infinitive":[["^#Modal #Infinitive$",[]]],infinitive:[["^#Infinitive$",[]]]},cB=[];Object.keys(cM).map(e=>{cM[e].forEach(t=>{cB.push({name:e,match:t[0],data:cz(t[1])})})});var cS=function(e,t){var n,r;let a={};n=(n=e).clone(),t.adverbs.post&&t.adverbs.post.found&&n.remove(t.adverbs.post),t.adverbs.pre&&t.adverbs.pre.found&&n.remove(t.adverbs.pre),n.has("#Negative")&&(n=n.remove("#Negative")),n.has("#Prefix")&&(n=n.remove("#Prefix")),t.root.has("#PhrasalVerb #Particle")&&n.remove("#Particle$"),e=n=n.not("#Adverb");for(let t=0;t<cB.length;t+=1){let n=cB[t];if(!0===e.has(n.match)){a.form=n.name,Object.assign(a,n.data);break}}return!a.form&&e.has("^#Verb$")&&(a.form="infinitive"),a.tense||(a.tense=t.root.has("#PastTense")?"PastTense":"PresentTense"),a.copula=t.root.has("#Copula"),a.isInfinitive=!!((r=e).has("#Infinitive")&&r.growLeft("to").has("^to #Infinitive")),a};let c$=function(e){return!(e.length<=1)&&(e.parse()[0]||{}).isSubordinate},cL=function(e){let t=e.clauses();return 0===(t=t.filter((e,t)=>!(e.has("^(if|unless|while|but|for|per|at|by|that|which|who|from)")||t>0&&e.has("^#Verb . #Noun+$")||t>0&&e.has("^#Adverb")))).length?e:t},cK=function(e){let t=e.before(),n=(t=cL(t)).nouns(),r=n.last(),a=r.match("(i|he|she|we|you|they)");if(a.found)return a.nouns();let i=n.if("^(that|this|those)");return i.found||!1===n.found&&(i=t.match("^(that|this|those)")).found?i:(c$(r=n.last())&&(n.remove(r),r=n.last()),c$(r)&&(n.remove(r),r=n.last()),r)};var cJ=function(e){let t=cK(e);return{subject:t,plural:!!(e.has("(are|were|does)")||t.has("(those|they|we)"))||!!t.found&&!!t.isPlural&&t.isPlural().found}};let cW=e=>e,cq=(e,t)=>{let n=cJ(e,t),r=n.subject;return!!(r.has("i")||r.has("we"))||n.plural},cR=(e,t)=>{let{subject:n,plural:r}=cJ(e,t);return r||n.has("we")?"were":"was"},cU=function(e,t){if(e.has("were"))return"are";let{subject:n,plural:r}=cJ(e,t);return n.has("i")?"am":n.has("we")||r?"are":"is"},c_=function(e,t){let n=cJ(e,t),r=n.subject;return r.has("i")||r.has("we")||n.plural?"do":"does"},cQ=function(e){return e.has("#Infinitive")?"Infinitive":e.has("#Participle")?"Participle":e.has("#PastTense")?"PastTense":e.has("#Gerund")?"Gerund":e.has("#PresentTense")?"PresentTense":void 0},cZ=function(e,t){let{toInfinitive:n}=e.methods.two.transform.verb,r=t.root.text({keepPunct:!1});return(r=n(r,e.model,cQ(e)))&&e.replace(t.root,r),e},cY=e=>e.has("will not")?e.replace("will not","have not"):e.remove("will"),cX=function(e){return e&&e.isView?e.json({normal:!0,terms:!1,text:!1}).map(e=>e.normal):[]},c0=function(e){return e&&e.isView?e.text("normal"):""},c1=function(e){let{toInfinitive:t}=e.methods.two.transform.verb;return t(e.text("normal"),e.model,cQ(e))};var c2=function(e){let t=cD(e),n=cS(e=e.clone().toView(),t);return{root:t.root.text(),preAdverbs:cX(t.adverbs.pre),postAdverbs:cX(t.adverbs.post),auxiliary:c0(t.auxiliary),negative:t.negative.found,prefix:c0(t.prefix),infinitive:c1(t.root),grammar:n}};let c3={tags:!0};var c5=function(e,t){let{toInfinitive:n}=e.methods.two.transform.verb,{root:r,auxiliary:a}=t,i=a.terms().harden(),o=r.text("normal");if((o=n(o,e.model,cQ(r)))&&e.replace(r,o,c3).tag("Verb").firstTerm().tag("Infinitive"),i.found&&e.remove(i),t.negative.found){e.has("not")||e.prepend("not");let n=c_(e,t);e.prepend(n)}return e.fullSentence().compute(["freeze","lexicon","preTagger","postTagger","unfreeze","chunks"]),e};let c4={tags:!0},c6={noAux:(e,t)=>(t.auxiliary.found&&(e=e.remove(t.auxiliary)),e),simple:(e,t)=>{let{conjugate:n,toInfinitive:r}=e.methods.two.transform.verb,a=t.root;if(a.has("#Modal"))return e;let i=a.text({keepPunct:!1});return"was"===(i="been"===(i=n(i=r(i,e.model,cQ(a)),e.model).PastTense)?"was":i)&&(i=cR(e,t)),i&&e.replace(a,i,c4),e},both:function(e,t){return t.negative.found?(e.replace("will","did"),e):(e=c6.simple(e,t),e=c6.noAux(e,t))},hasHad:e=>(e.replace("has","had",c4),e),hasParticiple:(e,t)=>{let{conjugate:n,toInfinitive:r}=e.methods.two.transform.verb,a=t.root,i=a.text("normal");return n(i=r(i,e.model,cQ(a)),e.model).Participle}},c9={infinitive:c6.simple,"simple-present":c6.simple,"simple-past":cW,"simple-future":c6.both,"present-progressive":e=>(e.replace("are","were",c4),e.replace("(is|are|am)","was",c4),e),"past-progressive":cW,"future-progressive":(e,t)=>(e.match(t.root).insertBefore("was"),e.remove("(will|be)"),e),"present-perfect":c6.hasHad,"past-perfect":cW,"future-perfect":(e,t)=>(e.match(t.root).insertBefore("had"),e.has("will")&&(e=cY(e)),e.remove("have"),e),"present-perfect-progressive":c6.hasHad,"past-perfect-progressive":cW,"future-perfect-progressive":e=>(e.remove("will"),e.replace("have","had",c4),e),"passive-past":e=>(e.replace("have","had",c4),e),"passive-present":e=>(e.replace("(is|are)","was",c4),e),"passive-future":(e,t)=>(t.auxiliary.has("will be")&&(e.match(t.root).insertBefore("had been"),e.remove("(will|be)")),t.auxiliary.has("will have been")&&(e.replace("have","had",c4),e.remove("will")),e),"present-conditional":e=>(e.replace("be","have been"),e),"past-conditional":cW,"auxiliary-future":e=>(e.replace("(is|are|am)","was",c4),e),"auxiliary-past":cW,"auxiliary-present":e=>(e.replace("(do|does)","did",c4),e),"modal-infinitive":(e,t)=>(e.has("can")?e.replace("can","could",c4):(c6.simple(e,t),e.match("#Modal").insertAfter("have").tag("Auxiliary")),e),"modal-past":cW,"want-infinitive":e=>(e.replace("(want|wants)","wanted",c4),e.remove("will"),e),"gerund-phrase":(e,t)=>(t.root=t.root.not("#Gerund$"),c6.simple(e,t),cY(e),e)},c7=function(e,t){let n=cJ(e,t),r=n.subject;return r.has("(i|we|you)")?"have":!1===n.plural||r.has("he")||r.has("she")||r.has("#Person")?"has":"have"},c8=(e,t)=>{let{conjugate:n,toInfinitive:r}=e.methods.two.transform.verb,{root:a,auxiliary:i}=t;if(a.has("#Modal"))return e;let o=a.text({keepPunct:!1}),s=n(o=r(o,e.model,cQ(a)),e.model);if(o=s.Participle||s.PastTense){let n=c7(e=e.replace(a,o),t);e.prepend(n).match(n).tag("Auxiliary"),e.remove(i)}return e},he={infinitive:c8,"simple-present":c8,"simple-future":(e,t)=>e.replace("will",c7(e,t)),"present-perfect":cW,"past-perfect":cW,"future-perfect":(e,t)=>e.replace("will have",c7(e,t)),"present-perfect-progressive":cW,"past-perfect-progressive":cW,"future-perfect-progressive":cW},ht={tags:!0},hn=(e,t)=>{let{conjugate:n,toInfinitive:r}=e.methods.two.transform.verb,a=t.root,i=a.text("normal");return i=r(i,e.model,cQ(a)),!1===cq(e,t)&&(i=n(i,e.model).PresentTense),a.has("#Copula")&&(i=cU(e,t)),i&&(e=e.replace(a,i,ht)).not("#Particle").tag("PresentTense"),e},hr=(e,t)=>{let{conjugate:n,toInfinitive:r}=e.methods.two.transform.verb,a=t.root,i=a.text("normal");return i=r(i,e.model,cQ(a)),!1===cq(e,t)&&(i=n(i,e.model).Gerund),i&&(e=e.replace(a,i,ht)).not("#Particle").tag("Gerund"),e},ha=(e,t)=>{let{toInfinitive:n}=e.methods.two.transform.verb,r=t.root,a=t.root.text("normal");return(a=n(a,e.model,cQ(r)))&&(e=e.replace(t.root,a,ht)),e},hi={infinitive:hn,"simple-present":(e,t)=>{let{conjugate:n}=e.methods.two.transform.verb,{root:r}=t;if(!r.has("#Infinitive"))return hn(e,t);{let a=cJ(e,t).subject;if(cq(e,t)||a.has("i"))return e;let i=r.text("normal"),o=n(i,e.model).PresentTense;i!==o&&e.replace(r,o,ht)}return e},"simple-past":hn,"simple-future":(e,t)=>{let{root:n,auxiliary:r}=t;if(r.has("will")&&n.has("be")){let r=cU(e,t);e.replace(n,r),(e=e.remove("will")).replace("not "+r,r+" not")}else hn(e,t),e=e.remove("will");return e},"present-progressive":cW,"past-progressive":(e,t)=>{let n=cU(e,t);return e.replace("(were|was)",n,ht)},"future-progressive":e=>(e.match("will").insertBefore("is"),e.remove("be"),e.remove("will")),"present-perfect":(e,t)=>(hn(e,t),e=e.remove("(have|had|has)")),"past-perfect":(e,t)=>{let n=cJ(e,t).subject;return cq(e,t)||n.has("i")?(e=cZ(e,t)).remove("had"):e.replace("had","has",ht),e},"future-perfect":e=>(e.match("will").insertBefore("has"),e.remove("have").remove("will")),"present-perfect-progressive":cW,"past-perfect-progressive":e=>e.replace("had","has",ht),"future-perfect-progressive":e=>(e.match("will").insertBefore("has"),e.remove("have").remove("will")),"passive-past":(e,t)=>{let n=cU(e,t);return e.has("(had|have|has)")&&e.has("been")?(e.replace("(had|have|has)",n,ht),e.replace("been","being"),e):e.replace("(got|was|were)",n)},"passive-present":cW,"passive-future":e=>(e.replace("will","is"),e.replace("be","being")),"present-conditional":cW,"past-conditional":e=>(e.replace("been","be"),e.remove("have")),"auxiliary-future":(e,t)=>(hr(e,t),e.remove("(going|to)"),e),"auxiliary-past":(e,t)=>{if(t.auxiliary.has("did")){let n=c_(e,t);return e.replace(t.auxiliary,n),e}return hr(e,t),e.replace(t.auxiliary,"is"),e},"auxiliary-present":cW,"modal-infinitive":cW,"modal-past":(e,t)=>(ha(e,t),e.remove("have")),"gerund-phrase":(e,t)=>(t.root=t.root.not("#Gerund$"),hn(e,t),e.remove("(will|have)")),"want-infinitive":(e,t)=>{let n="wants";return cq(e,t)&&(n="want"),e.replace("(want|wanted|wants)",n,ht),e.remove("will"),e}},ho={tags:!0},hs=(e,t)=>{let{toInfinitive:n}=e.methods.two.transform.verb,{root:r,auxiliary:a}=t;if(r.has("#Modal"))return e;let i=r.text("normal");return(i=n(i,e.model,cQ(r)))&&(e=e.replace(r,i,ho)).not("#Particle").tag("Verb"),e.prepend("will").match("will").tag("Auxiliary"),e.remove(a),e},hl=(e,t)=>{let{conjugate:n,toInfinitive:r}=e.methods.two.transform.verb,{root:a,auxiliary:i}=t,o=a.text("normal");return(o=r(o,e.model,cQ(a)))&&(o=n(o,e.model).Gerund,e.replace(a,o,ho),e.not("#Particle").tag("PresentTense")),e.remove(i),e.prepend("will be").match("will be").tag("Auxiliary"),e},hu={infinitive:hs,"simple-present":hs,"simple-past":hs,"simple-future":cW,"present-progressive":hl,"past-progressive":hl,"future-progressive":cW,"present-perfect":e=>(e.match("(have|has)").replaceWith("will have"),e),"past-perfect":e=>e.replace("(had|has)","will have"),"future-perfect":cW,"present-perfect-progressive":e=>e.replace("has","will have"),"past-perfect-progressive":e=>e.replace("had","will have"),"future-perfect-progressive":cW,"passive-past":e=>e.has("got")?e.replace("got","will get"):e.has("(was|were)")?(e.replace("(was|were)","will be"),e.remove("being")):e.has("(have|has|had) been")?e.replace("(have|has|had) been","will be"):e,"passive-present":e=>(e.replace("being","will be"),e.remove("(is|are|am)"),e),"passive-future":cW,"present-conditional":e=>e.replace("would","will"),"past-conditional":e=>e.replace("would","will"),"auxiliary-future":cW,"auxiliary-past":e=>e.has("used")&&e.has("to")?(e.replace("used","will"),e.remove("to")):(e.replace("did","will"),e),"auxiliary-present":e=>e.replace("(do|does)","will"),"modal-infinitive":cW,"modal-past":cW,"gerund-phrase":(e,t)=>(t.root=t.root.not("#Gerund$"),hs(e,t),e.remove("(had|have)")),"want-infinitive":e=>(e.replace("(want|wants|wanted)","will want"),e)},hc={tags:!0};var hh=function(e,t){let{toInfinitive:n,conjugate:r}=e.methods.two.transform.verb,{root:a,auxiliary:i}=t;if(e.has("#Gerund"))return e;let o=a.text("normal"),s=r(o=n(o,e.model,cQ(a)),e.model).Gerund;if(s){let n=cU(e,t);e.replace(a,s,hc),e.remove(i),e.prepend(n)}return e.replace("not is","is not"),e.replace("not are","are not"),e.fullSentence().compute(["tagger","chunks"]),e};let hd={tags:!0},hg=function(e,t){let n=c_(e,t);return e.prepend(n+" not"),e},hp=function(e){let t=e.match("be");return t.found?t.prepend("not"):(t=e.match("(is|was|am|are|will|were)")).found&&t.append("not"),e},hm=e=>e.has("(is|was|am|are|will|were|be)"),hf={"simple-present":(e,t)=>!0===hm(e)?hp(e,t):e=hg(e=cZ(e,t),t),"simple-past":(e,t)=>!0===hm(e)?hp(e,t):((e=cZ(e,t)).prepend("did not"),e),imperative:e=>(e.prepend("do not"),e),infinitive:(e,t)=>!0===hm(e)?hp(e,t):hg(e,t),"passive-past":e=>{if(e.has("got"))return e.replace("got","get",hd),e.prepend("did not"),e;let t=e.match("(was|were|had|have)");return t.found&&t.append("not"),e},"auxiliary-past":e=>{if(e.has("used"))return e.prepend("did not"),e;let t=e.match("(did|does|do)");return t.found&&t.append("not"),e},"want-infinitive":(e,t)=>e=(e=hg(e,t)).replace("wants","want",hd)};var hb=function(e,t,n){if(e.has("#Negative"))return e;if(hf.hasOwnProperty(n))return e=hf[n](e,t);let r=e.matchOne("be");return r.found?(r.prepend("not"),e):!0===hm(e)?hp(e,t):((r=e.matchOne("(will|had|have|has|did|does|do|#Modal)")).found&&r.append("not"),e)};let hv=function(e,t){let n=t.match(e);if(n.found){let e=n.pronouns().refersTo();if(e.found)return e}return t.none()},hy=function(e){if(!e.found)return e;let[t]=e.fullPointer[0];return t&&t>0?e.update([[t-1]]):e.none()};var hw=function(e,t){var n;let r=e.people();return(n=r,(r="m"===t?n.filter(e=>!e.presumedFemale().found):"f"===t?n.filter(e=>!e.presumedMale().found):n).found||(r=e.nouns("#Actor")).found)?r.last():"f"===t?hv("(she|her|hers)",e):"m"===t?hv("(he|him|his)",e):e.none()},hk=function(e){let t=e.nouns(),n=t.isPlural().notIf("#Pronoun");if(n.found)return n.last();let r=hv("(they|their|theirs)",e);return r.found?r:(n=t.match("(somebody|nobody|everybody|anybody|someone|noone|everyone|anyone)")).found?n.last():e.none()};let hP=function(e,t){t&&t.found&&(e.docs[0][0].reference=t.ptrs[0])},hA=function(e,t){let n=e.before(),r=t(n);return r.found||(r=t(n=hy(e))).found||(r=t(n=hy(n))).found?r:e.none()};y.plugin({api:function(e){class t extends e{constructor(e,t,n){super(e,t,n),this.viewType="Adjectives"}json(e={}){let{toAdverb:t,toNoun:n,toSuperlative:r,toComparative:a}=this.methods.two.transform.adjective;return e.normal=!0,this.map(i=>{let o=i.toView().json(e)[0]||{},s=lC(i);return o.adjective={adverb:t(s,this.model),noun:n(s,this.model),superlative:r(s,this.model),comparative:a(s,this.model)},o},[])}adverbs(){return this.before("#Adverb+$").concat(this.after("^#Adverb+"))}conjugate(e){let{toComparative:t,toSuperlative:n,toNoun:r,toAdverb:a}=this.methods.two.transform.adjective;return this.getNth(e).map(e=>{let i=lC(e);return{Adjective:i,Comparative:t(i,this.model),Superlative:n(i,this.model),Noun:r(i,this.model),Adverb:a(i,this.model)}},[])}toComparative(e){let{toComparative:t}=this.methods.two.transform.adjective;return this.getNth(e).map(e=>{let n=t(lC(e),this.model);return e.replaceWith(n)})}toSuperlative(e){let{toSuperlative:t}=this.methods.two.transform.adjective;return this.getNth(e).map(e=>{let n=t(lC(e),this.model);return e.replaceWith(n)})}toAdverb(e){let{toAdverb:t}=this.methods.two.transform.adjective;return this.getNth(e).map(e=>{let n=t(lC(e),this.model);return e.replaceWith(n)})}toNoun(e){let{toNoun:t}=this.methods.two.transform.adjective;return this.getNth(e).map(e=>{let n=t(lC(e),this.model);return e.replaceWith(n)})}}e.prototype.adjectives=function(e){let n=this.match("#Adjective");return new t((n=n.getNth(e)).document,n.pointer)},e.prototype.superlatives=function(e){let n=this.match("#Superlative");return new t((n=n.getNth(e)).document,n.pointer)},e.prototype.comparatives=function(e){let n=this.match("#Comparative");return new t((n=n.getNth(e)).document,n.pointer)}}}),y.plugin({api:function(e){class t extends e{constructor(e,t,n){super(e,t,n),this.viewType="Adverbs"}conjugate(e){return this.getNth(e).map(e=>{let t=e.compute("root").text("root");return{Adverb:e.text("normal"),Adjective:t}},[])}json(e={}){let t=this.methods.two.transform.adjective.fromAdverb;return e.normal=!0,this.map(n=>{let r=n.toView().json(e)[0]||{};return r.adverb={adjective:t(r.normal)},r},[])}}e.prototype.adverbs=function(e){let n=this.match("#Adverb");return new t((n=n.getNth(e)).document,n.pointer)}}}),y.plugin({compute:{chunks:function(e){let{document:t,world:n}=e;lH(t),lE(t),lV(e,t,n),lB(t,n),lS(t,n)}},api:function(e){class t extends e{constructor(e,t,n){super(e,t,n),this.viewType="Chunks"}isVerb(){return this.filter(e=>e.has("<Verb>"))}isNoun(){return this.filter(e=>e.has("<Noun>"))}isAdjective(){return this.filter(e=>e.has("<Adjective>"))}isPivot(){return this.filter(e=>e.has("<Pivot>"))}debug(){return this.toView().debug("chunks"),this}update(e){let n=new t(this.document,e);return n._cache=this._cache,n}}e.prototype.chunks=function(e){let n=lT(this);return n=n.getNth(e),new t(this.document,n.pointer)},e.prototype.clauses=lD},hooks:["chunks"]}),y.plugin({compute:{coreference:function(e){e.pronouns().if("(he|him|his|she|her|hers|they|their|theirs|it|its)").forEach(e=>{let t=null;e.has("(he|him|his)")?t=hA(e,e=>hw(e,"m")):e.has("(she|her|hers)")?t=hA(e,e=>hw(e,"f")):e.has("(they|their|theirs)")&&(t=hA(e,hk)),t&&t.found&&hP(e,t)})}},api:function(e){class t extends e{constructor(e,t,n){super(e,t,n),this.viewType="Pronouns"}hasReference(){return this.compute("coreference"),this.filter(e=>e.docs[0][0].reference)}refersTo(){return this.compute("coreference"),this.map(e=>{if(!e.found)return e.none();let t=e.docs[0][0];return t.reference?e.update([t.reference]):e.none()})}update(e){let n=new t(this.document,e);return n._cache=this._cache,n}}e.prototype.pronouns=function(e){let n=this.match("#Pronoun");return new t((n=n.getNth(e)).document,n.pointer)}}}),y.plugin({api:function(e){lL(e),lR(e),lQ(e),l3(e),l9(e),l8(e)}}),y.plugin({api:function(e){e.prototype.normalize=function(e="light"){return"string"==typeof e&&(e=ui[e]),Object.keys(e).forEach(t=>{ut.hasOwnProperty(t)&&ut[t](this,e[t])}),this}}}),y.plugin({api:function(e){class t extends e{constructor(e,t,n){super(e,t,n),this.viewType="Nouns"}parse(e){return this.getNth(e).map(uh)}json(e){let t="object"==typeof e?e:{};return this.getNth(e).map(e=>{let n=e.toView().json(t)[0]||{};return t&&!1!==t.noun&&(n.noun=um(e)),n},[])}conjugate(e){let t=this.world.methods.two.transform.noun;return this.getNth(e).map(e=>{let n=uh(e),r=n.root.compute("root").text("root"),a={Singular:r};return uf(n.root)&&(a.Plural=t.toPlural(r,this.model)),a.Singular===a.Plural&&delete a.Plural,a},[])}isPlural(e){return this.filter(e=>uh(e).isPlural).getNth(e)}isSingular(e){return this.filter(e=>!uh(e).isPlural).getNth(e)}adjectives(e){let t=this.update([]);return this.forEach(e=>{let n=uh(e).adjectives;n.found&&(t=t.concat(n))}),t.getNth(e)}toPlural(e){return this.getNth(e).map(e=>uv(e,uh(e)))}toSingular(e){return this.getNth(e).map(e=>{let t=uh(e);return uw(e,t)})}update(e){let n=new t(this.document,e);return n._cache=this._cache,n}}e.prototype.nouns=function(e){let n=uo(this);return n=n.getNth(e),new t(this.document,n.pointer)}}}),y.plugin({api:function(e){uX(e),ct(e)}}),y.plugin({api:function(e){e.prototype.redact=cr}}),y.plugin({api:function(e){class t extends e{constructor(e,t,n){super(e,t,n),this.viewType="Sentences"}json(e={}){return this.map(t=>{let n=t.toView().json(e)[0]||{},{subj:r,verb:a,pred:i,grammar:o}=cl(t);return n.sentence={subject:r.text("normal"),verb:a.text("normal"),predicate:i.text("normal"),grammar:o},n},[])}toPastTense(e){return this.getNth(e).map(e=>{let t=cl(e);return cu(e,t)})}toPresentTense(e){return this.getNth(e).map(e=>{let t=cl(e);return cc(e,t)})}toFutureTense(e){return this.getNth(e).map(e=>{let t=cl(e);return e=ch(e,t)})}toInfinitive(e){return this.getNth(e).map(e=>(cl(e),e.verbs().toInfinitive(),e))}toNegative(e){return this.getNth(e).map(e=>(cl(e),e.verbs().first().toNegative().compute("chunks"),e))}toPositive(e){return this.getNth(e).map(e=>(cl(e),e.verbs().first().toPositive().compute("chunks"),e))}isQuestion(e){return this.questions(e)}isExclamation(e){return this.filter(e=>e.lastTerm().has("@hasExclamation")).getNth(e)}isStatement(e){return this.filter(e=>!e.isExclamation().found&&!e.isQuestion().found).getNth(e)}update(e){let n=new t(this.document,e);return n._cache=this._cache,n}}t.prototype.toPresent=t.prototype.toPresentTense,t.prototype.toPast=t.prototype.toPastTense,t.prototype.toFuture=t.prototype.toFutureTense,Object.assign(e.prototype,{sentences:function(e){let n=this.map(e=>e.fullSentence());return n=n.getNth(e),new t(this.document,n.pointer)},questions:function(e){return ci(this).getNth(e)}})}}),y.plugin({api:function(e){cv(e),cw(e),ck(e),cA(e)}}),y.plugin({api:function(e){class t extends e{constructor(e,t,n){super(e,t,n),this.viewType="Verbs"}parse(e){return this.getNth(e).map(cD)}json(e,t){return this.getNth(t).map(t=>{let n=t.toView().json(e)[0]||{};return n.verb=c2(t),n},[])}subjects(e){return this.getNth(e).map(e=>{let t=cD(e);return cJ(e,t).subject})}adverbs(e){return this.getNth(e).map(e=>e.match("#Adverb"))}isSingular(e){return this.getNth(e).filter(e=>!0!==cJ(e).plural)}isPlural(e){return this.getNth(e).filter(e=>!0===cJ(e).plural)}isImperative(e){return this.getNth(e).filter(e=>e.has("#Imperative"))}toInfinitive(e){return this.getNth(e).map(e=>{let t=cD(e),n=cS(e,t);return c5(e,t,n.form)})}toPresentTense(e){return this.getNth(e).map(e=>{var t,n;let r=cD(e),a=cS(e,r);return a.isInfinitive?e:(t=e,n=a.form,hi.hasOwnProperty(n)&&(t=hi[n](t,r)).fullSentence().compute(["tagger","chunks"]),t)})}toPastTense(e){return this.getNth(e).map(e=>{var t,n;let r=cD(e),a=cS(e,r);return a.isInfinitive?e:(t=e,n=a.form,c9.hasOwnProperty(n)&&(t=c9[n](t,r)).fullSentence().compute(["tagger","chunks"]),t)})}toFutureTense(e){return this.getNth(e).map(e=>{var t,n;let r=cD(e),a=cS(e,r);return a.isInfinitive?e:(t=e,n=a.form,t.has("will")||t.has("going to")||hu.hasOwnProperty(n)&&(t=hu[n](t,r)).fullSentence().compute(["tagger","chunks"]),t)})}toGerund(e){return this.getNth(e).map(e=>{let t=cD(e),n=cS(e,t);return n.isInfinitive?e:hh(e,t,n.form)})}toPastParticiple(e){return this.getNth(e).map(e=>{var t,n;let r=cD(e),a=cS(e,r);return a.isInfinitive?e:(t=e,n=a.form,he.hasOwnProperty(n)?(t=he[n](t,r)).fullSentence().compute(["tagger","chunks"]):(t=c8(t,r,n)).fullSentence().compute(["tagger","chunks"]),t)})}conjugate(e){let{conjugate:t,toInfinitive:n}=this.world.methods.two.transform.verb;return this.getNth(e).map(e=>{let r=cD(e),a=cS(e,r);"imperative"===a.form&&(a.form="simple-present");let i=r.root.text("normal");if(!r.root.has("#Infinitive")){let t=cQ(r.root);i=n(i,e.model,t)||i}return t(i,e.model)},[])}isNegative(){return this.if("#Negative")}isPositive(){return this.ifNo("#Negative")}toPositive(){let e=this.match("do not #Verb");return e.found&&e.remove("do not"),this.remove("#Negative")}toNegative(e){return this.getNth(e).map(e=>{let t=cD(e),n=cS(e,t);return hb(e,t,n.form)})}update(e){let n=new t(this.document,e);return n._cache=this._cache,n}}t.prototype.toPast=t.prototype.toPastTense,t.prototype.toPresent=t.prototype.toPresentTense,t.prototype.toFuture=t.prototype.toFutureTense,e.prototype.verbs=function(e){let n=cx(this);return n=n.getNth(e),new t(this.document,n.pointer)}}});var hx=y},37346:function(e,t,n){n.d(t,{$Q:function(){return c}});var r,a=n(2265),i=n(55211);let o="label";function s(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function l(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:o,r=[];e.datasets=t.map(t=>{let a=e.datasets.find(e=>e[n]===t[n]);return!a||!t.data||r.includes(a)?{...t}:(r.push(a),Object.assign(a,t),a)})}let u=(0,a.forwardRef)(function(e,t){let{height:n=150,width:r=300,redraw:u=!1,datasetIdKey:c,type:h,data:d,options:g,plugins:p=[],fallbackContent:m,updateMode:f,...b}=e,v=(0,a.useRef)(null),y=(0,a.useRef)(null),w=()=>{v.current&&(y.current=new i.kL(v.current,{type:h,data:function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o,r={labels:[],datasets:[]};return t=e.labels,r.labels=t,l(r,e.datasets,n),r}(d,c),options:g&&{...g},plugins:p}),s(t,y.current))},k=()=>{s(t,null),y.current&&(y.current.destroy(),y.current=null)};return(0,a.useEffect)(()=>{!u&&y.current&&g&&function(e,t){let n=e.options;n&&t&&Object.assign(n,t)}(y.current,g)},[u,g]),(0,a.useEffect)(()=>{if(!u&&y.current){var e,t;e=y.current.config.data,t=d.labels,e.labels=t}},[u,d.labels]),(0,a.useEffect)(()=>{!u&&y.current&&d.datasets&&l(y.current.config.data,d.datasets,c)},[u,d.datasets]),(0,a.useEffect)(()=>{y.current&&(u?(k(),setTimeout(w)):y.current.update(f))},[u,g,d.labels,d.datasets,f]),(0,a.useEffect)(()=>{y.current&&(k(),setTimeout(w))},[h]),(0,a.useEffect)(()=>(w(),()=>k()),[]),a.createElement("canvas",{ref:v,role:"img",height:n,width:r,...b},m)}),c=(r=i.vn,i.kL.register(r),(0,a.forwardRef)((e,t)=>a.createElement(u,{...e,ref:t,type:"bar"})))}}]);