(()=>{var e={};e.id=8168,e.ids=[8168],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},23148:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>h,pages:()=>d,routeModule:()=>p,tree:()=>o}),a(52799),a(54302),a(12523);var t=a(23191),r=a(88716),l=a(37922),i=a.n(l),n=a(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let o=["",{children:["freelancer",{children:["market",{children:["[business_id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,52799)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\market\\[business_id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\market\\[business_id]\\page.tsx"],h="/freelancer/market/[business_id]/page",x={require:a,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/freelancer/market/[business_id]/page",pathname:"/freelancer/market/[business_id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},41641:(e,s,a)=>{Promise.resolve().then(a.bind(a,63911))},40900:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},43727:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},32019:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},5932:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},40617:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},42887:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},88378:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},60763:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69515:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},98091:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},63911:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N});var t=a(10326),r=a(3512),l=a(92166),i=a(86144),n=a(54406),c=a(87922),o=a(48586),d=a(17577),h=a(35047),x=a(5932),p=a(42887),m=a(32019);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let u=(0,a(80851).Z)("Earth",[["path",{d:"M21.54 15H17a2 2 0 0 0-2 2v4.54",key:"1djwo0"}],["path",{d:"M7 3.34V5a3 3 0 0 0 3 3v0a2 2 0 0 1 2 2v0c0 1.1.9 2 2 2v0a2 2 0 0 0 2-2v0c0-1.1.9-2 2-2h3.17",key:"1fi5u6"}],["path",{d:"M11 21.95V18a2 2 0 0 0-2-2v0a2 2 0 0 1-2-2v-1a2 2 0 0 0-2-2H2.05",key:"xsiumc"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var f=a(29752),v=a(3594),y=a(78062),j=a(6260),k=a(82631),b=a(56627);let g=()=>{let[e,s]=(0,d.useState)(null),{business_id:a}=(0,h.useParams)();return((0,d.useEffect)(()=>{a&&(async()=>{try{let e=await j.b.get(`/business/${a}`);s(e.data)}catch(e){console.error("Error fetching profile data:",e),(0,b.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}})()},[a]),e)?(0,t.jsxs)(f.Zb,{className:"w-full h-full max-w-4xl mx-auto p-6",children:[(0,t.jsxs)(f.Ol,{className:"flex items-center justify-center",children:[t.jsx("div",{children:(0,t.jsxs)(v.Avatar,{children:[t.jsx(v.AvatarImage,{src:e.profileImage||"/default-image.png",alt:e.companyName}),(0,t.jsxs)(v.AvatarFallback,{children:[e.firstName[0],e.lastName[0]]})]})}),(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-1",children:[t.jsx(f.ll,{className:"text-xl font-bold",children:e.companyName}),t.jsx("p",{className:"text-sm font-bold",children:e.position}),t.jsx("p",{className:"text-sm text-gray-500",children:`${e.firstName} ${e.lastName}`})]})]}),(0,t.jsxs)(f.aY,{className:"flex justify-around items-center pb-4 mb-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("p",{className:"text-xl font-semibold",children:e.connects}),t.jsx("p",{className:"text-xs",children:"Connects"})]}),t.jsx(y.Separator,{orientation:"vertical",className:"h-6 bg-gray-400"}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("p",{className:"text-xl font-semibold",children:e.ProjectList.length}),t.jsx("p",{className:"text-xs",children:"Projects"})]}),t.jsx(y.Separator,{orientation:"vertical",className:"h-6 bg-gray-400"}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("p",{className:"text-xl font-semibold",children:e.companySize}),t.jsx("p",{className:"text-xs",children:"Company Size"})]})]}),(0,t.jsxs)(f.aY,{className:"flex flex-col items-center space-y-4",children:[t.jsx("h3",{className:"text-lg font-bold text-center",children:"Contact Information"}),(0,t.jsxs)("div",{className:"flex justify-between w-full",children:[(0,t.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,t.jsxs)(k.u,{children:[t.jsx(k.aJ,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(x.Z,{className:"mr-1 h-5"}),t.jsx("a",{href:`mailto:${e.email}`,className:"text-blue-600 ml-1",children:e.email})]})}),t.jsx(k._v,{side:"left",children:"Email address for contact"})]}),(0,t.jsxs)(k.u,{children:[t.jsx(k.aJ,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(p.Z,{className:"mr-1 h-5"}),e.phone]})}),t.jsx(k._v,{side:"left",children:"Contact via phone"})]})]}),(0,t.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,t.jsxs)(k.u,{children:[t.jsx(k.aJ,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(m.Z,{className:"mr-1 h-5"}),t.jsx("a",{href:e.linkedin,className:"text-blue-600 ml-1",children:e.linkedin})]})}),t.jsx(k._v,{side:"left",children:"View LinkedIn profile"})]}),(0,t.jsxs)(k.u,{children:[t.jsx(k.aJ,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(u,{className:"mr-1 h-5"}),t.jsx("a",{href:e.personalWebsite,className:"text-blue-600 ml-1",children:e.personalWebsite})]})}),t.jsx(k._v,{side:"left",children:"Visit website"})]})]})]})]})]}):t.jsx("p",{children:"Loading..."})};function N(){return(0,t.jsxs)("div",{className:"flex min-h-screen w-full flex-col ",children:[t.jsx(l.Z,{menuItemsTop:o.yn,menuItemsBottom:o.$C,active:"Market"}),(0,t.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,t.jsxs)("header",{className:"sticky top-0 z-30 flex h-14 items-center gap-4 border-b bg-background px-4  sm:border-0  sm:px-6",children:[t.jsx(n.Z,{menuItemsTop:o.yn,menuItemsBottom:o.$C,active:"Market"}),t.jsx(i.Z,{items:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Freelancer Market",link:"#"}]}),t.jsx("div",{className:"relative ml-auto flex-1 md:grow-0",children:t.jsx(r.o,{className:"w-full md:w-[200px] lg:w-[336px]"})}),t.jsx(c.Z,{})]}),t.jsx("main",{className:"",children:t.jsx(g,{})})]})]})}},3512:(e,s,a)=>{"use strict";a.d(s,{o:()=>i});var t=a(10326),r=a(88307),l=a(41190);function i({placeholder:e="Search...",className:s=""}){return(0,t.jsxs)("div",{className:`relative ${s}`,children:[t.jsx(r.Z,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),t.jsx(l.I,{type:"search",placeholder:e,className:"w-full rounded-lg bg-background pl-8 md:w-[200px] lg:w-[336px]"})]})}},78062:(e,s,a)=>{"use strict";a.r(s),a.d(s,{Separator:()=>n});var t=a(10326),r=a(17577),l=a(90220),i=a(51223);let n=r.forwardRef(({className:e,orientation:s="horizontal",decorative:a=!0,...r},n)=>t.jsx(l.f,{ref:n,decorative:a,orientation:s,className:(0,i.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));n.displayName=l.f.displayName},48586:(e,s,a)=>{"use strict";a.d(s,{yL:()=>b,$C:()=>k,yn:()=>j});var t=a(10326),r=a(95920),l=a(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,l.Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]]),n=(0,l.Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);var c=a(43727);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,l.Z)("TabletSmartphone",[["rect",{width:"10",height:"14",x:"3",y:"8",rx:"2",key:"1vrsiq"}],["path",{d:"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4",key:"1j4zmg"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),d=(0,l.Z)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]]);var h=a(60763);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let x=(0,l.Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);var p=a(40617),m=a(69515),u=a(88378),f=a(40900),v=a(98091),y=a(46226);let j=[{href:"#",icon:t.jsx(y.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:t.jsx(r.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:t.jsx(i,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:t.jsx(n,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:t.jsx(c.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:t.jsx(o,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:t.jsx(d,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:t.jsx(h.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:t.jsx(x,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:t.jsx(p.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:t.jsx(m.Z,{className:"h-5 w-5"}),label:"Notes"}],k=[{href:"/freelancer/settings/personal-info",icon:t.jsx(u.Z,{className:"h-5 w-5"}),label:"Settings"}];y.default,r.Z,m.Z,f.Z,v.Z;let b=[{href:"#",icon:t.jsx(y.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:t.jsx(r.Z,{className:"h-5 w-5"}),label:"Home"}]},52799:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var t=a(68570);let r=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\market\[business_id]\page.tsx`),{__esModule:l,$$typeof:i}=r;r.default;let n=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\market\[business_id]\page.tsx#default`)},90220:(e,s,a)=>{"use strict";a.d(s,{f:()=>o});var t=a(17577),r=a(77335),l=a(10326),i="horizontal",n=["horizontal","vertical"],c=t.forwardRef((e,s)=>{let{decorative:a,orientation:t=i,...c}=e,o=n.includes(t)?t:i;return(0,l.jsx)(r.WV.div,{"data-orientation":o,...a?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...c,ref:s})});c.displayName="Separator";var o=c}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,4736,6499,8066],()=>a(23148));module.exports=t})();