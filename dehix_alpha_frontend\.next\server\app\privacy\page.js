(()=>{var e={};e.id=385,e.ids=[385],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71396:e=>{"use strict";e.exports=require("undici")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},71794:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>d,originalPathname:()=>l,pages:()=>u,routeModule:()=>x,tree:()=>p}),t(23852),t(54302),t(12523);var s=t(23191),i=t(88716),a=t(37922),o=t.n(a),n=t(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(r,c);let p=["",{children:["privacy",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,23852)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\privacy\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\privacy\\page.tsx"],l="/privacy/page",d={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/privacy/page",pathname:"/privacy",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},35303:()=>{},23852:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(19510);t(71159);var i=t(93804);let a=()=>s.jsx("div",{className:"flex justify-center items-center min-h-screen",children:(0,s.jsxs)(i.Zb,{className:"w-full max-w-lg p-6 shadow-lg",children:[s.jsx(i.Ol,{children:s.jsx("p",{className:"text-center text-xl font-semibold",children:"Page Under Development"})}),s.jsx(i.SZ,{children:s.jsx("p",{className:"text-center text-gray-600",children:"We're working hard to bring this page to you. Stay tuned for updates!"})})]})})},93804:(e,r,t)=>{"use strict";t.d(r,{Ol:()=>n,SZ:()=>p,Zb:()=>o,aY:()=>u,eW:()=>l,ll:()=>c});var s=t(19510),i=t(71159),a=t(3632);let o=i.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));o.displayName="Card";let n=i.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...r}));n.displayName="CardHeader";let c=i.forwardRef(({className:e,...r},t)=>s.jsx("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));c.displayName="CardTitle";let p=i.forwardRef(({className:e,...r},t)=>s.jsx("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",e),...r}));p.displayName="CardDescription";let u=i.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,a.cn)("p-6 pt-0",e),...r}));u.displayName="CardContent";let l=i.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",e),...r}));l.displayName="CardFooter"},3632:(e,r,t)=>{"use strict";t.d(r,{cn:()=>d});var s=t(55761),i=t(62386),a=t(19095);t(13237);let o=t(29712).Z.create({baseURL:"http://127.0.0.1:8080/"});console.log("Base URL:","http://127.0.0.1:8080/"),o.interceptors.request.use(e=>(console.log("Request config:",e),e),e=>(console.error("Request error:",e),Promise.reject(e))),o.interceptors.response.use(e=>(console.log("Response:",e.data),e),e=>(console.error("Response error:",e),Promise.reject(e)));var n=t(29362),c=t(85500),p=t(93820),u=t(38192);let l=(0,n.ZF)({apiKey:"AIzaSyBPTH9xikAUkgGof048klY6WGiZSmRoXXA",authDomain:"dehix-6c349.firebaseapp.com",databaseURL:"https://dehix-6c349-default-rtdb.firebaseio.com",projectId:"dehix-6c349",storageBucket:"dehix-6c349.appspot.com",messagingSenderId:"521082542540",appId:"1:521082542540:web:543857e713038c2927a569"});function d(...e){return(0,i.m6)((0,s.W)(e))}(0,p.ad)(l),(0,a.v0)(l).useDeviceLanguage(),new a.hJ,(0,c.N8)(l),(0,u.cF)(l)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,4198,8344,4736],()=>t(71794));module.exports=s})();