"use strict";exports.id=7926,exports.ids=[7926],exports.modules={47206:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},30361:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("CircleCheckBig",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},43810:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},39447:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},15919:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},9664:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},83855:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},76993:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},69508:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},59819:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},57793:(e,t,n)=>{n.d(t,{VY:()=>el,h4:()=>eo,ck:()=>er,fC:()=>en,xz:()=>ei});var r=n(17577),o=n(93095),i=n(73866),l=n(48051),u=n(82561),a=n(52067),c=n(77335),s=n(65819),d=n(9815),f=n(88957),p=n(10326),m="Collapsible",[v,h]=(0,o.b)(m),[g,y]=v(m),b=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,open:o,defaultOpen:i,disabled:l,onOpenChange:u,...s}=e,[d=!1,m]=(0,a.T)({prop:o,defaultProp:i,onChange:u});return(0,p.jsx)(g,{scope:n,disabled:l,contentId:(0,f.M)(),open:d,onOpenToggle:r.useCallback(()=>m(e=>!e),[m]),children:(0,p.jsx)(c.WV.div,{"data-state":S(d),"data-disabled":l?"":void 0,...s,ref:t})})});b.displayName=m;var E="CollapsibleTrigger",w=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,...r}=e,o=y(E,n);return(0,p.jsx)(c.WV.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":S(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...r,ref:t,onClick:(0,u.M)(e.onClick,o.onOpenToggle)})});w.displayName=E;var x="CollapsibleContent",C=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=y(x,e.__scopeCollapsible);return(0,p.jsx)(d.z,{present:n||o.open,children:({present:e})=>(0,p.jsx)(k,{...r,ref:t,present:e})})});C.displayName=x;var k=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,present:o,children:i,...u}=e,a=y(x,n),[d,f]=r.useState(o),m=r.useRef(null),v=(0,l.e)(t,m),h=r.useRef(0),g=h.current,b=r.useRef(0),E=b.current,w=a.open||d,C=r.useRef(w),k=r.useRef();return r.useEffect(()=>{let e=requestAnimationFrame(()=>C.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.b)(()=>{let e=m.current;if(e){k.current=k.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();h.current=t.height,b.current=t.width,C.current||(e.style.transitionDuration=k.current.transitionDuration,e.style.animationName=k.current.animationName),f(o)}},[a.open,o]),(0,p.jsx)(c.WV.div,{"data-state":S(a.open),"data-disabled":a.disabled?"":void 0,id:a.contentId,hidden:!w,...u,ref:v,style:{"--radix-collapsible-content-height":g?`${g}px`:void 0,"--radix-collapsible-content-width":E?`${E}px`:void 0,...e.style},children:w&&i})});function S(e){return e?"open":"closed"}var R=n(17124),D="Accordion",O=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[P,A,N]=(0,i.B)(D),[I,M]=(0,o.b)(D,[N,h]),T=h(),L=r.forwardRef((e,t)=>{let{type:n,...r}=e;return(0,p.jsx)(P.Provider,{scope:e.__scopeAccordion,children:"multiple"===n?(0,p.jsx)($,{...r,ref:t}):(0,p.jsx)(U,{...r,ref:t})})});L.displayName=D;var[F,j]=I(D),[_,V]=I(D,{collapsible:!1}),U=r.forwardRef((e,t)=>{let{value:n,defaultValue:o,onValueChange:i=()=>{},collapsible:l=!1,...u}=e,[c,s]=(0,a.T)({prop:n,defaultProp:o,onChange:i});return(0,p.jsx)(F,{scope:e.__scopeAccordion,value:c?[c]:[],onItemOpen:s,onItemClose:r.useCallback(()=>l&&s(""),[l,s]),children:(0,p.jsx)(_,{scope:e.__scopeAccordion,collapsible:l,children:(0,p.jsx)(z,{...u,ref:t})})})}),$=r.forwardRef((e,t)=>{let{value:n,defaultValue:o,onValueChange:i=()=>{},...l}=e,[u=[],c]=(0,a.T)({prop:n,defaultProp:o,onChange:i}),s=r.useCallback(e=>c((t=[])=>[...t,e]),[c]),d=r.useCallback(e=>c((t=[])=>t.filter(t=>t!==e)),[c]);return(0,p.jsx)(F,{scope:e.__scopeAccordion,value:u,onItemOpen:s,onItemClose:d,children:(0,p.jsx)(_,{scope:e.__scopeAccordion,collapsible:!0,children:(0,p.jsx)(z,{...l,ref:t})})})}),[H,Z]=I(D),z=r.forwardRef((e,t)=>{let{__scopeAccordion:n,disabled:o,dir:i,orientation:a="vertical",...s}=e,d=r.useRef(null),f=(0,l.e)(d,t),m=A(n),v="ltr"===(0,R.gm)(i),h=(0,u.M)(e.onKeyDown,e=>{if(!O.includes(e.key))return;let t=e.target,n=m().filter(e=>!e.ref.current?.disabled),r=n.findIndex(e=>e.ref.current===t),o=n.length;if(-1===r)return;e.preventDefault();let i=r,l=o-1,u=()=>{(i=r+1)>l&&(i=0)},c=()=>{(i=r-1)<0&&(i=l)};switch(e.key){case"Home":i=0;break;case"End":i=l;break;case"ArrowRight":"horizontal"===a&&(v?u():c());break;case"ArrowDown":"vertical"===a&&u();break;case"ArrowLeft":"horizontal"===a&&(v?c():u());break;case"ArrowUp":"vertical"===a&&c()}let s=i%o;n[s].ref.current?.focus()});return(0,p.jsx)(H,{scope:n,disabled:o,direction:i,orientation:a,children:(0,p.jsx)(P.Slot,{scope:n,children:(0,p.jsx)(c.WV.div,{...s,"data-orientation":a,ref:f,onKeyDown:o?void 0:h})})})}),W="AccordionItem",[B,q]=I(W),K=r.forwardRef((e,t)=>{let{__scopeAccordion:n,value:r,...o}=e,i=Z(W,n),l=j(W,n),u=T(n),a=(0,f.M)(),c=r&&l.value.includes(r)||!1,s=i.disabled||e.disabled;return(0,p.jsx)(B,{scope:n,open:c,disabled:s,triggerId:a,children:(0,p.jsx)(b,{"data-orientation":i.orientation,"data-state":et(c),...u,...o,ref:t,disabled:s,open:c,onOpenChange:e=>{e?l.onItemOpen(r):l.onItemClose(r)}})})});K.displayName=W;var Y="AccordionHeader",X=r.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,o=Z(D,n),i=q(Y,n);return(0,p.jsx)(c.WV.h3,{"data-orientation":o.orientation,"data-state":et(i.open),"data-disabled":i.disabled?"":void 0,...r,ref:t})});X.displayName=Y;var J="AccordionTrigger",G=r.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,o=Z(D,n),i=q(J,n),l=V(J,n),u=T(n);return(0,p.jsx)(P.ItemSlot,{scope:n,children:(0,p.jsx)(w,{"aria-disabled":i.open&&!l.collapsible||void 0,"data-orientation":o.orientation,id:i.triggerId,...u,...r,ref:t})})});G.displayName=J;var Q="AccordionContent",ee=r.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,o=Z(D,n),i=q(Q,n),l=T(n);return(0,p.jsx)(C,{role:"region","aria-labelledby":i.triggerId,"data-orientation":o.orientation,...l,...r,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function et(e){return e?"open":"closed"}ee.displayName=Q;var en=L,er=K,eo=X,ei=G,el=ee},97185:(e,t,n)=>{n.d(t,{VY:()=>N,fC:()=>P,xz:()=>A});var r,o=n(17577),i=n(82561),l=n(93095),u=n(52067),a=n(48051),c=n(17103),s=(n(83078),n(9815)),d=n(77335),f=n(825),p=n(10326),m="HoverCard",[v,h]=(0,l.b)(m,[c.D7]),g=(0,c.D7)(),[y,b]=v(m),E=e=>{let{__scopeHoverCard:t,children:n,open:r,defaultOpen:i,onOpenChange:l,openDelay:a=700,closeDelay:s=300}=e,d=g(t),f=o.useRef(0),m=o.useRef(0),v=o.useRef(!1),h=o.useRef(!1),[b=!1,E]=(0,u.T)({prop:r,defaultProp:i,onChange:l}),w=o.useCallback(()=>{clearTimeout(m.current),f.current=window.setTimeout(()=>E(!0),a)},[a,E]),x=o.useCallback(()=>{clearTimeout(f.current),v.current||h.current||(m.current=window.setTimeout(()=>E(!1),s))},[s,E]),C=o.useCallback(()=>E(!1),[E]);return o.useEffect(()=>()=>{clearTimeout(f.current),clearTimeout(m.current)},[]),(0,p.jsx)(y,{scope:t,open:b,onOpenChange:E,onOpen:w,onClose:x,onDismiss:C,hasSelectionRef:v,isPointerDownOnContentRef:h,children:(0,p.jsx)(c.fC,{...d,children:n})})};E.displayName=m;var w="HoverCardTrigger",x=o.forwardRef((e,t)=>{let{__scopeHoverCard:n,...r}=e,o=b(w,n),l=g(n);return(0,p.jsx)(c.ee,{asChild:!0,...l,children:(0,p.jsx)(d.WV.a,{"data-state":o.open?"open":"closed",...r,ref:t,onPointerEnter:(0,i.M)(e.onPointerEnter,O(o.onOpen)),onPointerLeave:(0,i.M)(e.onPointerLeave,O(o.onClose)),onFocus:(0,i.M)(e.onFocus,o.onOpen),onBlur:(0,i.M)(e.onBlur,o.onClose),onTouchStart:(0,i.M)(e.onTouchStart,e=>e.preventDefault())})})});x.displayName=w;var[C,k]=v("HoverCardPortal",{forceMount:void 0}),S="HoverCardContent",R=o.forwardRef((e,t)=>{let n=k(S,e.__scopeHoverCard),{forceMount:r=n.forceMount,...o}=e,l=b(S,e.__scopeHoverCard);return(0,p.jsx)(s.z,{present:r||l.open,children:(0,p.jsx)(D,{"data-state":l.open?"open":"closed",...o,onPointerEnter:(0,i.M)(e.onPointerEnter,O(l.onOpen)),onPointerLeave:(0,i.M)(e.onPointerLeave,O(l.onClose)),ref:t})})});R.displayName=S;var D=o.forwardRef((e,t)=>{let{__scopeHoverCard:n,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:s,onInteractOutside:d,...m}=e,v=b(S,n),h=g(n),y=o.useRef(null),E=(0,a.e)(t,y),[w,x]=o.useState(!1);return o.useEffect(()=>{if(w){let e=document.body;return r=e.style.userSelect||e.style.webkitUserSelect,e.style.userSelect="none",e.style.webkitUserSelect="none",()=>{e.style.userSelect=r,e.style.webkitUserSelect=r}}},[w]),o.useEffect(()=>{if(y.current){let e=()=>{x(!1),v.isPointerDownOnContentRef.current=!1,setTimeout(()=>{document.getSelection()?.toString()!==""&&(v.hasSelectionRef.current=!0)})};return document.addEventListener("pointerup",e),()=>{document.removeEventListener("pointerup",e),v.hasSelectionRef.current=!1,v.isPointerDownOnContentRef.current=!1}}},[v.isPointerDownOnContentRef,v.hasSelectionRef]),o.useEffect(()=>{y.current&&(function(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP});for(;n.nextNode();)t.push(n.currentNode);return t})(y.current).forEach(e=>e.setAttribute("tabindex","-1"))}),(0,p.jsx)(f.XB,{asChild:!0,disableOutsidePointerEvents:!1,onInteractOutside:d,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:(0,i.M)(s,e=>{e.preventDefault()}),onDismiss:v.onDismiss,children:(0,p.jsx)(c.VY,{...h,...m,onPointerDown:(0,i.M)(m.onPointerDown,e=>{e.currentTarget.contains(e.target)&&x(!0),v.hasSelectionRef.current=!1,v.isPointerDownOnContentRef.current=!0}),ref:E,style:{...m.style,userSelect:w?"text":void 0,WebkitUserSelect:w?"text":void 0,"--radix-hover-card-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-hover-card-content-available-width":"var(--radix-popper-available-width)","--radix-hover-card-content-available-height":"var(--radix-popper-available-height)","--radix-hover-card-trigger-width":"var(--radix-popper-anchor-width)","--radix-hover-card-trigger-height":"var(--radix-popper-anchor-height)"}})})});function O(e){return t=>"touch"===t.pointerType?void 0:e()}o.forwardRef((e,t)=>{let{__scopeHoverCard:n,...r}=e,o=g(n);return(0,p.jsx)(c.Eh,{...o,...r,ref:t})}).displayName="HoverCardArrow";var P=E,A=x,N=R},28260:(e,t,n)=>{let r;n.d(t,{mY:()=>e2});var o=/[\\\/_+.#"@\[\(\{&]/,i=/[\\\/_+.#"@\[\(\{&]/g,l=/[\s-]/,u=/[\s-]/g;function a(e){return e.toLowerCase().replace(u," ")}function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}var s=n(17577),d=n.t(s,2);function f(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(null==e||e(r),!1===n||!r.defaultPrevented)return null==t?void 0:t(r)}}function p(...e){return t=>e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}function m(...e){return(0,s.useCallback)(p(...e),e)}let v=(null==globalThis?void 0:globalThis.document)?s.useLayoutEffect:()=>{},h=d["useId".toString()]||(()=>void 0),g=0;function y(e){let[t,n]=s.useState(h());return v(()=>{e||n(e=>null!=e?e:String(g++))},[e]),e||(t?`radix-${t}`:"")}function b(e){let t=(0,s.useRef)(e);return(0,s.useEffect)(()=>{t.current=e}),(0,s.useMemo)(()=>(...e)=>{var n;return null===(n=t.current)||void 0===n?void 0:n.call(t,...e)},[])}var E=n(60962);let w=(0,s.forwardRef)((e,t)=>{let{children:n,...r}=e,o=s.Children.toArray(n),i=o.find(k);if(i){let e=i.props.children,n=o.map(t=>t!==i?t:s.Children.count(e)>1?s.Children.only(null):(0,s.isValidElement)(e)?e.props.children:null);return(0,s.createElement)(x,c({},r,{ref:t}),(0,s.isValidElement)(e)?(0,s.cloneElement)(e,void 0,n):null)}return(0,s.createElement)(x,c({},r,{ref:t}),n)});w.displayName="Slot";let x=(0,s.forwardRef)((e,t)=>{let{children:n,...r}=e;return(0,s.isValidElement)(n)?(0,s.cloneElement)(n,{...function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props),ref:t?p(t,n.ref):n.ref}):s.Children.count(n)>1?s.Children.only(null):null});x.displayName="SlotClone";let C=({children:e})=>(0,s.createElement)(s.Fragment,null,e);function k(e){return(0,s.isValidElement)(e)&&e.type===C}let S=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=(0,s.forwardRef)((e,n)=>{let{asChild:r,...o}=e,i=r?w:t;return(0,s.useEffect)(()=>{window[Symbol.for("radix-ui")]=!0},[]),(0,s.createElement)(i,c({},o,{ref:n}))});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),R="dismissableLayer.update",D=(0,s.createContext)({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),O=(0,s.forwardRef)((e,t)=>{var n;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:u,onInteractOutside:a,onDismiss:d,...p}=e,v=(0,s.useContext)(D),[h,g]=(0,s.useState)(null),y=null!==(n=null==h?void 0:h.ownerDocument)&&void 0!==n?n:null==globalThis?void 0:globalThis.document,[,E]=(0,s.useState)({}),w=m(t,e=>g(e)),x=Array.from(v.layers),[C]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),k=x.indexOf(C),O=h?x.indexOf(h):-1,N=v.layersWithOutsidePointerEventsDisabled.size>0,I=O>=k,M=function(e,t=null==globalThis?void 0:globalThis.document){let n=b(e),r=(0,s.useRef)(!1),o=(0,s.useRef)(()=>{});return(0,s.useEffect)(()=>{let e=e=>{if(e.target&&!r.current){let r={originalEvent:e};function i(){A("dismissableLayer.pointerDownOutside",n,r,{discrete:!0})}"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=i,t.addEventListener("click",o.current,{once:!0})):i()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...v.branches].some(e=>e.contains(t));!I||n||(null==l||l(e),null==a||a(e),e.defaultPrevented||null==d||d())},y),T=function(e,t=null==globalThis?void 0:globalThis.document){let n=b(e),r=(0,s.useRef)(!1);return(0,s.useEffect)(()=>{let e=e=>{e.target&&!r.current&&A("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...v.branches].some(e=>e.contains(t))||(null==u||u(e),null==a||a(e),e.defaultPrevented||null==d||d())},y);return function(e,t=null==globalThis?void 0:globalThis.document){let n=b(e);(0,s.useEffect)(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e),()=>t.removeEventListener("keydown",e)},[n,t])}(e=>{O!==v.layers.size-1||(null==i||i(e),!e.defaultPrevented&&d&&(e.preventDefault(),d()))},y),(0,s.useEffect)(()=>{if(h)return o&&(0===v.layersWithOutsidePointerEventsDisabled.size&&(r=y.body.style.pointerEvents,y.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(h)),v.layers.add(h),P(),()=>{o&&1===v.layersWithOutsidePointerEventsDisabled.size&&(y.body.style.pointerEvents=r)}},[h,y,o,v]),(0,s.useEffect)(()=>()=>{h&&(v.layers.delete(h),v.layersWithOutsidePointerEventsDisabled.delete(h),P())},[h,v]),(0,s.useEffect)(()=>{let e=()=>E({});return document.addEventListener(R,e),()=>document.removeEventListener(R,e)},[]),(0,s.createElement)(S.div,c({},p,{ref:w,style:{pointerEvents:N?I?"auto":"none":void 0,...e.style},onFocusCapture:f(e.onFocusCapture,T.onFocusCapture),onBlurCapture:f(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:f(e.onPointerDownCapture,M.onPointerDownCapture)}))});function P(){let e=new CustomEvent(R);document.dispatchEvent(e)}function A(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});(t&&o.addEventListener(e,t,{once:!0}),r)?o&&(0,E.flushSync)(()=>o.dispatchEvent(i)):o.dispatchEvent(i)}let N="focusScope.autoFocusOnMount",I="focusScope.autoFocusOnUnmount",M={bubbles:!1,cancelable:!0},T=(0,s.forwardRef)((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[u,a]=(0,s.useState)(null),d=b(o),f=b(i),p=(0,s.useRef)(null),v=m(t,e=>a(e)),h=(0,s.useRef)({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;(0,s.useEffect)(()=>{if(r){function e(e){if(h.paused||!u)return;let t=e.target;u.contains(t)?p.current=t:j(p.current,{select:!0})}function t(e){if(h.paused||!u)return;let t=e.relatedTarget;null===t||u.contains(t)||j(p.current,{select:!0})}document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&j(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,h.paused]),(0,s.useEffect)(()=>{if(u){_.add(h);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(N,M);u.addEventListener(N,d),u.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(j(r,{select:t}),document.activeElement!==n)return}(L(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&j(u))}return()=>{u.removeEventListener(N,d),setTimeout(()=>{let t=new CustomEvent(I,M);u.addEventListener(I,f),u.dispatchEvent(t),t.defaultPrevented||j(null!=e?e:document.body,{select:!0}),u.removeEventListener(I,f),_.remove(h)},0)}}},[u,d,f,h]);let g=(0,s.useCallback)(e=>{if(!n&&!r||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=L(e);return[F(t,e),F(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&j(i,{select:!0})):(e.preventDefault(),n&&j(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,h.paused]);return(0,s.createElement)(S.div,c({tabIndex:-1},l,{ref:v,onKeyDown:g}))});function L(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function F(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function j(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}let _=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=V(e,t)).unshift(t)},remove(t){var n;null===(n=(e=V(e,t))[0])||void 0===n||n.resume()}}}();function V(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}let U=(0,s.forwardRef)((e,t)=>{var n;let{container:r=null==globalThis?void 0:null===(n=globalThis.document)||void 0===n?void 0:n.body,...o}=e;return r?E.createPortal((0,s.createElement)(S.div,c({},o,{ref:t})),r):null}),$=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,o]=(0,s.useState)(),i=(0,s.useRef)({}),l=(0,s.useRef)(e),u=(0,s.useRef)("none"),[a,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},(0,s.useReducer)((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return(0,s.useEffect)(()=>{let e=H(i.current);u.current="mounted"===a?e:"none"},[a]),v(()=>{let t=i.current,n=l.current;if(n!==e){let r=u.current,o=H(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),l.current=e}},[e,c]),v(()=>{if(r){let e=e=>{let t=H(i.current).includes(e.animationName);e.target===r&&t&&(0,E.flushSync)(()=>c("ANIMATION_END"))},t=e=>{e.target===r&&(u.current=H(i.current))};return r.addEventListener("animationstart",t),r.addEventListener("animationcancel",e),r.addEventListener("animationend",e),()=>{r.removeEventListener("animationstart",t),r.removeEventListener("animationcancel",e),r.removeEventListener("animationend",e)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:(0,s.useCallback)(e=>{e&&(i.current=getComputedStyle(e)),o(e)},[])}}(t),o="function"==typeof n?n({present:r.isPresent}):s.Children.only(n),i=m(r.ref,o.ref);return"function"==typeof n||r.isPresent?(0,s.cloneElement)(o,{ref:i}):null};function H(e){return(null==e?void 0:e.animationName)||"none"}$.displayName="Presence";let Z=0;function z(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}var W=n(65826),B=n(32064),q=n(55745),K=(0,n(45305)._)(),Y=function(){},X=s.forwardRef(function(e,t){var n=s.useRef(null),r=s.useState({onScrollCapture:Y,onWheelCapture:Y,onTouchMoveCapture:Y}),o=r[0],i=r[1],l=e.forwardProps,u=e.children,a=e.className,c=e.removeScrollBar,d=e.enabled,f=e.shards,p=e.sideCar,m=e.noIsolation,v=e.inert,h=e.allowPinchZoom,g=e.as,y=(0,W._T)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),b=(0,q.q)([n,t]),E=(0,W.pi)((0,W.pi)({},y),o);return s.createElement(s.Fragment,null,d&&s.createElement(p,{sideCar:K,removeScrollBar:c,shards:f,noIsolation:m,inert:v,setCallbacks:i,allowPinchZoom:!!h,lockRef:n}),l?s.cloneElement(s.Children.only(u),(0,W.pi)((0,W.pi)({},E),{ref:b})):s.createElement(void 0===g?"div":g,(0,W.pi)({},E,{className:a,ref:b}),u))});X.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},X.classNames={fullWidth:B.zi,zeroRight:B.pF};var J=n(73943),G=n(3566),Q=n(98848),ee=!1;if("undefined"!=typeof window)try{var et=Object.defineProperty({},"passive",{get:function(){return ee=!0,!0}});window.addEventListener("test",et,et),window.removeEventListener("test",et,et)}catch(e){ee=!1}var en=!!ee&&{passive:!1},er=function(e,t){var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},eo=function(e,t){var n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),ei(e,n)){var r=el(e,n);if(r[1]>r[2])return!0}n=n.parentNode}while(n&&n!==document.body);return!1},ei=function(e,t){return"v"===e?er(t,"overflowY"):er(t,"overflowX")},el=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},eu=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),u=l*r,a=n.target,c=t.contains(a),s=!1,d=u>0,f=0,p=0;do{var m=el(e,a),v=m[0],h=m[1]-m[2]-l*v;(v||h)&&ei(e,a)&&(f+=h,p+=v),a=a.parentNode}while(!c&&a!==document.body||c&&(t.contains(a)||t===a));return d&&(o&&0===f||!o&&u>f)?s=!0:!d&&(o&&0===p||!o&&-u>p)&&(s=!0),s},ea=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ec=function(e){return[e.deltaX,e.deltaY]},es=function(e){return e&&"current"in e?e.current:e},ed=0,ef=[];let ep=(0,J.L)(K,function(e){var t=s.useRef([]),n=s.useRef([0,0]),r=s.useRef(),o=s.useState(ed++)[0],i=s.useState(function(){return(0,Q.Ws)()})[0],l=s.useRef(e);s.useEffect(function(){l.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,W.ev)([e.lockRef.current],(e.shards||[]).map(es),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=s.useCallback(function(e,t){if("touches"in e&&2===e.touches.length)return!l.current.allowPinchZoom;var o,i=ea(e),u=n.current,a="deltaX"in e?e.deltaX:u[0]-i[0],c="deltaY"in e?e.deltaY:u[1]-i[1],s=e.target,d=Math.abs(a)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=eo(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=eo(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(a||c)&&(r.current=o),!o)return!0;var p=r.current||o;return eu(p,t,e,"h"===p?a:c,!0)},[]),a=s.useCallback(function(e){if(ef.length&&ef[ef.length-1]===i){var n="deltaY"in e?ec(e):ea(e),r=t.current.filter(function(t){var r;return t.name===e.type&&t.target===e.target&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(es).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=s.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=s.useCallback(function(e){n.current=ea(e),r.current=void 0},[]),f=s.useCallback(function(t){c(t.type,ec(t),t.target,u(t,e.lockRef.current))},[]),p=s.useCallback(function(t){c(t.type,ea(t),t.target,u(t,e.lockRef.current))},[]);s.useEffect(function(){return ef.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",a,en),document.addEventListener("touchmove",a,en),document.addEventListener("touchstart",d,en),function(){ef=ef.filter(function(e){return e!==i}),document.removeEventListener("wheel",a,en),document.removeEventListener("touchmove",a,en),document.removeEventListener("touchstart",d,en)}},[]);var m=e.removeScrollBar,v=e.inert;return s.createElement(s.Fragment,null,v?s.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?s.createElement(G.jp,{gapMode:"margin"}):null)});var em=s.forwardRef(function(e,t){return s.createElement(X,(0,W.pi)({},e,{ref:t,sideCar:ep}))});em.classNames=X.classNames;var ev=n(35664);let eh="Dialog",[eg,ey]=function(e,t=[]){let n=[],r=()=>{let t=n.map(e=>(0,s.createContext)(e));return function(n){let r=(null==n?void 0:n[e])||t;return(0,s.useMemo)(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=(0,s.createContext)(r),i=n.length;function l(t){let{scope:n,children:r,...l}=t,u=(null==n?void 0:n[e][i])||o,a=(0,s.useMemo)(()=>l,Object.values(l));return(0,s.createElement)(u.Provider,{value:a},r)}return n=[...n,r],l.displayName=t+"Provider",[l,function(n,l){let u=(null==l?void 0:l[e][i])||o,a=(0,s.useContext)(u);if(a)return a;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return(0,s.useMemo)(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}(eh),[eb,eE]=eg(eh),ew="DialogPortal",[ex,eC]=eg(ew,{forceMount:void 0}),ek="DialogOverlay",eS=(0,s.forwardRef)((e,t)=>{let n=eC(ek,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=eE(ek,e.__scopeDialog);return i.modal?(0,s.createElement)($,{present:r||i.open},(0,s.createElement)(eR,c({},o,{ref:t}))):null}),eR=(0,s.forwardRef)((e,t)=>{let{__scopeDialog:n,...r}=e,o=eE(ek,n);return(0,s.createElement)(em,{as:w,allowPinchZoom:!0,shards:[o.contentRef]},(0,s.createElement)(S.div,c({"data-state":eI(o.open)},r,{ref:t,style:{pointerEvents:"auto",...r.style}})))}),eD="DialogContent",eO=(0,s.forwardRef)((e,t)=>{let n=eC(eD,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=eE(eD,e.__scopeDialog);return(0,s.createElement)($,{present:r||i.open},i.modal?(0,s.createElement)(eP,c({},o,{ref:t})):(0,s.createElement)(eA,c({},o,{ref:t})))}),eP=(0,s.forwardRef)((e,t)=>{let n=eE(eD,e.__scopeDialog),r=(0,s.useRef)(null),o=m(t,n.contentRef,r);return(0,s.useEffect)(()=>{let e=r.current;if(e)return(0,ev.Ry)(e)},[]),(0,s.createElement)(eN,c({},e,{ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:f(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:f(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:f(e.onFocusOutside,e=>e.preventDefault())}))}),eA=(0,s.forwardRef)((e,t)=>{let n=eE(eD,e.__scopeDialog),r=(0,s.useRef)(!1),o=(0,s.useRef)(!1);return(0,s.createElement)(eN,c({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var i,l;null===(i=e.onCloseAutoFocus)||void 0===i||i.call(e,t),t.defaultPrevented||(r.current||null===(l=n.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var i,l;null===(i=e.onInteractOutside)||void 0===i||i.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let u=t.target;(null===(l=n.triggerRef.current)||void 0===l?void 0:l.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}}))}),eN=(0,s.forwardRef)((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,...l}=e,u=eE(eD,n),a=m(t,(0,s.useRef)(null));return(0,s.useEffect)(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:z()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:z()),Z++,()=>{1===Z&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),Z--}},[]),(0,s.createElement)(s.Fragment,null,(0,s.createElement)(T,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i},(0,s.createElement)(O,c({role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":eI(u.open)},l,{ref:a,onDismiss:()=>u.onOpenChange(!1)}))),!1)});function eI(e){return e?"open":"closed"}let[eM,eT]=function(e,t){let n=(0,s.createContext)(t);function r(e){let{children:t,...r}=e,o=(0,s.useMemo)(()=>r,Object.values(r));return(0,s.createElement)(n.Provider,{value:o},t)}return r.displayName=e+"Provider",[r,function(r){let o=(0,s.useContext)(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}("DialogTitleWarning",{contentName:eD,titleName:"DialogTitle",docsSlug:"dialog"}),eL=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:i,modal:l=!0}=e,u=(0,s.useRef)(null),a=(0,s.useRef)(null),[c=!1,d]=function({prop:e,defaultProp:t,onChange:n=()=>{}}){let[r,o]=function({defaultProp:e,onChange:t}){let n=(0,s.useState)(e),[r]=n,o=(0,s.useRef)(r),i=b(t);return(0,s.useEffect)(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}({defaultProp:t,onChange:n}),i=void 0!==e,l=i?e:r,u=b(n);return[l,(0,s.useCallback)(t=>{if(i){let n="function"==typeof t?t(e):t;n!==e&&u(n)}else o(t)},[i,e,o,u])]}({prop:r,defaultProp:o,onChange:i});return(0,s.createElement)(eb,{scope:t,triggerRef:u,contentRef:a,contentId:y(),titleId:y(),descriptionId:y(),open:c,onOpenChange:d,onOpenToggle:(0,s.useCallback)(()=>d(e=>!e),[d]),modal:l},n)},eF=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:o}=e,i=eE(ew,t);return(0,s.createElement)(ex,{scope:t,forceMount:n},s.Children.map(r,e=>(0,s.createElement)($,{present:n||i.open},(0,s.createElement)(U,{asChild:!0,container:o},e))))};var ej='[cmdk-group=""]',e_='[cmdk-group-items=""]',eV='[cmdk-item=""]',eU=`${eV}:not([aria-disabled="true"])`,e$="cmdk-item-select",eH="data-value",eZ=(e,t,n)=>(function(e,t,n){return function e(t,n,r,a,c,s,d){if(s===n.length)return c===t.length?1:.99;var f=`${c},${s}`;if(void 0!==d[f])return d[f];for(var p,m,v,h,g=a.charAt(s),y=r.indexOf(g,c),b=0;y>=0;)(p=e(t,n,r,a,y+1,s+1,d))>b&&(y===c?p*=1:o.test(t.charAt(y-1))?(p*=.8,(v=t.slice(c,y-1).match(i))&&c>0&&(p*=Math.pow(.999,v.length))):l.test(t.charAt(y-1))?(p*=.9,(h=t.slice(c,y-1).match(u))&&c>0&&(p*=Math.pow(.999,h.length))):(p*=.17,c>0&&(p*=Math.pow(.999,y-c))),t.charAt(y)!==n.charAt(s)&&(p*=.9999)),(p<.1&&r.charAt(y-1)===a.charAt(s+1)||a.charAt(s+1)===a.charAt(s)&&r.charAt(y-1)!==a.charAt(s))&&.1*(m=e(t,n,r,a,y+1,s+2,d))>p&&(p=.1*m),p>b&&(b=p),y=r.indexOf(g,y+1);return d[f]=b,b}(e=n&&n.length>0?`${e+" "+n.join(" ")}`:e,t,a(e),a(t),0,0,{})})(e,t,n),ez=s.createContext(void 0),eW=()=>s.useContext(ez),eB=s.createContext(void 0),eq=()=>s.useContext(eB),eK=s.createContext(void 0),eY=s.forwardRef((e,t)=>{let n=e9(()=>{var t,n;return{search:"",value:null!=(n=null!=(t=e.value)?t:e.defaultValue)?n:"",filtered:{count:0,items:new Map,groups:new Set}}}),r=e9(()=>new Set),o=e9(()=>new Map),i=e9(()=>new Map),l=e9(()=>new Set),u=e5(e),{label:a,children:c,value:d,onValueChange:f,filter:p,shouldFilter:m,loop:v,disablePointerSelection:h=!1,vimBindings:g=!0,...y}=e,b=s.useId(),E=s.useId(),w=s.useId(),x=s.useRef(null),C=e6();e8(()=>{if(void 0!==d){let e=d.trim();n.current.value=e,k.emit()}},[d]),e8(()=>{C(6,N)},[]);let k=s.useMemo(()=>({subscribe:e=>(l.current.add(e),()=>l.current.delete(e)),snapshot:()=>n.current,setState:(e,t,r)=>{var o,i,l;if(!Object.is(n.current[e],t)){if(n.current[e]=t,"search"===e)A(),O(),C(1,P);else if("value"===e&&(r||C(5,N),(null==(o=u.current)?void 0:o.value)!==void 0)){null==(l=(i=u.current).onValueChange)||l.call(i,null!=t?t:"");return}k.emit()}},emit:()=>{l.current.forEach(e=>e())}}),[]),R=s.useMemo(()=>({value:(e,t,r)=>{var o;t!==(null==(o=i.current.get(e))?void 0:o.value)&&(i.current.set(e,{value:t,keywords:r}),n.current.filtered.items.set(e,D(t,r)),C(2,()=>{O(),k.emit()}))},item:(e,t)=>(r.current.add(e),t&&(o.current.has(t)?o.current.get(t).add(e):o.current.set(t,new Set([e]))),C(3,()=>{A(),O(),n.current.value||P(),k.emit()}),()=>{i.current.delete(e),r.current.delete(e),n.current.filtered.items.delete(e);let t=I();C(4,()=>{A(),(null==t?void 0:t.getAttribute("id"))===e&&P(),k.emit()})}),group:e=>(o.current.has(e)||o.current.set(e,new Set),()=>{i.current.delete(e),o.current.delete(e)}),filter:()=>u.current.shouldFilter,label:a||e["aria-label"],disablePointerSelection:h,listId:b,inputId:w,labelId:E,listInnerRef:x}),[]);function D(e,t){var r,o;let i=null!=(o=null==(r=u.current)?void 0:r.filter)?o:eZ;return e?i(e,n.current.search,t):0}function O(){if(!n.current.search||!1===u.current.shouldFilter)return;let e=n.current.filtered.items,t=[];n.current.filtered.groups.forEach(n=>{let r=o.current.get(n),i=0;r.forEach(t=>{i=Math.max(e.get(t),i)}),t.push([n,i])});let r=x.current;M().sort((t,n)=>{var r,o;let i=t.getAttribute("id"),l=n.getAttribute("id");return(null!=(r=e.get(l))?r:0)-(null!=(o=e.get(i))?o:0)}).forEach(e=>{let t=e.closest(e_);t?t.appendChild(e.parentElement===t?e:e.closest(`${e_} > *`)):r.appendChild(e.parentElement===r?e:e.closest(`${e_} > *`))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{let t=x.current.querySelector(`${ej}[${eH}="${encodeURIComponent(e[0])}"]`);null==t||t.parentElement.appendChild(t)})}function P(){let e=M().find(e=>"true"!==e.getAttribute("aria-disabled")),t=null==e?void 0:e.getAttribute(eH);k.setState("value",t||void 0)}function A(){var e,t,l,a;if(!n.current.search||!1===u.current.shouldFilter){n.current.filtered.count=r.current.size;return}n.current.filtered.groups=new Set;let c=0;for(let o of r.current){let r=D(null!=(t=null==(e=i.current.get(o))?void 0:e.value)?t:"",null!=(a=null==(l=i.current.get(o))?void 0:l.keywords)?a:[]);n.current.filtered.items.set(o,r),r>0&&c++}for(let[e,t]of o.current)for(let r of t)if(n.current.filtered.items.get(r)>0){n.current.filtered.groups.add(e);break}n.current.filtered.count=c}function N(){var e,t,n;let r=I();r&&((null==(e=r.parentElement)?void 0:e.firstChild)===r&&(null==(n=null==(t=r.closest(ej))?void 0:t.querySelector('[cmdk-group-heading=""]'))||n.scrollIntoView({block:"nearest"})),r.scrollIntoView({block:"nearest"}))}function I(){var e;return null==(e=x.current)?void 0:e.querySelector(`${eV}[aria-selected="true"]`)}function M(){var e;return Array.from(null==(e=x.current)?void 0:e.querySelectorAll(eU))}function T(e){let t=M()[e];t&&k.setState("value",t.getAttribute(eH))}function L(e){var t;let n=I(),r=M(),o=r.findIndex(e=>e===n),i=r[o+e];null!=(t=u.current)&&t.loop&&(i=o+e<0?r[r.length-1]:o+e===r.length?r[0]:r[o+e]),i&&k.setState("value",i.getAttribute(eH))}function F(e){let t=I(),n=null==t?void 0:t.closest(ej),r;for(;n&&!r;)r=null==(n=e>0?function(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return n;n=n.nextElementSibling}}(n,ej):function(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return n;n=n.previousElementSibling}}(n,ej))?void 0:n.querySelector(eU);r?k.setState("value",r.getAttribute(eH)):L(e)}let j=()=>T(M().length-1),_=e=>{e.preventDefault(),e.metaKey?j():e.altKey?F(1):L(1)},V=e=>{e.preventDefault(),e.metaKey?T(0):e.altKey?F(-1):L(-1)};return s.createElement(S.div,{ref:t,tabIndex:-1,...y,"cmdk-root":"",onKeyDown:e=>{var t;if(null==(t=y.onKeyDown)||t.call(y,e),!e.defaultPrevented)switch(e.key){case"n":case"j":g&&e.ctrlKey&&_(e);break;case"ArrowDown":_(e);break;case"p":case"k":g&&e.ctrlKey&&V(e);break;case"ArrowUp":V(e);break;case"Home":e.preventDefault(),T(0);break;case"End":e.preventDefault(),j();break;case"Enter":if(!e.nativeEvent.isComposing&&229!==e.keyCode){e.preventDefault();let t=I();if(t){let e=new Event(e$);t.dispatchEvent(e)}}}}},s.createElement("label",{"cmdk-label":"",htmlFor:R.inputId,id:R.labelId,style:tt},a),te(e,e=>s.createElement(eB.Provider,{value:k},s.createElement(ez.Provider,{value:R},e))))}),eX=s.forwardRef((e,t)=>{var n,r;let o=s.useId(),i=s.useRef(null),l=s.useContext(eK),u=eW(),a=e5(e),c=null!=(r=null==(n=a.current)?void 0:n.forceMount)?r:null==l?void 0:l.forceMount;e8(()=>{if(!c)return u.item(o,null==l?void 0:l.id)},[c]);let d=e4(o,i,[e.value,e.children,i],e.keywords),f=eq(),p=e7(e=>e.value&&e.value===d.current),m=e7(e=>!!c||!1===u.filter()||!e.search||e.filtered.items.get(o)>0);function v(){var e,t;h(),null==(t=(e=a.current).onSelect)||t.call(e,d.current)}function h(){f.setState("value",d.current,!0)}if(s.useEffect(()=>{let t=i.current;if(!(!t||e.disabled))return t.addEventListener(e$,v),()=>t.removeEventListener(e$,v)},[m,e.onSelect,e.disabled]),!m)return null;let{disabled:g,value:y,onSelect:b,forceMount:E,keywords:w,...x}=e;return s.createElement(S.div,{ref:e3([i,t]),...x,id:o,"cmdk-item":"",role:"option","aria-disabled":!!g,"aria-selected":!!p,"data-disabled":!!g,"data-selected":!!p,onPointerMove:g||u.disablePointerSelection?void 0:h,onClick:g?void 0:v},e.children)}),eJ=s.forwardRef((e,t)=>{let{heading:n,children:r,forceMount:o,...i}=e,l=s.useId(),u=s.useRef(null),a=s.useRef(null),c=s.useId(),d=eW(),f=e7(e=>!!o||!1===d.filter()||!e.search||e.filtered.groups.has(l));e8(()=>d.group(l),[]),e4(l,u,[e.value,e.heading,a]);let p=s.useMemo(()=>({id:l,forceMount:o}),[o]);return s.createElement(S.div,{ref:e3([u,t]),...i,"cmdk-group":"",role:"presentation",hidden:!f||void 0},n&&s.createElement("div",{ref:a,"cmdk-group-heading":"","aria-hidden":!0,id:c},n),te(e,e=>s.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":n?c:void 0},s.createElement(eK.Provider,{value:p},e))))}),eG=s.forwardRef((e,t)=>{let{alwaysRender:n,...r}=e,o=s.useRef(null),i=e7(e=>!e.search);return n||i?s.createElement(S.div,{ref:e3([o,t]),...r,"cmdk-separator":"",role:"separator"}):null}),eQ=s.forwardRef((e,t)=>{let{onValueChange:n,...r}=e,o=null!=e.value,i=eq(),l=e7(e=>e.search),u=e7(e=>e.value),a=eW(),c=s.useMemo(()=>{var e;let t=null==(e=a.listInnerRef.current)?void 0:e.querySelector(`${eV}[${eH}="${encodeURIComponent(u)}"]`);return null==t?void 0:t.getAttribute("id")},[]);return s.useEffect(()=>{null!=e.value&&i.setState("search",e.value)},[e.value]),s.createElement(S.input,{ref:t,...r,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":a.listId,"aria-labelledby":a.labelId,"aria-activedescendant":c,id:a.inputId,type:"text",value:o?e.value:l,onChange:e=>{o||i.setState("search",e.target.value),null==n||n(e.target.value)}})}),e0=s.forwardRef((e,t)=>{let{children:n,label:r="Suggestions",...o}=e,i=s.useRef(null),l=s.useRef(null),u=eW();return s.useEffect(()=>{if(l.current&&i.current){let e=l.current,t=i.current,n,r=new ResizeObserver(()=>{n=requestAnimationFrame(()=>{let n=e.offsetHeight;t.style.setProperty("--cmdk-list-height",n.toFixed(1)+"px")})});return r.observe(e),()=>{cancelAnimationFrame(n),r.unobserve(e)}}},[]),s.createElement(S.div,{ref:e3([i,t]),...o,"cmdk-list":"",role:"listbox","aria-label":r,id:u.listId},te(e,e=>s.createElement("div",{ref:e3([l,u.listInnerRef]),"cmdk-list-sizer":""},e)))}),e1=s.forwardRef((e,t)=>{let{open:n,onOpenChange:r,overlayClassName:o,contentClassName:i,container:l,...u}=e;return s.createElement(eL,{open:n,onOpenChange:r},s.createElement(eF,{container:l},s.createElement(eS,{"cmdk-overlay":"",className:o}),s.createElement(eO,{"aria-label":e.label,"cmdk-dialog":"",className:i},s.createElement(eY,{ref:t,...u}))))}),e2=Object.assign(eY,{List:e0,Item:eX,Input:eQ,Group:eJ,Separator:eG,Dialog:e1,Empty:s.forwardRef((e,t)=>e7(e=>0===e.filtered.count)?s.createElement(S.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Loading:s.forwardRef((e,t)=>{let{progress:n,children:r,label:o="Loading...",...i}=e;return s.createElement(S.div,{ref:t,...i,"cmdk-loading":"",role:"progressbar","aria-valuenow":n,"aria-valuemin":0,"aria-valuemax":100,"aria-label":o},te(e,e=>s.createElement("div",{"aria-hidden":!0},e)))})});function e5(e){let t=s.useRef(e);return e8(()=>{t.current=e}),t}var e8="undefined"==typeof window?s.useEffect:s.useLayoutEffect;function e9(e){let t=s.useRef();return void 0===t.current&&(t.current=e()),t}function e3(e){return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}}function e7(e){let t=eq(),n=()=>e(t.snapshot());return s.useSyncExternalStore(t.subscribe,n,n)}function e4(e,t,n,r=[]){let o=s.useRef(),i=eW();return e8(()=>{var l;let u=(()=>{var e;for(let t of n){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():o.current}})(),a=r.map(e=>e.trim());i.value(e,u,a),null==(l=t.current)||l.setAttribute(eH,u),o.current=u}),o}var e6=()=>{let[e,t]=s.useState(),n=e9(()=>new Map);return e8(()=>{n.current.forEach(e=>e()),n.current=new Map},[e]),(e,r)=>{n.current.set(e,r),t({})}};function te({asChild:e,children:t},n){let r;return e&&s.isValidElement(t)?s.cloneElement("function"==typeof(r=t.type)?r(t.props):"render"in r?r.render(t.props):t,{ref:t.ref},n(t.props.children)):n(t)}var tt={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"}},481:(e,t,n)=>{n.d(t,{Z:()=>S});var r=n(17577);function o(e){return"[object Object]"===Object.prototype.toString.call(e)||Array.isArray(e)}function i(e,t){let n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&JSON.stringify(Object.keys(e.breakpoints||{}))===JSON.stringify(Object.keys(t.breakpoints||{}))&&n.every(n=>{let r=e[n],l=t[n];return"function"==typeof r?`${r}`==`${l}`:o(r)&&o(l)?i(r,l):r===l})}function l(e){return e.concat().sort((e,t)=>e.name>t.name?1:-1).map(e=>e.options)}function u(e){return"number"==typeof e}function a(e){return"string"==typeof e}function c(e){return"boolean"==typeof e}function s(e){return"[object Object]"===Object.prototype.toString.call(e)}function d(e){return Math.abs(e)}function f(e){return Math.sign(e)}function p(e){return g(e).map(Number)}function m(e){return e[v(e)]}function v(e){return Math.max(0,e.length-1)}function h(e,t=0){return Array.from(Array(e),(e,n)=>t+n)}function g(e){return Object.keys(e)}function y(e,t){return void 0!==t.MouseEvent&&e instanceof t.MouseEvent}function b(){let e=[],t={add:function(n,r,o,i={passive:!0}){let l;return"addEventListener"in n?(n.addEventListener(r,o,i),l=()=>n.removeEventListener(r,o,i)):(n.addListener(o),l=()=>n.removeListener(o)),e.push(l),t},clear:function(){e=e.filter(e=>e())}};return t}function E(e=0,t=0){let n=d(e-t);function r(n){return n<e||n>t}return{length:n,max:t,min:e,constrain:function(n){return r(n)?n<e?e:t:n},reachedAny:r,reachedMax:function(e){return e>t},reachedMin:function(t){return t<e},removeOffset:function(e){return n?e-n*Math.ceil((e-t)/n):e}}}function w(e){let t=e;function n(e){return u(e)?e:e.get()}return{get:function(){return t},set:function(e){t=n(e)},add:function(e){t+=n(e)},subtract:function(e){t-=n(e)}}}function x(e,t){let n="x"===e.scroll?function(e){return`translate3d(${e}px,0px,0px)`}:function(e){return`translate3d(0px,${e}px,0px)`},r=t.style,o=null,i=!1;return{clear:function(){i||(r.transform="",t.getAttribute("style")||t.removeAttribute("style"))},to:function(t){if(i)return;let l=Math.round(100*e.direction(t))/100;l!==o&&(r.transform=n(l),o=l)},toggleActive:function(e){i=!e}}}let C={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function k(e,t,n){let r,o,i,l,S;let R=e.ownerDocument,D=R.defaultView,O=function(e){function t(e,t){return function e(t,n){return[t,n].reduce((t,n)=>(g(n).forEach(r=>{let o=t[r],i=n[r],l=s(o)&&s(i);t[r]=l?e(o,i):i}),t),{})}(e,t||{})}return{mergeOptions:t,optionsAtMedia:function(n){let r=n.breakpoints||{},o=g(r).filter(t=>e.matchMedia(t).matches).map(e=>r[e]).reduce((e,n)=>t(e,n),{});return t(n,o)},optionsMediaQueries:function(t){return t.map(e=>g(e.breakpoints||{})).reduce((e,t)=>e.concat(t),[]).map(e.matchMedia)}}}(D),P=(S=[],{init:function(e,t){return(S=t.filter(({options:e})=>!1!==O.optionsAtMedia(e).active)).forEach(t=>t.init(e,O)),t.reduce((e,t)=>Object.assign(e,{[t.name]:t}),{})},destroy:function(){S=S.filter(e=>e.destroy())}}),A=b(),N=function(){let e,t={},n={init:function(t){e=t},emit:function(r){return(t[r]||[]).forEach(t=>t(e,r)),n},off:function(e,r){return t[e]=(t[e]||[]).filter(e=>e!==r),n},on:function(e,r){return t[e]=(t[e]||[]).concat([r]),n},clear:function(){t={}}};return n}(),{mergeOptions:I,optionsAtMedia:M,optionsMediaQueries:T}=O,{on:L,off:F,emit:j}=N,_=!1,V=I(C,k.globalOptions),U=I(V),$=[];function H(t,n){!_&&(U=M(V=I(V,t)),$=n||$,function(){let{container:t,slides:n}=U;i=(a(t)?e.querySelector(t):t)||e.children[0];let r=a(n)?i.querySelectorAll(n):n;l=[].slice.call(r||i.children)}(),r=function t(n){let r=function(e,t,n,r,o,i,l){let s,C;let{align:k,axis:S,direction:R,startIndex:D,loop:O,duration:P,dragFree:A,dragThreshold:N,inViewThreshold:I,slidesToScroll:M,skipSnaps:T,containScroll:L,watchResize:F,watchSlides:j,watchDrag:_,watchFocus:V}=i,U={measure:function(e){let{offsetTop:t,offsetLeft:n,offsetWidth:r,offsetHeight:o}=e;return{top:t,right:n+r,bottom:t+o,left:n,width:r,height:o}}},$=U.measure(t),H=n.map(U.measure),Z=function(e,t){let n="rtl"===t,r="y"===e,o=!r&&n?-1:1;return{scroll:r?"y":"x",cross:r?"x":"y",startEdge:r?"top":n?"right":"left",endEdge:r?"bottom":n?"left":"right",measureSize:function(e){let{height:t,width:n}=e;return r?t:n},direction:function(e){return e*o}}}(S,R),z=Z.measureSize($),W={measure:function(e){return e/100*z}},B=function(e,t){let n={start:function(){return 0},center:function(e){return(t-e)/2},end:function(e){return t-e}};return{measure:function(r,o){return a(e)?n[e](r):e(t,r,o)}}}(k,z),q=!O&&!!L,{slideSizes:K,slideSizesWithGaps:Y,startGap:X,endGap:J}=function(e,t,n,r,o,i){let{measureSize:l,startEdge:u,endEdge:a}=e,c=n[0]&&o,s=function(){if(!c)return 0;let e=n[0];return d(t[u]-e[u])}(),f=c?parseFloat(i.getComputedStyle(m(r)).getPropertyValue(`margin-${a}`)):0,p=n.map(l),h=n.map((e,t,n)=>{let r=t===v(n);return t?r?p[t]+f:n[t+1][u]-e[u]:p[t]+s}).map(d);return{slideSizes:p,slideSizesWithGaps:h,startGap:s,endGap:f}}(Z,$,H,n,O||!!L,o),G=function(e,t,n,r,o,i,l,a,c){let{startEdge:s,endEdge:f,direction:h}=e,g=u(n);return{groupSlides:function(e){return g?p(e).filter(e=>e%n==0).map(t=>e.slice(t,t+n)):e.length?p(e).reduce((n,u,c)=>{let p=m(n)||0,g=u===v(e),y=o[s]-i[p][s],b=o[s]-i[u][f],E=r||0!==p?0:h(l),w=d(b-(!r&&g?h(a):0)-(y+E));return c&&w>t+2&&n.push(u),g&&n.push(e.length),n},[]).map((t,n,r)=>{let o=Math.max(r[n-1]||0);return e.slice(o,t)}):[]}}}(Z,z,M,O,$,H,X,J,0),{snaps:Q,snapsAligned:ee}=function(e,t,n,r,o){let{startEdge:i,endEdge:l}=e,{groupSlides:u}=o,a=u(r).map(e=>m(e)[l]-e[0][i]).map(d).map(t.measure),c=r.map(e=>n[i]-e[i]).map(e=>-d(e)),s=u(c).map(e=>e[0]).map((e,t)=>e+a[t]);return{snaps:c,snapsAligned:s}}(Z,B,$,H,G),et=-m(Q)+m(Y),{snapsContained:en,scrollContainLimit:er}=function(e,t,n,r,o){let i=E(-t+e,0),l=n.map((e,t)=>{let{min:r,max:o}=i,l=i.constrain(e),u=t===v(n);return t?u||1>=d(r-l)?r:1>=d(o-l)?o:l:o}).map(e=>parseFloat(e.toFixed(3))),u=function(){let e=l[0],t=m(l);return E(l.lastIndexOf(e),l.indexOf(t)+1)}();return{snapsContained:function(){if(t<=e+2)return[i.max];if("keepSnaps"===r)return l;let{min:n,max:o}=u;return l.slice(n,o)}(),scrollContainLimit:u}}(z,et,ee,L,0),eo=q?en:ee,{limit:ei}=function(e,t,n){let r=t[0];return{limit:E(n?r-e:m(t),r)}}(et,eo,O),el=function e(t,n,r){let{constrain:o}=E(0,t),i=t+1,l=u(n);function u(e){return r?d((i+e)%i):o(e)}function a(){return e(t,l,r)}let c={get:function(){return l},set:function(e){return l=u(e),c},add:function(e){return a().set(l+e)},clone:a};return c}(v(eo),D,O),eu=el.clone(),ea=p(n),ec=({dragHandler:e,scrollBody:t,scrollBounds:n,options:{loop:r}})=>{r||n.constrain(e.pointerDown()),t.seek()},es=({scrollBody:e,translate:t,location:n,offsetLocation:r,previousLocation:o,scrollLooper:i,slideLooper:l,dragHandler:u,animation:a,eventHandler:c,scrollBounds:s,options:{loop:d}},f)=>{let p=e.settled(),m=!s.shouldConstrain(),v=d?p:p&&m;v&&!u.pointerDown()&&(a.stop(),c.emit("settle")),v||c.emit("scroll");let h=n.get()*f+o.get()*(1-f);r.set(h),d&&(i.loop(e.direction()),l.loop()),t.to(r.get())},ed=function(e,t,n,r){let o=b(),i=1e3/60,l=null,u=0,a=0;function c(e){if(!a)return;l||(l=e,n(),n());let o=e-l;for(l=e,u+=o;u>=i;)n(),u-=i;r(u/i),a&&(a=t.requestAnimationFrame(c))}function s(){t.cancelAnimationFrame(a),l=null,u=0,a=0}return{init:function(){o.add(e,"visibilitychange",()=>{e.hidden&&(l=null,u=0)})},destroy:function(){s(),o.clear()},start:function(){a||(a=t.requestAnimationFrame(c))},stop:s,update:n,render:r}}(r,o,()=>ec(eS),e=>es(eS,e)),ef=eo[el.get()],ep=w(ef),em=w(ef),ev=w(ef),eh=w(ef),eg=function(e,t,n,r,o,i){let l=0,u=0,a=o,c=.68,s=e.get(),p=0;function m(e){return a=e,h}function v(e){return c=e,h}let h={direction:function(){return u},duration:function(){return a},velocity:function(){return l},seek:function(){let t=r.get()-e.get(),o=0;return a?(n.set(e),l+=t/a,l*=c,s+=l,e.add(l),o=s-p):(l=0,n.set(r),e.set(r),o=t),u=f(o),p=s,h},settled:function(){return .001>d(r.get()-t.get())},useBaseFriction:function(){return v(.68)},useBaseDuration:function(){return m(o)},useFriction:v,useDuration:m};return h}(ep,ev,em,eh,P,0),ey=function(e,t,n,r,o){let{reachedAny:i,removeOffset:l,constrain:u}=r;function a(e){return e.concat().sort((e,t)=>d(e)-d(t))[0]}function c(t,r){let o=[t,t+n,t-n];if(!e)return t;if(!r)return a(o);let i=o.filter(e=>f(e)===r);return i.length?a(i):m(o)-n}return{byDistance:function(n,r){let a=o.get()+n,{index:s,distance:f}=function(n){let r=e?l(n):u(n),{index:o}=t.map((e,t)=>({diff:c(e-r,0),index:t})).sort((e,t)=>d(e.diff)-d(t.diff))[0];return{index:o,distance:r}}(a),p=!e&&i(a);if(!r||p)return{index:s,distance:n};let m=n+c(t[s]-f,0);return{index:s,distance:m}},byIndex:function(e,n){let r=c(t[e]-o.get(),n);return{index:e,distance:r}},shortcut:c}}(O,eo,et,ei,eh),eb=function(e,t,n,r,o,i,l){function u(o){let u=o.distance,a=o.index!==t.get();i.add(u),u&&(r.duration()?e.start():(e.update(),e.render(1),e.update())),a&&(n.set(t.get()),t.set(o.index),l.emit("select"))}return{distance:function(e,t){u(o.byDistance(e,t))},index:function(e,n){let r=t.clone().set(e);u(o.byIndex(r.get(),n))}}}(ed,el,eu,eg,ey,eh,l),eE=function(e){let{max:t,length:n}=e;return{get:function(e){return n?-((e-t)/n):0}}}(ei),ew=b(),ex=function(e,t,n,r){let o;let i={},l=null,u=null,a=!1;return{init:function(){o=new IntersectionObserver(e=>{a||(e.forEach(e=>{i[t.indexOf(e.target)]=e}),l=null,u=null,n.emit("slidesInView"))},{root:e.parentElement,threshold:r}),t.forEach(e=>o.observe(e))},destroy:function(){o&&o.disconnect(),a=!0},get:function(e=!0){if(e&&l)return l;if(!e&&u)return u;let t=g(i).reduce((t,n)=>{let r=parseInt(n),{isIntersecting:o}=i[r];return(e&&o||!e&&!o)&&t.push(r),t},[]);return e&&(l=t),e||(u=t),t}}}(t,n,l,I),{slideRegistry:eC}=function(e,t,n,r,o,i){let{groupSlides:l}=o,{min:u,max:a}=r;return{slideRegistry:function(){let r=l(i);return 1===n.length?[i]:e&&"keepSnaps"!==t?r.slice(u,a).map((e,t,n)=>{let r=t===v(n);return t?r?h(v(i)-m(n)[0]+1,m(n)[0]):e:h(m(n[0])+1)}):r}()}}(q,L,eo,er,G,ea),ek=function(e,t,n,r,o,i,l,a){let s={passive:!0,capture:!0},d=0;function f(e){"Tab"===e.code&&(d=new Date().getTime())}return{init:function(p){a&&(i.add(document,"keydown",f,!1),t.forEach((t,f)=>{i.add(t,"focus",t=>{(c(a)||a(p,t))&&function(t){if(new Date().getTime()-d>10)return;l.emit("slideFocusStart"),e.scrollLeft=0;let i=n.findIndex(e=>e.includes(t));u(i)&&(o.useDuration(0),r.index(i,0),l.emit("slideFocus"))}(f)},s)}))}}}(e,n,eC,eb,eg,ew,l,V),eS={ownerDocument:r,ownerWindow:o,eventHandler:l,containerRect:$,slideRects:H,animation:ed,axis:Z,dragHandler:function(e,t,n,r,o,i,l,u,a,s,p,m,v,h,g,w,x,C,k){let{cross:S,direction:R}=e,D=["INPUT","SELECT","TEXTAREA"],O={passive:!1},P=b(),A=b(),N=E(50,225).constrain(h.measure(20)),I={mouse:300,touch:400},M={mouse:500,touch:600},T=g?43:25,L=!1,F=0,j=0,_=!1,V=!1,U=!1,$=!1;function H(e){if(!y(e,r)&&e.touches.length>=2)return Z(e);let t=i.readPoint(e),n=i.readPoint(e,S),l=d(t-F),a=d(n-j);if(!V&&!$&&(!e.cancelable||!(V=l>a)))return Z(e);let c=i.pointerMove(e);l>w&&(U=!0),s.useFriction(.3).useDuration(.75),u.start(),o.add(R(c)),e.preventDefault()}function Z(e){let t=p.byDistance(0,!1).index!==m.get(),n=i.pointerUp(e)*(g?M:I)[$?"mouse":"touch"],r=function(e,t){let n=m.add(-1*f(e)),r=p.byDistance(e,!g).distance;return g||d(e)<N?r:x&&t?.5*r:p.byIndex(n.get(),0).distance}(R(n),t),o=function(e,t){var n,r;if(0===e||0===t||d(e)<=d(t))return 0;let o=(n=d(e),r=d(t),d(n-r));return d(o/e)}(n,r);V=!1,_=!1,A.clear(),s.useDuration(T-10*o).useFriction(.68+o/50),a.distance(r,!g),$=!1,v.emit("pointerUp")}function z(e){U&&(e.stopPropagation(),e.preventDefault(),U=!1)}return{init:function(e){k&&P.add(t,"dragstart",e=>e.preventDefault(),O).add(t,"touchmove",()=>void 0,O).add(t,"touchend",()=>void 0).add(t,"touchstart",u).add(t,"mousedown",u).add(t,"touchcancel",Z).add(t,"contextmenu",Z).add(t,"click",z,!0);function u(u){(c(k)||k(e,u))&&function(e){let u=y(e,r);$=u,U=g&&u&&!e.buttons&&L,L=d(o.get()-l.get())>=2,u&&0!==e.button||function(e){let t=e.nodeName||"";return D.includes(t)}(e.target)||(_=!0,i.pointerDown(e),s.useFriction(0).useDuration(0),o.set(l),function(){let e=$?n:t;A.add(e,"touchmove",H,O).add(e,"touchend",Z).add(e,"mousemove",H,O).add(e,"mouseup",Z)}(),F=i.readPoint(e),j=i.readPoint(e,S),v.emit("pointerDown"))}(u)}},destroy:function(){P.clear(),A.clear()},pointerDown:function(){return _}}}(Z,e,r,o,eh,function(e,t){let n,r;function o(e){return e.timeStamp}function i(n,r){let o=r||e.scroll,i=`client${"x"===o?"X":"Y"}`;return(y(n,t)?n:n.touches[0])[i]}return{pointerDown:function(e){return n=e,r=e,i(e)},pointerMove:function(e){let t=i(e)-i(r),l=o(e)-o(n)>170;return r=e,l&&(n=e),t},pointerUp:function(e){if(!n||!r)return 0;let t=i(r)-i(n),l=o(e)-o(n),u=o(e)-o(r)>170,a=t/l;return l&&!u&&d(a)>.1?a:0},readPoint:i}}(Z,o),ep,ed,eb,eg,ey,el,l,W,A,N,T,0,_),eventStore:ew,percentOfView:W,index:el,indexPrevious:eu,limit:ei,location:ep,offsetLocation:ev,previousLocation:em,options:i,resizeHandler:function(e,t,n,r,o,i,l){let u,a;let s=[e].concat(r),f=[],p=!1;function m(e){return o.measureSize(l.measure(e))}return{init:function(o){i&&(a=m(e),f=r.map(m),u=new ResizeObserver(n=>{(c(i)||i(o,n))&&function(n){for(let i of n){if(p)return;let n=i.target===e,l=r.indexOf(i.target),u=n?a:f[l];if(d(m(n?e:r[l])-u)>=.5){o.reInit(),t.emit("resize");break}}}(n)}),n.requestAnimationFrame(()=>{s.forEach(e=>u.observe(e))}))},destroy:function(){p=!0,u&&u.disconnect()}}}(t,l,o,n,Z,F,U),scrollBody:eg,scrollBounds:function(e,t,n,r,o){let i=o.measure(10),l=o.measure(50),u=E(.1,.99),a=!1;function c(){return!!(!a&&e.reachedAny(n.get())&&e.reachedAny(t.get()))}return{shouldConstrain:c,constrain:function(o){if(!c())return;let a=e.reachedMin(t.get())?"min":"max",s=d(e[a]-t.get()),f=n.get()-t.get(),p=u.constrain(s/l);n.subtract(f*p),!o&&d(f)<i&&(n.set(e.constrain(n.get())),r.useDuration(25).useBaseFriction())},toggleActive:function(e){a=!e}}}(ei,ev,eh,eg,W),scrollLooper:function(e,t,n,r){let{reachedMin:o,reachedMax:i}=E(t.min+.1,t.max+.1);return{loop:function(t){if(!(1===t?i(n.get()):-1===t&&o(n.get())))return;let l=-1*t*e;r.forEach(e=>e.add(l))}}}(et,ei,ev,[ep,ev,em,eh]),scrollProgress:eE,scrollSnapList:eo.map(eE.get),scrollSnaps:eo,scrollTarget:ey,scrollTo:eb,slideLooper:function(e,t,n,r,o,i,l,u,a){let c=p(o),s=m(f(p(o).reverse(),l[0]),n,!1).concat(m(f(c,t-l[0]-1),-n,!0));function d(e,t){return e.reduce((e,t)=>e-o[t],t)}function f(e,t){return e.reduce((e,n)=>d(e,t)>0?e.concat([n]):e,[])}function m(o,l,c){let s=i.map((e,n)=>({start:e-r[n]+.5+l,end:e+t-.5+l}));return o.map(t=>{let r=c?0:-n,o=c?n:0,i=s[t][c?"end":"start"];return{index:t,loopPoint:i,slideLocation:w(-1),translate:x(e,a[t]),target:()=>u.get()>i?r:o}})}return{canLoop:function(){return s.every(({index:e})=>.1>=d(c.filter(t=>t!==e),t))},clear:function(){s.forEach(e=>e.translate.clear())},loop:function(){s.forEach(e=>{let{target:t,translate:n,slideLocation:r}=e,o=t();o!==r.get()&&(n.to(o),r.set(o))})},loopPoints:s}}(Z,z,et,K,Y,Q,eo,ev,n),slideFocus:ek,slidesHandler:(C=!1,{init:function(e){j&&(s=new MutationObserver(t=>{!C&&(c(j)||j(e,t))&&function(t){for(let n of t)if("childList"===n.type){e.reInit(),l.emit("slidesChanged");break}}(t)})).observe(t,{childList:!0})},destroy:function(){s&&s.disconnect(),C=!0}}),slidesInView:ex,slideIndexes:ea,slideRegistry:eC,slidesToScroll:G,target:eh,translate:x(Z,t)};return eS}(e,i,l,R,D,n,N);return n.loop&&!r.slideLooper.canLoop()?t(Object.assign({},n,{loop:!1})):r}(U),T([V,...$.map(({options:e})=>e)]).forEach(e=>A.add(e,"change",Z)),U.active&&(r.translate.to(r.location.get()),r.animation.init(),r.slidesInView.init(),r.slideFocus.init(q),r.eventHandler.init(q),r.resizeHandler.init(q),r.slidesHandler.init(q),r.options.loop&&r.slideLooper.loop(),i.offsetParent&&l.length&&r.dragHandler.init(q),o=P.init(q,$)))}function Z(e,t){let n=B();z(),H(I({startIndex:n},e),t),N.emit("reInit")}function z(){r.dragHandler.destroy(),r.eventStore.clear(),r.translate.clear(),r.slideLooper.clear(),r.resizeHandler.destroy(),r.slidesHandler.destroy(),r.slidesInView.destroy(),r.animation.destroy(),P.destroy(),A.clear()}function W(e,t,n){U.active&&!_&&(r.scrollBody.useBaseFriction().useDuration(!0===t?0:U.duration),r.scrollTo.index(e,n||0))}function B(){return r.index.get()}let q={canScrollNext:function(){return r.index.add(1).get()!==B()},canScrollPrev:function(){return r.index.add(-1).get()!==B()},containerNode:function(){return i},internalEngine:function(){return r},destroy:function(){_||(_=!0,A.clear(),z(),N.emit("destroy"),N.clear())},off:F,on:L,emit:j,plugins:function(){return o},previousScrollSnap:function(){return r.indexPrevious.get()},reInit:Z,rootNode:function(){return e},scrollNext:function(e){W(r.index.add(1).get(),e,-1)},scrollPrev:function(e){W(r.index.add(-1).get(),e,1)},scrollProgress:function(){return r.scrollProgress.get(r.location.get())},scrollSnapList:function(){return r.scrollSnapList},scrollTo:W,selectedScrollSnap:B,slideNodes:function(){return l},slidesInView:function(){return r.slidesInView.get()},slidesNotInView:function(){return r.slidesInView.get(!1)}};return H(t,n),setTimeout(()=>N.emit("init"),0),q}function S(e={},t=[]){let n=(0,r.useRef)(e),o=(0,r.useRef)(t),[u,a]=(0,r.useState)(),[c,s]=(0,r.useState)(),d=(0,r.useCallback)(()=>{u&&u.reInit(n.current,o.current)},[u]);return(0,r.useEffect)(()=>{i(n.current,e)||(n.current=e,d())},[e,d]),(0,r.useEffect)(()=>{!function(e,t){if(e.length!==t.length)return!1;let n=l(e),r=l(t);return n.every((e,t)=>i(e,r[t]))}(o.current,t)&&(o.current=t,d())},[t,d]),(0,r.useEffect)(()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&c){k.globalOptions=S.globalOptions;let e=k(c,n.current,o.current);return a(e),()=>e.destroy()}a(void 0)},[c,a]),[s,u]}k.globalOptions=void 0,S.globalOptions=void 0}};