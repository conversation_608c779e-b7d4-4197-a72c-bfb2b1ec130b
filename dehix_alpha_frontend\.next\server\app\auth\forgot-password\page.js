(()=>{var e={};e.id=6047,e.ids=[6047],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},32607:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),s(85231),s(54302),s(12523);var r=s(23191),a=s(88716),i=s(37922),o=s.n(i),n=s(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let d=["",{children:["auth",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,85231)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\auth\\forgot-password\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\auth\\forgot-password\\page.tsx"],u="/auth/forgot-password/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/forgot-password/page",pathname:"/auth/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},83801:(e,t,s)=>{Promise.resolve().then(s.bind(s,60136))},77506:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},60136:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(10326),a=s(77506),i=s(46226),o=s(90434),n=s(35047),l=s(17577),d=s(40603),c=s(91664),u=s(41190),p=s(44794),m=s(56627),x=s(51223);function f(){let[e,t]=(0,l.useState)(""),[s,f]=(0,l.useState)(!1),h=(0,n.useRouter)(),g=async t=>{t.preventDefault(),f(!0);try{await (0,x.c0)(e),(0,m.Am)({title:"Success",description:"Password reset email sent! Please check your inbox."}),h.push("/auth/login")}catch(e){(0,m.Am)({variant:"destructive",title:"Error",description:"Invalid Email or Password. Please try again."}),console.error(e.message)}finally{f(!1)}};return(0,r.jsxs)("div",{className:"w-full lg:grid lg:min-h-[600px] lg:grid-cols-2 xl:min-h-screen",children:[r.jsx("div",{className:"absolute left-10 top-10",children:r.jsx(d.T,{})}),r.jsx("div",{className:"flex items-center justify-center py-12",children:(0,r.jsxs)("div",{className:"mx-auto grid w-[350px] gap-6",children:[(0,r.jsxs)("div",{className:"grid gap-2 text-center",children:[r.jsx("h1",{className:"text-3xl font-bold",children:"Forgot Password"}),r.jsx("p",{className:"text-balance text-muted-foreground",children:"Enter your email address below to reset your password"})]}),r.jsx("form",{onSubmit:g,children:(0,r.jsxs)("div",{className:"grid gap-4",children:[(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(p.Label,{htmlFor:"email",children:"Email"}),r.jsx(u.I,{id:"email",type:"email",placeholder:"<EMAIL>",value:e,onChange:e=>t(e.target.value),required:!0})]}),r.jsx(c.z,{type:"submit",className:"w-full",disabled:s,children:s?r.jsx(a.Z,{className:"mr-2 h-4 w-4 animate-spin"}):"Send Reset Link"})]})}),(0,r.jsxs)("div",{className:"mt-4 text-center text-sm",children:["Remembered your password?"," ",r.jsx(c.z,{variant:"outline",size:"sm",className:"ml-2",asChild:!0,children:r.jsx(o.default,{href:"/auth/sign-in",children:"Login"})})]})]})}),r.jsx("div",{className:"hidden lg:block",children:r.jsx(i.default,{src:"/bg.png",alt:"Image",width:"1920",height:"1080",className:"h-full w-full object-cover dark:brightness-[0.2] dark:invert"})})]})}},40603:(e,t,s)=>{"use strict";s.d(t,{T:()=>d});var r=s(10326);s(17577);var a=s(60850),i=s(72607),o=s(14831),n=s(91664),l=s(10143);function d(){let{setTheme:e}=(0,o.F)();return(0,r.jsxs)(l.h_,{children:[r.jsx(l.$F,{asChild:!0,children:(0,r.jsxs)(n.z,{variant:"outline",size:"icon",children:[r.jsx(a.Z,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),r.jsx(i.Z,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),r.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,r.jsxs)(l.AW,{align:"end",children:[r.jsx(l.Xi,{onClick:()=>e("light"),children:"Light"}),r.jsx(l.Xi,{onClick:()=>e("dark"),children:"Dark"}),r.jsx(l.Xi,{onClick:()=>e("system"),children:"System"})]})]})}},10143:(e,t,s)=>{"use strict";s.d(t,{$F:()=>u,AW:()=>p,Ju:()=>f,VD:()=>h,Xi:()=>m,bO:()=>x,h_:()=>c});var r=s(10326),a=s(17577),i=s(76234),o=s(39183),n=s(32933),l=s(53982),d=s(51223);let c=i.fC,u=i.xz;i.ZA,i.Uv,i.Tr,i.Ee,a.forwardRef(({className:e,inset:t,children:s,...a},n)=>(0,r.jsxs)(i.fF,{ref:n,className:(0,d.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...a,children:[s,r.jsx(o.Z,{className:"ml-auto h-4 w-4"})]})).displayName=i.fF.displayName,a.forwardRef(({className:e,...t},s)=>r.jsx(i.tu,{ref:s,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=i.tu.displayName;let p=a.forwardRef(({className:e,sideOffset:t=4,...s},a)=>r.jsx(i.Uv,{children:r.jsx(i.VY,{ref:a,sideOffset:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})}));p.displayName=i.VY.displayName;let m=a.forwardRef(({className:e,inset:t,...s},a)=>r.jsx(i.ck,{ref:a,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...s}));m.displayName=i.ck.displayName;let x=a.forwardRef(({className:e,children:t,checked:s,...a},o)=>(0,r.jsxs)(i.oC,{ref:o,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:s,...a,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(i.wU,{children:r.jsx(n.Z,{className:"h-4 w-4"})})}),t]}));x.displayName=i.oC.displayName,a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(i.Rk,{ref:a,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(i.wU,{children:r.jsx(l.Z,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=i.Rk.displayName;let f=a.forwardRef(({className:e,inset:t,...s},a)=>r.jsx(i.__,{ref:a,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...s}));f.displayName=i.__.displayName;let h=a.forwardRef(({className:e,...t},s)=>r.jsx(i.Z0,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t}));h.displayName=i.Z0.displayName},41190:(e,t,s)=>{"use strict";s.d(t,{I:()=>o});var r=s(10326),a=s(17577),i=s(51223);let o=a.forwardRef(({className:e,type:t,...s},a)=>r.jsx("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));o.displayName="Input"},44794:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Label:()=>d});var r=s(10326),a=s(17577),i=s(34478),o=s(28671),n=s(51223);let l=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...t},s)=>r.jsx(i.f,{ref:s,className:(0,n.cn)(l(),e),...t}));d.displayName=i.f.displayName},85231:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>i,default:()=>n});var r=s(68570);let a=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\auth\forgot-password\page.tsx`),{__esModule:i,$$typeof:o}=a;a.default;let n=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\auth\forgot-password\page.tsx#default`)},34478:(e,t,s)=>{"use strict";s.d(t,{f:()=>n});var r=s(17577),a=s(77335),i=s(10326),o=r.forwardRef((e,t)=>(0,i.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var n=o}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,4198,6034,4718,6226,4736],()=>s(32607));module.exports=r})();