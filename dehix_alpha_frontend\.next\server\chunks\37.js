"use strict";exports.id=37,exports.ids=[37],exports.modules={40900:(e,a,t)=>{t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},37358:(e,a,t)=>{t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},11890:(e,a,t)=>{t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},43727:(e,a,t)=>{t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},40617:(e,a,t)=>{t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},23015:(e,a,t)=>{t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("PackageOpen",[["path",{d:"M12 22v-9",key:"x3hkom"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z",key:"2ntwy6"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13",key:"1pmm1c"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z",key:"12ttoo"}]])},60763:(e,a,t)=>{t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69515:(e,a,t)=>{t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},98091:(e,a,t)=>{t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},12199:(e,a,t)=>{t.d(a,{Z:()=>O});var s=t(10326),r=t(17577),l=t.n(r),n=t(23015),i=t(25842),d=t(83855),c=t(74723),o=t(74064),h=t(27256),m=t(90434),p=t(24118),x=t(91664),u=t(29280),y=t(41190),f=t(6260),b=t(56627),j=t(39958);let v=h.z.object({skillId:h.z.string(),label:h.z.string().nonempty("Please select a skill"),experience:h.z.string().nonempty("Please enter your experience").regex(/^\d+$/,"Experience must be a number"),monthlyPay:h.z.string().nonempty("Please enter your monthly pay").regex(/^\d+$/,"Monthly pay must be a number"),activeStatus:h.z.boolean(),status:h.z.string()}),g=({skills:e,onSubmitSkill:a,setSkills:t})=>{let[l,n]=(0,r.useState)(!1),[i,h]=(0,r.useState)(!1),{control:g,handleSubmit:N,formState:{errors:w},reset:k,setValue:S}=(0,c.cI)({resolver:(0,o.F)(v),defaultValues:{skillId:"",label:"",experience:"",monthlyPay:"",activeStatus:!1,status:j.sB.PENDING}}),I=async e=>{if(h(!0),!a({...e,uid:"",type:"SKILL"})){h(!1);return}try{let a=await f.b.post("/freelancer/dehix-talent",{talentId:e.skillId,talentName:e.label,experience:e.experience,monthlyPay:e.monthlyPay,activeStatus:e.activeStatus,status:e.status,type:"SKILL"});200===a.status&&(a.data.data,k(),n(!1),(0,b.Am)({title:"Talent Added",description:"The Talent has been successfully added."}))}catch(e){console.error("Error submitting skill data",e),k(),(0,b.Am)({variant:"destructive",title:"Error",description:"Failed to add talent. Please try again."})}finally{h(!1)}};return(0,s.jsxs)(p.Vq,{open:l,onOpenChange:n,children:[s.jsx(p.hg,{asChild:!0,children:(0,s.jsxs)(x.z,{onClick:()=>n(!0),children:[s.jsx(d.Z,{className:"mr-2 h-4 w-4"}),"Add Skill"]})}),(0,s.jsxs)(p.cZ,{children:[(0,s.jsxs)(p.fK,{children:[s.jsx(p.$N,{children:"Add Skill"}),s.jsx(p.Be,{children:"Select a skill, enter your experience and monthly pay."})]}),(0,s.jsxs)("form",{onSubmit:N(I),children:[s.jsx("div",{className:"mb-3",children:s.jsx(c.Qr,{control:g,name:"label",render:({field:a})=>(0,s.jsxs)(u.Ph,{value:a.value,onValueChange:t=>{let s=e.find(e=>e.name===t);a.onChange(t),S("skillId",s?._id||"")},children:[s.jsx(u.i4,{children:s.jsx(u.ki,{placeholder:"Select a skill"})}),s.jsx(u.Bw,{children:e.length>0?e.map(e=>s.jsx(u.Ql,{value:e.name,children:e.name},e._id)):(0,s.jsxs)("div",{className:"p-4 flex justify-center items-center",children:["No skills to add -"," ",s.jsx(m.default,{href:"/freelancer/settings/personal-info",className:"text-blue-500 ml-2",children:"Add some"})]})})]})})}),w.label&&s.jsx("p",{className:"text-red-600",children:w.label.message}),s.jsx("div",{className:"mb-3",children:s.jsx(c.Qr,{control:g,name:"experience",render:({field:e})=>(0,s.jsxs)("div",{className:"col-span-3 relative",children:[s.jsx(y.I,{type:"number",placeholder:"Experience (years)",min:0,max:50,step:.1,...e,className:"mt-2 w-full"}),s.jsx("span",{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"YEARS"})]})})}),w.experience&&s.jsx("p",{className:"text-red-600",children:w.experience.message}),s.jsx(c.Qr,{control:g,name:"monthlyPay",render:({field:e})=>(0,s.jsxs)("div",{className:"col-span-3 relative",children:[s.jsx(y.I,{type:"number",placeholder:"$ Monthly Pay",min:0,...e,className:"mt-2 w-full"}),s.jsx("span",{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"$"})]})}),w.monthlyPay&&s.jsx("p",{className:"text-red-600",children:w.monthlyPay.message}),s.jsx(p.cN,{className:"mt-3",children:s.jsx(x.z,{type:"submit",disabled:i,children:i?"Loading...":"Submit"})})]})]})]})},N=h.z.object({domainId:h.z.string(),label:h.z.string().nonempty("Please select a domain"),experience:h.z.string().nonempty("Please enter your experience").regex(/^\d+$/,"Experience must be a number"),monthlyPay:h.z.string().nonempty("Please enter your monthly pay").regex(/^\d+$/,"Monthly pay must be a number"),activeStatus:h.z.boolean(),status:h.z.string()}),w=({domains:e,onSubmitDomain:a,setDomains:t})=>{let[l,n]=(0,r.useState)(!1),[i,h]=(0,r.useState)(!1),{control:v,handleSubmit:g,formState:{errors:w},reset:k,setValue:S}=(0,c.cI)({resolver:(0,o.F)(N),defaultValues:{domainId:"",label:"",experience:"",monthlyPay:"",activeStatus:!1,status:j.sB.PENDING}}),I=async e=>{if(h(!0),!a({...e,uid:"",type:"DOMAIN"})){h(!1);return}try{let a=await f.b.post("/freelancer/dehix-talent",{talentId:e.domainId,talentName:e.label,experience:e.experience,monthlyPay:e.monthlyPay,activeStatus:e.activeStatus,status:e.status,type:"DOMAIN"});200===a.status&&(a.data.data,k(),n(!1),(0,b.Am)({title:"Talent Added",description:"The Talent has been successfully added."}))}catch(e){console.error("Error submitting domain data",e),k(),(0,b.Am)({variant:"destructive",title:"Error",description:"Failed to add talent. Please try again."})}finally{h(!1)}};return(0,s.jsxs)(p.Vq,{open:l,onOpenChange:n,children:[s.jsx(p.hg,{asChild:!0,children:(0,s.jsxs)(x.z,{onClick:()=>n(!0),children:[s.jsx(d.Z,{className:"mr-2 h-4 w-4"})," Add Domain"]})}),(0,s.jsxs)(p.cZ,{children:[(0,s.jsxs)(p.fK,{children:[s.jsx(p.$N,{children:"Add Domain"}),s.jsx(p.Be,{children:"Select a domain, enter your experience and monthly pay."})]}),(0,s.jsxs)("form",{onSubmit:g(I),children:[s.jsx("div",{className:"mb-3",children:s.jsx(c.Qr,{control:v,name:"label",render:({field:a})=>(0,s.jsxs)(u.Ph,{value:a.value,onValueChange:t=>{let s=e.find(e=>e.name===t);a.onChange(t),S("domainId",s?._id||"")},children:[s.jsx(u.i4,{children:s.jsx(u.ki,{placeholder:"Select a domain"})}),s.jsx(u.Bw,{children:e.length>0?e.map(e=>s.jsx(u.Ql,{value:e.name,children:e.name},e._id)):s.jsx(m.default,{href:"/freelancer/settings/personal-info",children:(0,s.jsxs)("p",{className:"p-4 flex justify-center items-center",children:["No domains to add -"," ",s.jsx("span",{className:"text-blue-500 ml-2",children:"Add some"})," "]})})})]})})}),w.label&&s.jsx("p",{className:"text-red-600",children:w.label.message}),s.jsx("div",{className:"mb-3",children:s.jsx(c.Qr,{control:v,name:"experience",render:({field:e})=>(0,s.jsxs)("div",{className:"col-span-3 relative",children:[s.jsx(y.I,{type:"number",placeholder:"Experience (years)",min:0,max:50,step:.1,...e,className:"w-full mt-2"}),s.jsx("span",{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"YEARS"})]})})}),w.experience&&s.jsx("p",{className:"text-red-600",children:w.experience.message}),s.jsx("div",{className:"mb-3",children:s.jsx(c.Qr,{control:v,name:"monthlyPay",render:({field:e})=>(0,s.jsxs)("div",{className:"col-span-3 relative",children:[s.jsx(y.I,{type:"number",placeholder:"$ Monthly Pay",min:0,...e,className:"w-full mt-2"}),s.jsx("span",{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"$"})]})})}),w.monthlyPay&&s.jsx("p",{className:"text-red-600",children:w.monthlyPay.message}),s.jsx(p.cN,{className:"mt-3",children:s.jsx(x.z,{type:"submit",disabled:i,children:i?"Loading...":"Save"})})]})]})]})};var k=t(37358),S=t(67480),I=t(98181),C=t(82015),P=t(33194),E=t(51027),M=t(51223);let A=({talentType:e,talentId:a,userId:t})=>{let[r,n]=l().useState(),[i,d]=l().useState(!1),[c,o]=l().useState(!1),{toast:h}=(0,b.pm)(),m=async s=>{if(s.preventDefault(),o(!0),!r){h({variant:"destructive",title:"Error",description:"Please select an interview date"}),o(!1);return}let l=(0,S.i)(r),n={intervieweeId:t,interviewType:"INTERVIEWER",talentType:e.toUpperCase(),talentId:a,interviewDate:l.toISOString(),description:s.target.description.value};console.log(n);try{let e=await f.b.post(`/interview/${t}`,n);(200===e.status||201===e.status)&&(h({title:"Success",description:"Interview scheduled successfully"}),d(!1))}catch(e){h({variant:"destructive",title:"Error",description:e.response?.data?.message||"Failed to schedule interview"})}finally{o(!1)}};return(0,s.jsxs)(p.Vq,{open:i,onOpenChange:d,children:[s.jsx(p.hg,{asChild:!0,children:s.jsx(x.z,{variant:"outline",className:"bg-primary text-primary-foreground hover:bg-primary/90 hover:text-black",children:"Verify"})}),(0,s.jsxs)(p.cZ,{className:"sm:max-w-[425px]",children:[s.jsx(p.fK,{children:(0,s.jsxs)(p.$N,{className:"text-lg font-semibold",children:["Verify ",e]})}),(0,s.jsxs)("form",{onSubmit:m,className:"space-y-4 mt-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("label",{htmlFor:"talentType",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Type"}),s.jsx(y.I,{id:"talentType",value:e,disabled:!0,className:"bg-muted"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("label",{htmlFor:"interviewDate",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Interview Date"}),(0,s.jsxs)(E.J2,{children:[s.jsx(E.xo,{asChild:!0,children:(0,s.jsxs)(x.z,{id:"interviewDate",variant:"outline",className:(0,M.cn)("w-full justify-start text-left font-normal",!r&&"text-muted-foreground"),children:[s.jsx(k.Z,{className:"mr-2 h-4 w-4"}),r?(0,I.WU)(r,"PPP"):"Pick a date"]})}),s.jsx(E.yk,{className:"w-auto p-0",align:"start",children:s.jsx(P.f,{mode:"single",selected:r,onSelect:n,initialFocus:!0,disabled:e=>e<new Date})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("label",{htmlFor:"description",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Description"}),s.jsx(C.g,{id:"description",name:"description",placeholder:"Enter interview description",className:"min-h-[100px] resize-none",required:!0})]}),s.jsx(x.z,{type:"submit",className:"w-full",disabled:c,children:c?(0,s.jsxs)("span",{className:"flex items-center gap-2",children:[s.jsx("span",{className:"h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"}),"Scheduling..."]}):"Schedule Interview"})]})]})]})};var D=t(29752),L=t(41156),Z=t(73326),T=t(38443),z=t(38227),V=t(54423);let O=()=>{let[e,a]=(0,r.useState)([]),[t,l]=(0,r.useState)([]),[d,c]=(0,r.useState)([]),[o,h]=(0,r.useState)([]),[m,p]=(0,r.useState)(!0),[x,u]=(0,r.useState)({}),[y,v]=(0,r.useState)({}),N=e=>{let a=new Set;return e.filter(e=>{let t=`${e.label.trim().toLowerCase()}-${e.type}`;return!a.has(t)&&(a.add(t),!0)})},k=(0,i.v9)(e=>e.user);(0,r.useEffect)(()=>{(async function(){p(!0);try{let e=await f.b.get(`/freelancer/${k.uid}/skill`),t=e.data?.data?.[0]?.skills||[],s=await f.b.get(`/freelancer/${k.uid}/domain`),r=s.data?.data?.[0]?.domain||[],n={data:{data:{}}};k?.uid&&(n=await f.b.get(`/freelancer/${k.uid}/dehix-talent`));let i=Array.isArray(n.data?.data)?n.data?.data:Object.values(n.data?.data||{});i.map(e=>e.talentId),i.map(e=>e.talentId);let d=i.flat().map(e=>({uid:e._id,label:e.talentName||"N/A",experience:e.experience||"N/A",monthlyPay:e.monthlyPay||"N/A",status:e.status,activeStatus:e.activeStatus,type:e.type,originalTalentId:e.talentId})),o=new Set(d.filter(e=>"SKILL"===e.type).map(e=>e.label?.toLowerCase().trim().replace(/\s+/g," ")).filter(Boolean)),m=new Set(d.filter(e=>"DOMAIN"===e.type).map(e=>e.label?.toLowerCase().trim().replace(/\s+/g," ")).filter(Boolean)),p=new Set(d.map(e=>e.originalTalentId).filter(Boolean)),x=Array.isArray(t)?t.filter(e=>{let a=e.name?.toLowerCase().trim().replace(/\s+/g," "),t=o.has(a),s=p.has(e._id);return!t&&!s}):[],y=Array.isArray(r)?r.filter(e=>{let a=e.name?.toLowerCase().trim().replace(/\s+/g," "),t=m.has(a),s=p.has(e._id);return!t&&!s}):[],b=N(d);c(b),h(b.map(e=>e.activeStatus)),a(x),l(y);let j={},g={};b.forEach(e=>{let a=e.label.trim().toLowerCase();"SKILL"===e.type&&(j[a]=(j[a]||0)+1),"DOMAIN"===e.type&&(g[a]=(g[a]||0)+1)}),u(j),v(g)}catch(e){console.error("Error fetching data:",e),(0,b.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}finally{p(!1)}})()},[k?.uid]);let S=e=>{let t=e.label.toLowerCase().trim().replace(/\s+/g," ");return d.some(e=>e.label.toLowerCase().trim().replace(/\s+/g," ")===t&&"SKILL"===e.type)?((0,b.Am)({variant:"destructive",title:"Duplicate Skill",description:"This skill has already been added."}),!1):(c([...d,{...e,status:j.sB.PENDING,activeStatus:!1,type:"SKILL"}]),h([...o,!1]),a(e=>e.filter(e=>e.name.toLowerCase().trim().replace(/\s+/g," ")!==t)),!0)},I=e=>{let a=e.label.toLowerCase().trim().replace(/\s+/g," ");return d.some(e=>e.label.toLowerCase().trim().replace(/\s+/g," ")===a&&"DOMAIN"===e.type)?((0,b.Am)({variant:"destructive",title:"Duplicate Domain",description:"This domain has already been added."}),!1):(c([...d,{...e,status:j.sB.PENDING,activeStatus:!1,type:"DOMAIN"}]),h([...o,!1]),l(e=>e.filter(e=>e.name.toLowerCase().trim().replace(/\s+/g," ")!==a)),!0)},C=async(e,a,t)=>{try{let s=await f.b.put(`/freelancer/dehix-talent/${t}`,{activeStatus:a});if(200===s.status){let t=[...o];t[e]=a,h(t)}}catch(e){console.error("Error updating visibility:",e),(0,b.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}};return(0,s.jsxs)("div",{className:"p-6 mt-2",children:[(0,s.jsxs)("div",{className:"mb-8 mt-1 ml-2",children:[s.jsx("h1",{className:"text-3xl font-bold",children:"Dehix Talent"}),s.jsx("p",{className:"text-gray-400 mt-2",children:"Here you can add relevant skills and domains to get directly hired from dehix talent."})]}),s.jsx("div",{className:"px-4",children:(0,s.jsxs)("div",{className:"mb-8 mt-4",children:[s.jsx("div",{className:"flex items-center justify-between mb-4",children:(0,s.jsxs)("div",{className:"flex space-x-4",children:[s.jsx(g,{setSkills:a,skills:e,onSubmitSkill:e=>{let a=S(e);return a&&(0,b.Am)({variant:"default",title:"Talent Added",description:"The skill has been successfully added."}),a}}),s.jsx(w,{setDomains:l,domains:t,onSubmitDomain:e=>{let a=I(e);return a&&(0,b.Am)({variant:"default",title:"Talent Added",description:"The domain has been successfully added."}),a}})]})}),s.jsx(D.Zb,{children:(0,s.jsxs)(L.iA,{children:[s.jsx(L.xD,{children:(0,s.jsxs)(L.SC,{children:[s.jsx(L.ss,{children:"Label"}),s.jsx(L.ss,{children:"Experience"}),s.jsx(L.ss,{className:"text-center",children:"Monthly Pay"}),s.jsx(L.ss,{children:"Status"}),s.jsx(L.ss,{})]})}),s.jsx(L.RM,{children:m?Array.from({length:9}).map((e,a)=>(0,s.jsxs)(L.SC,{children:[s.jsx(L.pj,{children:s.jsx(z.O,{className:"h-6 w-24"})}),s.jsx(L.pj,{children:s.jsx(z.O,{className:"h-6 w-16"})}),s.jsx(L.pj,{className:"text-center",children:s.jsx(z.O,{className:"mx-auto h-6 w-12"})}),s.jsx(L.pj,{children:s.jsx(z.O,{className:"h-6 w-20"})}),s.jsx(L.pj,{children:s.jsx(z.O,{className:"h-6 w-12 rounded-xl"})})]},a)):d.length>0?d.map((e,a)=>(0,s.jsxs)(L.SC,{children:[s.jsx(L.pj,{children:e.label}),(0,s.jsxs)(L.pj,{children:[e.experience," Years"]}),(0,s.jsxs)(L.pj,{className:"text-center",children:["$",e.monthlyPay]}),s.jsx(L.pj,{children:e.status.toUpperCase()===j.sB.PENDING?s.jsx(A,{talentType:e.type,talentId:e.uid,userId:k.uid}):s.jsx(T.C,{className:(0,V.S)(e.status),children:e?.status?.toUpperCase()})}),s.jsx(L.pj,{children:s.jsx(Z.r,{checked:o[a],onCheckedChange:t=>e.uid?C(a,t,e.uid):console.error("UID missing for item",e)})})]},a)):s.jsx(L.SC,{children:s.jsx(L.pj,{colSpan:5,className:"text-center",children:(0,s.jsxs)("div",{className:"text-center py-10 w-[90vw] h-[30vw] mt-10",children:[s.jsx(n.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,s.jsxs)("p",{className:"text-gray-500",children:["No data available.",s.jsx("br",{})," This feature will be available soon.",s.jsx("br",{}),"Here you can get directly hired for different roles."]})]})})})})]})})]})})]})}},33194:(e,a,t)=>{t.d(a,{f:()=>c});var s=t(10326);t(17577);var r=t(11890),l=t(39183),n=t(25579),i=t(51223),d=t(91664);function c({className:e,classNames:a,showOutsideDays:t=!0,...c}){return s.jsx(n._W,{showOutsideDays:t,className:(0,i.cn)("p-3",e),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,i.cn)((0,d.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,i.cn)((0,d.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...a},components:{IconLeft:({...e})=>s.jsx(r.Z,{className:"h-4 w-4"}),IconRight:({...e})=>s.jsx(l.Z,{className:"h-4 w-4"})},...c})}c.displayName="Calendar"},38227:(e,a,t)=>{t.d(a,{O:()=>l});var s=t(10326),r=t(51223);function l({className:e,...a}){return s.jsx("div",{className:(0,r.cn)("animate-pulse rounded-md bg-primary/10",e),...a})}},73326:(e,a,t)=>{t.d(a,{r:()=>i});var s=t(10326),r=t(17577),l=t(41959),n=t(51223);let i=r.forwardRef(({className:e,...a},t)=>s.jsx(l.fC,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...a,ref:t,children:s.jsx(l.bU,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));i.displayName=l.fC.displayName},48586:(e,a,t)=>{t.d(a,{yL:()=>g,$C:()=>v,yn:()=>j});var s=t(10326),r=t(95920),l=t(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,l.Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]]),i=(0,l.Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);var d=t(43727);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,l.Z)("TabletSmartphone",[["rect",{width:"10",height:"14",x:"3",y:"8",rx:"2",key:"1vrsiq"}],["path",{d:"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4",key:"1j4zmg"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),o=(0,l.Z)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]]);var h=t(60763);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,l.Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);var p=t(40617),x=t(69515),u=t(88378),y=t(40900),f=t(98091),b=t(46226);let j=[{href:"#",icon:s.jsx(b.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:s.jsx(r.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:s.jsx(n,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:s.jsx(i,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:s.jsx(d.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:s.jsx(c,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:s.jsx(o,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:s.jsx(h.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:s.jsx(m,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:s.jsx(p.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:s.jsx(x.Z,{className:"h-5 w-5"}),label:"Notes"}],v=[{href:"/freelancer/settings/personal-info",icon:s.jsx(u.Z,{className:"h-5 w-5"}),label:"Settings"}];b.default,r.Z,x.Z,y.Z,f.Z;let g=[{href:"#",icon:s.jsx(b.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:s.jsx(r.Z,{className:"h-5 w-5"}),label:"Home"}]},39958:(e,a,t)=>{var s,r,l;t.d(a,{cd:()=>s,d8:()=>n,kJ:()=>r,sB:()=>l}),function(e){e.Mastery="Mastery",e.Proficient="Proficient",e.Beginner="Beginner"}(s||(s={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.REJECTED="Rejected",e.COMPLETED="Completed"}(r||(r={})),function(e){e.ACTIVE="ACTIVE",e.PENDING="PENDING",e.REJECTED="REJECTED",e.COMPLETED="COMPLETED"}(l||(l={}));let n={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},41959:(e,a,t)=>{t.d(a,{bU:()=>w,fC:()=>N});var s=t(17577),r=t(82561),l=t(48051),n=t(93095),i=t(52067),d=t(53405),c=t(2566),o=t(77335),h=t(10326),m="Switch",[p,x]=(0,n.b)(m),[u,y]=p(m),f=s.forwardRef((e,a)=>{let{__scopeSwitch:t,name:n,checked:d,defaultChecked:c,required:m,disabled:p,value:x="on",onCheckedChange:y,...f}=e,[b,j]=s.useState(null),N=(0,l.e)(a,e=>j(e)),w=s.useRef(!1),k=!b||!!b.closest("form"),[S=!1,I]=(0,i.T)({prop:d,defaultProp:c,onChange:y});return(0,h.jsxs)(u,{scope:t,checked:S,disabled:p,children:[(0,h.jsx)(o.WV.button,{type:"button",role:"switch","aria-checked":S,"aria-required":m,"data-state":g(S),"data-disabled":p?"":void 0,disabled:p,value:x,...f,ref:N,onClick:(0,r.M)(e.onClick,e=>{I(e=>!e),k&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})}),k&&(0,h.jsx)(v,{control:b,bubbles:!w.current,name:n,value:x,checked:S,required:m,disabled:p,style:{transform:"translateX(-100%)"}})]})});f.displayName=m;var b="SwitchThumb",j=s.forwardRef((e,a)=>{let{__scopeSwitch:t,...s}=e,r=y(b,t);return(0,h.jsx)(o.WV.span,{"data-state":g(r.checked),"data-disabled":r.disabled?"":void 0,...s,ref:a})});j.displayName=b;var v=e=>{let{control:a,checked:t,bubbles:r=!0,...l}=e,n=s.useRef(null),i=(0,d.D)(t),o=(0,c.t)(a);return s.useEffect(()=>{let e=n.current,a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(i!==t&&a){let s=new Event("click",{bubbles:r});a.call(e,t),e.dispatchEvent(s)}},[i,t,r]),(0,h.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...l,tabIndex:-1,ref:n,style:{...e.style,...o,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function g(e){return e?"checked":"unchecked"}var N=f,w=j}};