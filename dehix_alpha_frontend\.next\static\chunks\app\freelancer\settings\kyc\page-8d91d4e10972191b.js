(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9379],{57400:function(e,t,a){Promise.resolve().then(a.bind(a,37684))},6540:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(33480).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},25912:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},40036:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(33480).Z)("ImagePlus",[["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7",key:"31hg93"}],["line",{x1:"16",x2:"22",y1:"5",y2:"5",key:"ez7e4s"}],["line",{x1:"19",x2:"19",y1:"2",y2:"8",key:"1gkr8c"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},67524:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(33480).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},37684:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return w}});var r=a(57437),l=a(11444),n=a(64797),s=a(27756),i=a(62688),o=a(2265),c=a(31014),d=a(39343),u=a(59772),m=a(66648),f=a(48185),h=a(93033),v=a(15922),x=a(89733),p=a(93363),g=a(77209),b=a(78068),y=a(29973),j=a(79055),I=a(97540);let k=u.z.object({aadharOrGovtId:u.z.string().optional(),frontImageUrl:u.z.union([u.z.instanceof(File),u.z.string().url(),u.z.null()]).optional(),backImageUrl:u.z.union([u.z.instanceof(File),u.z.string().url(),u.z.null()]).optional(),liveCaptureUrl:u.z.union([u.z.instanceof(File),u.z.string().url(),u.z.null()]).optional()});function N(e){let{user_id:t}=e,[a,l]=(0,o.useState)(!1),[n,s]=(0,o.useState)("PENDING"),[i,u]=(0,o.useState)(null),N=(0,d.cI)({resolver:(0,c.F)(k),defaultValues:{aadharOrGovtId:"",frontImageUrl:null,backImageUrl:null,liveCaptureUrl:null},mode:"all"});async function w(e){l(!0);try{let t={frontImageUrl:e.frontImageUrl,backImageUrl:e.backImageUrl,liveCaptureUrl:e.liveCaptureUrl};if(e.frontImageUrl instanceof File){let a=new FormData;a.append("frontImageUrl",e.frontImageUrl);let r=await v.b.post("/register/upload-image",a,{headers:{"Content-Type":"multipart/form-data"}});t.frontImageUrl=r.data.data.Location}if(e.backImageUrl instanceof File){let a=new FormData;a.append("backImageUrl",e.backImageUrl);let r=await v.b.post("/register/upload-image",a,{headers:{"Content-Type":"multipart/form-data"}});t.backImageUrl=r.data.data.Location}if(e.liveCaptureUrl instanceof File){let a=new FormData;a.append("liveCaptureUrl",e.liveCaptureUrl);let r=await v.b.post("/register/upload-image",a,{headers:{"Content-Type":"multipart/form-data"}});t.liveCaptureUrl=r.data.data.Location}let{aadharOrGovtId:a}=e,r={aadharOrGovtId:a,frontImageUrl:t.frontImageUrl,backImageUrl:t.backImageUrl,liveCaptureUrl:t.liveCaptureUrl,status:"APPLIED"};await v.b.put("/freelancer/kyc",{kyc:r}),u({...i,kyc:{...null==i?void 0:i.kyc,aadharOrGovtId:e.aadharOrGovtId,frontImageUrl:t.frontImageUrl,backImageUrl:t.backImageUrl,liveCaptureUrl:t.liveCaptureUrl}}),(0,b.Am)({title:"KYC Updated",description:"Your KYC has been successfully updated."})}catch(e){console.error("API Error:",e),(0,b.Am)({variant:"destructive",title:"Error",description:"Failed to update KYC. Please try again later."})}finally{l(!1)}}return(0,o.useEffect)(()=>{(async()=>{try{var e,a,r,l,n,i,o;let c=await v.b.get("/freelancer/".concat(t));u(c.data.data),s(null==c?void 0:null===(r=c.data)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:null===(e=a.kyc)||void 0===e?void 0:e.status),N.reset({aadharOrGovtId:(null===(l=c.data.data.kyc)||void 0===l?void 0:l.aadharOrGovtId)||"",frontImageUrl:(null===(n=c.data.data.kyc)||void 0===n?void 0:n.frontImageUrl)||null,backImageUrl:(null===(i=c.data.data.kyc)||void 0===i?void 0:i.backImageUrl)||null,liveCaptureUrl:(null===(o=c.data.data.kyc)||void 0===o?void 0:o.liveCapture)||null})}catch(e){console.error("API Error:",e),(0,b.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}})()},[t,N]),(0,o.useEffect)(()=>{var e,t,a,r;N.reset({aadharOrGovtId:(null==i?void 0:null===(e=i.kyc)||void 0===e?void 0:e.aadharOrGovtId)||"",frontImageUrl:(null==i?void 0:null===(t=i.kyc)||void 0===t?void 0:t.frontImageUrl)||null,backImageUrl:(null==i?void 0:null===(a=i.kyc)||void 0===a?void 0:a.backImageUrl)||null,liveCaptureUrl:(null==i?void 0:null===(r=i.kyc)||void 0===r?void 0:r.liveCaptureUrl)||null})},[i,N]),(0,r.jsx)(f.Zb,{className:"p-4 sm:p-6 md:p-10 shadow-md relative rounded-lg w-full max-w-3xl mx-auto",children:(0,r.jsxs)(p.l0,{...N,children:[(0,r.jsx)("div",{className:"flex flex-col mb-4 sm:mb-6 text-center sm:text-left",children:(0,r.jsxs)("div",{className:"text-sm sm:text-base font-medium",children:["KYC Status"," ",(0,r.jsx)(j.C,{className:"text-xs py-0.5 ".concat(I.d8[n]||""),children:n.toLowerCase()})]})}),(0,r.jsxs)("form",{onSubmit:N.handleSubmit(w),className:"grid gap-4 sm:gap-6 md:gap-8 grid-cols-1 md:grid-cols-2",children:[(0,r.jsx)(y.Separator,{className:"col-span-1 md:col-span-2"}),(0,r.jsx)(p.Wi,{control:N.control,name:"aadharOrGovtId",render:e=>{let{field:t}=e;return(0,r.jsxs)(p.xJ,{className:"w-full",children:[(0,r.jsx)(p.lX,{className:"font-semibold text-sm sm:text-base",children:"Aadhar or Govt Id"}),(0,r.jsx)(p.NI,{children:(0,r.jsx)(g.I,{placeholder:"Enter your Aadhar Id",...t,className:"w-full border rounded-md px-4 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"})}),(0,r.jsx)(p.zG,{})]})}}),(0,r.jsx)(p.Wi,{control:N.control,name:"frontImageUrl",render:e=>{let{field:t}=e;return(0,r.jsxs)(p.xJ,{className:"w-full",children:[(0,r.jsx)(p.lX,{className:"font-semibold text-sm sm:text-base",children:"Document Front Img"}),(0,r.jsx)(p.NI,{children:(0,r.jsx)("div",{className:"flex flex-col sm:flex-row items-center sm:items-start gap-3",children:t.value&&"string"==typeof t.value?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.default,{src:t.value,alt:"Front Document",width:128,height:128,className:"rounded-md object-cover border"}),(0,r.jsx)(x.z,{type:"button",variant:"outline",size:"sm",onClick:()=>t.onChange(""),children:"Change Image"})]}):(0,r.jsx)(g.I,{type:"file",onChange:e=>{var a;let r=null===(a=e.target.files)||void 0===a?void 0:a[0];r&&t.onChange(r)},onBlur:t.onBlur,className:"w-full border rounded-md px-4 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"})})}),(0,r.jsx)(p.zG,{})]})}}),(0,r.jsx)(p.Wi,{control:N.control,name:"backImageUrl",render:e=>{let{field:t}=e;return(0,r.jsxs)(p.xJ,{className:"w-full",children:[(0,r.jsx)(p.lX,{className:"font-semibold text-sm sm:text-base",children:"Document Back Img"}),(0,r.jsx)(p.NI,{children:(0,r.jsx)("div",{className:"flex flex-col sm:flex-row items-center sm:items-start gap-3",children:t.value&&"string"==typeof t.value?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.default,{src:t.value,alt:"Back Document",width:128,height:128,className:"rounded-md object-cover border"}),(0,r.jsx)(x.z,{type:"button",variant:"outline",size:"sm",onClick:()=>t.onChange(""),children:"Change Image"})]}):(0,r.jsx)(g.I,{type:"file",onChange:e=>{var a;let r=null===(a=e.target.files)||void 0===a?void 0:a[0];r&&t.onChange(r)},onBlur:t.onBlur,className:"w-full border rounded-md px-4 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"})})}),(0,r.jsx)(p.zG,{})]})}}),(0,r.jsx)("div",{className:"col-span-1  md:col-span-2",children:(0,r.jsx)(h.Z,{form:N})}),(0,r.jsx)(y.Separator,{className:"col-span-1 md:col-span-2"}),(0,r.jsx)("div",{className:"col-span-1 md:col-span-2",children:(0,r.jsx)(x.z,{type:"submit",className:"w-full rounded-md px-6 py-3 text-sm font-semibold disabled:opacity-50",disabled:a,children:a?"Loading...":"Update KYC"})})]})]})})}function w(){let e=(0,l.v9)(e=>e.user);return(0,r.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,r.jsx)(n.Z,{menuItemsTop:s.y,menuItemsBottom:s.$,active:"kyc",isKycCheck:!0}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,r.jsx)(i.Z,{menuItemsTop:s.y,menuItemsBottom:s.$,activeMenu:"Personal Info",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Settings",link:"#"},{label:"kyc",link:"#"}]}),(0,r.jsx)("main",{className:"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8",children:(0,r.jsx)(N,{user_id:e.uid})})]})]})}},93033:function(e,t,a){"use strict";a.d(t,{Z:function(){return c}});var r=a(57437),l=a(2265),n=a(66648);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(33480).Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var i=a(89733),o=a(93363),c=e=>{let{form:t}=e,a=(0,l.useRef)(null),c=(0,l.useRef)(null),[d,u]=(0,l.useState)(null),[m,f]=(0,l.useState)(!1),[h,v]=(0,l.useState)(null),x=async()=>{f(!0);try{let e=await navigator.mediaDevices.getUserMedia({video:!0});v(e),a.current&&(a.current.srcObject=e,await new Promise(e=>{a.current.onloadedmetadata=()=>e(!0)}))}catch(e){console.error("Error accessing camera:",e),f(!1)}},p=()=>{if(a.current&&c.current){let e=a.current,r=c.current,l=c.current.getContext("2d");if(l&&e.videoWidth>0&&e.videoHeight>0){r.width=e.videoWidth,r.height=e.videoHeight,l.drawImage(e,0,0,r.width,r.height);let a=r.toDataURL("image/jpeg");u(a),fetch(a).then(e=>e.blob()).then(e=>{let a=new File([e],"live-capture.jpg",{type:"image/jpeg"});t.setValue("liveCaptureUrl",a)}),h&&h.getTracks().forEach(e=>e.stop()),f(!1)}}};return(0,r.jsx)(o.Wi,{control:t.control,name:"liveCaptureUrl",render:e=>{let{field:l}=e;return(0,r.jsxs)(o.xJ,{children:[(0,r.jsx)(o.lX,{children:"Live Capture"}),(0,r.jsx)(o.NI,{children:(0,r.jsx)("div",{className:"flex flex-col md:justify-start md:items-start sm:justify-center items-center gap-2",children:(0,r.jsxs)("div",{className:"flex s gap-2",children:[(d||l.value)&&(0,r.jsx)("div",{className:"flex flex-col items-center",children:(0,r.jsx)(n.default,{src:d||l.value,alt:"Live Capture",width:128,height:128,className:"rounded-md object-cover"})}),!l.value&&d&&(0,r.jsx)(i.z,{type:"button",variant:"outline",size:"sm",className:"mt-2",onClick:()=>{u(null),t.setValue("liveCaptureUrl",""),x()},children:"Retake Photo"}),!m&&(0,r.jsxs)(i.z,{type:"button",onClick:x,children:[(0,r.jsx)(s,{className:"w-4 h-4 mr-2"}),"Live Capture"]}),m&&(0,r.jsx)(i.z,{type:"button",onClick:p,children:"Take Photo"}),m&&(0,r.jsx)("video",{ref:a,autoPlay:!0,className:"w-64 h-48 border rounded-md",children:(0,r.jsx)("track",{kind:"captions",srcLang:"en",label:"English captions",src:""})}),(0,r.jsx)("canvas",{ref:c,className:"hidden",width:"640",height:"480"})]})})}),(0,r.jsx)(o.zG,{})]})}})}},93363:function(e,t,a){"use strict";a.d(t,{NI:function(){return x},Wi:function(){return u},l0:function(){return c},lX:function(){return v},pf:function(){return p},xJ:function(){return h},zG:function(){return g}});var r=a(57437),l=a(2265),n=a(63355),s=a(39343),i=a(49354),o=a(70402);let c=s.RV,d=l.createContext({}),u=e=>{let{...t}=e;return(0,r.jsx)(d.Provider,{value:{name:t.name},children:(0,r.jsx)(s.Qr,{...t})})},m=()=>{let e=l.useContext(d),t=l.useContext(f),{getFieldState:a,formState:r}=(0,s.Gc)(),n=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...n}},f=l.createContext({}),h=l.forwardRef((e,t)=>{let{className:a,...n}=e,s=l.useId();return(0,r.jsx)(f.Provider,{value:{id:s},children:(0,r.jsx)("div",{ref:t,className:(0,i.cn)("space-y-2",a),...n})})});h.displayName="FormItem";let v=l.forwardRef((e,t)=>{let{className:a,...l}=e,{error:n,formItemId:s}=m();return(0,r.jsx)(o.Label,{ref:t,className:(0,i.cn)(n&&"text-destructive",a),htmlFor:s,...l})});v.displayName="FormLabel";let x=l.forwardRef((e,t)=>{let{...a}=e,{error:l,formItemId:s,formDescriptionId:i,formMessageId:o}=m();return(0,r.jsx)(n.g7,{ref:t,id:s,"aria-describedby":l?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!l,...a})});x.displayName="FormControl";let p=l.forwardRef((e,t)=>{let{className:a,...l}=e,{formDescriptionId:n}=m();return(0,r.jsx)("p",{ref:t,id:n,className:(0,i.cn)("text-sm text-muted-foreground",a),...l})});p.displayName="FormDescription";let g=l.forwardRef((e,t)=>{let{className:a,children:l,...n}=e,{error:s,formMessageId:o}=m(),c=s?String(null==s?void 0:s.message):l;return c?(0,r.jsx)("p",{ref:t,id:o,className:(0,i.cn)("text-sm font-medium text-destructive",a),...n,children:c}):null});g.displayName="FormMessage"},70402:function(e,t,a){"use strict";a.r(t),a.d(t,{Label:function(){return c}});var r=a(57437),l=a(2265),n=a(38364),s=a(12218),i=a(49354);let o=(0,s.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)(n.f,{ref:t,className:(0,i.cn)(o(),a),...l})});c.displayName=n.f.displayName},29973:function(e,t,a){"use strict";a.r(t),a.d(t,{Separator:function(){return i}});var r=a(57437),l=a(2265),n=a(48484),s=a(49354);let i=l.forwardRef((e,t)=>{let{className:a,orientation:l="horizontal",decorative:i=!0,...o}=e;return(0,r.jsx)(n.f,{ref:t,decorative:i,orientation:l,className:(0,s.cn)("shrink-0 bg-border","horizontal"===l?"h-[1px] w-full":"h-full w-[1px]",a),...o})});i.displayName=n.f.displayName},27756:function(e,t,a){"use strict";a.d(t,{$:function(){return m},y:function(){return u}});var r=a(57437),l=a(11005),n=a(52022),s=a(25912),i=a(67524),o=a(6540),c=a(40036),d=a(66648);let u=[{href:"#",icon:(0,r.jsx)(d.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,r.jsx)(l.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/settings/personal-info",icon:(0,r.jsx)(n.Z,{className:"h-5 w-5"}),label:"Personal Info"},{href:"/freelancer/settings/professional-info",icon:(0,r.jsx)(s.Z,{className:"h-5 w-5"}),label:"Professional Info"},{href:"/freelancer/settings/projects",icon:(0,r.jsx)(i.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"/freelancer/settings/education-info",icon:(0,r.jsx)(o.Z,{className:"h-5 w-5"}),label:"Education"},{href:"/freelancer/settings/resume",icon:(0,r.jsx)(c.Z,{className:"h-5 w-5"}),label:"Portfolio"}],m=[]},97540:function(e,t,a){"use strict";var r,l,n,s,i,o;a.d(t,{cd:function(){return r},d8:function(){return c},kJ:function(){return l},sB:function(){return n}}),(s=r||(r={})).Mastery="Mastery",s.Proficient="Proficient",s.Beginner="Beginner",(i=l||(l={})).ACTIVE="Active",i.PENDING="Pending",i.REJECTED="Rejected",i.COMPLETED="Completed",(o=n||(n={})).ACTIVE="ACTIVE",o.PENDING="PENDING",o.REJECTED="REJECTED",o.COMPLETED="COMPLETED";let c={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},38364:function(e,t,a){"use strict";a.d(t,{f:function(){return i}});var r=a(2265),l=a(18676),n=a(57437),s=r.forwardRef((e,t)=>(0,n.jsx)(l.WV.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var i=s},48484:function(e,t,a){"use strict";a.d(t,{f:function(){return c}});var r=a(2265),l=a(18676),n=a(57437),s="horizontal",i=["horizontal","vertical"],o=r.forwardRef((e,t)=>{let{decorative:a,orientation:r=s,...o}=e,c=i.includes(r)?r:s;return(0,n.jsx)(l.WV.div,{"data-orientation":c,...a?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:t})});o.displayName="Separator";var c=o}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,1374,2455,9726,2688,2971,7023,1744],function(){return e(e.s=57400)}),_N_E=e.O()}]);