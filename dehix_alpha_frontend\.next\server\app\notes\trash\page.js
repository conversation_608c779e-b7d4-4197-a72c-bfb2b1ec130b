(()=>{var e={};e.id=3259,e.ids=[3259],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},52330:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>d,originalPathname:()=>p,pages:()=>l,routeModule:()=>x,tree:()=>c}),s(4205),s(54302),s(12523);var r=s(23191),i=s(88716),n=s(37922),a=s.n(n),o=s(95231),u={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>o[e]);s.d(t,u);let c=["",{children:["notes",{children:["trash",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4205)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\notes\\trash\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\notes\\trash\\page.tsx"],p="/notes/trash/page",d={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/notes/trash/page",pathname:"/notes/trash",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},84303:(e,t,s)=>{Promise.resolve().then(s.bind(s,74382))},74382:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(10326),i=s(17577),n=s(25842),a=s(77506),o=s(12389),u=s(74935),c=s(92166),l=s(46319),p=s(17978),d=s(40588),x=s(48586);let h=()=>{let e=(0,n.v9)(e=>e.user?.uid),{trash:t,setTrash:s,isLoading:h,fetchNotes:m}=(0,p.Z)(e);return(0,i.useEffect)(()=>{e&&m()},[e,m]),(0,r.jsxs)("section",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[r.jsx(c.Z,{menuItemsTop:l.Ne,menuItemsBottom:x.$C,active:"Trash"}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[r.jsx("div",{children:r.jsx(d.Z,{menuItemsTop:l.Ne,menuItemsBottom:x.$C,activeMenu:"Trash",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Notes",link:"/notes"},{label:"Trash",link:"/trash"}]})}),(0,r.jsxs)("div",{children:[r.jsx(u.Z,{isTrash:!0,setNotes:s,notes:t}),r.jsx("div",{className:"p-6",children:r.jsx("div",{children:h?r.jsx("div",{className:"flex justify-center items-center h-[40vh]",children:r.jsx(a.Z,{className:"my-4 h-8 w-8 animate-spin"})}):r.jsx("div",{children:t?.length>0?r.jsx(o.Z,{fetchNotes:m,isTrash:!0,notes:t,setNotes:s,isArchive:!0}):r.jsx("div",{className:"flex justify-center items-center h-[40vh] w-full",children:r.jsx("p",{children:"No trash here! Add some to get started!"})})})})})]})]})]})}},4205:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>a,__esModule:()=>n,default:()=>o});var r=s(68570);let i=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\notes\trash\page.tsx`),{__esModule:n,$$typeof:a}=i;i.default;let o=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\notes\trash\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588,5442],()=>s(52330));module.exports=r})();