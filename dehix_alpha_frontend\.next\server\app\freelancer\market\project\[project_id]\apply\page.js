(()=>{var e={};e.id=6055,e.ids=[6055],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},61179:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>d}),t(2590),t(54302),t(12523);var a=t(23191),r=t(88716),l=t(37922),i=t.n(l),n=t(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["freelancer",{children:["market",{children:["project",{children:["[project_id]",{children:["apply",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2590)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\market\\project\\[project_id]\\apply\\page.tsx"]}]},{}]},{}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\market\\project\\[project_id]\\apply\\page.tsx"],m="/freelancer/market/project/[project_id]/apply/page",x={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/freelancer/market/project/[project_id]/apply/page",pathname:"/freelancer/market/project/[project_id]/apply",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79034:(e,s,t)=>{Promise.resolve().then(t.bind(t,27681))},40900:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},47546:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},37358:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41291:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},69669:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},48998:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},12714:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},43727:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},40617:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},60763:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69515:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},98091:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},27681:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>V});var a=t(10326),r=t(17577),l=t(35047),i=t(77506),n=t(56627),c=t(92166),d=t(48586),o=t(25842),m=t(79635),x=t(47546),h=t(71821),p=t(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let u=(0,p.Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);var j=t(12714),g=t(48998),f=t(37358),v=t(32933),N=t(82015),y=t(40617),b=t(30361),w=t(69669);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let k=(0,p.Z)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]]);var C=t(41291);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let Z=(0,p.Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),M=({percentage:e,size:s,strokeWidth:t})=>{let r=(s-t)/2,l=2*r*Math.PI;return(0,a.jsxs)("div",{className:"relative",style:{width:s,height:s},children:[(0,a.jsxs)("svg",{width:s,height:s,viewBox:`0 0 ${s} ${s}`,className:"transform -rotate-90",children:[a.jsx("circle",{cx:s/2,cy:s/2,r:r,strokeWidth:t,className:"stroke-gray-800 fill-none"}),a.jsx("circle",{cx:s/2,cy:s/2,r:r,strokeWidth:t,className:"stroke-green-500 fill-none transition-all duration-500 ease-out",strokeDasharray:l,strokeDashoffset:l-e/100*l,strokeLinecap:"round"})]}),a.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:a.jsx("span",{className:"text-3xl font-bold",children:e})})]})},S=({icon:e,label:s,value:t})=>a.jsx("div",{className:"bg-gray-800 p-4 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("div",{className:"text-gray-400",children:e}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-2xl font-bold",children:t}),a.jsx("p",{className:"text-sm text-gray-400",children:s})]})]})});var D=t(29752),A=t(38443),_=t(91664),q=t(94880),P=t(50384);let I=({projectData:e,onClose:s,setShowAnalyticsDrawer:t})=>{let[l,i]=(0,r.useState)("insights"),[n,c]=(0,r.useState)(!1),[d,o]=(0,r.useState)(null);if(!e)return a.jsx(D.Zb,{className:"flex flex-col h-full w-full overflow-auto text-white items-center justify-center",children:a.jsx("p",{children:"Loading project data..."})});let h=(()=>{let s=e.profiles?.reduce((e,s)=>e+(s.totalBid?.length||0),0)||0,t=e.profiles?.reduce((e,s)=>e+(s.selectedFreelancer?.length||0),0)||0,a=e.profiles?.filter(e=>"number"==typeof e.rate&&!isNaN(e.rate))||[],r=a.length>0?a.reduce((e,s)=>e+s.rate,0)/a.length:0,l=a.length>0?Math.max(...a.map(e=>e.rate)):0,i=a.length>0?Math.min(...a.map(e=>e.rate)):0,n=Math.min((e.profiles?.reduce((e,s)=>e+(s.experience||0),0)||0)/(e.profiles?.length||1)*10,100),c=l>0?Math.max(0,100-r/l*50):0;e.createdAt&&(new Date().getTime(),new Date(e.createdAt).getTime());let d=e.maxBidDate?Math.floor((new Date(e.maxBidDate).getTime()-new Date().getTime())/864e5):"N/A";return{score:Math.round((n+c)/2),applied:s,interviews:Math.floor(.15*s),hired:t,terminated:0,jobDuration:"N/A"!==d&&d>0?`${d} days remaining`:"COMPLETED"===e.status?"Completed":"Deadline passed/N/A",startDate:e.startBidDate?new Date(e.startBidDate).toISOString().split("T")[0]:"N/A",endDate:e.maxBidDate?new Date(e.maxBidDate).toISOString().split("T")[0]:"N/A",avgBid:`$${Math.round(r)}/hr`,topBid:`$${l}/hr`,lowBid:`$${i}/hr`,totalBids:s,status:e.status,clientResponsiveness:85+Math.floor(15*Math.random()),projectComplexity:Math.min(15*(e.skillsRequired?.length||0),100),rateCompetitiveness:Math.round(c),clientHistory:{totalProjects:8+Math.floor(12*Math.random()),completionRate:90+Math.floor(10*Math.random()),avgRating:4.2+.8*Math.random()},relevance:Math.min(20*(e.skillsRequired?.length||0),100),timeline:[e.createdAt&&{date:new Date(e.createdAt).toISOString().split("T")[0],event:"Project Posted"},e.startBidDate&&{date:new Date(e.startBidDate).toISOString().split("T")[0],event:"Bidding Started"},{date:new Date().toISOString().split("T")[0],event:"Current Status"},e.maxBidDate&&{date:new Date(e.maxBidDate).toISOString().split("T")[0],event:"Bidding Deadline"}].filter(Boolean),competitorInsights:{avgExperience:`${Math.round((e.profiles?.reduce((e,s)=>e+(s.experience||0),0)||0)/(e.profiles?.length||1))} years`,topSkills:(e.skillsRequired||[]).slice(0,4),bidRange:`$${i}-${l}/hr`}}})(),p=[{icon:a.jsx(m.Z,{className:"h-4 w-4"}),label:"Applied",value:h.applied},{icon:a.jsx(y.Z,{className:"h-4 w-4"}),label:"Interviews",value:h.interviews},{icon:a.jsx(b.Z,{className:"h-4 w-4"}),label:"Hired",value:h.hired},{icon:a.jsx(w.Z,{className:"h-4 w-4"}),label:"Terminated",value:h.terminated}];return(0,a.jsxs)(D.Zb,{className:"flex flex-col h-full w-full overflow-auto text-white  ",children:[a.jsx("div",{className:"p-4 border-b ",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold",children:[e.projectName," - Proposal Details"]}),a.jsx("p",{className:"text-sm text-gray-400",children:e.projectDomain?.join(", ")||"N/A"})]}),a.jsx(_.z,{variant:"outline",size:"sm",className:"text-gray-400 border-gray-700",onClick:()=>t(!1),children:"Close"})]})}),(0,a.jsxs)(P.mQ,{defaultValue:"insights",className:"w-full flex flex-col flex-grow",children:[(0,a.jsxs)(P.dr,{className:"w-full bg-[#09090B] gap-6 rounded-none",children:[a.jsx(P.SP,{value:"insights",className:"data-[state=active]:bg-gray-800 data-[state=active]:text-white hover:bg-gray-700",onClick:()=>i("insights"),children:"Insights"}),a.jsx(P.SP,{value:"timeline",className:"data-[state=active]:bg-gray-800 data-[state=active]:text-white hover:bg-gray-700",onClick:()=>i("timeline"),children:"Timeline"}),a.jsx(P.SP,{value:"competition",className:"data-[state=active]:bg-gray-800 data-[state=active]:text-white hover:bg-gray-700",onClick:()=>i("competition"),children:"Competition"})]}),(0,a.jsxs)("div",{className:"flex-grow overflow-y-auto p-4",children:[" ",(0,a.jsxs)(P.nU,{value:"insights",className:"mt-0",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)(D.Zb,{className:"  col-span-1 bg-gray-850",children:[a.jsx(D.Ol,{className:"pb-2",children:a.jsx(D.ll,{className:"text-sm font-medium text-gray-400",children:"Bid score"})}),a.jsx(D.aY,{className:"flex justify-center",children:a.jsx(M,{percentage:h.score,size:140,strokeWidth:12})})]}),(0,a.jsxs)(D.Zb,{className:"  md:col-span-2 bg-gray-850",children:[a.jsx(D.Ol,{className:"pb-2",children:a.jsx(D.ll,{className:"text-sm font-medium text-gray-400",children:"Metrics"})}),a.jsx(D.aY,{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:p.map((e,s)=>a.jsx(S,{icon:e.icon,label:e.label,value:e.value},s))})]})]}),a.jsx("div",{className:"mt-4 grid grid-cols-1 gap-4",children:(0,a.jsxs)(D.Zb,{className:"  bg-gray-850",children:[a.jsx(D.Ol,{className:"pb-2",children:a.jsx(D.ll,{className:"text-sm font-medium text-gray-400",children:"Project Details"})}),(0,a.jsxs)(D.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-xs text-gray-500",children:"Duration"}),a.jsx("p",{className:"font-medium",children:h.jobDuration})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-xs text-gray-500",children:"Start date"}),a.jsx("p",{className:"font-medium",children:h.startDate})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-xs text-gray-500",children:"Deadline"}),a.jsx("p",{className:"font-medium",children:h.endDate})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-xs text-gray-500",children:"Status"}),a.jsx(A.C,{className:`${"COMPLETED"===e.status?"bg-green-600/20 text-green-500 hover:bg-green-600/30":"ACTIVE"===e.status?"bg-blue-600/20 text-blue-500 hover:bg-blue-600/30":"bg-yellow-600/20 text-yellow-500 hover:bg-yellow-600/30"}`,children:e.status})]})]}),e.budget&&(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("p",{className:"text-sm font-medium",children:"Budget"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:"hourly"===e.budget.type&&e.budget.hourly?(0,a.jsxs)(A.C,{className:"bg-purple-600/20 text-purple-400",children:["Hourly: $",e.budget.hourly.minRate,"-$",e.budget.hourly.maxRate,"/hr (Est."," ",e.budget.hourly.estimatedHours," hrs)"]}):"fixed"===e.budget.type&&e.budget.fixedAmount?(0,a.jsxs)(A.C,{className:"bg-purple-600/20 text-purple-400",children:["Fixed: $",e.budget.fixedAmount]}):a.jsx(A.C,{className:"bg-gray-600/20 text-gray-400",children:"Budget not specified"})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("p",{className:"text-sm font-medium",children:"Required Skills"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:e.skillsRequired&&e.skillsRequired.length>0?e.skillsRequired.map((e,s)=>a.jsx(A.C,{className:"bg-gray-800 text-gray-300",children:e},s)):a.jsx("p",{className:"text-sm text-gray-500",children:"No specific skills listed."})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("p",{className:"text-sm font-medium",children:"Project Profiles"}),e.profiles&&e.profiles.length>0?e.profiles.map((e,s)=>(0,a.jsxs)("div",{className:"border border-gray-700 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[a.jsx("h4",{className:"font-medium",children:e.domain}),(0,a.jsxs)(A.C,{className:"bg-blue-600/20 text-blue-500",children:["$",e.rate,"/hr"]})]}),a.jsx("p",{className:"text-sm text-gray-400 mb-2",children:e.description||"No description provided."}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-2 text-xs",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-500",children:"Required: "}),a.jsx("span",{children:e.freelancersRequired||"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"text-gray-500",children:["Experience:"," "]}),(0,a.jsxs)("span",{children:[e.experience||"N/A"," years"]})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-500",children:"Bids: "}),a.jsx("span",{children:e.totalBid?.length||0})]})]}),(0,a.jsxs)("div",{className:"mt-2 text-xs",children:[(0,a.jsxs)("span",{className:"text-gray-500",children:["Selected Freelancers:"," "]}),a.jsx("span",{children:e.selectedFreelancer?.length||0})]})]},e._id)):a.jsx("p",{className:"text-sm text-gray-500",children:"No project profiles defined."})]})]})]})}),(0,a.jsxs)("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)(D.Zb,{className:"  bg-gray-850",children:[a.jsx(D.Ol,{className:"pb-2",children:a.jsx(D.ll,{className:"text-sm font-medium text-gray-400",children:"About the Client"})}),(0,a.jsxs)(D.aY,{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.companyName||"N/A"}),a.jsx("p",{className:"text-sm text-gray-400",children:e.email||"N/A"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(x.Z,{className:"text-gray-500 h-4 w-4"}),a.jsx("div",{children:(0,a.jsxs)("p",{className:"text-sm",children:[h.clientHistory.totalProjects," projects posted"]})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(b.Z,{className:"text-gray-500 h-4 w-4"}),a.jsx("div",{children:(0,a.jsxs)("p",{className:"text-sm",children:[h.clientHistory.completionRate,"% completion rate"]})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(u,{className:"text-yellow-500 h-4 w-4"}),a.jsx("span",{className:"text-sm",children:h.clientHistory.avgRating.toFixed(1)}),a.jsx("div",{className:"flex ml-1",children:[,,,,,].fill(0).map((e,s)=>a.jsx(u,{size:12,className:s<Math.floor(h.clientHistory.avgRating)?"text-yellow-500 fill-yellow-500":"text-gray-500"},s))})]})]})]}),(0,a.jsxs)(D.Zb,{className:"  md:col-span-2 bg-gray-850",children:[a.jsx(D.Ol,{className:"pb-2",children:a.jsx(D.ll,{className:"text-sm font-medium text-gray-400",children:"Bid Competitiveness"})}),(0,a.jsxs)(D.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-xs text-gray-500",children:"Avg bid"}),a.jsx("p",{className:"font-medium text-green-500",children:h.avgBid})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-xs text-gray-500",children:"Top bid"}),a.jsx("p",{className:"font-medium",children:h.topBid})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-xs text-gray-500",children:"Low bid"}),a.jsx("p",{className:"font-medium",children:h.lowBid})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("p",{className:"text-xs text-gray-500",children:"Relevance"}),(0,a.jsxs)("p",{className:"text-xs font-medium",children:[h.relevance,"%"]})]}),a.jsx(q.E,{value:h.relevance,className:"h-2 bg-gray-800"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("p",{className:"text-xs text-gray-500",children:"Rate competitiveness"}),(0,a.jsxs)("p",{className:"text-xs font-medium",children:[h.rateCompetitiveness,"%"]})]}),a.jsx(q.E,{value:h.rateCompetitiveness,className:"h-2 bg-gray-800"})]})]})]})]})]}),a.jsx(P.nU,{value:"timeline",className:"mt-0",children:(0,a.jsxs)(D.Zb,{className:"  bg-gray-850",children:[a.jsx(D.Ol,{children:a.jsx(D.ll,{className:"text-md font-medium",children:"Project Timeline"})}),a.jsx(D.aY,{children:a.jsx("div",{className:"space-y-4",children:h.timeline&&h.timeline.length>0?h.timeline.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full flex-shrink-0"}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"font-medium",children:e.event}),a.jsx("p",{className:"text-sm text-gray-400",children:e.date})]})]},s)):a.jsx("p",{className:"text-sm text-gray-500",children:"No timeline data available."})})})]})}),(0,a.jsxs)(P.nU,{value:"competition",className:"mt-0",children:[(0,a.jsxs)(D.Zb,{className:"  bg-gray-850",children:[a.jsx(D.Ol,{children:a.jsx(D.ll,{className:"text-md font-medium",children:"Competitor Analysis"})}),(0,a.jsxs)(D.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-sm font-medium mb-2",children:"Average Experience"}),a.jsx("p",{className:"text-2xl font-bold",children:h.competitorInsights.avgExperience})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-sm font-medium mb-2",children:"Bid Range"}),a.jsx("p",{className:"text-2xl font-bold",children:h.competitorInsights.bidRange})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-sm font-medium mb-2",children:"Total Competitors"}),a.jsx("p",{className:"text-2xl font-bold",children:h.totalBids})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-sm font-medium mb-2",children:"Top Skills Among Competitors"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:h.competitorInsights.topSkills&&h.competitorInsights.topSkills.length>0?h.competitorInsights.topSkills.map((e,s)=>a.jsx(A.C,{className:"bg-gray-800 text-gray-300",children:e},s)):a.jsx("p",{className:"text-sm text-gray-500",children:"No top skills identified among competitors."})})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-sm font-medium mb-2",children:"Bid Distribution"}),e.profiles&&e.profiles.length>0?a.jsx("div",{className:"space-y-2",children:e.profiles.map((e,s)=>(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-sm",children:e.domain}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-400",children:[e.totalBid?.length||0," bids"]}),(0,a.jsxs)(A.C,{className:"bg-blue-600/20 text-blue-500",children:["$",e.rate||"N/A","/hr"]})]})]},e._id))}):a.jsx("p",{className:"text-sm text-gray-500",children:"No bid distribution data available."})]})]})]}),(0,a.jsxs)(D.Zb,{className:"  mt-4 bg-gray-850",children:[a.jsx(D.Ol,{children:a.jsx(D.ll,{className:"text-md font-medium",children:"Competitive Edge"})}),a.jsx(D.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[a.jsx("div",{className:"bg-green-500/20 rounded-full p-2",children:a.jsx(k,{className:"h-5 w-5 text-green-500"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium",children:"Strong skill match"}),(0,a.jsxs)("p",{className:"text-sm text-gray-400",children:["Your skills match ",h.relevance,"% of the project requirements."]})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[a.jsx("div",{className:"bg-yellow-500/20 rounded-full p-2",children:a.jsx(C.Z,{className:"h-5 w-5 text-yellow-500"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium",children:"Rate consideration"}),(0,a.jsxs)("p",{className:"text-sm text-gray-400",children:["Your rate competitiveness is"," ",h.rateCompetitiveness,"%. Consider highlighting your unique value proposition."]})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[a.jsx("div",{className:"bg-blue-500/20 rounded-full p-2",children:a.jsx(Z,{className:"h-5 w-5 text-blue-500"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium",children:"Project complexity"}),(0,a.jsxs)("p",{className:"text-sm text-gray-400",children:["This project has a complexity score of"," ",h.projectComplexity,"% based on required skills."]})]})]})]})})]})]})]})]}),a.jsx("div",{className:"mt-auto p-4 border-t  ",children:a.jsx("div",{className:"flex justify-between",children:a.jsx(_.z,{variant:"outline",size:"sm",className:"text-gray-400 border-gray-700",onClick:()=>t(!1),children:"Back to projects"})})})]})};var R=t(41190),$=t(24118),B=t(44794),E=t(6260);let T=({project:e,isLoading:s,onCancel:t})=>{let[l,c]=(0,r.useState)(""),[d,p]=(0,r.useState)(!1),[y,b]=(0,r.useState)(!1),[w,k]=(0,r.useState)(!1),[C,Z]=(0,r.useState)(0),[M,S]=(0,r.useState)(!1),[q,P]=(0,r.useState)(null),[T,z]=(0,r.useState)([]),[V,O]=(0,r.useState)(!1),Y=(0,o.v9)(e=>e.user),[H,L]=(0,r.useState)(0),[F,U]=(0,r.useState)([]);(0,r.useEffect)(()=>{L(parseInt(localStorage.getItem("DHX_CONNECTS")||"0",10));let e=()=>{L(parseInt(localStorage.getItem("DHX_CONNECTS")||"0",10))};return window.addEventListener("connectsUpdated",e),W(),()=>{window.removeEventListener("connectsUpdated",e)}},[Y.uid]);let W=(0,r.useCallback)(async()=>{try{let s=(await E.b.get(`/bid/${Y.uid}/bid`)).data.data.filter(s=>s.project_id===e._id&&s.bidder_id===Y.uid).map(e=>e.profile_id);U(s);let t=e.profiles.filter(e=>s.includes(e._id||""));t.length>0?(console.log("Applied profiles:",t),z(t),k(!0)):(z([]),k(!1))}catch(e){console.error("API Error fetching applied data:",e),(0,n.Am)({variant:"destructive",title:"Error",description:"Failed to retrieve application status. Please try again."})}},[Y.uid,e._id,e.profiles]),X=e?.description?.length>100,G=d||!X?e?.description:e?.description.slice(0,100)+"...",K=async()=>{try{await E.b.patch(`/public/connect?userId=${Y.uid}&isFreelancer=true`),(0,n.Am)({title:"Connects Requested",description:"Your request for more connects has been submitted."});let e=parseInt(localStorage.getItem("DHX_CONNECTS")||"0",10);localStorage.setItem("DHX_CONNECTS",Math.max(0,e+100).toString()),window.dispatchEvent(new Event("connectsUpdated"))}catch(e){console.error("Error requesting connects:",e),(0,n.Am)({title:"Something went wrong",description:"Please try again later."})}},Q=async s=>{if(s.preventDefault(),!q||!q._id){(0,n.Am)({title:"Error",description:"No profile selected for bidding"});return}let t=parseInt(localStorage.getItem("DHX_CONNECTS")||"0",10);if("number"!=typeof q.minConnect||isNaN(C)||isNaN(t)||C>t){(0,n.Am)({description:"Connects are insufficient"});return}if(l.length<500){(0,n.Am)({title:"Cover letter too short",description:"Please write at least 500 characters."});return}if(l.length>2e3){(0,n.Am)({title:"Cover letter too long",description:"Maximum allowed is 2000 characters."});return}S(!0);try{await E.b.post("/bid",{current_price:C,description:l,bidder_id:Y.uid,profile_id:q._id,project_id:e._id,biddingValue:C});let s=(t-C).toString();localStorage.setItem("DHX_CONNECTS",s),window.dispatchEvent(new Event("connectsUpdated")),Z(0),b(!1),k(!0),c(""),(0,n.Am)({title:"Application Submitted",description:"Your application has been successfully submitted."}),W(),O(!0)}catch(e){console.error("Error submitting bid:",e),(0,n.Am)({title:"Something went wrong",description:"Please try again later."})}finally{S(!1)}},J=e?.bids?.length||0,ee=J>0?(e?.bids?.reduce((e,s)=>e+(s?.current_price||0),0)/J).toFixed(2):"N/A",es=new Date(e?.createdAt||Date.now()).toLocaleDateString(),et=F.some(s=>e.profiles.some(e=>e._id===s));return s?a.jsx("div",{className:"text-center py-10",children:a.jsx(i.Z,{className:"animate-spin w-5 h-5"})}):(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[!V&&(0,a.jsxs)(a.Fragment,{children:[" ",(0,a.jsxs)("div",{className:"md:col-span-2 space-y-6",children:[a.jsx(D.Zb,{children:(0,a.jsxs)(D.aY,{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("h1",{className:"text-2xl font-semibold",children:e?.projectName}),(0,a.jsxs)("p",{className:"text-sm",children:["Posted on ",es]})]}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:["Position: ",e?.projectType," Developer"]}),(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("p",{children:G}),X&&a.jsx("button",{onClick:()=>{p(!d)},className:"text-blue-500 hover:underline text-sm mt-2",children:d?"less":"more"})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("h2",{className:"text-lg font-medium mb-2",children:"Skills required"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:e?.skillsRequired?.map((e,s)=>a.jsx(A.C,{variant:"secondary",className:"px-3 py-1 rounded-full bg-gray-200 text-gray-700",children:e},s))})]})]})}),a.jsx(D.Zb,{children:(0,a.jsxs)(D.aY,{className:"p-6",children:[a.jsx("h2",{className:"text-lg font-medium mb-4",children:"Client Information"}),(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[a.jsx("div",{className:"bg-red-500 rounded-full w-8 h-8 flex items-center justify-center text-white",children:a.jsx(m.Z,{size:16})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e?.companyName}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-2 text-sm",children:[a.jsx(x.Z,{size:14}),a.jsx("span",{children:"12 projects posted"}),a.jsx("span",{className:"mx-1",children:"|"}),a.jsx(h.Z,{size:14}),a.jsx("span",{children:"$3.5k spent"})]}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[a.jsx(u,{className:"text-yellow-400",size:16}),a.jsx("span",{className:"ml-1",children:"4.5"})]})]})]}),(0,a.jsxs)("p",{className:"mt-4 text-sm",children:[e?.companyName," is a leading technology company focused on innovative AI solutions. They have a history of successful project completions with freelancers on our platform."]})]})}),a.jsx(D.Zb,{children:(0,a.jsxs)(D.aY,{className:"p-6",children:[a.jsx("h2",{className:"text-lg font-medium mb-4",children:"Project Details"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium",children:"Domains"}),a.jsx("p",{children:e?.projectDomain?.join(", ")})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium",children:"Status"}),a.jsx("p",{children:e?.status})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium",children:"Budget Type"}),a.jsx("p",{children:e?.budget?.type})]}),e?.budget?.type.toUpperCase()==="HOURLY"?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium",children:"Hourly Rate"}),(0,a.jsxs)("p",{children:["$",e?.budget?.hourly?.minRate||0," - $",e?.budget?.hourly?.maxRate||0," /hr"]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium",children:"Estimated Hours"}),(0,a.jsxs)("p",{children:[e?.budget?.hourly?.estimatedHours||0," hours"]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium",children:"Total Budget"}),(0,a.jsxs)("p",{children:["~$",(((e?.budget?.hourly?.minRate||0)+(e?.budget?.hourly?.maxRate||0))/2*(e?.budget?.hourly?.estimatedHours||0)).toLocaleString()]})]})]}):e?.budget?.type.toUpperCase()==="FIXED"?(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium",children:"Fixed Budget"}),(0,a.jsxs)("p",{children:["$",e?.budget?.fixedAmount?.toLocaleString()||0]})]}):null]})]})}),a.jsx(D.Zb,{children:(0,a.jsxs)(D.aY,{className:"p-6",children:[a.jsx("h2",{className:"text-lg font-medium mb-4",children:"Bid Summary"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium",children:"Total Bids"}),a.jsx("p",{children:J})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium",children:"Average Bid"}),(0,a.jsxs)("p",{children:["$",ee]})]})]})]})}),(0,a.jsxs)(D.Zb,{children:[a.jsx(D.Ol,{children:a.jsx(D.ll,{className:"text-lg font-medium",children:"Your Application"})}),(0,a.jsxs)(D.aY,{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("label",{htmlFor:"coverLetter",className:"block mb-2 font-medium",children:"Cover Letter"}),a.jsx(N.g,{value:l,onChange:e=>{let s=e.target.value.replace(/\s{10,}/g," ").replace(/^\s+/,"");s.length<=2e3&&c(s)},placeholder:"Write your cover letter...",rows:8,className:"w-full p-3 border rounded-md resize-none",disabled:et}),a.jsx("div",{className:"text-sm mt-1 text-right",children:(0,a.jsxs)("span",{className:l.length<500?"text-yellow-600":l.length>2e3?"text-red-600":"",children:[l.length<500&&`${500-l.length} characters left to reach minimum.`,l.length>=500&&l.length<=2e3&&`${2e3-l.length} characters left.`,l.length>2e3&&"Character limit exceeded!"]})})]}),(0,a.jsxs)("div",{className:"flex gap-4 mt-4",children:[et?(0,a.jsxs)(_.z,{onClick:()=>{O(!0)},className:"w-full md:w-auto px-8",children:[a.jsx(j.Z,{className:"mr-2 h-4 w-4"})," View Proposal"]}):(0,a.jsxs)($.Vq,{open:y,onOpenChange:b,children:[a.jsx(_.z,{onClick:()=>{e?.profiles&&e.profiles.length>0&&(1===e.profiles.length?(P(e.profiles[0]),Z(e.profiles[0].minConnect||0)):P(null),b(!0))},className:"w-full md:w-auto px-8",disabled:s||w||et,children:s?"Submitting...":et?"Applied":"Apply Now"}),q&&H<(q.minConnect||0)?(0,a.jsxs)($.cZ,{children:[(0,a.jsxs)($.fK,{children:[a.jsx($.$N,{children:"Insufficient Connects"}),(0,a.jsxs)($.Be,{children:["You don't have enough connects to apply for this project.",a.jsx("br",{}),"Please"," ",a.jsx("span",{className:"text-blue-600 font-bold cursor-pointer",onClick:K,children:"Request Connects"})," ","to proceed."]})]}),a.jsx($.cN,{children:a.jsx(_.z,{variant:"outline",onClick:()=>b(!1),children:"Close"})})]}):(0,a.jsxs)($.cZ,{className:"sm:max-w-[425px]",children:[(0,a.jsxs)($.fK,{children:[(0,a.jsxs)($.$N,{children:["Apply for ",e.projectName]}),a.jsx($.Be,{children:"Submit your bid to apply for this project."})]}),(0,a.jsxs)("form",{onSubmit:Q,children:[a.jsx("div",{className:"grid gap-4 py-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[a.jsx(B.Label,{htmlFor:"bidAmount",className:"text-center",children:"Connects"}),(0,a.jsxs)("div",{className:"col-span-3 relative",children:[a.jsx(R.I,{id:"bidAmount",type:"number",value:C,onChange:e=>Z(Number(e.target.value)),className:"w-full pl-2 pr-1",required:!0,min:q?.minConnect,placeholder:"Enter connects amount"}),a.jsx("div",{className:"absolute right-8 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"connects"})]})]})}),a.jsx("div",{className:"flex justify-end",children:a.jsx(_.z,{type:"submit",disabled:w||M,children:M?a.jsx(i.Z,{className:"animate-spin w-6 h-6"}):w?"Applied":"Submit Bid"})})]})]})]}),a.jsx(_.z,{onClick:t,variant:"outline",className:"w-full md:w-auto px-8",children:"Back"})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx(D.Zb,{children:(0,a.jsxs)(D.aY,{className:"p-6",children:[a.jsx("h2",{className:"text-lg font-medium mb-4",children:"Experience"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(x.Z,{className:"text-gray-500",size:18}),a.jsx("div",{children:a.jsx("p",{className:"font-medium",children:"3+ yrs"})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(h.Z,{className:"text-gray-500",size:18}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:"Hourly rate"}),(0,a.jsxs)("p",{children:["$",e?.budget?.hourly?.minRate||0," - $",e?.budget?.hourly?.maxRate||0]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(g.Z,{className:"text-gray-500",size:18}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:"Time per week"}),a.jsx("p",{children:"40 hours"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(f.Z,{className:"text-gray-500",size:18}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:"Project length"}),a.jsx("p",{children:"3 to 5 months"})]})]})]})]})}),e?.profiles?.length>0&&(0,a.jsxs)(D.Zb,{children:[a.jsx(D.Ol,{children:a.jsx(D.ll,{className:"text-lg font-medium",children:"Profiles Needed"})}),a.jsx(D.aY,{className:"p-6",children:a.jsx("div",{className:"space-y-4 h-[70vh] overflow-y-scroll no-scrollbar",children:e?.profiles?.map((e,s)=>a.jsx(D.Zb,{className:"border border-gray-200",children:a.jsxs(D.aY,{className:"p-4",children:[a.jsxs("div",{className:"flex justify-between items-center mb-3",children:[a.jsxs("h3",{className:"font-medium text-xs",children:[e?.domain," Developer"]}),a.jsxs(A.C,{variant:"outline",children:[e?.freelancersRequired," Needed"]})]}),a.jsx("p",{className:"mb-3 text-sm",children:e?.description}),a.jsxs("div",{className:"grid grid-cols-2 gap-3 mb-3",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-xs text-gray-500",children:"Experience"}),a.jsxs("p",{className:"font-medium",children:[e?.experience,"+ years"]})]}),a.jsxs("div",{children:[a.jsx("p",{className:"text-xs text-gray-500",children:"Rate"}),a.jsxs("p",{className:"font-medium",children:["$",e?.rate,"/hr"]})]}),a.jsxs("div",{children:[a.jsx("p",{className:"text-xs text-gray-500",children:"Minimum Connect"}),a.jsx("p",{className:"font-medium",children:e?.minConnect})]})]}),a.jsxs("div",{children:[a.jsx("p",{className:"text-xs text-gray-500 mb-2",children:"Skills"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:e?.skills?.map((e,s)=>a.jsx(A.C,{variant:"secondary",className:"border border-gray-200",children:e},s))})]}),a.jsx(_.z,{size:"sm",className:`w-full mt-4 ${F.includes(e._id||"")?"cursor-not-allowed":""}`,onClick:()=>{F.includes(e._id||"")?O(!0):(P(e),Z(e.minConnect||0),b(!0))},disabled:F.includes(e._id||"")||w,children:F.includes(e._id||"")?a.jsxs("span",{className:"flex items-center justify-center",children:[a.jsx(v.Z,{className:"mr-2 h-4 w-4"})," Applied"]}):"Apply for this role"})]})},e?._id||s))})})]})]})]}),V&&(0,a.jsxs)("div",{className:"col-span-1 md:col-span-3",children:[" ",a.jsx(I,{projectData:e,setShowAnalyticsDrawer:O})]})]})};var z=t(40588);let V=()=>{let e=(0,l.useParams)(),s=(0,l.useRouter)(),{toast:t}=(0,n.pm)(),[o,m]=(0,r.useState)(!1),[x,h]=(0,r.useState)(null),[p,u]=(0,r.useState)(""),j=e?.project_id||"123";return((0,r.useEffect)(()=>{(async()=>{try{m(!0);let e=await E.b.get(`/project/project/${j}`);h(e.data.data[0])}catch(e){console.error("Error fetching project details:",e),t({title:"Error",description:"Failed to load project details",variant:"destructive"})}finally{m(!1)}})()},[j,t]),o&&!x)?a.jsx("div",{className:"flex items-center justify-center h-screen",children:a.jsx("div",{className:"text-lg",children:a.jsx(i.Z,{className:"animate-spin w-5 h-5"})})}):(0,a.jsxs)("div",{className:"flex min-h-screen w-full bg-muted flex-col",children:[a.jsx(c.Z,{menuItemsTop:d.yn,menuItemsBottom:d.$C,active:"Market"}),(0,a.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[a.jsx(z.Z,{menuItemsTop:d.yn,menuItemsBottom:d.$C,activeMenu:"Market",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Marketplace",link:"#"}]}),a.jsx("main",{className:"w-[85vw] mx-auto text-foreground",children:x&&a.jsx(T,{project:x,isLoading:o,onCancel:()=>{s.back()}})})]})]})}},44794:(e,s,t)=>{"use strict";t.r(s),t.d(s,{Label:()=>d});var a=t(10326),r=t(17577),l=t(34478),i=t(28671),n=t(51223);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...s},t)=>a.jsx(l.f,{ref:t,className:(0,n.cn)(c(),e),...s}));d.displayName=l.f.displayName},94880:(e,s,t)=>{"use strict";t.d(s,{E:()=>y});var a=t(10326),r=t(17577),l=t(93095),i=t(77335),n="Progress",[c,d]=(0,l.b)(n),[o,m]=c(n),x=r.forwardRef((e,s)=>{var t,r;let{__scopeProgress:l,value:n=null,max:c,getValueLabel:d=u,...m}=e;(c||0===c)&&!f(c)&&console.error((t=`${c}`,`Invalid prop \`max\` of value \`${t}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let x=f(c)?c:100;null===n||v(n,x)||console.error((r=`${n}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let h=v(n,x)?n:null,p=g(h)?d(h,x):void 0;return(0,a.jsx)(o,{scope:l,value:h,max:x,children:(0,a.jsx)(i.WV.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":g(h)?h:void 0,"aria-valuetext":p,role:"progressbar","data-state":j(h,x),"data-value":h??void 0,"data-max":x,...m,ref:s})})});x.displayName=n;var h="ProgressIndicator",p=r.forwardRef((e,s)=>{let{__scopeProgress:t,...r}=e,l=m(h,t);return(0,a.jsx)(i.WV.div,{"data-state":j(l.value,l.max),"data-value":l.value??void 0,"data-max":l.max,...r,ref:s})});function u(e,s){return`${Math.round(e/s*100)}%`}function j(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function g(e){return"number"==typeof e}function f(e){return g(e)&&!isNaN(e)&&e>0}function v(e,s){return g(e)&&!isNaN(e)&&e<=s&&e>=0}p.displayName=h;var N=t(51223);let y=r.forwardRef(({className:e,value:s,...t},r)=>a.jsx(x,{ref:r,className:(0,N.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...t,children:a.jsx(p,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})}));y.displayName=x.displayName},50384:(e,s,t)=>{"use strict";t.d(s,{SP:()=>d,dr:()=>c,mQ:()=>n,nU:()=>o});var a=t(10326),r=t(17577),l=t(28407),i=t(51223);let n=l.fC,c=r.forwardRef(({className:e,...s},t)=>a.jsx(l.aV,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));c.displayName=l.aV.displayName;let d=r.forwardRef(({className:e,...s},t)=>a.jsx(l.xz,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));d.displayName=l.xz.displayName;let o=r.forwardRef(({className:e,...s},t)=>a.jsx(l.VY,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));o.displayName=l.VY.displayName},48586:(e,s,t)=>{"use strict";t.d(s,{yL:()=>y,$C:()=>N,yn:()=>v});var a=t(10326),r=t(95920),l=t(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,l.Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]]),n=(0,l.Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);var c=t(43727);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,l.Z)("TabletSmartphone",[["rect",{width:"10",height:"14",x:"3",y:"8",rx:"2",key:"1vrsiq"}],["path",{d:"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4",key:"1j4zmg"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),o=(0,l.Z)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]]);var m=t(60763);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let x=(0,l.Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);var h=t(40617),p=t(69515),u=t(88378),j=t(40900),g=t(98091),f=t(46226);let v=[{href:"#",icon:a.jsx(f.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:a.jsx(r.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:a.jsx(i,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:a.jsx(n,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:a.jsx(c.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:a.jsx(d,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:a.jsx(o,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:a.jsx(m.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:a.jsx(x,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:a.jsx(h.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:a.jsx(p.Z,{className:"h-5 w-5"}),label:"Notes"}],N=[{href:"/freelancer/settings/personal-info",icon:a.jsx(u.Z,{className:"h-5 w-5"}),label:"Settings"}];f.default,r.Z,p.Z,j.Z,g.Z;let y=[{href:"#",icon:a.jsx(f.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:a.jsx(r.Z,{className:"h-5 w-5"}),label:"Home"}]},2590:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var a=t(68570);let r=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\market\project\[project_id]\apply\page.tsx`),{__esModule:l,$$typeof:i}=r;r.default;let n=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\market\project\[project_id]\apply\page.tsx#default`)},34478:(e,s,t)=>{"use strict";t.d(s,{f:()=>n});var a=t(17577),r=t(77335),l=t(10326),i=a.forwardRef((e,s)=>(0,l.jsx)(r.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var n=i},28407:(e,s,t)=>{"use strict";t.d(s,{VY:()=>_,aV:()=>D,fC:()=>S,xz:()=>A});var a=t(17577),r=t(82561),l=t(93095),i=t(15594),n=t(9815),c=t(77335),d=t(17124),o=t(52067),m=t(88957),x=t(10326),h="Tabs",[p,u]=(0,l.b)(h,[i.Pc]),j=(0,i.Pc)(),[g,f]=p(h),v=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,onValueChange:r,defaultValue:l,orientation:i="horizontal",dir:n,activationMode:h="automatic",...p}=e,u=(0,d.gm)(n),[j,f]=(0,o.T)({prop:a,onChange:r,defaultProp:l});return(0,x.jsx)(g,{scope:t,baseId:(0,m.M)(),value:j,onValueChange:f,orientation:i,dir:u,activationMode:h,children:(0,x.jsx)(c.WV.div,{dir:u,"data-orientation":i,...p,ref:s})})});v.displayName=h;var N="TabsList",y=a.forwardRef((e,s)=>{let{__scopeTabs:t,loop:a=!0,...r}=e,l=f(N,t),n=j(t);return(0,x.jsx)(i.fC,{asChild:!0,...n,orientation:l.orientation,dir:l.dir,loop:a,children:(0,x.jsx)(c.WV.div,{role:"tablist","aria-orientation":l.orientation,...r,ref:s})})});y.displayName=N;var b="TabsTrigger",w=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,disabled:l=!1,...n}=e,d=f(b,t),o=j(t),m=Z(d.baseId,a),h=M(d.baseId,a),p=a===d.value;return(0,x.jsx)(i.ck,{asChild:!0,...o,focusable:!l,active:p,children:(0,x.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":h,"data-state":p?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:m,...n,ref:s,onMouseDown:(0,r.M)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(a)}),onKeyDown:(0,r.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(a)}),onFocus:(0,r.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;p||l||!e||d.onValueChange(a)})})})});w.displayName=b;var k="TabsContent",C=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,forceMount:l,children:i,...d}=e,o=f(k,t),m=Z(o.baseId,r),h=M(o.baseId,r),p=r===o.value,u=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>u.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,x.jsx)(n.z,{present:l||p,children:({present:t})=>(0,x.jsx)(c.WV.div,{"data-state":p?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":m,hidden:!t,id:h,tabIndex:0,...d,ref:s,style:{...e.style,animationDuration:u.current?"0s":void 0},children:t&&i})})});function Z(e,s){return`${e}-trigger-${s}`}function M(e,s){return`${e}-content-${s}`}C.displayName=k;var S=v,D=y,A=w,_=C}};var s=require("../../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588],()=>t(61179));module.exports=a})();