"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1272],{86494:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Chrome",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["line",{x1:"21.17",x2:"12",y1:"8",y2:"8",key:"a0cw5f"}],["line",{x1:"3.95",x2:"8.54",y1:"6.06",y2:"14",key:"1kftof"}],["line",{x1:"10.88",x2:"15.46",y1:"21.94",y2:"14",key:"1ymyh8"}]])},32309:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Dot",[["circle",{cx:"12.1",cy:"12.1",r:"1",key:"18d7e5"}]])},47019:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},75733:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},25489:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]])},3274:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},32226:function(e,t,n){n.d(t,{x8:function(){return ei},VY:function(){return eo},dk:function(){return ea},Vq:function(){return R},cZ:function(){return T},t9:function(){return F},aV:function(){return er},h_:function(){return en},fC:function(){return ee},Dx:function(){return el},xz:function(){return et}});var r=n(2265),o=n(78149),l=n(1584),a=n(98324),i=n(53201),u=n(91715),c=n(53938),s=n(80467),d=n(56935),p=n(31383),f=n(18676),m=n(20589),g=n(9219),v=n(78369),h=n(57437),y=r.forwardRef((e,t)=>{let{children:n,...o}=e,l=r.Children.toArray(n),a=l.find(x);if(a){let e=a.props.children,n=l.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,h.jsx)(b,{...o,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,h.jsx)(b,{...o,ref:t,children:n})});y.displayName="Slot";var b=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e,a;let i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref;return r.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...e)=>{l(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props),ref:t?(0,l.F)(t,i):i})}return r.Children.count(n)>1?r.Children.only(null):null});b.displayName="SlotClone";var w=({children:e})=>(0,h.jsx)(h.Fragment,{children:e});function x(e){return r.isValidElement(e)&&e.type===w}var E="Dialog",[C,k]=(0,a.b)(E),[D,M]=C(E),R=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:l,onOpenChange:a,modal:c=!0}=e,s=r.useRef(null),d=r.useRef(null),[p=!1,f]=(0,u.T)({prop:o,defaultProp:l,onChange:a});return(0,h.jsx)(D,{scope:t,triggerRef:s,contentRef:d,contentId:(0,i.M)(),titleId:(0,i.M)(),descriptionId:(0,i.M)(),open:p,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:c,children:n})};R.displayName=E;var j="DialogTrigger",S=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=M(j,n),i=(0,l.e)(t,a.triggerRef);return(0,h.jsx)(f.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":K(a.open),...r,ref:i,onClick:(0,o.M)(e.onClick,a.onOpenToggle)})});S.displayName=j;var P="DialogPortal",[O,I]=C(P,{forceMount:void 0}),W=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:l}=e,a=M(P,t);return(0,h.jsx)(O,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,h.jsx)(p.z,{present:n||a.open,children:(0,h.jsx)(d.h,{asChild:!0,container:l,children:e})}))})};W.displayName=P;var _="DialogOverlay",F=r.forwardRef((e,t)=>{let n=I(_,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=M(_,e.__scopeDialog);return l.modal?(0,h.jsx)(p.z,{present:r||l.open,children:(0,h.jsx)(A,{...o,ref:t})}):null});F.displayName=_;var A=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=M(_,n);return(0,h.jsx)(g.Z,{as:y,allowPinchZoom:!0,shards:[o.contentRef],children:(0,h.jsx)(f.WV.div,{"data-state":K(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),N="DialogContent",T=r.forwardRef((e,t)=>{let n=I(N,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=M(N,e.__scopeDialog);return(0,h.jsx)(p.z,{present:r||l.open,children:l.modal?(0,h.jsx)(B,{...o,ref:t}):(0,h.jsx)(V,{...o,ref:t})})});T.displayName=N;var B=r.forwardRef((e,t)=>{let n=M(N,e.__scopeDialog),a=r.useRef(null),i=(0,l.e)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Ry)(e)},[]),(0,h.jsx)(Z,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),V=r.forwardRef((e,t)=>{let n=M(N,e.__scopeDialog),o=r.useRef(!1),l=r.useRef(!1);return(0,h.jsx)(Z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,a;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current||null===(a=n.triggerRef.current)||void 0===a||a.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{var r,a;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(l.current=!0));let i=t.target;(null===(a=n.triggerRef.current)||void 0===a?void 0:a.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),Z=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,...u}=e,d=M(N,n),p=r.useRef(null),f=(0,l.e)(t,p);return(0,m.EW)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(s.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:i,children:(0,h.jsx)(c.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":K(d.open),...u,ref:f,onDismiss:()=>d.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(J,{titleId:d.titleId}),(0,h.jsx)(Q,{contentRef:p,descriptionId:d.descriptionId})]})]})}),L="DialogTitle",z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=M(L,n);return(0,h.jsx)(f.WV.h2,{id:o.titleId,...r,ref:t})});z.displayName=L;var H="DialogDescription",$=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=M(H,n);return(0,h.jsx)(f.WV.p,{id:o.descriptionId,...r,ref:t})});$.displayName=H;var q="DialogClose",G=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=M(q,n);return(0,h.jsx)(f.WV.button,{type:"button",...r,ref:t,onClick:(0,o.M)(e.onClick,()=>l.onOpenChange(!1))})});function K(e){return e?"open":"closed"}G.displayName=q;var U="DialogTitleWarning",[X,Y]=(0,a.k)(U,{contentName:N,titleName:L,docsSlug:"dialog"}),J=e=>{let{titleId:t}=e,n=Y(U),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},Q=e=>{let{contentRef:t,descriptionId:n}=e,o=Y("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(l)},[l,t,n]),null},ee=R,et=S,en=W,er=F,eo=T,el=z,ea=$,ei=G},38364:function(e,t,n){n.d(t,{f:function(){return i}});var r=n(2265),o=n(18676),l=n(57437),a=r.forwardRef((e,t)=>(0,l.jsx)(o.WV.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var i=a},66431:function(e,t,n){n.d(t,{VM:function(){return m},uZ:function(){return g}});var r=n(2265),o=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,s=(e,t,n)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,d=(e,t)=>{for(var n in t||(t={}))u.call(t,n)&&s(e,n,t[n]);if(i)for(var n of i(t))c.call(t,n)&&s(e,n,t[n]);return e},p=(e,t)=>l(e,a(t)),f=(e,t)=>{var n={};for(var r in e)u.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&i)for(var r of i(e))0>t.indexOf(r)&&c.call(e,r)&&(n[r]=e[r]);return n},m=r.createContext({}),g=r.forwardRef((e,t)=>{let n;var o,l,a,i,u,{value:c,onChange:s,maxLength:g,textAlign:y="left",pattern:b="^\\d+$",inputMode:w="numeric",onComplete:x,pushPasswordManagerStrategy:E="increase-width",containerClassName:C,noScriptCSSFallback:k=h,render:D,children:M}=e,R=f(e,["value","onChange","maxLength","textAlign","pattern","inputMode","onComplete","pushPasswordManagerStrategy","containerClassName","noScriptCSSFallback","render","children"]);let[j,S]=r.useState("string"==typeof R.defaultValue?R.defaultValue:""),P=null!=c?c:j,O=(n=r.useRef(),r.useEffect(()=>{n.current=P}),n.current),I=r.useCallback(e=>{null==s||s(e),S(e)},[s]),W=r.useMemo(()=>b?"string"==typeof b?new RegExp(b):b:null,[b]),_=r.useRef(null),F=r.useRef(null),A=r.useRef({value:P,onChange:I,isIOS:"undefined"!=typeof window&&(null==(l=null==(o=null==window?void 0:window.CSS)?void 0:o.supports)?void 0:l.call(o,"-webkit-touch-callout","none"))}),N=r.useRef({prev:[null==(a=_.current)?void 0:a.selectionStart,null==(i=_.current)?void 0:i.selectionEnd,null==(u=_.current)?void 0:u.selectionDirection]});r.useImperativeHandle(t,()=>_.current,[]),r.useEffect(()=>{let e=_.current,t=F.current;if(!e||!t)return;function n(){if(document.activeElement!==e){z(null),$(null);return}let t=e.selectionStart,n=e.selectionEnd,r=e.selectionDirection,o=e.maxLength,l=e.value,a=N.current.prev,i=-1,u=-1,c;if(0!==l.length&&null!==t&&null!==n){let e=t===n,r=t===l.length&&l.length<o;if(e&&!r){if(0===t)i=0,u=1,c="forward";else if(t===o)i=t-1,u=t,c="backward";else if(o>1&&l.length>1){let e=0;if(null!==a[0]&&null!==a[1]){c=t<a[1]?"backward":"forward";let n=a[0]===a[1]&&a[0]<o;"backward"!==c||n||(e=-1)}i=e+t,u=e+t+1}}-1!==i&&-1!==u&&i!==u&&_.current.setSelectionRange(i,u,c)}let s=-1!==i?i:t,d=-1!==u?u:n,p=null!=c?c:r;z(s),$(d),N.current.prev=[s,d,p]}if(A.current.value!==e.value&&A.current.onChange(e.value),N.current.prev=[e.selectionStart,e.selectionEnd,e.selectionDirection],document.addEventListener("selectionchange",n,{capture:!0}),n(),document.activeElement===e&&Z(!0),!document.getElementById("input-otp-style")){let e=document.createElement("style");if(e.id="input-otp-style",document.head.appendChild(e),e.sheet){let t="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";v(e.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),v(e.sheet,`[data-input-otp]:autofill { ${t} }`),v(e.sheet,`[data-input-otp]:-webkit-autofill { ${t} }`),v(e.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),v(e.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let r=()=>{t&&t.style.setProperty("--root-height",`${e.clientHeight}px`)};r();let o=new ResizeObserver(r);return o.observe(e),()=>{document.removeEventListener("selectionchange",n,{capture:!0}),o.disconnect()}},[]);let[T,B]=r.useState(!1),[V,Z]=r.useState(!1),[L,z]=r.useState(null),[H,$]=r.useState(null);r.useEffect(()=>{var e;setTimeout(e=()=>{var e,t,n,r;null==(e=_.current)||e.dispatchEvent(new Event("input"));let o=null==(t=_.current)?void 0:t.selectionStart,l=null==(n=_.current)?void 0:n.selectionEnd,a=null==(r=_.current)?void 0:r.selectionDirection;null!==o&&null!==l&&(z(o),$(l),N.current.prev=[o,l,a])},0),setTimeout(e,10),setTimeout(e,50)},[P,V]),r.useEffect(()=>{void 0!==O&&P!==O&&O.length<g&&P.length===g&&(null==x||x(P))},[g,x,O,P]);let q=function({containerRef:e,inputRef:t,pushPasswordManagerStrategy:n,isFocused:o}){let l=r.useRef({done:!1,refocused:!1}),[a,i]=r.useState(!1),[u,c]=r.useState(!1),[s,d]=r.useState(!1),p=r.useMemo(()=>"none"!==n&&("increase-width"===n||"experimental-no-flickering"===n)&&a&&u,[a,u,n]),f=r.useCallback(()=>{let r=e.current,o=t.current;if(!r||!o||s||"none"===n)return;let a=r.getBoundingClientRect().left+r.offsetWidth,u=r.getBoundingClientRect().top+r.offsetHeight/2;if(!(0===document.querySelectorAll('[data-lastpass-icon-root],com-1password-button,[data-dashlanecreated],[style$="2147483647 !important;"]').length&&document.elementFromPoint(a-18,u)===r)&&(i(!0),d(!0),!l.current.refocused&&document.activeElement===o)){let e=[o.selectionStart,o.selectionEnd];o.blur(),o.focus(),o.setSelectionRange(e[0],e[1]),l.current.refocused=!0}},[e,t,s,n]);return r.useEffect(()=>{let t=e.current;if(!t||"none"===n)return;function r(){c(window.innerWidth-t.getBoundingClientRect().right>=40)}r();let o=setInterval(r,1e3);return()=>{clearInterval(o)}},[e,n]),r.useEffect(()=>{let e=o||document.activeElement===t.current;if("none"===n||!e)return;let r=setTimeout(f,0),l=setTimeout(f,2e3),a=setTimeout(f,5e3),i=setTimeout(()=>{d(!0)},6e3);return()=>{clearTimeout(r),clearTimeout(l),clearTimeout(a),clearTimeout(i)}},[t,o,n,f]),{hasPWMBadge:a,willPushPWMBadge:p,PWM_BADGE_SPACE_WIDTH:"40px"}}({containerRef:F,inputRef:_,pushPasswordManagerStrategy:E,isFocused:V}),G=r.useCallback(e=>{let t=e.currentTarget.value.slice(0,g);if(t.length>0&&W&&!W.test(t)){e.preventDefault();return}"string"==typeof O&&t.length<O.length&&document.dispatchEvent(new Event("selectionchange")),I(t)},[g,I,O,W]),K=r.useCallback(()=>{var e;if(_.current){let t=Math.min(_.current.value.length,g-1),n=_.current.value.length;null==(e=_.current)||e.setSelectionRange(t,n),z(t),$(n)}Z(!0)},[g]),U=r.useCallback(e=>{var t,n;let r=_.current;if(!A.current.isIOS||!e.clipboardData||!r)return;let o=e.clipboardData.getData("text/plain");e.preventDefault();let l=null==(t=_.current)?void 0:t.selectionStart,a=null==(n=_.current)?void 0:n.selectionEnd,i=(l!==a?P.slice(0,l)+o+P.slice(a):P.slice(0,l)+o+P.slice(l)).slice(0,g);if(i.length>0&&W&&!W.test(i))return;r.value=i,I(i);let u=Math.min(i.length,g-1),c=i.length;r.setSelectionRange(u,c),z(u),$(c)},[g,I,W,P]),X=r.useMemo(()=>({position:"relative",cursor:R.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[R.disabled]),Y=r.useMemo(()=>({position:"absolute",inset:0,width:q.willPushPWMBadge?`calc(100% + ${q.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:q.willPushPWMBadge?`inset(0 ${q.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:y,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[q.PWM_BADGE_SPACE_WIDTH,q.willPushPWMBadge,y]),J=r.useMemo(()=>r.createElement("input",p(d({autoComplete:R.autoComplete||"one-time-code"},R),{"data-input-otp":!0,"data-input-otp-mss":L,"data-input-otp-mse":H,inputMode:w,pattern:null==W?void 0:W.source,style:Y,maxLength:g,value:P,ref:_,onPaste:e=>{var t;U(e),null==(t=R.onPaste)||t.call(R,e)},onChange:G,onMouseOver:e=>{var t;B(!0),null==(t=R.onMouseOver)||t.call(R,e)},onMouseLeave:e=>{var t;B(!1),null==(t=R.onMouseLeave)||t.call(R,e)},onFocus:e=>{var t;K(),null==(t=R.onFocus)||t.call(R,e)},onBlur:e=>{var t;Z(!1),null==(t=R.onBlur)||t.call(R,e)}})),[G,K,U,w,Y,g,H,L,R,null==W?void 0:W.source,P]),Q=r.useMemo(()=>({slots:Array.from({length:g}).map((e,t)=>{let n=V&&null!==L&&null!==H&&(L===H&&t===L||t>=L&&t<H),r=void 0!==P[t]?P[t]:null;return{char:r,isActive:n,hasFakeCaret:n&&null===r}}),isFocused:V,isHovering:!R.disabled&&T}),[V,T,g,H,L,R.disabled,P]),ee=r.useMemo(()=>D?D(Q):r.createElement(m.Provider,{value:Q},M),[M,Q,D]);return r.createElement(r.Fragment,null,null!==k&&r.createElement("noscript",null,r.createElement("style",null,k)),r.createElement("div",{ref:F,"data-input-otp-container":!0,style:X,className:C},ee,r.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},J)))});function v(e,t){try{e.insertRule(t)}catch(e){console.error("input-otp could not insert CSS rule:",t)}}g.displayName="Input";var h=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`}}]);