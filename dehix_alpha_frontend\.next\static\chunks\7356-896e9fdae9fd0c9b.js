"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7356],{89896:function(e,r,t){t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(33480).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},37193:function(e,r,t){t.d(r,{Ns:function(){return er},fC:function(){return Q},gb:function(){return L},q4:function(){return H},l_:function(){return ee}});var n=t(2265),o=t(54887);function l(...e){return r=>e.forEach(e=>{"function"==typeof e?e(r):null!=e&&(e.current=r)})}function i(...e){return n.useCallback(l(...e),e)}var a=t(57437),s=n.forwardRef((e,r)=>{let{children:t,...o}=e,l=n.Children.toArray(t),i=l.find(d);if(i){let e=i.props.children,t=l.map(r=>r!==i?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(u,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,t):null})}return(0,a.jsx)(u,{...o,ref:r,children:t})});s.displayName="Slot";var u=n.forwardRef((e,r)=>{let{children:t,...o}=e;if(n.isValidElement(t)){let e,i;let a=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref;return n.cloneElement(t,{...function(e,r){let t={...r};for(let n in r){let o=e[n],l=r[n];/^on[A-Z]/.test(n)?o&&l?t[n]=(...e)=>{l(...e),o(...e)}:o&&(t[n]=o):"style"===n?t[n]={...o,...l}:"className"===n&&(t[n]=[o,l].filter(Boolean).join(" "))}return{...e,...t}}(o,t.props),ref:r?l(r,a):a})}return n.Children.count(t)>1?n.Children.only(null):null});u.displayName="SlotClone";var c=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function d(e){return n.isValidElement(e)&&e.type===c}var f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=n.forwardRef((e,t)=>{let{asChild:n,...o}=e,l=n?s:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(l,{...o,ref:t})});return t.displayName=`Primitive.${r}`,{...e,[r]:t}},{}),p=globalThis?.document?n.useLayoutEffect:()=>{},v=e=>{var r,t;let l,a;let{present:s,children:u}=e,c=function(e){var r,t;let[l,i]=n.useState(),a=n.useRef({}),s=n.useRef(e),u=n.useRef("none"),[c,d]=(r=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,r)=>{let n=t[e][r];return null!=n?n:e},r));return n.useEffect(()=>{let e=h(a.current);u.current="mounted"===c?e:"none"},[c]),p(()=>{let r=a.current,t=s.current;if(t!==e){let n=u.current,o=h(r);e?d("MOUNT"):"none"===o||(null==r?void 0:r.display)==="none"?d("UNMOUNT"):t&&n!==o?d("ANIMATION_OUT"):d("UNMOUNT"),s.current=e}},[e,d]),p(()=>{if(l){let e=e=>{let r=h(a.current).includes(e.animationName);e.target===l&&r&&o.flushSync(()=>d("ANIMATION_END"))},r=e=>{e.target===l&&(u.current=h(a.current))};return l.addEventListener("animationstart",r),l.addEventListener("animationcancel",e),l.addEventListener("animationend",e),()=>{l.removeEventListener("animationstart",r),l.removeEventListener("animationcancel",e),l.removeEventListener("animationend",e)}}d("ANIMATION_END")},[l,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{e&&(a.current=getComputedStyle(e)),i(e)},[])}}(s),d="function"==typeof u?u({present:c.isPresent}):n.Children.only(u),f=i(c.ref,(l=null===(r=Object.getOwnPropertyDescriptor(d.props,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in l&&l.isReactWarning?d.ref:(l=null===(t=Object.getOwnPropertyDescriptor(d,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in l&&l.isReactWarning?d.props.ref:d.props.ref||d.ref);return"function"==typeof u||c.isPresent?n.cloneElement(d,{ref:f}):null};function h(e){return(null==e?void 0:e.animationName)||"none"}function w(e){let r=n.useRef(e);return n.useEffect(()=>{r.current=e}),n.useMemo(()=>(...e)=>r.current?.(...e),[])}v.displayName="Presence";var m=n.createContext(void 0),g=t(62361);function b(e,r,{checkForDefaultPrevented:t=!0}={}){return function(n){if(e?.(n),!1===t||!n.defaultPrevented)return r?.(n)}}var y="ScrollArea",[S,E]=function(e,r=[]){let t=[],o=()=>{let r=t.map(e=>n.createContext(e));return function(t){let o=t?.[e]||r;return n.useMemo(()=>({[`__scope${e}`]:{...t,[e]:o}}),[t,o])}};return o.scopeName=e,[function(r,o){let l=n.createContext(o),i=t.length;function s(r){let{scope:t,children:o,...s}=r,u=t?.[e][i]||l,c=n.useMemo(()=>s,Object.values(s));return(0,a.jsx)(u.Provider,{value:c,children:o})}return t=[...t,o],s.displayName=r+"Provider",[s,function(t,a){let s=a?.[e][i]||l,u=n.useContext(s);if(u)return u;if(void 0!==o)return o;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=t.reduce((r,{useScope:t,scopeName:n})=>{let o=t(e)[`__scope${n}`];return{...r,...o}},{});return n.useMemo(()=>({[`__scope${r.scopeName}`]:o}),[o])}};return t.scopeName=r.scopeName,t}(o,...r)]}(y),[x,C]=S(y),R=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,type:o="hover",dir:l,scrollHideDelay:s=600,...u}=e,[c,d]=n.useState(null),[p,v]=n.useState(null),[h,w]=n.useState(null),[g,b]=n.useState(null),[y,S]=n.useState(null),[E,C]=n.useState(0),[R,T]=n.useState(0),[N,P]=n.useState(!1),[L,_]=n.useState(!1),j=i(r,e=>d(e)),A=function(e){let r=n.useContext(m);return e||r||"ltr"}(l);return(0,a.jsx)(x,{scope:t,type:o,dir:A,scrollHideDelay:s,scrollArea:c,viewport:p,onViewportChange:v,content:h,onContentChange:w,scrollbarX:g,onScrollbarXChange:b,scrollbarXEnabled:N,onScrollbarXEnabledChange:P,scrollbarY:y,onScrollbarYChange:S,scrollbarYEnabled:L,onScrollbarYEnabledChange:_,onCornerWidthChange:C,onCornerHeightChange:T,children:(0,a.jsx)(f.div,{dir:A,...u,ref:j,style:{position:"relative","--radix-scroll-area-corner-width":E+"px","--radix-scroll-area-corner-height":R+"px",...e.style}})})});R.displayName=y;var T="ScrollAreaViewport",N=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,children:o,nonce:l,...s}=e,u=C(T,t),c=i(r,n.useRef(null),u.onViewportChange);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,a.jsx)(f.div,{"data-radix-scroll-area-viewport":"",...s,ref:c,style:{overflowX:u.scrollbarXEnabled?"scroll":"hidden",overflowY:u.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,a.jsx)("div",{ref:u.onContentChange,style:{minWidth:"100%",display:"table"},children:o})})]})});N.displayName=T;var P="ScrollAreaScrollbar",L=n.forwardRef((e,r)=>{let{forceMount:t,...o}=e,l=C(P,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:s}=l,u="horizontal"===e.orientation;return n.useEffect(()=>(u?i(!0):s(!0),()=>{u?i(!1):s(!1)}),[u,i,s]),"hover"===l.type?(0,a.jsx)(_,{...o,ref:r,forceMount:t}):"scroll"===l.type?(0,a.jsx)(j,{...o,ref:r,forceMount:t}):"auto"===l.type?(0,a.jsx)(A,{...o,ref:r,forceMount:t}):"always"===l.type?(0,a.jsx)(O,{...o,ref:r}):null});L.displayName=P;var _=n.forwardRef((e,r)=>{let{forceMount:t,...o}=e,l=C(P,e.__scopeScrollArea),[i,s]=n.useState(!1);return n.useEffect(()=>{let e=l.scrollArea,r=0;if(e){let t=()=>{window.clearTimeout(r),s(!0)},n=()=>{r=window.setTimeout(()=>s(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",t),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(r),e.removeEventListener("pointerenter",t),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,a.jsx)(v,{present:t||i,children:(0,a.jsx)(A,{"data-state":i?"visible":"hidden",...o,ref:r})})}),j=n.forwardRef((e,r)=>{var t,o;let{forceMount:l,...i}=e,s=C(P,e.__scopeScrollArea),u="horizontal"===e.orientation,c=J(()=>f("SCROLL_END"),100),[d,f]=(t="hidden",o={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,r)=>{let t=o[e][r];return null!=t?t:e},t));return n.useEffect(()=>{if("idle"===d){let e=window.setTimeout(()=>f("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[d,s.scrollHideDelay,f]),n.useEffect(()=>{let e=s.viewport,r=u?"scrollLeft":"scrollTop";if(e){let t=e[r],n=()=>{let n=e[r];t!==n&&(f("SCROLL"),c()),t=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[s.viewport,u,f,c]),(0,a.jsx)(v,{present:l||"hidden"!==d,children:(0,a.jsx)(O,{"data-state":"hidden"===d?"hidden":"visible",...i,ref:r,onPointerEnter:b(e.onPointerEnter,()=>f("POINTER_ENTER")),onPointerLeave:b(e.onPointerLeave,()=>f("POINTER_LEAVE"))})})}),A=n.forwardRef((e,r)=>{let t=C(P,e.__scopeScrollArea),{forceMount:o,...l}=e,[i,s]=n.useState(!1),u="horizontal"===e.orientation,c=J(()=>{if(t.viewport){let e=t.viewport.offsetWidth<t.viewport.scrollWidth,r=t.viewport.offsetHeight<t.viewport.scrollHeight;s(u?e:r)}},10);return K(t.viewport,c),K(t.content,c),(0,a.jsx)(v,{present:o||i,children:(0,a.jsx)(O,{"data-state":i?"visible":"hidden",...l,ref:r})})}),O=n.forwardRef((e,r)=>{let{orientation:t="vertical",...o}=e,l=C(P,e.__scopeScrollArea),i=n.useRef(null),s=n.useRef(0),[u,c]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=B(u.viewport,u.content),f={...o,sizes:u,onSizesChange:c,hasThumb:!!(d>0&&d<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>s.current=0,onThumbPointerDown:e=>s.current=e};function p(e,r){return function(e,r,t){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=$(t),l=r||o/2,i=t.scrollbar.paddingStart+l,a=t.scrollbar.size-t.scrollbar.paddingEnd-(o-l),s=t.content-t.viewport;return Z([i,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,s.current,u,r)}return"horizontal"===t?(0,a.jsx)(D,{...f,ref:r,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=q(l.viewport.scrollLeft,u,l.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=p(e,l.dir))}}):"vertical"===t?(0,a.jsx)(W,{...f,ref:r,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=q(l.viewport.scrollTop,u);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=p(e))}}):null}),D=n.forwardRef((e,r)=>{let{sizes:t,onSizesChange:o,...l}=e,s=C(P,e.__scopeScrollArea),[u,c]=n.useState(),d=n.useRef(null),f=i(r,d,s.onScrollbarXChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,a.jsx)(M,{"data-orientation":"horizontal",...l,ref:f,sizes:t,style:{bottom:0,left:"rtl"===s.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===s.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":$(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.x),onDragScroll:r=>e.onDragScroll(r.x),onWheelScroll:(r,t)=>{if(s.viewport){let n=s.viewport.scrollLeft+r.deltaX;e.onWheelScroll(n),n>0&&n<t&&r.preventDefault()}},onResize:()=>{d.current&&s.viewport&&u&&o({content:s.viewport.scrollWidth,viewport:s.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:F(u.paddingLeft),paddingEnd:F(u.paddingRight)}})}})}),W=n.forwardRef((e,r)=>{let{sizes:t,onSizesChange:o,...l}=e,s=C(P,e.__scopeScrollArea),[u,c]=n.useState(),d=n.useRef(null),f=i(r,d,s.onScrollbarYChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,a.jsx)(M,{"data-orientation":"vertical",...l,ref:f,sizes:t,style:{top:0,right:"ltr"===s.dir?0:void 0,left:"rtl"===s.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":$(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.y),onDragScroll:r=>e.onDragScroll(r.y),onWheelScroll:(r,t)=>{if(s.viewport){let n=s.viewport.scrollTop+r.deltaY;e.onWheelScroll(n),n>0&&n<t&&r.preventDefault()}},onResize:()=>{d.current&&s.viewport&&u&&o({content:s.viewport.scrollHeight,viewport:s.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:F(u.paddingTop),paddingEnd:F(u.paddingBottom)}})}})}),[I,U]=S(P),M=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,sizes:o,hasThumb:l,onThumbChange:s,onThumbPointerUp:u,onThumbPointerDown:c,onThumbPositionChange:d,onDragScroll:p,onWheelScroll:v,onResize:h,...m}=e,g=C(P,t),[y,S]=n.useState(null),E=i(r,e=>S(e)),x=n.useRef(null),R=n.useRef(""),T=g.viewport,N=o.content-o.viewport,L=w(v),_=w(d),j=J(h,10);function A(e){x.current&&p({x:e.clientX-x.current.left,y:e.clientY-x.current.top})}return n.useEffect(()=>{let e=e=>{let r=e.target;(null==y?void 0:y.contains(r))&&L(e,N)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[T,y,N,L]),n.useEffect(_,[o,_]),K(y,j),K(g.content,j),(0,a.jsx)(I,{scope:t,scrollbar:y,hasThumb:l,onThumbChange:w(s),onThumbPointerUp:w(u),onThumbPositionChange:_,onThumbPointerDown:w(c),children:(0,a.jsx)(f.div,{...m,ref:E,style:{position:"absolute",...m.style},onPointerDown:b(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),x.current=y.getBoundingClientRect(),R.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",g.viewport&&(g.viewport.style.scrollBehavior="auto"),A(e))}),onPointerMove:b(e.onPointerMove,A),onPointerUp:b(e.onPointerUp,e=>{let r=e.target;r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=R.current,g.viewport&&(g.viewport.style.scrollBehavior=""),x.current=null})})})}),z="ScrollAreaThumb",H=n.forwardRef((e,r)=>{let{forceMount:t,...n}=e,o=U(z,e.__scopeScrollArea);return(0,a.jsx)(v,{present:t||o.hasThumb,children:(0,a.jsx)(k,{ref:r,...n})})}),k=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,style:o,...l}=e,s=C(z,t),u=U(z,t),{onThumbPositionChange:c}=u,d=i(r,e=>u.onThumbChange(e)),p=n.useRef(),v=J(()=>{p.current&&(p.current(),p.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let r=()=>{if(v(),!p.current){let r=G(e,c);p.current=r,c()}};return c(),e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[s.viewport,v,c]),(0,a.jsx)(f.div,{"data-state":u.hasThumb?"visible":"hidden",...l,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...o},onPointerDownCapture:b(e.onPointerDownCapture,e=>{let r=e.target.getBoundingClientRect(),t=e.clientX-r.left,n=e.clientY-r.top;u.onThumbPointerDown({x:t,y:n})}),onPointerUp:b(e.onPointerUp,u.onThumbPointerUp)})});H.displayName=z;var X="ScrollAreaCorner",Y=n.forwardRef((e,r)=>{let t=C(X,e.__scopeScrollArea),n=!!(t.scrollbarX&&t.scrollbarY);return"scroll"!==t.type&&n?(0,a.jsx)(V,{...e,ref:r}):null});Y.displayName=X;var V=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,...o}=e,l=C(X,t),[i,s]=n.useState(0),[u,c]=n.useState(0),d=!!(i&&u);return K(l.scrollbarX,()=>{var e;let r=(null===(e=l.scrollbarX)||void 0===e?void 0:e.offsetHeight)||0;l.onCornerHeightChange(r),c(r)}),K(l.scrollbarY,()=>{var e;let r=(null===(e=l.scrollbarY)||void 0===e?void 0:e.offsetWidth)||0;l.onCornerWidthChange(r),s(r)}),d?(0,a.jsx)(f.div,{...o,ref:r,style:{width:i,height:u,position:"absolute",right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:0,...e.style}}):null});function F(e){return e?parseInt(e,10):0}function B(e,r){let t=e/r;return isNaN(t)?0:t}function $(e){let r=B(e.viewport,e.content),t=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-t)*r,18)}function q(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=$(r),o=r.scrollbar.paddingStart+r.scrollbar.paddingEnd,l=r.scrollbar.size-o,i=r.content-r.viewport,a=(0,g.u)(e,"ltr"===t?[0,i]:[-1*i,0]);return Z([0,i],[0,l-n])(a)}function Z(e,r){return t=>{if(e[0]===e[1]||r[0]===r[1])return r[0];let n=(r[1]-r[0])/(e[1]-e[0]);return r[0]+n*(t-e[0])}}var G=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},t={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=t.left!==l.left,a=t.top!==l.top;(i||a)&&r(),t=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function J(e,r){let t=w(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(t,r)},[t,r])}function K(e,r){let t=w(r);p(()=>{let r=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(t)});return n.observe(e),()=>{window.cancelAnimationFrame(r),n.unobserve(e)}}},[e,t])}var Q=R,ee=N,er=Y}}]);