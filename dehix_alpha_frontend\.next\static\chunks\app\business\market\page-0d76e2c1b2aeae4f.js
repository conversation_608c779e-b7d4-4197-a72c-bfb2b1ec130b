(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1069],{26043:function(e,s,l){Promise.resolve().then(l.bind(l,38667))},20897:function(e,s,l){"use strict";l.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,l(33480).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},13231:function(e,s,l){"use strict";l.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,l(33480).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},71935:function(e,s,l){"use strict";l.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,l(33480).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},98960:function(e,s,l){"use strict";l.d(s,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,l(33480).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},38667:function(e,s,l){"use strict";l.r(s),l.d(s,{default:function(){return E}});var a=l(57437),t=l(2265),r=l(11444),i=l(3274),c=l(64797),n=l(82230),d=l(15922),o=l(62688),m=()=>(0,a.jsxs)("div",{className:"flex flex-col sm:py-0 mb-8",children:[(0,a.jsx)(o.Z,{menuItemsTop:n.yn,menuItemsBottom:n.$C,activeMenu:"Dashboard",breadcrumbItems:[{label:"Business",link:"/dashboard/business"},{label:"Business Marketplace",link:"#"}]}),(0,a.jsxs)("div",{className:"mb-8 ml-6 mt-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:" Business Marketplace "}),(0,a.jsx)("p",{className:"text-gray-400 mt-2",children:"Discover a curated selection of business opportunities designed to connect freelancers with potential clients and projects."})]})]}),x=l(89733),h=l(48185),u=l(77209),f=l(70402),b=e=>{let{heading:s,setLimits:l}=e,[r,i]=t.useState(""),[c,n]=t.useState(""),[d,o]=t.useState(""),m=(e,s)=>{let a=e?Number(e):0,t=s?Number(s):0;if(a<0||t<0){o("Experience cannot be negative."),setTimeout(()=>o(""),3e3);return}if(t>30){o("Maximum experience cannot exceed 30 years."),setTimeout(()=>o(""),3e3);return}if(a>t&&""!==s){o("Please enter the maximum experience first"),setTimeout(()=>o(""),3e3);return}o(""),i(e),n(s),l("".concat(e||0,"-").concat(s||0))};return(0,a.jsxs)(h.Zb,{className:"w-full",children:[(0,a.jsx)(h.Ol,{className:"pb-3",children:(0,a.jsx)(h.ll,{className:"text-lg",children:s})}),(0,a.jsxs)(h.aY,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)(f.Label,{htmlFor:"lowerLimit",className:"text-sm",children:"Minimum"}),(0,a.jsx)(u.I,{id:"lowerLimit",type:"number",value:r,onChange:e=>m(e.target.value,c),className:"mt-1 w-full",placeholder:"0"})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)(f.Label,{htmlFor:"higherLimit",className:"text-sm",children:"Maximum"}),(0,a.jsx)(u.I,{id:"higherLimit",type:"number",value:c,onChange:e=>m(r,e.target.value),className:"mt-1 w-full",placeholder:"30"})]})]}),d&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-2",children:d})]})]})},p=l(32970),j=e=>{let{filters:s,domains:l,skills:t,handleFilterChange:r,handleApply:i,handleReset:c}=e,n=()=>{i()};return(0,a.jsx)("div",{className:"hidden mb-10 lg:block lg:sticky lg:top-16 lg:w-[400px] lg:self-start lg:h-[calc(100vh-4rem)] lg:overflow-hidden lg:transition-all lg:duration-300 lg:scrollbar  no-scrollbar lg:scrollbar-thumb-gray-500 lg:scrollbar-track-gray-200 hover:lg:overflow-y-auto",children:(0,a.jsxs)("div",{className:"h-full px-4 flex flex-col space-y-4 ",children:[(0,a.jsx)(x.z,{onClick:()=>{n()},className:"w-full",children:"Apply"}),(0,a.jsx)(x.z,{onClick:c,variant:"outline",style:{marginTop:"1rem"},className:"w-full dark:text-white ",children:"Reset"}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)(b,{heading:"Filter by Experience",setLimits:e=>r("experience",e)})}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)(p.Z,{heading:"Filter by Domains",checkboxLabels:l,selectedValues:s.domain,setSelectedValues:e=>r("domain",e)})}),(0,a.jsx)("div",{className:"mb-12",children:(0,a.jsx)(p.Z,{heading:"Filter by Skills",checkboxLabels:t,selectedValues:s.skills,setSelectedValues:e=>r("skills",e)})})]})})},N=l(44458),g=l(87138),v=l(79055),y=l(55936);let w=["left"];var k=e=>{let{name:s,skills:l,domains:r,experience:i,profile:c,userName:n,monthlyPay:d,Github:o,LinkedIn:m}=e,[u,f]=t.useState(!1);return(0,a.jsx)("div",{className:" sm:mx-10 mb-3 max-w-3xl",children:(0,a.jsx)(h.Zb,{className:"flex justify-between mt-5  shadow-lg shadow-gray-500/20  ",children:(0,a.jsxs)("div",{className:"flex flex-col justify-between p-4",children:[(0,a.jsx)(h.Ol,{children:(0,a.jsxs)("div",{className:"flex flex-col item-center gap-4",children:[(0,a.jsx)(N.qE,{className:"rounded-full w-20 h-20 overflow-hidden border-2 border-gray-400 ",children:(0,a.jsx)(N.F$,{className:"w-full h-full object-cover",src:c,alt:"Profile Picture"})}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)(h.ll,{className:"text-xl font-bold",children:s})})]})}),(0,a.jsxs)(h.aY,{children:[(0,a.jsxs)("p",{className:"text-sm",children:[(0,a.jsx)("span",{className:"font-medium text-gray-400",children:"Experience:"}),(0,a.jsxs)("span",{className:"font-bold",children:[" ",i," years"]})]}),l&&l.length>0&&(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("p",{className:"font-medium",children:"Skills:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:null==l?void 0:l.map((e,s)=>(0,a.jsx)(v.C,{className:"bg-foreground text-background border border-white rounded-xl font-bold uppercase",children:e.name},s))})]}),r&&r.length>0&&(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("p",{className:"font-medium",children:"Domains:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:null==r?void 0:r.map((e,s)=>(0,a.jsx)(v.C,{className:"bg-foreground text-background border border-white rounded-xl font-bold uppercase",children:e.name},s))})]}),(0,a.jsx)("div",{className:"py-4 mt-8",children:w.map(e=>(0,a.jsxs)(y.yo,{children:[(0,a.jsx)(y.aM,{asChild:!0,children:(0,a.jsx)(x.z,{className:"w-full sm:w-[350px] lg:w-[680px]",children:"View"})}),(0,a.jsxs)(y.ue,{side:e,className:"overflow-y-auto max-h-[100vh]",children:[(0,a.jsx)(y.Tu,{children:(0,a.jsx)(y.bC,{className:"text-center text-lg font-bold py-4",children:"View Talent Details"})}),(0,a.jsxs)("div",{className:"flex flex-col gap-4 items-center justify-center mt-2",children:[(0,a.jsx)(N.qE,{className:"rounded-full w-20 h-20 overflow-hidden border-2 border-gray-400 ",children:(0,a.jsx)(N.F$,{className:"w-full h-full object-cover",src:c,alt:"Profile Picture"})}),(0,a.jsx)("div",{className:"text-lg font-bold items-center justify-center mt-2",children:s}),(0,a.jsx)(h.Zb,{className:"w-full  shadow-lg shadow-gray-500/20 mt-4",children:(0,a.jsx)("table",{className:"min-w-full table-auto border-collapse ",children:(0,a.jsxs)("tbody",{children:[(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"border-b px-4 py-2 font-medium",children:"Username"}),(0,a.jsx)("td",{className:"border-b px-4 py-2",children:n||"N/A"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"border-b px-4 py-2 font-medium",children:"Skill"}),(0,a.jsx)("td",{className:"border-b px-4 py-2",children:(null==l?void 0:l.length)!==0&&l?(0,a.jsxs)(a.Fragment,{children:[l.slice(0,2).map((e,s)=>(0,a.jsxs)(v.C,{className:"bg-transparent text-foreground",children:[e.name,s<l.length-1&&","]},s)),l.length>2&&!u&&(0,a.jsxs)(v.C,{className:"bg-transparent border-none text-foreground",onClick:()=>f(!0),children:["+",l.length-2]},"extra"),u&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:l.slice(2).map((e,s)=>(0,a.jsxs)(v.C,{className:"bg-transparent border-none text-foreground",children:[e.name,s<l.slice(2).length-1&&","]},s+2))})]}):"N/A"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"border-b px-4 py-2 font-medium",children:"Experience"}),(0,a.jsx)("td",{className:"border-b px-4 py-2",children:i?"".concat(i," Years"):"N/A"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"border-b px-4 py-2 font-medium",children:"MonthlyPay"}),(0,a.jsx)("td",{className:"border-b px-4 py-2",children:d&&d.trim()?"".concat(d,"$"):"N/A"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"border-b px-4 py-2 font-medium",children:"Github"}),(0,a.jsx)("td",{className:"border-b px-4 py-2",children:o&&o.trim()?(0,a.jsx)("a",{href:o,target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 hover:underline overflow-hidden whitespace-nowrap text-ellipsis max- sm:max-w-md lg:max-w-lg",title:o,children:o}):"N/A"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"border-b px-4 py-2 font-medium",children:"LinkedIn"}),(0,a.jsx)("td",{className:"border-b px-4 py-2",children:m&&m.trim()?(0,a.jsx)("a",{href:m,target:"_blank",rel:"noopener noreferrer",className:"hover:underline overflow-hidden whitespace-nowrap text-ellipsis max-w-[120px] sm:max-w-[170px] block",title:m,children:m}):"N/A"})]})]})})}),(0,a.jsx)("div",{className:"w-full text-sm mt-6",children:(0,a.jsx)("div",{className:"w-full text-center",children:(0,a.jsx)(g.default,{href:"/business/freelancerProfile/".concat(n),passHref:!0,children:(0,a.jsx)(x.z,{className:"w-full text-sm text-black rounded-md",children:"Expand"})})})})]})]})]},e))})]})]})})})},C=e=>{let{freelancers:s}=e;return console.log(s),(0,a.jsx)("div",{className:"mt-4 p-4  lg:ml-10 space-y-4 w-full",children:0===s.length?(0,a.jsx)("p",{className:"text-center text-xl flex justify-center items-center h-[55vh] font-semibold",children:"No talent found"}):s.map((e,s)=>(0,a.jsx)(k,{name:e.firstName+" "+e.lastName,skills:e.skills,domains:e.domains,experience:e.workExperience,profile:e.profilePic,userName:e.userName,monthlyPay:e.monthlyPay,Github:e.github,LinkedIn:e.linkedin},s))})},Z=l(74697),S=l(87446),L=e=>{let{heading:s,setLimits:l}=e,[r,i]=t.useState(0),[c,n]=t.useState(10);return(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)(h.Ol,{className:"pb-3",children:(0,a.jsx)(h.ll,{className:"text-lg",children:s})}),(0,a.jsx)(h.aY,{children:(0,a.jsxs)("div",{className:"flex space-x-4 p-2",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)(f.Label,{htmlFor:"lowerLimit",className:"text-sm",children:"Minimum"}),(0,a.jsx)(u.I,{id:"lowerLimit",type:"number",value:r,onChange:e=>{let s=Number(e.target.value);i(s),l("".concat(s,"-").concat(c))},className:"w-20 mt-1"})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)(f.Label,{htmlFor:"higherLimit",className:"text-sm",children:"Maximum"}),(0,a.jsx)(u.I,{id:"higherLimit",type:"number",value:c,onChange:e=>{let s=Number(e.target.value);n(s),l("".concat(r,"-").concat(s))},className:"w-20 mt-1"})]})]})})]})},F=e=>{let{showFilters:s,filters:l,domains:t,skills:r,handleFilterChange:i,handleApply:c,handleModalToggle:n}=e;return(0,a.jsxs)(a.Fragment,{children:[s&&(0,a.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4 overflow-hidden",children:(0,a.jsxs)("div",{className:"bg-black relative rounded-lg w-full max-w-screen-lg mx-auto h-[80vh] max-h-full flex flex-col",children:[(0,a.jsx)(Z.Z,{onClick:n,className:"absolute cursor-pointer top-1 right-6"}),(0,a.jsxs)("div",{className:"overflow-y-auto p-4 flex-grow",children:[(0,a.jsx)("div",{className:"border-b border-gray-300 pb-4 ",children:(0,a.jsx)(L,{heading:"Filter by Experience",setLimits:e=>i("experience",e)})}),(0,a.jsx)("div",{className:"border-b border-gray-300 pb-4",children:(0,a.jsx)(S.Z,{label:"Domains",heading:"Filter by domains",checkboxLabels:t,selectedValues:l.domain,setSelectedValues:e=>i("domain",e)})}),(0,a.jsx)("div",{className:"border-b border-gray-300 pb-4",children:(0,a.jsx)(S.Z,{label:"Skills",heading:"Filter by skills",checkboxLabels:r,selectedValues:l.skills,setSelectedValues:e=>i("skills",e)})})]}),(0,a.jsx)("div",{className:"p-4 border-t border-gray-300",children:(0,a.jsx)(x.z,{onClick:c,className:"w-full",children:"Apply"})})]})}),(0,a.jsx)("div",{className:"fixed bottom-0 left-0 right-0 lg:hidden p-4 flex justify-center z-50",children:(0,a.jsx)("button",{className:"w-full max-w-xs p-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors duration-300 ease-in-out",onClick:n,children:s?"Hide Filters":"Show Filters"})})]})},A=l(78068),E=()=>{let e=(0,r.v9)(e=>e.user),[s,l]=(0,t.useState)(!1),[o,x]=(0,t.useState)([]),[h,u]=(0,t.useState)([]),[f,b]=(0,t.useState)([]),[p,N]=(0,t.useState)(!1),[g,v]=(0,t.useState)({location:[],jobType:[],experience:[],domain:[],skills:[]}),y=(e,s)=>{let l=s;l=(Array.isArray(s)?s:[s]).flatMap(e=>{if(e.includes("-")){let[s,l]=e.split("-").map(Number);return Array.from({length:l-s+1},(e,l)=>(s+l).toString())}return"7+"===e?["7","8","9","10"]:[e]}),v(s=>({...s,[e]:l}))},w=e=>{let s=[];if(Array.isArray(e.experience)&&e.experience.length>0){let l=e.experience.map(Number).sort((e,s)=>e-s),a=l[0],t=l[l.length-1];void 0!==a&&s.push("workExperienceFrom=".concat(a)),void 0!==t&&s.push("workExperienceTo=".concat(t))}return Object.entries(e).forEach(e=>{let[l,a]=e;if("experience"!==l){if(Array.isArray(a)&&a.length>0){let e=a.filter(e=>null!=e&&""!==e);e.length>0&&s.push("".concat(l,"=").concat(e.join(",")))}else"string"==typeof a&&s.push("".concat(l,"=").concat(a.split(",").map(e=>e.trim()).join(",")))}}),s.join("&")},k=(0,t.useCallback)(async e=>{try{N(!0);let s=w(e),l=await d.b.get("/freelancer?".concat(s));b(l.data.data)}catch(e){(0,A.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."}),console.error("API Error:",e)}finally{N(!1)}},[]);(0,t.useEffect)(()=>{(async function(){try{let e=(await d.b.get("/skills")).data.data.map(e=>e.label);x(e);let s=(await d.b.get("/domain")).data.data.map(e=>e.label);u(s)}catch(e){(0,A.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."}),console.error("Error fetching data:",e)}})()},[]),(0,t.useEffect)(()=>{k(g)},[e.uid,g,k]);let Z=()=>{k(g)};return(0,a.jsxs)("section",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,a.jsx)(c.Z,{menuItemsTop:n.yn,menuItemsBottom:n.$C,active:"Market"}),(0,a.jsxs)("div",{className:"flex flex-col sm:gap-4  sm:pl-14 mb-8",children:[(0,a.jsx)(m,{}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:space-x-5 md:-space-x-3 ml:20 sm:-space-x-4 md:ml-6 lg:ml-6",children:[(0,a.jsx)(j,{filters:g,domains:h,skills:o,handleFilterChange:y,handleApply:Z,handleReset:()=>{v({location:[],jobType:[],domain:[],skills:[],experience:[]})}}),p?(0,a.jsx)("div",{className:"mt-4 lg:mt-0 lg:ml-10 space-y-4 w-full flex justify-center items-center h-[60vh]",children:(0,a.jsx)(i.Z,{size:40,className:" text-white animate-spin "})}):(0,a.jsx)(C,{freelancers:f})]})]}),(0,a.jsx)(F,{showFilters:s,filters:g,domains:h,skills:o,handleFilterChange:y,handleApply:Z,handleModalToggle:()=>{l(!s)}})]})}},87446:function(e,s,l){"use strict";var a=l(57437),t=l(2265),r=l(14392),i=l(42421),c=l(44541),n=l(70402),d=l(77209),o=l(89733);s.Z=e=>{let{label:s,heading:l,checkboxLabels:m,selectedValues:x,setSelectedValues:h}=e,[u,f]=t.useState(!1),[b,p]=t.useState(""),j=e=>{x.includes(e)?h(x.filter(s=>s!==e)):h([...x,e])},N=m.filter(e=>e.toLowerCase().includes(b.toLowerCase())),g=N.slice(0,3),v=N.slice(3);return(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"mt-2 text-white",children:l}),(0,a.jsxs)("div",{className:"items-center p-2",children:[(0,a.jsx)(d.I,{placeholder:"Search ".concat(s),value:b,onChange:e=>p(e.target.value),className:"mb-2 bg-secondary border-black"}),g.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)(c.X,{id:e,checked:x.includes(e),onCheckedChange:()=>j(e)}),(0,a.jsx)(n.Label,{htmlFor:e,className:"text-sm",children:e})]},e)),u&&v.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)(c.X,{id:e,checked:x.includes(e),onCheckedChange:()=>j(e)}),(0,a.jsx)(n.Label,{htmlFor:e,className:"text-sm",children:e})]},e)),N.length>3&&(0,a.jsx)("div",{className:"flex items-center mb-1",children:(0,a.jsxs)(o.z,{size:"sm",variant:"ghost",className:"flex items-center text-sm cursor-pointer ml-auto",onClick:()=>f(!u),children:[u?"Less":"More",u?(0,a.jsx)(r.Z,{className:"ml-1 h-4 w-4"}):(0,a.jsx)(i.Z,{className:"ml-1 h-4 w-4"})]})}),0===N.length&&(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"No skills found."})]})]})}},32970:function(e,s,l){"use strict";var a=l(57437),t=l(2265),r=l(14392),i=l(42421),c=l(35265),n=l(48185),d=l(89733),o=l(77209),m=l(70402),x=l(44541);s.Z=e=>{let{label:s="Skills",heading:l,checkboxLabels:h,selectedValues:u,setSelectedValues:f,openItem:b,setOpenItem:p,useAccordion:j=!1}=e,[N,g]=(0,t.useState)(""),[v,y]=(0,t.useState)(!1),w=e=>{u.includes(e)?f(u.filter(s=>s!==e)):f([...u,e])},k=h.filter(e=>e.toLowerCase().includes(N.toLowerCase())),C=k.slice(0,3),Z=k.slice(3);return j?(0,a.jsx)(n.Zb,{className:"w-full",children:(0,a.jsx)(c.UQ,{type:"single",collapsible:!0,value:b===l?l:"",onValueChange:e=>null==p?void 0:p(e===l?l:null),children:(0,a.jsxs)(c.Qd,{value:l,children:[(0,a.jsx)(c.o4,{className:"text-base px-4 py-2",children:l}),(0,a.jsx)(c.vF,{children:(0,a.jsxs)("div",{className:"px-4 mt-2 pb-4 space-y-3",children:[(0,a.jsx)(o.I,{type:"text",placeholder:"Search ".concat(l.toLowerCase(),"..."),value:N,onChange:e=>g(e.target.value)}),(0,a.jsxs)("div",{className:"max-h-52 overflow-y-auto no-scrollbar space-y-2",children:[k.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.X,{id:e,checked:u.includes(e),onCheckedChange:()=>w(e)}),(0,a.jsx)(m.Label,{htmlFor:e,className:"text-sm",children:e})]},e)),0===k.length&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No options found."})]})]})})]})})}):(0,a.jsxs)(n.Zb,{className:"w-full",children:[(0,a.jsx)(n.Ol,{children:(0,a.jsx)(n.ll,{className:"text-lg",children:l})}),(0,a.jsxs)(n.aY,{children:[(0,a.jsx)(o.I,{type:"text",placeholder:"Search ".concat(s),value:N,onChange:e=>g(e.target.value),className:"w-full mb-2"}),(0,a.jsxs)(a.Fragment,{children:[C.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)(x.X,{id:e,checked:u.includes(e),onCheckedChange:()=>w(e)}),(0,a.jsx)(m.Label,{htmlFor:e,className:"text-sm",children:e})]},e)),v&&Z.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)(x.X,{id:e,checked:u.includes(e),onCheckedChange:()=>w(e)}),(0,a.jsx)(m.Label,{htmlFor:e,className:"text-sm",children:e})]},e))]})]}),(0,a.jsxs)(n.eW,{children:[k.length>3&&(0,a.jsxs)(d.z,{size:"sm",variant:"ghost",className:"flex items-center text-sm cursor-pointer ml-auto",onClick:()=>y(!v),children:[v?"Less":"More",v?(0,a.jsx)(r.Z,{className:"ml-1 h-4 w-4"}):(0,a.jsx)(i.Z,{className:"ml-1 h-4 w-4"})]}),0===k.length&&(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Skills"===s?"No skills found.":"No domain found."})]})]})}},44541:function(e,s,l){"use strict";l.d(s,{X:function(){return n}});var a=l(57437),t=l(2265),r=l(93943),i=l(22468),c=l(49354);let n=t.forwardRef((e,s)=>{let{className:l,...t}=e;return(0,a.jsx)(r.fC,{ref:s,className:(0,c.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",l),...t,children:(0,a.jsx)(r.z$,{className:(0,c.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})})});n.displayName=r.fC.displayName},70402:function(e,s,l){"use strict";l.r(s),l.d(s,{Label:function(){return d}});var a=l(57437),t=l(2265),r=l(38364),i=l(12218),c=l(49354);let n=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=t.forwardRef((e,s)=>{let{className:l,...t}=e;return(0,a.jsx)(r.f,{ref:s,className:(0,c.cn)(n(),l),...t})});d.displayName=r.f.displayName},82230:function(e,s,l){"use strict";l.d(s,{$C:function(){return p},Ne:function(){return j},yn:function(){return b}});var a=l(57437),t=l(11005),r=l(98960),i=l(38133),c=l(20897),n=l(13231),d=l(71935),o=l(47390),m=l(73347),x=l(24258),h=l(5891),u=l(10883),f=l(66648);let b=[{href:"#",icon:(0,a.jsx)(f.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,a.jsx)(t.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:(0,a.jsx)(r.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:(0,a.jsx)(i.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:(0,a.jsx)(i.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:(0,a.jsx)(c.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:(0,a.jsx)(n.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:(0,a.jsx)(d.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:(0,a.jsx)(o.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,a.jsx)(m.Z,{className:"h-5 w-5"}),label:"Notes"}],p=[{href:"/business/settings/business-info",icon:(0,a.jsx)(x.Z,{className:"h-5 w-5"}),label:"Settings"}],j=[{href:"#",icon:(0,a.jsx)(f.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,a.jsx)(t.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:(0,a.jsx)(m.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:(0,a.jsx)(h.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:(0,a.jsx)(u.Z,{className:"h-5 w-5"}),label:"Trash"}]}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,571,2455,9726,2688,2971,7023,1744],function(){return e(e.s=26043)}),_N_E=e.O()}]);