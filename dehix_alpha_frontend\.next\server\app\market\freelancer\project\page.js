(()=>{var e={};e.id=4808,e.ids=[4808],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71396:e=>{"use strict";e.exports=require("undici")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},70773:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c}),t(51927),t(54302),t(12523);var s=t(23191),a=t(88716),i=t(37922),n=t.n(i),l=t(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let c=["",{children:["market",{children:["freelancer",{children:["project",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,51927)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\market\\freelancer\\project\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\market\\freelancer\\project\\page.tsx"],u="/market/freelancer/project/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/market/freelancer/project/page",pathname:"/market/freelancer/project",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3614:(e,r,t)=>{Promise.resolve().then(t.bind(t,44794)),Promise.resolve().then(t.bind(t,78062))},44794:(e,r,t)=>{"use strict";t.r(r),t.d(r,{Label:()=>c});var s=t(10326),a=t(17577),i=t(34478),n=t(28671),l=t(51223);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...r},t)=>s.jsx(i.f,{ref:t,className:(0,l.cn)(o(),e),...r}));c.displayName=i.f.displayName},78062:(e,r,t)=>{"use strict";t.r(r),t.d(r,{Separator:()=>l});var s=t(10326),a=t(17577),i=t(90220),n=t(51223);let l=a.forwardRef(({className:e,orientation:r="horizontal",decorative:t=!0,...a},l)=>s.jsx(i.f,{ref:l,decorative:t,orientation:r,className:(0,n.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",e),...a}));l.displayName=i.f.displayName},72301:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var s=t(71159),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=(e,r)=>{let t=(0,s.forwardRef)(({color:t="currentColor",size:n=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:c="",children:d,...u},p)=>(0,s.createElement)("svg",{ref:p,...a,width:n,height:n,stroke:t,strokeWidth:o?24*Number(l)/Number(n):l,className:["lucide",`lucide-${i(e)}`,c].join(" "),...u},[...r.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(d)?d:[d]]));return t.displayName=`${e}`,t}},51927:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>q});var s=t(19510),a=t(71159),i=t(72301);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,i.Z)("MapPinned",[["path",{d:"M18 8c0 4.5-6 9-6 9s-6-4.5-6-9a6 6 0 0 1 12 0",key:"yrbn30"}],["circle",{cx:"12",cy:"8",r:"2",key:"1822b1"}],["path",{d:"M8.835 14H5a1 1 0 0 0-.9.7l-2 6c-.1.1-.1.2-.1.3 0 .6.4 1 1 1h18c.6 0 1-.4 1-1 0-.1 0-.2-.1-.3l-2-6a1 1 0 0 0-.9-.7h-3.835",key:"112zkj"}]]);var l=t(68570);let o=(0,l.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\ui\separator.tsx`),{__esModule:c,$$typeof:d}=o;o.default;let u=(0,l.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\ui\separator.tsx#Separator`),p=({project_name:e,project_id:r,location:t,description:a,email:i,company_name:l,start:o,end:c,skills_required:d,experience:p,role:m,project_type:x})=>(0,s.jsxs)("div",{className:"p-4  shadow-md",children:[s.jsx("div",{className:"max-w-4xl ml-20 mb-5",children:(0,s.jsxs)("h1",{className:"sm:text-5xl md:text-6xl",children:[" ",l]})}),(0,s.jsxs)("div",{className:"max-w-4xl mb-2 flex",children:[(0,s.jsxs)("div",{className:"max-w-4xl ml-20 mb-2 flex",children:[s.jsx(n,{color:"grey",size:20,className:"mr-3"}),s.jsx("p",{className:" text-gray-400",children:t})]}),s.jsx("div",{className:"ml-20",children:(0,s.jsxs)("p",{className:" text-gray-400",children:[s.jsx("strong",{children:"Id: "})," ",r]})})]}),s.jsx("div",{className:"mb-4 max-w-4xl",children:s.jsx(u,{className:"bg-white"})}),(0,s.jsxs)("div",{className:"max-w-3xl ml-20",children:[(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("div",{className:"mb-4",children:(0,s.jsxs)("p",{className:"font-bold",children:[s.jsx("strong",{children:"Project Name:"})," ",e]})}),(0,s.jsxs)("p",{className:"text-sm text-gray-400",children:[s.jsx("strong",{children:"Description:"})," ",a]})]}),s.jsx("div",{className:"mb-2",children:(0,s.jsxs)("p",{className:"text-sm font-bold",children:[s.jsx("strong",{children:"Role:"})," ",m]})}),s.jsx("div",{className:"mb-2",children:(0,s.jsxs)("p",{className:"text-sm text-gray-400",children:[s.jsx("strong",{children:"Project Type:"})," ",x]})}),s.jsx("div",{className:"mb-2",children:(0,s.jsxs)("p",{className:"text-sm text-gray-400",children:[s.jsx("strong",{children:"Start Date:"})," ",o?.toLocaleDateString()]})}),s.jsx("div",{className:"mb-2",children:(0,s.jsxs)("p",{className:"text-sm text-gray-400",children:[s.jsx("strong",{children:"End Date:"})," ",c?.toLocaleDateString()]})}),s.jsx("div",{className:"mb-2",children:(0,s.jsxs)("p",{className:"text-sm font-bold",children:[s.jsx("strong",{children:"Skills Required:"})," ",d.join(", ")]})}),s.jsx("div",{className:"mb-2",children:(0,s.jsxs)("p",{className:"text-sm text-gray-400",children:[s.jsx("strong",{children:"Experience:"})," ",p]})}),s.jsx("div",{className:"mt-4 ",children:(0,s.jsxs)("p",{className:"text-sm text-gray-400",children:[s.jsx("strong",{children:"Email:"})," ",i]})})]})]});var m=t(27039),x=t(93804),f=t(3632);let h=a.forwardRef(({className:e,type:r,...t},a)=>s.jsx("input",{type:r,className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));h.displayName="Input";let g=(0,l.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\ui\label.tsx`),{__esModule:v,$$typeof:j}=g;g.default;let b=(0,l.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\components\ui\label.tsx#Label`),y=a.forwardRef(({className:e,...r},t)=>s.jsx("textarea",{className:(0,f.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...r}));y.displayName="Textarea";let N=()=>(0,s.jsxs)(x.Zb,{className:"w-[350px]",children:[(0,s.jsxs)(x.Ol,{children:[s.jsx(x.ll,{className:"pb-2",children:"User-bid"}),s.jsx("div",{className:"mt-2",children:s.jsx(u,{})})]}),s.jsx(x.aY,{children:s.jsx("form",{children:(0,s.jsxs)("div",{className:"grid w-full items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex flex-col space-y-1.5",children:[s.jsx(b,{htmlFor:"bitAmt",children:"your bid amount:"}),s.jsx(h,{id:"bitAmt",placeholder:"Enter your bid amount"})]}),(0,s.jsxs)("div",{className:"flex flex-col space-y-1.5",children:[s.jsx(b,{htmlFor:"coverLetter",children:"Cover letter:"}),s.jsx(y,{id:"coverLetter",placeholder:"Write your cover letter"})]})]})})}),s.jsx(x.eW,{className:"flex justify-center ",children:s.jsx(m.z,{className:"py-3 px-6 w-full",children:"Apply"})})]}),w=(0,i.Z)("CircleUserRound",[["path",{d:"M18 20a6 6 0 0 0-12 0",key:"1qehca"}],["circle",{cx:"12",cy:"10",r:"4",key:"1h16sb"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),_=({usernames:e})=>(0,s.jsxs)(x.Zb,{className:"w-[350px]",children:[(0,s.jsxs)(x.Ol,{children:[s.jsx(x.ll,{className:"pb-2",children:"Other-bids"}),s.jsx("div",{className:"mt-2",children:s.jsx(u,{})})]}),s.jsx(x.aY,{children:s.jsx("div",{children:e.map((e,r)=>s.jsx("div",{className:"grid w-full items-center gap-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[s.jsx(w,{className:"w-6 h-5"}),(0,s.jsxs)(b,{children:[e.username," = ",e.bitAmount]})]})},r))})})]}),k=JSON.parse('{"yT":{"project_name":"AI Development Project","project_id":"#12345","location":"Delhi, India","description":"We\'re looking for an experienced web developer who\'s really good at making interactive forms. The perfect candidate should know a lot about web development and have a bunch of cool forms they\'ve made before. Your main job will be making forms that people can use easily and that look nice.","email":"<EMAIL>","company_name":"Tech Innovators Inc.","start":"2023-01-01T00:00:00.000Z","end":"2023-12-31T00:00:00.000Z","skills_required":["JavaScript","React","Python","Machine Learning"],"experience":"2+ years of experience in AI development.","role":"Lead Developer","project_type":"Full-time"},"qf":[{"username":"Alex004","bitAmount":100},{"username":"User2","bitAmount":150},{"username":"alen789","bitAmount":200}]}'),q=()=>{let e=k.yT,r=k.qf;return(0,s.jsxs)("div",{className:"container mx-auto p-4 flex flex-col md:flex-row",children:[s.jsx("div",{className:"flex-1 mr-4 mb-4 md:mb-0",children:s.jsx(p,{project_name:e.project_name,project_id:e.project_id,location:e.location,description:e.description,email:e.email,company_name:e.company_name,start:new Date(e.start),end:new Date(e.end),skills_required:e.skills_required,experience:e.experience,role:e.role,project_type:e.project_type})}),(0,s.jsxs)("div",{className:"w-full md:w-[400px] px-5 md:p-2 lg:p-6",children:[s.jsx(N,{}),s.jsx("div",{className:"mt-10 ml-5",children:s.jsx("a",{href:"link",className:"text-white hover:text-blue-500 hover:bg-transparent",children:"Request for more connect"})}),s.jsx("div",{className:"mt-10",children:s.jsx(_,{usernames:r})})]})]})}},27039:(e,r,t)=>{"use strict";t.d(r,{z:()=>c});var s=t(19510),a=t(71159),i=t(22813),n=t(60791),l=t(3632);let o=(0,n.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...n},c)=>{let d=a?i.g7:"button";return s.jsx(d,{className:(0,l.cn)(o({variant:r,size:t,className:e})),ref:c,...n})});c.displayName="Button"},93804:(e,r,t)=>{"use strict";t.d(r,{Ol:()=>l,SZ:()=>c,Zb:()=>n,aY:()=>d,eW:()=>u,ll:()=>o});var s=t(19510),a=t(71159),i=t(3632);let n=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));n.displayName="Card";let l=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...r},t)=>s.jsx("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle";let c=a.forwardRef(({className:e,...r},t)=>s.jsx("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));d.displayName="CardContent";let u=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},3632:(e,r,t)=>{"use strict";t.d(r,{cn:()=>p});var s=t(55761),a=t(62386),i=t(19095);t(13237);let n=t(29712).Z.create({baseURL:"http://127.0.0.1:8080/"});console.log("Base URL:","http://127.0.0.1:8080/"),n.interceptors.request.use(e=>(console.log("Request config:",e),e),e=>(console.error("Request error:",e),Promise.reject(e))),n.interceptors.response.use(e=>(console.log("Response:",e.data),e),e=>(console.error("Response error:",e),Promise.reject(e)));var l=t(29362),o=t(85500),c=t(93820),d=t(38192);let u=(0,l.ZF)({apiKey:"AIzaSyBPTH9xikAUkgGof048klY6WGiZSmRoXXA",authDomain:"dehix-6c349.firebaseapp.com",databaseURL:"https://dehix-6c349-default-rtdb.firebaseio.com",projectId:"dehix-6c349",storageBucket:"dehix-6c349.appspot.com",messagingSenderId:"521082542540",appId:"1:521082542540:web:543857e713038c2927a569"});function p(...e){return(0,a.m6)((0,s.W)(e))}(0,c.ad)(u),(0,i.v0)(u).useDeviceLanguage(),new i.hJ,(0,o.N8)(u),(0,d.cF)(u)},34478:(e,r,t)=>{"use strict";t.d(r,{f:()=>l});var s=t(17577),a=t(77335),i=t(10326),n=s.forwardRef((e,r)=>(0,i.jsx)(a.WV.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var l=n},90220:(e,r,t)=>{"use strict";t.d(r,{f:()=>c});var s=t(17577),a=t(77335),i=t(10326),n="horizontal",l=["horizontal","vertical"],o=s.forwardRef((e,r)=>{let{decorative:t,orientation:s=n,...o}=e,c=l.includes(s)?s:n;return(0,i.jsx)(a.WV.div,{"data-orientation":c,...t?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:r})});o.displayName="Separator";var c=o},22813:(e,r,t)=>{"use strict";t.d(r,{g7:()=>n});var s=t(71159);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var i=t(19510),n=s.forwardRef((e,r)=>{let{children:t,...a}=e,n=s.Children.toArray(t),o=n.find(c);if(o){let e=o.props.children,t=n.map(r=>r!==o?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,i.jsx)(l,{...a,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,t):null})}return(0,i.jsx)(l,{...a,ref:r,children:t})});n.displayName="Slot";var l=s.forwardRef((e,r)=>{let{children:t,...i}=e;if(s.isValidElement(t)){let e=function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(t);return s.cloneElement(t,{...function(e,r){let t={...r};for(let s in r){let a=e[s],i=r[s];/^on[A-Z]/.test(s)?a&&i?t[s]=(...e)=>{i(...e),a(...e)}:a&&(t[s]=a):"style"===s?t[s]={...a,...i}:"className"===s&&(t[s]=[a,i].filter(Boolean).join(" "))}return{...e,...t}}(i,t.props),ref:r?function(...e){return r=>{let t=!1,s=e.map(e=>{let s=a(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():a(e[r],null)}}}}(r,e):e})}return s.Children.count(t)>1?s.Children.only(null):null});l.displayName="SlotClone";var o=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});function c(e){return s.isValidElement(e)&&e.type===o}},60791:(e,r,t)=>{"use strict";t.d(r,{j:()=>i});let s=e=>"boolean"==typeof e?"".concat(e):0===e?"0":e,a=function(){for(var e,r,t=0,s="";t<arguments.length;)(e=arguments[t++])&&(r=function e(r){var t,s,a="";if("string"==typeof r||"number"==typeof r)a+=r;else if("object"==typeof r){if(Array.isArray(r))for(t=0;t<r.length;t++)r[t]&&(s=e(r[t]))&&(a&&(a+=" "),a+=s);else for(t in r)r[t]&&(a&&(a+=" "),a+=t)}return a}(e))&&(s&&(s+=" "),s+=r);return s},i=(e,r)=>t=>{var i;if((null==r?void 0:r.variants)==null)return a(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:n,defaultVariants:l}=r,o=Object.keys(n).map(e=>{let r=null==t?void 0:t[e],a=null==l?void 0:l[e];if(null===r)return null;let i=s(r)||s(a);return n[e][i]}),c=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return a(e,o,null==r?void 0:null===(i=r.compoundVariants)||void 0===i?void 0:i.reduce((e,r)=>{let{class:t,className:s,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...l,...c}[r]):({...l,...c})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,4198,8344,4736],()=>t(70773));module.exports=s})();