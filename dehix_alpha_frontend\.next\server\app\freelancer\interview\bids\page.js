(()=>{var e={};e.id=9301,e.ids=[9301],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},43841:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>c}),s(10495),s(54302),s(12523);var r=s(23191),i=s(88716),a=s(37922),l=s.n(a),n=s(95231),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let c=["",{children:["freelancer",{children:["interview",{children:["bids",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,10495)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\interview\\bids\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\interview\\bids\\page.tsx"],u="/freelancer/interview/bids/page",x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/freelancer/interview/bids/page",pathname:"/freelancer/interview/bids",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27685:(e,t,s)=>{Promise.resolve().then(s.bind(s,76920))},47546:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},37358:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},69669:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},48998:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},76920:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>D});var r=s(10326),i=s(17577),a=s(25842),l=s(92166),n=s(30325),d=s(40588),c=s(38227);let o=()=>r.jsx("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,t)=>r.jsx(c.O,{className:"h-20 w-full rounded-lg"},t))});var u=s(30361),x=s(69669),m=s(48998),h=s(79635),p=s(47546),v=s(37358),f=s(71821),w=s(38443),g=s(91664),b=s(29752),j=s(3236);let y=({bid:e,interviewId:t,setConfirmAction:s})=>(0,r.jsxs)(b.Zb,{className:"relative w-full max-w-lg mx-auto p-6 rounded-2xl border  shadow-lg hover:shadow-2xl transition-all space-y-4",children:[(0,r.jsxs)(w.C,{className:`absolute top-3 right-3 text-xs flex items-center gap-2 px-2 py-1 font-semibold rounded-full shadow-md transition-all 
      ${e?.status==="ACCEPTED"?"text-green-800 bg-green-100":e?.status==="REJECTED"?"text-red-800 bg-red-100":"text-yellow-800 bg-yellow-100"}`,children:[e?.status==="ACCEPTED"?r.jsx(u.Z,{className:"w-4 h-4"}):e?.status==="REJECTED"?r.jsx(x.Z,{className:"w-4 h-4"}):r.jsx(m.Z,{className:"w-4 h-4"}),e?.status?.toUpperCase()||"PENDING"]}),(0,r.jsxs)(b.aY,{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 text-lg font-semibold ",children:[r.jsx(h.Z,{className:"w-5 h-5 "}),e?.interviewer?.userName||"Not Provided"]}),(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("p",{className:"text-sm font-semibold  mr-2",children:"Skills:"}),(0,r.jsxs)(j.x,{className:"w-full whitespace-nowrap overflow-x-auto ",children:[r.jsx("div",{className:"flex items-center gap-2",children:e?.interviewer?.skills?.length?e.interviewer.skills.map((e,t)=>r.jsx(w.C,{className:"px-3 py-1 text-xs  rounded-md shadow-sm",children:e},t)):r.jsx("p",{className:"text-xs text-gray-400",children:"Not Provided"})}),e?.interviewer?.skills?.length?r.jsx("div",{className:"mt-2",children:r.jsx(j.B,{orientation:"horizontal"})}):null]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 text-sm ",children:[r.jsx(p.Z,{className:"w-5 h-5 "}),e?.interviewer?.workExperience?`${e.interviewer.workExperience} years`:"Not Provided"]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm ",children:[(0,r.jsxs)("p",{className:"flex items-center gap-2",children:[r.jsx(v.Z,{className:"w-5 h-5 "}),e?.suggestedDateTime?new Date(e.suggestedDateTime).toLocaleString():"Not Provided"]}),(0,r.jsxs)("p",{className:"flex items-center gap-2",children:[r.jsx(f.Z,{className:"w-5 h-5 "}),e?.fee||"Not Provided"]})]}),e?.status==="PENDING"&&(0,r.jsxs)("div",{className:"flex justify-center gap-4 mt-3",children:[r.jsx(g.z,{className:"px-5 py-2 text-sm font-semibold bg-green-600 hover:bg-green-700 text-white rounded-lg shadow-md transition-all",onClick:()=>s({interviewId:t,bidId:e._id,action:"ACCEPTED"}),children:"Accept"}),r.jsx(g.z,{className:"px-5 py-2 text-sm font-semibold bg-red-600 hover:bg-red-700 text-white rounded-lg shadow-md transition-all",onClick:()=>s({interviewId:t,bidId:e._id,action:"REJECTED"}),children:"Reject"})]})]})]}),N=({interview:e,setConfirmAction:t})=>{let s=Object.values(e?.interviewBids||{});return r.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 mt-4",children:s.length>0?s.map(s=>r.jsx(y,{bid:s,interviewId:e?._id,setConfirmAction:t},s?._id)):r.jsx("div",{className:"col-span-full text-center text-lg mb-4 text-gray-500",children:"No bids are available for the interview"})})};var _=s(6260),k=s(56556),C=s(24118),E=s(56627);let q=({userId:e})=>{let[t,s]=(0,i.useState)([]),[a,l]=(0,i.useState)(!0),[n,d]=(0,i.useState)(null);(0,i.useEffect)(()=>{(async()=>{try{l(!0);let t=await _.b.get("/interview",{params:{intervieweeId:e}});console.log(t),s(t?.data?.data||[])}catch(e){console.error("Error fetching interview bids",e),(0,E.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}finally{l(!1)}})()},[e]);let c=async()=>{if(!n)return;let{interviewId:e,bidId:r,action:i}=n,a=t.find(t=>t._id===e);if(!a)return;let l=a.talentId?.id,c="ACCEPTED"===i,o=Object.values(a.interviewBids||{}).map(e=>({_id:e._id,interviewerId:e.interviewer?._id||e.interviewerId,dateTimeAgreement:e.dateTimeAgreement||!1,suggestedDateTime:e.suggestedDateTime||null,fee:e.fee||"0",status:e._id===r?i:c?"REJECTED":e.status})).filter(e=>e.interviewerId).reduce((e,t)=>(e[t._id]=t,e),{}),u=Object.values(o).some(e=>"ACCEPTED"===e.status)?"SCHEDULED":"BIDDING",x={_id:a._id,talentId:l,interviewBids:o,InterviewStatus:u};try{await _.b.put(`/interview/${e}`,x),s(t=>c?t.filter(t=>t._id!==e):t.map(t=>t._id===e?{...t,interviewBids:o,InterviewStatus:u}:t))}catch(e){console.error("Error updating interview bid:",e),(0,E.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}d(null)};return(0,r.jsxs)("div",{className:"w-[84vw] mx-auto ",children:[a?r.jsx(o,{}):t?.length>0?r.jsx(k.UQ,{type:"single",collapsible:!0,defaultValue:t?.[0]?._id,children:t.map(e=>(0,r.jsxs)(k.Qd,{value:e?._id||"",children:[r.jsx(k.o4,{className:"text-xl w-full font-semibold hover:no-underline",children:(0,r.jsxs)("div",{className:"flex justify-between items-center w-full mx-3",children:[r.jsx("div",{children:e?.talentType||"No Talent Label"}),(0,r.jsxs)("div",{children:[Object.keys(e?.interviewBids||{}).length," Bids"]})]})}),r.jsx(k.vF,{children:r.jsx(N,{interview:e,setConfirmAction:d})})]},e?._id))}):r.jsx("div",{className:"text-center text-lg font-semibold mt-4",children:"No bids available"}),n&&r.jsx(C.Vq,{open:!!n,onOpenChange:()=>d(null),children:(0,r.jsxs)(C.cZ,{className:"m-2 w-[80vw] md:max-w-lg ",children:[r.jsx(C.fK,{children:(0,r.jsxs)(C.$N,{children:["Confirm ",n.action?.toLowerCase()," action?"]})}),(0,r.jsxs)(C.cN,{children:[r.jsx(g.z,{variant:"outline",onClick:()=>d(null),children:"Cancel"}),r.jsx(g.z,{className:"mb-3",onClick:c,children:"Confirm"})]})]})})]})};function D(){let e=(0,a.v9)(e=>e.user);return(0,r.jsxs)("div",{className:"flex min-h-screen w-full  bg-muted/40",children:[r.jsx(l.Z,{menuItemsTop:n.y,menuItemsBottom:n.$,active:"Bids"}),(0,r.jsxs)("div",{className:"flex mb-8 flex-col sm:pl-14 w-full",children:[r.jsx(d.Z,{menuItemsTop:n.y,menuItemsBottom:n.$,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Interview",link:"/freelancer/interview/profile"},{label:"Bids",link:"#"}]}),(0,r.jsxs)("div",{className:"ml-10 mt-6 mb-10",children:[r.jsx("h1",{className:"text-3xl font-bold",children:"Interview Bid's"}),r.jsx("p",{className:"text-gray-400 mt-2",children:"Select your bids strategically and secure your interview"})]}),r.jsx(q,{userId:e.uid})]})]})}},38227:(e,t,s)=>{"use strict";s.d(t,{O:()=>a});var r=s(10326),i=s(51223);function a({className:e,...t}){return r.jsx("div",{className:(0,i.cn)("animate-pulse rounded-md bg-primary/10",e),...t})}},30325:(e,t,s)=>{"use strict";s.d(t,{$:()=>m,y:()=>x});var r=s(10326),i=s(95920),a=s(94909),l=s(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,l.Z)("ListVideo",[["path",{d:"M12 12H3",key:"18klou"}],["path",{d:"M16 6H3",key:"1wxfjs"}],["path",{d:"M12 18H3",key:"11ftsu"}],["path",{d:"m16 12 5 3-5 3v-6Z",key:"zpskkp"}]]);var d=s(47546);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,l.Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var o=s(88378),u=s(46226);let x=[{href:"#",icon:r.jsx(u.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:r.jsx(i.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/interview/profile",icon:r.jsx(a.Z,{className:"h-5 w-5"}),label:"Profile"},{href:"/freelancer/interview/current",icon:r.jsx(n,{className:"h-5 w-5"}),label:"Current"},{href:"/freelancer/interview/bids",icon:r.jsx(d.Z,{className:"h-5 w-5"}),label:"Bids"},{href:"/freelancer/interview/history",icon:r.jsx(c,{className:"h-5 w-5"}),label:"History"}],m=[{href:"/freelancer/settings/personal-info",icon:r.jsx(o.Z,{className:"h-5 w-5"}),label:"Settings"}]},10495:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>l,__esModule:()=>a,default:()=>n});var r=s(68570);let i=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\interview\bids\page.tsx`),{__esModule:a,$$typeof:l}=i;i.default;let n=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\interview\bids\page.tsx#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588],()=>s(43841));module.exports=r})();