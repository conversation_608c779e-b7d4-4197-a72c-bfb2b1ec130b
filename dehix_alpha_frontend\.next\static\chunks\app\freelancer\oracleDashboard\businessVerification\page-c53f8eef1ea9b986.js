(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4575],{97963:function(e,s,t){Promise.resolve().then(t.bind(t,9923))},87055:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},27218:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},4086:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},89832:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},11240:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,t(33480).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},9923:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return V}});var l=t(57437),r=t(2265),i=t(404),a=t(29406),n=t(89733),c=t(54662),m=t(97694),d=t(62688),x=t(64797),o=t(746),h=t(97540),u=t(15922),f=t(78068);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let j=(0,t(33480).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var p=t(27218),N=t(87055),y=t(11240),v=t(4086),b=t(89832),g=t(47390),k=t(31014),Z=t(39343),w=t(59772),C=t(48185),E=t(79055),_=t(93363),S=t(89736),z=t(4919);let I=w.z.object({type:w.z.enum(["verified","rejected"],{required_error:"You need to select a type."}),comment:w.z.string().optional()});var M=e=>{let{firstName:s,lastName:t,email:i,phone:a,companyName:c,companySize:d,referenceEmail:x,websiteLink:o,linkedInLink:h,githubLink:u,comments:f,status:w,onStatusUpdate:M,onCommentUpdate:V}=e,[B,J]=(0,r.useState)(w),L=(0,Z.cI)({resolver:(0,k.F)(I)}),F=L.watch("type");async function P(e){J(e.type),M(e.type),V(e.comment||"")}return(0,r.useEffect)(()=>{J(w)},[w]),(0,l.jsxs)(C.Zb,{className:"max-w-full md:max-w-2xl",children:[(0,l.jsxs)(C.Ol,{children:[(0,l.jsxs)(C.ll,{className:"flex justify-between",children:[(0,l.jsxs)("span",{children:[s," ",t]}),(0,l.jsxs)("div",{className:"flex flex-row space-x-3",children:[o&&(0,l.jsx)("a",{href:o,target:"_blank",rel:"noopener noreferrer",className:"text-sm underline flex items-center",children:(0,l.jsx)(j,{className:"mt-auto"})}),h&&(0,l.jsx)("a",{href:h,target:"_blank",rel:"noopener noreferrer",className:"text-sm  underline flex items-center",children:(0,l.jsx)(p.Z,{})}),u&&(0,l.jsx)("a",{href:u,target:"_blank",rel:"noopener noreferrer",className:"text-sm  underline flex items-center",children:(0,l.jsx)(N.Z,{})})]})]}),(0,l.jsxs)(C.SZ,{className:"mt-1 text-justify text-gray-600",children:[c,(0,l.jsx)("br",{}),"pending"===B?(0,l.jsx)(E.C,{className:"bg-warning-foreground text-white mt-2",children:"PENDING"}):"verified"===B?(0,l.jsx)(E.C,{className:"bg-success text-white mt-2",children:"VERIFIED"}):(0,l.jsx)(E.C,{className:"bg-red-500 text-white mt-2",children:"REJECTED"})]})]}),(0,l.jsx)(C.aY,{children:(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)(S.u,{children:[(0,l.jsx)(S.aJ,{asChild:!0,children:(0,l.jsxs)("p",{className:"text-sm text-gray-600 flex items-center",children:[(0,l.jsx)(y.Z,{className:"mr-2"}),d]})}),(0,l.jsx)(S._v,{side:"bottom",children:"Company Size"})]}),(0,l.jsxs)(S.u,{children:[(0,l.jsx)(S.aJ,{asChild:!0,children:(0,l.jsxs)("p",{className:"text-sm text-gray-600 flex items-center",children:[(0,l.jsx)(v.Z,{className:"mr-2"}),i]})}),(0,l.jsx)(S._v,{side:"bottom",children:"Email"})]}),(0,l.jsxs)(S.u,{children:[(0,l.jsx)(S.aJ,{asChild:!0,children:(0,l.jsxs)("p",{className:"text-sm text-gray-600 flex items-center",children:[(0,l.jsx)(b.Z,{className:"mr-2"}),a]})}),(0,l.jsx)(S._v,{side:"bottom",children:"Phone"})]}),(0,l.jsxs)(S.u,{children:[(0,l.jsx)(S.aJ,{asChild:!0,children:(0,l.jsxs)("p",{className:"text-sm text-gray-600 flex items-center",children:[(0,l.jsx)(v.Z,{className:"mr-2"}),x]})}),(0,l.jsx)(S._v,{side:"bottom",children:"Reference Email"})]}),f&&(0,l.jsxs)("p",{className:"mt-2 flex items-center text-gray-500 border p-3 rounded",children:[(0,l.jsx)(g.Z,{className:"mr-2"}),f]})]})}),(0,l.jsx)(C.eW,{className:"flex flex-col items-center",children:"pending"===B&&(0,l.jsx)(_.l0,{...L,children:(0,l.jsxs)("form",{onSubmit:L.handleSubmit(P),className:"w-full space-y-6 mt-6",children:[(0,l.jsx)(_.Wi,{control:L.control,name:"type",render:e=>{let{field:s}=e;return(0,l.jsxs)(_.xJ,{className:"space-y-3",children:[(0,l.jsx)(_.lX,{children:"Choose Verification Status:"}),(0,l.jsx)(_.NI,{children:(0,l.jsxs)(m.E,{onValueChange:s.onChange,defaultValue:s.value,className:"flex flex-col space-y-1",children:[(0,l.jsxs)(_.xJ,{className:"flex items-center space-x-3",children:[(0,l.jsx)(_.NI,{children:(0,l.jsx)(m.m,{value:"verified"})}),(0,l.jsx)(_.lX,{className:"font-normal",children:"Verified"})]}),(0,l.jsxs)(_.xJ,{className:"flex items-center space-x-3",children:[(0,l.jsx)(_.NI,{children:(0,l.jsx)(m.m,{value:"rejected"})}),(0,l.jsx)(_.lX,{className:"font-normal",children:"Rejected"})]})]})}),(0,l.jsx)(_.zG,{})]})}}),(0,l.jsx)(_.Wi,{control:L.control,name:"comment",render:e=>{let{field:s}=e;return(0,l.jsxs)(_.xJ,{children:[(0,l.jsx)(_.lX,{children:"Comments:"}),(0,l.jsx)(_.NI,{children:(0,l.jsx)(z.g,{placeholder:"Enter comments:",...s})}),(0,l.jsx)(_.zG,{})]})}}),(0,l.jsx)(n.z,{type:"submit",className:"w-full",disabled:!F||L.formState.isSubmitting,children:"Submit"})]})})})]})};function V(){let[e,s]=(0,r.useState)([]),[t,j]=(0,r.useState)("all"),[p,N]=(0,r.useState)(!1),y=e=>{j(e),N(!1)},v=e.filter(e=>"all"===t||("current"===t?e.verificationStatus===h.sB.PENDING:e.verificationStatus===t)),b=(0,r.useCallback)(async()=>{try{let e=(await u.b.get("/verification/oracle?doc_type=business")).data.data.flatMap(e=>{var s;return(null===(s=e.result)||void 0===s?void 0:s.projects)?Object.values(e.result.projects).map(s=>({...s,verifier_id:e.verifier_id,verifier_username:e.verifier_username})):[]});s(e)}catch(e){console.error("Error in getting verification data:",e),(0,f.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}},[]);(0,r.useEffect)(()=>{b()},[b]);let g=(t,l)=>{let r=[...e];r[t].status=l,s(r)},k=(t,l)=>{let r=[...e];r[t].comments=l,s(r)};return(0,l.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,l.jsx)(x.Z,{menuItemsTop:o.y,menuItemsBottom:o.$,active:"Business Verification"}),(0,l.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,l.jsx)(d.Z,{menuItemsTop:o.y,menuItemsBottom:o.$,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Oracle",link:"#"},{label:"Business Verification",link:"#"}]}),(0,l.jsxs)("div",{className:"mb-8 ml-4 flex justify-between mt-8 md:mt-4 items-center",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold",children:"Business Verification"}),(0,l.jsx)("p",{className:"text-gray-400 mt-2",children:"Monitor the status of your Business verifications."})]}),(0,l.jsx)(n.z,{variant:"outline",size:"icon",className:"mr-8 mb-12",onClick:()=>N(!0),children:(0,l.jsx)(i.Z,{className:"h-4 w-4"})})]}),(0,l.jsx)(c.Vq,{open:p,onOpenChange:N,children:(0,l.jsxs)(c.cZ,{children:[(0,l.jsx)(c.fK,{children:(0,l.jsx)(c.$N,{children:"Filter Business Verification"})}),(0,l.jsxs)(m.E,{value:t,onValueChange:e=>y(e),className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(m.m,{value:"all",id:"filter-all"}),(0,l.jsx)("label",{htmlFor:"filter-all",children:"All"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(m.m,{value:"current",id:"filter-current"}),(0,l.jsx)("label",{htmlFor:"filter-current",children:"Pending"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(m.m,{value:"verified",id:"filter-verified"}),(0,l.jsx)("label",{htmlFor:"filter-verified",children:"Verified"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(m.m,{value:"rejected",id:"filter-rejected"}),(0,l.jsx)("label",{htmlFor:"filter-rejected",children:"Rejected"})]})]}),(0,l.jsx)(c.cN,{children:(0,l.jsx)(n.z,{type:"button",onClick:()=>N(!1),children:"Close"})})]})}),(0,l.jsx)("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8    grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3",children:v.length>0?v.map((e,s)=>(0,l.jsx)(M,{_id:e._id,firstName:e.firstName,lastName:e.lastName,email:e.email,phone:e.phone,companyName:e.companyName,companySize:e.companySize,referenceEmail:e.referenceEmail,websiteLink:e.websiteLink,linkedInLink:e.linkedInLink,githubLink:e.githubLink,comments:e.comments,status:e.status,onStatusUpdate:e=>g(s,e),onCommentUpdate:e=>k(s,e)},s)):(0,l.jsxs)("div",{className:"text-center w-full col-span-full mt-20 py-10",children:[(0,l.jsx)(a.Z,{className:"mx-auto text-gray-500",size:"100"}),(0,l.jsx)("p",{className:"text-gray-500",children:"No Business verification records found."})]})})]})]})}}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,1374,2455,9726,2688,2480,2971,7023,1744],function(){return e(e.s=97963)}),_N_E=e.O()}]);