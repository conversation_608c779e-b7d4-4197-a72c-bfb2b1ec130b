(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8867],{49777:function(e,s,a){Promise.resolve().then(a.bind(a,83363))},76035:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},25912:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},94499:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},43193:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]])},24241:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},76780:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},40933:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},7746:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},23787:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},47019:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},75733:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},49100:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},4086:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},59061:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},36141:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},33907:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},33149:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]])},40064:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("TabletSmartphone",[["rect",{width:"10",height:"14",x:"3",y:"8",rx:"2",key:"1vrsiq"}],["path",{d:"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4",key:"1j4zmg"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},11240:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(33480).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},83363:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return H}});var t=a(57437),l=a(2265),r=a(11444),i=a(3274),c=a(74697),n=a(32970),d=a(87446),o=a(64797),m=a(66227),h=a(89733),x=a(15922),u=a(80023),p=a(78068),f=a(62688);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let j=(0,a(33480).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var y=a(47019),N=a(75733),b=a(87138),v=a(48185),g=a(79055),k=a(6460),w=a(94499),Z=a(4086),C=a(25912),M=a(24241),S=a(40933),z=a(7746),D=a(11240),E=a(51077),P=a(92940),L=a(55936),A=a(29973),I=a(15642);function F(e){let{project:s,text:a,icon:r,isSizeSmall:i}=e,[c,n]=(0,l.useState)(!1),d=e=>(0,k.WU)(new Date(e),"MMM dd, yyyy");return(0,t.jsxs)(L.yo,{open:c,onOpenChange:n,children:[(0,t.jsx)(L.aM,{asChild:!0,children:(0,t.jsxs)(h.z,{variant:"outline",size:i?"sm":void 0,className:i?"":"w-full",children:[r,a]})}),(0,t.jsxs)(L.ue,{className:"w-full sm:max-w-md md:max-w-lg overflow-y-auto no-scrollbar",side:"left",children:[(0,t.jsxs)(L.Tu,{className:"pb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between mt-2 items-center",children:[(0,t.jsx)(L.bC,{className:"text-2xl font-bold",children:s.projectName}),(0,t.jsx)("div",{className:"flex items-center gap-2 mt-1",children:(0,t.jsx)(g.C,{className:(e=>{switch(e){case"COMPLETED":return"bg-green-100 text-green-800 hover:bg-green-100";case"IN_PROGRESS":return"bg-blue-100 text-blue-800 hover:bg-blue-100";case"PENDING":return"bg-yellow-100 text-yellow-800 hover:bg-yellow-100";default:return"bg-gray-100 text-gray-800 hover:bg-gray-100"}})(s.status),children:s.status})})]}),(0,t.jsx)(L.Ei,{className:"mt-2 text-base",children:s.description})]}),(0,t.jsx)(A.Separator,{className:"my-4"}),(0,t.jsxs)(v.Zb,{className:"mb-4 border-0 shadow-none",children:[(0,t.jsx)(v.Ol,{className:"p-0 pb-2",children:(0,t.jsxs)(v.ll,{className:"text-lg flex items-center gap-2",children:[(0,t.jsx)(w.Z,{className:"h-4 w-4"}),"Company Information"]})}),(0,t.jsxs)(v.aY,{className:"p-0 space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"font-medium",children:"Company:"}),(0,t.jsx)("span",{children:s.companyName})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(Z.Z,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{children:s.email})]})]})]}),(0,t.jsxs)(v.Zb,{className:"mb-4 border-0 shadow-none",children:[(0,t.jsx)(v.Ol,{className:"p-0 pb-2",children:(0,t.jsxs)(v.ll,{className:"text-lg flex items-center gap-2",children:[(0,t.jsx)(C.Z,{className:"h-4 w-4"}),"Project Details"]})}),(0,t.jsxs)(v.aY,{className:"p-0",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(M.Z,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsxs)("span",{className:"text-sm",children:["Created: ",d(s.createdAt)]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(S.Z,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsxs)("span",{className:"text-sm",children:["Updated: ",d(s.updatedAt)]})]})]}),s.skillsRequired.length>0&&(0,t.jsxs)("div",{className:"mb-3",children:[(0,t.jsxs)("div",{className:"font-medium mb-1 flex items-center gap-2",children:[(0,t.jsx)(z.Z,{className:"h-4 w-4"}),"Skills Required:"]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1.5",children:s.skillsRequired.map((e,s)=>(0,t.jsx)(g.C,{variant:"secondary",className:"font-normal",children:e},s))})]})]})]}),s.profiles.length>0&&(0,t.jsxs)(v.Zb,{className:"mb-4 border-0 shadow-none",children:[(0,t.jsx)(v.Ol,{className:"p-0 pb-2",children:(0,t.jsxs)(v.ll,{className:"text-lg flex items-center gap-2",children:[(0,t.jsx)(D.Z,{className:"h-4 w-4"}),"Profile Requirements (",s.profiles.length,")"]})}),(0,t.jsx)(v.aY,{className:"p-0",children:(0,t.jsxs)(I.lr,{className:"w-full mt-2",children:[(0,t.jsx)(I.KI,{children:s.profiles.map((e,s)=>(0,t.jsx)(I.d$,{className:"md:basis-full",children:(0,t.jsxs)("div",{className:"border rounded-lg p-3",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.domain}),(0,t.jsxs)(g.C,{variant:"outline",className:"font-normal",children:[e.freelancersRequired," freelancers"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2 mb-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(S.Z,{className:"h-3.5 w-3.5 text-muted-foreground"}),(0,t.jsxs)("span",{children:[e.experience,"+ years exp."]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(E.Z,{className:"h-3.5 w-3.5 text-muted-foreground"}),(0,t.jsxs)("span",{children:["$",e.rate,"/hr"]})]})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:e.description}),(0,t.jsxs)("div",{className:"flex items-center gap-1 text-sm",children:[(0,t.jsx)(P.Z,{className:"h-3.5 w-3.5 text-green-600"}),(0,t.jsxs)("span",{children:[e.totalBid.length," bids received"]})]})]})},s))}),(0,t.jsxs)("div",{className:"flex items-center justify-end mt-6",children:[(0,t.jsx)(I.am,{className:"relative right-0 translate-x-0 mr-2"}),(0,t.jsx)(I.Pz,{className:"relative right-0 translate-x-0"})]})]})})]}),(0,t.jsx)("div",{className:"mt-6 flex justify-end",children:(0,t.jsx)(h.z,{variant:"outline",onClick:()=>n(!1),children:"Close"})})]})]})}var q=a(62143);let _=()=>(0,t.jsx)("div",{className:"w-5 h-5 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"});var R=e=>{let{job:s,onApply:a,onNotInterested:i,bidExist:c}=e,[n,d]=(0,l.useState)(!1),[o,m]=(0,l.useState)(!1),u=(0,r.I0)(),p=(0,r.v9)(e=>e.projectDraft.draftedProjects),f=null==p?void 0:p.includes(s._id),k=async()=>{m(!0);try{let e=await x.b.put("/freelancer/draft",{project_id:s._id});200===e.status&&u((0,q.kd)(s._id))}catch(e){console.error("Failed to add project to draft:",e)}finally{m(!1)}},w=async()=>{m(!0);try{let e=await x.b.delete("/freelancer/draft",{data:{project_id:s._id}});200===e.status&&u((0,q.MR)(s._id))}catch(e){console.error("Failed to remove project from draft:",e)}finally{m(!1)}},Z=s.profiles&&s.profiles.length>0?s.profiles[0]:null;return(0,t.jsxs)(v.Zb,{className:"w-[97%]",children:[(0,t.jsx)(v.Ol,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(v.ll,{className:"text-xl",children:[s.projectName," "]}),(0,t.jsxs)(v.SZ,{className:"mt-1",children:["Position: ",s.position||"Web developer"," \xb7 Exp:"," ",(null==Z?void 0:Z.years)||"2"," yrs"]})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center gap-3",children:[s.status&&(0,t.jsx)(g.C,{variant:"outline",className:"pending"===s.status.toLowerCase()?"bg-amber-300/10 text-amber-500 border-amber-500/20":"bg-green-500/10 text-green-500 border-green-500/20",children:s.status}),o?(0,t.jsx)(_,{}):(0,t.jsx)(j,{className:"w-5 h-5 cursor-pointer ".concat(f?"fill-red-600 text-red-600":"hover:fill-red-700 hover:text-red-700"),onClick:o?void 0:f?w:k})]})]})}),(0,t.jsxs)(v.aY,{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500 ".concat(!n&&"line-clamp-3"),children:s.description}),s.description&&s.description.length>150&&(0,t.jsx)("button",{onClick:()=>d(!n),className:"text-primary text-sm mt-1 hover:underline",children:n?"less":"more"}),(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Skills required"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:s.skillsRequired&&s.skillsRequired.map((e,s)=>(0,t.jsx)(g.C,{variant:"secondary",className:"rounded-md",children:e},s))})]}),Z&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[Z.positions&&(0,t.jsxs)("span",{className:"bg-primary/10 text-primary px-2 py-1 rounded text-xs",children:[Z.positions," Positions"]}),Z.years&&(0,t.jsxs)("span",{className:"bg-primary/10 text-primary px-2 py-1 rounded text-xs",children:[Z.years," Years"]})]}),Z.connectsRequired&&(0,t.jsxs)("div",{className:"mt-2 text-sm",children:["Connects required:"," ",(0,t.jsx)("span",{className:"font-medium",children:Z.connectsRequired})]})]})]}),(0,t.jsxs)(v.eW,{className:"flex justify-end gap-2",children:[(0,t.jsxs)(h.z,{variant:"outline",size:"sm",onClick:i,className:"text-gray-500",children:[(0,t.jsx)(y.Z,{className:"h-4 w-4 mr-1"}),"Not Interested"]}),(0,t.jsx)(F,{icon:(0,t.jsx)(N.Z,{className:"h-4 w-4 mr-1"}),project:s,text:"View",isSizeSmall:!0}),(0,t.jsx)(b.default,{href:"/freelancer/market/project/".concat(s._id,"/apply"),children:(0,t.jsx)(h.z,{type:"submit",className:"",size:"sm",disabled:c,children:c?"Applied":"Bid"})})]})]})},V=a(76780),O=a(59061),T=a(23787),B=a(2183);function Y(e){let{open:s,setOpen:a}=e,[r,i]=(0,l.useState)([]),[c,n]=(0,l.useState)(!1);(0,l.useEffect)(()=>{s&&d()},[s]);let d=async()=>{try{var e,s;n(!0);let a=await x.b.get("/freelancer/draft/detail");i((null===(s=a.data)||void 0===s?void 0:null===(e=s.projectDraft)||void 0===e?void 0:e.projectDrafts)||[])}catch(e){console.error("Error fetching draft project details:",e)}finally{n(!1)}},o=e=>{switch(e){case"COMPLETED":return(0,t.jsxs)(g.C,{variant:"outline",className:"flex bg-green-900 text-green-500 items-center gap-1",children:[(0,t.jsx)(P.Z,{className:"h-3 w-3"}),"Completed"]});case"PENDING":return(0,t.jsxs)(g.C,{variant:"outline",className:"flex items-center gap-1 bg-amber-100 text-amber-700 dark:bg-amber-900 dark:text-amber-300",children:[(0,t.jsx)(S.Z,{className:"h-3 w-3"}),"Pending"]});default:return(0,t.jsxs)(g.C,{variant:"secondary",className:"flex items-center gap-1",children:[(0,t.jsx)(V.Z,{className:"h-3 w-3"}),e]})}};return(0,t.jsxs)(L.yo,{open:s,onOpenChange:a,children:[(0,t.jsx)(L.aM,{asChild:!0,children:(0,t.jsxs)(h.z,{variant:"default",children:[(0,t.jsx)(O.Z,{className:"mr-2 h-4 w-4"})," Drafts"]})}),(0,t.jsxs)(L.ue,{className:"w-[90vw] sm:max-w-lg p-0",children:[(0,t.jsxs)(L.Tu,{className:"p-6 pb-2",children:[(0,t.jsx)(L.bC,{children:"Draft Project Details"}),(0,t.jsx)(L.Ei,{children:"These are your saved drafts."})]}),(0,t.jsx)(u.x,{className:"h-[calc(100vh-10rem)]",children:c?(0,t.jsx)("div",{className:"p-6 space-y-4",children:[1,2].map(e=>(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(B.O,{className:"h-5 w-2/3"}),(0,t.jsx)(B.O,{className:"h-4 w-full"}),(0,t.jsx)(B.O,{className:"h-20 w-full"})]},e))}):(0,t.jsx)("div",{className:"p-6 pt-2 space-y-6",children:0===r.length?(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-10 text-center",children:[(0,t.jsx)(O.Z,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,t.jsx)("p",{className:"text-lg font-medium",children:"No drafts found"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Your saved project drafts will appear here"})]}):r.map(e=>(0,t.jsxs)(v.Zb,{className:"overflow-hidden border shadow-sm",children:[(0,t.jsxs)(v.Ol,{className:"pb-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsx)(v.ll,{className:"text-xl",children:e.projectName}),o(e.status)]}),(0,t.jsx)(v.SZ,{className:"line-clamp-2",children:e.description})]}),(0,t.jsxs)(v.aY,{className:"pb-2 space-y-3",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-x-4 gap-y-1 text-sm",children:[(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Company"}),(0,t.jsx)("span",{className:"font-medium",children:e.companyName})]}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Email"}),(0,t.jsx)("span",{className:"font-medium truncate",children:e.email})]})]}),e.skillsRequired&&e.skillsRequired.length>0&&(0,t.jsx)("div",{className:"flex flex-wrap gap-1 mt-2",children:e.skillsRequired.map((e,s)=>e&&(0,t.jsx)(g.C,{variant:"outline",children:e},s))}),e.profiles.length>0&&(0,t.jsxs)("div",{className:"mt-3",children:[(0,t.jsxs)("h4",{className:"text-sm font-semibold mb-2",children:["Profiles (",e.profiles.length,")"]}),(0,t.jsxs)("div",{className:"space-y-3",children:[e.profiles.slice(0,2).map(e=>{var s;return(0,t.jsxs)("div",{className:"bg-muted/40 rounded-lg p-3 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-1",children:[(0,t.jsx)("span",{className:"font-medium",children:e.domain||"No Domain"}),(0,t.jsxs)(g.C,{variant:"secondary",className:"text-xs",children:["$",e.rate,"/hr"]})]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground line-clamp-2 mb-2",children:e.description}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-x-4 gap-y-1 text-xs",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("span",{className:"text-muted-foreground",children:["Required:"," "]}),(0,t.jsx)("span",{children:e.freelancersRequired})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("span",{className:"text-muted-foreground",children:["Experience:"," "]}),(0,t.jsxs)("span",{children:[e.experience," years"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("span",{className:"text-muted-foreground",children:["Bids:"," "]}),(0,t.jsx)("span",{children:(null===(s=e.totalBid)||void 0===s?void 0:s.length)||0})]})]})]},e._id)}),e.profiles.length>2&&(0,t.jsxs)(h.z,{variant:"ghost",size:"sm",className:"w-full text-xs",children:["Show ",e.profiles.length-2," more profiles"]})]})]})]}),(0,t.jsx)(v.eW,{className:"pt-2",children:(0,t.jsx)(F,{isSizeSmall:!1,icon:(0,t.jsx)(T.Z,{className:"h-3 w-3 mr-1"}),project:e,text:"View Project Details"})})]},e._id))})}),(0,t.jsxs)("div",{className:"p-6 pt-0 mb-3",children:[(0,t.jsx)(A.Separator,{className:"my-2"}),(0,t.jsx)(L.FF,{children:(0,t.jsx)(L.sw,{asChild:!0,children:(0,t.jsx)(h.z,{type:"button",children:"Close"})})})]})]})]})}var H=()=>{let e=(0,r.v9)(e=>e.user),s=(0,r.I0)(),[a,j]=(0,l.useState)(!1),[y,N]=(0,l.useState)(!1),[b,v]=(0,l.useState)("Filter by Project Domains"),[g,k]=(0,l.useState)(!1),[w,Z]=(0,l.useState)({jobType:[],domain:[],skills:[],projects:[],projectDomain:[]}),[C,M]=(0,l.useState)([]),[S,z]=(0,l.useState)([]),[D,E]=(0,l.useState)([]),[P,L]=(0,l.useState)([]),[A,I]=(0,l.useState)([]),[F,_]=(0,l.useState)(!1);(0,l.useEffect)(()=>{j(!0)},[]);let V=(0,l.useCallback)(async()=>{try{let s=(await x.b.get("/bid/".concat(e.uid,"/bid"))).data.data.map(e=>e.profile_id);I(s)}catch(e){console.error("API Error:",e),(0,p.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}},[e.uid]);(0,l.useEffect)(()=>{V()},[V]),(0,l.useEffect)(()=>{(async()=>{try{let e=await x.b.get("/freelancer/draft");s((0,q.Dj)(e.data.projectDraft))}catch(e){console.error(e)}})()},[s]),(0,l.useEffect)(()=>{(async()=>{try{_(!0);let e=await x.b.get("/skills");z(e.data.data.map(e=>e.label));let s=await x.b.get("/domain");E(s.data.data.map(e=>e.label));let a=await x.b.get("/projectdomain");L(a.data.data.map(e=>e.label))}catch(e){console.error("Error loading filters",e),(0,p.Am)({variant:"destructive",title:"Error",description:"Failed to load filter options."})}finally{_(!1)}})()},[]);let O=(e,s)=>{Z(a=>({...a,[e]:s}))},T=()=>{Z({jobType:[],domain:[],skills:[],projects:[],projectDomain:[]})},B=e=>Object.entries(e).filter(e=>{let[s,a]=e;return a.length>0}).map(e=>{let[s,a]=e;return"".concat(s,"=").concat(a.join(","))}).join("&"),H=(0,l.useCallback)(async s=>{try{_(!0);let a=(await x.b.get("/freelancer")).data,t=B(s),l=(await x.b.get("/project/freelancer/".concat(e.uid,"?").concat(t))).data.data||[],r=a.notInterestedProject||[],i=l.filter(e=>!r.includes(e._id));M(i),console.log(i)}catch(e){console.error("Fetch jobs error:",e),(0,p.Am)({variant:"destructive",title:"Error",description:"Failed to load job listings."})}finally{_(!1)}},[e.uid]);(0,l.useEffect)(()=>{H(w)},[H]);let W=()=>{H(w)},$=()=>{window.innerWidth>=1024&&N(!1)};(0,l.useEffect)(()=>(window.addEventListener("resize",$),()=>window.removeEventListener("resize",$)),[]);let X=()=>{N(e=>!e)},U=async e=>{try{await x.b.put("/freelancer/".concat(e,"/not_interested_project")),M(s=>s.filter(s=>s._id!==e)),(0,p.Am)({title:"Success",description:"Project marked as not interested."})}catch(e){console.error("Remove job error:",e),(0,p.Am)({variant:"destructive",title:"Error",description:"Failed to update project status."})}},G=async e=>{try{await x.b.post("/project/apply/".concat(e)),(0,p.Am)({title:"Success",description:"Application submitted successfully."})}catch(e){console.error("Application error:",e),(0,p.Am)({variant:"destructive",title:"Error",description:"Failed to apply to the project."})}};return a?(0,t.jsxs)("div",{className:"flex min-h-screen bg-muted  w-full flex-col  pb-10",children:[(0,t.jsx)(o.Z,{menuItemsTop:m.yn,menuItemsBottom:m.$C,active:"Market"}),(0,t.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,t.jsx)(f.Z,{menuItemsTop:m.yn,menuItemsBottom:m.$C,activeMenu:"Market",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Marketplace",link:"#"}]}),(0,t.jsxs)("div",{className:"flex  items-start sm:items-center justify-between",children:[(0,t.jsxs)("div",{className:"w-full sm:w-[70%] mb-4 sm:mb-8 ml-4 sm:ml-8",children:[(0,t.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold",children:"Freelancer Marketplace"}),(0,t.jsx)("p",{className:"text-gray-400 mt-2 hidden sm:block",children:"Discover and manage your freelance opportunities, connect with potential projects, and filter by skills, domains and project domains to enhance your portfolio."})]}),(0,t.jsx)("div",{className:"w-full sm:w-[30%] flex justify-end pr-4 sm:pr-8",children:(0,t.jsx)(Y,{open:g,setOpen:k})})]})]}),(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row lg:space-x-6 px-4 lg:px-20 md:px-8",children:[(0,t.jsx)("div",{className:"hidden bg-background p-3 rounded-md lg:block lg:sticky lg:top-16 lg:w-1/3 xl:w-1/3 lg:self-start lg:h-[calc(100vh-4rem)]",children:(0,t.jsxs)(u.x,{className:"h-full no-scrollbar overflow-y-auto pr-4 space-y-4",children:[(0,t.jsx)(h.z,{onClick:W,className:"w-full",children:"Apply"}),(0,t.jsx)(h.z,{variant:"outline",onClick:T,className:"w-full mb-4 bg-gray dark:text-white",style:{marginTop:"1rem"},children:"Reset"}),(0,t.jsx)("div",{className:"my-4",children:(0,t.jsx)(n.Z,{heading:"Filter by Project Domains",checkboxLabels:P,selectedValues:w.projectDomain,setSelectedValues:e=>O("projectDomain",e),openItem:b,setOpenItem:v,useAccordion:!0})}),(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsx)(n.Z,{heading:"Filter by Skills",checkboxLabels:S,selectedValues:w.skills,setSelectedValues:e=>O("skills",e),openItem:b,setOpenItem:v,useAccordion:!0})}),(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsx)(n.Z,{heading:"Filter by Domains",checkboxLabels:D,selectedValues:w.domain,setSelectedValues:e=>O("domain",e),openItem:b,setOpenItem:v,useAccordion:!0})})]})}),F?(0,t.jsx)("div",{className:"mt-4 lg:mt-0 space-y-4 w-full flex justify-center items-center h-[60vh]",children:(0,t.jsx)(i.Z,{size:40,className:"text-primary animate-spin"})}):(0,t.jsx)("div",{className:"mt-4 lg:mt-0 w-full",children:(0,t.jsx)(u.x,{className:"h-[calc(100vh-8rem)] sm:h-[calc(100vh-4rem)] no-scrollbar overflow-y-auto",children:(0,t.jsx)("div",{className:"grid grid-cols-1 gap-6 pb-20 lg:pb-4",children:C.length>0?C.map(e=>(0,t.jsx)(R,{job:e,onApply:()=>G(e._id),onNotInterested:()=>U(e._id),bidExist:Array.isArray(e.profiles)&&e.profiles.some(e=>A.includes(e._id))},e._id)):(0,t.jsx)("div",{className:"text-center py-10",children:(0,t.jsx)("p",{className:"text-gray-400",children:"No projects found matching your filters."})})})})})]}),a&&y&&(0,t.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4 overflow-hidden",children:(0,t.jsxs)("div",{className:"bg-secondary rounded-lg w-full max-w-screen-lg mx-auto h-[80vh] max-h-full flex flex-col",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center p-4 border-b border-gray-300",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold",children:" Filters"}),(0,t.jsx)(h.z,{variant:"ghost",size:"sm",onClick:X,children:(0,t.jsx)(c.Z,{className:"h-5 w-5"})})]}),(0,t.jsxs)("div",{className:"overflow-y-auto p-4 flex-grow",children:[(0,t.jsx)("div",{className:"border-b border-gray-300 pb-4",children:(0,t.jsx)(d.Z,{label:"Domains",heading:"Filter by domain",checkboxLabels:D,selectedValues:w.domain,setSelectedValues:e=>O("domain",e)})}),(0,t.jsx)("div",{className:"border-b border-gray-300 py-4",children:(0,t.jsx)(d.Z,{label:"Skills",heading:"Filter by skills",checkboxLabels:S,selectedValues:w.skills,setSelectedValues:e=>O("skills",e)})}),(0,t.jsx)("div",{className:"pt-4",children:(0,t.jsx)(d.Z,{label:"ProjectDomain",heading:"Filter by project-domain",checkboxLabels:P,selectedValues:w.projectDomain,setSelectedValues:e=>O("projectDomain",e)})})]}),(0,t.jsx)("div",{className:"p-4 border-t border-gray-300",children:(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)(h.z,{onClick:W,className:"flex-1",children:"Apply"}),(0,t.jsx)(h.z,{variant:"outline",onClick:T,className:"flex-1",children:"Reset"})]})})]})}),a&&(0,t.jsx)("div",{className:"fixed bottom-0 left-0 right-0 lg:hidden p-4 flex justify-center z-40",children:(0,t.jsx)("button",{className:"w-full max-w-xs p-3 bg-primary text-white dark:text-black rounded-md hover:bg-primary/90 transition-colors duration-300 ease-in-out shadow-lg font-medium",onClick:X,children:y?"Hide Filters":"Show Filters"})})]}):null}},87446:function(e,s,a){"use strict";var t=a(57437),l=a(2265),r=a(14392),i=a(42421),c=a(44541),n=a(70402),d=a(77209),o=a(89733);s.Z=e=>{let{label:s,heading:a,checkboxLabels:m,selectedValues:h,setSelectedValues:x}=e,[u,p]=l.useState(!1),[f,j]=l.useState(""),y=e=>{h.includes(e)?x(h.filter(s=>s!==e)):x([...h,e])},N=m.filter(e=>e.toLowerCase().includes(f.toLowerCase())),b=N.slice(0,3),v=N.slice(3);return(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"mt-2 text-white",children:a}),(0,t.jsxs)("div",{className:"items-center p-2",children:[(0,t.jsx)(d.I,{placeholder:"Search ".concat(s),value:f,onChange:e=>j(e.target.value),className:"mb-2 bg-secondary border-black"}),b.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)(c.X,{id:e,checked:h.includes(e),onCheckedChange:()=>y(e)}),(0,t.jsx)(n.Label,{htmlFor:e,className:"text-sm",children:e})]},e)),u&&v.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)(c.X,{id:e,checked:h.includes(e),onCheckedChange:()=>y(e)}),(0,t.jsx)(n.Label,{htmlFor:e,className:"text-sm",children:e})]},e)),N.length>3&&(0,t.jsx)("div",{className:"flex items-center mb-1",children:(0,t.jsxs)(o.z,{size:"sm",variant:"ghost",className:"flex items-center text-sm cursor-pointer ml-auto",onClick:()=>p(!u),children:[u?"Less":"More",u?(0,t.jsx)(r.Z,{className:"ml-1 h-4 w-4"}):(0,t.jsx)(i.Z,{className:"ml-1 h-4 w-4"})]})}),0===N.length&&(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"No skills found."})]})]})}},32970:function(e,s,a){"use strict";var t=a(57437),l=a(2265),r=a(14392),i=a(42421),c=a(35265),n=a(48185),d=a(89733),o=a(77209),m=a(70402),h=a(44541);s.Z=e=>{let{label:s="Skills",heading:a,checkboxLabels:x,selectedValues:u,setSelectedValues:p,openItem:f,setOpenItem:j,useAccordion:y=!1}=e,[N,b]=(0,l.useState)(""),[v,g]=(0,l.useState)(!1),k=e=>{u.includes(e)?p(u.filter(s=>s!==e)):p([...u,e])},w=x.filter(e=>e.toLowerCase().includes(N.toLowerCase())),Z=w.slice(0,3),C=w.slice(3);return y?(0,t.jsx)(n.Zb,{className:"w-full",children:(0,t.jsx)(c.UQ,{type:"single",collapsible:!0,value:f===a?a:"",onValueChange:e=>null==j?void 0:j(e===a?a:null),children:(0,t.jsxs)(c.Qd,{value:a,children:[(0,t.jsx)(c.o4,{className:"text-base px-4 py-2",children:a}),(0,t.jsx)(c.vF,{children:(0,t.jsxs)("div",{className:"px-4 mt-2 pb-4 space-y-3",children:[(0,t.jsx)(o.I,{type:"text",placeholder:"Search ".concat(a.toLowerCase(),"..."),value:N,onChange:e=>b(e.target.value)}),(0,t.jsxs)("div",{className:"max-h-52 overflow-y-auto no-scrollbar space-y-2",children:[w.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.X,{id:e,checked:u.includes(e),onCheckedChange:()=>k(e)}),(0,t.jsx)(m.Label,{htmlFor:e,className:"text-sm",children:e})]},e)),0===w.length&&(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"No options found."})]})]})})]})})}):(0,t.jsxs)(n.Zb,{className:"w-full",children:[(0,t.jsx)(n.Ol,{children:(0,t.jsx)(n.ll,{className:"text-lg",children:a})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsx)(o.I,{type:"text",placeholder:"Search ".concat(s),value:N,onChange:e=>b(e.target.value),className:"w-full mb-2"}),(0,t.jsxs)(t.Fragment,{children:[Z.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)(h.X,{id:e,checked:u.includes(e),onCheckedChange:()=>k(e)}),(0,t.jsx)(m.Label,{htmlFor:e,className:"text-sm",children:e})]},e)),v&&C.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)(h.X,{id:e,checked:u.includes(e),onCheckedChange:()=>k(e)}),(0,t.jsx)(m.Label,{htmlFor:e,className:"text-sm",children:e})]},e))]})]}),(0,t.jsxs)(n.eW,{children:[w.length>3&&(0,t.jsxs)(d.z,{size:"sm",variant:"ghost",className:"flex items-center text-sm cursor-pointer ml-auto",onClick:()=>g(!v),children:[v?"Less":"More",v?(0,t.jsx)(r.Z,{className:"ml-1 h-4 w-4"}):(0,t.jsx)(i.Z,{className:"ml-1 h-4 w-4"})]}),0===w.length&&(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Skills"===s?"No skills found.":"No domain found."})]})]})}},44541:function(e,s,a){"use strict";a.d(s,{X:function(){return n}});var t=a(57437),l=a(2265),r=a(93943),i=a(22468),c=a(49354);let n=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.fC,{ref:s,className:(0,c.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...l,children:(0,t.jsx)(r.z$,{className:(0,c.cn)("flex items-center justify-center text-current"),children:(0,t.jsx)(i.Z,{className:"h-4 w-4"})})})});n.displayName=r.fC.displayName},70402:function(e,s,a){"use strict";a.r(s),a.d(s,{Label:function(){return d}});var t=a(57437),l=a(2265),r=a(38364),i=a(12218),c=a(49354);let n=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.f,{ref:s,className:(0,c.cn)(n(),a),...l})});d.displayName=r.f.displayName},29973:function(e,s,a){"use strict";a.r(s),a.d(s,{Separator:function(){return c}});var t=a(57437),l=a(2265),r=a(48484),i=a(49354);let c=l.forwardRef((e,s)=>{let{className:a,orientation:l="horizontal",decorative:c=!0,...n}=e;return(0,t.jsx)(r.f,{ref:s,decorative:c,orientation:l,className:(0,i.cn)("shrink-0 bg-border","horizontal"===l?"h-[1px] w-full":"h-full w-[1px]",a),...n})});c.displayName=r.f.displayName},2183:function(e,s,a){"use strict";a.d(s,{O:function(){return r}});var t=a(57437),l=a(49354);function r(e){let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,l.cn)("animate-pulse rounded-md bg-primary/10",s),...a})}},66227:function(e,s,a){"use strict";a.d(s,{$C:function(){return N},yL:function(){return b},yn:function(){return y}});var t=a(57437),l=a(11005),r=a(33149),i=a(76035),c=a(49100),n=a(40064),d=a(43193),o=a(36141),m=a(33907),h=a(47390),x=a(73347),u=a(24258),p=a(5891),f=a(10883),j=a(66648);let y=[{href:"#",icon:(0,t.jsx)(j.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,t.jsx)(l.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:(0,t.jsx)(r.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:(0,t.jsx)(i.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:(0,t.jsx)(c.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:(0,t.jsx)(n.Z,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:(0,t.jsx)(d.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:(0,t.jsx)(o.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:(0,t.jsx)(m.Z,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:(0,t.jsx)(h.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,t.jsx)(x.Z,{className:"h-5 w-5"}),label:"Notes"}],N=[{href:"/freelancer/settings/personal-info",icon:(0,t.jsx)(u.Z,{className:"h-5 w-5"}),label:"Settings"}];j.default,l.Z,x.Z,p.Z,f.Z;let b=[{href:"#",icon:(0,t.jsx)(j.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,t.jsx)(l.Z,{className:"h-5 w-5"}),label:"Home"}]},62143:function(e,s,a){"use strict";a.d(s,{Dj:function(){return c},MR:function(){return r},kd:function(){return l}});let t=(0,a(69753).oM)({name:"projectDraft",initialState:{draftedProjects:[]},reducers:{addDraftedProject:(e,s)=>{e.draftedProjects.includes(s.payload)||e.draftedProjects.push(s.payload)},removeDraftedProject:(e,s)=>{e.draftedProjects=e.draftedProjects.filter(e=>e!==s.payload)},clearDraftedProjects:e=>{e.draftedProjects=[]},setDraftedProjects:(e,s)=>{e.draftedProjects=s.payload}}}),{addDraftedProject:l,removeDraftedProject:r,clearDraftedProjects:i,setDraftedProjects:c}=t.actions;s.ZP=t.reducer},48484:function(e,s,a){"use strict";a.d(s,{f:function(){return d}});var t=a(2265),l=a(18676),r=a(57437),i="horizontal",c=["horizontal","vertical"],n=t.forwardRef((e,s)=>{let{decorative:a,orientation:t=i,...n}=e,d=c.includes(t)?t:i;return(0,r.jsx)(l.WV.div,{"data-orientation":d,...a?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...n,ref:s})});n.displayName="Separator";var d=n}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,571,2455,9726,2688,2971,7023,1744],function(){return e(e.s=49777)}),_N_E=e.O()}]);