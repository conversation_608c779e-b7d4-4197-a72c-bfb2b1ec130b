(()=>{var e={};e.id=9238,e.ids=[9238],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},15031:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>x,originalPathname:()=>p,pages:()=>l,routeModule:()=>d,tree:()=>u}),s(49707),s(54302),s(12523);var r=s(23191),i=s(88716),n=s(37922),a=s.n(n),o=s(95231),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);s.d(t,c);let u=["",{children:["notes",{children:["archive",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,49707)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\notes\\archive\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\notes\\archive\\page.tsx"],p="/notes/archive/page",x={require:s,loadChunk:()=>Promise.resolve()},d=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/notes/archive/page",pathname:"/notes/archive",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},38282:(e,t,s)=>{Promise.resolve().then(s.bind(s,20822))},20822:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(10326),i=s(17577),n=s(25842),a=s(77506),o=s(12389),c=s(74935),u=s(92166),l=s(46319),p=s(17978),x=s(40588),d=s(48586);let h=()=>{let e=(0,n.v9)(e=>e.user).uid,{archive:t,isLoading:s,fetchNotes:h,setArchive:m}=(0,p.Z)(e);return(0,i.useEffect)(()=>{e&&h()},[e,h]),(0,r.jsxs)("section",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[r.jsx(u.Z,{menuItemsTop:l.Ne,menuItemsBottom:d.$C,active:"Archive"}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[r.jsx(x.Z,{menuItemsTop:l.Ne,menuItemsBottom:d.$C,activeMenu:"Archive",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Notes",link:"/notes"},{label:"Archive",link:"/archive"}]}),(0,r.jsxs)("div",{className:"",children:[r.jsx(c.Z,{isTrash:!0,setNotes:m,notes:t}),r.jsx("div",{className:"p-6",children:s?r.jsx("div",{className:"flex justify-center items-center h-[40vh]",children:r.jsx(a.Z,{className:"my-4 h-8 w-8 animate-spin"})}):t?.length>0?r.jsx(o.Z,{notes:t,setNotes:m,isArchive:!0,fetchNotes:h}):r.jsx("div",{className:"flex justify-center items-center h-[40vh]",children:r.jsx("p",{children:"No archive available. Start adding some!"})})})]})]})]})}},49707:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>a,__esModule:()=>n,default:()=>o});var r=s(68570);let i=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\notes\archive\page.tsx`),{__esModule:n,$$typeof:a}=i;i.default;let o=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\notes\archive\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588,5442],()=>s(15031));module.exports=r})();