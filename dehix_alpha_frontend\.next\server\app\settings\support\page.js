(()=>{var e={};e.id=99,e.ids=[99],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},62134:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>d}),s(2768),s(54302),s(12523);var a=s(23191),i=s(88716),r=s(37922),l=s.n(r),n=s(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let d=["",{children:["settings",{children:["support",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,2768)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\settings\\support\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\settings\\support\\page.tsx"],u="/settings/support/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/settings/support/page",pathname:"/settings/support",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},84408:(e,t,s)=>{Promise.resolve().then(s.bind(s,72763))},4198:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(80851).Z)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},72763:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var a=s(10326),i=s(17577),r=s(25842),l=s(66562),n=s(46226),c=s(4198),d=s(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,d.Z)("LayoutGrid",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]]);var u=s(77506),p=s(43810),m=s(56627),h=s(6260),x=s(91664),f=s(41190),g=s(82015),j=s(24118),y=s(29752),v=s(52792);let b=()=>{let{user:e}=(0,v.a)(),[t,s]=(0,i.useState)(""),[r,l]=(0,i.useState)(""),[d,b]=(0,i.useState)(null),[N]=(0,i.useState)(e?.uid||""),[w,k]=(0,i.useState)("BUSINESS"),[q,S]=(0,i.useState)([]),[C,A]=(0,i.useState)(!1),[_,D]=(0,i.useState)(!1),[F,T]=(0,i.useState)(!1),[P,I]=(0,i.useState)(null),[E,Z]=(0,i.useState)("card"),[z,U]=(0,i.useState)(null),[M,V]=(0,i.useState)(null),[B,$]=(0,i.useState)(!1),[O,G]=(0,i.useState)(!1);(0,i.useEffect)(()=>{(async()=>{try{G(!0);let t=(await h.b.get(`/ticket?customerID=${e?.uid}`)).data.data.filter(t=>t.customerID===e?.uid);S(t)}catch(e){(0,m.Am)({variant:"destructive",title:"Error",description:"An error occurred while fetching tickets."})}finally{G(!1)}})()},[e?.uid]);let H=async s=>{if(s.preventDefault(),!t||!r||!N){(0,m.Am)({variant:"destructive",title:"Missing Fields",description:"Please fill in all required fields."});return}let a="";if(d)try{let e=new FormData;e.append("file",d);let{Location:t}=(await h.b.post("/register/upload-image",e,{headers:{"Content-Type":"multipart/form-data"}})).data.data;a=t}catch(e){(0,m.Am)({variant:"destructive",title:"Upload Failed",description:"Failed to upload the file. Please try again."});return}let i={customerID:e?.uid,customerType:w,description:r,status:"CREATED",subject:t,filesAttached:a};try{$(!0);let e=await h.b.post("/ticket",i);(0,m.Am)({title:"Ticket Submitted",description:"Ticket created successfully."}),S(t=>[...t,e.data.data]),A(!1),W()}catch{(0,m.Am)({variant:"destructive",title:"Error",description:"Failed to submit ticket."})}finally{$(!1)}},L=e=>{I(e._id),s(e.subject),l(e.description),D(!0)},W=()=>{s(""),l(""),k("BUSINESS"),b(null)},R=async e=>{if(e.preventDefault(),!P||!t||!r)return;$(!0);let s=z?.filesAttached||"";if(d)try{let e=new FormData;e.append("file",d);let{Location:t}=(await h.b.post("/register/upload-image",e,{headers:{"Content-Type":"multipart/form-data"}})).data.data;s=t}catch(e){(0,m.Am)({variant:"destructive",title:"Upload Failed",description:"Failed to upload the file. Please try again."});return}try{let e=await h.b.put(`/ticket/${P}`,{subject:t,description:r,filesAttached:s});200===e.status?(S(e=>e.map(e=>e._id===P?{...e,subject:t,description:r,filesAttached:s}:e)),(0,m.Am)({title:"Ticket Updated",description:"The ticket has been successfully updated."}),D(!1),W()):(0,m.Am)({variant:"destructive",title:"Update Failed",description:"Could not update the ticket."})}catch{(0,m.Am)({variant:"destructive",title:"Error",description:"An error occurred while updating the ticket."})}finally{$(!1)}},Y=e=>{U(e),T(!0)},Q=e=>{navigator.clipboard.writeText(e).then(()=>{(0,m.Am)({title:"ID Copied",description:"Ticket ID has been copied to your clipboard.",duration:1500})}).catch(()=>{(0,m.Am)({variant:"destructive",title:"Copy Failed",description:"Failed to copy the Ticket ID."})})};return(0,a.jsxs)("div",{className:"max-w-8xl mx-auto bg-background p-6 rounded shadow-sm",children:[a.jsx("h1",{className:" mt-3 sm:text-3xl",children:"Submit a Support Ticket"}),(0,a.jsxs)(j.Vq,{open:C,onOpenChange:A,children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx(j.hg,{asChild:!0,children:a.jsx(x.z,{onClick:()=>A(!0),className:"mt-6",children:"Create Ticket"})}),a.jsx("div",{className:"my-4",children:a.jsx(x.z,{onClick:()=>Z("cards"===E?"table":"cards"),children:"cards"===E?a.jsx(c.Z,{className:"w-5 h-5"}):a.jsx(o,{className:"w-5 h-5"})})})]}),(0,a.jsxs)(j.cZ,{children:[a.jsx(j.$N,{children:"Create a New Ticket"}),a.jsx(j.Be,{children:"Fill out the form to submit a support ticket."}),(0,a.jsxs)("form",{onSubmit:H,className:"space-y-4 flex flex-col",children:[a.jsx(f.I,{id:"subject",placeholder:"Subject",value:t,onChange:e=>s(e.target.value),required:!0}),a.jsx(g.g,{id:"description",placeholder:"Description",value:r,onChange:e=>l(e.target.value),rows:4,required:!0}),(0,a.jsxs)("div",{className:"file-upload-container w-80 mx-auto mt-6",children:[a.jsx("div",{className:"file-upload p-4 border-2 border-dashed border-gray-400 rounded-lg text-center",style:{cursor:"pointer"},onClick:()=>document.getElementById("file-input")?.click(),children:d?(0,a.jsxs)(a.Fragment,{children:[d.type.startsWith("image/")?M?a.jsx(n.default,{src:M,alt:"File Preview",width:200,height:200,className:"rounded border mt-4"}):a.jsx("div",{className:"text-center",children:a.jsx("p",{children:"Loading preview..."})}):(0,a.jsxs)("div",{className:"p-4 border rounded bg-gray-100",children:[a.jsx("p",{className:"text-sm",children:"Preview not available for this file type"}),a.jsx("p",{className:"text-xs text-gray-500",children:d.name})]}),a.jsx("button",{onClick:()=>{b(null),V(null)},className:"mt-4 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600",children:"Remove File"})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx("p",{className:"text-lg text-gray-500",children:"Click or drag to upload a file"}),a.jsx("p",{className:"text-sm text-gray-400",children:"Supported formats: Images, PDFs, etc."})]})}),a.jsx("input",{id:"file-input",type:"file",className:"hidden",onChange:e=>{let t=e.target.files?.[0];if(t){if(t.size>5242880){(0,m.Am)({variant:"destructive",title:"File Too Large",description:"The selected file exceeds the size limit of 5MB. Please select a smaller file."});return}if(!["image/jpeg","image/png","image/gif","application/pdf"].includes(t.type)){(0,m.Am)({variant:"destructive",title:"Unsupported File Type",description:"The selected file type is not supported. Please upload an image or PDF."});return}b(t),V(URL.createObjectURL(t))}else(0,m.Am)({variant:"destructive",title:"Invalid File",description:"Failed to load the file. Please try again."})},accept:"image/*,application/pdf"})]}),a.jsx(x.z,{disabled:B,type:"submit",children:B?a.jsx(u.Z,{className:"animate-spin w-8 h-8"}):"Submit Ticket"})]})]})]}),a.jsx(j.Vq,{open:_,onOpenChange:D,children:(0,a.jsxs)(j.cZ,{children:[a.jsx(j.$N,{children:"Edit Ticket"}),a.jsx(j.Be,{children:"Update the subject and description of the ticket."}),(0,a.jsxs)("form",{onSubmit:R,className:"space-y-4 flex flex-col",children:[a.jsx(f.I,{id:"edit-subject",placeholder:"Subject",value:t,onChange:e=>s(e.target.value),required:!0}),a.jsx(g.g,{id:"edit-description",placeholder:"Description",value:r,onChange:e=>l(e.target.value),rows:4,required:!0}),a.jsx(x.z,{disabled:B,type:"submit",children:B?a.jsx(u.Z,{className:"animate-spin w-8 h-8"}):"Update Ticket"})]})]})}),a.jsx(j.Vq,{open:F,onOpenChange:T,children:(0,a.jsxs)(j.cZ,{children:[a.jsx(j.$N,{children:"Ticket Details"}),a.jsx(j.Be,{children:"View and edit the details of the ticket."}),(0,a.jsxs)(y.Zb,{className:"p-6 space-y-4 rounded-md shadow-md",children:[a.jsx("div",{className:"space-y-2",children:(0,a.jsxs)("p",{className:"text-lg font-semibold",children:[a.jsx("strong",{children:"Subject: "}),z?.subject]})}),a.jsx("div",{className:"space-y-2",children:(0,a.jsxs)("p",{className:"text-sm",children:[a.jsx("strong",{children:"Description: "}),z?.description]})}),a.jsx("div",{className:"space-y-2",children:(0,a.jsxs)("p",{className:"text-sm",children:[a.jsx("strong",{children:"Status: "}),z?.status]})}),z?.filesAttached&&(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("p",{className:"text-sm font-medium",children:a.jsx("strong",{children:"Attached File:"})}),a.jsx("div",{className:"flex flex-col items-start space-y-2",children:z.filesAttached.match(/\.(jpeg|jpg|gif|png)$/)?a.jsx(n.default,{src:z.filesAttached,alt:"Attached file",width:200,height:200,className:"object-cover rounded-md shadow-md"}):a.jsx("a",{href:z.filesAttached,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 underline",children:"View/Download File"})})]})]})]})}),O?a.jsx("div",{className:"flex justify-center  items-center h-[60vh]",children:a.jsx(u.Z,{className:"h-10 w-10 animate-spin text-blue-500"})}):"cards"===E?a.jsx("div",{className:"mt-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:q.length>0?q.map(e=>(0,a.jsxs)(y.Zb,{className:"p-4 shadow-md rounded-lg border",children:[a.jsx("h2",{className:"text-lg font-semibold",children:e.subject}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[a.jsx("span",{className:"text-sm text-gray-600",children:e._id}),a.jsx(x.z,{variant:"outline",onClick:()=>Q(e._id),size:"icon",className:"p-1",children:a.jsx(p.Z,{className:"h-4 w-4"})})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-2",children:[a.jsx("strong",{children:"Description:"})," ",e.description]}),(0,a.jsxs)("p",{className:"text-sm font-medium mt-2",children:[a.jsx("strong",{children:"Status:"})," ",e.status]}),e.filesAttached&&(0,a.jsxs)("div",{className:"mt-3",children:[a.jsx("p",{className:"text-sm font-medium",children:"Attached File:"}),e.filesAttached.match(/\.(jpeg|jpg|gif|png)$/)?a.jsx(n.default,{src:e.filesAttached,alt:"Attached file",width:80,height:80,className:"rounded-md object-cover mt-2"}):a.jsx("a",{href:e.filesAttached,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 underline text-sm",children:"View/Download File"})]}),(0,a.jsxs)("div",{className:"mt-4 flex justify-between items-center",children:[a.jsx(x.z,{onClick:()=>L(e),size:"sm",children:"Edit"}),a.jsx(x.z,{onClick:()=>Y(e),size:"sm",children:"Open Ticket"})]})]},e._id)):a.jsx("p",{className:"text-center text-gray-500",children:"No tickets available."})}):a.jsx("div",{className:"overflow-x-auto mt-6",children:(0,a.jsxs)("table",{className:"min-w-full table-auto",children:[a.jsx("thead",{children:(0,a.jsxs)("tr",{className:"bg-white text-black",children:[a.jsx("th",{className:"px-4 py-2 text-left",children:"Subject"}),a.jsx("th",{className:"px-4 py-2 text-left",children:"Ticket ID"}),a.jsx("th",{className:"px-4 py-2 text-left",children:"Description"}),a.jsx("th",{className:"px-4 py-2 text-left",children:"Status"}),a.jsx("th",{className:"px-4 py-2 text-left"})]})}),a.jsx("tbody",{children:q.length>0?q.map(e=>(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"px-4 py-2 text-sm border-gray-300",children:e.subject.length>45?(0,a.jsxs)(a.Fragment,{children:[e.description.substring(0,45)," ",a.jsx("span",{onClick:()=>alert(e.subject),className:"text-white-500 cursor-pointer",children:"..."})]}):e.subject}),a.jsx("td",{className:"px-4 py-2",children:e._id}),a.jsx("td",{className:"px-4 py-2 text-sm border-gray-300",children:e.description.length>45?(0,a.jsxs)(a.Fragment,{children:[e.description.substring(0,45)," ",a.jsx("span",{onClick:()=>alert(e.description),className:"text-white-500 cursor-pointer",children:"..."})]}):e.description}),a.jsx("td",{className:"px-4 py-2",children:e.status}),(0,a.jsxs)("td",{className:"px-4 py-2 flex justify-between",children:[a.jsx(x.z,{onClick:()=>L(e),size:"sm",className:"mr-2 mt-2",variant:"outline",children:"Edit"}),a.jsx(x.z,{onClick:()=>Y(e),size:"sm",className:"ml-2 mt-2",variant:"outline",children:"Open Ticket"})]})]},e._id)):a.jsx("tr",{children:a.jsx("td",{colSpan:5,className:"px-4 py-2 text-center text-gray-500",children:"No tickets available."})})})]})})]})};var N=s(14987),w=s(95920);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let k=(0,d.Z)("HeartHandshake",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}],["path",{d:"M12 5 9.04 7.96a2.17 2.17 0 0 0 0 3.08v0c.82.82 2.13.85 3 .07l2.07-1.9a2.82 2.82 0 0 1 3.79 0l2.96 2.66",key:"12sd6o"}],["path",{d:"m18 15-2-2",key:"60u0ii"}],["path",{d:"m15 18-2-2",key:"6p76be"}]]),q=[{href:"#",icon:a.jsx(n.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:a.jsx(w.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/settings/support",icon:a.jsx(k,{className:"h-5 w-5"}),label:"support"}];var S=s(92166),C=s(40588);let A=()=>{let e=(0,r.v9)(e=>e.user),[,t]=(0,i.useState)(null),[s,n]=(0,i.useState)({name:"",email:"",message:""}),c=[],d=e=>{let{id:t,value:s}=e.target;"message"===t&&s.length>500||n(e=>({...e,[t]:s}))};return(0,i.useEffect)(()=>{e?.type?t(e.type):t(l.Z.get("userType")||null)},[e]),(0,a.jsxs)("div",{className:"",children:[a.jsx(S.Z,{menuItemsTop:q,menuItemsBottom:c,active:"support"}),(0,a.jsxs)("div",{className:"flex flex-col sm:gap-4 sm:py-4  sm:pl-14 mb-8",children:[a.jsx(C.Z,{menuItemsTop:q,menuItemsBottom:c,activeMenu:"Dashboard",breadcrumbItems:[{label:"Business",link:"/dashboard/business"},{label:"Support",link:"#"}]}),a.jsx("div",{className:"ml-2",children:(0,a.jsxs)("div",{className:"mb-8 ",children:[a.jsx(b,{})," "]})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsxs)("section",{className:"px-4 pt-20 md:px-6",children:[a.jsx("div",{className:"max-w-3xl mx-auto text-center",children:a.jsx("h2",{className:" sm:text-3xl",children:"FAQs"})}),a.jsx(N.Z,{})]}),a.jsx("section",{className:"px-4 py-20 md:px-6",children:a.jsx("div",{className:"max-w-3xl mx-auto text-center"})}),a.jsx("section",{id:"Contact",className:"px-4 py-20 md:px-6",children:(0,a.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[a.jsx("h2",{className:" sm:text-3xl",children:"Get in Touch"}),a.jsx("p",{className:"mt-4 md:text-xl",children:"Have a project in mind? Let's discuss how we can help."}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),(0,m.Am)({title:"Message sent successfully!",description:"Thanks for connecting! We will connect soon.",duration:1500}),n({name:"",email:"",message:""})},className:"mt-8 space-y-4 text-left",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-base font-medium ",htmlFor:"name",children:"Name"}),a.jsx(f.I,{className:"mt-2 w-full rounded-md  px-4 py-3  focus:outline-none focus:ring-2 focus:ring-[#00ffff]",id:"name",placeholder:"Enter your name",type:"text",required:!0,value:s.name,onChange:d})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-base font-medium ",htmlFor:"email",children:"Email"}),a.jsx(f.I,{className:"mt-2 w-full rounded-md  px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#00ffff]",id:"email",placeholder:"Enter your email",type:"email",required:!0,value:s.email,onChange:d})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-base font-medium ",htmlFor:"message",children:"Message"}),a.jsx(g.g,{className:"mt-2 w-full rounded-md px-4 py-3  focus:outline-none focus:ring-2 focus:ring-[#00ffff]",id:"message",required:!0,placeholder:"Enter your message",value:s.message,onChange:d}),(0,a.jsxs)("p",{className:"text-right text-sm text-gray-500",children:[s.message.length,"/",500," characters"]})]}),a.jsx(x.z,{type:"submit",className:"  px-8 py-3 rounded-md text-lg font-medium",children:"Send Message"})]})]})})]})]})]})}},14987:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var a=s(10326);s(17577);var i=s(56556);let r=[{question:"How can I start freelancing on our platform?",answer:"To start freelancing, you can create an account and set up your profile. Once done, you can browse available projects or create your own listings."},{question:"What types of projects are available?",answer:"Our platform hosts a wide range of projects including content creation, web development, graphic design, marketing, and more. You can find projects that match your skills and interests."},{question:"How are freelancers vetted on your platform?",answer:"We have a rigorous vetting process to ensure that freelancers have the necessary skills and experience. This includes reviewing portfolios, conducting interviews, and verifying credentials."},{question:"Can I hire freelancers from different countries?",answer:"Yes, our platform connects you with freelancers from around the world. You can choose freelancers based on their location, skills, and availability."},{question:"What payment methods are supported?",answer:"We support various payment methods including credit/debit cards, PayPal, and bank transfers. You can choose the payment method that is most convenient for you."}],l=()=>a.jsx(i.UQ,{type:"single",collapsible:!0,className:"w-full md:w-3/4 lg:w-2/3 xl:w-1/2 mx-auto",children:r.map((e,t)=>(0,a.jsxs)(i.Qd,{value:`item-${t}`,children:[a.jsx(i.o4,{children:e.question}),a.jsx(i.vF,{children:e.answer})]},t))})},2768:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>l,__esModule:()=>r,default:()=>n});var a=s(68570);let i=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\settings\support\page.tsx`),{__esModule:r,$$typeof:l}=i;i.default;let n=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\settings\support\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588],()=>s(62134));module.exports=a})();