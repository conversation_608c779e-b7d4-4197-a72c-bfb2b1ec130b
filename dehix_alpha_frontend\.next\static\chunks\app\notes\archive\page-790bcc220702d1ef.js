(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9238],{17162:function(e,s,t){Promise.resolve().then(t.bind(t,20789))},20789:function(e,s,t){"use strict";t.r(s);var l=t(57437),n=t(2265),i=t(11444),c=t(3274),a=t(85121),r=t(5498),m=t(64797),h=t(82230),o=t(22637),u=t(62688),d=t(66227);s.default=()=>{let e=(0,i.v9)(e=>e.user).uid,{archive:s,isLoading:t,fetchNotes:f,setArchive:v}=(0,o.Z)(e);return(0,n.useEffect)(()=>{e&&f()},[e,f]),(0,l.jsxs)("section",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[(0,l.jsx)(m.Z,{menuItemsTop:h.Ne,menuItemsBottom:d.$C,active:"Archive"}),(0,l.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[(0,l.jsx)(u.Z,{menuItemsTop:h.Ne,menuItemsBottom:d.$C,activeMenu:"Archive",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Notes",link:"/notes"},{label:"Archive",link:"/archive"}]}),(0,l.jsxs)("div",{className:"",children:[(0,l.jsx)(r.Z,{isTrash:!0,setNotes:v,notes:s}),(0,l.jsx)("div",{className:"p-6",children:t?(0,l.jsx)("div",{className:"flex justify-center items-center h-[40vh]",children:(0,l.jsx)(c.Z,{className:"my-4 h-8 w-8 animate-spin"})}):(null==s?void 0:s.length)>0?(0,l.jsx)(a.Z,{notes:s,setNotes:v,isArchive:!0,fetchNotes:f}):(0,l.jsx)("div",{className:"flex justify-center items-center h-[40vh]",children:(0,l.jsx)("p",{children:"No archive available. Start adding some!"})})})]})]})]})}}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2455,9726,2688,6755,2971,7023,1744],function(){return e(e.s=17162)}),_N_E=e.O()}]);