(()=>{var e={};e.id=7878,e.ids=[7878],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},13481:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>l,routeModule:()=>x,tree:()=>d}),r(13748),r(54302),r(12523);var s=r(23191),a=r(88716),i=r(37922),n=r.n(i),o=r(95231),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);r.d(t,c);let d=["",{children:["bidmanagement",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,13748)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\bidmanagement\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\bidmanagement\\page.tsx"],u="/bidmanagement/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/bidmanagement/page",pathname:"/bidmanagement",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},57870:(e,t,r)=>{Promise.resolve().then(r.bind(r,64561))},64561:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(10326),a=r(25842),i=r(17577),n=r(6260),o=r(91664),c=r(29752),d=r(84097);let l=({bid:e,onAction:t,actions:r})=>{let[a,l]=(0,i.useState)(""),[u,p]=(0,i.useState)(""),[x,m]=(0,i.useState)(!0),{_id:h,current_price:g,project_id:b,bidder_id:f}=e,[y,v]=(0,i.useState)("");(0,i.useEffect)(()=>{(async()=>{try{let e=(await n.b.get(`/project/${b}`)).data.data.projectName;l(e)}catch(e){(0,d.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.error("Error fetching project name:",e)}})()},[b]),(0,i.useEffect)(()=>{(async()=>{try{let e=(await n.b.get(`/freelancer/${f}`)).data.userName;p(e)}catch(e){(0,d.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.error("Error fetching bidder name:",e)}})()},[f]);let _=async e=>{try{await t(h,e),v(`Candidate ${e}ed`),m(!1)}catch(t){(0,d.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),v(`Error performing ${e} action.`),console.error("Error updating bid status:",t)}};return(0,s.jsxs)(c.Zb,{className:"p-4 mb-4 rounded-lg",children:[(0,s.jsxs)(c.Ol,{children:[(0,s.jsxs)(c.ll,{className:"text-xl font-bold",children:["Project: ",a]}),(0,s.jsxs)(c.SZ,{children:["Current Price: $",g]}),(0,s.jsxs)(c.SZ,{children:["Bidder: ",u]})]}),s.jsx(c.aY,{children:x&&s.jsx("div",{className:"actions mt-4",children:r.map(e=>s.jsx(o.z,{className:`bg-${e.variant}-500 hover:bg-${e.variant}-600 text-white font-bold py-2 px-4 rounded mr-2`,onClick:()=>_(e.label),children:e.label},e.type))})}),y&&s.jsx(c.eW,{children:s.jsx("p",{className:"text-lg font-semibold text-green-400",children:y})})]})},u=({bids:e,onAction:t})=>{let r=[{label:"Select",type:"select",variant:"success"},{label:"Reject",type:"reject",variant:"danger"},{label:"Schedule Interview",type:"schedule",variant:"primary"},{label:"Move to Lobby",type:"lobby",variant:"secondary"}];return s.jsx("div",{className:"applied-bids",children:e.map(e=>s.jsx(l,{bid:e,onAction:t,actions:r},e._id))})};var p=r(56627);let x=()=>{let e=(0,a.v9)(e=>e.user),[t,r]=(0,i.useState)([]),[o,c]=(0,i.useState)([]),d=(0,i.useMemo)(()=>({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."}),[]);(0,i.useEffect)(()=>{(async()=>{try{let e=(await n.b.get("/project/business/?status=Pending")).data.data.map(e=>e._id);r(e)}catch(e){console.error("Error fetching project IDs:",e)}})()},[e.uid]),(0,i.useEffect)(()=>{let e=async()=>{try{let e=[];for(let r of t)(await n.b.get(`/bid/${r}/bids`)).data.data.forEach(t=>{"Pending"===t.bid_status&&e.push(t)});c(e)}catch(e){(0,p.Am)(d),console.error("Error fetching bids:",e)}};t.length&&e()},[t,d]);let l=async(e,t)=>{let r;"Accept"===t?r="Accepted":"Reject"===t?r="Rejected":"Schedule Interview"===t?r="Interview":"Lobby"===t&&(r="Lobby");try{await n.b.put(`/bid/${e}/status`,{bid_status:r})}catch(e){(0,p.Am)(d),console.error("Error updating bid status:",e)}};return(0,s.jsxs)("div",{className:"bids-page max-w-6xl mx-auto p-8  mb-8",children:[s.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Manage Bids"}),o.length?s.jsx(u,{bids:o,onAction:l}):s.jsx("p",{className:"",children:"No bids available."})]})}},84097:(e,t,r)=>{"use strict";r.d(t,{Am:()=>l}),r(17577);let s=0,a=new Map,i=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],c={toasts:[]};function d(e){c=n(c,e),o.forEach(e=>{e(c)})}function l({...e}){let t=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>d({type:"DISMISS_TOAST",toastId:t});return d({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:t}})}}},13748:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>o});var s=r(68570);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\bidmanagement\page.tsx`),{__esModule:i,$$typeof:n}=a;a.default;let o=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\bidmanagement\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,4198,4736],()=>r(13481));module.exports=s})();