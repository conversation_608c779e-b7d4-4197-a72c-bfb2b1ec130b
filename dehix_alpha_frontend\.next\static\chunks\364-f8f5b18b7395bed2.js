"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[364],{60364:function(e,t,r){r.d(t,{VY:function(){return z},zt:function(){return F},fC:function(){return H},xz:function(){return Y}});var n=r(2265),o=r(78149),i=r(1584),l=r(98324),a=r(53938),s=r(53201),u=r(25510),c=(r(56935),r(31383)),d=r(18676),p=r(57437);n.forwardRef((e,t)=>{let{children:r,...o}=e,i=n.Children.toArray(r),l=i.find(g);if(l){let e=l.props.children,r=i.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,p.jsx)(f,{...o,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,p.jsx)(f,{...o,ref:t,children:r})}).displayName="Slot";var f=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){let e,l;let a=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref;return n.cloneElement(r,{...function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{i(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props),ref:t?(0,i.F)(t,a):a})}return n.Children.count(r)>1?n.Children.only(null):null});f.displayName="SlotClone";var h=({children:e})=>(0,p.jsx)(p.Fragment,{children:e});function g(e){return n.isValidElement(e)&&e.type===h}var x=r(91715),y=r(31725),[v,m]=(0,l.b)("Tooltip",[u.D7]),w=(0,u.D7)(),b="TooltipProvider",C="tooltip.open",[E,T]=v(b),R=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,[a,s]=n.useState(!0),u=n.useRef(!1),c=n.useRef(0);return n.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,p.jsx)(E,{scope:t,isOpenDelayed:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(c.current),s(!1)},[]),onClose:n.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>s(!0),o)},[o]),isPointerInTransitRef:u,onPointerInTransitChange:n.useCallback(e=>{u.current=e},[]),disableHoverableContent:i,children:l})};R.displayName=b;var j="Tooltip",[k,M]=v(j),L=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:i=!1,onOpenChange:l,disableHoverableContent:a,delayDuration:c}=e,d=T(j,e.__scopeTooltip),f=w(t),[h,g]=n.useState(null),y=(0,s.M)(),v=n.useRef(0),m=null!=a?a:d.disableHoverableContent,b=null!=c?c:d.delayDuration,E=n.useRef(!1),[R=!1,M]=(0,x.T)({prop:o,defaultProp:i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(C))):d.onClose(),null==l||l(e)}}),L=n.useMemo(()=>R?E.current?"delayed-open":"instant-open":"closed",[R]),P=n.useCallback(()=>{window.clearTimeout(v.current),E.current=!1,M(!0)},[M]),_=n.useCallback(()=>{window.clearTimeout(v.current),M(!1)},[M]),D=n.useCallback(()=>{window.clearTimeout(v.current),v.current=window.setTimeout(()=>{E.current=!0,M(!0)},b)},[b,M]);return n.useEffect(()=>()=>window.clearTimeout(v.current),[]),(0,p.jsx)(u.fC,{...f,children:(0,p.jsx)(k,{scope:t,contentId:y,open:R,stateAttribute:L,trigger:h,onTriggerChange:g,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayed?D():P()},[d.isOpenDelayed,D,P]),onTriggerLeave:n.useCallback(()=>{m?_():window.clearTimeout(v.current)},[_,m]),onOpen:P,onClose:_,disableHoverableContent:m,children:r})})};L.displayName=j;var P="TooltipTrigger",_=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...l}=e,a=M(P,r),s=T(P,r),c=w(r),f=n.useRef(null),h=(0,i.e)(t,f,a.onTriggerChange),g=n.useRef(!1),x=n.useRef(!1),y=n.useCallback(()=>g.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,p.jsx)(u.ee,{asChild:!0,...c,children:(0,p.jsx)(d.WV.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:h,onPointerMove:(0,o.M)(e.onPointerMove,e=>{"touch"===e.pointerType||x.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),x.current=!0)}),onPointerLeave:(0,o.M)(e.onPointerLeave,()=>{a.onTriggerLeave(),x.current=!1}),onPointerDown:(0,o.M)(e.onPointerDown,()=>{g.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,o.M)(e.onFocus,()=>{g.current||a.onOpen()}),onBlur:(0,o.M)(e.onBlur,a.onClose),onClick:(0,o.M)(e.onClick,a.onClose)})})});_.displayName=P;var[D,O]=v("TooltipPortal",{forceMount:void 0}),N="TooltipContent",V=n.forwardRef((e,t)=>{let r=O(N,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...i}=e,l=M(N,e.__scopeTooltip);return(0,p.jsx)(c.z,{present:n||l.open,children:l.disableHoverableContent?(0,p.jsx)(S,{side:o,...i,ref:t}):(0,p.jsx)(I,{side:o,...i,ref:t})})}),I=n.forwardRef((e,t)=>{let r=M(N,e.__scopeTooltip),o=T(N,e.__scopeTooltip),l=n.useRef(null),a=(0,i.e)(t,l),[s,u]=n.useState(null),{trigger:c,onClose:d}=r,f=l.current,{onPointerInTransitChange:h}=o,g=n.useCallback(()=>{u(null),h(!1)},[h]),x=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(r,n,o,i)){case i:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),h(!0)},[h]);return n.useEffect(()=>()=>g(),[g]),n.useEffect(()=>{if(c&&f){let e=e=>x(e,f),t=e=>x(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,x,g]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==c?void 0:c.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e].x,a=t[e].y,s=t[i].x,u=t[i].y;a>n!=u>n&&r<(s-l)*(n-a)/(u-a)+l&&(o=!o)}return o}(r,s);n?g():o&&(g(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,s,d,g]),(0,p.jsx)(S,{...e,ref:a})}),[W,B]=v(j,{isInside:!1}),S=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:s,...c}=e,d=M(N,r),f=w(r),{onClose:g}=d;return n.useEffect(()=>(document.addEventListener(C,g),()=>document.removeEventListener(C,g)),[g]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&g()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,g]),(0,p.jsx)(a.XB,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:g,children:(0,p.jsxs)(u.VY,{"data-state":d.stateAttribute,...f,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,p.jsx)(h,{children:o}),(0,p.jsx)(W,{scope:r,isInside:!0,children:(0,p.jsx)(y.f,{id:d.contentId,role:"tooltip",children:i||o})})]})})});V.displayName=N;var A="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=w(r);return B(A,r).isInside?null:(0,p.jsx)(u.Eh,{...o,...n,ref:t})}).displayName=A;var F=R,H=L,Y=_,z=V},31725:function(e,t,r){r.d(t,{T:function(){return l},f:function(){return a}});var n=r(2265),o=r(18676),i=r(57437),l=n.forwardRef((e,t)=>(0,i.jsx)(o.WV.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));l.displayName="VisuallyHidden";var a=l}}]);