import React, { useState, useEffect } from 'react';
import { Textarea } from '../ui/textarea';
import { FormControl, FormMessage } from '../ui/form';

interface CoverLetterTextareaProps {
  value?: string;
  onChange: (value: string) => void;
  error?: string;
}

const CoverLetterTextarea: React.FC<CoverLetterTextareaProps> = ({
  value = '',
  onChange,
  error,
}) => {
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);

  useEffect(() => {
    const words = value.trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
    setCharCount(value.length);
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
  };

  const isWordCountValid = wordCount >= 500;
  const isCharCountValid = charCount >= 500;

  return (
    <div className="space-y-2">
      <FormControl>
        <Textarea
          placeholder="Write your cover letter here... (minimum 500 words required)"
          value={value}
          onChange={handleChange}
          className={`min-h-[200px] resize-y ${
            error ? 'border-red-500 focus:border-red-500' : ''
          }`}
          rows={10}
        />
      </FormControl>
      
      <div className="flex justify-between items-center text-sm">
        <div className="flex gap-4">
          <span className={`${isWordCountValid ? 'text-green-600' : 'text-red-500'}`}>
            Words: {wordCount}/500 {isWordCountValid ? '✓' : '✗'}
          </span>
          <span className={`${isCharCountValid ? 'text-green-600' : 'text-red-500'}`}>
            Characters: {charCount}/500 {isCharCountValid ? '✓' : '✗'}
          </span>
        </div>
        
        {!isWordCountValid && wordCount > 0 && (
          <span className="text-red-500 text-xs">
            {500 - wordCount} more words needed
          </span>
        )}
      </div>
      
      {error && <FormMessage>{error}</FormMessage>}
      
      <div className="text-xs text-gray-500">
        <p>Tips for a great cover letter:</p>
        <ul className="list-disc list-inside mt-1 space-y-1">
          <li>Introduce yourself and your relevant experience</li>
          <li>Explain why you're interested in this type of work</li>
          <li>Highlight your key skills and achievements</li>
          <li>Mention specific technologies or tools you're proficient with</li>
          <li>Describe your work style and communication approach</li>
        </ul>
      </div>
    </div>
  );
};

export default CoverLetterTextarea;
