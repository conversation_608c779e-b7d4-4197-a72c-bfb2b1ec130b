"use strict";exports.id=4637,exports.ids=[4637],exports.modules={40900:(e,t,s)=>{s.d(t,{Z:()=>n});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,s(80851).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},40617:(e,t,s)=>{s.d(t,{Z:()=>n});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,s(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},69515:(e,t,s)=>{s.d(t,{Z:()=>n});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,s(80851).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},98091:(e,t,s)=>{s.d(t,{Z:()=>n});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,s(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},42196:(e,t,s)=>{s.d(t,{Z:()=>u});var n=s(10326),r=s(17577),a=s(96633),l=s(941),i=s(69626),c=s(44794),o=s(41190),d=s(91664);let u=({label:e,heading:t,checkboxLabels:s,selectedValues:u,setSelectedValues:m})=>{let[h,p]=r.useState(!1),[x,f]=r.useState(""),N=e=>{u.includes(e)?m(u.filter(t=>t!==e)):m([...u,e])},v=s.filter(e=>e.toLowerCase().includes(x.toLowerCase())),y=v.slice(0,3),j=v.slice(3);return(0,n.jsxs)("div",{children:[n.jsx("h1",{className:"mt-2 text-white",children:t}),(0,n.jsxs)("div",{className:"items-center p-2",children:[n.jsx(o.I,{placeholder:`Search ${e}`,value:x,onChange:e=>f(e.target.value),className:"mb-2 bg-secondary border-black"}),y.map(e=>(0,n.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[n.jsx(i.X,{id:e,checked:u.includes(e),onCheckedChange:()=>N(e)}),n.jsx(c.Label,{htmlFor:e,className:"text-sm",children:e})]},e)),h&&j.map(e=>(0,n.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[n.jsx(i.X,{id:e,checked:u.includes(e),onCheckedChange:()=>N(e)}),n.jsx(c.Label,{htmlFor:e,className:"text-sm",children:e})]},e)),v.length>3&&n.jsx("div",{className:"flex items-center mb-1",children:(0,n.jsxs)(d.z,{size:"sm",variant:"ghost",className:"flex items-center text-sm cursor-pointer ml-auto",onClick:()=>p(!h),children:[h?"Less":"More",h?n.jsx(a.Z,{className:"ml-1 h-4 w-4"}):n.jsx(l.Z,{className:"ml-1 h-4 w-4"})]})}),0===v.length&&n.jsx("p",{className:"text-sm text-gray-500 mt-2",children:"No skills found."})]})]})}},68005:(e,t,s)=>{s.d(t,{Z:()=>h});var n=s(10326),r=s(17577),a=s(96633),l=s(941),i=s(56556),c=s(29752),o=s(91664),d=s(41190),u=s(44794),m=s(69626);let h=({label:e="Skills",heading:t,checkboxLabels:s,selectedValues:h,setSelectedValues:p,openItem:x,setOpenItem:f,useAccordion:N=!1})=>{let[v,y]=(0,r.useState)(""),[j,b]=(0,r.useState)(!1),g=e=>{h.includes(e)?p(h.filter(t=>t!==e)):p([...h,e])},k=s.filter(e=>e.toLowerCase().includes(v.toLowerCase())),w=k.slice(0,3),C=k.slice(3);return N?n.jsx(c.Zb,{className:"w-full",children:n.jsx(i.UQ,{type:"single",collapsible:!0,value:x===t?t:"",onValueChange:e=>f?.(e===t?t:null),children:(0,n.jsxs)(i.Qd,{value:t,children:[n.jsx(i.o4,{className:"text-base px-4 py-2",children:t}),n.jsx(i.vF,{children:(0,n.jsxs)("div",{className:"px-4 mt-2 pb-4 space-y-3",children:[n.jsx(d.I,{type:"text",placeholder:`Search ${t.toLowerCase()}...`,value:v,onChange:e=>y(e.target.value)}),(0,n.jsxs)("div",{className:"max-h-52 overflow-y-auto no-scrollbar space-y-2",children:[k.map(e=>(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[n.jsx(m.X,{id:e,checked:h.includes(e),onCheckedChange:()=>g(e)}),n.jsx(u.Label,{htmlFor:e,className:"text-sm",children:e})]},e)),0===k.length&&n.jsx("p",{className:"text-sm text-muted-foreground",children:"No options found."})]})]})})]})})}):(0,n.jsxs)(c.Zb,{className:"w-full",children:[n.jsx(c.Ol,{children:n.jsx(c.ll,{className:"text-lg",children:t})}),(0,n.jsxs)(c.aY,{children:[n.jsx(d.I,{type:"text",placeholder:`Search ${e}`,value:v,onChange:e=>y(e.target.value),className:"w-full mb-2"}),(0,n.jsxs)(n.Fragment,{children:[w.map(e=>(0,n.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[n.jsx(m.X,{id:e,checked:h.includes(e),onCheckedChange:()=>g(e)}),n.jsx(u.Label,{htmlFor:e,className:"text-sm",children:e})]},e)),j&&C.map(e=>(0,n.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[n.jsx(m.X,{id:e,checked:h.includes(e),onCheckedChange:()=>g(e)}),n.jsx(u.Label,{htmlFor:e,className:"text-sm",children:e})]},e))]})]}),(0,n.jsxs)(c.eW,{children:[k.length>3&&(0,n.jsxs)(o.z,{size:"sm",variant:"ghost",className:"flex items-center text-sm cursor-pointer ml-auto",onClick:()=>b(!j),children:[j?"Less":"More",j?n.jsx(a.Z,{className:"ml-1 h-4 w-4"}):n.jsx(l.Z,{className:"ml-1 h-4 w-4"})]}),0===k.length&&n.jsx("p",{className:"text-sm text-gray-500 mt-2",children:"Skills"===e?"No skills found.":"No domain found."})]})]})}},69626:(e,t,s)=>{s.d(t,{X:()=>L});var n=s(10326),r=s(17577),a=s(48051),l=s(82561),i=s(52067),c=s(53405),o=s(2566),d=s(65819),u=e=>{let{present:t,children:s}=e,n=function(e){var t,s;let[n,a]=r.useState(),l=r.useRef({}),i=r.useRef(e),c=r.useRef("none"),[o,u]=(t=e?"mounted":"unmounted",s={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>s[e][t]??e,t));return r.useEffect(()=>{let e=m(l.current);c.current="mounted"===o?e:"none"},[o]),(0,d.b)(()=>{let t=l.current,s=i.current;if(s!==e){let n=c.current,r=m(t);e?u("MOUNT"):"none"===r||t?.display==="none"?u("UNMOUNT"):s&&n!==r?u("ANIMATION_OUT"):u("UNMOUNT"),i.current=e}},[e,u]),(0,d.b)(()=>{if(n){let e;let t=n.ownerDocument.defaultView??window,s=s=>{let r=m(l.current).includes(s.animationName);if(s.target===n&&r&&(u("ANIMATION_END"),!i.current)){let s=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=s)})}},r=e=>{e.target===n&&(c.current=m(l.current))};return n.addEventListener("animationstart",r),n.addEventListener("animationcancel",s),n.addEventListener("animationend",s),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",r),n.removeEventListener("animationcancel",s),n.removeEventListener("animationend",s)}}u("ANIMATION_END")},[n,u]),{isPresent:["mounted","unmountSuspended"].includes(o),ref:r.useCallback(e=>{e&&(l.current=getComputedStyle(e)),a(e)},[])}}(t),l="function"==typeof s?s({present:n.isPresent}):r.Children.only(s),i=(0,a.e)(n.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,s=t&&"isReactWarning"in t&&t.isReactWarning;return s?e.ref:(s=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof s||n.isPresent?r.cloneElement(l,{ref:i}):null};function m(e){return e?.animationName||"none"}u.displayName="Presence";var h=s(77335),p="Checkbox",[x,f]=function(e,t=[]){let s=[],a=()=>{let t=s.map(e=>r.createContext(e));return function(s){let n=s?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...s,[e]:n}}),[s,n])}};return a.scopeName=e,[function(t,a){let l=r.createContext(a),i=s.length;s=[...s,a];let c=t=>{let{scope:s,children:a,...c}=t,o=s?.[e]?.[i]||l,d=r.useMemo(()=>c,Object.values(c));return(0,n.jsx)(o.Provider,{value:d,children:a})};return c.displayName=t+"Provider",[c,function(s,n){let c=n?.[e]?.[i]||l,o=r.useContext(c);if(o)return o;if(void 0!==a)return a;throw Error(`\`${s}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let s=()=>{let s=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=s.reduce((t,{useScope:s,scopeName:n})=>{let r=s(e)[`__scope${n}`];return{...t,...r}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return s.scopeName=t.scopeName,s}(a,...t)]}(p),[N,v]=x(p),y=r.forwardRef((e,t)=>{let{__scopeCheckbox:s,name:c,checked:o,defaultChecked:d,required:u,disabled:m,value:p="on",onCheckedChange:x,form:f,...v}=e,[y,j]=r.useState(null),b=(0,a.e)(t,e=>j(e)),C=r.useRef(!1),M=!y||f||!!y.closest("form"),[L=!1,E]=(0,i.T)({prop:o,defaultProp:d,onChange:x}),O=r.useRef(L);return r.useEffect(()=>{let e=y?.form;if(e){let t=()=>E(O.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[y,E]),(0,n.jsxs)(N,{scope:s,state:L,disabled:m,children:[(0,n.jsx)(h.WV.button,{type:"button",role:"checkbox","aria-checked":k(L)?"mixed":L,"aria-required":u,"data-state":w(L),"data-disabled":m?"":void 0,disabled:m,value:p,...v,ref:b,onKeyDown:(0,l.M)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.M)(e.onClick,e=>{E(e=>!!k(e)||!e),M&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),M&&(0,n.jsx)(g,{control:y,bubbles:!C.current,name:c,value:p,checked:L,required:u,disabled:m,form:f,style:{transform:"translateX(-100%)"},defaultChecked:!k(d)&&d})]})});y.displayName=p;var j="CheckboxIndicator",b=r.forwardRef((e,t)=>{let{__scopeCheckbox:s,forceMount:r,...a}=e,l=v(j,s);return(0,n.jsx)(u,{present:r||k(l.state)||!0===l.state,children:(0,n.jsx)(h.WV.span,{"data-state":w(l.state),"data-disabled":l.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});b.displayName=j;var g=e=>{let{control:t,checked:s,bubbles:a=!0,defaultChecked:l,...i}=e,d=r.useRef(null),u=(0,c.D)(s),m=(0,o.t)(t);r.useEffect(()=>{let e=d.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(u!==s&&t){let n=new Event("click",{bubbles:a});e.indeterminate=k(s),t.call(e,!k(s)&&s),e.dispatchEvent(n)}},[u,s,a]);let h=r.useRef(!k(s)&&s);return(0,n.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l??h.current,...i,tabIndex:-1,ref:d,style:{...e.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function k(e){return"indeterminate"===e}function w(e){return k(e)?"indeterminate":e?"checked":"unchecked"}var C=s(32933),M=s(51223);let L=r.forwardRef(({className:e,...t},s)=>n.jsx(y,{ref:s,className:(0,M.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:n.jsx(b,{className:(0,M.cn)("flex items-center justify-center text-current"),children:n.jsx(C.Z,{className:"h-4 w-4"})})}));L.displayName=y.displayName},44794:(e,t,s)=>{s.r(t),s.d(t,{Label:()=>o});var n=s(10326),r=s(17577),a=s(34478),l=s(28671),i=s(51223);let c=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef(({className:e,...t},s)=>n.jsx(a.f,{ref:s,className:(0,i.cn)(c(),e),...t}));o.displayName=a.f.displayName},34478:(e,t,s)=>{s.d(t,{f:()=>i});var n=s(17577),r=s(77335),a=s(10326),l=n.forwardRef((e,t)=>(0,a.jsx)(r.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l}};