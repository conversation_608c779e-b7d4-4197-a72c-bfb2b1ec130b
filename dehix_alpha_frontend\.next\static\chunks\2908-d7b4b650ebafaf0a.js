"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2908],{8185:function(e,n,a){var t=a(57437);a(2265);var d=a(67319),i=a(2128),o=a(93363),m=a(77209);n.Z=e=>{let{code:n,setCode:a,control:r}=e,l=d.find(e=>e.code===n)||d[0],s=e=>{a(e)};return(0,t.jsx)(o.Wi,{control:r,name:"phone",render:e=>{let{field:a}=e;return(0,t.jsxs)(o.xJ,{className:"flex-1",children:[(0,t.jsx)("div",{className:"flex flex-col items-start w-full",children:(0,t.jsxs)("div",{className:"flex items-center w-full space-x-2",children:[(0,t.jsxs)(i.Ph,{onValueChange:s,value:n,children:[(0,t.jsx)(i.i4,{className:"w-[75px] border",children:(0,t.jsx)("span",{children:l.dialCode})}),(0,t.jsx)(i.Bw,{children:(0,t.jsx)(i.DI,{children:d.map(e=>(0,t.jsxs)(i.Ql,{value:e.code,children:[e.name," (",e.dialCode,")"]},e.code))})})]}),(0,t.jsx)(o.NI,{children:(0,t.jsx)(m.I,{placeholder:"Enter your phone number",type:"text",...a,className:"w-full"})})]})}),(0,t.jsx)(o.zG,{})]})}})}},65438:function(e,n,a){var t=a(57437),d=a(93363),i=a(77209),o=a(70402);n.Z=e=>{let{control:n,name:a,label:m,placeholder:r="Enter value",type:l="text",description:s="",className:c=""}=e;return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.Label,{children:m}),(0,t.jsx)(d.Wi,{control:n,name:a,render:e=>{var n;let{field:a}=e;return(0,t.jsxs)(d.xJ,{children:[(0,t.jsx)(d.NI,{children:(0,t.jsx)(i.I,{placeholder:r,type:l,...a,className:"p-2 border rounded-md ".concat(c),onChange:e=>{let n=e.target.value;"number"===l?a.onChange(n?parseFloat(n):""):a.onChange(n)},value:"number"===l?null!==(n=a.value)&&void 0!==n?n:"":a.value})}),(0,t.jsx)(d.pf,{children:s}),(0,t.jsx)(d.zG,{})]})}})]})}},93363:function(e,n,a){a.d(n,{NI:function(){return L},Wi:function(){return c},l0:function(){return l},lX:function(){return u},pf:function(){return f},xJ:function(){return x},zG:function(){return p}});var t=a(57437),d=a(2265),i=a(63355),o=a(39343),m=a(49354),r=a(70402);let l=o.RV,s=d.createContext({}),c=e=>{let{...n}=e;return(0,t.jsx)(s.Provider,{value:{name:n.name},children:(0,t.jsx)(o.Qr,{...n})})},h=()=>{let e=d.useContext(s),n=d.useContext(g),{getFieldState:a,formState:t}=(0,o.Gc)(),i=a(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:m}=n;return{id:m,name:e.name,formItemId:"".concat(m,"-form-item"),formDescriptionId:"".concat(m,"-form-item-description"),formMessageId:"".concat(m,"-form-item-message"),...i}},g=d.createContext({}),x=d.forwardRef((e,n)=>{let{className:a,...i}=e,o=d.useId();return(0,t.jsx)(g.Provider,{value:{id:o},children:(0,t.jsx)("div",{ref:n,className:(0,m.cn)("space-y-2",a),...i})})});x.displayName="FormItem";let u=d.forwardRef((e,n)=>{let{className:a,...d}=e,{error:i,formItemId:o}=h();return(0,t.jsx)(r.Label,{ref:n,className:(0,m.cn)(i&&"text-destructive",a),htmlFor:o,...d})});u.displayName="FormLabel";let L=d.forwardRef((e,n)=>{let{...a}=e,{error:d,formItemId:o,formDescriptionId:m,formMessageId:r}=h();return(0,t.jsx)(i.g7,{ref:n,id:o,"aria-describedby":d?"".concat(m," ").concat(r):"".concat(m),"aria-invalid":!!d,...a})});L.displayName="FormControl";let f=d.forwardRef((e,n)=>{let{className:a,...d}=e,{formDescriptionId:i}=h();return(0,t.jsx)("p",{ref:n,id:i,className:(0,m.cn)("text-sm text-muted-foreground",a),...d})});f.displayName="FormDescription";let p=d.forwardRef((e,n)=>{let{className:a,children:d,...i}=e,{error:o,formMessageId:r}=h(),l=o?String(null==o?void 0:o.message):d;return l?(0,t.jsx)("p",{ref:n,id:r,className:(0,m.cn)("text-sm font-medium text-destructive",a),...i,children:l}):null});p.displayName="FormMessage"},2128:function(e,n,a){a.d(n,{Bw:function(){return L},DI:function(){return c},Ph:function(){return s},Ql:function(){return f},i4:function(){return g},ki:function(){return h}});var t=a(57437),d=a(2265),i=a(48362),o=a(42421),m=a(14392),r=a(22468),l=a(49354);let s=i.fC,c=i.ZA,h=i.B4,g=d.forwardRef((e,n)=>{let{className:a,children:d,...m}=e;return(0,t.jsxs)(i.xz,{ref:n,className:(0,l.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...m,children:[d,(0,t.jsx)(i.JO,{asChild:!0,children:(0,t.jsx)(o.Z,{className:"h-4 w-4 opacity-50"})})]})});g.displayName=i.xz.displayName;let x=d.forwardRef((e,n)=>{let{className:a,...d}=e;return(0,t.jsx)(i.u_,{ref:n,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",a),...d,children:(0,t.jsx)(m.Z,{className:"h-4 w-4"})})});x.displayName=i.u_.displayName;let u=d.forwardRef((e,n)=>{let{className:a,...d}=e;return(0,t.jsx)(i.$G,{ref:n,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",a),...d,children:(0,t.jsx)(o.Z,{className:"h-4 w-4"})})});u.displayName=i.$G.displayName;let L=d.forwardRef((e,n)=>{let{className:a,children:d,position:o="popper",...m}=e;return(0,t.jsx)(i.h_,{children:(0,t.jsxs)(i.VY,{ref:n,className:(0,l.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===o&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:o,...m,children:[(0,t.jsx)(x,{}),(0,t.jsx)(i.l_,{className:(0,l.cn)("p-1","popper"===o&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:d}),(0,t.jsx)(u,{})]})})});L.displayName=i.VY.displayName,d.forwardRef((e,n)=>{let{className:a,...d}=e;return(0,t.jsx)(i.__,{ref:n,className:(0,l.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...d})}).displayName=i.__.displayName;let f=d.forwardRef((e,n)=>{let{className:a,children:d,...o}=e;return(0,t.jsxs)(i.ck,{ref:n,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...o,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(i.wU,{children:(0,t.jsx)(r.Z,{className:"h-4 w-4"})})}),(0,t.jsx)(i.eT,{children:d})]})});f.displayName=i.ck.displayName,d.forwardRef((e,n)=>{let{className:a,...d}=e;return(0,t.jsx)(i.Z0,{ref:n,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",a),...d})}).displayName=i.Z0.displayName},67319:function(e){e.exports=JSON.parse('[{"code":"AF","name":"Afghanistan","dialCode":"+93","minLength":9,"maxLength":10},{"code":"AR","name":"Argentina","dialCode":"+54","minLength":10,"maxLength":10},{"code":"AU","name":"Australia","dialCode":"+61","minLength":9,"maxLength":9},{"code":"AT","name":"Austria","dialCode":"+43","minLength":10,"maxLength":10},{"code":"BD","name":"Bangladesh","dialCode":"+880","minLength":10,"maxLength":10},{"code":"BE","name":"Belgium","dialCode":"+32","minLength":9,"maxLength":9},{"code":"BR","name":"Brazil","dialCode":"+55","minLength":10,"maxLength":11},{"code":"BN","name":"Brunei","dialCode":"+673","minLength":7,"maxLength":7},{"code":"BT","name":"Bhutan","dialCode":"+975","minLength":9,"maxLength":10},{"code":"KH","name":"Cambodia","dialCode":"+855","minLength":9,"maxLength":10},{"code":"CA","name":"Canada","dialCode":"+1","minLength":10,"maxLength":10},{"code":"CN","name":"China","dialCode":"+86","minLength":11,"maxLength":11},{"code":"DK","name":"Denmark","dialCode":"+45","minLength":8,"maxLength":8},{"code":"EG","name":"Egypt","dialCode":"+20","minLength":9,"maxLength":10},{"code":"FI","name":"Finland","dialCode":"+358","minLength":10,"maxLength":10},{"code":"FR","name":"France","dialCode":"+33","minLength":9,"maxLength":9},{"code":"DE","name":"Germany","dialCode":"+49","minLength":10,"maxLength":11},{"code":"GR","name":"Greece","dialCode":"+30","minLength":10,"maxLength":10},{"code":"GH","name":"Ghana","dialCode":"+233","minLength":9,"maxLength":10},{"code":"HK","name":"Hong Kong","dialCode":"+852","minLength":8,"maxLength":8},{"code":"IN","name":"India","dialCode":"+91","minLength":10,"maxLength":10},{"code":"ID","name":"Indonesia","dialCode":"+62","minLength":10,"maxLength":12},{"code":"IR","name":"Iran","dialCode":"+98","minLength":10,"maxLength":10},{"code":"IQ","name":"Iraq","dialCode":"+964","minLength":9,"maxLength":10},{"code":"IE","name":"Ireland","dialCode":"+353","minLength":9,"maxLength":9},{"code":"IL","name":"Israel","dialCode":"+972","minLength":9,"maxLength":10},{"code":"IT","name":"Italy","dialCode":"+39","minLength":9,"maxLength":10},{"code":"JP","name":"Japan","dialCode":"+81","minLength":10,"maxLength":10},{"code":"KE","name":"Kenya","dialCode":"+254","minLength":9,"maxLength":10},{"code":"KR","name":"South Korea","dialCode":"+82","minLength":10,"maxLength":11},{"code":"LA","name":"Laos","dialCode":"+856","minLength":9,"maxLength":10},{"code":"MY","name":"Malaysia","dialCode":"+60","minLength":9,"maxLength":10},{"code":"MV","name":"Maldives","dialCode":"+960","minLength":7,"maxLength":7},{"code":"MX","name":"Mexico","dialCode":"+52","minLength":10,"maxLength":10},{"code":"MN","name":"Mongolia","dialCode":"+976","minLength":9,"maxLength":10},{"code":"MM","name":"Myanmar","dialCode":"+95","minLength":9,"maxLength":10},{"code":"NP","name":"Nepal","dialCode":"+977","minLength":10,"maxLength":10},{"code":"NL","name":"Netherlands","dialCode":"+31","minLength":9,"maxLength":9},{"code":"NZ","name":"New Zealand","dialCode":"+64","minLength":9,"maxLength":10},{"code":"NG","name":"Nigeria","dialCode":"+234","minLength":10,"maxLength":10},{"code":"NO","name":"Norway","dialCode":"+47","minLength":8,"maxLength":8},{"code":"PK","name":"Pakistan","dialCode":"+92","minLength":10,"maxLength":11},{"code":"PH","name":"Philippines","dialCode":"+63","minLength":10,"maxLength":10},{"code":"PL","name":"Poland","dialCode":"+48","minLength":9,"maxLength":9},{"code":"PT","name":"Portugal","dialCode":"+351","minLength":9,"maxLength":9},{"code":"RU","name":"Russia","dialCode":"+7","minLength":10,"maxLength":10},{"code":"SA","name":"Saudi Arabia","dialCode":"+966","minLength":9,"maxLength":9},{"code":"SG","name":"Singapore","dialCode":"+65","minLength":8,"maxLength":8},{"code":"ZA","name":"South Africa","dialCode":"+27","minLength":9,"maxLength":9},{"code":"ES","name":"Spain","dialCode":"+34","minLength":9,"maxLength":9},{"code":"SE","name":"Sweden","dialCode":"+46","minLength":9,"maxLength":9},{"code":"CH","name":"Switzerland","dialCode":"+41","minLength":9,"maxLength":9},{"code":"TZ","name":"Tanzania","dialCode":"+255","minLength":9,"maxLength":10},{"code":"TH","name":"Thailand","dialCode":"+66","minLength":9,"maxLength":10},{"code":"TR","name":"Turkey","dialCode":"+90","minLength":10,"maxLength":11},{"code":"UG","name":"Uganda","dialCode":"+256","minLength":9,"maxLength":10},{"code":"UA","name":"Ukraine","dialCode":"+380","minLength":9,"maxLength":9},{"code":"AE","name":"United Arab Emirates","dialCode":"+971","minLength":9,"maxLength":9},{"code":"GB","name":"United Kingdom","dialCode":"+44","minLength":10,"maxLength":11},{"code":"US","name":"United States","dialCode":"+1","minLength":10,"maxLength":10},{"code":"VN","name":"Vietnam","dialCode":"+84","minLength":9,"maxLength":10}]')}}]);