(()=>{var e={};e.id=8475,e.ids=[8475],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},37113:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),t(63269),t(54302),t(12523);var r=t(23191),i=t(88716),a=t(37922),n=t.n(a),l=t(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c=["",{children:["business",{children:["settings",{children:["business-info",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,63269)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\settings\\business-info\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\settings\\business-info\\page.tsx"],u="/business/settings/business-info/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/business/settings/business-info/page",pathname:"/business/settings/business-info",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},85804:(e,s,t)=>{Promise.resolve().then(t.bind(t,4945))},4945:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var r=t(10326),i=t(25842),a=t(92166),n=t(73176),l=t(17577),o=t(74064),c=t(74723),d=t(27256),u=t(82287),m=t(44794),p=t(29752),x=t(56627),h=t(6260),f=t(91664),j=t(9969),g=t(41190),N=t(58285),b=t(78062),v=t(4594);let I=d.z.object({firstName:d.z.string().min(2,{message:"First Name must be at least 2 characters."}),lastName:d.z.string().min(2,{message:"Last Name must be at least 2 characters."}),email:d.z.string().email({message:"Email must be a valid email address."}),phone:d.z.string().min(10,{message:"Phone number must be at least 10 digits."}),companyName:d.z.string().optional(),companySize:d.z.string().optional(),position:d.z.string().optional(),linkedin:d.z.string().url({message:"Must be a valid URL."}).refine(e=>e.includes("linkedin.com/in/")||e.includes("linkedin.com/company/"),{message:"LinkedIn URL must start with: https://www.linkedin.com/in/"}).optional(),website:d.z.string().url({message:"Must be a valid URL."}).optional()});function y({user_id:e}){let[s,t]=(0,l.useState)({}),[a,n]=(0,l.useState)(!1),d=(0,i.I0)(),y=(0,c.cI)({resolver:(0,o.F)(I),defaultValues:{firstName:"",lastName:"",email:"",phone:"",companyName:"",companySize:"",position:"",linkedin:"",website:""},mode:"all"});async function E(e){n(!0);try{let r=await h.b.put("/business",{...e});if(t({...s,firstName:e.firstName,lastName:e.lastName,email:e.email,phone:e.phone,companyName:e.companyName,companySize:e.companySize,position:e.position,linkedin:e.linkedin,personalWebsite:e.website}),200===r.status){let r={...s,...e};d((0,v.av)(r)),t(r),(0,x.Am)({title:"Profile Updated",description:"Your profile has been successfully updated."})}else console.error("Unexpected status code:",r.status),(0,x.Am)({variant:"destructive",title:"Error",description:"Failed to update profile. Unexpected server response."})}catch(e){console.error("API Error:",e),(0,x.Am)({variant:"destructive",title:"Error",description:"Failed to update profile. Please try again later."})}finally{n(!1)}}return r.jsx(p.Zb,{className:"p-10",children:(0,r.jsxs)(j.l0,{...y,children:[r.jsx(u.Z,{profile:s.profilePic,entityType:N.Dy.BUSINESS}),(0,r.jsxs)("form",{onSubmit:y.handleSubmit(E),className:"space-y-6 mt-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(m.Label,{children:"First Name"}),r.jsx(j.Wi,{control:y.control,name:"firstName",render:({field:e})=>(0,r.jsxs)(j.xJ,{children:[r.jsx(j.NI,{children:r.jsx(g.I,{placeholder:"Enter your first name",...e})}),r.jsx(j.zG,{})]})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(m.Label,{children:"Last Name"}),r.jsx(j.Wi,{control:y.control,name:"lastName",render:({field:e})=>(0,r.jsxs)(j.xJ,{children:[r.jsx(j.NI,{children:r.jsx(g.I,{placeholder:"Enter your last name",...e})}),r.jsx(j.zG,{})]})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(m.Label,{children:"Email"}),r.jsx(j.Wi,{control:y.control,name:"email",render:({field:e})=>(0,r.jsxs)(j.xJ,{children:[r.jsx(j.NI,{children:r.jsx(g.I,{disabled:!0,placeholder:"Enter your email",type:"email",...e,readOnly:!0})}),r.jsx(j.zG,{}),r.jsx(j.pf,{children:"Non editable field"})]})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(m.Label,{children:"Phone"}),r.jsx(j.Wi,{control:y.control,name:"phone",render:({field:e})=>(0,r.jsxs)(j.xJ,{children:[r.jsx(j.NI,{children:r.jsx(g.I,{disabled:!0,placeholder:"Enter your phone number",type:"tel",...e,readOnly:!0})}),r.jsx(j.zG,{}),r.jsx(j.pf,{children:"Non editable field"})]})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(m.Label,{children:"Company Name"}),r.jsx(j.Wi,{control:y.control,name:"companyName",render:({field:e})=>(0,r.jsxs)(j.xJ,{children:[r.jsx(j.NI,{children:r.jsx(g.I,{placeholder:"Enter your company name",...e})}),r.jsx(j.zG,{})]})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(m.Label,{children:"Company Size"}),r.jsx(j.Wi,{control:y.control,name:"companySize",render:({field:e})=>(0,r.jsxs)(j.xJ,{children:[r.jsx(j.NI,{children:r.jsx(g.I,{placeholder:"Enter your company size",...e})}),r.jsx(j.zG,{})]})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(m.Label,{children:"Position"}),r.jsx(j.Wi,{control:y.control,name:"position",render:({field:e})=>(0,r.jsxs)(j.xJ,{children:[r.jsx(j.NI,{children:r.jsx(g.I,{placeholder:"Enter your position",...e})}),r.jsx(j.zG,{})]})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(m.Label,{children:"LinkedIn URL"}),r.jsx(j.Wi,{control:y.control,name:"linkedin",render:({field:e})=>(0,r.jsxs)(j.xJ,{children:[r.jsx(j.NI,{children:r.jsx(g.I,{placeholder:"Enter your LinkedIn URL",type:"url",...e})}),r.jsx(j.zG,{})]})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(m.Label,{children:"Website URL"}),r.jsx(j.Wi,{control:y.control,name:"website",render:({field:e})=>(0,r.jsxs)(j.xJ,{children:[r.jsx(j.NI,{children:r.jsx(g.I,{placeholder:"Enter your website URL",type:"url",...e})}),r.jsx(j.zG,{})]})})]})]}),r.jsx(b.Separator,{className:"col-span-2"}),r.jsx(f.z,{className:"w-full",type:"submit",disabled:a,children:a?"Loading...":"Save changes"})]})]})})}var E=t(40588);function P(){let e=(0,i.v9)(e=>e.user);return(0,r.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[r.jsx(a.Z,{menuItemsTop:n.y,menuItemsBottom:n.$,active:"Business Info",isKycCheck:!0}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 mb-8 sm:pl-14",children:[r.jsx(E.Z,{menuItemsTop:n.y,menuItemsBottom:n.$,activeMenu:"Personal Info",breadcrumbItems:[{label:"Business",link:"/dashboard/business"},{label:"Settings",link:"#"},{label:"Business Info",link:"#"}]}),r.jsx("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8",children:r.jsx(y,{user_id:e.uid})})]})]})}},82287:(e,s,t)=>{"use strict";t.d(s,{Z:()=>f});var r=t(10326),i=t(17577);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var n=t(83855),l=t(77506),o=t(46226),c=t(25842),d=t(56627),u=t(91664),m=t(6260),p=t(4594),x=t(58285);let h=["image/png","image/jpeg","image/jpg","image/gif","image/svg+xml"],f=({profile:e,entityType:s})=>{let t=(0,c.v9)(e=>e.user),f=(0,c.I0)(),[j,g]=(0,i.useState)(null),[N,b]=(0,i.useState)(e),[v,I]=(0,i.useState)(!1),y=(0,i.useRef)(null),E=async e=>{if(e.preventDefault(),!j){(0,d.Am)({variant:"destructive",title:"No Image Selected",description:"Please select an image before submitting."});return}I(!0);let r=new FormData;r.append("profilePicture",j);try{let{Location:e}=(await m.b.post("/register/upload-image",r,{headers:{"Content-Type":"multipart/form-data"}})).data.data;f((0,p.av)({...t,photoURL:e}));let i=s===x.Dy.FREELANCER?"/freelancer":"/business",a=await m.b.put(i,{profilePic:e});if(200===a.status)(0,d.Am)({title:"Success",description:"Profile picture uploaded successfully!"});else throw Error("Failed to update profile picture")}catch(e){console.error("Error during upload:",e),(0,d.Am)({variant:"destructive",title:"Upload failed",description:"Image upload failed. Please try again."})}finally{I(!1)}};return r.jsx("div",{className:"upload-form max-w-md mx-auto rounded shadow-md",children:(0,r.jsxs)("form",{onSubmit:E,className:"space-y-6",children:[r.jsx("input",{type:"file",accept:h.join(","),onChange:e=>{let s=e.target.files?.[0];s&&h.includes(s.type)?s.size<=1048576?(g(s),b(URL.createObjectURL(s))):(0,d.Am)({variant:"destructive",title:"File too large",description:"Image size should not exceed 1MB."}):(0,d.Am)({variant:"destructive",title:"Invalid file type",description:`Please upload a valid image file. Allowed formats: ${h.join(", ")}`})},className:"hidden",ref:y}),r.jsx("div",{className:"relative flex flex-col items-center",children:(0,r.jsxs)("label",{htmlFor:"file-input",className:"cursor-pointer relative",children:[N?r.jsx(o.default,{width:28,height:28,src:N,alt:"Avatar Preview",className:"w-28 h-28 rounded-full object-cover border-2 border-black-300"}):r.jsx("div",{className:"w-28 h-28 rounded-full bg-gray-700 flex items-center justify-center",children:r.jsx(o.default,{width:112,height:112,src:e,alt:"Avatar Preview",className:"w-28 h-28 rounded-full object-cover border-2 border-black-300"})}),r.jsx(u.z,{variant:"outline",type:"button",size:"icon",className:"absolute bottom-0 right-0 w-10 h-10 rounded-full bg-black border border-gray-300 flex items-center justify-center shadow-md",onClick:()=>{N?b(null):y.current?.click()},children:N?r.jsx(a,{className:"h-4 w-4 text-gray-400"}):r.jsx(n.Z,{className:"h-4 w-4 text-gray-400"})})]})}),N&&r.jsx(u.z,{type:"submit",className:"w-full",disabled:!j||v,children:v?(0,r.jsxs)(r.Fragment,{children:[r.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Please wait"]}):"Upload Profile Picture"})]})})}},9969:(e,s,t)=>{"use strict";t.d(s,{NI:()=>f,Wi:()=>u,l0:()=>c,lX:()=>h,pf:()=>j,xJ:()=>x,zG:()=>g});var r=t(10326),i=t(17577),a=t(99469),n=t(74723),l=t(51223),o=t(44794);let c=n.RV,d=i.createContext({}),u=({...e})=>r.jsx(d.Provider,{value:{name:e.name},children:r.jsx(n.Qr,{...e})}),m=()=>{let e=i.useContext(d),s=i.useContext(p),{getFieldState:t,formState:r}=(0,n.Gc)(),a=t(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=s;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...a}},p=i.createContext({}),x=i.forwardRef(({className:e,...s},t)=>{let a=i.useId();return r.jsx(p.Provider,{value:{id:a},children:r.jsx("div",{ref:t,className:(0,l.cn)("space-y-2",e),...s})})});x.displayName="FormItem";let h=i.forwardRef(({className:e,...s},t)=>{let{error:i,formItemId:a}=m();return r.jsx(o.Label,{ref:t,className:(0,l.cn)(i&&"text-destructive",e),htmlFor:a,...s})});h.displayName="FormLabel";let f=i.forwardRef(({...e},s)=>{let{error:t,formItemId:i,formDescriptionId:n,formMessageId:l}=m();return r.jsx(a.g7,{ref:s,id:i,"aria-describedby":t?`${n} ${l}`:`${n}`,"aria-invalid":!!t,...e})});f.displayName="FormControl";let j=i.forwardRef(({className:e,...s},t)=>{let{formDescriptionId:i}=m();return r.jsx("p",{ref:t,id:i,className:(0,l.cn)("text-sm text-muted-foreground",e),...s})});j.displayName="FormDescription";let g=i.forwardRef(({className:e,children:s,...t},i)=>{let{error:a,formMessageId:n}=m(),o=a?String(a?.message):s;return o?r.jsx("p",{ref:i,id:n,className:(0,l.cn)("text-sm font-medium text-destructive",e),...t,children:o}):null});g.displayName="FormMessage"},44794:(e,s,t)=>{"use strict";t.r(s),t.d(s,{Label:()=>c});var r=t(10326),i=t(17577),a=t(34478),n=t(28671),l=t(51223);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=i.forwardRef(({className:e,...s},t)=>r.jsx(a.f,{ref:t,className:(0,l.cn)(o(),e),...s}));c.displayName=a.f.displayName},78062:(e,s,t)=>{"use strict";t.r(s),t.d(s,{Separator:()=>l});var r=t(10326),i=t(17577),a=t(90220),n=t(51223);let l=i.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...i},l)=>r.jsx(a.f,{ref:l,decorative:t,orientation:s,className:(0,n.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...i}));l.displayName=a.f.displayName},73176:(e,s,t)=>{"use strict";t.d(s,{$:()=>o,y:()=>l});var r=t(10326),i=t(95920),a=t(79635),n=t(46226);let l=[{href:"#",icon:r.jsx(n.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:r.jsx(i.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/business/settings/business-info",icon:r.jsx(a.Z,{className:"h-5 w-5"}),label:"Business Info"}],o=[]},58285:(e,s,t)=>{"use strict";var r,i,a,n,l,o,c,d,u,m,p;t.d(s,{Dy:()=>m,Dz:()=>x});let x={BATCH:3};(function(e){e.PROJECT_HIRING="PROJECT_HIRING",e.SKILL_INTERVIEW="SKILL_INTERVIEW",e.DOMAIN_INTERVIEW="DOMAIN_INTERVIEW",e.TALENT_INTERVIEW="TALENT_INTERVIEW"})(r||(r={})),function(e){e.ADDED="Added",e.APPROVED="Approved",e.CLOSED="Closed",e.COMPLETED="Completed"}(i||(i={})),function(e){e.ACTIVE="Active",e.IN_ACTIVE="Inactive",e.NOT_VERIFIED="Not Verified"}(a||(a={})),function(e){e.BUSINESS="Business",e.FREELANCER="Freelancer",e.BOTH="Both"}(n||(n={})),function(e){e.ACTIVE="Active",e.IN_ACTIVE="Inactive"}(l||(l={})),function(e){e.APPLIED="APPLIED",e.NOT_APPLIED="NOT_APPLIED",e.APPROVED="APPROVED",e.FAILED="FAILED",e.STOPPED="STOPPED",e.REAPPLIED="REAPPLIED"}(o||(o={})),function(e){e.PENDING="Pending",e.ACCEPTED="Accepted",e.REJECTED="Rejected",e.PANEL="Panel",e.INTERVIEW="Interview"}(c||(c={})),function(e){e.ACTIVE="ACTIVE",e.INACTIVE="INACTIVE",e.ARCHIVED="ARCHIVED"}(d||(d={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.INACTIVE="Inactive",e.CLOSED="Closed"}(u||(u={})),function(e){e.FREELANCER="FREELANCER",e.ADMIN="ADMIN",e.BUSINESS="BUSINESS"}(m||(m={})),function(e){e.CREATED="Created",e.CLOSED="Closed",e.ACTIVE="Active"}(p||(p={}))},63269:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>a,default:()=>l});var r=t(68570);let i=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\business\settings\business-info\page.tsx`),{__esModule:a,$$typeof:n}=i;i.default;let l=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\business\settings\business-info\page.tsx#default`)},34478:(e,s,t)=>{"use strict";t.d(s,{f:()=>l});var r=t(17577),i=t(77335),a=t(10326),n=r.forwardRef((e,s)=>(0,a.jsx)(i.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var l=n},90220:(e,s,t)=>{"use strict";t.d(s,{f:()=>c});var r=t(17577),i=t(77335),a=t(10326),n="horizontal",l=["horizontal","vertical"],o=r.forwardRef((e,s)=>{let{decorative:t,orientation:r=n,...o}=e,c=l.includes(r)?r:n;return(0,a.jsx)(i.WV.div,{"data-orientation":c,...t?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:s})});o.displayName="Separator";var c=o}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,6686,4736,6499,8066,588],()=>t(37113));module.exports=r})();