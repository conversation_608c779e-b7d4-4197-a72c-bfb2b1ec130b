(()=>{var e={};e.id=381,e.ids=[381],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},21709:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>c}),r(55939),r(54302),r(12523);var a=r(23191),t=r(88716),l=r(37922),i=r.n(l),d=r(95231),n={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);r.d(s,n);let c=["",{children:["business",{children:["freelancerProfile",{children:["[freelancer_id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,55939)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\freelancerProfile\\[freelancer_id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\freelancerProfile\\[freelancer_id]\\page.tsx"],m="/business/freelancerProfile/[freelancer_id]/page",x={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/business/freelancerProfile/[freelancer_id]/page",pathname:"/business/freelancerProfile/[freelancer_id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},39941:(e,s,r)=>{Promise.resolve().then(r.bind(r,8121))},40900:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},12070:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},6343:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},47546:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},66307:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69669:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},92498:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},40617:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},57671:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},69515:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},98091:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},8121:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>k});var a=r(10326),t=r(17577),l=r(35047),i=r(94019),d=r(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,d.Z)("CircleUser",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}],["path",{d:"M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662",key:"154egf"}]]);var c=r(92498);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,d.Z)("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]]);var m=r(6343),x=r(47546);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let h=(0,d.Z)("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]);var u=r(46226),p=r(98181),b=r(56627),f=r(6260),j=r(46319),g=r(92166),N=r(40588),y=r(78062),v=r(29752);let k=()=>{let{freelancer_id:e}=(0,l.useParams)(),[s,r]=(0,t.useState)(null),[d,k]=(0,t.useState)(!0),[w,Z]=(0,t.useState)(null);(0,t.useEffect)(()=>{e&&(async()=>{try{k(!0);let s=await f.b.get(`/freelancer/${e}/profile-info`);200===s.status&&r(s.data)}catch(e){console.error("Error fetching freelancer details",e),(0,b.Am)({variant:"destructive",title:"Error",description:"Failed to fetch freelancer details."})}finally{k(!1)}})()},[e]);let _=e=>{if(!e)return"";try{return(0,p.WU)(new Date(e),"MMM yyyy")}catch(s){return e}};return d?a.jsx("div",{className:"flex min-h-screen w-full flex-col bg-background",children:a.jsx("div",{className:"flex justify-center items-center h-screen",children:(0,a.jsxs)("div",{className:"animate-pulse space-y-4",children:[a.jsx("div",{className:"h-12 w-48 bg-muted rounded"}),a.jsx("div",{className:"h-64 w-full max-w-2xl bg-muted rounded"}),a.jsx("div",{className:"h-32 w-full max-w-2xl bg-muted rounded"})]})})}):(0,a.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-background",children:[a.jsx(g.Z,{menuItemsTop:j.yn,menuItemsBottom:j.$C,active:""}),(0,a.jsxs)("div",{className:"flex flex-col sm:gap-4 mb-8 sm:pl-14",children:[a.jsx(N.Z,{menuItemsTop:j.yn,menuItemsBottom:j.$C,activeMenu:"Projects",breadcrumbItems:[{label:"Business",link:"/dashboard/business"},{label:"Business Marketplace",link:"/business/market"},{label:"Freelancer Profile",link:"/business/market"},{label:`${s?.firstName||""} ${s?.lastName||""}`,link:`/dashboard/business/${e}`}]}),a.jsx("div",{className:"flex p-3 px-3 md:px-14 relative flex-col sm:gap-8 sm:py-0",children:(0,a.jsxs)("main",{className:"mt-8 max-w-4xl mx-auto",children:[a.jsx(v.Zb,{className:"mb-8 shadow-md",children:a.jsx(v.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-6",children:[a.jsx("div",{className:"relative h-24 w-24 rounded-full overflow-hidden bg-muted border-2 border-border shadow-md",children:s?.profilePic?a.jsx(u.default,{src:s.profilePic,alt:"Profile",fill:!0,className:"object-cover"}):a.jsx("div",{className:"h-full w-full flex items-center justify-center bg-primary/10",children:a.jsx(n,{className:"h-12 w-12 text-primary/60"})})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-2xl font-bold text-foreground",children:[s?.firstName," ",s?.lastName]}),a.jsx("p",{className:"text-muted-foreground mt-1",children:s?.description||"No description available"})]})]})})}),(0,a.jsxs)(v.Zb,{className:"mb-6 overflow-hidden border border-border shadow-md",children:[a.jsx(v.Ol,{className:"bg-primary/5 border-b border-border py-4",children:(0,a.jsxs)(v.ll,{className:"text-md font-semibold text-primary flex items-center gap-2",children:[a.jsx(c.Z,{className:"h-5 w-5"}),"Skills"]})}),a.jsx(v.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[s?.skills?.map(e=>a.jsx("div",{className:"flex items-center gap-1 px-3 py-1 bg-muted/50 border border-border rounded-full shadow-sm",children:a.jsx("span",{children:e.name})},e._id)),!s?.skills?.length&&a.jsx("p",{className:"text-muted-foreground italic",children:"No skills added"})]})})]}),(0,a.jsxs)(v.Zb,{className:"mb-6 overflow-hidden border border-border shadow-md",children:[a.jsx(v.Ol,{className:"bg-indigo-500/5 dark:bg-indigo-500/10 border-b border-border py-4",children:(0,a.jsxs)(v.ll,{className:"text-md font-semibold text-indigo-600 dark:text-indigo-400 flex items-center gap-2",children:[a.jsx(o,{className:"h-5 w-5"}),"Domain"]})}),a.jsx(v.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-1",children:[s?.domain?.map(e=>a.jsx("div",{className:"py-2 px-3 bg-muted/50 border border-border rounded-md mb-2 shadow-sm",children:e.name},e._id)),!s?.domain?.length&&a.jsx("p",{className:"text-muted-foreground italic",children:"No domains added"})]})})]}),(0,a.jsxs)(v.Zb,{className:"mb-6 overflow-hidden border border-border shadow-md",children:[a.jsx(v.Ol,{className:"bg-purple-500/5 dark:bg-purple-500/10 border-b border-border py-4",children:(0,a.jsxs)(v.ll,{className:"text-md font-semibold text-purple-600 dark:text-purple-400 flex items-center gap-2",children:[a.jsx(m.Z,{className:"h-5 w-5"}),"Project Domain"]})}),a.jsx(v.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-1",children:[s?.projectDomain?.map(e=>a.jsx("div",{className:"py-2 px-3 bg-muted/50 border border-border rounded-md mb-2 shadow-sm",children:e.name},e._id)),!s?.projectDomain?.length&&a.jsx("p",{className:"text-muted-foreground italic",children:"No project domains added"})]})})]}),(0,a.jsxs)(v.Zb,{className:"mb-6 overflow-hidden border border-border shadow-md",children:[a.jsx(v.Ol,{className:"bg-green-500/5 dark:bg-green-500/10 border-b border-border py-4",children:(0,a.jsxs)(v.ll,{className:"text-md font-semibold text-green-600 dark:text-green-400 flex items-center gap-2",children:[a.jsx(c.Z,{className:"h-5 w-5"}),"Projects"]})}),a.jsx(v.aY,{className:"p-4",children:a.jsx("div",{className:"space-y-6",children:s&&s.projects&&s.projects.length>0?s.projects.slice(0,3).map(e=>a.jsx("div",{className:"border-b border-border pb-4 last:border-b-0",children:a.jsx("div",{className:"flex justify-between items-start",children:a.jsx("h3",{className:"font-medium text-foreground hover:text-primary cursor-pointer",onClick:()=>Z(e),children:e.projectName})})},e._id)):a.jsx("p",{className:"text-muted-foreground italic",children:"No projects added"})})})]}),(0,a.jsxs)(v.Zb,{className:"mb-6 overflow-hidden border border-border shadow-md",children:[a.jsx(v.Ol,{className:"bg-amber-500/5 dark:bg-amber-500/10 border-b border-border py-4",children:(0,a.jsxs)(v.ll,{className:"text-md font-semibold text-amber-600 dark:text-amber-400 flex items-center gap-2",children:[a.jsx(x.Z,{className:"h-5 w-5"}),"Professional Experience"]})}),a.jsx(v.aY,{className:"p-4",children:a.jsx("div",{className:"space-y-4",children:s&&s.professionalInfo&&s.professionalInfo.length>0?s.professionalInfo.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex gap-4 p-3 bg-muted/50 border border-border rounded-md shadow-sm",children:[a.jsx("div",{className:"w-12 h-12 bg-amber-100 dark:bg-amber-950/30 rounded-md flex items-center justify-center flex-shrink-0",children:a.jsx(x.Z,{className:"h-6 w-6 text-amber-600 dark:text-amber-400"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium text-foreground",children:e.jobTitle}),a.jsx("p",{className:"text-sm text-muted-foreground",children:e.company}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:[_(e.workFrom)," -"," ",_(e.workTo)]})]})]},e._id)):a.jsx("p",{className:"text-muted-foreground italic",children:"No professional experience added"})})})]}),a.jsx(y.Separator,{className:"h-px bg-border my-6"}),(0,a.jsxs)(v.Zb,{className:"mb-6 overflow-hidden border border-border shadow-md",children:[a.jsx(v.Ol,{className:"bg-cyan-500/5 dark:bg-cyan-500/10 border-b border-border py-4",children:(0,a.jsxs)(v.ll,{className:"text-md font-semibold text-cyan-600 dark:text-cyan-400 flex items-center gap-2",children:[a.jsx(h,{className:"h-5 w-5"}),"Education"]})}),a.jsx(v.aY,{className:"p-4",children:a.jsx("div",{className:"space-y-4",children:s?.education?.length?s.education.slice(0,3).map(e=>(0,a.jsxs)("div",{className:"flex justify-between p-3 bg-muted/50 border border-border rounded-md shadow-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-foreground",children:e.degree}),a.jsx("p",{className:"text-sm text-muted-foreground",children:e.fieldOfStudy}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["Grade: ",e.grade]})]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground bg-cyan-500/5 dark:bg-cyan-500/10 px-3 py-1 rounded-md h-fit",children:[_(e.startDate)," -"," ",_(e.endDate)]})]},e._id)):a.jsx("p",{className:"text-muted-foreground italic",children:"No education details added"})})})]})]})}),w&&a.jsx(({project:e,onClose:s})=>a.jsx("div",{className:"fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-card text-card-foreground p-6 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-lg border",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[a.jsx("h2",{className:"text-xl font-bold",children:e.projectName}),a.jsx("button",{onClick:s,className:"p-1 hover:bg-muted rounded-full",children:a.jsx(i.Z,{size:24})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold",children:"Description"}),a.jsx("p",{className:"text-muted-foreground",children:e.description})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold",children:"Role"}),a.jsx("p",{className:"text-muted-foreground",children:e.role})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold",children:"Duration"}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:[_(e.start)," - ",_(e.end)]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold",children:"Technologies Used"}),a.jsx("div",{className:"flex flex-wrap gap-2 mt-1",children:e.techUsed?.map((e,s)=>a.jsx("span",{className:"px-2 py-1 bg-muted rounded-full text-sm",children:e},s))})]}),e.githubLink&&(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold",children:"GitHub"}),a.jsx("a",{href:e.githubLink,target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:e.githubLink})]}),e.projectType&&(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold",children:"Project Type"}),a.jsx("p",{className:"text-muted-foreground",children:e.projectType})]})]})]})}),{project:w,onClose:()=>Z(null)})]})]})}},78062:(e,s,r)=>{"use strict";r.r(s),r.d(s,{Separator:()=>d});var a=r(10326),t=r(17577),l=r(90220),i=r(51223);let d=t.forwardRef(({className:e,orientation:s="horizontal",decorative:r=!0,...t},d)=>a.jsx(l.f,{ref:d,decorative:r,orientation:s,className:(0,i.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...t}));d.displayName=l.f.displayName},46319:(e,s,r)=>{"use strict";r.d(s,{$C:()=>f,Ne:()=>j,yn:()=>b});var a=r(10326),t=r(95920),l=r(57671),i=r(94909),d=r(12070),n=r(66307),c=r(69669),o=r(40617),m=r(69515),x=r(88378),h=r(40900),u=r(98091),p=r(46226);let b=[{href:"#",icon:a.jsx(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:a.jsx(t.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:a.jsx(l.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:a.jsx(i.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:a.jsx(i.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:a.jsx(d.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:a.jsx(n.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:a.jsx(c.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:a.jsx(o.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:a.jsx(m.Z,{className:"h-5 w-5"}),label:"Notes"}],f=[{href:"/business/settings/business-info",icon:a.jsx(x.Z,{className:"h-5 w-5"}),label:"Settings"}],j=[{href:"#",icon:a.jsx(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:a.jsx(t.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:a.jsx(m.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:a.jsx(h.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:a.jsx(u.Z,{className:"h-5 w-5"}),label:"Trash"}]},55939:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>d});var a=r(68570);let t=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\business\freelancerProfile\[freelancer_id]\page.tsx`),{__esModule:l,$$typeof:i}=t;t.default;let d=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\business\freelancerProfile\[freelancer_id]\page.tsx#default`)},90220:(e,s,r)=>{"use strict";r.d(s,{f:()=>c});var a=r(17577),t=r(77335),l=r(10326),i="horizontal",d=["horizontal","vertical"],n=a.forwardRef((e,s)=>{let{decorative:r,orientation:a=i,...n}=e,c=d.includes(a)?a:i;return(0,l.jsx)(t.WV.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...n,ref:s})});n.displayName="Separator";var c=n}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588],()=>r(21709));module.exports=a})();