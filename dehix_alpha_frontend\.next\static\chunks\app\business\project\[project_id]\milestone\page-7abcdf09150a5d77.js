(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9161],{85197:function(e,t,s){Promise.resolve().then(s.bind(s,63104))},5891:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(33480).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},20897:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(33480).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},24241:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(33480).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},70518:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(33480).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},13231:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(33480).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},71935:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(33480).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},47390:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(33480).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},98960:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(33480).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},73347:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(33480).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},10883:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(33480).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},63104:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return T}});var a,l,n,r,i=s(57437),c=s(2265),o=s(16463),d=s(64797),u=s(62688),m=s(56508),h=s(82230),x=s(89733),p=s(92513),f=s(11444),v=s(77209),b=s(6460),y=s(24241),j=s(49354),N=s(69081),g=s(21413);function w(e){let{selected:t,onChange:s,placeholderText:a="Pick a date",className:l}=e;return(0,i.jsxs)(g.J2,{children:[(0,i.jsx)(g.xo,{asChild:!0,children:(0,i.jsx)(x.z,{variant:"outline",className:(0,j.cn)("w-[280px] justify-start text-left font-normal",!t&&"text-muted-foreground",l),children:(0,i.jsxs)("div",{className:"flex justify-center",children:[(0,i.jsx)(y.Z,{className:"mr-2 h-4 w-4"}),t?(0,b.WU)(t,"PPP"):(0,i.jsx)("span",{children:a})]})})}),(0,i.jsx)(g.yk,{className:"w-auto p-0 text-xs ",children:(0,i.jsx)(N.f,{mode:"single",selected:null!=t?t:void 0,onSelect:e=>s(null!=e?e:null),initialFocus:!0})})]})}var k=s(31590);let D=e=>{let{options:t,value:s,onChange:a}=e,l=t.find(e=>e.value===s);return(0,i.jsxs)(k.h_,{children:[(0,i.jsx)(k.$F,{className:"border rounded p-2 w-full text-left",children:(null==l?void 0:l.label)||"Select an option"}),(0,i.jsx)(k.AW,{className:"mt-1 w-full border rounded shadow-md",children:t.map(e=>(0,i.jsx)(k.Xi,{className:"p-2 w-full cursor-pointer",onClick:()=>a(e.value),children:e.label},e.value))})]})};(a=n||(n={})).NOT_STARTED="NOT_STARTED",a.ONGOING="ONGOING",a.COMPLETED="COMPLETED",(l=r||(r={})).PENDING="PENDING",l.PAID="PAID";let Z=e=>{let{setErrors:t}=e,[s,a]=(0,c.useState)({title:"",description:"",startDate:{expected:""},endDate:{expected:""},amount:void 0,status:n.NOT_STARTED});return{formData:s,setFormData:a,handleChange:e=>{let{name:t,value:s}=e.target;a(e=>({...e,[t]:s}))},validateForm:()=>{var e;let a={};return s.title.trim()||(a.title="Title is required."),s.description.trim()||(a.description="Description is required."),new Date(s.startDate.expected)>new Date(s.endDate.expected)&&(a.startDate="Start date cannot be after end date."),(null!==(e=s.amount)&&void 0!==e?e:0)<0&&(a.amount="Amount must be a positive number."),t(a),0===Object.keys(a).length}}},C=(e,t)=>{let s=(e,s,a)=>{t(t=>({...t,[e]:{...t[e],[s]:a}}))};return{handleNestedChange:s,handleDateChange:(e,t,a)=>{s(e,t,a?a.toISOString():"")}}};var _=s(15922),M=s(78068),S=e=>{let{projectId:t,fetchMilestones:s,closeDialog:a}=e,l=(0,f.v9)(e=>{var t;return null===(t=e.user)||void 0===t?void 0:t.uid}),[r,o]=(0,c.useState)({}),{formData:d,setFormData:u,handleChange:m,validateForm:h}=Z({setErrors:o}),{handleDateChange:p}=C(d,u),b=async e=>{if(e.preventDefault(),h())try{await _.b.post("/milestones",{...d,userId:l,projectId:t}),(0,M.Am)({title:"Success",description:"Milestone created successfully!",variant:"default"}),s()}catch(e){(0,M.Am)({title:"Error",description:"Failed to create milestone.",variant:"destructive"})}finally{a()}};return(0,i.jsx)("div",{className:"flex justify-center items-center py-4",children:(0,i.jsxs)("div",{className:"w-full max-w-lg shadow-lg",children:[(0,i.jsx)("div",{children:(0,i.jsx)("h2",{className:"text-xl font-semibold",children:"Create Milestone"})}),(0,i.jsx)("div",{children:(0,i.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"title",className:"block text-sm font-medium mb-2",children:"Title"}),(0,i.jsx)(v.I,{id:"title",name:"title",value:d.title,onChange:m,placeholder:"Title",required:!0,className:"mt-1 block w-full"}),r.title&&(0,i.jsx)("p",{className:"text-xs text-red-500 mt-1",children:r.title})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium mb-2",children:"Description"}),(0,i.jsx)(v.I,{id:"description",name:"description",value:d.description,onChange:m,placeholder:"Description",required:!0,className:"mt-1 block w-full"}),r.description&&(0,i.jsx)("p",{className:"text-xs text-red-500 mt-1",children:r.description})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"startDate",className:"block whitespace-nowrap text-sm font-medium mb-2",children:"Start Date (Expected)"}),(0,i.jsx)(w,{selected:d.startDate.expected?new Date(d.startDate.expected):null,onChange:e=>p("startDate","expected",e),placeholderText:"Start Date (Expected)",className:"mt-1 block text-xs w-full"}),r.startDate&&(0,i.jsx)("p",{className:"text-xs text-red-500 mt-1",children:r.startDate})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"endDate",className:"block whitespace-nowrap text-sm font-medium mb-2",children:"End Date (Expected)"}),(0,i.jsx)(w,{selected:d.endDate.expected?new Date(d.endDate.expected):null,onChange:e=>p("endDate","expected",e),placeholderText:"End Date (Expected)",className:"mt-1 block text-xs w-full"}),r.endDate&&(0,i.jsx)("p",{className:"text-xs text-red-500 mt-1",children:r.endDate})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"amount",className:"block text-sm font-medium mb-2",children:"Amount"}),(0,i.jsx)(v.I,{id:"amount",name:"amount",type:"number",value:d.amount,onChange:m,placeholder:"Amount",required:!0,className:"mt-1 block w-full"}),r.amount&&(0,i.jsx)("p",{className:"text-xs text-red-500 mt-1",children:r.amount})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium mb-2",children:"Status"}),(0,i.jsx)(D,{options:[{label:"Not Started",value:n.NOT_STARTED},{label:"Ongoing",value:n.ONGOING},{label:"Completed",value:n.COMPLETED}],value:d.status,onChange:e=>u(t=>({...t,status:e}))}),r.status&&(0,i.jsx)("p",{className:"text-xs text-red-500 mt-1",children:r.status})]}),(0,i.jsx)("div",{className:"mt-4",children:(0,i.jsx)(x.z,{type:"submit",className:"w-full py-2",variant:"default",children:"Create Milestone"})})]})})]})})},E=s(54662);function I(e){let{projectId:t,fetchMilestones:s}=e,[a,l]=c.useState(!1);return(0,i.jsxs)(E.Vq,{open:a,onOpenChange:l,children:[(0,i.jsx)(E.hg,{asChild:!0,children:(0,i.jsxs)(x.z,{variant:"default",className:"hover:bg-transparent",children:[(0,i.jsx)(p.Z,{className:"mr-2"}),"Milestone"]})}),(0,i.jsxs)(E.cZ,{className:"max-h-[80vh] sm:max-w-[80vw] md:w-1/3 p-4 no-scrollbar overflow-y-auto",children:[(0,i.jsxs)(E.fK,{children:[(0,i.jsx)(E.$N,{children:"Create Milestone"}),(0,i.jsx)(E.Be,{children:"Fill out the form below to create a new milestone."})]}),(0,i.jsx)(S,{projectId:t,fetchMilestones:s,closeDialog:()=>{l(!1)}})]})]})}var T=()=>{let{project_id:e}=(0,o.useParams)(),[t,s]=(0,c.useState)([]),[a,l]=(0,c.useState)(!0),n=(0,c.useCallback)(async()=>{try{var t;let a=await _.b.get("/milestones",{params:{projectId:e}}),n={},r=null===(t=a.data)||void 0===t?void 0:t.data.map(e=>{var t;let s=(null===(t=e.stories)||void 0===t?void 0:t.length)?e.stories:null;return e._id&&(n[e._id]=s),{...e,stories:s||[]}});s(r),l(!1)}catch(e){(0,M.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."}),console.error("Error fetching milestones:",e),l(!1)}},[e]),r=async function(e,t,s){var a,l;let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;if(e.preventDefault(),!s){console.error("Milestone ID is undefined.");return}let c=r&&i?(null!==(a=s.stories)&&void 0!==a?a:[]).map(e=>e._id===i.storyId?{...e,tasks:[...e.tasks||[],i.formData]}:e):[...s.stories||[],t],o={...s,stories:c};try{await _.b.put("/milestones/".concat(s._id),o),(0,M.Am)({title:"Success",description:r?"Task added successfully!":"Story added successfully!",duration:3e3}),n()}catch(e){console.error("Error updating milestone:",(null===(l=e.response)||void 0===l?void 0:l.data)||e.message),(0,M.Am)({title:"Error",description:"Failed to update milestone.",variant:"destructive",duration:3e3})}};return(0,c.useEffect)(()=>{n()},[n]),(0,i.jsxs)("div",{className:"flex min-h-screen h-auto w-full flex-col bg-muted/40",children:[(0,i.jsx)(d.Z,{menuItemsTop:h.yn,menuItemsBottom:h.$C,active:""}),(0,i.jsxs)("div",{className:"flex flex-col sm:gap-4 sm:py-4 sm:pl-14 mb-8",children:[(0,i.jsx)(u.Z,{menuItemsTop:h.yn,menuItemsBottom:h.$C,activeMenu:"",breadcrumbItems:[{label:"Dashboard",link:"/dashboard/business"},{label:"Project",link:"/dashboard/business"},{label:e,link:"/business/project/".concat(e)},{label:"Milestone",link:"#"}]}),(0,i.jsxs)("div",{className:"py-8 px-2 md:px-4",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("h1",{className:"text-xl md:text-2xl font-bold",children:"Project Milestones"}),(0,i.jsx)(x.z,{className:"px-3 py-1 ",children:(0,i.jsx)(I,{projectId:e,fetchMilestones:n})})]}),(0,i.jsx)("div",{className:"w-full flex justify-center items-center",children:a?(0,i.jsx)("p",{children:"Loading milestones..."}):t.length>0?(0,i.jsx)(m.Z,{fetchMilestones:n,milestones:t,handleStorySubmit:r}):(0,i.jsx)("div",{className:"flex justify-center items-center h-[50vh]",children:"No milestones found."})})]})]})]})}},69081:function(e,t,s){"use strict";s.d(t,{f:function(){return o}});var a=s(57437);s(2265);var l=s(70518),n=s(87592),r=s(13793),i=s(49354),c=s(89733);function o(e){let{className:t,classNames:s,showOutsideDays:o=!0,...d}=e;return(0,a.jsx)(r._W,{showOutsideDays:o,className:(0,i.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,i.cn)((0,c.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,i.cn)((0,c.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...s},components:{IconLeft:e=>{let{...t}=e;return(0,a.jsx)(l.Z,{className:"h-4 w-4"})},IconRight:e=>{let{...t}=e;return(0,a.jsx)(n.Z,{className:"h-4 w-4"})}},...d})}o.displayName="Calendar"},82230:function(e,t,s){"use strict";s.d(t,{$C:function(){return v},Ne:function(){return b},yn:function(){return f}});var a=s(57437),l=s(11005),n=s(98960),r=s(38133),i=s(20897),c=s(13231),o=s(71935),d=s(47390),u=s(73347),m=s(24258),h=s(5891),x=s(10883),p=s(66648);let f=[{href:"#",icon:(0,a.jsx)(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,a.jsx)(l.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:(0,a.jsx)(n.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:(0,a.jsx)(r.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:(0,a.jsx)(r.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:(0,a.jsx)(i.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:(0,a.jsx)(c.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:(0,a.jsx)(o.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:(0,a.jsx)(d.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:(0,a.jsx)(u.Z,{className:"h-5 w-5"}),label:"Notes"}],v=[{href:"/business/settings/business-info",icon:(0,a.jsx)(m.Z,{className:"h-5 w-5"}),label:"Settings"}],b=[{href:"#",icon:(0,a.jsx)(p.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:(0,a.jsx)(l.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:(0,a.jsx)(u.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:(0,a.jsx)(h.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:(0,a.jsx)(x.Z,{className:"h-5 w-5"}),label:"Trash"}]}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,2455,9726,2688,2971,7023,1744],function(){return e(e.s=85197)}),_N_E=e.O()}]);