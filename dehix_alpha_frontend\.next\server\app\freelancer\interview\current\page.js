(()=>{var e={};e.id=9150,e.ids=[9150],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},70960:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>o,originalPathname:()=>m,pages:()=>x,routeModule:()=>h,tree:()=>d}),t(79506),t(54302),t(12523);var a=t(23191),r=t(88716),l=t(37922),i=t.n(l),n=t(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["freelancer",{children:["interview",{children:["current",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,79506)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\interview\\current\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],x=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\interview\\current\\page.tsx"],m="/freelancer/interview/current/page",o={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/freelancer/interview/current/page",pathname:"/freelancer/interview/current",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2777:(e,s,t)=>{Promise.resolve().then(t.bind(t,67227))},47546:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},4198:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(80851).Z)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},67227:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>S});var a=t(10326),r=t(17577),l=t.n(r),i=t(97685),n=t(88307),c=t(4198),d=t(23015),x=t(76812),m=t(25842),o=t(10143),h=t(40588),p=t(92166),u=t(30325),j=t(91664),f=t(47546),N=t(71821),w=t(37358),v=t(41156),g=t(38443),y=t(29752);let b=({filter:e,isTableView:s,searchQuery:t,skillData:r,domainData:l})=>{let i=()=>{let s="All"===e?[...r,...l]:"Skills"===e?r:"Domain"===e?l:[];return t?s.filter(({talentType:e})=>e?.toLowerCase().includes(t.toLowerCase())):s},n=e=>{if(!e)return"N/A";let s=new Date().getTime(),t=Math.ceil((new Date(e).getTime()-s)/864e5);return t>0?`${t}d`:"Today"};return(0,a.jsxs)("div",{className:"p-6 w-full",children:[a.jsx("div",{className:"mb-8  ml-0 md:ml-5 ",children:a.jsx("h1",{className:"text-2xl font-bold",children:"Dehix-talent interview"})}),a.jsx("div",{className:" p-0 md:p-6 flex flex-col gap-4  sm:px-6 sm:py-0 md:gap-8  pt-2 pl-0 sm:pt-4 sm:pl-6 md:pt-6 md:pl-8  relative",children:a.jsx("div",{children:0===i().length?a.jsx("div",{className:"text-center text-gray-500 py-6",children:"No related data found."}):s?a.jsx("div",{className:"w-full bg-card  mx-auto px-4 md:px-10 py-6 border border-gray-200 rounded-xl shadow-md",children:(0,a.jsxs)(v.iA,{children:[a.jsx(v.xD,{children:(0,a.jsxs)(v.SC,{className:" hover:bg-[#09090B",children:[a.jsx(v.ss,{className:"w-[180px] text-center font-medium",children:"Interviwer"}),a.jsx(v.ss,{className:"w-[180px] text-center font-medium",children:"Talent Name"}),a.jsx(v.ss,{className:" font-medium text-center ",children:"Experience"}),a.jsx(v.ss,{className:" font-medium text-center",children:"Interview Fees"}),a.jsx(v.ss,{className:" font-medium text-center",children:"Level"}),a.jsx(v.ss,{className:" font-medium text-center",children:"Status"}),a.jsx(v.ss,{className:"  font-medium text-center",children:"Actions"})]})}),a.jsx(v.RM,{children:i().map(e=>Object.values(e?.interviewBids||{}).filter(e=>e?.status==="ACCEPTED").map(s=>(0,a.jsxs)(v.SC,{className:" transition",children:[a.jsx(v.pj,{className:"py-3 text-center",children:s?.interviewer?.userName||"Unknown"}),a.jsx(v.pj,{className:"py-3 text-center",children:e.talentType}),a.jsx(v.pj,{className:"py-3 text-center",children:s?.interviewer?.workExperience}),a.jsx(v.pj,{className:"py-3 text-center",children:s?.fee}),a.jsx(v.pj,{className:"py-3 text-center",children:e.level}),a.jsx(v.pj,{className:"py-3 text-center",children:a.jsx(g.C,{variant:"default",className:"px-1 py-1 text-xs",children:e?.InterviewStatus})}),(0,a.jsxs)(v.pj,{className:"text-right py-3",children:[a.jsx(j.z,{variant:"outline",size:"sm",className:"mr-3",children:"Edit"}),a.jsx(j.z,{size:"sm",children:"View"})]})]},e._id)))})]})}):a.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:i().map(e=>Object.values(e?.interviewBids||{}).filter(e=>e?.status==="ACCEPTED").map(s=>(0,a.jsxs)(y.Zb,{className:"p-6 relative rounded-2xl shadow-xl border border-gray-300 hover:shadow-2xl ",children:[(0,a.jsxs)(y.Ol,{className:"p-4 border-b  rounded-t-2xl",children:[a.jsx(y.ll,{className:"text-xl font-semibold ",children:s?.interviewer?.userName||"Unknown"}),a.jsx(y.SZ,{className:"text-sm ",children:e?.level}),a.jsx("p",{className:"text-sm absolute top-1 right-3 flex items-center gap-2",children:a.jsx(g.C,{variant:"default",className:"px-1 py-1 text-xs",children:e?.InterviewStatus})})]}),(0,a.jsxs)(y.aY,{className:"p-4 space-y-3",children:[(0,a.jsxs)("p",{className:"text-sm flex items-center gap-2",children:[a.jsx(f.Z,{size:16,className:""}),a.jsx("span",{className:"font-medium",children:"Experience :"}),s?.interviewer?.workExperience," years"]}),(0,a.jsxs)("p",{className:"text-sm flex items-center gap-2",children:[a.jsx(N.Z,{size:16,className:""}),a.jsx("span",{className:"font-medium",children:"Interview Fees :"}),"₹",s?.fee]}),(0,a.jsxs)("p",{className:"text-sm flex items-center whitespace-nowrap gap-2",children:[a.jsx(w.Z,{size:16,className:""}),a.jsx("span",{className:"font-medium",children:"Schedule Date :"}),new Date(s?.suggestedDateTime).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]})]}),(0,a.jsxs)(y.eW,{className:"flex  justify-between p-4 border-t  rounded-b-2xl",children:[a.jsx(j.z,{variant:"outline",size:"sm",className:"",children:"Edit"}),a.jsx(j.z,{size:"sm",className:"",children:"View"}),a.jsx("span",{className:"absolute bottom-3 right-4 text-xs font-medium text-gray-600  px-2 py-1 rounded-md",children:n(s?.suggestedDateTime)})]})]},s?._id)))})})})]})};var k=t(41190);t(6260);var D=t(38227);let C=({isTableView:e})=>(0,a.jsxs)("div",{className:"space-y-4 w-full",children:[a.jsx(D.O,{className:"h-6 w-1/4 flex justify-start"}),e?(0,a.jsxs)("div",{className:"border rounded-lg shadow-sm p-4",children:[a.jsx(D.O,{className:"h-6 w-1/3 mb-3"})," ",a.jsx("div",{className:"border-t",children:[...Array(6)].map((e,s)=>(0,a.jsxs)("div",{className:"flex justify-between items-center py-3 border-b",children:[a.jsx(D.O,{className:"h-5 w-1/4"}),a.jsx(D.O,{className:"h-5 w-1/6"}),a.jsx(D.O,{className:"h-5 w-1/5"}),a.jsx(D.O,{className:"h-5 w-1/6"}),a.jsx(D.O,{className:"h-5 w-1/8"})]},s))})]}):a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)("div",{className:"border rounded-2xl shadow-md p-4 space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx(D.O,{className:"h-5 w-1/3"}),a.jsx(D.O,{className:"h-5 w-1/6 rounded-full"})]}),a.jsx(D.O,{className:"h-4 w-1/2"}),a.jsx(D.O,{className:"h-4 w-3/4"}),a.jsx(D.O,{className:"h-4 w-3/4"}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx(D.O,{className:"h-7 w-1/5"}),a.jsx(D.O,{className:"h-7 w-1/5"})]})]},s))}),(0,a.jsxs)("div",{className:"flex justify-between items-center mt-10",children:[a.jsx(D.O,{className:"h-6 w-1/4 flex justify-start"}),a.jsx(D.O,{className:"h-6 w-1/12 flex justify-start"})]}),e?a.jsx("div",{className:"border rounded-lg shadow-sm p-4",children:a.jsx("div",{className:"border-t",children:[...Array(6)].map((e,s)=>(0,a.jsxs)("div",{className:"flex justify-between items-center py-3 border-b",children:[a.jsx(D.O,{className:"h-5 w-1/4"}),a.jsx(D.O,{className:"h-5 w-1/6"}),a.jsx(D.O,{className:"h-5 w-1/5"}),a.jsx(D.O,{className:"h-5 w-1/6"}),a.jsx(D.O,{className:"h-5 w-1/8"}),a.jsx(D.O,{className:"h-5 w-1/8"}),a.jsx(D.O,{className:"h-5 w-1/8"})]},s))})}):a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)("div",{className:"border rounded-2xl shadow-md p-4 space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx(D.O,{className:"h-5 w-1/3"}),a.jsx(D.O,{className:"h-5 w-1/6 rounded-full"})]}),a.jsx(D.O,{className:"h-4 w-1/2"}),a.jsx(D.O,{className:"h-4 w-3/4"}),a.jsx(D.O,{className:"h-4 w-3/4"}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx(D.O,{className:"h-7 w-1/5"}),a.jsx(D.O,{className:"h-7 w-1/5"})]})]},s))})]}),O=({filter:e,isTableView:s,searchQuery:t,skillData:r,domainData:l})=>{let i=()=>{let s="All"===e?[...r,...l]:"Skills"===e?r:"Domain"===e?l:[];return t?s.filter(({talentType:e})=>e?.toLowerCase().includes(t.toLowerCase())):s},n=e=>{if(!e)return"N/A";let s=new Date().getTime(),t=Math.ceil((new Date(e).getTime()-s)/864e5);return t>0?`${t}d`:"Today"};return(0,a.jsxs)("div",{className:"p-6 w-full",children:[a.jsx("div",{className:"mb-8 ml-0 md:ml-5 flex justify-between items-center",children:a.jsx("h1",{className:"text-2xl font-bold",children:"Project interview"})}),a.jsx("div",{className:" p-0 md:p-6 flex flex-col gap-4  sm:px-6 sm:py-0 md:gap-8  pt-2 pl-0 sm:pt-4 sm:pl-6 md:pt-6 md:pl-8  relative",children:a.jsx("div",{children:0===i().length?a.jsx("div",{className:"text-center text-gray-500 py-6",children:"No related data found."}):s?a.jsx("div",{className:"w-full bg-card  mx-auto px-4 md:px-10 py-6 border border-gray-200 rounded-xl shadow-md",children:(0,a.jsxs)(v.iA,{children:[a.jsx(v.xD,{children:(0,a.jsxs)(v.SC,{className:" hover:bg-[#09090B",children:[a.jsx(v.ss,{className:"w-[180px] text-center font-medium",children:"Interviwer"}),a.jsx(v.ss,{className:"w-[180px] text-center font-medium",children:"Talent Name"}),a.jsx(v.ss,{className:" font-medium text-center ",children:"Experience"}),a.jsx(v.ss,{className:" font-medium text-center",children:"Interview Fees"}),a.jsx(v.ss,{className:" font-medium text-center",children:"Level"}),a.jsx(v.ss,{className:" font-medium text-center",children:"Status"}),a.jsx(v.ss,{className:"  font-medium text-center",children:"Actions"})]})}),a.jsx(v.RM,{children:i().map(e=>Object.values(e?.interviewBids||{}).filter(e=>e?.status==="ACCEPTED").map(s=>(0,a.jsxs)(v.SC,{className:" transition",children:[a.jsx(v.pj,{className:"py-3 text-center",children:s?.interviewer?.userName||"Unknown"}),a.jsx(v.pj,{className:"py-3 text-center",children:e.talentType}),a.jsx(v.pj,{className:"py-3 text-center",children:s?.interviewer?.workExperience}),a.jsx(v.pj,{className:"py-3 text-center",children:s?.fee}),a.jsx(v.pj,{className:"py-3 text-center",children:e.level}),a.jsx(v.pj,{className:"py-3 text-center",children:a.jsx(g.C,{variant:"default",className:"px-1 py-1 text-xs",children:e?.InterviewStatus})}),(0,a.jsxs)(v.pj,{className:"text-right py-3",children:[a.jsx(j.z,{variant:"outline",size:"sm",className:"mr-3",children:"Edit"}),a.jsx(j.z,{size:"sm",children:"View"})]})]},e._id)))})]})}):a.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:i().map(e=>Object.values(e?.interviewBids||{}).filter(e=>e?.status==="ACCEPTED").map(s=>(0,a.jsxs)(y.Zb,{className:"p-6 relative rounded-2xl shadow-xl border border-gray-300 hover:shadow-2xl ",children:[(0,a.jsxs)(y.Ol,{className:"p-4 border-b  rounded-t-2xl",children:[a.jsx(y.ll,{className:"text-xl font-semibold ",children:s?.interviewer?.userName||"Unknown"}),a.jsx(y.SZ,{className:"text-sm ",children:e?.level}),a.jsx("p",{className:"text-sm absolute top-1 right-3 flex items-center gap-2",children:a.jsx(g.C,{variant:"default",className:"px-1 py-1 text-xs",children:e?.InterviewStatus})})]}),(0,a.jsxs)(y.aY,{className:"p-4 space-y-3",children:[(0,a.jsxs)("p",{className:"text-sm flex items-center gap-2",children:[a.jsx(f.Z,{size:16,className:""}),a.jsx("span",{className:"font-medium",children:"Experience :"}),s?.interviewer?.workExperience," years"]}),(0,a.jsxs)("p",{className:"text-sm flex items-center gap-2",children:[a.jsx(N.Z,{size:16,className:""}),a.jsx("span",{className:"font-medium",children:"Interview Fees :"}),"₹",s?.fee]}),(0,a.jsxs)("p",{className:"text-sm flex items-center whitespace-nowrap gap-2",children:[a.jsx(w.Z,{size:16,className:""}),a.jsx("span",{className:"font-medium",children:"Schedule Date :"}),new Date(s?.suggestedDateTime).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]})]}),(0,a.jsxs)(y.eW,{className:"flex  justify-between p-4 border-t  rounded-b-2xl",children:[a.jsx(j.z,{variant:"outline",size:"sm",className:"",children:"Edit"}),a.jsx(j.z,{size:"sm",className:"",children:"View"}),a.jsx("span",{className:"absolute bottom-3 right-4 text-xs font-medium text-gray-600  px-2 py-1 rounded-md",children:n(s?.suggestedDateTime)})]})]},s?._id)))})})})]})};function S(){let[e,s]=l().useState("All"),[t,f]=(0,r.useState)(!1),[N,w]=(0,r.useState)(""),[v,g]=(0,r.useState)(!1),y=(0,r.useRef)(null);(0,m.v9)(e=>e.user);let[D,S]=(0,r.useState)([]),[z,_]=(0,r.useState)([]),[q,Z]=(0,r.useState)([]),[T,P]=(0,r.useState)([]),[E,A]=(0,r.useState)(!1),[I,M]=(0,r.useState)(!1);return D.length>0||z.length>0||q.length>0||T.length,(0,a.jsxs)("div",{className:"flex min-h-screen w-full bg-muted/40",children:[a.jsx(p.Z,{menuItemsTop:u.y,menuItemsBottom:u.$,active:"Current"}),(0,a.jsxs)("div",{className:"flex flex-col mb-8 sm:gap-4 sm:py-0 sm:pl-14 w-full",children:[a.jsx(h.Z,{breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Interview",link:"/freelancer/interview/profile"},{label:"Current Interviews",link:"#"}],menuItemsTop:u.y,menuItemsBottom:u.$,activeMenu:"Current"}),(0,a.jsxs)("div",{className:"ml-10",children:[a.jsx("h1",{className:"text-3xl font-bold",children:"Current Interviews"}),a.jsx("p",{className:"text-gray-400 mt-2",children:"View and manage your current interviews, and update skills for better matches."})]}),(0,a.jsxs)("div",{className:"flex flex-col flex-1 items-start gap-4 p-2 sm:px-6 sm:py-0 md:gap-8 lg:flex-col xl:flex-col pt-2 pl-4 sm:pt-4 sm:pl-6 md:pt-6 md:pl-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center w-full",children:[(0,a.jsxs)(o.h_,{children:[a.jsx(o.$F,{asChild:!0,children:(0,a.jsxs)(j.z,{variant:"outline",size:"sm",className:"h-7 gap-1  text-sm",children:[a.jsx(i.Z,{className:"h-3.5 w-3.5"}),a.jsx("span",{className:"sr-only sm:not-sr-only",children:"Filter"})]})}),(0,a.jsxs)(o.AW,{align:"end",children:[a.jsx(o.Ju,{children:"Filter by"}),a.jsx(o.VD,{}),a.jsx(o.bO,{checked:"All"===e,onSelect:()=>s("All"),children:"All"}),a.jsx(o.bO,{checked:"Skills"===e,onSelect:()=>s("Skills"),children:"Skills"}),a.jsx(o.bO,{checked:"Domain"===e,onSelect:()=>s("Domain"),children:"Domain"})]})]}),(0,a.jsxs)("div",{className:"flex justify-center gap-3 items-center",children:[(0,a.jsxs)("div",{className:"relative flex-1 mr-2",children:[!v&&a.jsx(n.Z,{size:"sm",className:"absolute left-2 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 sm:block md:hidden ml-0.5 cursor-pointer",onClick:()=>g(!0)}),a.jsx(n.Z,{size:"sm",className:`absolute h-7 gap-1 text-sm left-2 top-1/2 transform -translate-y-1/2 w-5 text-gray-400 cursor-pointer 
        ${v?"sm:flex":"hidden md:flex"}`}),a.jsx(k.I,{placeholder:"Search interview",value:N,ref:y,onChange:e=>w(e.target.value),onFocus:()=>g(!0),onBlur:()=>g(!1),className:`pl-8 transition-all duration-300 ease-in-out
          ${v?"w-full sm:w-72":"w-0 sm:w-0 md:w-full"} sm:hidden `}),a.jsx(k.I,{placeholder:"Search interview by...",value:N,onChange:e=>w(e.target.value),onFocus:()=>g(!0),onBlur:()=>g(!1),className:"pl-8 hidden md:flex border focus-visible:ring-1  focus:ring-0 "})]}),!v&&(0,a.jsxs)("div",{className:"gap-2 md:hidden flex",children:[a.jsx(j.z,{onClick:()=>f(!0),variant:"outline",size:"sm",className:"h-7 gap-1 text-sm",children:a.jsx(c.Z,{className:"h-3.5 w-3.5"})}),a.jsx(j.z,{onClick:()=>f(!1),variant:"outline",size:"sm",className:"h-7 gap-1 text-sm",children:a.jsx(x.kxK,{className:"h-3.5 w-3.5"})})]}),(0,a.jsxs)("div",{className:"gap-2 md:flex hidden",children:[a.jsx(j.z,{onClick:()=>f(!0),variant:"outline",size:"sm",className:"h-7 gap-1 text-sm",children:a.jsx(c.Z,{className:"h-3.5 w-3.5"})}),a.jsx(j.z,{onClick:()=>f(!1),variant:"outline",size:"sm",className:"h-7 gap-1 text-sm",children:a.jsx(x.kxK,{className:"h-3.5 w-3.5"})})]})]})]}),a.jsx("div",{className:"w-full flex justify-center items-center flex-col",children:E?a.jsx(C,{isTableView:t}):(0,a.jsxs)("div",{className:"w-full space-y-8",children:[(0,a.jsxs)("div",{className:"w-full",children:[a.jsx("div",{className:"mb-4",children:a.jsx("h2",{className:"text-2xl font-semibold ",children:"Dehix Talent Interviews"})}),0===D.length&&0===z.length?(0,a.jsxs)("div",{className:"text-center py-8 w-full ",children:[a.jsx(d.Z,{className:"mx-auto text-gray-400",size:"60"}),a.jsx("p",{className:"text-gray-500 text-base font-medium mt-3",children:"No Dehix talent interviews scheduled."}),a.jsx("p",{className:"text-gray-400 text-sm mt-1",children:"Browse available talent opportunities to schedule new interviews."})]}):a.jsx(b,{skillData:D,domainData:z,searchQuery:N,isTableView:t,filter:e})]}),(0,a.jsxs)("div",{className:"w-full",children:[a.jsx("div",{className:"mb-4",children:a.jsx("h2",{className:"text-2xl font-semibold ",children:"Project Interviews"})}),0===q.length&&0===T.length?(0,a.jsxs)("div",{className:"text-center py-8 w-full",children:[a.jsx(d.Z,{className:"mx-auto text-gray-400",size:"60"}),a.jsx("p",{className:"text-gray-500 text-base font-medium mt-3",children:"No project interviews scheduled."}),a.jsx("p",{className:"text-gray-400 text-sm mt-1",children:"Check your project applications for upcoming interview opportunities."})]}):a.jsx(O,{searchQuery:N,isTableView:t,skillData:q,domainData:T,filter:e})]})]})})]})]})]})}t(56627)},38227:(e,s,t)=>{"use strict";t.d(s,{O:()=>l});var a=t(10326),r=t(51223);function l({className:e,...s}){return a.jsx("div",{className:(0,r.cn)("animate-pulse rounded-md bg-primary/10",e),...s})}},30325:(e,s,t)=>{"use strict";t.d(s,{$:()=>h,y:()=>o});var a=t(10326),r=t(95920),l=t(94909),i=t(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,i.Z)("ListVideo",[["path",{d:"M12 12H3",key:"18klou"}],["path",{d:"M16 6H3",key:"1wxfjs"}],["path",{d:"M12 18H3",key:"11ftsu"}],["path",{d:"m16 12 5 3-5 3v-6Z",key:"zpskkp"}]]);var c=t(47546);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,i.Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var x=t(88378),m=t(46226);let o=[{href:"#",icon:a.jsx(m.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:a.jsx(r.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/interview/profile",icon:a.jsx(l.Z,{className:"h-5 w-5"}),label:"Profile"},{href:"/freelancer/interview/current",icon:a.jsx(n,{className:"h-5 w-5"}),label:"Current"},{href:"/freelancer/interview/bids",icon:a.jsx(c.Z,{className:"h-5 w-5"}),label:"Bids"},{href:"/freelancer/interview/history",icon:a.jsx(d,{className:"h-5 w-5"}),label:"History"}],h=[{href:"/freelancer/settings/personal-info",icon:a.jsx(x.Z,{className:"h-5 w-5"}),label:"Settings"}]},79506:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var a=t(68570);let r=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\interview\current\page.tsx`),{__esModule:l,$$typeof:i}=r;r.default;let n=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\interview\current\page.tsx#default`)}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,9561,4736,6499,8066,588],()=>t(70960));module.exports=a})();