"use strict";exports.id=588,exports.ids=[588],exports.modules={40588:(e,s,t)=>{t.d(s,{Z:()=>$});var a=t(10326),r=t(17577),n=t(25842),l=t(54406),c=t(87922),i=t(79635),d=t(88378),o=t(36283),x=t(40167),m=t(15721),u=t(94909),h=t(71821),j=t(32933),f=t(21985),p=t(6507),g=t(35047),w=t(88118),N=t(3594),v=t(51027),b=t(91664),y=t(58595);let C=()=>{let e=(0,g.useRouter)(),s=(0,n.v9)(e=>e.user),[t,l]=(0,r.useState)([]),c=t.filter(e=>!e.isRead).length;return(0,r.useEffect)(()=>{let e;return s?.uid&&(e=(0,y.e$)(s.uid,e=>{l(e)})),()=>{e&&e()}},[s]),(0,a.jsxs)(v.J2,{children:[a.jsx(v.xo,{asChild:!0,children:(0,a.jsxs)(b.z,{variant:"ghost",size:"icon",className:"relative rounded-full hover:scale-105 transition-transform",children:[a.jsx(p.Z,{className:"w-6 h-6 relative rounded-full hover:scale-105 transition-transform"}),c>0&&a.jsx("span",{className:"absolute top-1 left-9 flex h-4 w-7 items-center justify-center rounded-full bg-red-500 text-white text-xs transform -translate-x-1/2 -translate-y-1/2",children:c})]})}),(0,a.jsxs)(v.yk,{className:"w-[300px] p-4",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("div",{className:"flex justify-between items-center",children:(0,a.jsxs)("p",{className:"ml-auto text-xs text-muted-foreground",children:[c," unread"]})}),0===t.length?a.jsx("p",{className:"text-sm text-muted-foreground",children:"No notifications available."}):a.jsx("div",{className:"space-y-2",children:t.map(s=>(0,a.jsxs)("div",{onClick:()=>e.push(s.path),className:"rounded py-4 mb-4 items-start cursor-pointer hover:bg-muted hover:opacity-75 transition",children:[a.jsx("div",{children:!s.isRead&&a.jsx("span",{className:"flex h-2 w-2 translate-y-1 rounded-full bg-sky-500"})}),(0,a.jsxs)("div",{className:"space-y-1 px-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.jsx(N.Avatar,{className:"h-6 w-6 text-white flex items-center justify-center p-1 ring-1 ring-white",children:function(e){switch(e){case"Account":return a.jsx(i.Z,{});case"Settings":return a.jsx(d.Z,{});case"Document":return a.jsx(o.Z,{});case"Bid":return a.jsx(x.Z,{});case"Interview":return a.jsx(m.Z,{});case"Hire":return a.jsx(u.Z,{});case"Transaction":return a.jsx(h.Z,{});case"Verification":return a.jsx(j.Z,{});case"Ticket":return a.jsx(f.Z,{});default:return a.jsx(p.Z,{})}}(s.entity)}),a.jsx("p",{className:"text-sm font-medium leading-none",children:s.message})]}),a.jsx("p",{className:"flex justify-end text-xs text-muted-foreground",children:(0,w.Q)(s.timestamp)})]})]},s.id))})]}),t.length>0&&a.jsx("div",{className:"mt-4",children:(0,a.jsxs)(b.z,{variant:"outline",size:"sm",className:"w-full",onClick:()=>{(0,y.Go)(s.uid)},children:[a.jsx(j.Z,{className:"mr-2 h-4 w-4"})," Mark all as read"]})})]})]})};var E=t(86144),S=t(51682),k=t(16671),Z=t(97685),I=t(38443),A=t(10143),D=t(3236),L=t(6260),J=t(54423),R=t(41156),T=t(24118);let z=({connects:e,userId:s})=>{let[t,n]=(0,r.useState)("ALL"),[l,c]=(0,r.useState)([]),[i,d]=(0,r.useState)([]),[o,x]=(0,r.useState)(!1),[m,u]=(0,r.useState)(!1),h=(0,r.useCallback)(async()=>{if(!m){u(!0);try{let e=await L.b.get(`/token-request/user/${s}`,{params:{latestConnects:!0}});d(e.data.data),c(e.data.data)}catch(e){console.error("Error fetching data:",e)}finally{u(!1)}}},[s,m]),j=e=>{let s=e.detail;d(e=>{let t=[s,...e.slice(0,2)];return c(t),t})};(0,r.useEffect)(()=>(window.addEventListener("newConnectRequest",j),()=>{window.removeEventListener("newConnectRequest",j)}),[]),(0,r.useEffect)(()=>{o&&h()},[o,h]),(0,r.useEffect)(()=>{c(()=>"ALL"===t?i??[]:(i??[]).filter(e=>e.status===t))},[i,t]);let f=e=>{n(e)},p=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"2-digit"});return(0,a.jsxs)(T.Vq,{open:o,onOpenChange:x,children:[a.jsx(T.hg,{asChild:!0,children:(0,a.jsxs)("div",{className:"relative flex items-center justify-end md:justify-center cursor-pointer hover:scale-105 transition-transform",children:[a.jsx(k.Z,{}),e>=0&&a.jsx("span",{className:"absolute -top-2 -right-3 bg-red-500 text-white text-[9px] font-bold rounded-full px-2 shadow-md",children:e})]})}),(0,a.jsxs)(T.cZ,{className:"max-w-3xl max-h-[80vh] flex flex-col",children:[a.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Connects Request"}),a.jsx("div",{className:"flex items-center gap-4 mb-4",children:(0,a.jsxs)(A.h_,{children:[a.jsx(A.$F,{asChild:!0,children:(0,a.jsxs)(b.z,{variant:"outline",size:"sm",className:"h-7 gap-1 w-auto text-sm",children:[a.jsx(Z.Z,{className:"h-3.5 w-3.5"}),a.jsx("span",{className:"sr-only sm:not-sr-only",children:"Filter"})]})}),(0,a.jsxs)(A.AW,{align:"center",className:"w-40",children:[a.jsx(A.Ju,{children:"Status"}),a.jsx(A.VD,{}),[{key:"ALL",label:"All"},{key:"PENDING",label:"Pending"},{key:"REJECTED",label:"Rejected"},{key:"ACCEPTED",label:"Accepted"}].map(({key:e,label:s})=>a.jsx(A.bO,{className:"py-1",checked:t===e,onSelect:()=>f(e),children:s},e))]})]})}),(0,a.jsxs)("div",{className:"relative overflow-hidden",children:[a.jsx(R.iA,{className:"min-w-full border rounded-lg shadow-lg",children:a.jsx(R.xD,{className:"sticky top-0 z-10 bg-muted/40",children:(0,a.jsxs)(R.SC,{className:"text-center",children:[a.jsx(R.ss,{className:"text-center w-1/3",children:"Connects"}),a.jsx(R.ss,{className:"text-center w-1/3",children:"Status"}),a.jsx(R.ss,{className:"text-center w-1/3",children:"Date"})]})})}),a.jsx(D.x,{className:"max-h-[300px] no-scrollbar pb-10 overflow-y-auto",children:a.jsx(R.iA,{className:"min-w-full",children:a.jsx(R.RM,{children:l.length>0?l.map((e,s)=>(0,a.jsxs)(R.SC,{className:"text-center",children:[a.jsx(R.pj,{className:"font-semibold text-center w-1/3",children:e.amount}),a.jsx(R.pj,{className:"text-center w-1/3",children:a.jsx(I.C,{className:(0,J.S)(e.status),children:e.status})}),a.jsx(R.pj,{className:"text-center w-1/3",children:p(e.dateTime)})]},s)):a.jsx(R.SC,{children:a.jsx(R.pj,{colSpan:3,className:"text-center font-semibold text-gray-500",children:"No Transactions Found"})})})})})]})]})]})},$=({menuItemsTop:e,menuItemsBottom:s,activeMenu:t,breadcrumbItems:i,conversations:d,activeConversation:o,setActiveConversation:x})=>{let m=(0,n.v9)(e=>e.user),[u,h]=(0,r.useState)(0),j=async()=>{try{let e=localStorage.getItem("DHX_CONNECTS"),s=e?parseInt(e):0;isNaN(s)||h(s)}catch(e){console.error("Error fetching connects:",e)}};return(0,r.useEffect)(()=>{m?.uid&&j();let e=()=>j();return window.addEventListener("connectsUpdated",e),()=>{window.removeEventListener("connectsUpdated",e)}},[m?.uid]),(0,a.jsxs)("header",{className:"sticky top-0 z-30 flex h-14 items-center py-6 gap-4 border-b bg-background px-4 sm:border-0 sm:px-6",children:[a.jsx(l.Z,{menuItemsTop:e,menuItemsBottom:s,active:t,setActiveConversation:x,conversations:d,activeConversation:o}),a.jsx(E.Z,{items:i}),a.jsx("div",{className:"relative ml-auto flex-1 md:grow-0"}),a.jsx(S.zs,{children:(0,a.jsxs)("div",{className:"relative ml-auto flex-1 md:grow-0",children:[a.jsx(S.Yi,{asChild:!0,children:a.jsx(z,{userId:m.uid,connects:u})}),a.jsx(S.bZ,{className:"w-auto px-4 py-2 text-center font-bold shadow-xl rounded-lg",children:null!==u?`${u?u>=1e6?(u/1e6).toFixed(1).replace(/\.0$/,"")+"M":u>=1e3?(u/1e3).toFixed(1).replace(/\.0$/,"")+"K":u.toString():"0"} rewards Available`:"No rewards yet!"})]})}),a.jsx(C,{}),a.jsx(c.Z,{setConnects:h})]})}},58595:(e,s,t)=>{t.d(s,{Go:()=>d,Hd:()=>l,K5:()=>n,e$:()=>i,fy:()=>c});var a=t(76),r=t(45722);function n(e,s,t){let n=(0,a.hJ)(r.db,e),l=(0,a.IO)(n,(0,a.ar)("participants","array-contains",s),(0,a.Xo)("lastMessage.timestamp","desc"));return(0,a.cf)(l,async e=>{t(await Promise.all(e.docs.map(async e=>{let s={id:e.id,...e.data()},t=s?.lastMessage||null;return{...s,lastMessage:t}})))})}async function l(e,s,t,n){try{return await (0,a.i3)(r.db,async l=>{let c=(0,a.JU)(r.db,e,s),i=(0,a.hJ)(r.db,e,s,"messages");l.update(c,{lastMessage:t,timestamp:n});let d=(0,a.JU)(i);l.set(d,{...t,timestamp:n}),console.log(`Transaction committed: Message ID - ${d.id}`)}),"Transaction successful"}catch(e){throw console.error("Transaction failed:",e),e}}async function c(e,s,t){try{let n=(0,a.JU)(r.db,e,s);await (0,a.r7)(n,t),console.log("Document updated with ID:",s)}catch(e){console.error("Error updating document:",e.message)}}let i=(e,s)=>{let t=(0,a.hJ)(r.db,"notifications"),n=(0,a.IO)(t,(0,a.ar)("userId","array-contains",e));return(0,a.cf)(n,e=>{let t=[];e.forEach(e=>{t.push({id:e.id,...e.data()})}),s(t)})},d=async e=>{let s=(0,a.qs)(r.db);try{let t=(0,a.hJ)(r.db,"notifications"),n=(0,a.IO)(t,(0,a.ar)("userId","==",e),(0,a.ar)("isRead","==",!1));(await (0,a.PL)(n)).forEach(e=>{let t=(0,a.JU)(r.db,"notifications",e.id);s.update(t,{isRead:!0})}),await s.commit()}catch(e){throw console.error("Error marking notifications as read:",e),Error("Failed to mark notifications as read")}}},54423:(e,s,t)=>{t.d(s,{S:()=>a});let a=e=>{switch(e?.toLowerCase()){case"active":case"verified":case"added":return"bg-green-500 text-white";case"pending":return"bg-yellow-500 text-black";case"approved":return"bg-green-500 text-black";case"rejected":return"bg-red-500 text-black";default:return"bg-gray-500 text-white"}}}};