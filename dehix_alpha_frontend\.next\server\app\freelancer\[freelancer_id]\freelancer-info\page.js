(()=>{var e={};e.id=3977,e.ids=[3977],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},2636:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c}),s(78237),s(54302),s(12523);var t=s(23191),a=s(88716),n=s(37922),i=s.n(n),o=s(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(r,l);let c=["",{children:["freelancer",{children:["[freelancer_id]",{children:["freelancer-info",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,78237)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\[freelancer_id]\\freelancer-info\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\[freelancer_id]\\freelancer-info\\page.tsx"],m="/freelancer/[freelancer_id]/freelancer-info/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/freelancer/[freelancer_id]/freelancer-info/page",pathname:"/freelancer/[freelancer_id]/freelancer-info",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},59696:(e,r,s)=>{Promise.resolve().then(s.bind(s,54562))},6343:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(80851).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},47546:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},48705:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(80851).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},54562:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>j});var t=s(10326),a=s(17577),n=s.n(a),i=s(3512),o=s(29752),l=s(38443),c=s(92166),d=s(87922),m=s(86144),p=s(54406),x=s(45175),u=s(54423);let h={profile:{firstName:"John",lastName:"Doe",username:"johndoe",email:"<EMAIL>",phone:"+**********",role:"Developer",skills:[{name:"JavaScript"},{name:"React"},{name:"Node.js"}],domains:[{name:"Web Development"},{name:"Frontend"}]},professional:[{company:"Tech Solutions",jobTitle:"Senior Developer",workDescription:"Developed and maintained web applications using modern technologies.",workFrom:"2020-01-01",workTo:"2024-01-01",referencePersonName:"Jane Smith",referencePersonContact:"+0987654321",githubRepoLink:"https://github.com/username/repo",location:"India",status:"Verified"},{company:"Innovatech",jobTitle:"Software Engineer",workDescription:"Worked on various innovative projects and improved software performance.",workFrom:"2018-01-01",workTo:"2019-12-31",referencePersonName:"Mike Johnson",referencePersonContact:"+1234509876",githubRepoLink:"https://github.com/username/innovatech-repo",location:"India",status:"Pending"}],project:[{projectName:"Awesome Project",description:"A cutting-edge project involving the latest technologies.",githubRepoLink:"https://github.com/username/project-repo",startDate:"2023-01-01",endDate:"2023-12-31",reference:"Project reference details",technologiesUsed:"React, Node.js, Express",role:"Lead Developer",projectType:"Web Application",status:"Verified"},{projectName:"Another Cool Project",description:"An innovative project solving real-world problems.",githubRepoLink:"https://github.com/username/cool-project-repo",startDate:"2022-01-01",endDate:"2022-12-31",reference:"Cool project reference details",technologiesUsed:"Angular, Node.js, MongoDB",role:"Full Stack Developer",projectType:"Mobile Application",status:"Pending"}],education:[{degree:"Bachelor of Technology",universityName:"KJ Somaiya Institute of Technology",fieldOfStudy:"Information Technology",startDate:"2018-08-01",endDate:"2022-06-30",grade:"9.86 CGPA",status:"Pending"},{degree:"Master of Technology",universityName:"ABC University",fieldOfStudy:"Computer Science",startDate:"2023-08-01",endDate:"2025-06-30",grade:"9.95 CGPA",status:"Verified"}]},f=({title:e,children:r})=>(0,t.jsxs)(o.Zb,{className:"p-8 bg-black rounded-lg shadow-md",children:[t.jsx("h2",{className:"text-xl font-bold text-white mb-4",children:e}),t.jsx("div",{className:"space-y-6",children:r})]}),g=({title:e,data:r})=>t.jsx("div",{children:(0,t.jsxs)("p",{children:[(0,t.jsxs)("strong",{className:"font-semibold",children:[e,":"]})," ",t.jsx("span",{className:"text-muted-foreground",children:r})]})}),j=()=>(0,t.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[t.jsx(c.Z,{menuItemsTop:x.y,menuItemsBottom:x.$,active:"Personal Info"}),(0,t.jsxs)("div",{className:"flex flex-col mb-8 sm:gap-8 sm:py-0 sm:pl-14",children:[(0,t.jsxs)("header",{className:"sticky top-0 z-30 flex h-14 items-center gap-4 border-b bg-background px-4 sm:border-0  sm:px-6",children:[t.jsx(p.Z,{menuItemsTop:x.y,menuItemsBottom:x.$,active:"Personal Info"}),t.jsx(m.Z,{items:[{label:"Freelancer",link:"#"},{label:"Freelancer Info",link:"#"}]}),t.jsx("div",{className:"relative ml-auto flex-1 md:grow-0",children:t.jsx(i.o,{className:"w-full md:w-[200px] lg:w-[336px]"})}),t.jsx(d.Z,{})]}),(0,t.jsxs)("main",{className:"flex flex-1 flex-col md:flex-row items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8",children:[(0,t.jsxs)("div",{className:"flex-1 grid gap-4 md:grid-cols-1",children:[t.jsx(f,{title:"Profile Details",children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[t.jsx(g,{title:"First Name",data:h.profile.firstName}),t.jsx(g,{title:"Last Name",data:h.profile.lastName}),t.jsx(g,{title:"Username",data:h.profile.username}),t.jsx(g,{title:"Email",data:h.profile.email}),t.jsx(g,{title:"Phone",data:h.profile.phone}),t.jsx(g,{title:"Role",data:h.profile.role})]})}),t.jsx(f,{title:"Professional Information",children:h.professional.map((e,r)=>(0,t.jsxs)(n().Fragment,{children:[r>0&&t.jsx("hr",{className:"my-4 border-t border-gray-600"}),(0,t.jsxs)("div",{className:"relative space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("p",{className:"text-lg font-bold",children:e.jobTitle}),t.jsx(l.C,{className:(0,u.S)(e.status),children:e.status})]}),(0,t.jsxs)("p",{className:"font-semibold",children:[e.company," \xb7 Full-time"]}),(0,t.jsxs)("p",{className:"text-muted-foreground",children:[new Date(e.workFrom).toLocaleDateString()," -"," ",new Date(e.workTo).toLocaleDateString()," \xb7"," ",(new Date(e.workTo).getFullYear()-new Date(e.workFrom).getFullYear())*12+(new Date(e.workTo).getMonth()-new Date(e.workFrom).getMonth())," ","mos"]}),t.jsx("p",{className:"text-muted-foreground",children:e.location}),t.jsx("p",{className:"text-muted-foreground",children:e.workDescription}),(0,t.jsxs)("p",{className:"text-muted-foreground",children:[t.jsx("strong",{children:"Reference:"})," ",e.referencePersonName,","," ",e.referencePersonContact]}),t.jsx("p",{className:"text-muted-foreground",children:t.jsx("a",{href:e.githubRepoLink,className:"text-blue-400",children:e.githubRepoLink})})]})]},r))}),t.jsx(f,{title:"Project Information",children:h.project.map((e,r)=>(0,t.jsxs)(n().Fragment,{children:[r>0&&t.jsx("hr",{className:"my-4 border-t border-gray-600"}),(0,t.jsxs)("div",{className:"relative space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("p",{className:"text-lg font-bold",children:e.projectName}),t.jsx(l.C,{className:(0,u.S)(e.status),children:e.status})]}),t.jsx("p",{className:"font-semibold",children:e.projectType}),(0,t.jsxs)("p",{className:"text-muted-foreground",children:[e.startDate," - ",e.endDate]}),t.jsx("p",{className:"text-muted-foreground",children:e.description}),(0,t.jsxs)("p",{className:"text-muted-foreground",children:[t.jsx("strong",{children:"Reference:"})," ",e.reference]}),t.jsx("p",{className:"text-muted-foreground",children:t.jsx("a",{href:e.githubRepoLink,className:"text-blue-400",children:e.githubRepoLink})}),(0,t.jsxs)("div",{className:"mt-4",children:[t.jsx("h3",{className:"text-lg font-semibold",children:"Technologies Used"}),t.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:e.technologiesUsed.split(", ").map((e,r)=>t.jsx(l.C,{className:"bg-gray-700 text-muted-foreground",children:e},r))})]})]})]},r))})]}),(0,t.jsxs)("div",{className:"flex flex-col gap-4 w-full md:w-1/3",children:[t.jsx(f,{title:"Education Information",children:h.education.map((e,r)=>(0,t.jsxs)(n().Fragment,{children:[r>0&&t.jsx("hr",{className:"my-4 border-t border-gray-600"}),(0,t.jsxs)("div",{className:"relative space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("p",{className:"text-lg font-bold",children:e.universityName}),t.jsx(l.C,{className:(0,u.S)(e.status),children:e.status})]}),t.jsx("p",{className:"font-semibold",children:e.degree}),t.jsx("p",{className:"text-muted-foreground",children:e.fieldOfStudy}),(0,t.jsxs)("p",{className:"text-muted-foreground",children:[e.startDate," - ",e.endDate]}),(0,t.jsxs)("p",{className:"text-white",children:[t.jsx("strong",{children:"Grade:"})," ",e.grade]})]})]},r))}),t.jsx(f,{title:"Skills and Domains",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-semibold",children:"Skills"}),t.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:h.profile.skills.map((e,r)=>t.jsx(l.C,{className:"bg-gray-700 text-muted-foreground",children:e.name},r))})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-semibold",children:"Domains"}),t.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:h.profile.domains.map((e,r)=>t.jsx(l.C,{className:"bg-gray-700 text-muted-foreground",children:e.name},r))})]})]})})]})]})]})]})},3512:(e,r,s)=>{"use strict";s.d(r,{o:()=>i});var t=s(10326),a=s(88307),n=s(41190);function i({placeholder:e="Search...",className:r=""}){return(0,t.jsxs)("div",{className:`relative ${r}`,children:[t.jsx(a.Z,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),t.jsx(n.I,{type:"search",placeholder:e,className:"w-full rounded-lg bg-background pl-8 md:w-[200px] lg:w-[336px]"})]})}},45175:(e,r,s)=>{"use strict";s.d(r,{$:()=>p,y:()=>m});var t=s(10326),a=s(95920),n=s(79635),i=s(47546),o=s(48705),l=s(6343);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,s(80851).Z)("ImagePlus",[["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7",key:"31hg93"}],["line",{x1:"16",x2:"22",y1:"5",y2:"5",key:"ez7e4s"}],["line",{x1:"19",x2:"19",y1:"2",y2:"8",key:"1gkr8c"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var d=s(46226);let m=[{href:"#",icon:t.jsx(d.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:t.jsx(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/settings/personal-info",icon:t.jsx(n.Z,{className:"h-5 w-5"}),label:"Personal Info"},{href:"/freelancer/settings/professional-info",icon:t.jsx(i.Z,{className:"h-5 w-5"}),label:"Professional Info"},{href:"/freelancer/settings/projects",icon:t.jsx(o.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"/freelancer/settings/education-info",icon:t.jsx(l.Z,{className:"h-5 w-5"}),label:"Education"},{href:"/freelancer/settings/resume",icon:t.jsx(c,{className:"h-5 w-5"}),label:"Portfolio"}],p=[]},54423:(e,r,s)=>{"use strict";s.d(r,{S:()=>t});let t=e=>{switch(e?.toLowerCase()){case"active":case"verified":case"added":return"bg-green-500 text-white";case"pending":return"bg-yellow-500 text-black";case"approved":return"bg-green-500 text-black";case"rejected":return"bg-red-500 text-black";default:return"bg-gray-500 text-white"}}},78237:(e,r,s)=>{"use strict";s.r(r),s.d(r,{$$typeof:()=>i,__esModule:()=>n,default:()=>o});var t=s(68570);let a=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\[freelancer_id]\freelancer-info\page.tsx`),{__esModule:n,$$typeof:i}=a;a.default;let o=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\[freelancer_id]\freelancer-info\page.tsx#default`)}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,4736,6499,8066],()=>s(2636));module.exports=t})();