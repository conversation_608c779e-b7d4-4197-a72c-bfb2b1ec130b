(()=>{var e={};e.id=6716,e.ids=[6716],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},51971:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>h,tree:()=>c}),r(95293),r(54302),r(12523);var s=r(23191),a=r(88716),n=r(37922),i=r.n(n),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,95293)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\auth\\login\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\auth\\login\\page.tsx"],d="/auth/login/page",p={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},22034:(e,t,r)=>{Promise.resolve().then(r.bind(r,53292))},77506:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},53292:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var s=r(10326),a=r(17577),n=r(35047),i=r(25842),o=r(12714),l=r(91216),c=r(77506),u=r(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,u.Z)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]]),p=(0,u.Z)("Chrome",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["line",{x1:"21.17",x2:"12",y1:"8",y2:"8",key:"a0cw5f"}],["line",{x1:"3.95",x2:"8.54",y1:"6.06",y2:"14",key:"1kftof"}],["line",{x1:"10.88",x2:"15.46",y1:"21.94",y2:"14",key:"1ymyh8"}]]);var h=r(46226),x=r(90434),f=r(56627),m=r(91664),g=r(41190),y=r(44794),v=r(40603),j=r(51223),b=r(4594),w=r(6260),D=r(27918);function C(){let e=(0,n.useRouter)(),t=(0,i.I0)(),[r,u]=(0,a.useState)(""),[C,N]=(0,a.useState)(""),[_,q]=(0,a.useState)(""),[P,E]=(0,a.useState)(!1),[R,k]=(0,a.useState)(!1),[I,F]=(0,a.useState)(!1),[M,O]=(0,a.useState)(!1),V=async(e,t)=>{try{let r="";return"business"===t.toLowerCase()?r="/business/kyc":"freelancer"===t.toLowerCase()&&(r=`/freelancer/${e}/kyc`),(await w.b.get(r)).data.status||null}catch(e){return console.error("KYC Fetch Error:",e),(0,f.Am)({variant:"destructive",title:"Error",description:"Failed to fetch KYC data. Please try again."}),null}},A=async s=>{s.preventDefault(),F(!0);try{let s=await w.b.get(`/public/user_email?user=${r}`);if(q(s.data.phone),s.data.phoneVerify)try{let s=await (0,j.pH)(r,C),{user:a,claims:n}=await (0,j.is)(s),i=await V(a.uid,n.type);i?t((0,b.av)({...a,type:n.type,kycStatus:i})):t((0,b.av)({...a,type:n.type})),e.replace(`/dashboard/${n.type}`),(0,f.Am)({title:"Login Successful",description:"You have successfully logged in."})}catch(e){(0,f.Am)({variant:"destructive",title:"Error",description:"Invalid Email or Password. Please try again."}),console.error(e.message)}else k(!0)}catch(e){(0,f.Am)({variant:"destructive",title:"Error",description:"Invalid Email or Password. Please try again."}),console.error(e.message)}finally{F(!1)}},S=async r=>{r.preventDefault(),O(!0);try{let r=await (0,j.bx)(),{user:s,claims:a}=await (0,j.is)(r);t((0,b.av)({...s,type:a.type})),e.replace(`/dashboard/${a.type}`),(0,f.Am)({title:"Login Successful",description:"You have successfully logged in with Google."})}catch(e){(0,f.Am)({variant:"destructive",title:"Error",description:"Failed to login with Google. Please try again."}),console.error(e.message)}finally{O(!1)}};return(0,s.jsxs)("div",{className:"w-full lg:grid lg:min-h-[600px] lg:grid-cols-2 xl:min-h-screen",children:[s.jsx("div",{className:"absolute left-10 top-10",children:s.jsx(v.T,{})}),s.jsx("div",{className:"flex items-center justify-center py-12",children:(0,s.jsxs)("div",{className:"mx-auto grid w-[350px] gap-6",children:[(0,s.jsxs)("div",{className:"grid gap-2 text-center",children:[s.jsx("h1",{className:"text-3xl font-bold",children:"Login"}),s.jsx("p",{className:"text-balance text-muted-foreground",children:"Enter your email below to login to your account"})]}),s.jsx("form",{onSubmit:A,children:(0,s.jsxs)("div",{className:"grid gap-4",children:[(0,s.jsxs)("div",{className:"grid gap-2",children:[s.jsx(y.Label,{htmlFor:"email",children:"Email"}),s.jsx(g.I,{id:"email",type:"email",placeholder:"<EMAIL>",value:r,onChange:e=>u(e.target.value),required:!0})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(y.Label,{htmlFor:"password",children:"Password"}),s.jsx(x.default,{href:"/auth/forgot-password",className:"ml-auto inline-block text-sm underline",children:"Forgot your password?"})]}),(0,s.jsxs)("div",{className:"relative",children:[s.jsx(g.I,{id:"password",type:P?"text":"password",onChange:e=>N(e.target.value),required:!0}),s.jsx("button",{type:"button",onClick:()=>{E(e=>!e)},className:"absolute right-2 top-1/2 transform -translate-y-1/2",children:P?s.jsx(o.Z,{className:"h-4 w-4"}):s.jsx(l.Z,{className:"h-4 w-4"})})]})]}),s.jsx(m.z,{type:"submit",className:"w-full",disabled:I,children:I?(0,s.jsxs)(s.Fragment,{children:[s.jsx(c.Z,{className:"mr-2 h-4 w-4 animate-spin"})," ","Logging in..."]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(d,{className:"mr-2 h-4 w-4"})," Login"]})}),(0,s.jsxs)(m.z,{variant:"outline",className:"w-full",disabled:M,onClick:S,children:[M?s.jsx(c.Z,{className:"mr-2 h-4 w-4 animate-spin"}):s.jsx(p,{className:"mr-2 h-4 w-4"})," ","Google Login"]})]})}),(0,s.jsxs)("div",{className:"mt-4 text-center text-sm",children:["Don't have an account?"," ",s.jsx(m.z,{variant:"outline",size:"sm",className:"ml-2",asChild:!0,children:s.jsx(x.default,{href:"/auth/sign-up/freelancer",children:"Sign up"})})]})]})}),s.jsx("div",{className:"hidden lg:block",children:s.jsx(h.default,{src:"/bg.png",alt:"Image",width:"1920",height:"1080",className:"h-full w-full object-cover dark:brightness-[0.2] dark:invert"})}),s.jsx(D.Z,{phoneNumber:_,isModalOpen:R,setIsModalOpen:k})]})}},95293:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>o});var s=r(68570);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\auth\login\page.tsx`),{__esModule:n,$$typeof:i}=a;a.default;let o=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\auth\login\page.tsx#default`)},37956:(e,t,r)=>{"use strict";r.d(t,{x8:()=>eo,VY:()=>ea,dk:()=>ei,Vq:()=>q,cZ:()=>S,t9:()=>O,aV:()=>es,h_:()=>er,fC:()=>ee,Dx:()=>en,xz:()=>et});var s=r(17577),a=r(82561),n=r(48051),i=r(93095),o=r(88957),l=r(52067),c=r(825),u=r(10441),d=r(83078),p=r(9815),h=r(77335),x=r(80699),f=r(17397),m=r(35664),g=r(10326),y=s.forwardRef((e,t)=>{let{children:r,...a}=e,n=s.Children.toArray(r),i=n.find(b);if(i){let e=i.props.children,r=n.map(t=>t!==i?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,g.jsx)(v,{...a,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,r):null})}return(0,g.jsx)(v,{...a,ref:t,children:r})});y.displayName="Slot";var v=s.forwardRef((e,t)=>{let{children:r,...a}=e;if(s.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return s.cloneElement(r,{...function(e,t){let r={...t};for(let s in t){let a=e[s],n=t[s];/^on[A-Z]/.test(s)?a&&n?r[s]=(...e)=>{n(...e),a(...e)}:a&&(r[s]=a):"style"===s?r[s]={...a,...n}:"className"===s&&(r[s]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props),ref:t?(0,n.F)(t,e):e})}return s.Children.count(r)>1?s.Children.only(null):null});v.displayName="SlotClone";var j=({children:e})=>(0,g.jsx)(g.Fragment,{children:e});function b(e){return s.isValidElement(e)&&e.type===j}var w="Dialog",[D,C]=(0,i.b)(w),[N,_]=D(w),q=e=>{let{__scopeDialog:t,children:r,open:a,defaultOpen:n,onOpenChange:i,modal:c=!0}=e,u=s.useRef(null),d=s.useRef(null),[p=!1,h]=(0,l.T)({prop:a,defaultProp:n,onChange:i});return(0,g.jsx)(N,{scope:t,triggerRef:u,contentRef:d,contentId:(0,o.M)(),titleId:(0,o.M)(),descriptionId:(0,o.M)(),open:p,onOpenChange:h,onOpenToggle:s.useCallback(()=>h(e=>!e),[h]),modal:c,children:r})};q.displayName=w;var P="DialogTrigger",E=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,i=_(P,r),o=(0,n.e)(t,i.triggerRef);return(0,g.jsx)(h.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":B(i.open),...s,ref:o,onClick:(0,a.M)(e.onClick,i.onOpenToggle)})});E.displayName=P;var R="DialogPortal",[k,I]=D(R,{forceMount:void 0}),F=e=>{let{__scopeDialog:t,forceMount:r,children:a,container:n}=e,i=_(R,t);return(0,g.jsx)(k,{scope:t,forceMount:r,children:s.Children.map(a,e=>(0,g.jsx)(p.z,{present:r||i.open,children:(0,g.jsx)(d.h,{asChild:!0,container:n,children:e})}))})};F.displayName=R;var M="DialogOverlay",O=s.forwardRef((e,t)=>{let r=I(M,e.__scopeDialog),{forceMount:s=r.forceMount,...a}=e,n=_(M,e.__scopeDialog);return n.modal?(0,g.jsx)(p.z,{present:s||n.open,children:(0,g.jsx)(V,{...a,ref:t})}):null});O.displayName=M;var V=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,a=_(M,r);return(0,g.jsx)(f.Z,{as:y,allowPinchZoom:!0,shards:[a.contentRef],children:(0,g.jsx)(h.WV.div,{"data-state":B(a.open),...s,ref:t,style:{pointerEvents:"auto",...s.style}})})}),A="DialogContent",S=s.forwardRef((e,t)=>{let r=I(A,e.__scopeDialog),{forceMount:s=r.forceMount,...a}=e,n=_(A,e.__scopeDialog);return(0,g.jsx)(p.z,{present:s||n.open,children:n.modal?(0,g.jsx)(W,{...a,ref:t}):(0,g.jsx)(Z,{...a,ref:t})})});S.displayName=A;var W=s.forwardRef((e,t)=>{let r=_(A,e.__scopeDialog),i=s.useRef(null),o=(0,n.e)(t,r.contentRef,i);return s.useEffect(()=>{let e=i.current;if(e)return(0,m.Ry)(e)},[]),(0,g.jsx)(L,{...e,ref:o,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,a.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,a.M)(e.onFocusOutside,e=>e.preventDefault())})}),Z=s.forwardRef((e,t)=>{let r=_(A,e.__scopeDialog),a=s.useRef(!1),n=s.useRef(!1);return(0,g.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(a.current||r.triggerRef.current?.focus(),t.preventDefault()),a.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(a.current=!0,"pointerdown"!==t.detail.originalEvent.type||(n.current=!0));let s=t.target;r.triggerRef.current?.contains(s)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),L=s.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:a,onOpenAutoFocus:i,onCloseAutoFocus:o,...l}=e,d=_(A,r),p=s.useRef(null),h=(0,n.e)(t,p);return(0,x.EW)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(u.M,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:i,onUnmountAutoFocus:o,children:(0,g.jsx)(c.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":B(d.open),...l,ref:h,onDismiss:()=>d.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(J,{titleId:d.titleId}),(0,g.jsx)(Q,{contentRef:p,descriptionId:d.descriptionId})]})]})}),$="DialogTitle",z=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,a=_($,r);return(0,g.jsx)(h.WV.h2,{id:a.titleId,...s,ref:t})});z.displayName=$;var G="DialogDescription",T=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,a=_(G,r);return(0,g.jsx)(h.WV.p,{id:a.descriptionId,...s,ref:t})});T.displayName=G;var U="DialogClose",Y=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,n=_(U,r);return(0,g.jsx)(h.WV.button,{type:"button",...s,ref:t,onClick:(0,a.M)(e.onClick,()=>n.onOpenChange(!1))})});function B(e){return e?"open":"closed"}Y.displayName=U;var K="DialogTitleWarning",[H,X]=(0,i.k)(K,{contentName:A,titleName:$,docsSlug:"dialog"}),J=({titleId:e})=>{let t=X(K),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return s.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},Q=({contentRef:e,descriptionId:t})=>{let r=X("DialogDescriptionWarning"),a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return s.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(a)},[a,e,t]),null},ee=q,et=E,er=F,es=O,ea=S,en=z,ei=T,eo=Y}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,4198,6034,4718,6226,4736,9169],()=>r(51971));module.exports=s})();