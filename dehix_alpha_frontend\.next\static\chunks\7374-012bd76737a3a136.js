"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7374],{22468:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},28165:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},92699:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},38296:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(33480).Z)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},87138:function(e,t,n){n.d(t,{default:function(){return o.a}});var r=n(231),o=n.n(r)},844:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return r}}),n(18157);let r=function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25944:function(e,t,n){function r(e,t,n,r){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return r}}),n(18157),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},231:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return w}});let r=n(99920),o=n(57437),a=r._(n(2265)),l=n(98016),i=n(18029),u=n(41142),c=n(43461),s=n(844),d=n(60291),f=n(44467),p=n(53106),h=n(25944),m=n(4897),g=n(51507),v=new Set;function y(e,t,n,r,o,a){if("undefined"!=typeof window&&(a||(0,i.isLocalURL)(t))){if(!r.bypassPrefetchedCheck){let o=t+"%"+n+"%"+(void 0!==r.locale?r.locale:"locale"in e?e.locale:void 0);if(v.has(o))return;v.add(o)}(async()=>a?e.prefetch(t,o):e.prefetch(t,n,r))().catch(e=>{})}}function b(e){return"string"==typeof e?e:(0,u.formatUrl)(e)}let w=a.default.forwardRef(function(e,t){let n,r;let{href:u,as:v,children:w,prefetch:x=null,passHref:M,replace:R,shallow:C,scroll:j,locale:_,onClick:P,onMouseEnter:E,onTouchStart:S,legacyBehavior:O=!1,...k}=e;n=w,O&&("string"==typeof n||"number"==typeof n)&&(n=(0,o.jsx)("a",{children:n}));let N=a.default.useContext(d.RouterContext),I=a.default.useContext(f.AppRouterContext),T=null!=N?N:I,D=!N,A=!1!==x,L=null===x?g.PrefetchKind.AUTO:g.PrefetchKind.FULL,{href:F,as:W}=a.default.useMemo(()=>{if(!N){let e=b(u);return{href:e,as:v?b(v):e}}let[e,t]=(0,l.resolveHref)(N,u,!0);return{href:e,as:v?(0,l.resolveHref)(N,v):t||e}},[N,u,v]),U=a.default.useRef(F),K=a.default.useRef(W);O&&(r=a.default.Children.only(n));let V=O?r&&"object"==typeof r&&r.ref:t,[z,B,Z]=(0,p.useIntersection)({rootMargin:"200px"}),G=a.default.useCallback(e=>{(K.current!==W||U.current!==F)&&(Z(),K.current=W,U.current=F),z(e),V&&("function"==typeof V?V(e):"object"==typeof V&&(V.current=e))},[W,V,F,Z,z]);a.default.useEffect(()=>{T&&B&&A&&y(T,F,W,{locale:_},{kind:L},D)},[W,F,B,_,A,null==N?void 0:N.locale,T,D,L]);let q={ref:G,onClick(e){O||"function"!=typeof P||P(e),O&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),T&&!e.defaultPrevented&&function(e,t,n,r,o,l,u,c,s){let{nodeName:d}=e.currentTarget;if("A"===d.toUpperCase()&&(function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!s&&!(0,i.isLocalURL)(n)))return;e.preventDefault();let f=()=>{let e=null==u||u;"beforePopState"in t?t[o?"replace":"push"](n,r,{shallow:l,locale:c,scroll:e}):t[o?"replace":"push"](r||n,{scroll:e})};s?a.default.startTransition(f):f()}(e,T,F,W,R,C,j,_,D)},onMouseEnter(e){O||"function"!=typeof E||E(e),O&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),T&&(A||!D)&&y(T,F,W,{locale:_,priority:!0,bypassPrefetchedCheck:!0},{kind:L},D)},onTouchStart:function(e){O||"function"!=typeof S||S(e),O&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),T&&(A||!D)&&y(T,F,W,{locale:_,priority:!0,bypassPrefetchedCheck:!0},{kind:L},D)}};if((0,c.isAbsoluteUrl)(W))q.href=W;else if(!O||M||"a"===r.type&&!("href"in r.props)){let e=void 0!==_?_:null==N?void 0:N.locale,t=(null==N?void 0:N.isLocaleDomain)&&(0,h.getDomainLocale)(W,e,null==N?void 0:N.locales,null==N?void 0:N.domainLocales);q.href=t||(0,m.addBasePath)((0,s.addLocale)(W,e,null==N?void 0:N.defaultLocale))}return O?a.default.cloneElement(r,q):(0,o.jsx)("a",{...k,...q,children:n})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49189:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{cancelIdleCallback:function(){return r},requestIdleCallback:function(){return n}});let n="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},r="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98016:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return d}});let r=n(18323),o=n(41142),a=n(45519),l=n(43461),i=n(18157),u=n(18029),c=n(59195),s=n(80020);function d(e,t,n){let d;let f="string"==typeof t?t:(0,o.formatWithValidation)(t),p=f.match(/^[a-zA-Z]{1,}:\/\//),h=p?f.slice(p[0].length):f;if((h.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+f+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,l.normalizeRepeatedSlashes)(h);f=(p?p[0]:"")+t}if(!(0,u.isLocalURL)(f))return n?[f]:f;try{d=new URL(f.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(f,d);e.pathname=(0,i.normalizePathTrailingSlash)(e.pathname);let t="";if((0,c.isDynamicRoute)(e.pathname)&&e.searchParams&&n){let n=(0,r.searchParamsToUrlQuery)(e.searchParams),{result:l,params:i}=(0,s.interpolateAs)(e.pathname,e.pathname,n);l&&(t=(0,o.formatWithValidation)({pathname:l,hash:e.hash,query:(0,a.omit)(n,i)}))}let l=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return n?[l,t||l]:l}catch(e){return n?[f]:f}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53106:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return u}});let r=n(2265),o=n(49189),a="function"==typeof IntersectionObserver,l=new Map,i=[];function u(e){let{rootRef:t,rootMargin:n,disabled:u}=e,c=u||!a,[s,d]=(0,r.useState)(!1),f=(0,r.useRef)(null),p=(0,r.useCallback)(e=>{f.current=e},[]);return(0,r.useEffect)(()=>{if(a){if(c||s)return;let e=f.current;if(e&&e.tagName)return function(e,t,n){let{id:r,observer:o,elements:a}=function(e){let t;let n={root:e.root||null,margin:e.rootMargin||""},r=i.find(e=>e.root===n.root&&e.margin===n.margin);if(r&&(t=l.get(r)))return t;let o=new Map;return t={id:n,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),n=e.isIntersecting||e.intersectionRatio>0;t&&n&&t(n)})},e),elements:o},i.push(n),l.set(n,t),t}(n);return a.set(e,t),o.observe(e),function(){if(a.delete(e),o.unobserve(e),0===a.size){o.disconnect(),l.delete(r);let e=i.findIndex(e=>e.root===r.root&&e.margin===r.margin);e>-1&&i.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:n})}else if(!s){let e=(0,o.requestIdleCallback)(()=>d(!0));return()=>(0,o.cancelIdleCallback)(e)}},[c,n,t,s,f.current]),[p,s,(0,r.useCallback)(()=>{d(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},81943:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let n=/[|\\{}()[\]^$+*?.-]/,r=/[|\\{}()[\]^$+*?.-]/g;function o(e){return n.test(e)?e.replace(r,"\\$&"):e}},60291:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return r}});let r=n(99920)._(n(2265)).default.createContext(null)},41142:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return i},urlObjectKeys:function(){return l}});let r=n(41452)._(n(18323)),o=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:n}=e,a=e.protocol||"",l=e.pathname||"",i=e.hash||"",u=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:n&&(c=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(c+=":"+e.port)),u&&"object"==typeof u&&(u=String(r.urlQueryToSearchParams(u)));let s=e.search||u&&"?"+u||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||o.test(a))&&!1!==c?(c="//"+(c||""),l&&"/"!==l[0]&&(l="/"+l)):c||(c=""),i&&"#"!==i[0]&&(i="#"+i),s&&"?"!==s[0]&&(s="?"+s),""+a+c+(l=l.replace(/[?#]/g,encodeURIComponent))+(s=s.replace("#","%23"))+i}let l=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return a(e)}},59195:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getSortedRoutes:function(){return r.getSortedRoutes},isDynamicRoute:function(){return o.isDynamicRoute}});let r=n(49089),o=n(28083)},80020:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return a}});let r=n(41533),o=n(63169);function a(e,t,n){let a="",l=(0,o.getRouteRegex)(e),i=l.groups,u=(t!==e?(0,r.getRouteMatcher)(l)(t):"")||n;a=e;let c=Object.keys(i);return c.every(e=>{let t=u[e]||"",{repeat:n,optional:r}=i[e],o="["+(n?"...":"")+e+"]";return r&&(o=(t?"":"/")+"["+o+"]"),n&&!Array.isArray(t)&&(t=[t]),(r||e in u)&&(a=a.replace(o,n?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(a=""),{params:c,result:a}}},28083:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return a}});let r=n(82269),o=/\/\[[^/]+?\](?=\/|$)/;function a(e){return(0,r.isInterceptionRouteAppPath)(e)&&(e=(0,r.extractInterceptionRouteInformation)(e).interceptedRoute),o.test(e)}},18029:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let r=n(43461),o=n(49404);function a(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,o.hasBasePath)(n.pathname)}catch(e){return!1}}},45519:function(e,t){function n(e,t){let n={};return Object.keys(e).forEach(r=>{t.includes(r)||(n[r]=e[r])}),n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return n}})},18323:function(e,t){function n(e){let t={};return e.forEach((e,n)=>{void 0===t[n]?t[n]=e:Array.isArray(t[n])?t[n].push(e):t[n]=[t[n],e]}),t}function r(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[n,o]=e;Array.isArray(o)?o.forEach(e=>t.append(n,r(e))):t.set(n,r(o))}),t}function a(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,n)=>e.append(n,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},41533:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let r=n(43461);function o(e){let{re:t,groups:n}=e;return e=>{let o=t.exec(e);if(!o)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw new r.DecodeError("failed to decode param")}},l={};return Object.keys(n).forEach(e=>{let t=n[e],r=o[t.pos];void 0!==r&&(l[e]=~r.indexOf("/")?r.split("/").map(e=>a(e)):t.repeat?[a(r)]:a(r))}),l}}},63169:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getNamedMiddlewareRegex:function(){return f},getNamedRouteRegex:function(){return d},getRouteRegex:function(){return u}});let r=n(82269),o=n(81943),a=n(67741);function l(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let n=e.startsWith("...");return n&&(e=e.slice(3)),{key:e,repeat:n,optional:t}}function i(e){let t=(0,a.removeTrailingSlash)(e).slice(1).split("/"),n={},i=1;return{parameterizedRoute:t.map(e=>{let t=r.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&a){let{key:e,optional:r,repeat:u}=l(a[1]);return n[e]={pos:i++,repeat:u,optional:r},"/"+(0,o.escapeStringRegexp)(t)+"([^/]+?)"}if(!a)return"/"+(0,o.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:r}=l(a[1]);return n[e]={pos:i++,repeat:t,optional:r},t?r?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:n}}function u(e){let{parameterizedRoute:t,groups:n}=i(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:n}}function c(e){let{interceptionMarker:t,getSafeRouteKey:n,segment:r,routeKeys:a,keyPrefix:i}=e,{key:u,optional:c,repeat:s}=l(r),d=u.replace(/\W/g,"");i&&(d=""+i+d);let f=!1;(0===d.length||d.length>30)&&(f=!0),isNaN(parseInt(d.slice(0,1)))||(f=!0),f&&(d=n()),i?a[d]=""+i+u:a[d]=u;let p=t?(0,o.escapeStringRegexp)(t):"";return s?c?"(?:/"+p+"(?<"+d+">.+?))?":"/"+p+"(?<"+d+">.+?)":"/"+p+"(?<"+d+">[^/]+?)"}function s(e,t){let n;let l=(0,a.removeTrailingSlash)(e).slice(1).split("/"),i=(n=0,()=>{let e="",t=++n;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),u={};return{namedParameterizedRoute:l.map(e=>{let n=r.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);if(n&&a){let[n]=e.split(a[0]);return c({getSafeRouteKey:i,interceptionMarker:n,segment:a[1],routeKeys:u,keyPrefix:t?"nxtI":void 0})}return a?c({getSafeRouteKey:i,segment:a[1],routeKeys:u,keyPrefix:t?"nxtP":void 0}):"/"+(0,o.escapeStringRegexp)(e)}).join(""),routeKeys:u}}function d(e,t){let n=s(e,t);return{...u(e),namedRegex:"^"+n.namedParameterizedRoute+"(?:/)?$",routeKeys:n.routeKeys}}function f(e,t){let{parameterizedRoute:n}=i(e),{catchAll:r=!0}=t;if("/"===n)return{namedRegex:"^/"+(r?".*":"")+"$"};let{namedParameterizedRoute:o}=s(e,!1);return{namedRegex:"^"+o+(r?"(?:(/.*)?)":"")+"$"}}},49089:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSortedRoutes",{enumerable:!0,get:function(){return r}});class n{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let n=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&n.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").');n.unshift(t)}return null!==this.restSlugName&&n.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&n.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),n}_insert(e,t,r){if(0===e.length){this.placeholder=!1;return}if(r)throw Error("Catch-all must be the last part of the URL.");let o=e[0];if(o.startsWith("[")&&o.endsWith("]")){let n=o.slice(1,-1),l=!1;if(n.startsWith("[")&&n.endsWith("]")&&(n=n.slice(1,-1),l=!0),n.startsWith("...")&&(n=n.substring(3),r=!0),n.startsWith("[")||n.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+n+"').");if(n.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+n+"').");function a(e,n){if(null!==e&&e!==n)throw Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+n+"').");t.forEach(e=>{if(e===n)throw Error('You cannot have the same slug name "'+n+'" repeat within a single dynamic path');if(e.replace(/\W/g,"")===o.replace(/\W/g,""))throw Error('You cannot have the slug names "'+e+'" and "'+n+'" differ only by non-word symbols within a single dynamic path')}),t.push(n)}if(r){if(l){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).');a(this.optionalRestSlugName,n),this.optionalRestSlugName=n,o="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").');a(this.restSlugName,n),this.restSlugName=n,o="[...]"}}else{if(l)throw Error('Optional route parameters are not yet supported ("'+e[0]+'").');a(this.slugName,n),this.slugName=n,o="[]"}}this.children.has(o)||this.children.set(o,new n),this.children.get(o)._insert(e.slice(1),t,r)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function r(e){let t=new n;return e.forEach(e=>t.insert(e)),t.smoosh()}},43461:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return v},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return u},getLocationOrigin:function(){return l},getURL:function(){return i},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return s},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),a=0;a<r;a++)o[a]=arguments[a];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>o.test(e);function l(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function i(){let{href:e}=window.location,t=l();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function s(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&c(n))return r;if(!r)throw Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.');return r}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},921:function(e,t,n){n.d(t,{B:function(){return d}});var r=n(2265),o=n(98324),a=n(1584),l=n(57437),i=r.forwardRef((e,t)=>{let{children:n,...o}=e,a=r.Children.toArray(n),i=a.find(s);if(i){let e=i.props.children,n=a.map(t=>t!==i?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,l.jsx)(u,{...o,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,l.jsx)(u,{...o,ref:t,children:n})});i.displayName="Slot";var u=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e,l;let i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref;return r.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let o=e[r],a=t[r];/^on[A-Z]/.test(r)?o&&a?n[r]=(...e)=>{a(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...a}:"className"===r&&(n[r]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props),ref:t?(0,a.F)(t,i):i})}return r.Children.count(n)>1?r.Children.only(null):null});u.displayName="SlotClone";var c=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});function s(e){return r.isValidElement(e)&&e.type===c}function d(e){let t=e+"CollectionProvider",[n,u]=(0,o.b)(t),[c,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),a=r.useRef(new Map).current;return(0,l.jsx)(c,{scope:t,itemMap:a,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(f,n),u=(0,a.e)(t,o.collectionRef);return(0,l.jsx)(i,{ref:u,children:r})});p.displayName=f;let h=e+"CollectionItemSlot",m="data-radix-collection-item",g=r.forwardRef((e,t)=>{let{scope:n,children:o,...u}=e,c=r.useRef(null),d=(0,a.e)(t,c),f=s(h,n);return r.useEffect(()=>(f.itemMap.set(c,{ref:c,...u}),()=>void f.itemMap.delete(c))),(0,l.jsx)(i,{[m]:"",ref:d,children:o})});return g.displayName=h,[{Provider:d,Slot:p,ItemSlot:g},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}},87513:function(e,t,n){n.d(t,{gm:function(){return a}});var r=n(2265);n(57437);var o=r.createContext(void 0);function a(e){let t=r.useContext(o);return e||t||"ltr"}},86610:function(e,t,n){n.d(t,{oC:function(){return e7},VY:function(){return e9},ZA:function(){return e8},ck:function(){return e6},wU:function(){return tn},__:function(){return e5},Uv:function(){return e3},Ee:function(){return te},Rk:function(){return tt},fC:function(){return e2},Z0:function(){return tr},Tr:function(){return to},tu:function(){return tl},fF:function(){return ta},xz:function(){return e4}});var r=n(2265),o=n(78149),a=n(1584),l=n(98324),i=n(91715),u=n(18676),c=n(921),s=n(87513),d=n(53938),f=n(20589),p=n(80467),h=n(53201),m=n(25510),g=n(56935),v=n(31383),y=n(53398),b=n(57437),w=r.forwardRef((e,t)=>{let{children:n,...o}=e,a=r.Children.toArray(n),l=a.find(R);if(l){let e=l.props.children,n=a.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,b.jsx)(x,{...o,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,b.jsx)(x,{...o,ref:t,children:n})});w.displayName="Slot";var x=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e,l;let i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref;return r.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let o=e[r],a=t[r];/^on[A-Z]/.test(r)?o&&a?n[r]=(...e)=>{a(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...a}:"className"===r&&(n[r]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props),ref:t?(0,a.F)(t,i):i})}return r.Children.count(n)>1?r.Children.only(null):null});x.displayName="SlotClone";var M=({children:e})=>(0,b.jsx)(b.Fragment,{children:e});function R(e){return r.isValidElement(e)&&e.type===M}var C=n(75137),j=n(78369),_=n(9219),P=["Enter"," "],E=["ArrowUp","PageDown","End"],S=["ArrowDown","PageUp","Home",...E],O={ltr:[...P,"ArrowRight"],rtl:[...P,"ArrowLeft"]},k={ltr:["ArrowLeft"],rtl:["ArrowRight"]},N="Menu",[I,T,D]=(0,c.B)(N),[A,L]=(0,l.b)(N,[D,m.D7,y.Pc]),F=(0,m.D7)(),W=(0,y.Pc)(),[U,K]=A(N),[V,z]=A(N),B=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:a,onOpenChange:l,modal:i=!0}=e,u=F(t),[c,d]=r.useState(null),f=r.useRef(!1),p=(0,C.W)(l),h=(0,s.gm)(a);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,b.jsx)(m.fC,{...u,children:(0,b.jsx)(U,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:d,children:(0,b.jsx)(V,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:i,children:o})})})};B.displayName=N;var Z=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=F(n);return(0,b.jsx)(m.ee,{...o,...r,ref:t})});Z.displayName="MenuAnchor";var G="MenuPortal",[q,Y]=A(G,{forceMount:void 0}),$=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,a=K(G,t);return(0,b.jsx)(q,{scope:t,forceMount:n,children:(0,b.jsx)(v.z,{present:n||a.open,children:(0,b.jsx)(g.h,{asChild:!0,container:o,children:r})})})};$.displayName=G;var H="MenuContent",[X,Q]=A(H),J=r.forwardRef((e,t)=>{let n=Y(H,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=K(H,e.__scopeMenu),l=z(H,e.__scopeMenu);return(0,b.jsx)(I.Provider,{scope:e.__scopeMenu,children:(0,b.jsx)(v.z,{present:r||a.open,children:(0,b.jsx)(I.Slot,{scope:e.__scopeMenu,children:l.modal?(0,b.jsx)(ee,{...o,ref:t}):(0,b.jsx)(et,{...o,ref:t})})})})}),ee=r.forwardRef((e,t)=>{let n=K(H,e.__scopeMenu),l=r.useRef(null),i=(0,a.e)(t,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,j.Ry)(e)},[]),(0,b.jsx)(en,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),et=r.forwardRef((e,t)=>{let n=K(H,e.__scopeMenu);return(0,b.jsx)(en,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),en=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:l=!1,trapFocus:i,onOpenAutoFocus:u,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:h,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:x,onInteractOutside:M,onDismiss:R,disableOutsideScroll:C,...j}=e,P=K(H,n),O=z(H,n),k=F(n),N=W(n),I=T(n),[D,A]=r.useState(null),L=r.useRef(null),U=(0,a.e)(t,L,P.onContentChange),V=r.useRef(0),B=r.useRef(""),Z=r.useRef(0),G=r.useRef(null),q=r.useRef("right"),Y=r.useRef(0),$=C?_.Z:r.Fragment,Q=e=>{var t,n;let r=B.current+e,o=I().filter(e=>!e.disabled),a=document.activeElement,l=null===(t=o.find(e=>e.ref.current===a))||void 0===t?void 0:t.textValue,i=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(o.map(e=>e.textValue),r,l),u=null===(n=o.find(e=>e.textValue===i))||void 0===n?void 0:n.ref.current;!function e(t){B.current=t,window.clearTimeout(V.current),""!==t&&(V.current=window.setTimeout(()=>e(""),1e3))}(r),u&&setTimeout(()=>u.focus())};r.useEffect(()=>()=>window.clearTimeout(V.current),[]),(0,f.EW)();let J=r.useCallback(e=>{var t,n,r;return q.current===(null===(t=G.current)||void 0===t?void 0:t.side)&&!!(r=null===(n=G.current)||void 0===n?void 0:n.area)&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let l=t[e].x,i=t[e].y,u=t[a].x,c=t[a].y;i>r!=c>r&&n<(u-l)*(r-i)/(c-i)+l&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)},[]);return(0,b.jsx)(X,{scope:n,searchRef:B,onItemEnter:r.useCallback(e=>{J(e)&&e.preventDefault()},[J]),onItemLeave:r.useCallback(e=>{var t;J(e)||(null===(t=L.current)||void 0===t||t.focus(),A(null))},[J]),onTriggerLeave:r.useCallback(e=>{J(e)&&e.preventDefault()},[J]),pointerGraceTimerRef:Z,onPointerGraceIntentChange:r.useCallback(e=>{G.current=e},[]),children:(0,b.jsx)($,{...C?{as:w,allowPinchZoom:!0}:void 0,children:(0,b.jsx)(p.M,{asChild:!0,trapped:i,onMountAutoFocus:(0,o.M)(u,e=>{var t;e.preventDefault(),null===(t=L.current)||void 0===t||t.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,b.jsx)(d.XB,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:x,onInteractOutside:M,onDismiss:R,children:(0,b.jsx)(y.fC,{asChild:!0,...N,dir:O.dir,orientation:"vertical",loop:l,currentTabStopId:D,onCurrentTabStopIdChange:A,onEntryFocus:(0,o.M)(h,e=>{O.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,b.jsx)(m.VY,{role:"menu","aria-orientation":"vertical","data-state":eO(P.open),"data-radix-menu-content":"",dir:O.dir,...k,...j,ref:U,style:{outline:"none",...j.style},onKeyDown:(0,o.M)(j.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&Q(e.key));let o=L.current;if(e.target!==o||!S.includes(e.key))return;e.preventDefault();let a=I().filter(e=>!e.disabled).map(e=>e.ref.current);E.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(V.current),B.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,eI(e=>{let t=e.target,n=Y.current!==e.clientX;if(e.currentTarget.contains(t)&&n){let t=e.clientX>Y.current?"right":"left";q.current=t,Y.current=e.clientX}}))})})})})})})});J.displayName=H;var er=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,b.jsx)(u.WV.div,{role:"group",...r,ref:t})});er.displayName="MenuGroup";var eo=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,b.jsx)(u.WV.div,{...r,ref:t})});eo.displayName="MenuLabel";var ea="MenuItem",el="menu.itemSelect",ei=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:l,...i}=e,c=r.useRef(null),s=z(ea,e.__scopeMenu),d=Q(ea,e.__scopeMenu),f=(0,a.e)(t,c),p=r.useRef(!1);return(0,b.jsx)(eu,{...i,ref:f,disabled:n,onClick:(0,o.M)(e.onClick,()=>{let e=c.current;if(!n&&e){let t=new CustomEvent(el,{bubbles:!0,cancelable:!0});e.addEventListener(el,e=>null==l?void 0:l(e),{once:!0}),(0,u.jH)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{var n;null===(n=e.onPointerDown)||void 0===n||n.call(e,t),p.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{var t;p.current||null===(t=e.currentTarget)||void 0===t||t.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!n&&(!t||" "!==e.key)&&P.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ei.displayName=ea;var eu=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:l=!1,textValue:i,...c}=e,s=Q(ea,n),d=W(n),f=r.useRef(null),p=(0,a.e)(t,f),[h,m]=r.useState(!1),[g,v]=r.useState("");return r.useEffect(()=>{let e=f.current;if(e){var t;v((null!==(t=e.textContent)&&void 0!==t?t:"").trim())}},[c.children]),(0,b.jsx)(I.ItemSlot,{scope:n,disabled:l,textValue:null!=i?i:g,children:(0,b.jsx)(y.ck,{asChild:!0,...d,focusable:!l,children:(0,b.jsx)(u.WV.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...c,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,eI(e=>{l?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eI(e=>s.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>m(!0)),onBlur:(0,o.M)(e.onBlur,()=>m(!1))})})})}),ec=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...a}=e;return(0,b.jsx)(ev,{scope:e.__scopeMenu,checked:n,children:(0,b.jsx)(ei,{role:"menuitemcheckbox","aria-checked":ek(n)?"mixed":n,...a,ref:t,"data-state":eN(n),onSelect:(0,o.M)(a.onSelect,()=>null==r?void 0:r(!!ek(n)||!n),{checkForDefaultPrevented:!1})})})});ec.displayName="MenuCheckboxItem";var es="MenuRadioGroup",[ed,ef]=A(es,{value:void 0,onValueChange:()=>{}}),ep=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,a=(0,C.W)(r);return(0,b.jsx)(ed,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,b.jsx)(er,{...o,ref:t})})});ep.displayName=es;var eh="MenuRadioItem",em=r.forwardRef((e,t)=>{let{value:n,...r}=e,a=ef(eh,e.__scopeMenu),l=n===a.value;return(0,b.jsx)(ev,{scope:e.__scopeMenu,checked:l,children:(0,b.jsx)(ei,{role:"menuitemradio","aria-checked":l,...r,ref:t,"data-state":eN(l),onSelect:(0,o.M)(r.onSelect,()=>{var e;return null===(e=a.onValueChange)||void 0===e?void 0:e.call(a,n)},{checkForDefaultPrevented:!1})})})});em.displayName=eh;var eg="MenuItemIndicator",[ev,ey]=A(eg,{checked:!1}),eb=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,a=ey(eg,n);return(0,b.jsx)(v.z,{present:r||ek(a.checked)||!0===a.checked,children:(0,b.jsx)(u.WV.span,{...o,ref:t,"data-state":eN(a.checked)})})});eb.displayName=eg;var ew=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,b.jsx)(u.WV.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ew.displayName="MenuSeparator";var ex=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=F(n);return(0,b.jsx)(m.Eh,{...o,...r,ref:t})});ex.displayName="MenuArrow";var eM="MenuSub",[eR,eC]=A(eM),ej=e=>{let{__scopeMenu:t,children:n,open:o=!1,onOpenChange:a}=e,l=K(eM,t),i=F(t),[u,c]=r.useState(null),[s,d]=r.useState(null),f=(0,C.W)(a);return r.useEffect(()=>(!1===l.open&&f(!1),()=>f(!1)),[l.open,f]),(0,b.jsx)(m.fC,{...i,children:(0,b.jsx)(U,{scope:t,open:o,onOpenChange:f,content:s,onContentChange:d,children:(0,b.jsx)(eR,{scope:t,contentId:(0,h.M)(),triggerId:(0,h.M)(),trigger:u,onTriggerChange:c,children:n})})})};ej.displayName=eM;var e_="MenuSubTrigger",eP=r.forwardRef((e,t)=>{let n=K(e_,e.__scopeMenu),l=z(e_,e.__scopeMenu),i=eC(e_,e.__scopeMenu),u=Q(e_,e.__scopeMenu),c=r.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,b.jsx)(Z,{asChild:!0,...f,children:(0,b.jsx)(eu,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":i.contentId,"data-state":eO(n.open),...e,ref:(0,a.F)(t,i.onTriggerChange),onClick:t=>{var r;null===(r=e.onClick)||void 0===r||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,eI(t=>{u.onItemEnter(t),t.defaultPrevented||e.disabled||n.open||c.current||(u.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eI(e=>{var t,r;p();let o=null===(t=n.content)||void 0===t?void 0:t.getBoundingClientRect();if(o){let t=null===(r=n.content)||void 0===r?void 0:r.dataset.side,a="right"===t,l=o[a?"left":"right"],i=o[a?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:i,y:o.top},{x:i,y:o.bottom},{x:l,y:o.bottom}],side:t}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;if(!e.disabled&&(!r||" "!==t.key)&&O[l.dir].includes(t.key)){var o;n.onOpenChange(!0),null===(o=n.content)||void 0===o||o.focus(),t.preventDefault()}})})})});eP.displayName=e_;var eE="MenuSubContent",eS=r.forwardRef((e,t)=>{let n=Y(H,e.__scopeMenu),{forceMount:l=n.forceMount,...i}=e,u=K(H,e.__scopeMenu),c=z(H,e.__scopeMenu),s=eC(eE,e.__scopeMenu),d=r.useRef(null),f=(0,a.e)(t,d);return(0,b.jsx)(I.Provider,{scope:e.__scopeMenu,children:(0,b.jsx)(v.z,{present:l||u.open,children:(0,b.jsx)(I.Slot,{scope:e.__scopeMenu,children:(0,b.jsx)(en,{id:s.contentId,"aria-labelledby":s.triggerId,...i,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;c.isUsingKeyboardRef.current&&(null===(t=d.current)||void 0===t||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==s.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=k[c.dir].includes(e.key);if(t&&n){var r;u.onOpenChange(!1),null===(r=s.trigger)||void 0===r||r.focus(),e.preventDefault()}})})})})})});function eO(e){return e?"open":"closed"}function ek(e){return"indeterminate"===e}function eN(e){return ek(e)?"indeterminate":e?"checked":"unchecked"}function eI(e){return t=>"mouse"===t.pointerType?e(t):void 0}eS.displayName=eE;var eT="DropdownMenu",[eD,eA]=(0,l.b)(eT,[L]),eL=L(),[eF,eW]=eD(eT),eU=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:a,defaultOpen:l,onOpenChange:u,modal:c=!0}=e,s=eL(t),d=r.useRef(null),[f=!1,p]=(0,i.T)({prop:a,defaultProp:l,onChange:u});return(0,b.jsx)(eF,{scope:t,triggerId:(0,h.M)(),triggerRef:d,contentId:(0,h.M)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,b.jsx)(B,{...s,open:f,onOpenChange:p,dir:o,modal:c,children:n})})};eU.displayName=eT;var eK="DropdownMenuTrigger",eV=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...l}=e,i=eW(eK,n),c=eL(n);return(0,b.jsx)(Z,{asChild:!0,...c,children:(0,b.jsx)(u.WV.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...l,ref:(0,a.F)(t,i.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{r||0!==e.button||!1!==e.ctrlKey||(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eV.displayName=eK;var ez=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eL(t);return(0,b.jsx)($,{...r,...n})};ez.displayName="DropdownMenuPortal";var eB="DropdownMenuContent",eZ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,l=eW(eB,n),i=eL(n),u=r.useRef(!1);return(0,b.jsx)(J,{id:l.contentId,"aria-labelledby":l.triggerId,...i,...a,ref:t,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var t;u.current||null===(t=l.triggerRef.current)||void 0===t||t.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!l.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eZ.displayName=eB;var eG=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,b.jsx)(er,{...o,...r,ref:t})});eG.displayName="DropdownMenuGroup";var eq=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,b.jsx)(eo,{...o,...r,ref:t})});eq.displayName="DropdownMenuLabel";var eY=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,b.jsx)(ei,{...o,...r,ref:t})});eY.displayName="DropdownMenuItem";var e$=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,b.jsx)(ec,{...o,...r,ref:t})});e$.displayName="DropdownMenuCheckboxItem";var eH=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,b.jsx)(ep,{...o,...r,ref:t})});eH.displayName="DropdownMenuRadioGroup";var eX=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,b.jsx)(em,{...o,...r,ref:t})});eX.displayName="DropdownMenuRadioItem";var eQ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,b.jsx)(eb,{...o,...r,ref:t})});eQ.displayName="DropdownMenuItemIndicator";var eJ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,b.jsx)(ew,{...o,...r,ref:t})});eJ.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,b.jsx)(ex,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var e0=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,b.jsx)(eP,{...o,...r,ref:t})});e0.displayName="DropdownMenuSubTrigger";var e1=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,b.jsx)(eS,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e1.displayName="DropdownMenuSubContent";var e2=eU,e4=eV,e3=ez,e9=eZ,e8=eG,e5=eq,e6=eY,e7=e$,te=eH,tt=eX,tn=eQ,tr=eJ,to=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:a}=e,l=eL(t),[u=!1,c]=(0,i.T)({prop:r,defaultProp:a,onChange:o});return(0,b.jsx)(ej,{...l,open:u,onOpenChange:c,children:n})},ta=e0,tl=e1},53398:function(e,t,n){n.d(t,{Pc:function(){return x},ck:function(){return k},fC:function(){return O}});var r=n(2265),o=n(78149),a=n(921),l=n(1584),i=n(98324),u=n(53201),c=n(18676),s=n(75137),d=n(91715),f=n(87513),p=n(57437),h="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[v,y,b]=(0,a.B)(g),[w,x]=(0,i.b)(g,[b]),[M,R]=w(g),C=r.forwardRef((e,t)=>(0,p.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(j,{...e,ref:t})})}));C.displayName=g;var j=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:i=!1,dir:u,currentTabStopId:g,defaultCurrentTabStopId:v,onCurrentTabStopIdChange:b,onEntryFocus:w,preventScrollOnEntryFocus:x=!1,...R}=e,C=r.useRef(null),j=(0,l.e)(t,C),_=(0,f.gm)(u),[P=null,E]=(0,d.T)({prop:g,defaultProp:v,onChange:b}),[O,k]=r.useState(!1),N=(0,s.W)(w),I=y(n),T=r.useRef(!1),[D,A]=r.useState(0);return r.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(h,N),()=>e.removeEventListener(h,N)},[N]),(0,p.jsx)(M,{scope:n,orientation:a,dir:_,loop:i,currentTabStopId:P,onItemFocus:r.useCallback(e=>E(e),[E]),onItemShiftTab:r.useCallback(()=>k(!0),[]),onFocusableItemAdd:r.useCallback(()=>A(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>A(e=>e-1),[]),children:(0,p.jsx)(c.WV.div,{tabIndex:O||0===D?-1:0,"data-orientation":a,...R,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{T.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!T.current;if(e.target===e.currentTarget&&t&&!O){let t=new CustomEvent(h,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=I().filter(e=>e.focusable);S([e.find(e=>e.active),e.find(e=>e.id===P),...e].filter(Boolean).map(e=>e.ref.current),x)}}T.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>k(!1))})})}),_="RovingFocusGroupItem",P=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:l=!1,tabStopId:i,...s}=e,d=(0,u.M)(),f=i||d,h=R(_,n),m=h.currentTabStopId===f,g=y(n),{onFocusableItemAdd:b,onFocusableItemRemove:w}=h;return r.useEffect(()=>{if(a)return b(),()=>w()},[a,b,w]),(0,p.jsx)(v.ItemSlot,{scope:n,id:f,focusable:a,active:l,children:(0,p.jsx)(c.WV.span,{tabIndex:m?0:-1,"data-orientation":h.orientation,...s,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?h.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>h.onItemFocus(f)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){h.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return E[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var n,r;"prev"===t&&o.reverse();let a=o.indexOf(e.currentTarget);o=h.loop?(n=o,r=a+1,n.map((e,t)=>n[(r+t)%n.length])):o.slice(a+1)}setTimeout(()=>S(o))}})})})});P.displayName=_;var E={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function S(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var O=C,k=P},79512:function(e,t,n){n.d(t,{F:function(){return c},f:function(){return s}});var r=n(2265),o=["light","dark"],a="(prefers-color-scheme: dark)",l="undefined"==typeof window,i=r.createContext(void 0),u={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=r.useContext(i))?e:u},s=e=>r.useContext(i)?e.children:r.createElement(f,{...e}),d=["light","dark"],f=e=>{let{forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:l=!0,enableColorScheme:u=!0,storageKey:c="theme",themes:s=d,defaultTheme:f=l?"system":"light",attribute:v="data-theme",value:y,children:b,nonce:w}=e,[x,M]=r.useState(()=>h(c,f)),[R,C]=r.useState(()=>h(c)),j=y?Object.values(y):s,_=r.useCallback(e=>{let t=e;if(!t)return;"system"===e&&l&&(t=g());let r=y?y[t]:t,a=n?m():null,i=document.documentElement;if("class"===v?(i.classList.remove(...j),r&&i.classList.add(r)):r?i.setAttribute(v,r):i.removeAttribute(v),u){let e=o.includes(f)?f:null,n=o.includes(t)?t:e;i.style.colorScheme=n}null==a||a()},[]),P=r.useCallback(e=>{let t="function"==typeof e?e(e):e;M(t);try{localStorage.setItem(c,t)}catch(e){}},[t]),E=r.useCallback(e=>{C(g(e)),"system"===x&&l&&!t&&_("system")},[x,t]);r.useEffect(()=>{let e=window.matchMedia(a);return e.addListener(E),E(e),()=>e.removeListener(E)},[E]),r.useEffect(()=>{let e=e=>{e.key===c&&P(e.newValue||f)};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[P]),r.useEffect(()=>{_(null!=t?t:x)},[t,x]);let S=r.useMemo(()=>({theme:x,setTheme:P,forcedTheme:t,resolvedTheme:"system"===x?R:x,themes:l?[...s,"system"]:s,systemTheme:l?R:void 0}),[x,P,t,R,l,s]);return r.createElement(i.Provider,{value:S},r.createElement(p,{forcedTheme:t,disableTransitionOnChange:n,enableSystem:l,enableColorScheme:u,storageKey:c,themes:s,defaultTheme:f,attribute:v,value:y,children:b,attrs:j,nonce:w}),b)},p=r.memo(e=>{let{forcedTheme:t,storageKey:n,attribute:l,enableSystem:i,enableColorScheme:u,defaultTheme:c,value:s,attrs:d,nonce:f}=e,p="system"===c,h="class"===l?"var d=document.documentElement,c=d.classList;".concat("c.remove(".concat(d.map(e=>"'".concat(e,"'")).join(","),")"),";"):"var d=document.documentElement,n='".concat(l,"',s='setAttribute';"),m=u?(o.includes(c)?c:null)?"if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'".concat(c,"'"):"if(e==='light'||e==='dark')d.style.colorScheme=e":"",g=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2)||void 0===arguments[2]||arguments[2],r=s?s[e]:e,a=t?e+"|| ''":"'".concat(r,"'"),i="";return u&&n&&!t&&o.includes(e)&&(i+="d.style.colorScheme = '".concat(e,"';")),"class"===l?t||r?i+="c.add(".concat(a,")"):i+="null":r&&(i+="d[s](n,".concat(a,")")),i},v=t?"!function(){".concat(h).concat(g(t),"}()"):i?"!function(){try{".concat(h,"var e=localStorage.getItem('").concat(n,"');if('system'===e||(!e&&").concat(p,")){var t='").concat(a,"',m=window.matchMedia(t);if(m.media!==t||m.matches){").concat(g("dark"),"}else{").concat(g("light"),"}}else if(e){").concat(s?"var x=".concat(JSON.stringify(s),";"):"").concat(g(s?"x[e]":"e",!0),"}").concat(p?"":"else{"+g(c,!1,!1)+"}").concat(m,"}catch(e){}}()"):"!function(){try{".concat(h,"var e=localStorage.getItem('").concat(n,"');if(e){").concat(s?"var x=".concat(JSON.stringify(s),";"):"").concat(g(s?"x[e]":"e",!0),"}else{").concat(g(c,!1,!1),";}").concat(m,"}catch(t){}}();");return r.createElement("script",{nonce:f,dangerouslySetInnerHTML:{__html:v}})}),h=(e,t)=>{let n;if(!l){try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t}},m=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},g=e=>(e||(e=window.matchMedia(a)),e.matches?"dark":"light")}}]);