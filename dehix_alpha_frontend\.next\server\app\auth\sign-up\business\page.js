(()=>{var e={};e.id=5032,e.ids=[5032],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},92193:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c}),t(83998),t(54302),t(12523);var r=t(23191),i=t(88716),a=t(37922),n=t.n(a),l=t(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c=["",{children:["auth",{children:["sign-up",{children:["business",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,83998)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\auth\\sign-up\\business\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\auth\\sign-up\\business\\page.tsx"],m="/auth/sign-up/business/page",u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/auth/sign-up/business/page",pathname:"/auth/sign-up/business",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},32304:(e,s,t)=>{Promise.resolve().then(t.bind(t,6079))},79635:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(80851).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},6079:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>E});var r=t(10326),i=t(90434),a=t(40603),n=t(74064),l=t(10321),o=t(79635),c=t(47546),d=t(58038),m=t(66307),u=t(12714),p=t(91216),h=t(77506),x=t(24230),f=t(86333),g=t(45691),j=t(35047),b=t(17577),y=t.n(b),w=t(74723),v=t(27256),N=t(89124),C=t(57776),P=t(26408),k=t(27918),q=t(91664),z=t(9969),Z=t(41190),_=t(44794),S=t(29280),A=t(56627),I=t(6260),U=t(51223),T=t(24118);function D({open:e,setOpen:s,setIsChecked:t}){return r.jsx(T.Vq,{open:e,onOpenChange:s,children:(0,r.jsxs)(T.cZ,{className:"max-w-5xl sm:mx-4 max-h-screen overflow-y-auto rounded-2xl p-6 shadow-lg",children:[r.jsx(T.fK,{children:r.jsx(T.$N,{className:"text-2xl font-bold mb-4 text-center",children:"Terms & Conditions for Businesses/Clients"})}),(0,r.jsxs)("div",{className:"space-y-6 text-sm leading-relaxed px-2 sm:px-4",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-base mb-1",children:"1. Registration and Account Management"}),r.jsx("p",{children:"By registering as a business or client on the platform, users agree to abide by these terms and conditions. Businesses must provide accurate, complete, and up-to-date company information during registration. Failure to provide correct details may result in account rejection or termination. The platform reserves the right to approve, reject, or terminate business accounts at its sole discretion."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-base mb-1",children:"2. Responsibilities of Businesses"}),r.jsx("p",{children:"Businesses are responsible for carefully reviewing freelancer profiles, portfolios, and qualifications before making hiring decisions. The platform does not vet or guarantee the performance of freelancers. Businesses acknowledge that freelancers are independent contractors and not employees of the platform. Therefore, the platform holds no liability for disputes regarding employment status, wages, or project outcomes. Businesses must comply with local labor laws when engaging freelancers through the platform."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-base mb-1",children:"3. Platform Limitations"}),r.jsx("p",{children:"The platform acts solely as a connection facilitator between freelancers and businesses. It does not provide job guarantees or ensure the quality of work delivered. The platform is not responsible for delays, missed deadlines, incomplete work, or disputes between businesses and freelancers."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-base mb-1",children:"4. Payment Policies"}),r.jsx("p",{children:"Direct payments to freelancers outside the platform are strictly prohibited. All payments must be processed through the platform to ensure security, accountability, and dispute resolution. Upon successful payment, businesses retain full rights to freelancer-created content or work unless otherwise agreed in writing."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-base mb-1",children:"5. Job Posting Guidelines"}),r.jsx("p",{children:"Businesses are prohibited from posting scam jobs, misleading job descriptions, or any job listings that promote illegal or unethical activities. The platform reserves the right to review, modify, or remove job postings that violate these guidelines."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-base mb-1",children:"6. Communication and Conduct"}),r.jsx("p",{children:"Businesses must maintain professional, respectful, and non-offensive communication when interacting with freelancers or platform representatives. Spam, harassment, or abusive behavior will result in account suspension or termination."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-base mb-1",children:"7. Liability and Disputes"}),r.jsx("p",{children:"The platform is not liable for freelancer performance or quality of work, missed deadlines or incomplete deliverables, or financial losses incurred by businesses due to freelancer engagements."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-base mb-1",children:"8. Account Termination"}),r.jsx("p",{children:"Accounts may be terminated if businesses provide false or misleading information, attempt to bypass platform payment processes, post scam or illegal job offers, or violate communication guidelines or engage in unethical conduct."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-base mb-1",children:"9. Use of User Information"}),r.jsx("p",{children:"User information may be utilized to improve platform functionality and matchmaking, assist in effective communication between clients and freelancers, and support marketing campaigns, promotions, or platform updates (with the option to manage notification preferences)."})]})]}),r.jsx("div",{className:"mt-8 flex justify-end",children:r.jsx(q.z,{onClick:()=>{t(!0),s(!1)},className:"px-6 py-2 text-sm font-medium rounded-md bg-primary hover:bg-primary/90 transition",children:"I Accept"})})]})})}let L=({currentStep:e=0})=>{let s=[{id:0,title:"Personal Info",icon:o.Z},{id:1,title:"Company Info",icon:c.Z},{id:2,title:"Verification",icon:d.Z}];return(0,r.jsxs)("div",{className:"w-full max-w-5xl mx-auto py-4  sm:py-6 mb-10 sm:mb-8",children:[(0,r.jsxs)("div",{className:"text-center space-y-2 sm:space-y-4",children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold",children:["Create Your Business ",r.jsx("span",{className:"block",children:"Account"})]}),r.jsx("p",{className:"text-muted-foreground",children:"Join our community and find the best talent in web3 space"})]}),(0,r.jsxs)("div",{className:"my-4 text-center text-xs sm:text-sm",children:["Are you a Freelancer?"," ",r.jsx(q.z,{variant:"outline",size:"sm",className:"ml-2",asChild:!0,children:r.jsx(i.default,{href:"/auth/sign-up/freelancer",children:"Register Freelancer"})})]}),r.jsx("div",{className:"flex items-center justify-center sm:mt-8 px-2 sm:px-0",children:s.map((t,i)=>(0,r.jsxs)(y().Fragment,{children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:`w-8 h-8 sm:w-12 sm:h-12 flex items-center justify-center rounded-full border-2 transition-all duration-300
                ${e>t.id?"bg-primary border-primary":e===t.id?"border-primary bg-background text-primary":"border-muted bg-background text-muted"}`,children:e>t.id?r.jsx(m.Z,{className:"w-4 h-4 sm:w-6 sm:h-6 text-background"}):r.jsx(t.icon,{className:"w-4 h-4 sm:w-6 sm:h-6"})}),r.jsx("span",{className:`absolute -bottom-6 left-1/2 -translate-x-1/2 text-xs sm:text-sm whitespace-nowrap font-medium
                ${e>=t.id?"text-primary":"text-muted-foreground"}`,children:t.title})]}),i<s.length-1&&r.jsx("div",{className:"w-20 sm:w-40 mx-2 sm:mx-4 h-[2px] bg-muted",children:r.jsx("div",{className:"h-full bg-primary transition-all duration-500",style:{width:e>t.id?"100%":"0%"}})})]},t.id))})]})},V=v.z.object({firstName:v.z.string().min(1,"First name is required"),lastName:v.z.string().min(1,"Last name is required"),userName:v.z.string().min(1,"Username is required"),companyName:v.z.string().min(1,"Company name is required"),companySize:v.z.string().min(1,"Company size is required"),position:v.z.string().min(1,"Position is required"),email:v.z.string().email("Invalid email address"),phone:v.z.string().min(1,"Phone number is required"),referralCode:v.z.string().optional(),linkedin:v.z.string().url("Invalid URL").optional().refine(e=>!e||/^https:\/\/www\.linkedin\.com\/in\/[a-zA-Z0-9_-]+\/?$/.test(e),{message:'LinkedIn URL must start with "https://www.linkedin.com/in/" and have a valid username'}),personalWebsite:v.z.string().optional().refine(e=>!e||/^(https?:\/\/|www\.)[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}.*[a-zA-Z0-9].*$/.test(e),{message:'Invalid website URL. Must start with "www." or "https://" and contain letters'}),password:v.z.string().min(8,"Password must be at least 8 characters long"),confirmPassword:v.z.string().min(8,"Confirm Password must be at least 8 characters long")}).refine(e=>e.password===e.confirmPassword,{path:["confirmPassword"],message:"Passwords do not match"});function $(){let[e,s]=(0,b.useState)(0);return r.jsx("div",{className:"flex w-full items-center justify-center",children:(0,r.jsxs)("div",{className:"w-full max-w-5xl px-4 sm:px-6 lg:px-4",children:[r.jsx(L,{currentStep:e}),r.jsx("div",{className:"flex justify-center w-full ",children:r.jsx("div",{className:"w-full max-w-4xl",children:r.jsx(B,{currentStep:e,setCurrentStep:s})})})]})})}function B({currentStep:e,setCurrentStep:s}){let[t,i]=(0,b.useState)(!1),[a,o]=(0,b.useState)(!1),[c,d]=(0,b.useState)("IN"),[m,y]=(0,b.useState)(""),[v,T]=(0,b.useState)(!1),[L,$]=(0,b.useState)(!1),[B,E]=(0,b.useState)(!1),[F,M]=(0,b.useState)(!1),G=(0,j.useSearchParams)(),[R,O]=(0,b.useState)(null),Q=()=>{o(e=>!e)},W=(0,w.cI)({resolver:(0,n.F)(V),defaultValues:{firstName:"",lastName:"",userName:"",companyName:"",companySize:"",position:"",email:"",phone:"",linkedin:"",personalWebsite:"",password:"",referralCode:""},mode:"all"}),J=async()=>{s(e-1)},H=async()=>{if(0===e){if(await W.trigger(["firstName","lastName","userName","email","password","confirmPassword"])){let{userName:t}=W.getValues();try{if(E(!0),t===R){s(e+1);return}let r=await I.b.get(`/public/username/check-duplicate?username=${t}&is_business=true`);!1===r.data.duplicate?s(e+1):((0,A.Am)({variant:"destructive",title:"User Already Exists",description:"This username is already taken. Please choose another one."}),O(t))}catch(e){(0,A.Am)({variant:"destructive",title:"API Error",description:"There was an error while checking the username."})}finally{E(!1)}}else(0,A.Am)({variant:"destructive",title:"Validation Error",description:"Please fill in all required fields before proceeding."})}else 1===e&&(await W.trigger(["companyName","companySize","position","linkedin","personalWebsite"])?s(e+1):(0,A.Am)({variant:"destructive",title:"Validation Error",description:"Please fill in all required fields before proceeding."}))},Y=async e=>{i(!0);let s=G.get("referral"),t=e.referralCode,a=s||t||null;y(`${N.find(e=>e.code===c)?.dialCode}${e.phone}`);let n={...e,phone:`${N.find(e=>e.code===c)?.dialCode}${e.phone}`,phoneVerify:!1,isBusiness:!0,connects:0,otp:"123456",otpverified:"No",ProjectList:[],Appliedcandidates:[],hirefreelancer:[],refer:"",verified:"",isVerified:!1},o=a?`/register/business?referralCode=${a}`:"/register/business";try{await I.b.post(o,n),(0,A.Am)({title:"Account created successfully!",description:"Your business account has been created."}),$(!0)}catch(e){console.error("API Error:",e),(0,A.Am)({variant:"destructive",title:"Uh oh! Something went wrong.",description:`Error: ${e.response?.data.message||"Something went wrong!"}`,action:r.jsx(l.gD,{altText:"Try again",children:"Try again"})})}finally{i(!1)}};return r.jsx(z.l0,{...W,children:(0,r.jsxs)("form",{onSubmit:W.handleSubmit(Y),className:"w-full max-w-3xl mx-auto",children:[r.jsx("div",{className:"w-full p-4 sm:p-6 rounded-lg shadow-sm border",children:(0,r.jsxs)("div",{className:"grid gap-4 sm:gap-6 w-full",children:[(0,r.jsxs)("div",{className:(0,U.cn)("grid gap-4",0===e?"":"hidden"),children:[(0,r.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[r.jsx(P.Z,{control:W.control,name:"firstName",label:"First name",placeholder:"First Name"}),r.jsx(P.Z,{control:W.control,name:"lastName",label:"Last name",placeholder:"Last Name"})]}),(0,r.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[r.jsx(P.Z,{control:W.control,name:"userName",label:"Username",placeholder:"Username"}),r.jsx(P.Z,{control:W.control,name:"email",label:"Email",placeholder:"Enter your email",type:"email"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(_.Label,{children:"Password"}),r.jsx(z.Wi,{control:W.control,name:"password",render:({field:e})=>(0,r.jsxs)(z.xJ,{children:[r.jsx(z.NI,{children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx(Z.I,{placeholder:"Enter your password",type:a?"text":"password",className:"pr-10",...e}),r.jsx("button",{type:"button",onClick:Q,className:"absolute inset-y-0 right-0 px-3 flex items-center",children:a?r.jsx(u.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"}):r.jsx(p.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"})})]})}),r.jsx(z.zG,{})]})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(_.Label,{children:"Confirm Password"}),r.jsx(z.Wi,{control:W.control,name:"confirmPassword",render:({field:e})=>(0,r.jsxs)(z.xJ,{children:[r.jsx(z.NI,{children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx(Z.I,{placeholder:"Confirm your password",type:a?"text":"password",className:"pr-10",...e}),r.jsx("button",{type:"button",onClick:Q,className:"absolute inset-y-0 right-0 px-3 flex items-center",children:a?r.jsx(u.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"}):r.jsx(p.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"})})]})}),r.jsx(z.zG,{})]})})]}),r.jsx("div",{className:"flex gap-2 justify-end mt-4",children:r.jsx(q.z,{type:"button",onClick:H,className:"w-full sm:w-auto flex items-center justify-center",disabled:B,children:B?r.jsx(h.Z,{size:20,className:"animate-spin"}):(0,r.jsxs)(r.Fragment,{children:["Next",r.jsx(x.Z,{className:"w-4 h-4 ml-2"})]})})})]}),(0,r.jsxs)("div",{className:(0,U.cn)("grid gap-4",1===e?"":"hidden"),children:[r.jsx(P.Z,{control:W.control,name:"companyName",label:"Company Name",placeholder:"Company Name"}),(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(_.Label,{htmlFor:"company-size",children:"Company Size"}),r.jsx(w.Qr,{control:W.control,name:"companySize",render:({field:e})=>(0,r.jsxs)(S.Ph,{onValueChange:e.onChange,value:e.value,children:[r.jsx(S.i4,{id:"company-size",children:r.jsx(S.ki,{placeholder:"Select Size"})}),r.jsx(S.Bw,{children:(0,r.jsxs)(S.DI,{children:[r.jsx(S.Ql,{value:"0-20",children:"0-20"}),r.jsx(S.Ql,{value:"20-50",children:"20-50"}),r.jsx(S.Ql,{value:"50-100",children:"50-100"}),r.jsx(S.Ql,{value:"100-500",children:"100-500"}),r.jsx(S.Ql,{value:"500+",children:"500 +"})]})})]})})]}),(0,r.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[r.jsx(P.Z,{control:W.control,name:"position",label:"Position",placeholder:"CTO"}),r.jsx(P.Z,{control:W.control,name:"referralCode",label:"Referral",type:"string",placeholder:"JOHN123",className:"w-full"})]}),(0,r.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[r.jsx(P.Z,{control:W.control,name:"linkedin",label:"LinkedIn",placeholder:"https://www.linkedin.com/in/username",type:"url"}),r.jsx(P.Z,{control:W.control,name:"personalWebsite",label:"Portfolio Url",type:"url",placeholder:"https://www.yourwebsite.com"})]}),(0,r.jsxs)("div",{className:"flex gap-2 justify-between mt-4",children:[(0,r.jsxs)(q.z,{type:"button",onClick:J,className:"w-full sm:w-auto",children:[r.jsx(f.Z,{className:"w-4 h-4 mr-2"}),"Previous"]}),(0,r.jsxs)(q.z,{type:"button",onClick:H,className:"w-full sm:w-auto",children:["Next",r.jsx(x.Z,{className:"w-4 h-4 ml-2"})]})]})]}),(0,r.jsxs)("div",{className:(0,U.cn)("grid gap-4",2===e?"":"hidden"),children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(_.Label,{htmlFor:"phone",children:"Phone Number"}),r.jsx(C.Z,{control:W.control,setCode:d,code:c})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-4",children:[r.jsx("input",{type:"checkbox",id:"terms",checked:v,onChange:()=>{F||T(!v)},className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,r.jsxs)("label",{htmlFor:"terms",className:"text-sm text-gray-600",children:["I agree to the"," ",r.jsx("span",{onClick:()=>M(!0),className:"text-primary hover:underline",children:"Terms and Conditions"})]}),r.jsx(D,{open:F,setOpen:M,setIsChecked:T})]}),(0,r.jsxs)("div",{className:"flex gap-2 flex-col sm:flex-row justify-between mt-4",children:[(0,r.jsxs)(q.z,{type:"button",onClick:J,className:"w-full sm:w-auto",children:[r.jsx(f.Z,{className:"w-4 h-4 mr-2"}),"Previous"]}),(0,r.jsxs)(q.z,{type:"submit",className:"w-full sm:w-auto",disabled:t||!v,children:[t?r.jsx(h.Z,{className:"mr-2 h-4 w-4 animate-spin"}):r.jsx(g.Z,{className:"mr-2 h-4 w-4"}),"Create account"]})]})]})]})}),r.jsx(k.Z,{phoneNumber:m,isModalOpen:L,setIsModalOpen:$})]})})}function E(){return(0,r.jsxs)("div",{className:"relative min-h-screen w-full",children:[r.jsx("div",{className:"absolute left-4 top-4 sm:left-10 sm:top-10",children:r.jsx(a.T,{})}),r.jsx("div",{className:"flex items-center justify-center py-20 sm:py-12",children:r.jsx("div",{className:"mx-auto w-full  px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"grid gap-6",children:[r.jsx($,{}),(0,r.jsxs)("div",{className:"mt-4 text-center text-xs sm:text-sm",children:["Already have an account?"," ",r.jsx(q.z,{variant:"outline",size:"sm",className:"ml-2",asChild:!0,children:r.jsx(i.default,{href:"/auth/login",children:"Sign in"})})]}),(0,r.jsxs)("p",{className:"px-2 text-center text-xs text-muted-foreground sm:px-8 sm:text-sm",children:["By clicking continue, you agree to our"," ",r.jsx(q.z,{variant:"link",className:"p-0",asChild:!0,children:r.jsx(i.default,{href:"/terms",children:"Terms of Service"})})," ","and"," ",r.jsx(q.z,{variant:"link",className:"p-0",asChild:!0,children:r.jsx(i.default,{href:"/privacy",children:"Privacy Policy."})})]})]})})})]})}},83998:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>a,default:()=>l});var r=t(68570);let i=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\auth\sign-up\business\page.tsx`),{__esModule:a,$$typeof:n}=i;i.default;let l=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\auth\sign-up\business\page.tsx#default`)},37125:(e,s,t)=>{"use strict";function r(e,[s,t]){return Math.min(t,Math.max(s,e))}t.d(s,{u:()=>r})}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8948,4198,6034,4718,2146,6686,4736,9169,8893],()=>t(92193));module.exports=r})();