(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9160],{46936:function(e,t,r){Promise.resolve().then(r.bind(r,94616))},33480:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var n=r(2265),o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=(e,t)=>{let r=(0,n.forwardRef)((r,s)=>{let{color:i="currentColor",size:l=24,strokeWidth:c=2,absoluteStrokeWidth:u,className:d="",children:f,...m}=r;return(0,n.createElement)("svg",{ref:s,...o,width:l,height:l,stroke:i,strokeWidth:u?24*Number(c)/Number(l):c,className:["lucide","lucide-".concat(a(e)),d].join(" "),...m},[...t.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(f)?f:[f]])});return r.displayName="".concat(e),r}},95137:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(33480).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},16463:function(e,t,r){"use strict";var n=r(71169);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},94616:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return d}});var n=r(57437),o=r(2265);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(33480).Z)("Map",[["path",{d:"M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z",key:"169xi5"}],["path",{d:"M15 5.764v15",key:"1pn4in"}],["path",{d:"M9 3.236v15",key:"1uimfh"}]]);var s=r(95137),i=r(16463),l=r(44785),c=r(48185),u=r(89733),d=()=>{let e=(0,i.useRouter)(),[t,r]=(0,o.useState)(null);return((0,o.useEffect)(()=>{r(!!l.Z.get("token"))},[]),null===t)?null:(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:(0,n.jsxs)(c.Zb,{className:"w-full max-w-md shadow-lg",children:[(0,n.jsxs)(c.Ol,{className:"text-center space-y-2",children:[(0,n.jsx)("div",{className:"flex justify-center mb-4",children:(0,n.jsx)(a,{className:"h-16 w-16 text-primary animate-pulse"})}),(0,n.jsx)(c.ll,{className:"text-4xl font-extrabold",children:"404"}),(0,n.jsx)("p",{className:"text-2xl font-semibold text-muted-foreground",children:"Page Not Found"})]}),(0,n.jsxs)(c.aY,{className:"text-center space-y-4",children:[(0,n.jsx)("p",{className:"text-lg text-muted-foreground",children:"We were unable to find the page you are looking for."}),(0,n.jsx)("div",{className:"border-t border-border pt-4",children:(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:"The page might have been moved, deleted, or never existed."})})]}),(0,n.jsx)(c.eW,{className:"flex justify-center",children:(0,n.jsxs)(u.z,{size:"lg",onClick:()=>{if(t)try{window.history.back()}catch(t){e.push("/")}else e.push("/auth/login")},className:"w-full sm:w-auto gap-2",children:[(0,n.jsx)(s.Z,{className:"h-4 w-4"}),t?"Go Back":"Login"]})})]})})}},89733:function(e,t,r){"use strict";r.d(t,{d:function(){return l},z:function(){return c}});var n=r(57437),o=r(2265),a=r(63355),s=r(12218),i=r(49354);let l=(0,s.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=o.forwardRef((e,t)=>{let{className:r,variant:o,size:s,asChild:c=!1,...u}=e,d=c?a.g7:"button";return(0,n.jsx)(d,{className:(0,i.cn)(l({variant:o,size:s,className:r})),ref:t,...u})});c.displayName="Button"},48185:function(e,t,r){"use strict";r.d(t,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return s},aY:function(){return u},eW:function(){return d},ll:function(){return l}});var n=r(57437),o=r(2265),a=r(49354);let s=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...o})});s.displayName="Card";let i=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",r),...o})});i.displayName="CardHeader";let l=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",r),...o})});l.displayName="CardTitle";let c=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",r),...o})});c.displayName="CardDescription";let u=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",r),...o})});u.displayName="CardContent";let d=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",r),...o})});d.displayName="CardFooter"},42361:function(e,t,r){"use strict";r.d(t,{I8:function(){return u},Vv:function(){return d},db:function(){return c}});var n=r(15236),o=r(75735),a=r(60516),s=r(69842),i=r(99854);let l=(0,n.ZF)({apiKey:"AIzaSyBPTH9xikAUkgGof048klY6WGiZSmRoXXA",authDomain:"dehix-6c349.firebaseapp.com",databaseURL:"https://dehix-6c349-default-rtdb.firebaseio.com",projectId:"dehix-6c349",storageBucket:"dehix-6c349.appspot.com",messagingSenderId:"521082542540",appId:"1:521082542540:web:543857e713038c2927a569"}),c=(0,s.ad)(l),u=(0,o.v0)(l);u.useDeviceLanguage();let d=new o.hJ;(0,a.N8)(l),(0,i.cF)(l)},15922:function(e,t,r){"use strict";r.d(t,{b:function(){return o},q:function(){return a}});var n=r(38472);let o=n.Z.create({baseURL:"http://127.0.0.1:8080/"});console.log("Base URL:","http://127.0.0.1:8080/");let a=e=>{console.log("Initializing Axios with token:",e),o=n.Z.create({baseURL:"http://127.0.0.1:8080/",headers:{Authorization:"Bearer ".concat(e)}})};o.interceptors.request.use(e=>(console.log("Request config:",e),e),e=>(console.error("Request error:",e),Promise.reject(e))),o.interceptors.response.use(e=>(console.log("Response:",e.data),e),e=>(console.error("Response error:",e),Promise.reject(e)))},49354:function(e,t,r){"use strict";r.d(t,{bx:function(){return f},c0:function(){return u},cn:function(){return c},is:function(){return m},pH:function(){return d}});var n=r(44839),o=r(96164),a=r(75735),s=r(44785),i=r(15922),l=r(42361);function c(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.m6)((0,n.W)(t))}let u=async e=>{try{await (0,a.LS)(l.I8,e),console.log("Password reset email sent successfully.")}catch(r){let e=r.code,t=r.message;throw console.log(e,t),Error(t)}},d=async(e,t)=>{try{return await (0,a.e5)(l.I8,e,t)}catch(r){let e=r.code,t=r.message;throw console.log(e,t),Error(t)}},f=async()=>await (0,a.rh)(l.I8,l.Vv),m=async e=>{try{let t=e.user,r=await t.getIdToken();(0,i.q)(r);let n=(await t.getIdTokenResult()).claims,o={uid:t.uid,email:t.email,displayName:t.displayName,phoneNumber:t.phoneNumber,photoURL:t.photoURL,emailVerified:t.emailVerified},a=n.type;return s.Z.set("userType",a,{expires:1,path:"/"}),s.Z.set("token",r,{expires:1,path:"/"}),{user:o,claims:n}}catch(e){throw console.error("Error fetching user data:",e),e}}},63355:function(e,t,r){"use strict";r.d(t,{g7:function(){return s}});var n=r(2265);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var a=r(57437),s=n.forwardRef((e,t)=>{let{children:r,...o}=e,s=n.Children.toArray(r),l=s.find(c);if(l){let e=l.props.children,r=s.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(i,{...o,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,a.jsx)(i,{...o,ref:t,children:r})});s.displayName="Slot";var i=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){let e,s;let i=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref;return n.cloneElement(r,{...function(e,t){let r={...t};for(let n in t){let o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=(...e)=>{a(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...a}:"className"===n&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props),ref:t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}(t,i):i})}return n.Children.count(r)>1?n.Children.only(null):null});i.displayName="SlotClone";var l=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function c(e){return n.isValidElement(e)&&e.type===l}}},function(e){e.O(0,[4358,7481,9208,9668,2971,7023,1744],function(){return e(e.s=46936)}),_N_E=e.O()}]);