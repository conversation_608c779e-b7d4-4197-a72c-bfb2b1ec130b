"use strict";(()=>{var e={};e.id=7955,e.ids=[7955],e.modules={47849:e=>{e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71396:e=>{e.exports=require("undici")},83122:e=>{e.exports=require("undici")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},85158:e=>{e.exports=require("http2")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},77282:e=>{e.exports=require("process")},12781:e=>{e.exports=require("stream")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},27267:(e,s,r)=>{r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c}),r(12119),r(54302),r(12523);var a=r(23191),t=r(88716),i=r(37922),n=r.n(i),l=r(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let c=["",{children:["business",{children:["market",{children:["invited",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,12119)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\market\\invited\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\business\\market\\invited\\page.tsx"],x="/business/market/invited/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/business/market/invited/page",pathname:"/business/market/invited",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},12119:(e,s,r)=>{r.r(s),r.d(s,{default:()=>v});var a=r(19510);r(71159);var t=r(72301);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,t.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var n=r(69013),l=r(90071);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,t.Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);var c=r(80086),d=r(24767),x=r(93804),p=r(27039),m=r(55116),h=r(59558);let u=[{id:1,name:"Alex Johnson",avatar:"/avatars/alex.jpg",role:"Frontend Developer",experience:"5 years",skills:["React","TypeScript","NextJS"],location:"New York, USA",invitedDate:"Feb 25, 2025",email:"<EMAIL>"},{id:2,name:"Sarah Williams",avatar:"/avatars/sarah.jpg",role:"UI/UX Designer",experience:"3 years",skills:["Figma","Adobe XD","User Research"],location:"London, UK",invitedDate:"Feb 26, 2025",email:"<EMAIL>"},{id:3,name:"Michael Chen",avatar:"/avatars/michael.jpg",role:"Full Stack Developer",experience:"7 years",skills:["React","Node.js","MongoDB"],location:"San Francisco, USA",invitedDate:"Feb 24, 2025",email:"<EMAIL>"},{id:4,name:"Emma Rodriguez",avatar:"/avatars/emma.jpg",role:"Product Designer",experience:"4 years",skills:["UI/UX","Wireframing","Prototyping"],location:"Berlin, Germany",invitedDate:"Feb 23, 2025",email:"<EMAIL>"}],v=()=>(0,a.jsxs)(d.ZP,{activeTab:"invited",children:[(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[a.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Invited Talents"}),(0,a.jsxs)("span",{className:"text-muted-foreground",children:["Showing ",u.length," results"]})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:u.map(e=>(0,a.jsxs)(x.Zb,{className:"overflow-hidden",children:[a.jsx(x.Ol,{className:"pb-2",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,a.jsxs)(m.qE,{className:"h-12 w-12",children:[a.jsx(m.F$,{src:e.avatar,alt:e.name}),a.jsx(m.Q5,{children:e.name.slice(0,2).toUpperCase()})]}),(0,a.jsxs)("div",{children:[a.jsx(x.ll,{children:e.name}),a.jsx(x.SZ,{children:e.role})]})]}),(0,a.jsxs)(h.C,{variant:"outline",className:"flex items-center gap-1",children:[a.jsx(i,{className:"h-3 w-3"}),"Invited"]})]})}),a.jsx(x.aY,{className:"pb-2",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[a.jsx(n.Z,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("span",{children:[e.experience," experience"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[a.jsx(l.Z,{className:"h-4 w-4 text-muted-foreground"}),a.jsx("span",{children:e.location})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[a.jsx(o,{className:"h-4 w-4 text-muted-foreground"}),a.jsx("span",{children:e.email})]}),a.jsx("div",{className:"flex flex-wrap gap-2 pt-2",children:e.skills.map(e=>a.jsx(h.C,{variant:"secondary",children:e},e))})]})}),(0,a.jsxs)(x.eW,{className:"flex justify-between pt-2",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Invited on ",e.invitedDate]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[a.jsx(p.z,{size:"sm",variant:"ghost",children:"Cancel"}),(0,a.jsxs)(p.z,{size:"sm",variant:"outline",className:"flex items-center gap-1",children:[a.jsx(c.Z,{className:"h-3 w-3"}),"View Profile"]})]})]})]},e.id))})]})}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[8948,4198,6034,4718,6226,495,8344,5165,4736,6499,1889],()=>r(27267));module.exports=a})();