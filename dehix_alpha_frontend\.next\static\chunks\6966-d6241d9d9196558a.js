"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6966],{6600:function(t,e,n){n.d(e,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,n(33480).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},51077:function(t,e,n){n.d(e,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,n(33480).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},22023:function(t,e,n){n.d(e,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,n(33480).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},5068:function(t,e,n){n.d(e,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,n(33480).Z)("Gavel",[["path",{d:"m14.5 12.5-8 8a2.119 2.119 0 1 1-3-3l8-8",key:"15492f"}],["path",{d:"m16 16 6-6",key:"vzrcl6"}],["path",{d:"m8 8 6-6",key:"18bi4p"}],["path",{d:"m9 7 8 8",key:"5jnvq1"}],["path",{d:"m21 11-8-8",key:"z4y7zo"}]])},83220:function(t,e,n){n.d(e,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,n(33480).Z)("LaptopMinimal",[["rect",{width:"18",height:"12",x:"3",y:"4",rx:"2",ry:"2",key:"1qhy41"}],["line",{x1:"2",x2:"22",y1:"20",y2:"20",key:"ni3hll"}]])},72377:function(t,e,n){n.d(e,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,n(33480).Z)("ListFilter",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M7 12h10",key:"b7w52i"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},24258:function(t,e,n){n.d(e,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,n(33480).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},37505:function(t,e,n){n.d(e,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,n(33480).Z)("Ticket",[["path",{d:"M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z",key:"qn84l0"}],["path",{d:"M13 5v2",key:"dyzc3o"}],["path",{d:"M13 17v2",key:"1ont0d"}],["path",{d:"M13 11v2",key:"1wjjxi"}]])},38133:function(t,e,n){n.d(e,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,n(33480).Z)("UsersRound",[["path",{d:"M18 21a8 8 0 0 0-16 0",key:"3ypg7q"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["path",{d:"M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3",key:"10s06x"}]])},95737:function(t,e,n){n.d(e,{Z:function(){return a}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,n(33480).Z)("Wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]])},77396:function(t,e,n){n.d(e,{y:function(){return r}});var a=n(11880);function r(t){return(0,a.L)(t,Date.now())}},62230:function(t,e,n){n.d(e,{i:function(){return r}});var a=n(74546);function r(t){let e=(0,a.Q)(t);return e.setHours(23,59,59,999),e}},25224:function(t,e,n){n.d(e,{Q:function(){return d}});var a=n(77396),r=n(74546);function i(t,e){let n=(0,r.Q)(t),a=(0,r.Q)(e),i=n.getTime()-a.getTime();return i<0?-1:i>0?1:i}var u=n(87283),o=n(91544),l=n(62230),f=n(4192),c=n(23028),s=n(40603),h=n(96634);function d(t,e){return function(t,e,n){var a,d,y,M,k,v;let p,m,D;let Z=(0,s.j)(),x=null!==(d=null!==(a=null==n?void 0:n.locale)&&void 0!==a?a:Z.locale)&&void 0!==d?d:c._,H=i(t,e);if(isNaN(H))throw RangeError("Invalid time value");let b=Object.assign({},n,{addSuffix:null==n?void 0:n.addSuffix,comparison:H});H>0?(p=(0,r.Q)(e),m=(0,r.Q)(t)):(p=(0,r.Q)(t),m=(0,r.Q)(e));let Q=(y=m,M=p,(v=null==void 0?void 0:(void 0).roundingMethod,t=>{let e=(v?Math[v]:Math.trunc)(t);return 0===e?0:e})((+(0,r.Q)(y)-+(0,r.Q)(M))/1e3)),g=Math.round((Q-((0,h.D)(m)-(0,h.D)(p))/1e3)/60);if(g<2){if(null==n?void 0:n.includeSeconds){if(Q<5)return x.formatDistance("lessThanXSeconds",5,b);if(Q<10)return x.formatDistance("lessThanXSeconds",10,b);if(Q<20)return x.formatDistance("lessThanXSeconds",20,b);if(Q<40)return x.formatDistance("halfAMinute",0,b);else if(Q<60)return x.formatDistance("lessThanXMinutes",1,b);else return x.formatDistance("xMinutes",1,b)}return 0===g?x.formatDistance("lessThanXMinutes",1,b):x.formatDistance("xMinutes",g,b)}if(g<45)return x.formatDistance("xMinutes",g,b);if(g<90)return x.formatDistance("aboutXHours",1,b);if(g<u.H_)return x.formatDistance("aboutXHours",Math.round(g/60),b);if(g<2520)return x.formatDistance("xDays",1,b);if(g<u.fH){let t=Math.round(g/u.H_);return x.formatDistance("xDays",t,b)}if(g<2*u.fH)return D=Math.round(g/u.fH),x.formatDistance("aboutXMonths",D,b);if((D=function(t,e){let n;let a=(0,r.Q)(t),u=(0,r.Q)(e),c=i(a,u),s=Math.abs((0,o.T)(a,u));if(s<1)n=0;else{1===a.getMonth()&&a.getDate()>27&&a.setDate(30),a.setMonth(a.getMonth()-c*s);let e=i(a,u)===-c;(function(t){let e=(0,r.Q)(t);return+(0,l.i)(e)==+(0,f.V)(e)})((0,r.Q)(t))&&1===s&&1===i(t,u)&&(e=!1),n=c*(s-Number(e))}return 0===n?0:n}(m,p))<12){let t=Math.round(g/u.fH);return x.formatDistance("xMonths",t,b)}{let t=D%12,e=Math.trunc(D/12);return t<3?x.formatDistance("aboutXYears",e,b):t<9?x.formatDistance("overXYears",e,b):x.formatDistance("almostXYears",e+1,b)}}(t,(0,a.y)(t),e)}}}]);